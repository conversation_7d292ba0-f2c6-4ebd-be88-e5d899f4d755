/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.chat-detail-page.data-v-53009239 {
  position: relative !important;
  width: 100% !important;
  height: 100vh !important;
  background-color: #f8f9fa;
  overflow: visible !important;
}
.data-v-53009239 .page-layout {
  background-color: #f8f9fa !important;
  position: relative !important;
  overflow: visible !important;
}
.data-v-53009239 .page-layout-content {
  background-color: #f8f9fa !important;
  position: relative !important;
  overflow: visible !important;
}
.page-wrapper.data-v-53009239 {
  background-color: #f8f9fa;
  min-height: 100vh;
  width: 100%;
}
.page-scroll-view.data-v-53009239 {
  height: 100%;
  box-sizing: border-box;
  background-color: transparent;
  padding-bottom: 180px;
}
.patient-question-card.data-v-53009239 {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 20px;
  margin: 16px;
  padding: 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid #e9ecef;
  overflow: hidden;
}
.patient-header.data-v-53009239 {
  display: flex;
  align-items: center;
  padding: 20px;
  background-color: #ffffff;
  border-bottom: 1px solid #e9ecef;
}
.patient-avatar.data-v-53009239 {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: 2px solid #e9ecef;
  -o-object-fit: cover;
     object-fit: cover;
  background-color: #f8f9fa;
}
.patient-info.data-v-53009239 {
  margin-left: 12px;
  flex: 1;
}
.patient-name.data-v-53009239 {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  display: block;
  margin-bottom: 4px;
}
.question-time.data-v-53009239 {
  font-size: 12px;
  color: #6c757d;
  display: block;
}
.title-section.data-v-53009239 {
  margin: 16px 20px;
  padding: 16px 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  position: relative;
}
.title-section.data-v-53009239::before {
  content: "";
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 28px;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  border-radius: 2px;
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}
.title-text.data-v-53009239 {
  font-size: 18px;
  font-weight: 700;
  color: #1a1a1a;
  line-height: 1.4;
  margin-left: 20px;
  letter-spacing: 0.3px;
}
.content-section.data-v-53009239 {
  padding: 20px;
}
.content-header.data-v-53009239 {
  margin-bottom: 12px;
}
.content-label.data-v-53009239 {
  font-size: 14px;
  font-weight: 600;
  color: #6c757d;
}
.content-text.data-v-53009239 {
  font-size: 16px;
  line-height: 1.6;
  color: #2c3e50;
  word-wrap: break-word;
}
.images-section.data-v-53009239 {
  padding: 0 20px 20px 20px;
  border-bottom: 1px solid #e9ecef;
}
.images-header.data-v-53009239 {
  margin-bottom: 12px;
}
.images-label.data-v-53009239 {
  font-size: 14px;
  font-weight: 600;
  color: #6c757d;
}
.images-grid.data-v-53009239 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}
.images-grid.data-v-53009239 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}
.image-item.data-v-53009239 {
  position: relative;
  aspect-ratio: 1;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
}
.image-item.loading.data-v-53009239 {
  border-color: #07C160;
}
.image-item.error.data-v-53009239 {
  border-color: #ff4444;
  opacity: 0.5;
}
.image-item.success.data-v-53009239 {
  border-color: transparent;
}
.question-image.data-v-53009239 {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  transition: transform 0.2s ease;
}
.image-item:active .question-image.data-v-53009239 {
  transform: scale(0.95);
}
.image-overlay.data-v-53009239 {
  position: absolute;
  top: 6px;
  right: 6px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  border-radius: 12px;
  padding: 2px 6px;
}
.image-index.data-v-53009239 {
  font-size: 10px;
  font-weight: 600;
}
.image-debug.data-v-53009239 {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 2px 4px;
  font-size: 9px;
  line-height: 1.2;
}
.image-debug text.data-v-53009239 {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.message-container.data-v-53009239 {
  padding: 0 0 20px 0;
  background-color: #fff;
  border-radius: 16px;
  margin: 16px;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  overflow: hidden;
}
.message-title.data-v-53009239 {
  font-size: 18px;
  color: #333;
  margin-bottom: 0;
  font-weight: 600;
  padding: 20px 20px 16px 20px;
}
.reply.data-v-53009239 {
  padding: 16px 20px 50px 20px;
  margin: 0;
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  position: relative;
  background-color: transparent;
  box-sizing: border-box;
  cursor: pointer;
  transition: background-color 0.2s ease;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.reply.data-v-53009239:hover {
  background-color: rgba(0, 0, 0, 0.03);
}
.reply.data-v-53009239:active {
  background-color: transparent;
}
.reply-content.data-v-53009239 {
  font-size: 14px;
  color: #333;
  line-height: 1.4;
  margin-top: 6px;
  margin-bottom: 4px;
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-word;
  width: 100%;
  box-sizing: border-box;
}
.mentioned-section.data-v-53009239 {
  margin: 16rpx 0;
  padding: 16rpx 24rpx;
}
.mentioned-doctors-list.data-v-53009239 {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8rpx;
}
.doctor-tag.data-v-53009239 {
  font-size: 24rpx;
  color: #07C160;
  font-weight: 500;
  background-color: rgba(7, 193, 96, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  margin-right: 8rpx;
  margin-bottom: 4rpx;
}
.chat-time.data-v-53009239 {
  font-size: 12px;
  color: #999;
  position: absolute;
  bottom: 16px;
  right: 20px;
  white-space: nowrap;
  min-width: 120px;
  text-align: right;
}
.reply-part.data-v-53009239 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex: 1;
  min-width: 0;
  margin-left: 16px;
  padding-right: 60px;
}
.reply-name-container.data-v-53009239 {
  display: flex;
  flex-direction: column;
  margin-left: 0;
  margin-bottom: 0;
}
.reply-name.data-v-53009239 {
  font-size: 15px;
  color: #333;
  line-height: 1.3;
  font-weight: 500;
  margin-bottom: 2px;
}
.reply-mention.data-v-53009239 {
  margin: 4px 0;
}
.mention-text.data-v-53009239 {
  font-size: 13px;
  color: #07C160;
  background-color: #F0F9F5;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 400;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}
.reply-content-quote.data-v-53009239 {
  margin: 4px 0;
  padding: 8px 12px;
  background-color: #f5f5f5;
  border-left: 3px solid #ddd;
  border-radius: 4px;
}
.quote-text.data-v-53009239 {
  font-size: 13px;
  color: #666;
  font-style: italic;
  line-height: 1.4;
}
.delete-button.data-v-53009239 {
  position: absolute;
  top: 16px;
  right: 20px;
  width: 28px;
  height: 28px;
  background: rgba(128, 128, 128, 0.1);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0.8;
  transition: all 0.3s ease;
  z-index: 10;
  border: 1px solid rgba(128, 128, 128, 0.2);
  box-sizing: border-box;
  flex-shrink: 0;
  flex-grow: 0;
}
.delete-button.data-v-53009239:hover {
  opacity: 1;
  transform: scale(1.05);
  background: rgba(128, 128, 128, 0.15);
  border-color: rgba(128, 128, 128, 0.3);
}
.trash-icon.data-v-53009239 {
  position: relative;
  width: 12px;
  height: 14px;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-shrink: 0;
  flex-grow: 0;
}
.trash-lid.data-v-53009239 {
  width: 14px;
  height: 2px;
  background: #666;
  border-radius: 1px;
  position: relative;
  margin-bottom: 1px;
  flex-shrink: 0;
}
.trash-lid.data-v-53009239::before {
  content: "";
  position: absolute;
  top: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 2px;
  background: #666;
  border-radius: 1px 1px 0 0;
}
.trash-body.data-v-53009239 {
  width: 10px;
  height: 11px;
  background: transparent;
  border: 1.5px solid #666;
  border-top: none;
  border-radius: 0 0 2px 2px;
  position: relative;
  display: flex;
  justify-content: space-around;
  align-items: flex-start;
  padding-top: 2px;
  flex-shrink: 0;
  box-sizing: border-box;
}
.trash-line.data-v-53009239 {
  width: 1px;
  height: 6px;
  background: #666;
  border-radius: 0.5px;
  flex-shrink: 0;
}
.empty-reply-container.data-v-53009239 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background: transparent;
  margin: 0 auto;
  width: 100%;
  min-height: 200px;
  text-align: center;
}
.empty-icon.data-v-53009239 {
  font-size: 32px;
  margin-bottom: 12px;
  opacity: 0.6;
  display: block;
  text-align: center;
}
.message-empty.data-v-53009239 {
  color: #666;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  text-align: center;
  display: block;
  width: 100%;
}
.empty-subtitle.data-v-53009239 {
  color: #999;
  font-size: 14px;
  font-weight: 400;
  text-align: center;
  display: block;
  width: 100%;
}
.reply-window-fixed.data-v-53009239 {
  position: fixed !important;
  left: 16px !important;
  right: 16px !important;
  bottom: 20px !important;
  background: #fff !important;
  border-radius: 16px !important;
  padding: 16px !important;
  box-sizing: border-box;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  display: flex !important;
  flex-direction: column;
  z-index: 9999 !important;
  width: auto !important;
  min-height: 60px !important;
  visibility: visible !important;
  opacity: 1 !important;
  border: 1px solid #e0e0e0;
}
.reply-tip-above-input.data-v-53009239 {
  background: #f8f9fa;
  color: #666;
  border-radius: 12px;
  padding: 10px 16px;
  font-size: 13px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  border: 1px solid #e9ecef;
  margin: 0 0 12px 0;
  min-height: 40px;
  width: 100%;
  animation: slideDown-53009239 0.3s ease-out;
}
.reply-input-row.data-v-53009239 {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  min-height: 50px;
}
.reply-avatar.data-v-53009239 {
  width: 42px !important;
  height: 42px !important;
  border-radius: 50%;
  flex-shrink: 0 !important;
  flex-grow: 0 !important;
  -o-object-fit: cover;
     object-fit: cover;
  background-color: #f0f0f0;
  display: block !important;
  box-sizing: border-box;
  border: 2px solid #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.reply-images.data-v-53009239 {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 8px 0;
}
.reply-image.data-v-53009239 {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  -o-object-fit: cover;
     object-fit: cover;
  cursor: pointer;
  border: 1px solid #e0e0e0;
}
.reply-input-images.data-v-53009239 {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin: 8px 16px 16px 16px;
  max-width: calc(100% - 32px);
  justify-content: flex-start;
  padding: 8px;
}
.reply-input-image.data-v-53009239 {
  position: relative;
  display: inline-block;
  flex-shrink: 0;
  width: calc(33.333% - 4px);
  max-width: 70px;
  cursor: pointer;
}
.input-image-preview.data-v-53009239 {
  width: 100%;
  height: 70px;
  border-radius: 6px;
  -o-object-fit: cover;
     object-fit: cover;
  border: 1px solid #e0e0e0;
  display: block;
  transition: transform 0.2s ease;
}
.input-image-preview.data-v-53009239:hover {
  transform: scale(1.05);
  border-color: #07C160;
}
.remove-image.data-v-53009239 {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 16px;
  height: 16px;
  background: #ff4757;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  cursor: pointer;
  font-weight: bold;
  line-height: 1;
  border: 1px solid white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  z-index: 10;
}
.remove-image.data-v-53009239:hover {
  background: #ff3742;
  transform: scale(1.1);
}
@keyframes slideDown-53009239 {
from {
    transform: translateY(-100%);
    opacity: 0;
}
to {
    transform: translateY(0);
    opacity: 1;
}
}
.reply-content-text.data-v-53009239 {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 8px;
  max-width: calc(100% - 60px);
}
.cancel-reply.data-v-53009239 {
  color: #07C160;
  cursor: pointer;
  flex-shrink: 0;
  font-weight: 500;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}
.cancel-reply.data-v-53009239:hover {
  background: rgba(7, 193, 96, 0.1);
}
.reply-input-area.data-v-53009239 {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-width: 0;
}
.reply-textarea.data-v-53009239 {
  width: 100% !important;
  min-height: 36px !important;
  max-height: 100px !important;
  padding: 10px 12px !important;
  border-radius: 18px !important;
  border: 1px solid #e0e0e0;
  background: #f8f9fa;
  resize: none !important;
  font-size: 14px;
  box-sizing: border-box !important;
  overflow-y: auto;
  line-height: 18px !important;
  vertical-align: top !important;
  word-wrap: break-word !important;
  white-space: pre-wrap !important;
  transition: border-color 0.2s ease;
}
.reply-textarea.data-v-53009239:focus {
  border-color: #07C160;
  background: #fff;
  outline: none;
}
.upload-button.data-v-53009239 {
  width: 28px;
  height: 28px;
  border-radius: 6px;
  background: #f0f0f0;
  -o-object-fit: cover;
     object-fit: cover;
  flex-shrink: 0;
  flex-grow: 0;
  transition: background-color 0.2s ease;
}
.upload-button.data-v-53009239:hover {
  background: #e0e0e0;
}
.sub-reply.data-v-53009239 {
  background: #07C160;
  color: #fff;
  border: none;
  border-radius: 18px;
  font-size: 14px;
  cursor: pointer;
  height: 36px;
  padding: 0 16px;
  flex-shrink: 0;
  flex-grow: 0;
  transition: background-color 0.2s ease;
}
.sub-reply.data-v-53009239:hover {
  background: #06a84f;
}
.sub-reply.data-v-53009239:active {
  background: #059142;
}
.sub-reply.disabled.data-v-53009239 {
  background: #cccccc;
  color: #999999;
  cursor: not-allowed;
}
.sub-reply.disabled.data-v-53009239:hover {
  background: #cccccc;
}
.sub-reply.disabled.data-v-53009239:active {
  background: #cccccc;
}