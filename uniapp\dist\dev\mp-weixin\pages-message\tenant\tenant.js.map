{"version": 3, "file": "tenant.js", "sources": ["../../../../../src/pages-message/tenant/tenant.vue", "../../../../../uniPage:/cGFnZXMtbWVzc2FnZVx0ZW5hbnRcdGVuYW50LnZ1ZQ"], "sourcesContent": ["\r\n<template>\n<layout-default-uni >\r\n  <PageLayout :navTitle=\"title\" backRouteName=\"message\" routeMethod=\"pushTab\">\r\n    <wd-tabs customClass=\"\" v-model=\"tabActive\">\r\n      <template v-for=\"(item, index) in tabList\" :key=\"index\">\r\n        <wd-tab :title=\"item.title\" :name=\"item.key\">\r\n          <workmate\r\n            v-if=\"tabActive == '1'\"\r\n            :tenantId=\"tenantId\"\r\n            :key=\"reloadKey\"\r\n            :workType=\"workType\"\r\n          ></workmate>\r\n          <department v-if=\"tabActive == '2'\" :tenantId=\"tenantId\"></department>\r\n        </wd-tab>\r\n      </template>\r\n    </wd-tabs>\r\n    <template #navRight>\r\n      <view\r\n        class=\"cuIcon-filter font-size-20px color-white\"\r\n        @click=\"() => (conditionFilter.show = true)\"\r\n      ></view>\r\n    </template>\r\n    <rightConditionFilter\r\n      v-if=\"conditionFilter.show\"\r\n      v-bind=\"conditionFilter\"\r\n      @close=\"() => (conditionFilter.show = false)\"\r\n      @change=\"handleChange\"\r\n    ></rightConditionFilter>\r\n  </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'\r\nimport { ref } from 'vue'\r\nimport workmate from './components/workmate.vue'\r\nimport department from './components/department.vue'\r\nimport rightConditionFilter from '@/components/RightConditionFilter/RightConditionFilter.vue'\r\n\r\ndefineOptions({\r\n  name: 'tenant',\r\n  options: {\r\n    // apply-shared‌：当前页面样式会影响到子组件样式.(小程序)\r\n    // shared‌：当前页面样式影响到子组件，子组件样式也会影响到当前页面.(小程序)\r\n    styleIsolation: '‌shared‌',\r\n  },\r\n})\r\nconst reloadKey = ref(1)\r\nconst tabList = ref([\r\n  { key: '1', title: '全部' },\r\n  { key: '2', title: '按部门' },\r\n])\r\nconst conditionFilter = reactive({\r\n  show: false,\r\n  checked: 'all',\r\n  options: [\r\n    { key: 'all', title: '所有同事' },\r\n    { key: 'group', title: '群组' },\r\n  ],\r\n})\r\nconst tabActive = ref('2')\r\nconst tenantId = ref()\r\nconst title = ref()\r\nconst workType = ref('')\r\nconst handleChange = ({ option }) => {\r\n  conditionFilter.checked = option.key\r\n  if (option.key == 'all') {\r\n    tabList.value[1]['title'] = '按部门'\r\n    workType.value = ''\r\n  } else if (option.key == 'group') {\r\n    tabList.value[1]['title'] = '我创建的'\r\n    if (tabActive.value == '1') {\r\n      workType.value = 'allGroup'\r\n    } else {\r\n      workType.value = 'createdGroup'\r\n    }\r\n  }\r\n  tabList.value = [...tabList.value]\r\n\r\n  // 重新加载\r\n  reloadKey.value = Math.random()\r\n}\r\nconst init = () => {}\r\ninit()\r\nonLoad((options) => {\r\n  tenantId.value = options.id\r\n  title.value = options.title\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n:deep(.wd-tabs) {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  .wd-tabs__nav {\r\n    border-bottom: 1px solid #f1f1f1;\r\n  }\r\n  .wd-tabs__container {\r\n    flex: 1;\r\n    width: 100%;\r\n  }\r\n  .wd-tabs__body {\r\n    position: relative;\r\n  }\r\n}\r\n:deep(.wd-tab) {\r\n  .wd-tab__body {\r\n    position: absolute;\r\n    height: 100%;\r\n    width: 100%;\r\n    top: 0;\r\n    left: 0;\r\n    background-color: #f1f1f1;\r\n  }\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-message/tenant/tenant.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "onLoad"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA,MAAA,WAAqB,MAAA;AACrB,MAAA,aAAuB,MAAA;AACvB,MAAA,uBAAiC,MAAA;;;;;;;;;;;AAU3B,UAAA,YAAYA,kBAAI,CAAC;AACvB,UAAM,UAAUA,cAAAA,IAAI;AAAA,MAClB,EAAE,KAAK,KAAK,OAAO,KAAK;AAAA,MACxB,EAAE,KAAK,KAAK,OAAO,MAAM;AAAA,IAAA,CAC1B;AACD,UAAM,kBAAkBC,cAAAA,SAAS;AAAA,MAC/B,MAAM;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,QACP,EAAE,KAAK,OAAO,OAAO,OAAO;AAAA,QAC5B,EAAE,KAAK,SAAS,OAAO,KAAK;AAAA,MAAA;AAAA,IAC9B,CACD;AACK,UAAA,YAAYD,kBAAI,GAAG;AACzB,UAAM,WAAWA,cAAAA,IAAI;AACrB,UAAM,QAAQA,cAAAA,IAAI;AACZ,UAAA,WAAWA,kBAAI,EAAE;AACvB,UAAM,eAAe,CAAC,EAAE,aAAa;AACnC,sBAAgB,UAAU,OAAO;AAC7B,UAAA,OAAO,OAAO,OAAO;AACvB,gBAAQ,MAAM,CAAC,EAAE,OAAO,IAAI;AAC5B,iBAAS,QAAQ;AAAA,MAAA,WACR,OAAO,OAAO,SAAS;AAChC,gBAAQ,MAAM,CAAC,EAAE,OAAO,IAAI;AACxB,YAAA,UAAU,SAAS,KAAK;AAC1B,mBAAS,QAAQ;AAAA,QAAA,OACZ;AACL,mBAAS,QAAQ;AAAA,QAAA;AAAA,MACnB;AAEF,cAAQ,QAAQ,CAAC,GAAG,QAAQ,KAAK;AAGvB,gBAAA,QAAQ,KAAK,OAAO;AAAA,IAChC;AAGAE,kBAAA,OAAO,CAAC,YAAY;AAClB,eAAS,QAAQ,QAAQ;AACzB,YAAM,QAAQ,QAAQ;AAAA,IAAA,CACvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvFD,GAAG,WAAW,eAAe;"}