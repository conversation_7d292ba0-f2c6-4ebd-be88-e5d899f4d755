"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../../common/vendor.js");
if (!Math) {
  wdIcon();
}
const wdIcon = () => "../wd-icon/wd-icon.js";
const __default__ = {
  name: "wd-tag",
  options: {
    addGlobalClass: true,
    virtualHost: true,
    styleIsolation: "shared"
  }
};
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, __default__), {
  props: common_vendor.tagProps,
  emits: ["click", "close", "confirm"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const { translate } = common_vendor.useTranslate("tag");
    const tagClass = common_vendor.ref("");
    const dynamicValue = common_vendor.ref("");
    const dynamicInput = common_vendor.ref(false);
    common_vendor.watch(
      [() => props.useIconSlot, () => props.icon, () => props.plain, () => props.dynamic, () => props.round, () => props.mark],
      () => {
        computeTagClass();
      },
      { deep: true, immediate: true }
    );
    common_vendor.watch(
      () => props.type,
      (newValue) => {
        if (!newValue)
          return;
        const type = ["primary", "danger", "warning", "success", "default"];
        if (type.indexOf(newValue) === -1)
          console.error(`type must be one of ${type.toString()}`);
        computeTagClass();
      },
      { deep: true, immediate: true }
    );
    common_vendor.watch(
      () => dynamicInput.value,
      () => {
        computeTagClass();
      },
      { deep: true, immediate: true }
    );
    const rootClass = common_vendor.computed(() => {
      return `wd-tag ${props.customClass} ${tagClass.value}`;
    });
    const rootStyle = common_vendor.computed(() => {
      const rootStyle2 = {};
      if (!props.plain && props.bgColor) {
        rootStyle2["background"] = props.bgColor;
      }
      if (props.bgColor) {
        rootStyle2["border-color"] = props.bgColor;
      }
      return `${common_vendor.objToStyle(rootStyle2)}${props.customStyle}`;
    });
    const textStyle = common_vendor.computed(() => {
      const textStyle2 = {};
      if (props.color) {
        textStyle2["color"] = props.color;
      }
      return common_vendor.objToStyle(textStyle2);
    });
    function computeTagClass() {
      const { type, plain, round, mark, dynamic, icon, useIconSlot } = props;
      let tagClassList = [];
      type && tagClassList.push(`is-${type}`);
      plain && tagClassList.push("is-plain");
      round && tagClassList.push("is-round");
      mark && tagClassList.push("is-mark");
      dynamic && tagClassList.push("is-dynamic");
      dynamicInput.value && tagClassList.push("is-dynamic-input");
      if (icon || useIconSlot)
        tagClassList.push("is-icon");
      tagClass.value = tagClassList.join(" ");
    }
    function handleClick(event) {
      emit("click", event);
    }
    function handleClose(event) {
      emit("close", event);
    }
    function handleAdd() {
      dynamicInput.value = true;
      dynamicValue.value = "";
    }
    function handleBlur() {
      setDynamicInput();
    }
    function handleConfirm(event) {
      setDynamicInput();
      emit("confirm", {
        value: event.detail.value
      });
    }
    function setDynamicInput() {
      dynamicInput.value = false;
    }
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: _ctx.useIconSlot
      }, _ctx.useIconSlot ? {} : _ctx.icon ? {
        c: common_vendor.p({
          name: _ctx.icon,
          ["custom-class"]: "wd-tag__icon"
        })
      } : {}, {
        b: _ctx.icon,
        d: common_vendor.s(textStyle.value),
        e: _ctx.closable && _ctx.round
      }, _ctx.closable && _ctx.round ? {
        f: common_vendor.p({
          name: "error-fill"
        }),
        g: common_vendor.o(handleClose)
      } : {}, {
        h: dynamicInput.value && _ctx.dynamic
      }, dynamicInput.value && _ctx.dynamic ? {
        i: common_vendor.unref(translate)("placeholder"),
        j: common_vendor.o(handleBlur),
        k: common_vendor.o(handleConfirm),
        l: dynamicValue.value,
        m: common_vendor.o(($event) => dynamicValue.value = $event.detail.value)
      } : _ctx.dynamic ? common_vendor.e$1({
        o: _ctx.$slots.add
      }, _ctx.$slots.add ? {} : {
        p: common_vendor.p({
          name: "add",
          ["custom-class"]: "wd-tag__add wd-tag__icon"
        }),
        q: common_vendor.t(common_vendor.unref(translate)("add"))
      }, {
        r: common_vendor.s(textStyle.value),
        s: common_vendor.o(handleAdd)
      }) : {}, {
        n: _ctx.dynamic,
        t: common_vendor.n(rootClass.value),
        v: common_vendor.s(rootStyle.value),
        w: common_vendor.o(handleClick)
      });
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-14921b29"]]);
wx.createComponent(Component);
//# sourceMappingURL=wd-tag.js.map
