"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const store_user = require("../../store/user.js");
if (!Array) {
  const _component_NavBar = common_vendor.resolveComponent("NavBar");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_component_NavBar + _easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  _easycom_PageLayout();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "SoicalRegistration",
  options: {
    styleIsolation: "shared"
  }
}), {
  __name: "form",
  setup(__props) {
    const userStore = store_user.useUserStore();
    const toast = common_vendor.useToast();
    const submitting = common_vendor.ref(false);
    const formData = common_vendor.ref({
      name: "",
      community: ""
    });
    const validateForm = () => {
      if (!formData.value.name.trim()) {
        toast.error("请输入姓名");
        return false;
      }
      if (!formData.value.community.trim()) {
        toast.error("请输入社区");
        return false;
      }
      return true;
    };
    const handleSubmit = () => __async(this, null, function* () {
      var _a, _b;
      if (!validateForm()) {
        return;
      }
      submitting.value = true;
      try {
        const response = yield common_vendor.index.request({
          url: `${"https://www.mograine.cn/api"}/sys/user/2/addApply`,
          method: "POST",
          header: {
            "X-Access-Token": userStore.userInfo.token
          },
          data: {
            userId: userStore.userInfo.userid,
            userName: formData.value.name,
            community: formData.value.community,
            phone: userStore.userInfo.phone
          }
        });
        if ((_a = response.data) == null ? void 0 : _a.success) {
          common_vendor.index.showModal({
            title: "提交注册申请成功",
            content: "提交注册申请成功！",
            showCancel: false,
            success: () => {
              common_vendor.index.navigateBack();
            }
          });
        } else {
          const errorMsg = ((_b = response.data) == null ? void 0 : _b.message) || "提交注册申请失败，请稍后重试";
          toast.error(errorMsg);
        }
      } catch (error) {
        console.error("提交注册申请失败:", error);
        toast.error("网络错误，请稍后重试");
      } finally {
        submitting.value = false;
      }
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          title: "社工/社区医生注册",
          showBack: true
        }),
        b: formData.value.name,
        c: common_vendor.o(($event) => formData.value.name = $event.detail.value),
        d: formData.value.community,
        e: common_vendor.o(($event) => formData.value.community = $event.detail.value),
        f: common_vendor.t(submitting.value ? "提交中..." : "提交"),
        g: common_vendor.o(handleSubmit),
        h: submitting.value
      };
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-db74f9c7"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=form.js.map
