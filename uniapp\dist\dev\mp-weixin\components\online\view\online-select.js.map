{"version": 3, "file": "online-select.js", "sources": ["../../../../../../src/components/online/view/online-select.vue", "../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvY29tcG9uZW50cy9vbmxpbmUvdmlldy9vbmxpbmUtc2VsZWN0LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <wd-picker\r\n    :label-width=\"labelWidth\"\r\n    :label=\"label\"\r\n    filterable\r\n    v-model=\"selected\"\r\n    :columns=\"options\"\r\n    :disabled=\"disabled\"\r\n    placeholder=\"请选择\"\r\n    @change=\"handleChange\"\r\n  ></wd-picker>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, watch, onMounted } from 'vue'\r\nimport { isArray, isString } from 'lodash'\r\nimport {http} from \"@/utils/http\"; // 假设使用 lodash 来判断类型\r\n\r\n// 定义 props\r\nconst props = defineProps({\r\n  dict: {\r\n    type: [Array, String],\r\n    default: () => [],\r\n    required: true,\r\n  },\r\n  label: {\r\n    type: String,\r\n    default: '',\r\n    required: false,\r\n  },\r\n  labelWidth: {\r\n    type: String,\r\n    default: '80px',\r\n    required: false,\r\n  },\r\n  name: {\r\n    type: String,\r\n    default: '',\r\n    required: false,\r\n  },\r\n  dictStr: {\r\n    type: String,\r\n    default: '',\r\n    required: false,\r\n  },\r\n  type: {\r\n    type: String,\r\n    default: '',\r\n    required: false,\r\n  },\r\n  value: {\r\n    type: [String, Number],\r\n    required: false,\r\n  },\r\n  disabled: {\r\n    type: Boolean,\r\n    default: false,\r\n    required: false,\r\n  },\r\n})\r\n\r\n// 定义 emits\r\nconst emit = defineEmits(['input', 'change', 'update:value'])\r\n\r\n// 定义响应式数据\r\nconst selected = ref('请选择')\r\nconst options = ref([])\r\n\r\n// 初始化选项\r\nconst initSelections = async () => {\r\n  options.value = []\r\n  if (props.type === 'sel_search' && props.dictStr) {\r\n    let temp = props.dictStr\r\n    if (temp.indexOf(' ') > 0) {\r\n      temp = encodeURI(props.dictStr)\r\n    }\r\n    try {\r\n      const res = await http.get('/sys/dict/getDictItems/' + temp)\r\n      if (res.success) {\r\n        options.value = res.result;\r\n      }\r\n    } catch (error) {\r\n      console.error('请求数据出错:', error)\r\n    }\r\n  }\r\n  else {\r\n    if (!props.dict || props.dict.length === 0) {\r\n      return\r\n    }\r\n    if (isString(props.dict)) {\r\n      try {\r\n        const res = await http.get('/sys/dict/getDictItems/' + props.dict)\r\n        if (res.success) {\r\n          options.value = res.result;\r\n        }\r\n      } catch (error) {\r\n        console.error('请求数据出错:', error)\r\n      }\r\n    } else {\r\n      props.dict.forEach((item) => {\r\n        options.value.push(item)\r\n      })\r\n    }\r\n  }\r\n}\r\n\r\n// 选择器改变事件处理函数\r\nconst handleChange = (e) => {\r\n  emit('update:value', selected.value);\r\n  emit('change', selected.value);\r\n}\r\n\r\n// 监听 dict 和 value 的变化\r\nwatch(() => props.dict, () => {\r\n    initSelections();\r\n});\r\n// 监听 value 的变化\r\nwatch(\r\n  () => props.value,\r\n  (val) => {\r\n      selected.value = !val? [] : props.value;\r\n  },\r\n  { immediate: true },\r\n)\r\n\r\n// 组件挂载时初始化选项\r\nonMounted(() => {\r\n  initSelections()\r\n})\r\n</script>\r\n\r\n<style></style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/components/online/view/online-select.vue'\nwx.createComponent(Component)"], "names": ["ref", "http", "isString", "watch", "onMounted", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBA,UAAM,QAAQ;AA2Cd,UAAM,OAAO;AAGb,UAAM,WAAWA,cAAG,IAAC,KAAK;AAC1B,UAAM,UAAUA,cAAG,IAAC,EAAE;AAGtB,UAAM,iBAAiB,MAAY;AACjC,cAAQ,QAAQ,CAAE;AAClB,UAAI,MAAM,SAAS,gBAAgB,MAAM,SAAS;AAChD,YAAI,OAAO,MAAM;AACjB,YAAI,KAAK,QAAQ,GAAG,IAAI,GAAG;AACzB,iBAAO,UAAU,MAAM,OAAO;AAAA,QAC/B;AACD,YAAI;AACF,gBAAM,MAAM,MAAMC,WAAAA,KAAK,IAAI,4BAA4B,IAAI;AAC3D,cAAI,IAAI,SAAS;AACf,oBAAQ,QAAQ,IAAI;AAAA,UACrB;AAAA,QACF,SAAQ,OAAO;AACd,kBAAQ,MAAM,WAAW,KAAK;AAAA,QAC/B;AAAA,MACF,OACI;AACH,YAAI,CAAC,MAAM,QAAQ,MAAM,KAAK,WAAW,GAAG;AAC1C;AAAA,QACD;AACD,YAAIC,cAAQ,cAAA,SAAC,MAAM,IAAI,GAAG;AACxB,cAAI;AACF,kBAAM,MAAM,MAAMD,WAAI,KAAC,IAAI,4BAA4B,MAAM,IAAI;AACjE,gBAAI,IAAI,SAAS;AACf,sBAAQ,QAAQ,IAAI;AAAA,YACrB;AAAA,UACF,SAAQ,OAAO;AACd,oBAAQ,MAAM,WAAW,KAAK;AAAA,UAC/B;AAAA,QACP,OAAW;AACL,gBAAM,KAAK,QAAQ,CAAC,SAAS;AAC3B,oBAAQ,MAAM,KAAK,IAAI;AAAA,UAC/B,CAAO;AAAA,QACF;AAAA,MACF;AAAA,IACH;AAGA,UAAM,eAAe,CAAC,MAAM;AAC1B,WAAK,gBAAgB,SAAS,KAAK;AACnC,WAAK,UAAU,SAAS,KAAK;AAAA,IAC/B;AAGAE,kBAAAA,MAAM,MAAM,MAAM,MAAM,MAAM;AAC1B;IACJ,CAAC;AAEDA,kBAAK;AAAA,MACH,MAAM,MAAM;AAAA,MACZ,CAAC,QAAQ;AACL,iBAAS,QAAQ,CAAC,MAAK,CAAA,IAAK,MAAM;AAAA,MACrC;AAAA,MACD,EAAE,WAAW,KAAM;AAAA,IACrB;AAGAC,kBAAAA,UAAU,MAAM;AACd,qBAAgB;AAAA,IAClB,CAAC;;;;;;;;;;;;;;;;;;AC/HD,GAAG,gBAAgBC,SAAS;"}