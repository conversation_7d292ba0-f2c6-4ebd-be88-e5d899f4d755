{"version": 3, "file": "chat-item.js", "sources": ["../../../../../../src/pages-message/chat/components/chat-item.vue", "../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvcGFnZXMtbWVzc2FnZS9jaGF0L2NvbXBvbmVudHMvY2hhdC1pdGVtLnZ1ZQ"], "sourcesContent": ["<!-- z-paging聊天item -->\r\n\r\n<template>\r\n  <view class=\"chat-item\">\r\n    <text class=\"chat-time\" v-if=\"item.sendTime && item.sendTime.length\">\r\n      {{ item.sendTime }}\r\n    </text>\r\n    <view :class=\"{ 'chat-container': true, 'chat-location-me': isMe(item) }\">\r\n      <view class=\"chat-icon-container\">\r\n        <image class=\"chat-icon\" :src=\"item.fromAvatar\" mode=\"aspectFill\" />\r\n      </view>\r\n      <view class=\"chat-content-container\">\r\n        <text :class=\"{ 'chat-user-name': true, 'chat-location-me': isMe(item) }\">\r\n          {{ item.fromUserName }}\r\n        </text>\r\n        <view\r\n          :class=\"{\r\n            'chat-text-container-super': true,\r\n            'flex-end': isMe(item),\r\n            'flex-start': !isMe(item),\r\n          }\"\r\n        >\r\n          <!---文字-->\r\n          <template v-if=\"['text'].includes(item.msgType)\">\r\n            <view :class=\"{ 'chat-text-container': true, 'chat-text-container-me': isMe(item) }\">\r\n              <text :class=\"{ 'chat-text': true, 'chat-text-me': isMe(item) }\">\r\n                <rich-text :nodes=\"item.msgData\"></rich-text>\r\n              </text>\r\n            </view>\r\n          </template>\r\n          <!--图片-->\r\n          <template v-else-if=\"['image'].includes(item.msgType)\">\r\n            <wd-img\r\n              width=\"200\"\r\n              height=\"200\"\r\n              :enable-preview=\"true\"\r\n              :radius=\"10\"\r\n              :src=\"getFileAccessHttpUrl(item.msgData)\"\r\n            ></wd-img>\r\n          </template>\r\n          <!--语音-->\r\n          <template v-else-if=\"['voice'].includes(item.msgType)\">\r\n            <view\r\n              :class=\"{\r\n                'chat-voice-container': true,\r\n                'chat-voice-container-me': isMe(item),\r\n                play: playMsgid == item.id,\r\n              }\"\r\n              @click=\"playVoice(item)\"\r\n            >\r\n              <view class=\"length mr-2\">{{ item.msgData.length }}</view>\r\n              <view class=\"icon my-voice\"></view>\r\n            </view>\r\n          </template>\r\n          <!--文件-->\r\n          <template v-else-if=\"['file'].includes(item.msgType)\"></template>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { useUserStore } from '@/store/user'\r\nimport { cache, getFileAccessHttpUrl } from '@/common/uitls'\r\n\r\ndefineOptions({ name: 'chat-item' })\r\n\r\nconst userStore = useUserStore()\r\nconst emit = defineEmits(['playVoice'])\r\nconst props = defineProps({\r\n  playMsgid: {\r\n    type: String,\r\n    default: '',\r\n  },\r\n  item: {\r\n    type: Object,\r\n    default: function () {\r\n      return {\r\n        sendTime: '',\r\n        fromAvatar: '',\r\n        fromUserName: '',\r\n        msgData: '',\r\n      }\r\n    },\r\n  },\r\n})\r\nconst isMe = (item) => {\r\n  return item.msgFrom == userStore.userInfo.userid\r\n}\r\nconst playVoice = (item) => {\r\n  emit('playVoice', item)\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.chat-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 20upx;\r\n}\r\n.chat-time {\r\n  padding: 4upx 0upx;\r\n  text-align: center;\r\n  font-size: 22upx;\r\n  color: #aaaaaa;\r\n}\r\n.chat-container {\r\n  display: flex;\r\n  flex-direction: row;\r\n}\r\n.chat-location-me {\r\n  flex-direction: row-reverse;\r\n  text-align: right;\r\n}\r\n.chat-icon-container {\r\n  margin-top: 12upx;\r\n}\r\n.chat-icon {\r\n  width: 80upx;\r\n  height: 80upx;\r\n  border-radius: 8px;\r\n  background-color: #eeeeee;\r\n}\r\n.chat-content-container {\r\n  margin: 0upx 15upx;\r\n}\r\n.chat-user-name {\r\n  font-size: 26upx;\r\n  color: #888888;\r\n}\r\n.chat-text-container,\r\n.chat-voice-container {\r\n  text-align: left;\r\n  background-color: #fff;\r\n  border-radius: 8upx;\r\n  padding: 7px 10px;\r\n  margin-top: 10upx;\r\n\r\n  max-width: 500upx;\r\n\r\n}\r\n.chat-text-container-me,\r\n.chat-voice-container {\r\n  background-color: #55aaff;\r\n}\r\n.chat-voice-container {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #fff;\r\n}\r\n.chat-text-container-super {\r\n  display: flex;\r\n  flex-direction: row;\r\n  &.flex-end {\r\n    justify-content: flex-end;\r\n  }\r\n  &.flex-start {\r\n    justify-content: flex-start;\r\n  }\r\n}\r\n.chat-text {\r\n  font-size: 28upx;\r\n\r\n  word-break: break-all;\r\n\r\n\r\n\r\n\r\n}\r\n.chat-text-me {\r\n  color: white;\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-message/chat/components/chat-item.vue'\nwx.createComponent(Component)"], "names": ["useUserStore"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoEA,UAAM,YAAYA,WAAAA,aAAa;AAC/B,UAAM,OAAO;AAkBP,UAAA,OAAO,CAAC,SAAS;AACd,aAAA,KAAK,WAAW,UAAU,SAAS;AAAA,IAC5C;AACM,UAAA,YAAY,CAAC,SAAS;AAC1B,WAAK,aAAa,IAAI;AAAA,IACxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3FA,GAAG,gBAAgB,SAAS;"}