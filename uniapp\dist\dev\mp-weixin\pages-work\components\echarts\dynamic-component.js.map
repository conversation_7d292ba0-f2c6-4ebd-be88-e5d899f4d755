{"version": 3, "file": "dynamic-component.js", "sources": ["../../../../../../src/pages-work/components/echarts/dynamic-component.vue", "../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvcGFnZXMtd29yay9jb21wb25lbnRzL2VjaGFydHMvZHluYW1pYy1jb21wb25lbnQudnVl"], "sourcesContent": ["<template>\r\n\t<view>\r\n\t   <!-- 非echart组件 -->\r\n\t  <!-- <JCustomButton v-model:compName=\"compName\" v-model:id=\"i\" :appId=\"appId\" v-model:config=\"config\" v-if=\"this.compName.indexOf('JCustomButton')>=0\"></JCustomButton>\r\n\t   <JCarousel v-model:compName=\"compName\" v-model:id=\"i\" :appId=\"appId\" v-model:config=\"config\" v-if=\"this.compName.indexOf('JCarousel')>=0\"></JCarousel>\r\n\t   <JDragEditor v-model:compName=\"compName\" v-model:id=\"i\" :appId=\"appId\" v-model:config=\"config\" v-if=\"this.compName.indexOf('JDragEditor')>=0\"></JDragEditor>\r\n\t   <JIframe v-model:compName=\"compName\" v-model:id=\"i\" :appId=\"appId\" v-model:config=\"config\" v-if=\"this.compName.indexOf('JIframe')>=0\"></JIframe>\r\n\t   <JNumber v-model:compName=\"compName\" v-model:id=\"i\" :appId=\"appId\" v-model:config=\"config\" v-if=\"this.compName.indexOf('JNumber')>=0\"></JNumber>\r\n\t   <JPivotTable v-model:compName=\"compName\" v-model:id=\"i\" :appId=\"appId\" v-model:config=\"config\" v-if=\"this.compName.indexOf('JPivotTable')>=0\"></JPivotTable>\r\n\t   <JText v-model:compName=\"compName\" v-model:id=\"i\" :appId=\"appId\" v-model:config=\"config\" v-if=\"this.compName.indexOf('JText')>=0\"></JText>\r\n\t   <JFilterQuery v-model:compName=\"compName\" v-model:id=\"i\" :appId=\"appId\" v-model:config=\"config\" v-if=\"this.compName.indexOf('JFilterQuery')>=0\"></JFilterQuery>\r\n\t  -->\r\n\t  <!-- echart组件 -->\r\n\t   <JBar :compName=\"compName\" :i=\"i\"  :config=\"config\" v-if=\"comp=='JBar'\"></JBar>\r\n\t   <JBackgroundBar :compName=\"compName\" :i=\"i\"  :config=\"config\" v-if=\"comp=='JBackgroundBar'\"></JBackgroundBar>\r\n\t   <JDynamicBar :compName=\"compName\" :i=\"i\"  :config=\"config\" v-if=\"comp=='JDynamicBar'\"></JDynamicBar>\r\n\t   <JMixLineBar :compName=\"compName\" :i=\"i\"  :config=\"config\" v-if=\"comp=='JMixLineBar'\"></JMixLineBar>\r\n\t   <JStackBar :compName=\"compName\" :i=\"i\" :config=\"config\" v-else-if=\"comp.indexOf('JStackBar')>=0\"></JStackBar>\r\n\t   <JMultipleBar :compName=\"compName\" :i=\"i\"  :config=\"config\" v-else-if=\"comp.indexOf('JMultipleBar')>=0\"></JMultipleBar>\r\n\t   <JNegativeBar :compName=\"compName\" :i=\"i\"  :config=\"config\" v-else-if=\"comp.indexOf('JNegativeBar')>=0\"></JNegativeBar>\r\n\t   <JLine :compName=\"compName\" :i=\"i\"  :config=\"config\" v-else-if=\"comp.indexOf('JLine')>=0\"></JLine>\r\n\t   <JStepLine :compName=\"compName\" :i=\"i\"  :config=\"config\" v-else-if=\"comp.indexOf('JStepLine')>=0\"></JStepLine>\r\n\t   <JSmoothLine :compName=\"compName\" :i=\"i\"  :config=\"config\" v-else-if=\"comp.indexOf('JSmoothLine')>=0\"></JSmoothLine>\r\n\t   <JMultipleLine :compName=\"compName\" :i=\"i\"  :config=\"config\" v-else-if=\"comp.indexOf('JMultipleLine')>=0\"></JMultipleLine>\r\n\t   <JPie :compName=\"compName\" :i=\"i\"  :config=\"config\" v-else-if=\"comp.indexOf('JPie')>=0\"></JPie>\r\n\t   <JRing :compName=\"compName\" :i=\"i\"  :config=\"config\" v-else-if=\"comp.indexOf('JRing')>=0\"></JRing>\r\n\t   <JFunnel :compName=\"compName\" :i=\"i\"  :config=\"config\" v-else-if=\"comp.indexOf('JFunnel')>=0\"></JFunnel>\r\n\t   <JPyramidFunnel :compName=\"compName\" :i=\"i\"  :config=\"config\" v-else-if=\"comp.indexOf('JPyramidFunnel')>=0\"></JPyramidFunnel>\r\n\t   <JRadar :compName=\"compName\" :i=\"i\"  :config=\"config\" v-else-if=\"comp.indexOf('JRadar')>=0\"></JRadar>\r\n\t   <JCircleRadar :compName=\"compName\" :i=\"i\"  :config=\"config\" v-else-if=\"comp.indexOf('JCircleRadar')>=0\"></JCircleRadar>\r\n\t   <JGauge :compName=\"compName\" :i=\"i\"  :config=\"config\" v-else-if=\"comp.indexOf('JGauge')>=0\"></JGauge>\r\n\t   <JColorGauge :compName=\"compName\" :i=\"i\"  :config=\"config\" v-else-if=\"comp.indexOf('JColorGauge')>=0\"></JColorGauge>\r\n\t   <JScatter :compName=\"compName\" :i=\"i\"  :config=\"config\" v-else-if=\"comp.indexOf('JScatter')>=0\"></JScatter>\r\n\t   <JBubble :compName=\"compName\" :i=\"i\"  :config=\"config\" v-else-if=\"comp=='JBubble'\"></JBubble>\r\n\t   <DoubleLineBar :compName=\"compName\" :i=\"i\"  :config=\"config\" v-else-if=\"comp.indexOf('DoubleLineBar')>=0\"></DoubleLineBar>\r\n\t   <JRose :compName=\"compName\" :i=\"i\"  :config=\"config\" v-else-if=\"comp.indexOf('JRose')>=0\"></JRose>\r\n\t   <JHorizontalBar :compName=\"compName\" :i=\"i\"  :config=\"config\" v-if=\"comp.indexOf('JHorizontalBar')>=0\"></JHorizontalBar>\r\n\t   <JArea :compName=\"compName\" :i=\"i\"  :config=\"config\" v-else-if=\"comp=='JArea'\"></JArea>\r\n\t   <JPictorial :compName=\"compName\" :i=\"i\"  :config=\"config\" v-else-if=\"comp=='JPictorial'\"></JPictorial>\r\n\t   <JPictorialBar :compName=\"compName\" :i=\"i\"  :config=\"config\" v-else-if=\"comp=='JPictorialBar'\"></JPictorialBar>\r\n       <!-- echart地图组件 -->\r\n\t   <JBubbleMap :compName=\"compName\" :i=\"i\" :config=\"config\" v-else-if=\"comp=='JBubbleMap'\"></JBubbleMap>\r\n\t   <JBarMap :compName=\"compName\" :i=\"i\" :config=\"config\" v-else-if=\"comp.indexOf('JBarMap')>=0\"></JBarMap>\r\n\t   <JHeatMap :compName=\"compName\" :i=\"i\" :config=\"config\" v-else-if=\"comp.indexOf('JHeatMap')>=0\"></JHeatMap>\r\n\t   <JAreaMap :compName=\"compName\" :i=\"i\" :config=\"config\" v-else-if=\"comp.indexOf('JAreaMap')>=0\"></JAreaMap>\r\n  </view>\r\n</template>\r\n<script setup>\r\n//echart\r\nimport JBar from '@/pages-work/components/echarts/JBar/index.vue'\r\nimport JStackBar from '@/pages-work/components/echarts/JStackBar/index.vue'\r\nimport JMultipleBar from '@/pages-work/components/echarts/JMultipleBar/index.vue'\r\nimport JNegativeBar from '@/pages-work/components/echarts/JNegativeBar/index.vue'\r\nimport JBackgroundBar from '@/pages-work/components/echarts/JBackgroundBar/index.vue';\r\nimport JDynamicBar from '@/pages-work/components/echarts/JDynamicBar/index.vue';\r\nimport JMixLineBar from '@/pages-work/components/echarts/JMixLineBar/index.vue';\r\nimport JStepLine from '@/pages-work/components/echarts/JStepLine/index.vue';\r\nimport JSmoothLine from '@/pages-work/components/echarts/JSmoothLine/index.vue';\r\nimport JLine from '@/pages-work/components/echarts/JLine/index.vue'\r\nimport JMultipleLine from '@/pages-work/components/echarts/JMultipleLine/index.vue'\r\nimport JPie from '@/pages-work/components/echarts/JPie/index.vue'\r\nimport JRing from '@/pages-work/components/echarts/JRing/index.vue'\r\nimport JFunnel from '@/pages-work/components/echarts/JFunnel/index.vue'\r\nimport JPyramidFunnel from '@/pages-work/components/echarts/JPyramidFunnel/index.vue'\r\nimport JRadar from '@/pages-work/components/echarts/JRadar/index.vue'\r\nimport JCircleRadar from '@/pages-work/components/echarts/JCircleRadar/index.vue'\r\nimport JGauge from '@/pages-work/components/echarts/JGauge/index.vue'\r\nimport JColorGauge from '@/pages-work/components/echarts/JColorGauge/index.vue'\r\nimport JScatter from '@/pages-work/components/echarts/JScatter/index.vue'\r\nimport JBubble from '@/pages-work/components/echarts/JBubble/index.vue'\r\nimport DoubleLineBar from '@/pages-work/components/echarts/DoubleLineBar/index.vue'\r\nimport JRose from '@/pages-work/components/echarts/JRose/index.vue'\r\nimport JHorizontalBar from '@/pages-work/components/echarts/JHorizontalBar/index.vue'\r\nimport JArea from '@/pages-work/components/echarts/JArea/index.vue'\r\nimport JBubbleMap from '@/pages-work/components/echarts/map/JBubbleMap/index.vue'\r\nimport JBarMap from '@/pages-work/components/echarts/map/JBarMap/index.vue'\r\nimport JHeatMap from '@/pages-work/components/echarts/map/JHeatMap/index.vue'\r\nimport JAreaMap from '@/pages-work/components/echarts/map/JAreaMap/index.vue'\r\nimport JPictorial from '@/pages-work/components/echarts/JPictorial/index.vue';\r\nimport JPictorialBar from '@/pages-work/components/echarts/JPictorialBar/index.vue';\r\n\r\n\t //接收参数\r\n\t const props = defineProps({\r\n\t\tcompName: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\ti: {\r\n\t\t\ttype: [String,Number],\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\tconfig: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault: () => {\r\n\t\t\t\treturn {}\r\n\t\t\t}\r\n\t\t}\r\n\t})\r\n\t //动态的组件名称\r\n\t const comp = computed(() => {\r\n\t\tconsole.log(\"组件名称：compName:\",props.compName);\r\n\t\tconsole.log(\"组件数据：config:\",props.config);\r\n\t\treturn props.compName\r\n\t})\r\n\r\n</script>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-work/components/echarts/dynamic-component.vue'\nwx.createComponent(Component)"], "names": ["computed", "Component"], "mappings": ";;;;;AAiDA,MAAM,OAAO,MAAW;AACxB,MAAM,YAAY,MAAW;AAC7B,MAAM,eAAe,MAAW;AAChC,MAAM,eAAe,MAAW;AAChC,MAAM,iBAAiB,MAAW;AAClC,MAAM,cAAc,MAAW;AAC/B,MAAM,cAAc,MAAW;AAC/B,MAAM,YAAY,MAAW;AAC7B,MAAM,cAAc,MAAW;AAC/B,MAAM,QAAQ,MAAW;AACzB,MAAM,gBAAgB,MAAW;AACjC,MAAM,OAAO,MAAW;AACxB,MAAM,QAAQ,MAAW;AACzB,MAAM,UAAU,MAAW;AAC3B,MAAM,iBAAiB,MAAW;AAClC,MAAM,SAAS,MAAW;AAC1B,MAAM,eAAe,MAAW;AAChC,MAAM,SAAS,MAAW;AAC1B,MAAM,cAAc,MAAW;AAC/B,MAAM,WAAW,MAAW;AAC5B,MAAM,UAAU,MAAW;AAC3B,MAAM,gBAAgB,MAAW;AACjC,MAAM,QAAQ,MAAW;AACzB,MAAM,iBAAiB,MAAW;AAClC,MAAM,QAAQ,MAAW;AACzB,MAAM,aAAa,MAAW;AAC9B,MAAM,UAAU,MAAW;AAC3B,MAAM,WAAW,MAAW;AAC5B,MAAM,WAAW,MAAW;AAC5B,MAAM,aAAa,MAAW;AAC9B,MAAM,gBAAgB,MAAW;;;;;;;;;;;;;;;;;;;;AAG/B,UAAM,QAAQ;AAiBd,UAAM,OAAOA,cAAAA,SAAS,MAAM;AAC5B,cAAQ,IAAI,kBAAiB,MAAM,QAAQ;AAC3C,cAAQ,IAAI,gBAAe,MAAM,MAAM;AACvC,aAAO,MAAM;AAAA,IACf,CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtGF,GAAG,gBAAgBC,SAAS;"}