"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../../common/vendor.js");
if (!Math) {
  wdImg();
}
const wdImg = () => "../wd-img/wd-img.js";
const __default__ = {
  name: "wd-status-tip",
  options: {
    addGlobalClass: true,
    virtualHost: true,
    styleIsolation: "shared"
  }
};
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, __default__), {
  props: common_vendor.statusTipProps,
  setup(__props) {
    const props = __props;
    const imgUrl = common_vendor.computed(() => {
      let img = "";
      if (["search", "network", "content", "collect", "comment", "halo", "message"].includes(props.image)) {
        img = `${props.urlPrefix}${props.image}.png`;
      } else {
        img = props.image;
      }
      return img;
    });
    const imgStyle = common_vendor.computed(() => {
      let style = {};
      if (props.imageSize) {
        if (common_vendor.isObj(props.imageSize)) {
          common_vendor.isDef(props.imageSize.height) && (style.height = common_vendor.addUnit(props.imageSize.height));
          common_vendor.isDef(props.imageSize.width) && (style.width = common_vendor.addUnit(props.imageSize.width));
        } else {
          style = {
            height: common_vendor.addUnit(props.imageSize),
            width: common_vendor.addUnit(props.imageSize)
          };
        }
      }
      return `${common_vendor.objToStyle(style)}`;
    });
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: _ctx.$slots.image
      }, _ctx.$slots.image ? {} : imgUrl.value ? {
        c: common_vendor.p({
          mode: _ctx.imageMode,
          src: imgUrl.value,
          ["custom-class"]: "wd-status-tip__image",
          ["custom-style"]: imgStyle.value
        })
      } : {}, {
        b: imgUrl.value,
        d: _ctx.tip
      }, _ctx.tip ? {
        e: common_vendor.t(_ctx.tip)
      } : {}, {
        f: common_vendor.n(`wd-status-tip  ${_ctx.customClass}`),
        g: common_vendor.s(_ctx.customStyle)
      });
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-1a3f88eb"]]);
wx.createComponent(Component);
//# sourceMappingURL=wd-status-tip.js.map
