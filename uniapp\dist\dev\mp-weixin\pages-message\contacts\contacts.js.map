{"version": 3, "file": "contacts.js", "sources": ["../../../../../src/pages-message/contacts/contacts.vue", "../../../../../uniPage:/cGFnZXMtbWVzc2FnZVxjb250YWN0c1xjb250YWN0cy52dWU"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n  <PageLayout navTitle=\"联系人\" backRouteName=\"message\" routeMethod=\"pushTab\">\r\n    <view class=\"wrap\">\r\n      <z-paging\r\n        ref=\"paging\"\r\n        :fixed=\"false\"\r\n        v-model=\"dataList\"\r\n        @query=\"queryList\"\r\n        :default-page-size=\"100\"\r\n      >\r\n        <template #top>\r\n          <wd-search\r\n            hide-cancel\r\n            placeholder=\"我要去哪里？\"\r\n            v-model=\"keyword\"\r\n            @search=\"handleSearch\"\r\n            @clear=\"handleClear\"\r\n          />\r\n        </template>\r\n        <view class=\"wraper\">\r\n          <wd-index-bar sticky v-if=\"dataSource.length\">\r\n            <view v-for=\"item in dataSource\" :key=\"item.index\">\r\n              <wd-index-anchor :index=\"item.index\" />\r\n              <wd-cell\r\n                border\r\n                clickable\r\n                v-for=\"inItem in item.data\"\r\n                :key=\"item.username\"\r\n                @click=\"handleClick(item.index, inItem)\"\r\n              >\r\n                <template #icon>\r\n                  <wd-img\r\n                    customClass=\"avatar\"\r\n                    :width=\"50\"\r\n                    :height=\"50\"\r\n                    :src=\"getFileAccessHttpUrl(inItem.avatar) || defaultAvatar\"\r\n                  ></wd-img>\r\n                </template>\r\n                <template #title>\r\n                  <view class=\"content text-gray-4\">\r\n                    <text>{{ inItem.realname }}</text>\r\n                    <text>{{ inItem.orgCodeTxt ?? '暂无' }}</text>\r\n                  </view>\r\n                </template>\r\n              </wd-cell>\r\n            </view>\r\n          </wd-index-bar>\r\n        </view>\r\n      </z-paging>\r\n    </view>\r\n    <template #navRight>\r\n      <view\r\n        class=\"cuIcon-filter font-size-20px color-white\"\r\n        @click=\"() => (conditionFilter.show = true)\"\r\n      ></view>\r\n    </template>\r\n    <rightConditionFilter\r\n      v-if=\"conditionFilter.show\"\r\n      v-bind=\"conditionFilter\"\r\n      @close=\"() => (conditionFilter.show = false)\"\r\n      @change=\"handleChange\"\r\n    ></rightConditionFilter>\r\n  </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { onLaunch, onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'\r\nimport { nextTick, onMounted, ref } from 'vue'\r\nimport { useUserStore } from '@/store/user'\r\nimport { http } from '@/utils/http'\r\nimport { useParamsStore } from '@/store/page-params'\r\nimport { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'\r\nimport { useRouter } from '@/plugin/uni-mini-router'\r\nimport { cache, getFileAccessHttpUrl, hasRoute } from '@/common/uitls'\r\nimport vPinyin from '../common/vue-py'\r\nimport rightConditionFilter from '@/components/RightConditionFilter/RightConditionFilter.vue'\r\nimport { TENANT_LIST } from '@/common/constants'\r\nimport defaultAvatar from '@/static/default-avatar.png';\r\n\r\nconst toast = useToast()\r\nconst userStore = useUserStore()\r\nconst paramsStore = useParamsStore()\r\nconst paging = ref(null)\r\nconst router = useRouter()\r\nconst dataList = ref([])\r\n// 接口拿到的数据处理之后的\r\nconst originData = ref([])\r\nconst keyword = ref('')\r\nconst dataSource = ref([])\r\nconst conditionFilter = reactive({ show: false, checked: '', options: [] })\r\n\r\nconst queryList = (pageNo, pageSize) => {\r\n  const pararms = { pageNo, pageSize, tenantId: conditionFilter.checked }\r\n  if (conditionFilter.checked === '') delete pararms.tenantId\r\n  http\r\n    .get('/sys/user/appQueryUser', pararms)\r\n    .then((res: any) => {\r\n      if (res.success && res.result.length) {\r\n        paging.value.complete(res.result)\r\n      } else {\r\n        paging.value.complete(false)\r\n      }\r\n    })\r\n    .catch((res) => {\r\n      paging.value.complete(false)\r\n    })\r\n}\r\n// 监听接口数据\r\nwatch(dataList, () => {\r\n  let result = handleResult(dataList.value)\r\n  result = transformData(result)\r\n  result.sort((a, b) => a.index.localeCompare(b.index))\r\n  originData.value = [...result]\r\n  dataSource.value = result\r\n  console.log('dataSource:::', dataSource.value)\r\n})\r\n\r\n// 搜索\r\nfunction handleSearch() {\r\n  dataSource.value = []\r\n  nextTick(() => {\r\n    if (keyword.value) {\r\n      dataSource.value = originData.value.filter((item) => {\r\n        return item.data.some((inItem) => {\r\n          return inItem.realname.indexOf(keyword.value) != -1\r\n        })\r\n      })\r\n    } else {\r\n      dataSource.value = originData.value\r\n    }\r\n  })\r\n}\r\n// 清除搜索条件\r\nfunction handleClear() {\r\n  keyword.value = ''\r\n  handleSearch()\r\n}\r\n\r\nconst handleClick = (index: string, data: any) => {\r\n  paramsStore.setPageParams('personPage', { backRouteName: 'contacts', data })\r\n  router.push({ name: 'personPage' })\r\n}\r\nconst handleChange = ({ option }) => {\r\n  conditionFilter.checked = option.key\r\n  paging.value.reload()\r\n}\r\nconst transformData = (data) => {\r\n  const grouped = data.reduce((acc, item) => {\r\n    const key = item.szm\r\n    if (!acc[key]) {\r\n      acc[key] = []\r\n    }\r\n    acc[key].push(item)\r\n    return acc\r\n  }, {})\r\n\r\n  return Object.entries(grouped)\r\n    .map(([index, data]) => ({ index, data }))\r\n    .sort((a, b) => b.index.localeCompare(a.index))\r\n}\r\nconst handleResult = (arr) => {\r\n  let newArr = []\r\n  arr.forEach((item) => {\r\n    let { id, realname, avatar, username, phone, email, post, orgCodeTxt } = item\r\n    //聊天通讯录把自己过滤掉\r\n    if (username !== userStore.userInfo.username) {\r\n      let pinYin = realname\r\n      if (realname) {\r\n        //TODO 判断汉字的位置\r\n        if (/.*[\\u4e00-\\u9fa5]+.*$/.test(realname)) {\r\n          pinYin = vPinyin.chineseToPinYin(realname)\r\n        }\r\n      }\r\n      if (avatar) {\r\n        avatar = getFileAccessHttpUrl(avatar)\r\n      }\r\n      let szm = pinYin.substr(0, 1)\r\n      var numReg = /^[0-9]*$/\r\n      var numRe = new RegExp(numReg)\r\n      szm = !numRe.test(szm) ? szm.toUpperCase() : '#'\r\n      newArr.push({\r\n        id,\r\n        realname,\r\n        avatar,\r\n        username,\r\n        phone,\r\n        email,\r\n        post,\r\n        orgCodeTxt,\r\n        szm: szm,\r\n      })\r\n    }\r\n  })\r\n  return newArr\r\n}\r\n\r\nonLoad(() => {\r\n  const tenantList = cache(TENANT_LIST)\r\n  const result = tenantList?.map((item) => {\r\n    return { key: item.id, title: item.name }\r\n  })\r\n  conditionFilter.options = [{ key: '', title: '全部' }, ...result]\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.wrap {\r\n  height: 100%;\r\n}\r\n.z-paging-content {\r\n  // :deep(.zp-paging-container) {\r\n  //   flex: none;\r\n  //   height: calc(100% - 42px);\r\n  //   .zp-paging-container-content {\r\n  //     height: 100%;\r\n  //   }\r\n  // }\r\n}\r\n:deep(.avatar) {\r\n  border-radius: 50%;\r\n  overflow: hidden;\r\n  margin-right: 10px;\r\n  background-color: #eee;\r\n}\r\n.content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  uni-text {\r\n    &:first-child {\r\n      font-size: 16px;\r\n      color: #8799a3;\r\n    }\r\n    &:last-child {\r\n      font-size: 12px;\r\n    }\r\n  }\r\n}\r\n.wraper {\r\n  // height: 100%;\r\n  height: calc(100vh - var(--window-top) - constant(safe-area-inset-bottom) - 140px);\r\n  height: calc(100vh - var(--window-top) - env(safe-area-inset-bottom) - 140px);\r\n  :deep(.wd-cell__right) {\r\n    display: none;\r\n  }\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-message/contacts/contacts.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useToast", "useUserStore", "useParamsStore", "ref", "useRouter", "reactive", "http", "watch", "nextTick", "data", "vPinyin", "getFileAccessHttpUrl", "onLoad", "cache", "TENANT_LIST"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+EA,MAAA,uBAAiC,MAAA;;;;AAInBA,kBAAS,SAAA;AACvB,UAAM,YAAYC,WAAAA,aAAa;AAC/B,UAAM,cAAcC,iBAAAA,eAAe;AAC7B,UAAA,SAASC,kBAAI,IAAI;AACvB,UAAM,SAASC,gCAAAA,UAAU;AACnB,UAAA,WAAWD,cAAI,IAAA,EAAE;AAEjB,UAAA,aAAaA,cAAI,IAAA,EAAE;AACnB,UAAA,UAAUA,kBAAI,EAAE;AAChB,UAAA,aAAaA,cAAI,IAAA,EAAE;AACnB,UAAA,kBAAkBE,uBAAS,EAAE,MAAM,OAAO,SAAS,IAAI,SAAS,CAAA,GAAI;AAEpE,UAAA,YAAY,CAAC,QAAQ,aAAa;AACtC,YAAM,UAAU,EAAE,QAAQ,UAAU,UAAU,gBAAgB,QAAQ;AACtE,UAAI,gBAAgB,YAAY;AAAI,eAAO,QAAQ;AACnDC,iBAAA,KACG,IAAI,0BAA0B,OAAO,EACrC,KAAK,CAAC,QAAa;AAClB,YAAI,IAAI,WAAW,IAAI,OAAO,QAAQ;AAC7B,iBAAA,MAAM,SAAS,IAAI,MAAM;AAAA,QAAA,OAC3B;AACE,iBAAA,MAAM,SAAS,KAAK;AAAA,QAAA;AAAA,MAC7B,CACD,EACA,MAAM,CAAC,QAAQ;AACP,eAAA,MAAM,SAAS,KAAK;AAAA,MAAA,CAC5B;AAAA,IACL;AAEAC,kBAAA,MAAM,UAAU,MAAM;AAChB,UAAA,SAAS,aAAa,SAAS,KAAK;AACxC,eAAS,cAAc,MAAM;AACtB,aAAA,KAAK,CAAC,GAAG,MAAM,EAAE,MAAM,cAAc,EAAE,KAAK,CAAC;AACzC,iBAAA,QAAQ,CAAC,GAAG,MAAM;AAC7B,iBAAW,QAAQ;AACX,cAAA,IAAI,iBAAiB,WAAW,KAAK;AAAA,IAAA,CAC9C;AAGD,aAAS,eAAe;AACtB,iBAAW,QAAQ,CAAC;AACpBC,oBAAAA,WAAS,MAAM;AACb,YAAI,QAAQ,OAAO;AACjB,qBAAW,QAAQ,WAAW,MAAM,OAAO,CAAC,SAAS;AACnD,mBAAO,KAAK,KAAK,KAAK,CAAC,WAAW;AAChC,qBAAO,OAAO,SAAS,QAAQ,QAAQ,KAAK,KAAK;AAAA,YAAA,CAClD;AAAA,UAAA,CACF;AAAA,QAAA,OACI;AACL,qBAAW,QAAQ,WAAW;AAAA,QAAA;AAAA,MAChC,CACD;AAAA,IAAA;AAGH,aAAS,cAAc;AACrB,cAAQ,QAAQ;AACH,mBAAA;AAAA,IAAA;AAGT,UAAA,cAAc,CAAC,OAAe,SAAc;AAChD,kBAAY,cAAc,cAAc,EAAE,eAAe,YAAY,MAAM;AAC3E,aAAO,KAAK,EAAE,MAAM,aAAA,CAAc;AAAA,IACpC;AACA,UAAM,eAAe,CAAC,EAAE,aAAa;AACnC,sBAAgB,UAAU,OAAO;AACjC,aAAO,MAAM,OAAO;AAAA,IACtB;AACM,UAAA,gBAAgB,CAAC,SAAS;AAC9B,YAAM,UAAU,KAAK,OAAO,CAAC,KAAK,SAAS;AACzC,cAAM,MAAM,KAAK;AACb,YAAA,CAAC,IAAI,GAAG,GAAG;AACT,cAAA,GAAG,IAAI,CAAC;AAAA,QAAA;AAEV,YAAA,GAAG,EAAE,KAAK,IAAI;AACX,eAAA;AAAA,MACT,GAAG,EAAE;AAEE,aAAA,OAAO,QAAQ,OAAO,EAC1B,IAAI,CAAC,CAAC,OAAOC,KAAI,OAAO,EAAE,OAAO,MAAAA,MAAK,EAAE,EACxC,KAAK,CAAC,GAAG,MAAM,EAAE,MAAM,cAAc,EAAE,KAAK,CAAC;AAAA,IAClD;AACM,UAAA,eAAe,CAAC,QAAQ;AAC5B,UAAI,SAAS,CAAC;AACV,UAAA,QAAQ,CAAC,SAAS;AAChB,YAAA,EAAE,IAAI,UAAU,QAAQ,UAAU,OAAO,OAAO,MAAM,WAAA,IAAe;AAErE,YAAA,aAAa,UAAU,SAAS,UAAU;AAC5C,cAAI,SAAS;AACb,cAAI,UAAU;AAER,gBAAA,wBAAwB,KAAK,QAAQ,GAAG;AACjC,uBAAAC,0BAAAA,QAAQ,gBAAgB,QAAQ;AAAA,YAAA;AAAA,UAC3C;AAEF,cAAI,QAAQ;AACV,qBAASC,kCAAqB,MAAM;AAAA,UAAA;AAEtC,cAAI,MAAM,OAAO,OAAO,GAAG,CAAC;AAC5B,cAAI,SAAS;AACT,cAAA,QAAQ,IAAI,OAAO,MAAM;AAC7B,gBAAM,CAAC,MAAM,KAAK,GAAG,IAAI,IAAI,gBAAgB;AAC7C,iBAAO,KAAK;AAAA,YACV;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UAAA,CACD;AAAA,QAAA;AAAA,MACH,CACD;AACM,aAAA;AAAA,IACT;AAEAC,kBAAAA,OAAO,MAAM;AACL,YAAA,aAAaC,mBAAMC,4BAAW;AACpC,YAAM,SAAS,yCAAY,IAAI,CAAC,SAAS;AACvC,eAAO,EAAE,KAAK,KAAK,IAAI,OAAO,KAAK,KAAK;AAAA,MAAA;AAE1B,sBAAA,UAAU,CAAC,EAAE,KAAK,IAAI,OAAO,QAAQ,GAAG,MAAM;AAAA,IAAA,CAC/D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7MD,GAAG,WAAW,eAAe;"}