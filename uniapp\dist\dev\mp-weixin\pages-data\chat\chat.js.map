{"version": 3, "file": "chat.js", "sources": ["../../../../../src/pages-data/chat/chat.vue", "../../../../../uniPage:/cGFnZXMtZGF0YVxjaGF0XGNoYXQudnVl"], "sourcesContent": ["\r\n<template>\n<layout-default-uni >\r\n  <PageLayout :navLeftArrow=\"true\" navLeftText=\"返回\">\r\n    <!-- 医生端查询区域 - 固定在顶部 -->\r\n    <view class=\"doctor-filter-fixed\" v-if=\"Number(userStore.userInfo.userCategory) === 0\">\r\n      <view class=\"filter-section\">\r\n            <view class=\"date-filter\">\r\n              <text class=\"filter-label\">时间：</text>\r\n              <picker mode=\"date\" :value=\"startDate\" start=\"1900-01-01\" end=\"2099-12-31\" @change=\"onStartDateChange\">\r\n                <view class=\"date-picker\">\r\n                  <text>{{ startDate || '开始日期' }}</text>\r\n                  <uni-icons type=\"calendar\" size=\"15\" color=\"#666666\" />\r\n                </view>\r\n              </picker>\r\n              <view class=\"date-separator\">至</view>\r\n              <picker mode=\"date\" :value=\"endDate\" start=\"1900-01-01\" end=\"2099-12-31\" @change=\"onEndDateChange\">\r\n                <view class=\"date-picker\">\r\n                  <text>{{ endDate || '结束日期' }}</text>\r\n                  <uni-icons type=\"calendar\" size=\"15\" color=\"#666666\" />\r\n                </view>\r\n              </picker>\r\n              <view class=\"reset-btn\" :class=\"{ 'refreshing': isRefreshing }\" @click=\"resetDateFilter\">\r\n                <uni-icons type=\"reload\" size=\"18\" color=\"#07C160\" />\r\n              </view>\r\n            </view>\r\n          <!-- 患者姓名搜索 -->\r\n          <view class=\"search-row\">\r\n            <text class=\"search-label\">姓名：</text>\r\n            <view class=\"search-input-container\">\r\n              <input\r\n                type=\"text\"\r\n                v-model=\"searchParams.patientName\"\r\n                placeholder=\"请输入患者姓名\"\r\n                class=\"search-input name-input\"\r\n                :maxlength=\"20\"\r\n                @input=\"onPatientNameInput\"\r\n              />\r\n              <text\r\n                v-if=\"searchParams.patientName\"\r\n                class=\"clear-btn\"\r\n                @click=\"clearPatientName\"\r\n              >\r\n                ✕\r\n              </text>\r\n            </view>\r\n          </view>\r\n\r\n          <!-- 问题关键字搜索 -->\r\n          <view class=\"search-row\">\r\n            <text class=\"search-label\">问题：</text>\r\n            <view class=\"search-input-container\">\r\n              <input\r\n                type=\"text\"\r\n                v-model=\"searchParams.question\"\r\n                placeholder=\"请输入问题关键字\"\r\n                class=\"search-input question-input\"\r\n                :maxlength=\"50\"\r\n                @input=\"onQuestionInput\"\r\n              />\r\n              <text\r\n                v-if=\"searchParams.question\"\r\n                class=\"clear-btn\"\r\n                @click=\"clearQuestion\"\r\n              >\r\n                ✕\r\n              </text>\r\n            </view>\r\n          </view>\r\n\r\n          <!-- 回复状态筛选 -->\r\n          <view class=\"search-row\">\r\n            <text class=\"search-label\">状态：</text>\r\n            <view class=\"status-radio-container\">\r\n              <view\r\n                v-for=\"option in replyStatusOptions\"\r\n                :key=\"option.value\"\r\n                class=\"radio-option\"\r\n                @click=\"onReplyStatusChange(option.value)\"\r\n              >\r\n                <view class=\"radio-circle\" :class=\"{ 'radio-checked': option.checked }\">\r\n                  <view v-if=\"option.checked\" class=\"radio-dot\"></view>\r\n                </view>\r\n                <text class=\"radio-label\">{{ option.label }}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n\r\n          <!-- @我的问题筛选 -->\r\n          <view class=\"search-row\">\r\n            <text class=\"search-label\">筛选：</text>\r\n            <view class=\"mention-filter-container\">\r\n              <view\r\n                class=\"mention-filter-option\"\r\n                :class=\"{ 'mention-active': showOnlyMentioned }\"\r\n                @click=\"toggleMentionFilter\"\r\n              >\r\n                <view class=\"mention-checkbox\" :class=\"{ 'mention-checked': showOnlyMentioned }\">\r\n                  <uni-icons v-if=\"showOnlyMentioned\" type=\"checkmarkempty\" size=\"12\" color=\"#FFFFFF\"></uni-icons>\r\n                </view>\r\n                <text class=\"mention-label\">只看@我的问题</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n\r\n          <view class=\"button-row\">\r\n            <view class=\"search-button\" @click=\"handleSearch\">\r\n              <uni-icons type=\"search\" size=\"16\" color=\"#FFFFFF\"></uni-icons>\r\n              <text class=\"search-button-text\">查询</text>\r\n            </view>\r\n            <view class=\"clear-all-button\" @click=\"clearAllSearch\">\r\n              <uni-icons type=\"clear\" size=\"16\" color=\"#666666\"></uni-icons>\r\n              <text class=\"clear-all-button-text\">清空</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n    </view>\r\n\r\n    <!-- 医生端滚动内容区域 -->\r\n    <scroll-view class=\"page-scroll-view doctor-scroll\" scroll-y=\"true\" v-if=\"Number(userStore.userInfo.userCategory) === 0\">\r\n        <!-- 医生端聊天列表 -->\r\n        <!-- 加载状态 -->\r\n        <view class=\"loading-container\" v-if=\"loadingChat\">\r\n          <wd-loading />\r\n        </view>\r\n\r\n        <!-- 空状态 -->\r\n        <view v-if=\"chatList.length === 0 && !loadingChat\" class=\"empty-tip\">\r\n          <text class=\"empty-text\">暂时没有问题记录</text>\r\n          <text class=\"empty-hint\">使用上方筛选条件查找患者问题</text>\r\n          <view class=\"retry-button\" @click=\"handleRetry\">\r\n            <text class=\"retry-text\">重新加载</text>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 聊天记录列表 -->\r\n        <view class=\"chat-list\" v-if=\"chatList.length > 0\">\r\n          <view class=\"chat-item\" v-for=\"(item, index) in chatList\" :key=\"index\" @click=\"goToChatDetail(item.id)\">\r\n            <image class=\"chat-avatar\" :src=\"getAvatarUrl(getDisplayAvatar(item))\" mode=\"aspectFill\" @error=\"handleAvatarError\"/>\r\n            <view class=\"chat-content\">\r\n              <view class=\"chat-row\">\r\n                <view class=\"chat-nickname-container\">\r\n                  <text class=\"chat-nickname\">{{ getDisplayName(item) }}</text>\r\n                  <!-- 显示回复关系 -->\r\n                  <text v-if=\"item.replyToName\" class=\"reply-text\">\r\n                    回复 <text class=\"reply-target\">{{ item.replyToName }}</text>：<text class=\"reply-content\">{{ item.replyToContent || '原消息' }}</text>\r\n                  </text>\r\n                </view>\r\n                <text class=\"chat-time\">{{ item.createTime || '-' }}</text>\r\n              </view>\r\n              <!-- 问题标题 -->\r\n              <view v-if=\"item.title\" class=\"chat-title-row\">\r\n                <text class=\"chat-title\">{{ item.title }}</text>\r\n              </view>\r\n              <!-- @医生信息 -->\r\n              <view v-if=\"item.doctors && item.doctors.length > 0\" class=\"mentioned-doctors-row\">\r\n                <view class=\"mentioned-doctors\">\r\n                  <text\r\n                    v-for=\"(doctor, doctorIndex) in item.doctors\"\r\n                    :key=\"doctorIndex\"\r\n                    class=\"mentioned-doctor\"\r\n                  >\r\n                    @{{ doctor.realname || doctor.username || '医生' }}\r\n                  </text>\r\n                </view>\r\n              </view>\r\n              <view class=\"chat-bottom-row\">\r\n                <text class=\"chat-msg\">{{ item.question || '-' }}</text>\r\n                <!-- 回复状态标志 - 移动到右下角 -->\r\n                <view class=\"reply-status-badge\" :class=\"getReplyStatusClass(item.status)\">\r\n                  <text class=\"status-text\">{{ getReplyStatusText(item.status) }}</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n    </scroll-view>\r\n\r\n    <!-- 患者界面的查询区域 - 固定在顶部 -->\r\n    <view class=\"patient-filter-fixed\" v-if=\"Number(userStore.userInfo.userCategory) === 1\">\r\n      <view class=\"filter-section\">\r\n        <view class=\"date-filter\">\r\n          <text class=\"filter-label\">时间：</text>\r\n          <picker mode=\"date\" :value=\"startDate\" start=\"1900-01-01\" end=\"2099-12-31\" @change=\"onStartDateChange\">\r\n            <view class=\"date-picker\">\r\n              <text>{{ startDate || '开始日期' }}</text>\r\n              <uni-icons type=\"calendar\" size=\"15\" color=\"#666666\" />\r\n            </view>\r\n          </picker>\r\n          <view class=\"date-separator\">至</view>\r\n          <picker mode=\"date\" :value=\"endDate\" start=\"1900-01-01\" end=\"2099-12-31\" @change=\"onEndDateChange\">\r\n            <view class=\"date-picker\">\r\n              <text>{{ endDate || '结束日期' }}</text>\r\n              <uni-icons type=\"calendar\" size=\"15\" color=\"#666666\" />\r\n            </view>\r\n          </picker>\r\n          <view class=\"reset-btn\" :class=\"{ 'refreshing': isRefreshing }\" @click=\"resetDateFilter\">\r\n            <uni-icons type=\"reload\" size=\"18\" color=\"#07C160\" />\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 问题关键字搜索 -->\r\n        <view class=\"search-row\">\r\n          <text class=\"search-label\">问题：</text>\r\n          <view class=\"search-input-container\">\r\n            <input\r\n              type=\"text\"\r\n              v-model=\"searchParams.question\"\r\n              placeholder=\"请输入问题关键字\"\r\n              class=\"search-input question-input\"\r\n              :maxlength=\"50\"\r\n              @input=\"onQuestionInput\"\r\n            />\r\n            <text\r\n              v-if=\"searchParams.question\"\r\n              class=\"clear-btn\"\r\n              @click=\"clearQuestion\"\r\n            >\r\n              ✕\r\n            </text>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 回复状态筛选 -->\r\n        <view class=\"search-row\">\r\n          <text class=\"search-label\">状态：</text>\r\n          <view class=\"status-radio-container\">\r\n            <view\r\n              v-for=\"option in replyStatusOptions\"\r\n              :key=\"option.value\"\r\n              class=\"radio-option\"\r\n              @click=\"onReplyStatusChange(option.value)\"\r\n            >\r\n              <view class=\"radio-circle\" :class=\"{ 'radio-checked': option.checked }\">\r\n                <view v-if=\"option.checked\" class=\"radio-dot\"></view>\r\n              </view>\r\n              <text class=\"radio-label\">{{ option.label }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <view class=\"button-row\">\r\n          <view class=\"search-button\" @click=\"handleSearch\">\r\n            <uni-icons type=\"search\" size=\"16\" color=\"#FFFFFF\"></uni-icons>\r\n            <text class=\"search-button-text\">查询</text>\r\n          </view>\r\n          <view class=\"clear-all-button\" @click=\"clearAllSearch\">\r\n            <uni-icons type=\"clear\" size=\"16\" color=\"#666666\"></uni-icons>\r\n            <text class=\"clear-all-button-text\">清空</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      <!-- 发起提问按钮放在查询逻辑下面 -->\r\n      <button class=\"add-btn\" @click=\"addChat\" :disabled=\"isViewMode\">发起提问</button>\r\n    </view>\r\n\r\n    <!-- 滚动内容区域 - 仅患者端使用 -->\r\n    <scroll-view class=\"page-scroll-view\" :class=\"{ 'patient-scroll': Number(userStore.userInfo.userCategory) === 1 }\" scroll-y=\"true\" @navLeftClick=\"goHome\" v-if=\"Number(userStore.userInfo.userCategory) === 1\">\r\n        <!-- 加载状态 -->\r\n        <view class=\"loading-container\" v-if=\"loadingChat\">\r\n          <wd-loading />\r\n        </view>\r\n\r\n        <!-- 空状态 -->\r\n        <view v-if=\"chatList.length === 0 && !loadingChat\" class=\"empty-tip\">\r\n          <text class=\"empty-text\">暂时没有问题记录</text>\r\n          <text class=\"empty-hint\">点击下方\"发起提问\"按钮开始咨询</text>\r\n          <view class=\"retry-button\" @click=\"handleRetry\">\r\n            <text class=\"retry-text\">重新加载</text>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 聊天记录列表 -->\r\n        <view class=\"chat-list\" v-if=\"chatList.length > 0\">\r\n          <view class=\"chat-item\" v-for=\"(item, index) in chatList\" :key=\"index\" @click=\"goToChatDetail(item.id)\">\r\n            <image class=\"chat-avatar\" :src=\"getAvatarUrl(getDisplayAvatar(item))\" mode=\"aspectFill\" @error=\"handleAvatarError\"/>\r\n            <view class=\"chat-content\">\r\n              <view class=\"chat-row\">\r\n                <view class=\"chat-nickname-container\">\r\n                  <text class=\"chat-nickname\">{{ getDisplayName(item) }}</text>\r\n                  <!-- 显示回复关系 -->\r\n                  <text v-if=\"item.replyToName\" class=\"reply-text\">\r\n                    回复 <text class=\"reply-target\">{{ item.replyToName }}</text>：<text class=\"reply-content\">{{ item.replyToContent || '原消息' }}</text>\r\n                  </text>\r\n                </view>\r\n                <text class=\"chat-time\">{{ item.createTime || '-' }}</text>\r\n              </view>\r\n              <!-- 问题标题 -->\r\n              <view v-if=\"item.title\" class=\"chat-title-row\">\r\n                <text class=\"chat-title\">{{ item.title }}</text>\r\n              </view>\r\n              <!-- @医生信息 -->\r\n              <view v-if=\"item.doctors && item.doctors.length > 0\" class=\"mentioned-doctors-row\">\r\n                <view class=\"mentioned-doctors\">\r\n                  <text\r\n                    v-for=\"(doctor, doctorIndex) in item.doctors\"\r\n                    :key=\"doctorIndex\"\r\n                    class=\"mentioned-doctor\"\r\n                  >\r\n                    @{{ doctor.realname || doctor.username || '医生' }}\r\n                  </text>\r\n                </view>\r\n              </view>\r\n              <view class=\"chat-bottom-row\">\r\n                <text class=\"chat-msg\">{{ item.question || '-' }}</text>\r\n                <!-- 回复状态标志 - 移动到右下角 -->\r\n                <view class=\"reply-status-badge\" :class=\"getReplyStatusClass(item.status)\">\r\n                  <text class=\"status-text\">{{ getReplyStatusText(item.status) }}</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n    </scroll-view>\r\n    <!-- 日期选择器弹窗 -->\r\n    <uni-calendar ref=\"calendar\" :insert=\"false\" @confirm=\"dateConfirm\" :range=\"false\" />\r\n  </PageLayout>\r\n</layout-default-uni>\n</template>\r\n<script lang=\"ts\" setup>\r\nimport { ref, computed } from 'vue';\r\nimport { useUserStore } from '@/store/user';\r\nimport { http } from '@/utils/http';\r\nimport { onMounted } from 'vue';\r\nimport { onShow } from '@dcloudio/uni-app';\r\n\r\nconst userStore = useUserStore();\r\nconst chatList = ref([]); // 聊天记录列表\r\nconst loadingChat = ref(false); // 加载状态\r\nconst isRefreshing = ref(false); // 刷新状态\r\n\r\n\r\n// 日期筛选\r\nconst startDate = ref('')\r\nconst endDate = ref('')\r\nconst datePickerType = ref('') // 用于标识当前打开的是开始日期还是结束日期选择器\r\nconst calendar = ref(null)\r\n\r\n\r\n// 处理开始日期变化\r\nfunction onStartDateChange(e: any) {\r\n  startDate.value = e.detail.value\r\n}\r\n\r\n// 处理结束日期变化\r\nfunction onEndDateChange(e: any) {\r\n  endDate.value = e.detail.value\r\n}\r\n\r\n// 日期选择确认\r\nconst dateConfirm = (e: any) => {\r\n  const selectedDate = e.fulldate\r\n  if (datePickerType.value === 'start') {\r\n    startDate.value = selectedDate\r\n  } else if (datePickerType.value === 'end') {\r\n    endDate.value = selectedDate\r\n  }\r\n}\r\nconst loading = ref(false) // 数据加载状态（保留以防其他地方使用）\r\n\r\n// 刷新整个页面 - 完全重置页面状态并重新获取所有数据\r\nconst resetDateFilter = () => {\r\n  // 防止重复刷新\r\n  if (isRefreshing.value) {\r\n    return;\r\n  }\r\n\r\n  // 设置刷新状态\r\n  isRefreshing.value = true;\r\n\r\n  // 显示刷新提示\r\n  uni.showLoading({\r\n    title: '刷新中...',\r\n    mask: true\r\n  });\r\n\r\n\r\n  // 1. 完全重置所有搜索条件和页面状态\r\n  startDate.value = '';\r\n  endDate.value = '';\r\n  searchParams.value.patientName = '';\r\n  searchParams.value.question = '';\r\n  searchParams.value.patientAvatar = '';\r\n  searchParams.value.createTime = '';\r\n\r\n  // 2. 清空当前列表数据\r\n  chatList.value = [];\r\n\r\n  // 3. 重置加载状态\r\n  loadingChat.value = false;\r\n\r\n  // 4. 重新获取完整的数据列表\r\n  fetchChatList().then(() => {\r\n    uni.hideLoading();\r\n    uni.showToast({\r\n      title: '页面刷新成功',\r\n      icon: 'success',\r\n      duration: 1500\r\n    });\r\n\r\n    // 延迟重置刷新状态，让动画效果更明显\r\n    setTimeout(() => {\r\n      isRefreshing.value = false;\r\n    }, 500);\r\n  }).catch((error) => {\r\n    uni.hideLoading();\r\n    console.error('❌ 页面刷新失败:', error);\r\n    uni.showToast({\r\n      title: '刷新失败，请重试',\r\n      icon: 'none',\r\n      duration: 2000\r\n    });\r\n\r\n    // 失败时也要重置刷新状态\r\n    setTimeout(() => {\r\n      isRefreshing.value = false;\r\n    }, 500);\r\n  });\r\n}\r\n\r\n// 检查是否有搜索条件\r\nconst hasSearchConditions = () => {\r\n  const hasReplyStatusFilter = getSelectedReplyStatus() !== 'all';\r\n\r\n  // 患者端不检查姓名条件\r\n  if (Number(userStore.userInfo.userCategory) === 1) {\r\n    return !!(\r\n      (searchParams.value.question && searchParams.value.question.trim()) ||\r\n      startDate.value ||\r\n      endDate.value ||\r\n      hasReplyStatusFilter\r\n    );\r\n  }\r\n\r\n  // 医生端检查所有条件\r\n  return !!(\r\n    (searchParams.value.patientName && searchParams.value.patientName.trim()) ||\r\n    (searchParams.value.question && searchParams.value.question.trim()) ||\r\n    startDate.value ||\r\n    endDate.value ||\r\n    hasReplyStatusFilter ||\r\n    showOnlyMentioned.value\r\n  );\r\n};\r\n\r\n// 搜索处理函数\r\nconst handleSearch = () => {\r\n  fetchChatList();\r\n};\r\n\r\nconsole.log('userCategory:', userStore.userInfo.userCategory)\r\n\r\n// 默认头像\r\n// 默认头像，使用SVG格式确保显示\r\nconst defAvatar = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNGNUY1RjUiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iIzk5OTk5OSIvPgo8cGF0aCBkPSJNMzAgMzJDMzAgMjYuNDc3MSAyNS41MjI5IDIyIDIwIDIyQzE0LjQ3NzEgMjIgMTAgMjYuNDc3MSAxMCAzMkgzMFoiIGZpbGw9IiM5OTk5OTkiLz4KPC9zdmc+'\r\n\r\n// 缓存用户头像URL\r\nconst userAvatarCache = ref('')\r\n\r\n// 获取文件URL的函数\r\nconst fetchFileUrl = async (objectName: string) => {\r\n  try {\r\n    const response: any = await http.get('/file/url', {\r\n      objectName: objectName,\r\n      token: userStore.userInfo.token\r\n    })\r\n\r\n    if (response?.success && response?.result?.fileUrl) {\r\n      return response.result.fileUrl;\r\n    } else {\r\n      return '';\r\n    }\r\n  } catch (error) {\r\n    return '';\r\n  }\r\n}\r\n\r\n// 通用头像处理函数\r\nconst getAvatarUrl = (avatar: string | null | undefined) => {\r\n  // 如果头像为空、null、undefined或空字符串，返回默认头像\r\n  if (!avatar || avatar.trim() === '') {\r\n    return defAvatar;\r\n  }\r\n\r\n  // 检查是否已经是完整URL (http或https开头)\r\n  if (avatar.startsWith('http://') || avatar.startsWith('https://')) {\r\n    return avatar;\r\n  }\r\n\r\n  // 检查是否是base64格式\r\n  if (avatar.startsWith('data:image/')) {\r\n    return avatar;\r\n  }\r\n\r\n  // 如果是相对路径，拼接基础URL\r\n  if (avatar.startsWith('/')) {\r\n    return 'https://www.mograine.cn' + avatar;\r\n  }\r\n\r\n  // 如果是文件名，先检查缓存\r\n  if (userAvatarCache.value && avatar === userStore.userInfo.avatar) {\r\n    return userAvatarCache.value;\r\n  }\r\n\r\n  // 如果是文件名，拼接完整路径作为备用\r\n  return 'https://www.mograine.cn/images/' + avatar;\r\n}\r\n\r\n// 头像加载错误处理\r\nconst handleAvatarError = (event: any) => {\r\n  event.target.src = defAvatar;\r\n}\r\n\r\nconst searchParams = ref({\r\n    patientName: '',     // 患者姓名\r\n    question: '',        // 问题关键字\r\n    patientAvatar: '',\r\n    createTime: '',\r\n})\r\n\r\n// 回复状态筛选选项\r\nconst replyStatusOptions = ref([\r\n  { value: 'all', label: '全部', checked: true },\r\n  { value: 'pending', label: '待回复', checked: false },\r\n  { value: 'replied', label: '已回复', checked: false }\r\n])\r\n\r\n// @我的问题筛选\r\nconst showOnlyMentioned = ref(false)\r\n\r\n\r\n\r\n// 获取当前选中的回复状态\r\nconst getSelectedReplyStatus = () => {\r\n  const selected = replyStatusOptions.value.find(option => option.checked)\r\n  return selected ? selected.value : 'all'\r\n}\r\n\r\n// 处理回复状态选择\r\nconst onReplyStatusChange = (selectedValue: string) => {\r\n  // 更新选中状态\r\n  replyStatusOptions.value.forEach(option => {\r\n    option.checked = option.value === selectedValue\r\n  })\r\n}\r\n\r\n// 切换@我的问题筛选\r\nconst toggleMentionFilter = () => {\r\n  showOnlyMentioned.value = !showOnlyMentioned.value\r\n  console.log('🔍 切换@我的问题筛选:', showOnlyMentioned.value ? '开启' : '关闭')\r\n}\r\n\r\n\r\n\r\n//获取沟通记录 - 统一查询函数\r\nconst fetchChatList = async () => {\r\n  return new Promise((resolve, reject) => {\r\n    try {\r\n      loadingChat.value = true; // 显示加载状态\r\n\r\n      // 构建查询参数 - 根据后端接口文档要求\r\n      const params: any = {};\r\n\r\n      // 必需参数：用户ID\r\n      if (userStore.userInfo.userid) {\r\n        params.user_id = userStore.userInfo.userid;\r\n      }\r\n\r\n      // 必需参数：用户类别 (0=医生, 1=患者)\r\n      if (userStore.userInfo.userCategory !== undefined && userStore.userInfo.userCategory !== '') {\r\n        params.category = Number(userStore.userInfo.userCategory);\r\n      }\r\n\r\n      // 可选参数：患者姓名（仅医生端）\r\n      if (Number(userStore.userInfo.userCategory) === 0 && searchParams.value.patientName && searchParams.value.patientName.trim()) {\r\n        params.patientName = searchParams.value.patientName.trim();\r\n      }\r\n\r\n      // 可选参数：问题关键字\r\n      if (searchParams.value.question && searchParams.value.question.trim()) {\r\n        params.question = searchParams.value.question.trim();\r\n      }\r\n\r\n      // 可选参数：时间范围\r\n      if (startDate.value) {\r\n        params.startDate = startDate.value;\r\n      }\r\n      if (endDate.value) {\r\n        params.endDate = endDate.value;\r\n      }\r\n\r\n\r\n      if (!userStore.userInfo.token) {\r\n        console.error('❌ Token未设置，可能需要重新登录');\r\n        uni.showToast({\r\n          title: '登录状态已过期，请重新登录',\r\n          icon: 'none',\r\n          duration: 3000\r\n        });\r\n        // 跳转到登录页面\r\n        uni.reLaunch({\r\n          url: '/pages/login/login'\r\n        });\r\n        reject(new Error('Token未设置'));\r\n        return;\r\n      }\r\n      // 使用最终的参数组合\r\n      http.get('/communication/list', params).then((res:any) => {\r\n\r\n        if(res.success && res.result){\r\n          console.log('API返回的获取沟通记录详情:', res.result);\r\n          // 处理回复关系数据\r\n          const processedData = res.result.map((item: any) => {\r\n            let replyToContent = null;\r\n\r\n            // 如果有回复关系（点击了某个评论进行回复，出现了@符号）\r\n            if (item.replyToUserId) {\r\n              console.log('聊天列表检测到回复关系，replyToUserId:', item.replyToUserId);\r\n\r\n              // 有 replyToUserId 就说明是通过点击评论触发的回复，需要显示回复关系\r\n              if (item.replyToContent && typeof item.replyToContent === 'string' && item.replyToContent.trim()) {\r\n                // 截断过长的内容，最多显示15个字符\r\n                replyToContent = item.replyToContent.length > 15\r\n                  ? item.replyToContent.substring(0, 15) + '...'\r\n                  : item.replyToContent;\r\n              } else {\r\n                // 如果没有具体的回复内容，显示默认文本\r\n                replyToContent = '原评论';\r\n              }\r\n            }\r\n\r\n            // 调试：打印doctors信息\r\n            if (item.doctors && item.doctors.length > 0) {\r\n              console.log('📋 检测到@医生信息:', {\r\n                问题ID: item.id,\r\n                医生数量: item.doctors.length,\r\n                医生列表: item.doctors.map(d => d.realname || d.username || '未知医生')\r\n              });\r\n            }\r\n\r\n            return {\r\n              ...item,\r\n              // 有 replyToUserId 就显示回复关系\r\n              replyToName: item.replyToUserId ? (item.replyToUserName || '某用户') : null,\r\n              replyToContent: replyToContent\r\n            };\r\n          });\r\n\r\n          // 前端综合过滤逻辑\r\n          let filteredData = processedData || [];\r\n          const originalCount = filteredData.length;\r\n\r\n          // 患者端过滤：只显示自己的问题\r\n          if (Number(userStore.userInfo.userCategory) === 1) {\r\n            const currentUserId = userStore.userInfo.userid;\r\n\r\n            filteredData = filteredData.filter((item: any) => {\r\n              // 尝试多个可能的字段来匹配当前用户\r\n              const match = item.userId === currentUserId ||\r\n                           item.patientId === currentUserId ||\r\n                           item.user_id === currentUserId ||\r\n                           item.patient_id === currentUserId;\r\n              return match;\r\n            });\r\n          }\r\n\r\n          // 医生端：应用患者姓名过滤\r\n          if (Number(userStore.userInfo.userCategory) === 0 && searchParams.value.patientName && searchParams.value.patientName.trim()) {\r\n            const nameKeyword = searchParams.value.patientName.trim().toLowerCase();\r\n            filteredData = filteredData.filter((item: any) => {\r\n              const patientName = (item.patientName || '').toLowerCase();\r\n              return patientName.includes(nameKeyword);\r\n            });\r\n          }\r\n\r\n          // 应用问题关键字过滤\r\n          if (searchParams.value.question && searchParams.value.question.trim()) {\r\n            const questionKeyword = searchParams.value.question.trim().toLowerCase();\r\n            filteredData = filteredData.filter((item: any) => {\r\n              const question = (item.question || '').toLowerCase();\r\n              return question.includes(questionKeyword);\r\n            });\r\n          }\r\n\r\n          // 应用时间范围过滤\r\n          if (startDate.value || endDate.value) {\r\n            filteredData = filteredData.filter((item: any) => {\r\n              const itemDate = item.createTime || item.updateTime;\r\n              if (!itemDate) return false; // 没有时间信息的数据不显示\r\n\r\n              const itemDateStr = itemDate.substring(0, 10); // 提取日期部分 YYYY-MM-DD\r\n\r\n              // 检查是否在开始时间之后\r\n              if (startDate.value && itemDateStr < startDate.value) {\r\n                return false;\r\n              }\r\n\r\n              // 检查是否在结束时间之前\r\n              if (endDate.value && itemDateStr > endDate.value) {\r\n                return false;\r\n              }\r\n\r\n              return true;\r\n            });\r\n\r\n          }\r\n\r\n          // 应用回复状态过滤\r\n          const selectedStatus = getSelectedReplyStatus();\r\n          if (selectedStatus !== 'all') {\r\n            filteredData = filteredData.filter((item: any) => {\r\n              if (selectedStatus === 'pending') {\r\n                return item.status === 0; // 待回复\r\n              } else if (selectedStatus === 'replied') {\r\n                return item.status === 1; // 已回复\r\n              }\r\n              return true;\r\n            })\r\n          }\r\n\r\n          // 应用@我的问题筛选（仅医生端）\r\n          if (Number(userStore.userInfo.userCategory) === 0 && showOnlyMentioned.value) {\r\n            const currentUserId = userStore.userInfo.userid;\r\n            filteredData = filteredData.filter((item: any) => {\r\n              // 检查doctors数组中是否包含当前医生\r\n              if (item.doctors && Array.isArray(item.doctors)) {\r\n                return item.doctors.some((doctor: any) =>\r\n                  doctor.id === currentUserId ||\r\n                  doctor.userId === currentUserId ||\r\n                  doctor.user_id === currentUserId\r\n                );\r\n              }\r\n              return false;\r\n            });\r\n            console.log('🔍 @我的问题筛选结果:', {\r\n              当前医生ID: currentUserId,\r\n              筛选后数量: filteredData.length,\r\n              筛选前数量: processedData?.length || 0\r\n            });\r\n          }\r\n\r\n          chatList.value = filteredData;\r\n          // 显示查询结果提示\r\n          if (hasSearchConditions()) {\r\n            uni.showToast({\r\n              title: `找到 ${chatList.value.length} 条记录`,\r\n              icon: 'none',\r\n              duration: 1500\r\n            });\r\n          }\r\n\r\n          resolve(res); // 成功时 resolve\r\n        } else {\r\n          // 更详细的错误处理\r\n          const errorMessage = res.message || '获取沟通记录失败';\r\n          console.error('❌ API返回错误:', {\r\n            success: res.success,\r\n            message: res.message,\r\n            code: res.code,\r\n            result: res.result\r\n          });\r\n\r\n          uni.showToast({\r\n            title: errorMessage,\r\n            icon: 'none',\r\n            duration: 3000\r\n          });\r\n\r\n          // 如果是500错误，可能是服务器问题，设置空数据而不是完全失败\r\n          if (res.code === 500) {\r\n            console.log('🔄 服务器错误，设置空数据列表');\r\n            chatList.value = [];\r\n            resolve(res); // 仍然resolve，避免阻塞后续操作\r\n          } else {\r\n            reject(new Error(errorMessage));\r\n          }\r\n        }\r\n      }).catch((error: any) => {\r\n        console.error('❌ 网络请求失败:', error);\r\n\r\n        // 网络错误处理\r\n        let errorMessage = '网络连接失败，请检查网络设置';\r\n        if (error.message) {\r\n          errorMessage = error.message;\r\n        }\r\n\r\n        uni.showToast({\r\n          title: errorMessage,\r\n          icon: 'none',\r\n          duration: 3000\r\n        });\r\n\r\n        // 设置空数据，避免页面崩溃\r\n        chatList.value = [];\r\n        reject(error);\r\n      });\r\n    } catch (error) {\r\n      console.error('❌ fetchChatList 异常:', error);\r\n\r\n      uni.showToast({\r\n        title: '系统异常，请稍后重试',\r\n        icon: 'none',\r\n        duration: 3000\r\n      });\r\n\r\n      // 设置空数据，避免页面崩溃\r\n      chatList.value = [];\r\n      reject(error);\r\n    } finally {\r\n      loadingChat.value = false; // 隐藏加载状态\r\n    }\r\n  });\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nonMounted(async () => {\r\n\r\n  // 如果是患者端且用户头像是文件名，获取真实URL\r\n  if (Number(userStore.userInfo.userCategory) === 1 && userStore.userInfo.avatar && !userStore.userInfo.avatar.startsWith('http')) {\r\n    const realUrl = await fetchFileUrl(userStore.userInfo.avatar);\r\n    if (realUrl) {\r\n      userAvatarCache.value = realUrl;\r\n    }\r\n  }\r\n\r\n  fetchChatList();\r\n\r\n\r\n});\r\n\r\n// 页面显示时重新加载数据\r\nonShow(() => {\r\n  fetchChatList();\r\n});\r\n\r\n\r\n\r\nfunction goHome() {\r\n  uni.switchTab({\r\n    url: '/pages/index/index' \r\n  });\r\n}\r\nfunction goToChatDetail(id: any) {\r\n  console.log('跳转到聊天详情，ID:', id);\r\n  uni.navigateTo({\r\n    url: `/pages-data/chat/chatDetail?communication_id=${id}`\r\n  })\r\n}\r\n\r\n// 获取显示的头像\r\nfunction getDisplayAvatar(item: any) {\r\n  // 患者端显示自己的头像，医生端显示患者头像\r\n  if (Number(userStore.userInfo.userCategory) === 1) {\r\n    const userAvatar = userStore.userInfo.avatar;\r\n    const patientAvatar = item.patientAvatar;\r\n\r\n    // 优先使用用户自己的头像\r\n    if (userAvatar) {\r\n      return userAvatar;\r\n    }\r\n\r\n    // 如果用户头像为空，使用患者头像\r\n    if (patientAvatar) {\r\n      return patientAvatar;\r\n    }\r\n\r\n    // 都没有则返回空，让 getAvatarUrl 处理默认头像\r\n    return '';\r\n  } else {\r\n    return item.patientAvatar || '';\r\n  }\r\n}\r\n\r\n// 获取显示的姓名\r\nfunction getDisplayName(item: any) {\r\n  // 患者端显示用户的真实姓名，医生端显示患者姓名\r\n  if (Number(userStore.userInfo.userCategory) === 1) {\r\n    return userStore.userInfo.realname || userStore.userInfo.username || '我的提问';\r\n  } else {\r\n    return item.patientName || '-';\r\n  }\r\n}\r\n\r\n\r\n\r\n// 添加聊天功能\r\nfunction addChat() {\r\n  console.log('发起提问');\r\n\r\n  // 直接跳转到发起提问页面\r\n  uni.navigateTo({\r\n    url: '/pages-data/chat/addChat'\r\n  });\r\n}\r\n\r\n// 检查是否为查看模式\r\nconst isViewMode = ref(false);\r\n\r\n// 处理患者姓名输入\r\nconst onPatientNameInput = (e: any) => {\r\n  const value = e.detail.value;\r\n  // 限制字符长度，超过20个字符时截断\r\n  if (value.length > 20) {\r\n    searchParams.value.patientName = value.substring(0, 20);\r\n    uni.showToast({\r\n      title: '患者姓名最多输入20个字符',\r\n      icon: 'none',\r\n      duration: 1500\r\n    });\r\n  } else {\r\n    searchParams.value.patientName = value;\r\n  }\r\n};\r\n\r\n// 处理问题关键字输入\r\nconst onQuestionInput = (e: any) => {\r\n  const value = e.detail.value;\r\n  // 限制字符长度，超过50个字符时截断\r\n  if (value.length > 50) {\r\n    searchParams.value.question = value.substring(0, 50);\r\n    uni.showToast({\r\n      title: '问题关键字最多输入50个字符',\r\n      icon: 'none',\r\n      duration: 1500\r\n    });\r\n  } else {\r\n    searchParams.value.question = value;\r\n  }\r\n};\r\n\r\n// 清除患者姓名\r\nconst clearPatientName = () => {\r\n  searchParams.value.patientName = '';\r\n};\r\n\r\n// 清除问题关键字\r\nconst clearQuestion = () => {\r\n  searchParams.value.question = '';\r\n};\r\n\r\n// 清空所有搜索条件\r\nconst clearAllSearch = () => {\r\n  startDate.value = '';\r\n  endDate.value = '';\r\n  // 只有医生端才清空患者姓名\r\n  if (Number(userStore.userInfo.userCategory) === 0) {\r\n    searchParams.value.patientName = '';\r\n  }\r\n  searchParams.value.question = '';\r\n  // 重置回复状态为\"全部\"\r\n  replyStatusOptions.value.forEach(option => {\r\n    option.checked = option.value === 'all'\r\n  });\r\n  // 重置@我的问题筛选\r\n  showOnlyMentioned.value = false;\r\n  uni.showToast({\r\n    title: '已清空搜索条件',\r\n    icon: 'none',\r\n    duration: 1000\r\n  });\r\n  // 清空后自动查询所有数据\r\n  fetchChatList();\r\n};\r\n\r\n// 重试加载数据\r\nconst handleRetry = () => {\r\n  uni.showLoading({\r\n    title: '重新加载中...'\r\n  });\r\n\r\n  fetchChatList().then(() => {\r\n    uni.hideLoading();\r\n    uni.showToast({\r\n      title: '加载成功',\r\n      icon: 'success',\r\n      duration: 1500\r\n    });\r\n  }).catch(() => {\r\n    uni.hideLoading();\r\n    uni.showToast({\r\n      title: '加载失败，请稍后重试',\r\n      icon: 'none',\r\n      duration: 2000\r\n    });\r\n  });\r\n};\r\n\r\n// 新增计算属性来截断显示文本\r\nconst truncatedPatientName = computed(() => {\r\n  return searchParams.value.patientName.length > 5\r\n    ? searchParams.value.patientName.slice(0, 5) + '...'\r\n    : searchParams.value.patientName;\r\n});\r\n\r\n\r\n\r\n// 获取回复状态对应的CSS类名\r\nconst getReplyStatusClass = (status: number) => {\r\n  switch (status) {\r\n    case 0:\r\n      return 'status-pending'; // 待回复 - 红色\r\n    case 1:\r\n      return 'status-replied'; // 已回复 - 绿色\r\n    default:\r\n      return 'status-pending'; // 默认为待回复\r\n  }\r\n};\r\n\r\n// 获取回复状态对应的文本\r\nconst getReplyStatusText = (status: number) => {\r\n  switch (status) {\r\n    case 0:\r\n      return '待回复';\r\n    case 1:\r\n      return '已回复';\r\n    default:\r\n      return '待回复';\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n// 患者端固定查询区域样式\r\n.patient-filter-fixed {\r\n  position: fixed;\r\n  top: 88px;\r\n  left: 0;\r\n  right: 0;\r\n  background-color: #fff;\r\n  z-index: 999;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  padding: 10px; // 减少内边距\r\n  box-sizing: border-box;\r\n  // 移除 max-height 和 overflow-y，让内容自动撑开高度\r\n}\r\n\r\n// 医生端固定查询区域样式\r\n.doctor-filter-fixed {\r\n  position: fixed;\r\n  top: 88px;\r\n  left: 0;\r\n  right: 0;\r\n  background-color: #fff;\r\n  z-index: 999;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  padding: 10px;\r\n  box-sizing: border-box;\r\n  // 移除 max-height 和 overflow-y，让内容自动撑开高度\r\n}\r\n\r\n.page-scroll-view {\r\n  height: calc(100vh - 44px);\r\n  width: 100%;\r\n  padding-bottom: 120rpx; // 增加底部内边距，确保内容不被遮挡\r\n  box-sizing: border-box;\r\n}\r\n\r\n// 患者端的scroll-view需要顶部内边距，避免被固定查询区域遮挡\r\n.page-scroll-view.patient-scroll {\r\n  padding-top: 220px; // 减少顶部内边距，最小化灰色空白区域\r\n}\r\n\r\n// 医生端的scroll-view需要顶部内边距，避免被固定查询区域遮挡\r\n.page-scroll-view.doctor-scroll {\r\n  padding-top: 240px; // 减少顶部内边距，最小化空白区域\r\n  padding-left: 10rpx;\r\n  padding-right: 10rpx;\r\n}\r\n\r\n.search-container {\r\n    background: #FFFFFF;\r\n    padding: 12rpx; // 减少内边距\r\n    border-radius: 12rpx;\r\n    margin-bottom: 12rpx; // 减少底部间距\r\n}\r\n\r\n.filter-section {\r\n    background-color: #FFFFFF;\r\n    border-radius: 16rpx;\r\n    padding: 12rpx; // 进一步减少内边距\r\n    margin-bottom: 8rpx; // 进一步减少底部间距\r\n    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n    }\r\n\r\n\r\n\r\n    .date-filter {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: 20rpx;\r\n      gap: 16rpx; // 增加间距，让布局更均匀\r\n      width: 100%; // 确保铺满整行\r\n      }\r\n\r\n      .filter-label {\r\n        color: #333333;\r\n        font-size: 28rpx;\r\n        font-weight: 500;\r\n        flex-shrink: 0;\r\n      }\r\n\r\n      .date-picker {\r\n        flex: 1;\r\n        min-width: 180rpx;\r\n        // 移除最大宽度限制，让日期选择器能够更好地利用空间\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        background-color: #F7F7F7;\r\n        padding: 12rpx 16rpx;\r\n        border-radius: 8rpx;\r\n\r\n        text {\r\n          color: #666666;\r\n          font-size: 24rpx;\r\n          white-space: nowrap;\r\n          overflow: hidden;\r\n          text-overflow: ellipsis;\r\n        }\r\n      }\r\n\r\n      .date-separator {\r\n        color: #666666;\r\n        font-size: 24rpx;\r\n        flex-shrink: 0;\r\n      }\r\n\r\n\r\n    .reset-btn {\r\n        width: 35rpx;\r\n        height: 35rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        background-color: transparent;\r\n        border: none;\r\n        transition: all 0.3s ease;\r\n        cursor: pointer;\r\n        flex-shrink: 0;\r\n\r\n        &:active {\r\n          transform: scale(0.85) rotate(360deg);\r\n          opacity: 0.7;\r\n        }\r\n\r\n        &:hover {\r\n          transform: scale(1.1);\r\n          opacity: 0.8;\r\n        }\r\n\r\n        // 刷新动画效果\r\n        &.refreshing {\r\n          animation: refresh-spin 1s linear infinite;\r\n        }\r\n      }\r\n\r\n      // 刷新旋转动画\r\n      @keyframes refresh-spin {\r\n        from {\r\n          transform: rotate(0deg);\r\n        }\r\n        to {\r\n          transform: rotate(360deg);\r\n        }\r\n      }\r\n    \r\n.search-row {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n  gap: 15rpx;\r\n  margin-bottom: 30rpx; /* 添加底部间距，分隔不同的输入框 */\r\n}\r\n\r\n.search-row:last-of-type {\r\n  margin-bottom: 0; /* 最后一个输入框不需要底部间距 */\r\n}\r\n\r\n.search-label {\r\n  color: #333333;\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n  white-space: nowrap;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.search-input-container {\r\n  display: flex;\r\n  align-items: center;\r\n  position: relative;\r\n  flex: 1;\r\n}\r\n\r\n.search-input {\r\n    height: 80rpx;\r\n    background: #F5F5F5;\r\n    border-radius: 8rpx;\r\n    padding: 0 60rpx 0 20rpx;\r\n    font-size: 28rpx;\r\n    border: none;\r\n    outline: none;\r\n    box-sizing: border-box;\r\n    flex: 1;\r\n    overflow: hidden;\r\n    text-overflow: clip;\r\n    white-space: nowrap;\r\n}\r\n\r\n/* 姓名输入框和问题输入框 - 保持相同的宽度 */\r\n.search-input.name-input,\r\n.search-input.question-input {\r\n    flex: 1;\r\n    width: 100%;\r\n}\r\n\r\n.clear-btn {\r\n    position: absolute;\r\n    right: 15rpx;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    width: 40rpx;\r\n    height: 40rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    background: rgba(0, 0, 0, 0.1);\r\n    border-radius: 50%;\r\n    color: #666666;\r\n    font-size: 24rpx;\r\n    font-weight: bold;\r\n    z-index: 10;\r\n    cursor: pointer;\r\n    transition: all 0.2s ease;\r\n\r\n    &:active {\r\n      background: rgba(0, 0, 0, 0.2);\r\n      transform: translateY(-50%) scale(0.9);\r\n    }\r\n}\r\n\r\n.button-row {\r\n  display: flex;\r\n  gap: 20rpx;\r\n  margin-top: 20rpx;\r\n}\r\n\r\n.search-button {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #07C160;\r\n  height: 80rpx;\r\n  border-radius: 8rpx;\r\n  cursor: pointer;\r\n  padding: 0 30rpx;\r\n  flex: 1;\r\n  vertical-align: middle;\r\n}\r\n\r\n.search-button-text {\r\n  color: #FFFFFF;\r\n  font-size: 28rpx;\r\n  margin-left: 10rpx;\r\n}\r\n\r\n.clear-all-button {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #F5F5F5;\r\n  height: 80rpx;\r\n  border-radius: 8rpx;\r\n  cursor: pointer;\r\n  padding: 0 30rpx;\r\n  flex: 1;\r\n  border: 1px solid #E0E0E0;\r\n}\r\n\r\n.clear-all-button-text {\r\n  color: #666666;\r\n  font-size: 28rpx;\r\n  margin-left: 10rpx;\r\n}\r\n\r\n// 回复状态单选按钮样式\r\n.status-radio-container {\r\n  display: flex;\r\n  flex: 1;\r\n  gap: 30rpx;\r\n  align-items: center;\r\n}\r\n\r\n.radio-option {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n  gap: 12rpx;\r\n}\r\n\r\n.radio-circle {\r\n  width: 36rpx;\r\n  height: 36rpx;\r\n  border: 3rpx solid #D0D0D0;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.radio-checked {\r\n  border-color: #07C160;\r\n}\r\n\r\n.radio-dot {\r\n  width: 20rpx;\r\n  height: 20rpx;\r\n  background-color: #07C160;\r\n  border-radius: 50%;\r\n}\r\n\r\n// @我的问题筛选样式\r\n.mention-filter-container {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.mention-filter-option {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.mention-filter-option:hover {\r\n  opacity: 0.8;\r\n}\r\n\r\n.mention-checkbox {\r\n  width: 36rpx;\r\n  height: 36rpx;\r\n  border: 3rpx solid #D0D0D0;\r\n  border-radius: 8rpx;\r\n  margin-right: 16rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.mention-checkbox.mention-checked {\r\n  background-color: #07C160;\r\n  border-color: #07C160;\r\n}\r\n\r\n.mention-label {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  user-select: none;\r\n}\r\n\r\n.mention-filter-option.mention-active .mention-label {\r\n  color: #07C160;\r\n  font-weight: 500;\r\n}\r\n\r\n\r\n\r\n.radio-label {\r\n  font-size: 28rpx;\r\n  color: #333333;\r\n  font-weight: 500;\r\n}\r\n.add-btn {\r\nbackground-color: #07C160;\r\ncolor: #FFFFFF;\r\nfont-size: 28rpx;\r\npadding: 15rpx 25rpx;\r\nborder-radius: 8rpx;\r\nmargin: 20rpx;\r\n\r\n&:disabled {\r\n    background-color: #cccccc;\r\n}\r\n}\r\n.scrollView {\r\n    padding: 0 10rpx;\r\n    padding-top: 0; // 移除顶部内边距，减少空白\r\n}\r\n\r\n.empty-tip {\r\n  color: #999;\r\n  text-align: center;\r\n  margin: 20rpx; // 进一步减少上下间距\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 15rpx; // 减少元素间距\r\n  padding: 15rpx; // 减少内边距\r\n}\r\n\r\n.empty-text {\r\n  font-size: 34rpx; // 增大字体\r\n  color: #333; // 使用更深的颜色，提高可读性\r\n  font-weight: 500;\r\n  line-height: 1.4; // 增加行高\r\n  word-wrap: break-word; // 确保长文本能够换行\r\n}\r\n\r\n.empty-hint {\r\n  font-size: 28rpx; // 增大字体\r\n  color: #666; // 使用更深的颜色，提高可读性\r\n  line-height: 1.4; // 增加行高\r\n  word-wrap: break-word; // 确保长文本能够换行\r\n}\r\n\r\n.retry-button {\r\n  margin-top: 30rpx;\r\n  padding: 16rpx 32rpx;\r\n  background-color: #07C160;\r\n  border-radius: 8rpx;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n.retry-button:active {\r\n  background-color: #06A050;\r\n}\r\n\r\n.retry-text {\r\n  color: #fff;\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n}\r\n// 移除 patient-data 样式，不再需要\r\n\r\n.loading-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 30rpx 20rpx; // 减少内边距\r\n  min-height: 100rpx; // 减少最小高度\r\n}\r\n\r\n.chat-list {\r\n  padding: 0 20rpx; // 移除顶部内边距\r\n  padding-bottom: 100rpx; // 增加底部间距，确保最后一个项目不被遮挡\r\n  margin-top: 70px;\r\n}\r\n\r\n// 为最后一个聊天项添加额外的底部间距\r\n.chat-item:last-child {\r\n  margin-bottom: 120rpx; // 增加最后一个项目的底部空间，确保不被遮挡\r\n}\r\n.chat-item {\r\n  min-height: 160rpx; // 最小高度，允许内容撑开\r\n  height: auto; // 自动高度，根据内容调整\r\n  border-radius: 16rpx; // 增加圆角\r\n  margin: 8rpx 16rpx; // 减少外边距，特别是上下间距\r\n  display: flex;\r\n  align-items: flex-start; // 改为顶部对齐，适应多行内容\r\n  padding: 20rpx 24rpx; // 减少内边距\r\n  background: #ffffff;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  transition: background 0.2s;\r\n  position: relative; // 添加相对定位，为状态标志提供定位基准\r\n  box-sizing: border-box; // 确保padding不会影响总宽度\r\n}\r\n.chat-item:active {\r\n  background: #e6e6e6;\r\n}\r\n.chat-avatar {\r\n  width: 96rpx;\r\n  height: 96rpx;\r\n  border-radius: 50%;\r\n  margin-right: 32rpx; // 增加与内容的间距\r\n  margin-top: 0; // 移除顶部间距，与名字顶部对齐\r\n  object-fit: cover;\r\n  background: #eee;\r\n  flex-shrink: 0; // 防止头像被压缩\r\n  align-self: flex-start; // 确保头像始终在顶部对齐\r\n}\r\n.chat-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: flex-start; // 改为顶部对齐，不再居中\r\n  min-width: 0;\r\n  align-self: flex-start; // 确保内容区域从顶部开始\r\n}\r\n.chat-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start; // 保持顶部对齐\r\n  width: 100%;\r\n  margin-bottom: 12rpx; // 增加底部间距\r\n  margin-top: 0; // 确保没有顶部间距\r\n}\r\n\r\n// 标题行布局\r\n.chat-title-row {\r\n  width: 100%;\r\n  margin: 12rpx 0; // 增加上下间距\r\n}\r\n\r\n.chat-title {\r\n  font-size: 30rpx;\r\n  color: #333;\r\n  font-weight: 600;\r\n  line-height: 1.3;\r\n  display: block;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n// 底部行布局 - 只包含问题内容\r\n.chat-bottom-row {\r\n  display: flex;\r\n  width: 100%;\r\n  padding-right: 140rpx; // 增加为右下角状态标志预留的空间\r\n  margin-top: 8rpx; // 增加与标题的间距\r\n}\r\n\r\n.chat-nickname-container {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  margin-left: 0; // 移除左边距，因为头像已经有足够间距\r\n  min-width: 0;\r\n  align-self: flex-start; // 确保名字容器从顶部开始\r\n}\r\n\r\n.chat-nickname {\r\n  font-size: 32rpx;\r\n  color: #222;\r\n  font-weight: 500;\r\n  line-height: 1.2;\r\n  margin-top: 0; // 确保没有顶部外边距\r\n  padding-top: 0; // 确保没有顶部内边距\r\n}\r\n\r\n.reply-text {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n  margin-top: 4rpx;\r\n  line-height: 1.3;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: baseline;\r\n}\r\n\r\n.reply-target {\r\n  color: #07C160;\r\n  font-weight: 500;\r\n}\r\n\r\n.reply-content {\r\n  color: #999;\r\n  font-style: italic;\r\n  margin-left: 4rpx;\r\n  word-break: break-all;\r\n  flex: 1;\r\n}\r\n\r\n.chat-time {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  margin-left: 16rpx;\r\n  flex-shrink: 0;\r\n  line-height: 1.2;\r\n}\r\n\r\n// 回复状态标志 - 固定在右下角\r\n.reply-status-badge {\r\n  position: absolute; // 使用绝对定位\r\n  bottom: 24rpx; // 增加距离底部的距离，适应新的内边距\r\n  right: 32rpx; // 增加距离右边的距离，适应新的内边距\r\n  padding: 8rpx 18rpx; // 增加内边距，让标志更大一些\r\n  border-radius: 18rpx; // 增加圆角\r\n  font-size: 22rpx;\r\n  font-weight: 500;\r\n  text-align: center;\r\n  min-width: 80rpx;\r\n  box-sizing: border-box;\r\n  z-index: 1; // 确保在其他元素之上\r\n}\r\n\r\n// 待回复状态 - 红色\r\n.status-pending {\r\n  background-color: #ffebee;\r\n  border: 1rpx solid #ffcdd2;\r\n\r\n  .status-text {\r\n    color: #d32f2f;\r\n  }\r\n}\r\n\r\n// 已回复状态 - 绿色\r\n.status-replied {\r\n  background-color: #e8f5e8;\r\n  border: 1rpx solid #c8e6c9;\r\n\r\n  .status-text {\r\n    color: #2e7d32;\r\n  }\r\n}\r\n\r\n\r\n\r\n.status-text {\r\n  font-size: 22rpx;\r\n  font-weight: 500;\r\n  line-height: 1.2;\r\n}\r\n.chat-msg {\r\n  font-size: 28rpx;\r\n  color: #666;\r\n  line-height: 1.4; // 增加行高，提高可读性\r\n  flex: 1; // 占据剩余空间\r\n  word-wrap: break-word; // 允许长单词换行\r\n  word-break: break-all; // 在必要时断开单词\r\n  // 移除单行限制，允许多行显示\r\n  // overflow: hidden;\r\n  // text-overflow: ellipsis;\r\n  // white-space: nowrap;\r\n}\r\n\r\n// @医生信息样式 - 去掉背景色，保持简洁\r\n.mentioned-doctors-row {\r\n  display: flex;\r\n  align-items: center;\r\n  margin: 8rpx 0;\r\n  padding: 8rpx 12rpx;\r\n}\r\n\r\n.mentioned-doctors {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  flex: 1;\r\n}\r\n\r\n.mentioned-doctor {\r\n  font-size: 24rpx;\r\n  color: #07C160;\r\n  font-weight: 500;\r\n  background-color: rgba(7, 193, 96, 0.1);\r\n  padding: 4rpx 8rpx;\r\n  border-radius: 4rpx;\r\n  margin-right: 4rpx;\r\n  margin-bottom: 2rpx;\r\n}\r\n\r\n\r\n</style>", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-data/chat/chat.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "ref", "uni", "http", "onMounted", "onShow", "computed"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAscA,MAAM,YAAY;;;;AAhIlB,UAAM,YAAYA,WAAAA,aAAa;AACzB,UAAA,WAAWC,cAAI,IAAA,EAAE;AACjB,UAAA,cAAcA,kBAAI,KAAK;AACvB,UAAA,eAAeA,kBAAI,KAAK;AAIxB,UAAA,YAAYA,kBAAI,EAAE;AAClB,UAAA,UAAUA,kBAAI,EAAE;AAChB,UAAA,iBAAiBA,kBAAI,EAAE;AACvB,UAAA,WAAWA,kBAAI,IAAI;AAIzB,aAAS,kBAAkB,GAAQ;AACvB,gBAAA,QAAQ,EAAE,OAAO;AAAA,IAAA;AAI7B,aAAS,gBAAgB,GAAQ;AACvB,cAAA,QAAQ,EAAE,OAAO;AAAA,IAAA;AAIrB,UAAA,cAAc,CAAC,MAAW;AAC9B,YAAM,eAAe,EAAE;AACnB,UAAA,eAAe,UAAU,SAAS;AACpC,kBAAU,QAAQ;AAAA,MAAA,WACT,eAAe,UAAU,OAAO;AACzC,gBAAQ,QAAQ;AAAA,MAAA;AAAA,IAEpB;AACgBA,kBAAAA,IAAI,KAAK;AAGzB,UAAM,kBAAkB,MAAM;AAE5B,UAAI,aAAa,OAAO;AACtB;AAAA,MAAA;AAIF,mBAAa,QAAQ;AAGrBC,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,MAAM;AAAA,MAAA,CACP;AAID,gBAAU,QAAQ;AAClB,cAAQ,QAAQ;AAChB,mBAAa,MAAM,cAAc;AACjC,mBAAa,MAAM,WAAW;AAC9B,mBAAa,MAAM,gBAAgB;AACnC,mBAAa,MAAM,aAAa;AAGhC,eAAS,QAAQ,CAAC;AAGlB,kBAAY,QAAQ;AAGN,oBAAA,EAAE,KAAK,MAAM;AACzBA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAAA,CACX;AAGD,mBAAW,MAAM;AACf,uBAAa,QAAQ;AAAA,WACpB,GAAG;AAAA,MAAA,CACP,EAAE,MAAM,CAAC,UAAU;AAClBA,sBAAAA,MAAI,YAAY;AACR,gBAAA,MAAM,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAAA,CACX;AAGD,mBAAW,MAAM;AACf,uBAAa,QAAQ;AAAA,WACpB,GAAG;AAAA,MAAA,CACP;AAAA,IACH;AAGA,UAAM,sBAAsB,MAAM;AAC1B,YAAA,uBAAuB,6BAA6B;AAG1D,UAAI,OAAO,UAAU,SAAS,YAAY,MAAM,GAAG;AACjD,eAAO,CAAC,EACL,aAAa,MAAM,YAAY,aAAa,MAAM,SAAS,KAAK,KACjE,UAAU,SACV,QAAQ,SACR;AAAA,MAAA;AAKG,aAAA,CAAC,EACL,aAAa,MAAM,eAAe,aAAa,MAAM,YAAY,UACjE,aAAa,MAAM,YAAY,aAAa,MAAM,SAAS,KAAK,KACjE,UAAU,SACV,QAAQ,SACR,wBACA,kBAAkB;AAAA,IAEtB;AAGA,UAAM,eAAe,MAAM;AACX,oBAAA;AAAA,IAChB;AAEA,YAAQ,IAAI,iBAAiB,UAAU,SAAS,YAAY;AAOtD,UAAA,kBAAkBD,kBAAI,EAAE;AAGxB,UAAA,eAAe,CAAO,eAAuB;;AAC7C,UAAA;AACF,cAAM,WAAgB,MAAME,gBAAK,IAAI,aAAa;AAAA,UAChD;AAAA,UACA,OAAO,UAAU,SAAS;AAAA,QAAA,CAC3B;AAED,aAAI,qCAAU,cAAW,0CAAU,WAAV,mBAAkB,UAAS;AAClD,iBAAO,SAAS,OAAO;AAAA,QAAA,OAClB;AACE,iBAAA;AAAA,QAAA;AAAA,eAEF,OAAO;AACP,eAAA;AAAA,MAAA;AAAA,IAEX;AAGM,UAAA,eAAe,CAAC,WAAsC;AAE1D,UAAI,CAAC,UAAU,OAAO,KAAA,MAAW,IAAI;AAC5B,eAAA;AAAA,MAAA;AAIT,UAAI,OAAO,WAAW,SAAS,KAAK,OAAO,WAAW,UAAU,GAAG;AAC1D,eAAA;AAAA,MAAA;AAIL,UAAA,OAAO,WAAW,aAAa,GAAG;AAC7B,eAAA;AAAA,MAAA;AAIL,UAAA,OAAO,WAAW,GAAG,GAAG;AAC1B,eAAO,4BAA4B;AAAA,MAAA;AAIrC,UAAI,gBAAgB,SAAS,WAAW,UAAU,SAAS,QAAQ;AACjE,eAAO,gBAAgB;AAAA,MAAA;AAIzB,aAAO,oCAAoC;AAAA,IAC7C;AAGM,UAAA,oBAAoB,CAAC,UAAe;AACxC,YAAM,OAAO,MAAM;AAAA,IACrB;AAEA,UAAM,eAAeF,cAAAA,IAAI;AAAA,MACrB,aAAa;AAAA;AAAA,MACb,UAAU;AAAA;AAAA,MACV,eAAe;AAAA,MACf,YAAY;AAAA,IAAA,CACf;AAGD,UAAM,qBAAqBA,cAAAA,IAAI;AAAA,MAC7B,EAAE,OAAO,OAAO,OAAO,MAAM,SAAS,KAAK;AAAA,MAC3C,EAAE,OAAO,WAAW,OAAO,OAAO,SAAS,MAAM;AAAA,MACjD,EAAE,OAAO,WAAW,OAAO,OAAO,SAAS,MAAM;AAAA,IAAA,CAClD;AAGK,UAAA,oBAAoBA,kBAAI,KAAK;AAKnC,UAAM,yBAAyB,MAAM;AACnC,YAAM,WAAW,mBAAmB,MAAM,KAAK,CAAA,WAAU,OAAO,OAAO;AAChE,aAAA,WAAW,SAAS,QAAQ;AAAA,IACrC;AAGM,UAAA,sBAAsB,CAAC,kBAA0B;AAElC,yBAAA,MAAM,QAAQ,CAAU,WAAA;AAClC,eAAA,UAAU,OAAO,UAAU;AAAA,MAAA,CACnC;AAAA,IACH;AAGA,UAAM,sBAAsB,MAAM;AACd,wBAAA,QAAQ,CAAC,kBAAkB;AAC7C,cAAQ,IAAI,iBAAiB,kBAAkB,QAAQ,OAAO,IAAI;AAAA,IACpE;AAKA,UAAM,gBAAgB,MAAY;AAChC,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAClC,YAAA;AACF,sBAAY,QAAQ;AAGpB,gBAAM,SAAc,CAAC;AAGjB,cAAA,UAAU,SAAS,QAAQ;AACtB,mBAAA,UAAU,UAAU,SAAS;AAAA,UAAA;AAItC,cAAI,UAAU,SAAS,iBAAiB,UAAa,UAAU,SAAS,iBAAiB,IAAI;AAC3F,mBAAO,WAAW,OAAO,UAAU,SAAS,YAAY;AAAA,UAAA;AAI1D,cAAI,OAAO,UAAU,SAAS,YAAY,MAAM,KAAK,aAAa,MAAM,eAAe,aAAa,MAAM,YAAY,QAAQ;AAC5H,mBAAO,cAAc,aAAa,MAAM,YAAY,KAAK;AAAA,UAAA;AAI3D,cAAI,aAAa,MAAM,YAAY,aAAa,MAAM,SAAS,QAAQ;AACrE,mBAAO,WAAW,aAAa,MAAM,SAAS,KAAK;AAAA,UAAA;AAIrD,cAAI,UAAU,OAAO;AACnB,mBAAO,YAAY,UAAU;AAAA,UAAA;AAE/B,cAAI,QAAQ,OAAO;AACjB,mBAAO,UAAU,QAAQ;AAAA,UAAA;AAIvB,cAAA,CAAC,UAAU,SAAS,OAAO;AAC7B,oBAAQ,MAAM,qBAAqB;AACnCC,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YAAA,CACX;AAEDA,0BAAAA,MAAI,SAAS;AAAA,cACX,KAAK;AAAA,YAAA,CACN;AACM,mBAAA,IAAI,MAAM,UAAU,CAAC;AAC5B;AAAA,UAAA;AAGFC,qBAAA,KAAK,IAAI,uBAAuB,MAAM,EAAE,KAAK,CAAC,QAAY;AAErD,gBAAA,IAAI,WAAW,IAAI,QAAO;AACnB,sBAAA,IAAI,mBAAmB,IAAI,MAAM;AAEzC,oBAAM,gBAAgB,IAAI,OAAO,IAAI,CAAC,SAAc;AAClD,oBAAI,iBAAiB;AAGrB,oBAAI,KAAK,eAAe;AACd,0BAAA,IAAI,8BAA8B,KAAK,aAAa;AAGxD,sBAAA,KAAK,kBAAkB,OAAO,KAAK,mBAAmB,YAAY,KAAK,eAAe,QAAQ;AAE/E,qCAAA,KAAK,eAAe,SAAS,KAC1C,KAAK,eAAe,UAAU,GAAG,EAAE,IAAI,QACvC,KAAK;AAAA,kBAAA,OACJ;AAEY,qCAAA;AAAA,kBAAA;AAAA,gBACnB;AAIF,oBAAI,KAAK,WAAW,KAAK,QAAQ,SAAS,GAAG;AAC3C,0BAAQ,IAAI,gBAAgB;AAAA,oBAC1B,MAAM,KAAK;AAAA,oBACX,MAAM,KAAK,QAAQ;AAAA,oBACnB,MAAM,KAAK,QAAQ,IAAI,OAAK,EAAE,YAAY,EAAE,YAAY,MAAM;AAAA,kBAAA,CAC/D;AAAA,gBAAA;AAGI,uBAAA,iCACF,OADE;AAAA;AAAA,kBAGL,aAAa,KAAK,gBAAiB,KAAK,mBAAmB,QAAS;AAAA,kBACpE;AAAA,gBACF;AAAA,cAAA,CACD;AAGG,kBAAA,eAAe,iBAAiB,CAAC;AACrC,oBAAM,gBAAgB,aAAa;AAGnC,kBAAI,OAAO,UAAU,SAAS,YAAY,MAAM,GAAG;AAC3C,sBAAA,gBAAgB,UAAU,SAAS;AAE1B,+BAAA,aAAa,OAAO,CAAC,SAAc;AAE1C,wBAAA,QAAQ,KAAK,WAAW,iBACjB,KAAK,cAAc,iBACnB,KAAK,YAAY,iBACjB,KAAK,eAAe;AAC1B,yBAAA;AAAA,gBAAA,CACR;AAAA,cAAA;AAIH,kBAAI,OAAO,UAAU,SAAS,YAAY,MAAM,KAAK,aAAa,MAAM,eAAe,aAAa,MAAM,YAAY,QAAQ;AAC5H,sBAAM,cAAc,aAAa,MAAM,YAAY,OAAO,YAAY;AACvD,+BAAA,aAAa,OAAO,CAAC,SAAc;AAChD,wBAAM,eAAe,KAAK,eAAe,IAAI,YAAY;AAClD,yBAAA,YAAY,SAAS,WAAW;AAAA,gBAAA,CACxC;AAAA,cAAA;AAIH,kBAAI,aAAa,MAAM,YAAY,aAAa,MAAM,SAAS,QAAQ;AACrE,sBAAM,kBAAkB,aAAa,MAAM,SAAS,OAAO,YAAY;AACxD,+BAAA,aAAa,OAAO,CAAC,SAAc;AAChD,wBAAM,YAAY,KAAK,YAAY,IAAI,YAAY;AAC5C,yBAAA,SAAS,SAAS,eAAe;AAAA,gBAAA,CACzC;AAAA,cAAA;AAIC,kBAAA,UAAU,SAAS,QAAQ,OAAO;AACrB,+BAAA,aAAa,OAAO,CAAC,SAAc;AAC1C,wBAAA,WAAW,KAAK,cAAc,KAAK;AACzC,sBAAI,CAAC;AAAiB,2BAAA;AAEtB,wBAAM,cAAc,SAAS,UAAU,GAAG,EAAE;AAG5C,sBAAI,UAAU,SAAS,cAAc,UAAU,OAAO;AAC7C,2BAAA;AAAA,kBAAA;AAIT,sBAAI,QAAQ,SAAS,cAAc,QAAQ,OAAO;AACzC,2BAAA;AAAA,kBAAA;AAGF,yBAAA;AAAA,gBAAA,CACR;AAAA,cAAA;AAKH,oBAAM,iBAAiB,uBAAuB;AAC9C,kBAAI,mBAAmB,OAAO;AACb,+BAAA,aAAa,OAAO,CAAC,SAAc;AAChD,sBAAI,mBAAmB,WAAW;AAChC,2BAAO,KAAK,WAAW;AAAA,kBAAA,WACd,mBAAmB,WAAW;AACvC,2BAAO,KAAK,WAAW;AAAA,kBAAA;AAElB,yBAAA;AAAA,gBAAA,CACR;AAAA,cAAA;AAIH,kBAAI,OAAO,UAAU,SAAS,YAAY,MAAM,KAAK,kBAAkB,OAAO;AACtE,sBAAA,gBAAgB,UAAU,SAAS;AAC1B,+BAAA,aAAa,OAAO,CAAC,SAAc;AAEhD,sBAAI,KAAK,WAAW,MAAM,QAAQ,KAAK,OAAO,GAAG;AAC/C,2BAAO,KAAK,QAAQ;AAAA,sBAAK,CAAC,WACxB,OAAO,OAAO,iBACd,OAAO,WAAW,iBAClB,OAAO,YAAY;AAAA,oBACrB;AAAA,kBAAA;AAEK,yBAAA;AAAA,gBAAA,CACR;AACD,wBAAQ,IAAI,iBAAiB;AAAA,kBAC3B,QAAQ;AAAA,kBACR,OAAO,aAAa;AAAA,kBACpB,QAAO,+CAAe,WAAU;AAAA,gBAAA,CACjC;AAAA,cAAA;AAGH,uBAAS,QAAQ;AAEjB,kBAAI,uBAAuB;AACzBD,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO,MAAM,SAAS,MAAM,MAAM;AAAA,kBAClC,MAAM;AAAA,kBACN,UAAU;AAAA,gBAAA,CACX;AAAA,cAAA;AAGH,sBAAQ,GAAG;AAAA,YAAA,OACN;AAEC,oBAAA,eAAe,IAAI,WAAW;AACpC,sBAAQ,MAAM,cAAc;AAAA,gBAC1B,SAAS,IAAI;AAAA,gBACb,SAAS,IAAI;AAAA,gBACb,MAAM,IAAI;AAAA,gBACV,QAAQ,IAAI;AAAA,cAAA,CACb;AAEDA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,gBACN,UAAU;AAAA,cAAA,CACX;AAGG,kBAAA,IAAI,SAAS,KAAK;AACpB,wBAAQ,IAAI,kBAAkB;AAC9B,yBAAS,QAAQ,CAAC;AAClB,wBAAQ,GAAG;AAAA,cAAA,OACN;AACE,uBAAA,IAAI,MAAM,YAAY,CAAC;AAAA,cAAA;AAAA,YAChC;AAAA,UACF,CACD,EAAE,MAAM,CAAC,UAAe;AACf,oBAAA,MAAM,aAAa,KAAK;AAGhC,gBAAI,eAAe;AACnB,gBAAI,MAAM,SAAS;AACjB,6BAAe,MAAM;AAAA,YAAA;AAGvBA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YAAA,CACX;AAGD,qBAAS,QAAQ,CAAC;AAClB,mBAAO,KAAK;AAAA,UAAA,CACb;AAAA,iBACM,OAAO;AACN,kBAAA,MAAM,uBAAuB,KAAK;AAE1CA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAAA,CACX;AAGD,mBAAS,QAAQ,CAAC;AAClB,iBAAO,KAAK;AAAA,QAAA,UACZ;AACA,sBAAY,QAAQ;AAAA,QAAA;AAAA,MACtB,CACD;AAAA,IACH;AAQAE,kBAAAA,UAAU,MAAY;AAGpB,UAAI,OAAO,UAAU,SAAS,YAAY,MAAM,KAAK,UAAU,SAAS,UAAU,CAAC,UAAU,SAAS,OAAO,WAAW,MAAM,GAAG;AAC/H,cAAM,UAAU,MAAM,aAAa,UAAU,SAAS,MAAM;AAC5D,YAAI,SAAS;AACX,0BAAgB,QAAQ;AAAA,QAAA;AAAA,MAC1B;AAGY,oBAAA;AAAA,IAAA,EAGf;AAGDC,kBAAAA,OAAO,MAAM;AACG,oBAAA;AAAA,IAAA,CACf;AAID,aAAS,SAAS;AAChBH,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,MAAA,CACN;AAAA,IAAA;AAEH,aAAS,eAAe,IAAS;AACvB,cAAA,IAAI,eAAe,EAAE;AAC7BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,gDAAgD,EAAE;AAAA,MAAA,CACxD;AAAA,IAAA;AAIH,aAAS,iBAAiB,MAAW;AAEnC,UAAI,OAAO,UAAU,SAAS,YAAY,MAAM,GAAG;AAC3C,cAAA,aAAa,UAAU,SAAS;AACtC,cAAM,gBAAgB,KAAK;AAG3B,YAAI,YAAY;AACP,iBAAA;AAAA,QAAA;AAIT,YAAI,eAAe;AACV,iBAAA;AAAA,QAAA;AAIF,eAAA;AAAA,MAAA,OACF;AACL,eAAO,KAAK,iBAAiB;AAAA,MAAA;AAAA,IAC/B;AAIF,aAAS,eAAe,MAAW;AAEjC,UAAI,OAAO,UAAU,SAAS,YAAY,MAAM,GAAG;AACjD,eAAO,UAAU,SAAS,YAAY,UAAU,SAAS,YAAY;AAAA,MAAA,OAChE;AACL,eAAO,KAAK,eAAe;AAAA,MAAA;AAAA,IAC7B;AAMF,aAAS,UAAU;AACjB,cAAQ,IAAI,MAAM;AAGlBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MAAA,CACN;AAAA,IAAA;AAIG,UAAA,aAAaD,kBAAI,KAAK;AAGtB,UAAA,qBAAqB,CAAC,MAAW;AAC/B,YAAA,QAAQ,EAAE,OAAO;AAEnB,UAAA,MAAM,SAAS,IAAI;AACrB,qBAAa,MAAM,cAAc,MAAM,UAAU,GAAG,EAAE;AACtDC,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAAA,CACX;AAAA,MAAA,OACI;AACL,qBAAa,MAAM,cAAc;AAAA,MAAA;AAAA,IAErC;AAGM,UAAA,kBAAkB,CAAC,MAAW;AAC5B,YAAA,QAAQ,EAAE,OAAO;AAEnB,UAAA,MAAM,SAAS,IAAI;AACrB,qBAAa,MAAM,WAAW,MAAM,UAAU,GAAG,EAAE;AACnDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAAA,CACX;AAAA,MAAA,OACI;AACL,qBAAa,MAAM,WAAW;AAAA,MAAA;AAAA,IAElC;AAGA,UAAM,mBAAmB,MAAM;AAC7B,mBAAa,MAAM,cAAc;AAAA,IACnC;AAGA,UAAM,gBAAgB,MAAM;AAC1B,mBAAa,MAAM,WAAW;AAAA,IAChC;AAGA,UAAM,iBAAiB,MAAM;AAC3B,gBAAU,QAAQ;AAClB,cAAQ,QAAQ;AAEhB,UAAI,OAAO,UAAU,SAAS,YAAY,MAAM,GAAG;AACjD,qBAAa,MAAM,cAAc;AAAA,MAAA;AAEnC,mBAAa,MAAM,WAAW;AAEX,yBAAA,MAAM,QAAQ,CAAU,WAAA;AAClC,eAAA,UAAU,OAAO,UAAU;AAAA,MAAA,CACnC;AAED,wBAAkB,QAAQ;AAC1BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MAAA,CACX;AAEa,oBAAA;AAAA,IAChB;AAGA,UAAM,cAAc,MAAM;AACxBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MAAA,CACR;AAEa,oBAAA,EAAE,KAAK,MAAM;AACzBA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAAA,CACX;AAAA,MAAA,CACF,EAAE,MAAM,MAAM;AACbA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAAA,CACX;AAAA,MAAA,CACF;AAAA,IACH;AAG6BI,kBAAAA,SAAS,MAAM;AAC1C,aAAO,aAAa,MAAM,YAAY,SAAS,IAC3C,aAAa,MAAM,YAAY,MAAM,GAAG,CAAC,IAAI,QAC7C,aAAa,MAAM;AAAA,IACxB,CAAA;AAKK,UAAA,sBAAsB,CAAC,WAAmB;AAC9C,cAAQ,QAAQ;AAAA,QACd,KAAK;AACI,iBAAA;AAAA,QACT,KAAK;AACI,iBAAA;AAAA,QACT;AACS,iBAAA;AAAA,MAAA;AAAA,IAEb;AAGM,UAAA,qBAAqB,CAAC,WAAmB;AAC7C,cAAQ,QAAQ;AAAA,QACd,KAAK;AACI,iBAAA;AAAA,QACT,KAAK;AACI,iBAAA;AAAA,QACT;AACS,iBAAA;AAAA,MAAA;AAAA,IAEb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9/BA,GAAG,WAAW,eAAe;"}