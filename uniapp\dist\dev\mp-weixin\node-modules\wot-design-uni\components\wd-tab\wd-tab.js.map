{"version": 3, "file": "wd-tab.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-tab/wd-tab.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC10YWIvd2QtdGFiLnZ1ZQ"], "sourcesContent": ["<template>\n  <view :class=\"`wd-tab ${customClass}`\" :style=\"customStyle\">\n    <view :class=\"['wd-tab__body', { 'wd-tab__body--inactive': !active }]\" v-if=\"shouldBeRender\" :style=\"tabBodyStyle\">\n      <slot />\n    </view>\n  </view>\n</template>\n<script lang=\"ts\">\nexport default {\n  name: 'wd-tab',\n  options: {\n    addGlobalClass: true,\n    virtualHost: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n<script lang=\"ts\" setup>\nimport { getCurrentInstance, ref, watch, type CSSProperties } from 'vue'\nimport { isDef, isNumber, isString, objToStyle } from '../common/util'\nimport { useParent } from '../composables/useParent'\nimport { TABS_KEY } from '../wd-tabs/types'\nimport { computed } from 'vue'\nimport { tabProps } from './types'\n\nconst props = defineProps(tabProps)\n\nconst { proxy } = getCurrentInstance() as any\nconst { parent: tabs, index } = useParent(TABS_KEY)\n\n// 激活项下标\nconst active = computed(() => {\n  return isDef(tabs) ? tabs.state.activeIndex === index.value : false\n})\n\nconst painted = ref<boolean>(active.value) // 初始状态tab不会渲染，必须通过tabs来设置painted使tab渲染\n\nconst tabBodyStyle = computed(() => {\n  const style: CSSProperties = {}\n  if (!active.value && (!isDef(tabs) || !tabs.props.animated)) {\n    style.display = 'none'\n  }\n  return objToStyle(style)\n})\n\nconst shouldBeRender = computed(() => !props.lazy || painted.value || active.value)\n\nwatch(active, (val) => {\n  if (val) painted.value = true\n})\n\nwatch(\n  () => props.name,\n  (newValue) => {\n    if (isDef(newValue) && !isNumber(newValue) && !isString(newValue)) {\n      console.error('[wot design] error(wd-tab): the type of name should be number or string')\n      return\n    }\n    if (tabs) {\n      checkName(proxy)\n    }\n  },\n  {\n    deep: true,\n    immediate: true\n  }\n)\n\n/**\n * @description 检测tab绑定的name是否和其它tab的name冲突\n * @param {Object} self 自身\n */\nfunction checkName(self: any) {\n  const { name: myName } = props\n  if (myName === undefined || myName === null || myName === '') {\n    return\n  }\n  tabs &&\n    tabs.children.forEach((child: any) => {\n      if (child.$.uid !== self.$.uid && child.name === myName) {\n        console.error(`The tab's bound value: ${myName} has been used`)\n      }\n    })\n}\n</script>\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-tab/wd-tab.vue'\nwx.createComponent(Component)"], "names": ["getCurrentInstance", "useParent", "TABS_KEY", "computed", "isDef", "ref", "objToStyle", "watch", "isNumber", "isString"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAQA,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,gBAAgB;AAAA,EAAA;AAEpB;;;;AAUA,UAAM,QAAQ;AAER,UAAA,EAAE,MAAM,IAAIA,iCAAmB;AACrC,UAAM,EAAE,QAAQ,MAAM,MAAM,IAAIC,cAAAA,UAAUC,cAAAA,QAAQ;AAG5C,UAAA,SAASC,cAAAA,SAAS,MAAM;AAC5B,aAAOC,oBAAM,IAAI,IAAI,KAAK,MAAM,gBAAgB,MAAM,QAAQ;AAAA,IAAA,CAC/D;AAEK,UAAA,UAAUC,cAAAA,IAAa,OAAO,KAAK;AAEnC,UAAA,eAAeF,cAAAA,SAAS,MAAM;AAClC,YAAM,QAAuB,CAAC;AAC1B,UAAA,CAAC,OAAO,UAAU,CAACC,cAAA,MAAM,IAAI,KAAK,CAAC,KAAK,MAAM,WAAW;AAC3D,cAAM,UAAU;AAAA,MAAA;AAElB,aAAOE,cAAAA,WAAW,KAAK;AAAA,IAAA,CACxB;AAEK,UAAA,iBAAiBH,uBAAS,MAAM,CAAC,MAAM,QAAQ,QAAQ,SAAS,OAAO,KAAK;AAE5EI,wBAAA,QAAQ,CAAC,QAAQ;AACjB,UAAA;AAAK,gBAAQ,QAAQ;AAAA,IAAA,CAC1B;AAEDA,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,aAAa;AACR,YAAAH,cAAA,MAAM,QAAQ,KAAK,CAACI,cAAAA,SAAS,QAAQ,KAAK,CAACC,uBAAS,QAAQ,GAAG;AACjE,kBAAQ,MAAM,yEAAyE;AACvF;AAAA,QAAA;AAEF,YAAI,MAAM;AACR,oBAAU,KAAK;AAAA,QAAA;AAAA,MAEnB;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,WAAW;AAAA,MAAA;AAAA,IAEf;AAMA,aAAS,UAAU,MAAW;AACtB,YAAA,EAAE,MAAM,OAAA,IAAW;AACzB,UAAI,WAAW,UAAa,WAAW,QAAQ,WAAW,IAAI;AAC5D;AAAA,MAAA;AAEF,cACE,KAAK,SAAS,QAAQ,CAAC,UAAe;AAChC,YAAA,MAAM,EAAE,QAAQ,KAAK,EAAE,OAAO,MAAM,SAAS,QAAQ;AAC/C,kBAAA,MAAM,0BAA0B,MAAM,gBAAgB;AAAA,QAAA;AAAA,MAChE,CACD;AAAA,IAAA;;;;;;;;;;;;;;;;;ACjFL,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}