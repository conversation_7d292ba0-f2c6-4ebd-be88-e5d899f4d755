{"version": 3, "file": "index.js", "sources": ["../../../../../../../src/pages-work/components/echarts/JPie/index.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvcGFnZXMtd29yay9jb21wb25lbnRzL2VjaGFydHMvSlBpZS9pbmRleC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"content\">\r\n    <statusTip v-if=\"pageTips.show\" :status=\"pageTips.status\"></statusTip>\r\n    <echartsUniapp v-else :option=\"option\"></echartsUniapp>\r\n  </view>\r\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { echartProps } from '@/pages-work/components/echarts/props'\r\nimport {\r\n  deepMerge,\r\n  handleTotalAndUnit,\r\n  disposeGridLayout,\r\n  getCustomColor,\r\n} from '../../common/echartUtil'\r\nimport { isNumber } from '@/utils/is'\r\nimport useChartHook from '@/pages-work/components/hooks/useEchart'\r\nimport { deepClone } from '@/uni_modules/da-tree/utils'\r\nimport echartsUniapp from '@/pages-work/components/echarts/index.vue'\r\nimport statusTip from '@/pages-work/components/statusTip.vue'\r\nimport {merge} from \"lodash-es\";\r\n//组件传参\r\nconst props = defineProps({\r\n  ...echartProps,\r\n})\r\n\r\n//最终图表配置项\r\nconst option = ref({})\r\nlet chartOption = {\r\n  legend: {\r\n    show: true,\r\n    data: [],\r\n  },\r\n  tooltip: {\r\n    formatter: (params) => {\r\n      return `${params.name || '空'}:${params.value}`\r\n    },\r\n  },\r\n  series: [\r\n    {\r\n      type: 'pie',\r\n      radius: '72%',\r\n      center: ['50%', '55%'],\r\n      data: [],\r\n      labelLine: { show: true },\r\n      label: {\r\n        show: false,\r\n        position: 'outside',\r\n        formatter: '{b} \\n ({d}%)',\r\n        color: '#B1B9D3',\r\n      },\r\n    },\r\n  ],\r\n}\r\n//图表数据查询\r\nlet [{ dataSource, reload, pageTips, config }, { queryData }] = useChartHook(props, initOption)\r\n\r\n//初始化图表配置项\r\nfunction initOption(data) {\r\n  let chartData: any = dataSource.value\r\n  if (typeof chartData === 'string') {\r\n    chartData = JSON.parse(chartData)\r\n  }\r\n  if (chartData && chartData.length > 0) {\r\n    //显示坐标轴前几项\r\n    if (config.dataFilterNum && isNumber(config.dataFilterNum)) {\r\n      chartData = chartData.slice(0, config.dataFilterNum)\r\n    }\r\n    const colors = getCustomColor(config.option.customColor)\r\n    //设置配色\r\n    chartData = chartData.map((item, index) => {\r\n      let legendColor = config.option.series[0].color ? config.option.series[0].color[index] : null\r\n      return {\r\n        ...item,\r\n        itemStyle: { color: legendColor || colors[index].color || null },\r\n      }\r\n    })\r\n    chartOption.series[0].data = chartData\r\n    chartOption.series[0].roseType = config.option.isRose ? 'radius' : ''\r\n    chartOption.series[0].label.position = config.option.pieLabelPosition || 'outside'\r\n    chartOption.series[0].center = [\r\n      (config.option.grid.left || 50) + '%',\r\n      (config.option.grid.top || 50) + '%',\r\n    ]\r\n\r\n    // 合并配置\r\n    if (props.config && config.option) {\r\n      merge(chartOption, config.option)\r\n      chartOption = handleTotalAndUnit(props.compName, chartOption, config, chartData)\r\n      option.value = deepClone(chartOption)\r\n      pageTips.show = false\r\n    }\r\n  } else {\r\n    pageTips.status = 1\r\n    pageTips.show = true\r\n  }\r\n}\r\n\r\nonMounted(() => {\r\n  queryData()\r\n})\r\n</script>\r\n<style>\r\n.content {\r\n  padding: 10px;\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-work/components/echarts/JPie/index.vue'\nwx.createComponent(Component)"], "names": ["ref", "useChartHook", "isNumber", "getCustomColor", "merge", "handleTotalAndUnit", "deepClone", "onMounted", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBA,MAAA,gBAA0B,MAAA;AAC1B,MAAA,YAAsB,MAAA;;;;;AAGtB,UAAM,QAAQ;AAKR,UAAA,SAASA,cAAI,IAAA,EAAE;AACrB,QAAI,cAAc;AAAA,MAChB,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,MAAM,CAAA;AAAA,MACR;AAAA,MACA,SAAS;AAAA,QACP,WAAW,CAAC,WAAW;AACrB,iBAAO,GAAG,OAAO,QAAQ,GAAG,IAAI,OAAO,KAAK;AAAA,QAAA;AAAA,MAEhD;AAAA,MACA,QAAQ;AAAA,QACN;AAAA,UACE,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ,CAAC,OAAO,KAAK;AAAA,UACrB,MAAM,CAAC;AAAA,UACP,WAAW,EAAE,MAAM,KAAK;AAAA,UACxB,OAAO;AAAA,YACL,MAAM;AAAA,YACN,UAAU;AAAA,YACV,WAAW;AAAA,YACX,OAAO;AAAA,UAAA;AAAA,QACT;AAAA,MACF;AAAA,IAEJ;AAEA,QAAI,CAAC,EAAE,YAAY,QAAQ,UAAU,OAAA,GAAU,EAAE,WAAW,IAAIC,kDAAa,OAAO,UAAU;AAG9F,aAAS,WAAW,MAAM;AACxB,UAAI,YAAiB,WAAW;AAC5B,UAAA,OAAO,cAAc,UAAU;AACrB,oBAAA,KAAK,MAAM,SAAS;AAAA,MAAA;AAE9B,UAAA,aAAa,UAAU,SAAS,GAAG;AAErC,YAAI,OAAO,iBAAiBC,SAAS,SAAA,OAAO,aAAa,GAAG;AAC1D,sBAAY,UAAU,MAAM,GAAG,OAAO,aAAa;AAAA,QAAA;AAErD,cAAM,SAASC,uCAAA,eAAe,OAAO,OAAO,WAAW;AAEvD,oBAAY,UAAU,IAAI,CAAC,MAAM,UAAU;AACzC,cAAI,cAAc,OAAO,OAAO,OAAO,CAAC,EAAE,QAAQ,OAAO,OAAO,OAAO,CAAC,EAAE,MAAM,KAAK,IAAI;AAClF,iBAAA,iCACF,OADE;AAAA,YAEL,WAAW,EAAE,OAAO,eAAe,OAAO,KAAK,EAAE,SAAS,KAAK;AAAA,UACjE;AAAA,QAAA,CACD;AACW,oBAAA,OAAO,CAAC,EAAE,OAAO;AAC7B,oBAAY,OAAO,CAAC,EAAE,WAAW,OAAO,OAAO,SAAS,WAAW;AACnE,oBAAY,OAAO,CAAC,EAAE,MAAM,WAAW,OAAO,OAAO,oBAAoB;AAC7D,oBAAA,OAAO,CAAC,EAAE,SAAS;AAAA,WAC5B,OAAO,OAAO,KAAK,QAAQ,MAAM;AAAA,WACjC,OAAO,OAAO,KAAK,OAAO,MAAM;AAAA,QACnC;AAGI,YAAA,MAAM,UAAU,OAAO,QAAQ;AAC3BC,8BAAA,aAAa,OAAO,MAAM;AAChC,wBAAcC,uCAAmB,mBAAA,MAAM,UAAU,aAAa,QAAQ,SAAS;AACxE,iBAAA,QAAQC,mCAAU,WAAW;AACpC,mBAAS,OAAO;AAAA,QAAA;AAAA,MAClB,OACK;AACL,iBAAS,SAAS;AAClB,iBAAS,OAAO;AAAA,MAAA;AAAA,IAClB;AAGFC,kBAAAA,UAAU,MAAM;AACJ,gBAAA;AAAA,IAAA,CACX;;;;;;;;;;;;;;;;ACnGD,GAAG,gBAAgBC,SAAS;"}