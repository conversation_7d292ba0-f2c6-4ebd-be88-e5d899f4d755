<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationBarTitleText: '医患沟通详情',
        navigationStyle: 'custom'
      },
    }
</route>
<template>
  <view class="chat-detail-page">
    <PageLayout :navLeftArrow="true" navLeftText="返回">
        <view class="page-wrapper">
            <scroll-view class="page-scroll-view" scroll-y="true">
            <!-- 重新设计的患者问题卡片 -->
            <view class="patient-question-card">
              <!-- 患者信息头部 -->
              <view class="patient-header">
                <img class="patient-avatar" :src="getAvatarUrl(searchParams.problem.patientAvatar)" alt="头像" @error="handleAvatarError"/>
                <view class="patient-info">
                  <text class="patient-name">{{ searchParams.problem.patientName }}</text>
                  <text class="question-time">{{ formatTime(searchParams.problem.createTime || searchParams.problem.updateTime || '-') }}</text>
                </view>
              </view>

              <!-- 问题标题区域 -->
              <view v-if="searchParams.problem.title && searchParams.problem.title.trim()" class="title-section">
                <text class="title-text">{{ searchParams.problem.title }}</text>
              </view>

              <!-- 问题内容区域 -->
              <view class="content-section">
                <view class="content-header">
                  <text class="content-label">问题描述</text>
                </view>
                <text class="content-text">{{ searchParams.problem.question }}</text>
              </view>



              <!-- 问题图片区域 - 简化版本 -->
              <view v-if="problemImages.length > 0" class="images-section">
                <view class="images-header">
                  <text class="images-label">相关图片 ({{ problemImages.length }})</text>
                </view>
                <view class="images-grid">
                  <view
                    v-for="(img, index) in problemImages"
                    :key="index"
                    class="image-item"
                    @click="previewSimpleImage(img)"
                  >
                    <image
                      class="question-image"
                      :src="img"
                      mode="aspectFill"
                      @error="handleSimpleImageError"
                    />
                    <!-- 调试信息 -->
                    <view class="image-debug">
                      <text>{{ img }}</text>
                    </view>
                  </view>
                </view>
              </view>



              <!-- @医生信息区域 -->
              <view v-if="mentionedDoctors && mentionedDoctors.length > 0" class="mentioned-section">
                <view class="mentioned-doctors-list">
                  <text
                    v-for="(doctor, index) in mentionedDoctors"
                    :key="doctor.userId || doctor.id || index"
                    class="doctor-tag"
                  >
                    @{{ doctor.realname || doctor.realName || doctor.username || '医生' }}
                  </text>
                </view>
              </view>


            </view>
            <!-- 回复 -->
            <view class="message-container">
              <text class="message-title">回复</text>
              <view v-if="reply && reply.length >0"> 
                <view class="reply" v-for="(rep, index) in reply" :key="index" @click="setReplyTarget(rep)">
                  <image class="reply-avatar" :src="getAvatarUrl(rep.userAvatar)" alt="头像" @error="handleAvatarError"/>
                  <view class="reply-part">
                    <view class="reply-name-container">
                      <text class="reply-name" v-if="rep.userCategory === 0">{{ rep.userName || '未知用户' }}医生</text>
                      <text class="reply-name" v-else>{{ rep.userName || '未知用户' }}</text>
                    </view>
        
                    <view v-if="rep.replyUserId && rep.replyUserId !== '' && rep.replyUserId !== null" class="reply-mention">
                      <text class="mention-text">@{{ findUserNameById(rep.replyUserId) || '未知用户' }}：{{ getSafeReplyContent(rep.replyContent) || getReplyTargetText(rep.replyId, rep) || '原回复内容' }}</text>
                    </view>

                    <text class="reply-content">{{ rep.text || '内容为空' }}</text>

                    <!-- 显示回复的图片 -->
                    <view v-if="getImageList(rep.image).length > 0" class="reply-images">
                      <!-- 调试信息：显示图片数量和原始数据 -->
                      <text class="debug-info" style="font-size: 12px; color: #999; margin-bottom: 5px;">
                        图片 ({{ getImageList(rep.image).length }}) - 原始: {{ rep.image }}
                      </text>
                      <view
                          v-for="(img, imgIndex) in getImageList(rep.image)"
                          :key="imgIndex"
                          class="reply-image-container"
                      >
                        <image
                          class="reply-image"
                          :src="img"
                          mode="aspectFill"
                          @click.stop="previewReplyImage(img, getImageList(rep.image))"
                          @error="handleSimpleImageError"
                        />
                        <!-- 调试信息：显示最终URL -->
                        <text class="debug-url" style="font-size: 10px; color: #666;">
                          {{ img }}
                        </text>
                      </view>
                    </view>
                  </view>

                  <!-- 删除按钮，只有当前用户的评论才显示 -->
                  <view v-if="rep.userId === userStore.userInfo.userid" class="delete-button" @click.stop="deleteReply(rep)">
                    <view class="trash-icon">
                      <view class="trash-lid"></view>
                      <view class="trash-body">
                        <view class="trash-line"></view>
                        <view class="trash-line"></view>
                        <view class="trash-line"></view>
                      </view>
                    </view>
                  </view>

                  <!-- 时间显示在右下角，在删除按钮下面 -->
                  <text class="chat-time">{{ formatTime(rep.createTime || rep.updateTime || '-') }}</text>
                </view>
              </view>
              <view v-else class="empty-reply-container">
                <view class="empty-icon">💬</view>
                <text class="message-empty">暂无回复</text>
                <text class="empty-subtitle">成为第一个回复的人吧</text>
              </view>

            </view>
        </scroll-view>
        </view>
    </PageLayout>

    <!-- 回复窗口 - 移到PageLayout外面 -->
    <view class="reply-window-fixed">
      <!-- @回复提示框紧贴在输入框上方 -->
      <view v-if="replyTarget" class="reply-tip-above-input">
        <span class="reply-content-text">@{{ replyTarget.userName }}：{{ truncatedReplyText }}</span>
        <span class="cancel-reply" @click="replyTarget = null">取消</span>
      </view>

      <view class="reply-input-row">
        <image class="reply-avatar" :src="avatarSrc" alt="头像" @error="handleAvatarError"/>
        <view class="reply-input-area">
          <textarea
          ref="replyTextarea"
          class="reply-textarea"
          v-model="replyContent"
          placeholder="请输入回复内容"
          :focus="textareaFocus"
          :auto-height="true"
          :fixed="true"
          :show-confirm-bar="false"
        ></textarea>
        </view>
        <image src="https://www.mograine.cn/images/upload_picture.jpg" class="upload-button" @click="chooseImage" />
        <button class="sub-reply" :class="{ 'disabled': !canSubmit }" @click="submitReply" :disabled="!canSubmit">提交</button>
      </view>

      <!-- 显示选择的多张图片，独立一行 -->
      <view v-if="replyImages.length > 0" class="reply-input-images">
        <view
          v-for="(img, index) in replyImages"
          :key="index"
          class="reply-input-image"
          @click="previewImage(img)"
        >
          <image class="input-image-preview" :src="img" mode="aspectFill" />
          <view class="remove-image" @click.stop="removeImageByIndex(index)">×</view>
        </view>
      </view>
    </view>


  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, nextTick } from 'vue';
const replyTextarea = ref<HTMLTextAreaElement | null>(null);
import { http } from '@/utils/http';
import { onLoad } from '@dcloudio/uni-app';
import { useUserStore } from '@/store/user'; // 请根据你的实际 store 路径调整

const userStore = useUserStore();
const replyContent = ref('');
const replyImages = ref<string[]>([]); // 改为数组，支持多张图片
const loadingChat = ref(false);
// 问题图片 - 简化版本，使用相对路径
const problemImages = ref<string[]>([]); // 直接存储完整的图片URL

// 简化的图片错误处理
const handleSimpleImageError = (event: any) => {
  try {
    const currentSrc = event?.target?.src;
    console.log('📷 图片加载失败:', currentSrc);

    // 设置默认图片
    event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjVGNUY1Ii8+Cjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIiBmaWxsPSIjOTk5OTk5IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7lm77niYfliqDovb3lpLHotKU8L3RleHQ+Cjwvc3ZnPg==';
  } catch (error) {
    console.error('📷 图片错误处理失败:', error);
  }
};

// 简单的图片预览
const previewSimpleImage = (url: string) => {
  handleImagePreview(url, problemImages.value);
};
const reply = ref<any[]>([]); // 回复列表
const communicationId = ref('');
const mentionedDoctors = ref<any[]>([]); // @的医生列表
// 默认头像，提供多个备选方案
const defAvatar = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNGNUY1RjUiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iIzk5OTk5OSIvPgo8cGF0aCBkPSJNMzAgMzJDMzAgMjYuNDc3MSAyNS41MjI5IDIyIDIwIDIyQzE0LjQ3NzEgMjIgMTAgMjYuNDc3MSAxMCAzMkgzMFoiIGZpbGw9IiM5OTk5OTkiLz4KPC9zdmc+'
const replyTarget = ref<any | null>(null);
const textareaFocus = ref(false); // 用于控制输入框的聚焦状态





onLoad((options) => {
  console.log('页面加载时的参数:', options);
  communicationId.value = options.communication_id || '';
  console.log('communicationId:', communicationId.value);
  
});

// 通用头像处理函数
const getAvatarUrl = (avatar: string | null | undefined) => {
  // 如果头像为空、null、undefined或空字符串，返回默认头像
  if (!avatar || avatar.trim() === '') {
    return defAvatar;
  }

  // 检查是否已经是完整URL
  if (avatar.startsWith('http')) {
    return avatar;
  }

  // 如果是相对路径，拼接基础URL
  if (avatar.startsWith('/')) {
    return 'https://www.mograine.cn' + avatar;
  }

  // 如果是文件名，拼接完整路径
  return 'https://www.mograine.cn/images/' + avatar;
}

// 头像加载错误处理
const handleAvatarError = (event: any) => {
  event.target.src = defAvatar;
}



// 计算属性：返回最终显示的头像URL
const avatarSrc = computed(() => {
  return getAvatarUrl(userStore.userInfo.avatar)
})

// 计算属性：判断是否可以提交
const canSubmit = computed(() => {
  return replyContent.value.trim().length > 0 && !isSubmitting.value;
})



// 截断回复文本，限制显示长度
const truncatedReplyText = computed(() => {
  if (!replyTarget.value) {
    return '';
  }

  // 如果有文本内容，显示截断的文本
  if (replyTarget.value.text && replyTarget.value.text.trim() !== '') {
    return replyTarget.value.text.length > 30
      ? replyTarget.value.text.substring(0, 30) + '...'
      : replyTarget.value.text;
  }

  // 如果没有文本但有图片，显示[图片]
  if (replyTarget.value.image && replyTarget.value.image.trim() !== '') {
    return '[图片]';
  }

  // 既没有文本也没有图片
  return '原回复内容';
})

// 根据用户ID查找用户名
const findUserNameById = (userId: string) => {
  if (!userId || userId === "") {
    return null;
  }

  console.log('查找用户ID:', userId);
  console.log('当前回复列表:', reply.value);

  // 先在回复列表中查找
  const foundInReply = reply.value.find(r => r.userId === userId);
  if (foundInReply) {
    console.log('在回复列表中找到用户:', foundInReply.userName);
    return foundInReply.userName;
  }

  // 如果在回复中没找到，检查是否是患者
  // 注意：这里可能需要根据实际的患者userId来判断
  if (searchParams.value.problem && searchParams.value.problem.patientName) {
    console.log('未在回复中找到，返回患者名称作为备选:', searchParams.value.problem.patientName);
    return searchParams.value.problem.patientName;
  }

  console.log('未找到用户，返回null');
  return null;
}

// 根据回复ID获取被回复的内容文本（截断显示）
const getReplyTargetText = (replyId: string, currentReply: any) => {
  console.log('🔍 查找被回复内容');
  console.log('  - replyId:', replyId);
  console.log('  - currentReply:', currentReply);
  console.log('  - currentReply.replyContent:', currentReply.replyContent);

  if (!replyId || replyId === "") {
    console.log('❌ replyId为空');
    return '原回复内容';
  }

  // 首先检查当前回复是否有replyContent对象，优先使用这个数据
  if (currentReply && currentReply.replyContent) {
    console.log('🔍 检查当前回复的replyContent对象:', currentReply.replyContent);

    // 如果replyContent是对象且有text字段
    if (typeof currentReply.replyContent === 'object' && currentReply.replyContent.text) {
      const replyText = currentReply.replyContent.text.trim();
      if (replyText && replyText !== '') {
        const truncated = replyText.length > 30
          ? replyText.substring(0, 30) + '...'
          : replyText;
        console.log('✅ 从replyContent对象获取到原回复内容:', truncated);
        return truncated;
      }
    }

    // 如果replyContent是字符串
    if (typeof currentReply.replyContent === 'string' && currentReply.replyContent.trim() !== '') {
      const truncated = currentReply.replyContent.length > 30
        ? currentReply.replyContent.substring(0, 30) + '...'
        : currentReply.replyContent;
      console.log('✅ 从replyContent字符串获取到原回复内容:', truncated);
      return truncated;
    }
  }

  // 如果replyContent没有数据，再在回复列表中查找对应的回复内容（根据回复ID）
  const targetReply = reply.value.find((rep: any) => String(rep.id) === String(replyId));

  console.log('🔍 在回复列表中查找结果:', {
    找到目标回复: !!targetReply,
    回复列表中的所有ID: reply.value.map(r => r.id),
    查找的replyId: replyId
  });

  if (targetReply) {
    console.log('✅ 找到目标回复:', {
      id: targetReply.id,
      text: targetReply.text,
      hasText: !!targetReply.text,
      textType: typeof targetReply.text,
      textLength: targetReply.text ? targetReply.text.length : 0
    });

    if (targetReply.text && targetReply.text.trim() !== '') {
      // 截断文本，与弹窗中的 truncatedReplyText 保持一致
      const truncated = targetReply.text.length > 30
        ? targetReply.text.substring(0, 30) + '...'
        : targetReply.text;
      return truncated;
    } else {
      // 如果text为空但有图片，显示[图片]
      if (targetReply.image && targetReply.image.trim() !== '') {
        return '[图片]';
      }
      return '原回复内容';
    }
  }

  // 如果在回复列表中没找到，可能是回复的原问题
  console.log('🔍 未在回复中找到，检查原问题...');
  if (searchParams.value.problem && searchParams.value.problem.question) {
    const questionText = searchParams.value.problem.question;
    const truncated = questionText.length > 30
      ? questionText.substring(0, 30) + '...'
      : questionText;
    console.log('📋 使用原问题内容:', truncated);
    return truncated;
  }

  console.log('❌ 未找到任何匹配内容');
  return '原回复内容';
}

// 安全地获取replyContent内容
const getSafeReplyContent = (replyContent: any): string => {
  if (!replyContent) {
    return '';
  }

  // 如果replyContent是对象，尝试获取其中的text字段
  if (typeof replyContent === 'object' && replyContent !== null) {
    console.log('🔍 replyContent是对象，尝试获取text字段:', replyContent);

    // 检查是否有text字段
    if (replyContent.text && typeof replyContent.text === 'string') {
      const trimmed = replyContent.text.trim();
      if (trimmed && trimmed !== '' && trimmed !== 'null' && trimmed !== 'undefined') {
        console.log('✅ 从replyContent对象中获取到text:', trimmed);
        return trimmed;
      }
    }

    // 如果没有text字段但有images，显示[图片]
    if (replyContent.images && replyContent.images !== null) {
      return '[图片]';
    }

    return '';
  }

  // 如果是字符串类型
  if (typeof replyContent === 'string') {
    const trimmed = replyContent.trim();
    // 如果是空字符串或者是"[object Object]"这样的无效内容，返回空
    if (trimmed === '' || trimmed === '[object Object]' || trimmed === 'null' || trimmed === 'undefined') {
      return '';
    }
    return trimmed;
  }

  // 如果不是字符串也不是对象，尝试转换为字符串
  try {
    const converted = String(replyContent).trim();
    // 检查转换后的结果是否有效
    if (converted === '[object Object]' || converted === 'null' || converted === 'undefined' || converted === '') {
      return '';
    }
    return converted;
  } catch (error) {
    console.warn('replyContent转换失败:', error);
    return '';
  }
}

// 格式化时间显示 - 显示完整的年月日时间
const formatTime = (timeStr: string): string => {
  if (!timeStr || timeStr === '-') {
    return '-';
  }

  try {
    // 如果时间字符串包含日期和时间，显示完整的年月日时间
    if (timeStr.includes(' ')) {
      const parts = timeStr.split(' ');
      if (parts.length >= 2) {
        const datePart = parts[0]; // 日期部分 2025-06-18
        const timePart = parts[1]; // 时间部分 17:33:40

        // 格式化日期显示 (年-月-日 时:分)
        if (datePart.includes('-') && timePart.includes(':')) {
          const dateComponents = datePart.split('-');
          const timeComponents = timePart.split(':');

          if (dateComponents.length >= 3 && timeComponents.length >= 2) {
            const year = dateComponents[0];
            const month = dateComponents[1];
            const day = dateComponents[2];
            const hour = timeComponents[0];
            const minute = timeComponents[1];

            return `${year}-${month}-${day} ${hour}:${minute}`;
          }
        }

        // 如果格式不标准，返回原始时间部分
        return timePart;
      }
    }

    // 如果只有时间部分，直接返回
    return timeStr;
  } catch (error) {
    console.warn('时间格式化失败:', error);
    return timeStr;
  }
}

// 检查回复是否有@信息
const hasReplyInfo = (rep: any) => {
  // 检查 replyUserId 和 replyId 是否存在且不为空（支持 null、undefined、空字符串的判断）
  const hasValidReplyUserId = rep.replyUserId && rep.replyUserId !== "" && rep.replyUserId !== null;
  const hasValidReplyId = rep.replyId && rep.replyId !== "" && rep.replyId !== null;
  const hasInfo = hasValidReplyUserId && hasValidReplyId;

  return hasInfo;
}



const setReplyTarget = (rep: any) => {
  console.log('🎯 设置回复目标:', {
    userId: rep.userId,
    userName: rep.userName,
    id: rep.id,
    text: rep.text
  });

  replyTarget.value = rep;
  textareaFocus.value = false; // 先关闭，确保切换时能生效
  // 可选：聚焦输入框
  nextTick(() => {
    textareaFocus.value = true; // 触发聚焦
    // replyTextarea.value?.focus();
  });
};

const searchParams = ref({
  communication_id: '',
  problem: {
    patientName: '',
    patientAvatar: '',
    images: [],
    question: '',
    title: '', // 添加title字段
    createTime: '', // 添加创建时间字段
    updateTime: '' // 添加更新时间字段
  },
  reply: {
    id: '',
    userAvatar: '',
    userName: '',
    replyContent: '',
    replyImage: '',
    image: [],
    text: ''
  }


});

// 根据医生ID获取医生信息
const fetchDoctorsByIds = async (doctorIds: string | string[]) => {
  try {
    console.log('获取医生信息，doctorIds:', doctorIds);

    // 处理doctorIds，确保是数组格式
    let idsArray: string[] = [];
    if (typeof doctorIds === 'string') {
      idsArray = doctorIds.split(',').filter(id => id.trim() !== '');
    } else if (Array.isArray(doctorIds)) {
      idsArray = doctorIds.filter(id => id && id.trim() !== '');
    }

    if (idsArray.length === 0) {
      console.log('没有有效的医生ID');
      mentionedDoctors.value = [];
      return;
    }

    // 调用医生列表API
    const response = await http.get('/sys/user/doctorList');

    if (response.success && response.result) {
      const allDoctors = response.result;

      // 根据ID筛选出@的医生
      const selectedDoctors = allDoctors.filter((doctor: any) =>
        idsArray.includes(String(doctor.id)) || idsArray.includes(String(doctor.userId))
      );

      mentionedDoctors.value = selectedDoctors;
      console.log('成功获取@医生信息:', selectedDoctors);
    } else {
      console.error('获取医生列表失败:', response.message);
      mentionedDoctors.value = [];
    }
  } catch (error) {
    console.error('获取医生信息异常:', error);
    mentionedDoctors.value = [];
  }
};

const fetchReply = async () => {
  try {
    loadingChat.value = true; // 显示加载状态

    // 验证参数
    if (!communicationId.value) {
      showError('页面参数错误，请重新进入');
      return;
    }

    console.log('获取回复列表，communicationId:', communicationId.value);

    // 简化参数，只传递必要的数据，避免URL过长
    const params = {
      communication_id: communicationId.value,
      user_id: userStore.userInfo.userid || '',
      category: Number(userStore.userInfo.userCategory)
    };
    console.log('获取回复列表请求参数:', params);
    http.get('/communication/getDetail',  params ).then((res:any) => {
        console.log('=== 聊天详情页面接口数据打印 ===');
        console.log('📡 接口地址: GET /communication/getDetail');
        console.log('📤 请求参数:', JSON.stringify(params, null, 2));
        console.log('📥 完整响应数据:', JSON.stringify(res, null, 2));
        console.log('✅ 响应状态:', res.success ? '成功' : '失败');
        console.log('📊 响应代码:', res.code);
        console.log('💬 响应消息:', res.message);
        console.log('⏰ 响应时间戳:', res.timestamp);

        if(res.success && res.result){
          console.log('🎯 业务数据 (res.result):', JSON.stringify(res.result, null, 2));
          console.log('📋 数据结构分析:');
          console.log('  - communication_id:', res.result.communication_id);
          console.log('  - problem 对象:', res.result.problem ? '存在' : '不存在');
          console.log('  - reply 数组:', res.result.reply ? `存在，长度: ${res.result.reply.length}` : '不存在');

          if (res.result.problem) {
            console.log('🔍 问题详情 (problem):');

          }

          if (res.result.reply && Array.isArray(res.result.reply)) {
            console.log('💬 回复列表 (reply):');
            console.log('  - 回复总数:', res.result.reply.length);
            res.result.reply.forEach((item: any, index: number) => {
              console.log(`  📝 回复 ${index + 1}:`, {
                id: item.id,
                用户名: item.userName,
                用户类别: item.userCategory,
                用户ID: item.userId,
                回复内容: item.text,
                图片: item.image,
                创建时间: item.createTime,
                更新时间: item.updateTime,
                回复用户ID: item.replyUserId,
                回复ID: item.replyId,
                头像: item.userAvatar
              });
            });
          }
          console.log('================================');

          console.log('API返回的获取沟通记录', res.result);
          console.log('当前 communicationId:', communicationId.value);
          console.log('返回的 communication_id:', res.result.communication_id);

          // 处理问题图片数组 - 使用完整URL
          const rawImages = res.result.problem.images || [];
          console.log('📷 原始图片数据:', rawImages);

          problemImages.value = rawImages.map((filename: string) => {
            // 如果已经是完整URL，直接返回
            if (filename.startsWith('http')) {
              return filename;
            }

            // 如果是相对路径，构建完整URL
            if (filename.startsWith('/')) {
              return `https://www.mograine.cn${filename}`;
            }

            // 对于纯文件名，构建完整URL
            return `https://www.mograine.cn/tmp/${filename}`;
          });

          console.log('📷 处理后的图片URLs:', problemImages.value);

          // 处理回复数据，添加回复关系信息
          const replyList = res.result.reply || [];
          console.log('原始回复列表:', replyList);
          console.log('回复列表长度:', replyList.length);

          // 直接使用后端返回的回复列表，不进行排序
          const processedReply = replyList;



          // 检查每个回复项的数据结构
          processedReply.forEach((item: any, index: number) => {
            console.log(`📝 回复 ${index}:`, {
              userName: item.userName,
              userAvatar: item.userAvatar,
              text: item.text,
              createTime: item.createTime,
              updateTime: item.updateTime,
              userCategory: item.userCategory,
              userId: item.userId,
              image: item.image,
              imageType: typeof item.image,
              imageLength: Array.isArray(item.image) ? item.image.length : 'not array',
              processedImages: getImageList(item.image), // 添加处理后的图片数组
              finalImageUrls: getImageList(item.image).map(img => getReplyImageUrl(img)), // 最终的图片URL
              replyUserId: item.replyUserId,
              replyId: item.replyId,
              hasReplyInfo: hasReplyInfo(item)
            });

            // 特别关注有图片的回复
            if (item.image && getImageList(item.image).length > 0) {
              console.log(`🖼️ 回复 ${index} 图片详情:`, {
                原始图片数据: item.image,
                处理后图片数组: getImageList(item.image),
                最终显示URLs: getImageList(item.image)
              });
            }
          });



          reply.value = processedReply;
          searchParams.value.problem = res.result.problem || {};
          searchParams.value.reply = res.result.reply || {};

          // 处理@医生信息
          if (res.result.problem && res.result.problem.doctors && Array.isArray(res.result.problem.doctors)) {
            // 优先使用doctors字段（新的API响应格式）
            mentionedDoctors.value = res.result.problem.doctors;
            console.log('📋 获取到@医生信息 (doctors字段):', mentionedDoctors.value);
          } else if (res.result.problem && res.result.problem.mentionedDoctors) {
            // 兼容旧的mentionedDoctors字段
            mentionedDoctors.value = res.result.problem.mentionedDoctors;
            console.log('📋 获取到@医生信息 (mentionedDoctors字段):', mentionedDoctors.value);
          } else if (res.result.problem && res.result.problem.doctorIds) {
            // 如果后端返回的是doctorIds，需要根据ID获取医生信息
            console.log('📋 检测到doctorIds，需要获取医生详细信息:', res.result.problem.doctorIds);
            fetchDoctorsByIds(res.result.problem.doctorIds).catch(error => {
              console.error('获取医生信息失败:', error);
            });
          } else {
            mentionedDoctors.value = [];
            console.log('📋 未检测到@医生信息');
          }
        } else {
          console.log('❌ 接口调用失败:');
          console.log('  - success:', res.success);
          console.log('  - code:', res.code);
          console.log('  - message:', res.message);
          console.log('  - result:', res.result);
          console.log('  - 完整响应:', JSON.stringify(res, null, 2));

          uni.showToast({
            title: res.message || '获取沟通记录失败',
            icon: 'none',
          })
        }
      }).catch((error: any) => {
        console.log('🚨 网络请求异常:');
        console.log('  - 错误类型:', typeof error);
        console.log('  - 错误信息:', error.message || error);
        console.log('  - 完整错误对象:', error);
        console.log('  - 请求参数:', params);
        console.log('  - 接口地址: GET /communication/getDetail');

        uni.showToast({
          title: '网络连接失败，请检查网络后重试',
          icon: 'none',
          duration: 3000
        });
      });
  } finally {
    loadingChat.value = false; // 隐藏加载状态
  }
}

onMounted(() => {
  fetchReply();
});


  
// 从相册选择图片
const chooseFromGallery = () => {
  if (replyImages.value.length >= 6) {
    uni.showToast({ title: '最多只能上传6张图片', icon: 'none' });
    return;
  }

  const remainingCount = 6 - replyImages.value.length;
  uni.chooseImage({
    count: remainingCount,
    sourceType: ['album'],
    success: (res: any) => {
      if (res.tempFilePaths && res.tempFilePaths.length > 0) {
        replyImages.value.push(...res.tempFilePaths);
      }
    },
    fail: (err: any) => {
      console.error('选择图片失败:', err);
      uni.showToast({ title: '选择图片失败', icon: 'none' });
    }
  });
};

// 从相机拍照
const chooseFromCamera = () => {
  if (replyImages.value.length >= 6) {
    uni.showToast({ title: '最多只能上传6张图片', icon: 'none' });
    return;
  }

  uni.chooseImage({
    count: 1,
    sourceType: ['camera'],
    success: (res: any) => {
      if (res.tempFilePaths && res.tempFilePaths.length > 0) {
        replyImages.value.push(...res.tempFilePaths);
      }
    },
    fail: (err: any) => {
      console.error('拍照失败:', err);
      uni.showToast({ title: '拍照失败', icon: 'none' });
    }
  });
};

// 选择图片来源
const chooseImage = () => {
  uni.showActionSheet({
    itemList: ['从相册选择', '拍照'],
    success: (res) => {
      if (res.tapIndex === 0) {
        chooseFromGallery();
      } else if (res.tapIndex === 1) {
        chooseFromCamera();
      }
    },
    fail: (err) => {
      console.error('选择操作失败:', err);
    }
  });
};



  // 上传单张图片到服务器
  const uploadImage = async (tempFilePath: string): Promise<string | null> => {
    return new Promise((resolve, reject) => {
      // 检查文件路径是否有效
      if (!tempFilePath || tempFilePath.trim() === '') {
        reject(new Error('文件路径无效'));
        return;
      }

      // 检查是否是有效的临时文件路径
      if (!tempFilePath.includes('tmp') && !tempFilePath.includes('temp') && !tempFilePath.startsWith('wxfile://')) {
        reject(new Error('文件路径格式不正确'));
        return;
      }

      // 使用正确的上传URL
      const uploadUrl = 'https://www.mograine.cn/api/sys/common/upload';

      uni.uploadFile({
        url: uploadUrl,
        filePath: tempFilePath,
        name: 'file',
        header: {
          'X-Access-Token': userStore.userInfo.token || '',
          'X-Tenant-Id': userStore.userInfo.tenantId || ''
          // 不要设置Content-Type，让浏览器自动设置
        },
        formData: {
          // 可能需要的额外参数
          'biz': 'temp'
        },
        success: (uploadRes) => {
          console.log('📤 图片上传响应:', {
            statusCode: uploadRes.statusCode,
            data: uploadRes.data
          });

          try {
            // 检查响应状态码
            if (uploadRes.statusCode === 404) {
              reject(new Error('上传接口不存在，请检查服务器配置'));
              return;
            }

            if (uploadRes.statusCode === 401 || uploadRes.statusCode === 403) {
              reject(new Error('权限不足，请重新登录'));
              return;
            }

            if (uploadRes.statusCode !== 200) {
              reject(new Error(`上传失败，状态码: ${uploadRes.statusCode}`));
              return;
            }

            // 尝试解析JSON响应
            let result: any;
            if (typeof uploadRes.data === 'string') {
              result = JSON.parse(uploadRes.data);
            } else {
              result = uploadRes.data;
            }

            console.log('📤 解析后的上传响应:', result);

            // 检查不同的响应格式，返回完整URL
            if (result.success && result.message) {
              // 格式1: {success: true, message: "/upload/xxx.jpg"}
              let imageUrl = result.message;
              // 如果不是完整URL，构建完整URL
              if (!imageUrl.startsWith('http')) {
                if (imageUrl.startsWith('/')) {
                  imageUrl = `https://www.mograine.cn${imageUrl}`;
                } else {
                  imageUrl = `https://www.mograine.cn/tmp/${imageUrl}`;
                }
              }
              console.log('📤 格式1 - 图片URL:', imageUrl);
              resolve(imageUrl);
            } else if (result.code === 200 && result.result) {
              // 格式2: {code: 200, result: "/upload/xxx.jpg"} 或 {code: 200, result: "filename.jpg"}
              let imageUrl: string = result.result;
              // 如果不是完整URL，构建完整URL
              if (!imageUrl.startsWith('http')) {
                if (imageUrl.startsWith('/')) {
                  imageUrl = `https://www.mograine.cn${imageUrl}`;
                } else {
                  imageUrl = `https://www.mograine.cn/tmp/${imageUrl}`;
                }
              }
              console.log('📤 格式2 - 图片URL:', imageUrl);
              resolve(imageUrl);
            } else if (result.data) {
              // 格式3: {data: "/upload/xxx.jpg"} 或 {data: "filename.jpg"}
              let imageUrl: string = result.data;
              // 如果不是完整URL，构建完整URL
              if (!imageUrl.startsWith('http')) {
                if (imageUrl.startsWith('/')) {
                  imageUrl = `https://www.mograine.cn${imageUrl}`;
                } else {
                  imageUrl = `https://www.mograine.cn/tmp/${imageUrl}`;
                }
              }
              console.log('📤 格式3 - 图片URL:', imageUrl);
              resolve(imageUrl);
            } else {
              console.log('📤 上传响应格式不匹配，完整响应:', result);
              reject(new Error(result.message || result.msg || '上传失败，响应格式不正确'));
            }
          } catch (parseError) {
            reject(new Error('服务器响应格式错误'));
          }
        },
        fail: () => {
          reject(new Error('上传失败，请重试'));
        }
      });
    });
  }
console.log("uploadedUrl",replyImages.value)
// 防重复提交标志
const isSubmitting = ref(false);

// 通用验证函数
const showError = (message: string) => {
  uni.showToast({ title: message, icon: 'none' });
};

const validateSubmission = () => {
  // 防重复提交
  if (isSubmitting.value) {
    showError('正在提交中，请勿重复操作');
    return false;
  }

  // 验证必须输入文本内容
  if (!replyContent.value.trim()) {
    showError('请输入回复内容');
    // 自动聚焦到输入框，先重置再设置确保聚焦生效
    textareaFocus.value = false;
    nextTick(() => {
      textareaFocus.value = true;
    });
    return false;
  }

  // 验证内容长度
  if (replyContent.value.trim().length > 1000) {
    showError('回复内容不能超过1000个字符');
    return false;
  }

  // 验证必要参数
  if (!communicationId.value) {
    showError('页面参数错误，请重新进入');
    return false;
  }

  if (!userStore.userInfo.userid) {
    showError('用户信息错误，请重新登录');
    return false;
  }

  return true;
};

// 提交回复方法
const submitReply = async () => {
  if (!validateSubmission()) return;

  try {
    isSubmitting.value = true; // 设置提交状态
    uni.showLoading({ title: '提交中...' });

    // 构建请求数据 - 严格按照后端接口文档格式
    const requestData: any = {
      communicationId: String(communicationId.value), // 问题id (string) - 必填
      userId: String(userStore.userInfo.userid), // 用户id (string) - 必填
      replyUserId: "", // 被回复的用户id (string) - 可选，没有时传空字符串
      replyId: "", // 被回复的信息的id (string) - 可选，没有时传空字符串
      text: "", // 回复内容 (string) - 可选，没有时传空字符串
      images: [] // 回复的图片 (string | null) - 可选，注意字段名是 images
    };

    // 处理 text 字段 - 根据接口文档，传空字符串而不是null
    const trimmedText = replyContent.value.trim();
    if (trimmedText && trimmedText.length > 0) {
      requestData.text = trimmedText;
    }
    // 如果没有文本内容，保持为空字符串

    // 处理图片数据 - 先上传图片再提交
    if (replyImages.value.length > 0) {
      try {
        uni.showLoading({ title: '上传图片中...' });

        const uploadedUrls: string[] = [];

        // 逐个上传图片
        for (const tempPath of replyImages.value) {
          // 如果已经是URL，直接使用
          if (tempPath.startsWith('http')) {
            uploadedUrls.push(tempPath);
          } else {
            // 上传本地图片
            const uploadedUrl = await uploadImage(tempPath);
            if (uploadedUrl) {
              uploadedUrls.push(uploadedUrl);
            } else {
              uni.hideLoading();
              showError('图片上传失败，请重试');
              return;
            }
          }
        }

        uni.hideLoading();

        if (uploadedUrls.length === 0) {
          showError('图片上传失败，请重新选择图片');
          return;
        }

        // 直接传数组给后端
        requestData.images = uploadedUrls;

        // 同时设置 image 字段，以防后端需要这个字段来返回数据
        // 如果只有一张图片，直接设置字符串；多张图片设置为逗号分隔的字符串
        if (uploadedUrls.length === 1) {
          requestData.image = uploadedUrls[0];
        } else if (uploadedUrls.length > 1) {
          requestData.image = uploadedUrls.join(',');
        }
      } catch (error) {
        uni.hideLoading();
        console.error('图片上传失败:', error);
        showError('图片上传失败，请重试');
        return;
      }
    }

    // 处理回复相关字段 - 如果是回复别人的评论，设置相应字段；否则保持空字符串
    if (replyTarget.value && replyTarget.value.userId && replyTarget.value.id) {
      requestData.replyUserId = String(replyTarget.value.userId);
      requestData.replyId = String(replyTarget.value.id);

      // 添加replyContent字段，传入被回复的内容
      if (replyTarget.value.text && replyTarget.value.text.trim() !== '') {
        requestData.replyContent = replyTarget.value.text.trim();
      }

    }
    // 如果没有回复目标，这两个字段保持为空字符串（已在初始化时设置）

    // 简化的数据验证
    const validateRequestData = (data: any) => {
      if (!data.communicationId || !data.userId) {
        showError('数据验证失败，请重试');
        return false;
      }

      if (data.text && data.text.length > 2000) {
        showError('回复内容不能超过2000个字符');
        return false;
      }

      if (!/^\d+$/.test(data.communicationId) || !/^\d+$/.test(data.userId)) {
        showError('数据格式错误');
        return false;
      }

      if (!data.text && !data.images) {
        showError('请输入回复内容或选择图片');
        return false;
      }

      return true;
    };

    if (!validateRequestData(requestData)) return;

    // 打印请求数据用于调试
    console.log('📤 提交回复请求数据:', {
      ...requestData,
      imagesCount: requestData.images ? requestData.images.length : 0,
      hasImages: !!requestData.images && requestData.images.length > 0,
      imageUrls: requestData.images || [],
      imageField: requestData.image || 'undefined'
    });

    if (requestData.images && requestData.images.length > 0) {
      console.log('📸 提交的图片数据:', {
        images数组: requestData.images,
        image字段: requestData.image,
        图片数量: requestData.images.length
      });
    }

    // 使用 http 工具发送请求，与其他接口保持一致
    // http 工具会自动添加 X-Access-Token 等必要的请求头
    try {
      const response = await http.post('/communication/reply', requestData);
      console.log('📥 提交回复响应数据:', response);

      if (response && response.result) {
        console.log('📝 新创建的回复数据:', response.result);
      }

      if (response && response.success) {
        uni.showToast({ title: '提交成功', icon: 'success' });

        // 清空输入内容
        replyContent.value = '';
        replyImages.value = [];
        replyTarget.value = null;

        // 多次刷新确保数据完整显示
        // 立即刷新
        fetchReply();

        // 短延迟刷新
        setTimeout(() => {
          fetchReply();
        }, 800);

        // 长延迟刷新，确保服务器数据已完全更新
        setTimeout(() => {
          fetchReply();
        }, 2000);
      } else {
        // 详细的错误处理
        let errorMsg = '提交失败';

        if (response?.message) {
          errorMsg = response.message;

          // 针对数据库错误的特殊处理
          if (response.message.includes('完整性例如') || response.message.includes('字段内容超出长度')) {
            errorMsg = '内容过长或格式不正确，请检查后重试';
          } else if (response.message.includes('违反唯一约束')) {
            errorMsg = '数据重复，请勿重复提交';
          } else if (response.message.includes('违反非空限制')) {
            errorMsg = '必填信息缺失，请完善后重试';
          }
        } else if (response?.msg) {
          errorMsg = response.msg;
        }

        console.error('提交失败详情:', {
          response,
          requestData,
          errorMsg
        });

        uni.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 3000 // 延长显示时间
        });
      }
    } catch (error) {
      console.error('提交异常:', error);
      uni.showToast({
        title: '网络连接异常，请检查网络后重试',
        icon: 'none',
        duration: 3000
      });
    }
  } finally {
    isSubmitting.value = false; // 重置提交状态
    uni.hideLoading();
  }
};

// 删除回复方法
const deleteReply = async (rep: any) => {
  try {
    // 显示确认对话框
    const result = await new Promise((resolve) => {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这条评论吗？',
        success: (res) => {
          resolve(res.confirm);
        },
        fail: () => {
          resolve(false);
        }
      });
    });

    if (!result) {
      return; // 用户取消删除
    }

    uni.showLoading({ title: '删除中...' });

    // DELETE请求使用查询参数，注意参数名要与服务器期望的一致
    const queryParams = `reply_id=${rep.id}&user_id=${userStore.userInfo.userid}&communicationId=${communicationId.value}`;

    const res = await http.delete(`/communication/del_reply?${queryParams}`);

    if (res.success) {
      uni.showToast({ title: '删除成功', icon: 'success' });
      fetchReply(); // 刷新回复列表
    } else {
      uni.showToast({ title: res.message || '删除失败', icon: 'none' });
    }
  } catch (e) {
    console.error('删除评论失败:', e);
    uni.showToast({ title: '删除失败', icon: 'none' });
  } finally {
    uni.hideLoading();
  }
};

// 通用图片预览函数
const handleImagePreview = (url: string, imageList: string[]) => {
  // 过滤有效的图片URL
  const validUrls = imageList.filter(img => img && typeof img === 'string' && img.trim() !== '');

  if (validUrls.length === 0) {
    showError('没有可预览的图片');
    return;
  }

  // 确保当前URL有效
  const currentUrl = url && typeof url === 'string' && url.trim() !== '' ? url : validUrls[0];

  uni.previewImage({
    current: currentUrl,
    urls: validUrls
  });
};

// 预览图片方法 - 已移动到上面的 previewProblemImage 函数

// 预览回复中的图片
const previewReplyImage = (url: string, imageList: string[]) => {
  handleImagePreview(url, imageList);
};

// 根据索引移除指定的图片
const removeImageByIndex = (index: number) => {
  replyImages.value.splice(index, 1);
  uni.showToast({
    title: '图片已移除',
    icon: 'success',
    duration: 1000
  });
}

// 预览图片（模仿addChat的逻辑）
const previewImage = (url: string) => {
  uni.previewImage({
    current: url,
    urls: replyImages.value
  });
};

// 工具函数：保证图片字段为数组，并返回完整URL
const getImageList = (imgField: any): string[] => {
  if (!imgField) return [];

  let imageArray: string[] = [];

  if (Array.isArray(imgField)) {
    imageArray = imgField;
  } else if (typeof imgField === 'string') {
    // 逗号分隔或单个图片
    if (imgField.startsWith('[')) {
      try {
        const arr = JSON.parse(imgField);
        imageArray = Array.isArray(arr) ? arr : [];
      } catch {
        imageArray = [];
      }
    } else if (imgField.includes(',')) {
      imageArray = imgField.split(',').map(s => s.trim()).filter(Boolean);
    } else if (imgField.trim() !== '') {
      imageArray = [imgField];
    }
  }

  // 将所有图片转换为完整URL
  return imageArray.map(img => {
    if (!img) return '';

    // 如果已经是完整URL，直接返回
    if (img.startsWith('http')) {
      return img;
    }

    // 如果是相对路径，构建完整URL
    if (img.startsWith('/')) {
      return `https://www.mograine.cn${img}`;
    }

    // 对于纯文件名，构建完整URL
    return `https://www.mograine.cn/tmp/${img}`;
  }).filter(Boolean);
};

// 工具函数：构建回复图片的完整URL
const getReplyImageUrl = (img: string): string => {
  if (!img) return '';

  // 如果已经是完整URL，直接返回
  if (img.startsWith('http')) {
    return img;
  }

  // 如果是相对路径，构建完整URL
  if (img.startsWith('/')) {
    return `https://www.mograine.cn${img}`;
  }

  // 对于纯文件名，构建完整URL
  return `https://www.mograine.cn/tmp/${img}`;
};




</script>

<style lang="scss" scoped>
.chat-detail-page {
  position: relative !important;
  width: 100% !important;
  height: 100vh !important;
  background-color: #f8f9fa;        // 改为淡灰色背景
  overflow: visible !important;      // 确保子元素可见
}

// 全局页面背景
:deep(.page-layout) {
  background-color: #f8f9fa !important; // 改为淡灰色背景
  position: relative !important;
  overflow: visible !important;
}

:deep(.page-layout-content) {
  background-color: #f8f9fa !important; // 改为淡灰色背景
  position: relative !important;
  overflow: visible !important;
}

.page-wrapper {
  background-color: #f8f9fa;        // 改为淡灰色背景，突出悬浮框
  min-height: 100vh;
  width: 100%;
}
.page-scroll-view {
    height: 100%;
    box-sizing: border-box;
    background-color: transparent;    // 改为透明背景
    padding-bottom: 180px;           // 增加底部内边距，确保不遮挡最后一条评论
}
// 重新设计的患者问题卡片
.patient-question-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    margin: 16px;
    padding: 0;
    box-shadow: 0 8px 32px rgba(0,0,0,0.12);
    border: 1px solid #e9ecef;
    overflow: hidden;
}

// 患者信息头部样式
.patient-header {
    display: flex;
    align-items: center;
    padding: 20px;
    background-color: #ffffff;
    border-bottom: 1px solid #e9ecef;
}

.patient-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    border: 2px solid #e9ecef;
    object-fit: cover;
    background-color: #f8f9fa;
}

.patient-info {
    margin-left: 12px;
    flex: 1;
}

.patient-name {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    display: block;
    margin-bottom: 4px;
}

.question-time {
    font-size: 12px;
    color: #6c757d;
    display: block;
}

// 标题区域样式 - 浮窗设计
.title-section {
    margin: 16px 20px;
    padding: 16px 20px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
    position: relative;
}

.title-section::before {
    content: '';
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 28px;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-radius: 2px;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.title-text {
    font-size: 18px;
    font-weight: 700;
    color: #1a1a1a;
    line-height: 1.4;
    margin-left: 20px;
    letter-spacing: 0.3px;
}

// 内容区域样式
.content-section {
    padding: 20px;
}

.content-header {
    margin-bottom: 12px;
}

.content-label {
    font-size: 14px;
    font-weight: 600;
    color: #6c757d;
}

.content-text {
    font-size: 16px;
    line-height: 1.6;
    color: #2c3e50;
    word-wrap: break-word;
}
// 图片区域样式
.images-section {
    padding: 0 20px 20px 20px;
    border-bottom: 1px solid #e9ecef;
}

.images-header {
    margin-bottom: 12px;
}

.images-label {
    font-size: 14px;
    font-weight: 600;
    color: #6c757d;
}

.images-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
}

.image-item {
    position: relative;
    aspect-ratio: 1;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 2px solid transparent;

    // 加载状态
    &.loading {
        border-color: #07C160;
    }

    // 错误状态
    &.error {
        border-color: #ff4444;
        opacity: 0.5;
    }

    // 成功状态
    &.success {
        border-color: transparent;
    }
}

.question-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.2s ease;
}

.image-item:active .question-image {
    transform: scale(0.95);
}

.image-overlay {
    position: absolute;
    top: 6px;
    right: 6px;
    background: rgba(0,0,0,0.6);
    color: white;
    border-radius: 12px;
    padding: 2px 6px;
}

.image-index {
    font-size: 10px;
    font-weight: 600;
}

.image-debug {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 2px 4px;
    font-size: 9px;
    line-height: 1.2;

    text {
        display: block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

// 旧样式已移除，使用新的标题区域样式
.message-container {
    padding: 0 0 20px 0;             // 只保留底部内边距
    background-color: #fff;
    border-radius: 16px;             // 保持圆角
    margin: 16px;                    // 统一边距
    display: flex;
    flex-direction: column;
    align-items: stretch;            // 改为stretch，让子元素可以自由对齐
    box-shadow: 0 2px 12px rgba(0,0,0,0.08); // 保持阴影
    border: 1px solid #f0f0f0;       // 保持边框
    overflow: hidden;                // 确保圆角效果
}
// 旧的消息内容和图片样式已移除，使用新的内容区域和图片区域样式
.message-title {
    font-size: 18px;                 // 稍微减小字体
    color: #333;
    margin-bottom: 0;                // 移除底部边距
    font-weight: 600;                // 增加字重
    padding: 20px 20px 16px 20px;    // 添加内边距，底部稍小
}
.reply {
    padding: 16px 20px 50px 20px;    // 增加底部内边距，为时间留出空间
    margin: 0;                       // 移除负边距，保持在容器内
    width: 100%;                     // 占满父容器宽度
    display: flex;
    flex-direction: row;
    align-items: flex-start;         // 顶端对齐
    position: relative;              // 为删除按钮和时间定位做准备
    background-color: transparent;   // 透明背景，继承父容器背景
    box-sizing: border-box;          // 确保盒模型一致
    cursor: pointer;                 // 鼠标指针样式
    transition: background-color 0.2s ease; // 背景色过渡动画

    // 禁用移动端点击高亮效果
    -webkit-tap-highlight-color: transparent; // 禁用 WebKit 点击高亮
    -webkit-touch-callout: none;     // 禁用长按菜单
    -webkit-user-select: none;       // 禁用文本选择
    -moz-user-select: none;          // Firefox 禁用文本选择
    -ms-user-select: none;           // IE 禁用文本选择
    user-select: none;               // 标准属性禁用文本选择
}

// 回复区域悬停效果
.reply:hover {
    background-color: rgba(0, 0, 0, 0.03); // 淡灰色悬停背景
}

// 移除点击效果，保持原始背景
.reply:active {
    background-color: transparent; // 点击时保持透明背景
}



.reply-content {
  font-size: 14px;                 // 适中的字体大小
  color: #333;                     // 深色文字，提高可读性
  line-height: 1.4;                // 适中的行高
  margin-top: 6px;                 // 减少上边距
  margin-bottom: 4px;              // 减少下边距
  white-space: pre-wrap;           // 保持空格和换行，允许自动换行
  word-wrap: break-word;           // 长单词自动换行
  word-break: break-word;          // 在任意字符间换行
  width: 100%;                     // 占满可用宽度
  box-sizing: border-box;          // 包含padding和border
}

// 旧的绝对定位时间样式已移除，时间现在显示在头部

// @医生区域样式 - 去掉背景色，保持简洁
.mentioned-section {
    margin: 16rpx 0;
    padding: 16rpx 24rpx;
}

.mentioned-doctors-list {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 8rpx;
}

.doctor-tag {
    font-size: 24rpx;
    color: #07C160;
    font-weight: 500;
    background-color: rgba(7, 193, 96, 0.1);
    padding: 8rpx 16rpx;
    border-radius: 8rpx;
    margin-right: 8rpx;
    margin-bottom: 4rpx;
}

// 回复时间样式 - 显示在回复框右下角，删除按钮下面
.chat-time {
  font-size: 12px;                 // 保持小字体
  color: #999;
  position: absolute;              // 绝对定位
  bottom: 16px;                    // 距离底部16px
  right: 20px;                     // 距离右边20px
  white-space: nowrap;             // 防止时间换行
  min-width: 120px;                // 确保有足够空间显示完整的年月日时间
  text-align: right;               // 文本右对齐
}
.reply-part {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;     // 顶端对齐
    flex: 1;                         // 占据剩余空间
    min-width: 0;                    // 确保flex子项能正确收缩
    margin-left: 16px;               // 增加与头像的间距
    padding-right: 60px;             // 为删除按钮预留更多空间
}

.reply-name-container {
    display: flex;
    flex-direction: column;
    margin-left: 0;                  // 移除左边距
    margin-bottom: 0;                // 移除底边距
}

.reply-name {
    font-size: 15px;                 // 稍微减小字体
    color: #333;
    line-height: 1.3;
    font-weight: 500;                // 适中的字重
    margin-bottom: 2px;              // 减少与内容的间距
}

// @ 回复信息样式
.reply-mention {
    margin: 4px 0;                   // 上下间距
}

.mention-text {
    font-size: 13px;                 // 稍小的字体
    color: #07C160;                  // 主题绿色
    background-color: #F0F9F5;       // 淡绿色背景
    padding: 2px 6px;                // 内边距
    border-radius: 4px;              // 圆角
    font-weight: 400;                // 正常字重
    display: block;                  // 块级元素
    white-space: nowrap;             // 不换行
    overflow: hidden;                // 隐藏超出部分
    text-overflow: ellipsis;         // 显示省略号
    max-width: 100%;                 // 最大宽度为容器宽度
}

// replyContent 引用内容样式
.reply-content-quote {
    margin: 4px 0;                   // 上下间距
    padding: 8px 12px;               // 内边距
    background-color: #f5f5f5;       // 浅灰色背景
    border-left: 3px solid #ddd;     // 左侧边框，表示引用
    border-radius: 4px;              // 圆角
}

.quote-text {
    font-size: 13px;                 // 稍小的字体
    color: #666;                     // 灰色文字
    font-style: italic;              // 斜体，表示引用
    line-height: 1.4;                // 行高
}



.delete-button {
  position: absolute;
  top: 16px;                   // 距离回复容器顶部16px
  right: 20px;                 // 距离回复容器右边20px
  width: 28px;
  height: 28px;
  background: rgba(128, 128, 128, 0.1);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0.8;
  transition: all 0.3s ease;
  z-index: 10;
  border: 1px solid rgba(128, 128, 128, 0.2);
  box-sizing: border-box;      // 确保盒模型一致
  flex-shrink: 0;              // 防止按钮被压缩
  flex-grow: 0;                // 防止按钮被拉伸
}

.delete-button:hover {
  opacity: 1;
  transform: scale(1.05);
  background: rgba(128, 128, 128, 0.15);
  border-color: rgba(128, 128, 128, 0.3);
}

// 垃圾筒图标样式
.trash-icon {
  position: relative;
  width: 12px;
  height: 14px;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-shrink: 0;              // 防止图标被压缩
  flex-grow: 0;                // 防止图标被拉伸
}

.trash-lid {
  width: 14px;
  height: 2px;
  background: #666;
  border-radius: 1px;
  position: relative;
  margin-bottom: 1px;
  flex-shrink: 0;              // 防止盖子被压缩
}

.trash-lid::before {
  content: '';
  position: absolute;
  top: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 2px;
  background: #666;
  border-radius: 1px 1px 0 0;
}

.trash-body {
  width: 10px;
  height: 11px;
  background: transparent;
  border: 1.5px solid #666;
  border-top: none;
  border-radius: 0 0 2px 2px;
  position: relative;
  display: flex;
  justify-content: space-around;
  align-items: flex-start;
  padding-top: 2px;
  flex-shrink: 0;              // 防止主体被压缩
  box-sizing: border-box;      // 确保边框计算一致
}

.trash-line {
  width: 1px;
  height: 6px;
  background: #666;
  border-radius: 0.5px;
  flex-shrink: 0;              // 防止竖线被压缩
}

// 暂无回复容器 - 确保完美居中
.empty-reply-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;               // 增加上下内边距
  background: transparent;          // 透明背景
  margin: 0 auto;                   // 水平居中
  width: 100%;                      // 占满宽度
  min-height: 200px;                // 设置最小高度，确保有足够空间居中
  text-align: center;               // 文本居中
}

// 图标样式 - 居中显示
.empty-icon {
  font-size: 32px;                  // 适中的图标大小
  margin-bottom: 12px;              // 适中间距
  opacity: 0.6;                     // 适中的透明度
  display: block;                   // 确保块级显示
  text-align: center;               // 文本居中
}

// 主文本样式 - 居中显示
.message-empty {
  color: #666;                      // 适中的颜色
  font-size: 16px;                  // 适中的字体
  font-weight: 500;                 // 稍微加粗
  margin-bottom: 8px;               // 适中间距
  text-align: center;               // 文本居中
  display: block;                   // 确保块级显示
  width: 100%;                      // 占满宽度
}

// 副标题样式 - 居中显示
.empty-subtitle {
  color: #999;                      // 适中的颜色
  font-size: 14px;                  // 适中字体
  font-weight: 400;
  text-align: center;               // 文本居中
  display: block;                   // 确保块级显示
  width: 100%;                      // 占满宽度
}



.reply-window-fixed {
  position: fixed !important;       // 改回固定定位
  left: 16px !important;            // 左边距
  right: 16px !important;           // 右边距
  bottom: 20px !important;          // 距离底部20px
  background: #fff !important;      // 恢复白色背景
  border-radius: 16px !important;   // 全圆角，悬浮框效果
  padding: 16px !important;         // 统一内边距
  box-sizing: border-box;
  box-shadow: 0 4px 20px rgba(0,0,0,0.15) !important; // 增强阴影，悬浮效果
  display: flex !important;
  flex-direction: column;           // 垂直布局，@框在上，输入行在下
  z-index: 9999 !important;         // 高层级
  width: auto !important;           // 自动宽度
  min-height: 60px !important;      // 减少最小高度
  visibility: visible !important;   // 确保可见
  opacity: 1 !important;            // 确保不透明
  border: 1px solid #e0e0e0;        // 添加边框
}

// @回复提示框紧贴输入框上方
.reply-tip-above-input {
  background: #f8f9fa;
  color: #666;
  border-radius: 12px;              // 增加圆角
  padding: 10px 16px;               // 调整内边距，左右增加
  font-size: 13px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  border: 1px solid #e9ecef;        // 添加边框
  margin: 0 0 12px 0;               // 移除左右边距，只保留底部边距
  min-height: 40px;                 // 适中的最小高度
  width: 100%;                      // 占满整个宽度
  animation: slideDown 0.3s ease-out;
}

// 输入框行布局
.reply-input-row {
  display: flex;
  flex-direction: row;
  align-items: center;              // 垂直居中对齐
  gap: 8px;                         // 元素间距
  min-height: 50px;                 // 最小高度
}
.reply-avatar {
  width: 42px !important;           // 稍微增大头像
  height: 42px !important;
  border-radius: 50%;
  flex-shrink: 0 !important;
  flex-grow: 0 !important;
  object-fit: cover;
  background-color: #f0f0f0;
  display: block !important;
  box-sizing: border-box;
  border: 2px solid #fff;           // 添加白色边框
  box-shadow: 0 2px 8px rgba(0,0,0,0.1); // 添加轻微阴影
}

// 回复中的图片样式
.reply-images {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 8px 0;
}

.reply-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  object-fit: cover;
  cursor: pointer;
  border: 1px solid #e0e0e0;
}

// 输入框中的多张图片容器
.reply-input-images {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin: 8px 16px 16px 16px;  // 简单的外边距
  max-width: calc(100% - 32px);
  justify-content: flex-start;
  padding: 8px;                     // 简单的内边距
}

// 输入框中的单张图片预览
.reply-input-image {
  position: relative;
  display: inline-block;
  flex-shrink: 0;
  width: calc(33.333% - 4px);  // 一行3个，减去gap的影响
  max-width: 70px;  // 减小最大宽度
  cursor: pointer;
}

.input-image-preview {
  width: 100%;
  height: 70px;  // 减小固定高度
  border-radius: 6px;
  object-fit: cover;
  border: 1px solid #e0e0e0;
  display: block;
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.05);
    border-color: #07C160;
  }
}

.remove-image {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 16px;
  height: 16px;
  background: #ff4757;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  cursor: pointer;
  font-weight: bold;
  line-height: 1;
  border: 1px solid white;
  box-shadow: 0 1px 3px rgba(0,0,0,0.3);
  z-index: 10;

  &:hover {
    background: #ff3742;
    transform: scale(1.1);
  }
}



// 滑入动画 - 从上往下滑入
@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.reply-content-text {
  flex: 1;                         // 占据剩余空间
  white-space: nowrap;             // 单行显示
  overflow: hidden;                // 超出隐藏
  text-overflow: ellipsis;         // 超出显示省略号
  margin-right: 8px;               // 与取消按钮的间距
  max-width: calc(100% - 60px);    // 为取消按钮预留空间，让文本区域更大
}

.cancel-reply {
  color: #07C160;
  cursor: pointer;
  flex-shrink: 0;                  // 不被压缩
  font-weight: 500;
  font-size: 12px;                 // 减小字体
  padding: 4px 8px;                // 添加内边距
  border-radius: 8px;              // 添加圆角
  transition: background-color 0.2s ease; // 添加过渡效果

  &:hover {
    background: rgba(7, 193, 96, 0.1); // 悬停时的淡绿色背景
  }
}

.reply-input-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;         // 垂直居中
  min-width: 0;                    // 确保能正确收缩
}

.reply-textarea {
  width: 100% !important;
  min-height: 36px !important;    // 减小最小高度
  max-height: 100px !important;   // 减小最大高度
  padding: 10px 12px !important;  // 调整内边距
  border-radius: 18px !important; // 增加圆角，更现代
  border: 1px solid #e0e0e0;      // 更淡的边框
  background: #f8f9fa;            // 淡灰色背景
  resize: none !important;        // 禁止手动调整大小
  font-size: 14px;                // 减小字体
  box-sizing: border-box !important;
  overflow-y: auto;               // 超过最大高度时显示滚动条
  line-height: 18px !important;   // 调整行高
  vertical-align: top !important; // 防止基线对齐导致的高度变化
  word-wrap: break-word !important; // 强制换行
  white-space: pre-wrap !important; // 保持空格和换行，但允许自动换行
  transition: border-color 0.2s ease; // 添加过渡效果

  &:focus {
    border-color: #07C160;          // 聚焦时的边框颜色
    background: #fff;               // 聚焦时的背景色
    outline: none;
  }
}

.upload-button {
  width: 28px;
  height: 28px;
  border-radius: 6px;               // 增加圆角
  background: #f0f0f0;              // 更淡的背景
  object-fit: cover;
  flex-shrink: 0;
  flex-grow: 0;
  transition: background-color 0.2s ease; // 添加过渡效果

  &:hover {
    background: #e0e0e0;            // 悬停效果
  }
}

.sub-reply{
  background: #07C160;
  color: #fff;
  border: none;
  border-radius: 18px;              // 增加圆角
  font-size: 14px;                  // 减小字体
  cursor: pointer;
  height: 36px;                     // 减小高度
  padding: 0 16px;                  // 添加水平内边距
  flex-shrink: 0;                   // 防止按钮被压缩
  flex-grow: 0;
  transition: background-color 0.2s ease; // 添加过渡效果

  &:hover {
    background: #06a84f;            // 悬停时的深绿色
  }

  &:active {
    background: #059142;            // 点击时的更深绿色
  }

  &.disabled {
    background: #cccccc;            // 禁用状态的灰色背景
    color: #999999;                 // 禁用状态的灰色文字
    cursor: not-allowed;            // 禁用状态的鼠标样式

    &:hover {
      background: #cccccc;          // 禁用状态下悬停不变色
    }

    &:active {
      background: #cccccc;          // 禁用状态下点击不变色
    }
  }
}



</style>