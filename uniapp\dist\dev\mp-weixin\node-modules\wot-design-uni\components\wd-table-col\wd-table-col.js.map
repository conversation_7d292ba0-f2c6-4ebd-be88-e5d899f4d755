{"version": 3, "file": "wd-table-col.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-table-col/wd-table-col.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC10YWJsZS1jb2wvd2QtdGFibGUtY29sLnZ1ZQ"], "sourcesContent": ["<template>\n  <view\n    :class=\"`wd-table-col ${fixed ? 'wd-table-col--fixed' : ''} ${isLastFixed && isDef(table) && table.state.scrollLeft ? 'is-shadow' : ''}`\"\n    :style=\"columnStyle\"\n  >\n    <view\n      :class=\"`wd-table__cell ${stripe && isOdd(index) ? 'is-stripe' : ''} ${border ? 'is-border' : ''} is-${align}`\"\n      v-for=\"(row, index) in column\"\n      :key=\"index\"\n      :style=\"cellStyle\"\n      @click=\"handleRowClick(index)\"\n    >\n      <slot name=\"value\" v-if=\"$slots.value\" :row=\"getScope(index)\" :index=\"index\"></slot>\n      <text :class=\"`wd-table__value ${ellipsis ? 'is-ellipsis' : ''}`\" v-else>{{ row }}</text>\n    </view>\n  </view>\n</template>\n\n<script lang=\"ts\">\nexport default {\n  name: 'wd-table-col',\n  options: {\n    addGlobalClass: true,\n    virtualHost: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n<script lang=\"ts\" setup>\nimport { type CSSProperties, computed, ref } from 'vue'\nimport { addUnit, isDef, objToStyle, isOdd, isFunction } from '../common/util'\nimport { tableColumnProps, type SortDirection } from './types'\nimport { useParent } from '../composables/useParent'\nimport { TABLE_KEY } from '../wd-table/types'\n\nconst props = defineProps(tableColumnProps)\n\nconst { parent: table, index: columnIndex } = useParent(TABLE_KEY)\n\nconst sortDirection = ref<SortDirection>(0) // 排序方向\n\n// 是否开启斑马纹\nconst stripe = computed(() => {\n  if (isDef(table)) {\n    return table.props.stripe\n  } else {\n    return false\n  }\n})\n\n/**\n * 是否有边框\n */\nconst border = computed(() => {\n  if (isDef(table)) {\n    return table.props.border\n  } else {\n    return false\n  }\n})\n\n/**\n * 是否超出省略\n */\nconst ellipsis = computed(() => {\n  if (isDef(table)) {\n    return table.props.ellipsis\n  } else {\n    return false\n  }\n})\n\n/**\n * 是否最后一个固定元素\n */\nconst isLastFixed = computed(() => {\n  let isLastFixed: boolean = false\n  if (props.fixed && isDef(table)) {\n    isLastFixed = table.getIsLastFixed(props)\n  }\n  return isLastFixed\n})\n\n/**\n * 列样式\n */\nconst columnStyle = computed(() => {\n  let style: CSSProperties = {}\n  if (isDef(props.width)) {\n    style['width'] = addUnit(props.width)\n  }\n  if (props.fixed && isDef(table) && isFunction(table.getFixedStyle)) {\n    style = table.getFixedStyle(columnIndex.value, style)\n  }\n  return style\n})\n\n/**\n * 单元格样式\n */\nconst cellStyle = computed(() => {\n  let style: CSSProperties = {}\n  const rowHeight: string | number = isDef(table) && isDef(table.props) ? table.props.rowHeight : 50 // 自定义行高\n  if (isDef(rowHeight)) {\n    style['height'] = addUnit(rowHeight)\n  }\n  if (props.fixed && isDef(table) && isFunction(table.getFixedStyle)) {\n    style = table.getFixedStyle(columnIndex.value, style)\n  }\n  return objToStyle(style)\n})\n\n// 列数据\nconst column = computed(() => {\n  if (!isDef(table) || !isDef(table.props) || !isDef(table.props.data) || !Array.isArray(table.props.data)) {\n    return []\n  }\n\n  const column: any[] = table.props.data.map((item) => {\n    return item[props.prop]\n  })\n  return column\n})\n\n/**\n * 行点击事件\n * @param index 行下标\n */\nfunction handleRowClick(index: number) {\n  if (!isDef(table)) {\n    return\n  }\n  isFunction(table.rowClick) && table.rowClick(index)\n}\n\n// 行数据\nfunction getScope(index: number) {\n  if (!isDef(table) || !isDef(table.props) || !isDef(table.props.data) || !Array.isArray(table.props.data)) {\n    return {}\n  }\n  return table.props.data[index] || {}\n}\n\ndefineExpose({ sortDirection: sortDirection })\n</script>\n\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-table-col/wd-table-col.vue'\nwx.createComponent(Component)"], "names": ["useParent", "TABLE_KEY", "ref", "computed", "isDef", "isLastFixed", "addUnit", "isFunction", "objToStyle", "column"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAmBA,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,gBAAgB;AAAA,EAAA;AAEpB;;;;AASA,UAAM,QAAQ;AAEd,UAAM,EAAE,QAAQ,OAAO,OAAO,YAAY,IAAIA,wBAAUC,uBAAS;AAE3D,UAAA,gBAAgBC,kBAAmB,CAAC;AAGpC,UAAA,SAASC,cAAAA,SAAS,MAAM;AACxB,UAAAC,cAAAA,MAAM,KAAK,GAAG;AAChB,eAAO,MAAM,MAAM;AAAA,MAAA,OACd;AACE,eAAA;AAAA,MAAA;AAAA,IACT,CACD;AAKK,UAAA,SAASD,cAAAA,SAAS,MAAM;AACxB,UAAAC,cAAAA,MAAM,KAAK,GAAG;AAChB,eAAO,MAAM,MAAM;AAAA,MAAA,OACd;AACE,eAAA;AAAA,MAAA;AAAA,IACT,CACD;AAKK,UAAA,WAAWD,cAAAA,SAAS,MAAM;AAC1B,UAAAC,cAAAA,MAAM,KAAK,GAAG;AAChB,eAAO,MAAM,MAAM;AAAA,MAAA,OACd;AACE,eAAA;AAAA,MAAA;AAAA,IACT,CACD;AAKK,UAAA,cAAcD,cAAAA,SAAS,MAAM;AACjC,UAAIE,eAAuB;AAC3B,UAAI,MAAM,SAASD,cAAM,MAAA,KAAK,GAAG;AAC/BC,uBAAc,MAAM,eAAe,KAAK;AAAA,MAAA;AAEnCA,aAAAA;AAAAA,IAAA,CACR;AAKK,UAAA,cAAcF,cAAAA,SAAS,MAAM;AACjC,UAAI,QAAuB,CAAC;AACxB,UAAAC,cAAA,MAAM,MAAM,KAAK,GAAG;AACtB,cAAM,OAAO,IAAIE,sBAAQ,MAAM,KAAK;AAAA,MAAA;AAElC,UAAA,MAAM,SAASF,oBAAM,KAAK,KAAKG,yBAAW,MAAM,aAAa,GAAG;AAClE,gBAAQ,MAAM,cAAc,YAAY,OAAO,KAAK;AAAA,MAAA;AAE/C,aAAA;AAAA,IAAA,CACR;AAKK,UAAA,YAAYJ,cAAAA,SAAS,MAAM;AAC/B,UAAI,QAAuB,CAAC;AACtB,YAAA,YAA6BC,cAAAA,MAAM,KAAK,KAAKA,cAAA,MAAM,MAAM,KAAK,IAAI,MAAM,MAAM,YAAY;AAC5F,UAAAA,cAAAA,MAAM,SAAS,GAAG;AACd,cAAA,QAAQ,IAAIE,cAAA,QAAQ,SAAS;AAAA,MAAA;AAEjC,UAAA,MAAM,SAASF,oBAAM,KAAK,KAAKG,yBAAW,MAAM,aAAa,GAAG;AAClE,gBAAQ,MAAM,cAAc,YAAY,OAAO,KAAK;AAAA,MAAA;AAEtD,aAAOC,cAAAA,WAAW,KAAK;AAAA,IAAA,CACxB;AAGK,UAAA,SAASL,cAAAA,SAAS,MAAM;AACxB,UAAA,CAACC,cAAAA,MAAM,KAAK,KAAK,CAACA,cAAM,MAAA,MAAM,KAAK,KAAK,CAACA,oBAAM,MAAM,MAAM,IAAI,KAAK,CAAC,MAAM,QAAQ,MAAM,MAAM,IAAI,GAAG;AACxG,eAAO,CAAC;AAAA,MAAA;AAGV,YAAMK,UAAgB,MAAM,MAAM,KAAK,IAAI,CAAC,SAAS;AAC5C,eAAA,KAAK,MAAM,IAAI;AAAA,MAAA,CACvB;AACMA,aAAAA;AAAAA,IAAA,CACR;AAMD,aAAS,eAAe,OAAe;AACjC,UAAA,CAACL,cAAAA,MAAM,KAAK,GAAG;AACjB;AAAA,MAAA;AAEFG,oBAAA,WAAW,MAAM,QAAQ,KAAK,MAAM,SAAS,KAAK;AAAA,IAAA;AAIpD,aAAS,SAAS,OAAe;AAC3B,UAAA,CAACH,cAAAA,MAAM,KAAK,KAAK,CAACA,cAAM,MAAA,MAAM,KAAK,KAAK,CAACA,oBAAM,MAAM,MAAM,IAAI,KAAK,CAAC,MAAM,QAAQ,MAAM,MAAM,IAAI,GAAG;AACxG,eAAO,CAAC;AAAA,MAAA;AAEV,aAAO,MAAM,MAAM,KAAK,KAAK,KAAK,CAAC;AAAA,IAAA;AAGxB,aAAA,EAAE,eAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9I7C,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}