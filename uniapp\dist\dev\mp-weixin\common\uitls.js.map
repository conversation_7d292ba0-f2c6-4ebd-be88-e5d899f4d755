{"version": 3, "file": "uitls.js", "sources": ["../../../../src/common/uitls.ts"], "sourcesContent": ["import pagesJson from '../pages.json'\r\n// 引入uni-parse-pages\r\nimport pagesJsonToRoutes from 'uni-parse-pages'\r\nimport { colorPanel } from './constants'\r\n\r\n/**\r\n * 缓存,默认有效期2小时\r\n * @param key 缓存key\r\n * @param value  缓存值\r\n * @param seconds 缓存时间（秒）\r\n * @returns {*}\r\n */\r\nexport function cache(key, value = null, seconds = 2 * 3600) {\r\n  var timestamp = +new Date() / 1000\r\n  if (key && value === null) {\r\n    //获取缓存\r\n    var val = uni.getStorageSync(key)\r\n    if (val && val.length > 0) {\r\n      var tmp = val.split('|')\r\n      if (!tmp[2] || timestamp >= tmp[2]) {\r\n        console.log('key已失效')\r\n        //删除缓存\r\n        uni.removeStorageSync(key)\r\n        return ''\r\n      } else {\r\n        console.log('key未失效')\r\n        if (tmp[1] == 'json') {\r\n          return JSON.parse(tmp[0])\r\n        }\r\n        return tmp[0]\r\n      }\r\n    }\r\n  } else if (key && value) {\r\n    //设置缓存\r\n    var expire = timestamp + seconds\r\n    console.log('typeof value', typeof value)\r\n    if (typeof value == 'object') {\r\n      value = JSON.stringify(value) + '|json|' + expire\r\n    } else {\r\n      value = value + '|string|' + expire\r\n    }\r\n    uni.setStorageSync(key, value)\r\n  } else {\r\n    console.log('key不能空')\r\n  }\r\n}\r\n\r\n// 获取静态文件地址\r\nexport const getStaticDomainURL = () => {\r\n  return import.meta.env.VITE_SERVER_BASEURL + '/sys/common/static'\r\n}\r\n\r\nexport const getFileAccessHttpUrl = function (avatar, subStr?) {\r\n  if (!avatar) return ''\r\n  if (!subStr) subStr = 'http'\r\n  if (avatar) {\r\n    avatar = avatar.replace(/user_imgs\\\\/, 'user_imgs/')\r\n  }\r\n  if (avatar && avatar.startsWith(subStr)) {\r\n    return avatar\r\n  } else {\r\n    return getStaticDomainURL() + '/' + avatar\r\n  }\r\n}\r\ninterface hasRouteType {\r\n  name?: string\r\n  path?: string\r\n  routeList?: any\r\n}\r\n// 判断路由是否存在\r\nexport const hasRoute = ({ name, path, routeList }: hasRouteType) => {\r\n  routeList = routeList ?? pagesJsonToRoutes(pagesJson)\r\n  if (path) {\r\n    return !!routeList.find((item) => item.path === path)\r\n  }\r\n  if (name) {\r\n    return !!routeList.find((item) => item.path.split('/').pop() === name)\r\n  }\r\n}\r\n\r\n/**\r\n * 人性化显示时间\r\n *\r\n * @param {Object} datetime\r\n */\r\nexport function beautifyTime(datetime = '') {\r\n  if (datetime == null) {\r\n    return ''\r\n  }\r\n  datetime = datetime.toString().replace(/-/g, '/')\r\n  let time = new Date()\r\n  let outTime = new Date(datetime)\r\n  if (/^[1-9]\\d*$/.test(datetime)) {\r\n    outTime = new Date(parseInt(datetime))\r\n  }\r\n\r\n  if (time.getTime() < outTime.getTime()) {\r\n    return parseTime(outTime, '{y}/{m}/{d}')\r\n  }\r\n\r\n  if (time.getFullYear() != outTime.getFullYear()) {\r\n    return parseTime(outTime, '{y}/{m}/{d}')\r\n  }\r\n\r\n  if (time.getMonth() != outTime.getMonth()) {\r\n    return parseTime(outTime, '{m}/{d}')\r\n  }\r\n\r\n  if (time.getDate() != outTime.getDate()) {\r\n    let day = outTime.getDate() - time.getDate()\r\n    if (day == -1) {\r\n      return parseTime(outTime, '昨天 {h}:{i}')\r\n    }\r\n\r\n    if (day == -2) {\r\n      return parseTime(outTime, '前天 {h}:{i}')\r\n    }\r\n\r\n    return parseTime(outTime, '{m}-{d}')\r\n  }\r\n\r\n  if (time.getHours() != outTime.getHours()) {\r\n    return parseTime(outTime, '{h}:{i}')\r\n  }\r\n\r\n  let minutes = outTime.getMinutes() - time.getMinutes()\r\n  if (minutes == 0) {\r\n    return '刚刚'\r\n  }\r\n\r\n  minutes = Math.abs(minutes)\r\n  return `${minutes}分钟前`\r\n}\r\n/**\r\n * 格式化时间\r\n * @param {Object} time\r\n * @param {Object} cFormat\r\n */\r\nexport function parseTime(time, cFormat) {\r\n  if (arguments.length === 0) {\r\n    return null\r\n  }\r\n\r\n  let date\r\n  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'\r\n\r\n  if (typeof time === 'object') {\r\n    date = time\r\n  } else {\r\n    if (typeof time === 'string' && /^[0-9]+$/.test(time)) {\r\n      time = parseInt(time)\r\n    } else {\r\n      time = new Date(time)\r\n    }\r\n    date = new Date(time.toString().replace(/-/g, '/'))\r\n  }\r\n\r\n  const formatObj = {\r\n    y: date.getFullYear(),\r\n    m: date.getMonth() + 1,\r\n    d: date.getDate(),\r\n    h: date.getHours(),\r\n    i: date.getMinutes(),\r\n    s: date.getSeconds(),\r\n    a: date.getDay(),\r\n  }\r\n\r\n  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {\r\n    const value = formatObj[key]\r\n    // Note: getDay() returns 0 on Sunday\r\n    if (key === 'a') {\r\n      return ['日', '一', '二', '三', '四', '五', '六'][value]\r\n    }\r\n\r\n    return value.toString().padStart(2, '0')\r\n  })\r\n\r\n  return time_str\r\n}\r\n\r\n/**\r\n * 随机生成字符串\r\n * @param length 字符串的长度\r\n * @param chats 可选字符串区间（只会生成传入的字符串中的字符）\r\n * @return string 生成的字符串\r\n */\r\nexport function randomString(length, chats) {\r\n  if (!length) length = 1\r\n  if (!chats) chats = '0123456789qwertyuioplkjhgfdsazxcvbnm'\r\n  let str = ''\r\n  for (let i = 0; i < length; i++) {\r\n    //@ts-ignore\r\n    let num = randomNumber(0, chats.length - 1)\r\n    str += chats[num]\r\n  }\r\n  return str\r\n}\r\n\r\n/**\r\n * 随机生成数字\r\n *\r\n * 示例：生成长度为 12 的随机数：randomNumber(12)\r\n * 示例：生成 3~23 之间的随机数：randomNumber(3, 23)\r\n *\r\n * @param1 最小值 | 长度\r\n * @param2 最大值\r\n * @return int 生成后的数字\r\n */\r\nexport function randomNumber() {\r\n  // 生成 最小值 到 最大值 区间的随机数\r\n  const random = (min, max) => {\r\n    return Math.floor(Math.random() * (max - min + 1) + min)\r\n  }\r\n  if (arguments.length === 1) {\r\n    //@ts-ignore\r\n    let [length] = arguments\r\n    // 生成指定长度的随机数字，首位一定不是 0\r\n    //@ts-ignore\r\n    let nums = [...Array(length).keys()].map((i) => (i > 0 ? random(0, 9) : random(1, 9)))\r\n    return parseInt(nums.join(''))\r\n  } else if (arguments.length >= 2) {\r\n    //@ts-ignore\r\n    let [min, max] = arguments\r\n    return random(min, max)\r\n  } else {\r\n    return Number.NaN\r\n  }\r\n}\r\n\r\n/**\r\n * 时间格式化\r\n * @param value\r\n * @param fmt\r\n * @returns {*}\r\n */\r\nexport function formatDate(value, fmt) {\r\n  var regPos = /^\\d+(\\.\\d+)?$/\r\n  if (regPos.test(value)) {\r\n    //如果是数字\r\n    let getDate = new Date(value)\r\n    let o = {\r\n      'M+': getDate.getMonth() + 1,\r\n      'd+': getDate.getDate(),\r\n      'h+': getDate.getHours(),\r\n      'H+': getDate.getHours(),\r\n      'm+': getDate.getMinutes(),\r\n      's+': getDate.getSeconds(),\r\n      'q+': Math.floor((getDate.getMonth() + 3) / 3),\r\n      S: getDate.getMilliseconds(),\r\n    }\r\n    if (/(y+)/.test(fmt)) {\r\n      fmt = fmt.replace(RegExp.$1, (getDate.getFullYear() + '').substr(4 - RegExp.$1.length))\r\n    }\r\n    for (let k in o) {\r\n      if (new RegExp('(' + k + ')').test(fmt)) {\r\n        fmt = fmt.replace(\r\n          RegExp.$1,\r\n          RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length),\r\n        )\r\n      }\r\n    }\r\n    return fmt\r\n  } else {\r\n    //TODO\r\n    if (value && value.length > 0) {\r\n      value = value.trim()\r\n      return value.substr(0, fmt.length)\r\n    }\r\n    return value\r\n  }\r\n}\r\n\r\n// 通过时间或者时间戳获取对应antd的年、月、周、季度。\r\nexport function getWeekMonthQuarterYear(date) {\r\n  // 获取 ISO 周数的函数\r\n  const getISOWeek = (date) => {\r\n    const jan4 = new Date(date.getFullYear(), 0, 4)\r\n    const oneDay = 86400000 // 一天的毫秒数\r\n    return Math.ceil(((date - jan4.getTime()) / oneDay + jan4.getDay() + 1) / 7)\r\n  }\r\n  // 将时间戳转换为日期对象\r\n  const dateObj = new Date(date)\r\n  // 计算周\r\n  const week = getISOWeek(dateObj)\r\n  // 计算月\r\n  const month = dateObj.getMonth() + 1 // 月份是从0开始的，所以要加1\r\n  // 计算季度\r\n  const quarter = Math.floor(dateObj.getMonth() / 3) + 1\r\n  // 计算年\r\n  const year = dateObj.getFullYear()\r\n  return {\r\n    year: `${year}`,\r\n    month: `${year}-${month.toString().padStart(2, '0')}`,\r\n    week: `${year}-${week}周`,\r\n    quarter: `${year}-Q${quarter}`,\r\n  }\r\n}\r\n\r\n// 生成 1 到 10 之间的随机整数\r\nexport function getRandomIntBetweenOneAndTen() {\r\n    return Math.floor(Math.random() * 10) + 1;\r\n}\r\n/**\r\n * 获取随机颜色\r\n * @param {any} color\r\n * 颜色板\r\n * classic：经典\r\n * technology：科技\r\n * business：商务\r\n * botany：植物\r\n * natural：自然\r\n * colour：彩色\r\n * @return\r\n */\r\nexport function getRandomColor() {\r\n\tlet colorType = ['classic','technology','business','botany','natural','colour'];\r\n\t// 生成一个随机索引，范围是从 0 到数组长度减 1\r\n\tlet randomIndex = Math.floor(Math.random() * colorType.length);\r\n\t// 根据随机索引从数组中获取一个随机类型\r\n\tlet randomColorType = colorType[randomIndex];\r\n\treturn colorPanel['natural'][getRandomIntBetweenOneAndTen()] || '#00bcd4';\r\n}\r\n\r\n// 消除后缀：\r\nexport const getPlaceholder = (attrs: any = {}) => {\r\n  let label = attrs.label\r\n  if (label.endsWith('：') || label.endsWith(':')) {\r\n    label = label.substr(0, label.length - 1)\r\n  }\r\n  return `请选择${label}`\r\n}"], "names": ["uni", "pagesJsonToRoutes", "pagesJson", "date", "colorPanel"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYO,SAAS,MAAM,KAAK,QAAQ,MAAM,UAAU,IAAI,MAAM;AAC3D,MAAI,YAAY,CAAK,oBAAA,KAAA,IAAS;AAC1B,MAAA,OAAO,UAAU,MAAM;AAErB,QAAA,MAAMA,cAAAA,MAAI,eAAe,GAAG;AAC5B,QAAA,OAAO,IAAI,SAAS,GAAG;AACrB,UAAA,MAAM,IAAI,MAAM,GAAG;AACvB,UAAI,CAAC,IAAI,CAAC,KAAK,aAAa,IAAI,CAAC,GAAG;AAClC,gBAAQ,IAAI,QAAQ;AAEpBA,sBAAA,MAAI,kBAAkB,GAAG;AAClB,eAAA;AAAA,MAAA,OACF;AACL,gBAAQ,IAAI,QAAQ;AAChB,YAAA,IAAI,CAAC,KAAK,QAAQ;AACpB,iBAAO,KAAK,MAAM,IAAI,CAAC,CAAC;AAAA,QAAA;AAE1B,eAAO,IAAI,CAAC;AAAA,MAAA;AAAA,IACd;AAAA,EACF,WACS,OAAO,OAAO;AAEvB,QAAI,SAAS,YAAY;AACjB,YAAA,IAAI,gBAAgB,OAAO,KAAK;AACpC,QAAA,OAAO,SAAS,UAAU;AAC5B,cAAQ,KAAK,UAAU,KAAK,IAAI,WAAW;AAAA,IAAA,OACtC;AACL,cAAQ,QAAQ,aAAa;AAAA,IAAA;AAE3BA,wBAAA,eAAe,KAAK,KAAK;AAAA,EAAA,OACxB;AACL,YAAQ,IAAI,QAAQ;AAAA,EAAA;AAExB;AAGO,MAAM,qBAAqB,MAAM;AAC/B,SAAA;AACT;AAEa,MAAA,uBAAuB,SAAU,QAAQ,QAAS;AAC7D,MAAI,CAAC;AAAe,WAAA;AACpB,MAAI,CAAC;AAAiB,aAAA;AACtB,MAAI,QAAQ;AACD,aAAA,OAAO,QAAQ,eAAe,YAAY;AAAA,EAAA;AAErD,MAAI,UAAU,OAAO,WAAW,MAAM,GAAG;AAChC,WAAA;AAAA,EAAA,OACF;AACE,WAAA,mBAAA,IAAuB,MAAM;AAAA,EAAA;AAExC;AAOO,MAAM,WAAW,CAAC,EAAE,MAAM,MAAM,gBAA8B;AACvD,cAAA,gCAAaC,gBAAkBC,CAAS;AACpD,MAAI,MAAM;AACD,WAAA,CAAC,CAAC,UAAU,KAAK,CAAC,SAAS,KAAK,SAAS,IAAI;AAAA,EAAA;AAEtD,MAAI,MAAM;AACR,WAAO,CAAC,CAAC,UAAU,KAAK,CAAC,SAAS,KAAK,KAAK,MAAM,GAAG,EAAE,IAAA,MAAU,IAAI;AAAA,EAAA;AAEzE;AAOgB,SAAA,aAAa,WAAW,IAAI;AAC1C,MAAI,YAAY,MAAM;AACb,WAAA;AAAA,EAAA;AAET,aAAW,SAAS,SAAA,EAAW,QAAQ,MAAM,GAAG;AAC5C,MAAA,2BAAW,KAAK;AAChB,MAAA,UAAU,IAAI,KAAK,QAAQ;AAC3B,MAAA,aAAa,KAAK,QAAQ,GAAG;AAC/B,cAAU,IAAI,KAAK,SAAS,QAAQ,CAAC;AAAA,EAAA;AAGvC,MAAI,KAAK,QAAA,IAAY,QAAQ,WAAW;AAC/B,WAAA,UAAU,SAAS,aAAa;AAAA,EAAA;AAGzC,MAAI,KAAK,YAAA,KAAiB,QAAQ,eAAe;AACxC,WAAA,UAAU,SAAS,aAAa;AAAA,EAAA;AAGzC,MAAI,KAAK,SAAA,KAAc,QAAQ,YAAY;AAClC,WAAA,UAAU,SAAS,SAAS;AAAA,EAAA;AAGrC,MAAI,KAAK,QAAA,KAAa,QAAQ,WAAW;AACvC,QAAI,MAAM,QAAQ,QAAQ,IAAI,KAAK,QAAQ;AAC3C,QAAI,OAAO,IAAI;AACN,aAAA,UAAU,SAAS,YAAY;AAAA,IAAA;AAGxC,QAAI,OAAO,IAAI;AACN,aAAA,UAAU,SAAS,YAAY;AAAA,IAAA;AAGjC,WAAA,UAAU,SAAS,SAAS;AAAA,EAAA;AAGrC,MAAI,KAAK,SAAA,KAAc,QAAQ,YAAY;AAClC,WAAA,UAAU,SAAS,SAAS;AAAA,EAAA;AAGrC,MAAI,UAAU,QAAQ,WAAW,IAAI,KAAK,WAAW;AACrD,MAAI,WAAW,GAAG;AACT,WAAA;AAAA,EAAA;AAGC,YAAA,KAAK,IAAI,OAAO;AAC1B,SAAO,GAAG,OAAO;AACnB;AAMgB,SAAA,UAAU,MAAM,SAAS;AACnC,MAAA,UAAU,WAAW,GAAG;AACnB,WAAA;AAAA,EAAA;AAGL,MAAA;AACJ,QAAM,SAAS,WAAW;AAEtB,MAAA,OAAO,SAAS,UAAU;AACrB,WAAA;AAAA,EAAA,OACF;AACL,QAAI,OAAO,SAAS,YAAY,WAAW,KAAK,IAAI,GAAG;AACrD,aAAO,SAAS,IAAI;AAAA,IAAA,OACf;AACE,aAAA,IAAI,KAAK,IAAI;AAAA,IAAA;AAEf,WAAA,IAAI,KAAK,KAAK,WAAW,QAAQ,MAAM,GAAG,CAAC;AAAA,EAAA;AAGpD,QAAM,YAAY;AAAA,IAChB,GAAG,KAAK,YAAY;AAAA,IACpB,GAAG,KAAK,SAAA,IAAa;AAAA,IACrB,GAAG,KAAK,QAAQ;AAAA,IAChB,GAAG,KAAK,SAAS;AAAA,IACjB,GAAG,KAAK,WAAW;AAAA,IACnB,GAAG,KAAK,WAAW;AAAA,IACnB,GAAG,KAAK,OAAO;AAAA,EACjB;AAEA,QAAM,WAAW,OAAO,QAAQ,mBAAmB,CAAC,QAAQ,QAAQ;AAC5D,UAAA,QAAQ,UAAU,GAAG;AAE3B,QAAI,QAAQ,KAAK;AACR,aAAA,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,EAAE,KAAK;AAAA,IAAA;AAGlD,WAAO,MAAM,SAAA,EAAW,SAAS,GAAG,GAAG;AAAA,EAAA,CACxC;AAEM,SAAA;AACT;AAQgB,SAAA,aAAa,QAAQ,OAAO;AAE1C,MAAI,CAAC;AAAe,YAAA;AACpB,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAE/B,QAAI,MAAM,aAAa,GAAG,MAAM,SAAS,CAAC;AAC1C,WAAO,MAAM,GAAG;AAAA,EAAA;AAEX,SAAA;AACT;AAYO,SAAS,eAAe;AAEvB,QAAA,SAAS,CAAC,KAAK,QAAQ;AACpB,WAAA,KAAK,MAAM,KAAK,OAAA,KAAY,MAAM,MAAM,KAAK,GAAG;AAAA,EACzD;AACI,MAAA,UAAU,WAAW,GAAG;AAEtB,QAAA,CAAC,MAAM,IAAI;AAGX,QAAA,OAAO,CAAC,GAAG,MAAM,MAAM,EAAE,KAAM,CAAA,EAAE,IAAI,CAAC,MAAO,IAAI,IAAI,OAAO,GAAG,CAAC,IAAI,OAAO,GAAG,CAAC,CAAE;AACrF,WAAO,SAAS,KAAK,KAAK,EAAE,CAAC;AAAA,EAAA,WACpB,UAAU,UAAU,GAAG;AAE5B,QAAA,CAAC,KAAK,GAAG,IAAI;AACV,WAAA,OAAO,KAAK,GAAG;AAAA,EAAA,OACjB;AACL,WAAO,OAAO;AAAA,EAAA;AAElB;AAQgB,SAAA,WAAW,OAAO,KAAK;AACrC,MAAI,SAAS;AACT,MAAA,OAAO,KAAK,KAAK,GAAG;AAElB,QAAA,UAAU,IAAI,KAAK,KAAK;AAC5B,QAAI,IAAI;AAAA,MACN,MAAM,QAAQ,SAAA,IAAa;AAAA,MAC3B,MAAM,QAAQ,QAAQ;AAAA,MACtB,MAAM,QAAQ,SAAS;AAAA,MACvB,MAAM,QAAQ,SAAS;AAAA,MACvB,MAAM,QAAQ,WAAW;AAAA,MACzB,MAAM,QAAQ,WAAW;AAAA,MACzB,MAAM,KAAK,OAAO,QAAQ,SAAS,IAAI,KAAK,CAAC;AAAA,MAC7C,GAAG,QAAQ,gBAAgB;AAAA,IAC7B;AACI,QAAA,OAAO,KAAK,GAAG,GAAG;AACpB,YAAM,IAAI,QAAQ,OAAO,KAAK,QAAQ,YAAgB,IAAA,IAAI,OAAO,IAAI,OAAO,GAAG,MAAM,CAAC;AAAA,IAAA;AAExF,aAAS,KAAK,GAAG;AACX,UAAA,IAAI,OAAO,MAAM,IAAI,GAAG,EAAE,KAAK,GAAG,GAAG;AACvC,cAAM,IAAI;AAAA,UACR,OAAO;AAAA,UACP,OAAO,GAAG,WAAW,IAAI,EAAE,CAAC,KAAK,OAAO,EAAE,CAAC,GAAG,QAAQ,KAAK,EAAE,CAAC,GAAG,MAAM;AAAA,QACzE;AAAA,MAAA;AAAA,IACF;AAEK,WAAA;AAAA,EAAA,OACF;AAED,QAAA,SAAS,MAAM,SAAS,GAAG;AAC7B,cAAQ,MAAM,KAAK;AACnB,aAAO,MAAM,OAAO,GAAG,IAAI,MAAM;AAAA,IAAA;AAE5B,WAAA;AAAA,EAAA;AAEX;AAGO,SAAS,wBAAwB,MAAM;AAEtC,QAAA,aAAa,CAACC,UAAS;AAC3B,UAAM,OAAO,IAAI,KAAKA,MAAK,YAAY,GAAG,GAAG,CAAC;AAC9C,UAAM,SAAS;AACR,WAAA,KAAK,OAAOA,QAAO,KAAK,aAAa,SAAS,KAAK,WAAW,KAAK,CAAC;AAAA,EAC7E;AAEM,QAAA,UAAU,IAAI,KAAK,IAAI;AAEvB,QAAA,OAAO,WAAW,OAAO;AAEzB,QAAA,QAAQ,QAAQ,SAAA,IAAa;AAEnC,QAAM,UAAU,KAAK,MAAM,QAAQ,SAAS,IAAI,CAAC,IAAI;AAE/C,QAAA,OAAO,QAAQ,YAAY;AAC1B,SAAA;AAAA,IACL,MAAM,GAAG,IAAI;AAAA,IACb,OAAO,GAAG,IAAI,IAAI,MAAM,SAAW,EAAA,SAAS,GAAG,GAAG,CAAC;AAAA,IACnD,MAAM,GAAG,IAAI,IAAI,IAAI;AAAA,IACrB,SAAS,GAAG,IAAI,KAAK,OAAO;AAAA,EAC9B;AACF;AAGO,SAAS,+BAA+B;AAC3C,SAAO,KAAK,MAAM,KAAK,OAAO,IAAI,EAAE,IAAI;AAC5C;AAaO,SAAS,iBAAiB;AAMhC,SAAOC,iBAAW,WAAA,SAAS,EAAE,6BAAA,CAA8B,KAAK;AACjE;AAGO,MAAM,iBAAiB,CAAC,QAAa,OAAO;AACjD,MAAI,QAAQ,MAAM;AAClB,MAAI,MAAM,SAAS,GAAG,KAAK,MAAM,SAAS,GAAG,GAAG;AAC9C,YAAQ,MAAM,OAAO,GAAG,MAAM,SAAS,CAAC;AAAA,EAAA;AAE1C,SAAO,MAAM,KAAK;AACpB;;;;;;;;;;;;;"}