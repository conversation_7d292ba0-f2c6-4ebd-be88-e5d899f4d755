{"version": 3, "file": "app.js", "sources": ["../../../src/App.vue", "../../../src/main.ts"], "sourcesContent": ["<script lang=\"ts\">\r\nimport { onLaunch, onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'\r\nimport 'abortcontroller-polyfill/dist/abortcontroller-polyfill-only'\r\nimport { beforEach } from '@/router/index'\r\nexport default {\r\n  onLaunch: function (options) {\r\n    console.log('App Launch')\r\n    console.log('应用启动路径：', options.path)\r\n  },\r\n  onShow: function (options) {\r\n    console.log('App Show')\r\n    console.log('应用启动路径：', options.path)\r\n    // 首次进入页面时路由拦截\r\n    setTimeout(() => {\r\n      const currentPage = options.path\r\n      beforEach({ path: '/' }, { path: currentPage, fullPath: currentPage }, (data) => {\r\n        if (data?.path) {\r\n          uni.redirectTo({ url: data.path })\r\n        }\r\n      })\r\n    }, 100)\r\n  },\r\n  onHide: function () {\r\n    console.log('App Hide')\r\n  },\r\n  // 全局变量\r\n  globalData: {\r\n    isLocalConfig: true,\r\n    systemInfo: uni.getSystemInfoSync(),\r\n    navHeight: 44,\r\n  },\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\nbody {\r\n  font-size: 14px;\r\n  color: #333333;\r\n  font-family:\r\n    Helvetica Neue,\r\n    Helvetica,\r\n    sans-serif;\r\n}\r\nuni-page-body {\r\n  height: 100%;\r\n  & > uni-view {\r\n    height: 100%;\r\n  }\r\n}\r\n.shadow-warp {\r\n  position: relative;\r\n  box-shadow: 0 0 5px rgba(168, 92, 92, 0.1);\r\n}\r\n\r\n/* stylelint-disable selector-type-no-unknown */\r\nbutton::after {\r\n  border: none;\r\n}\r\n\r\nswiper,\r\nscroll-view {\r\n  flex: 1;\r\n  height: 100%;\r\n  overflow: hidden;\r\n}\r\n\r\nimage {\r\n  width: 100%;\r\n  height: 100%;\r\n  vertical-align: middle;\r\n}\r\n\r\n// 单行省略，优先使用 unocss: text-ellipsis\r\n.ellipsis {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n// 两行省略\r\n.ellipsis-2 {\r\n  display: -webkit-box;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  word-break: break-all;\r\n}\r\n\r\n// 三行省略\r\n.ellipsis-3 {\r\n  display: -webkit-box;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  -webkit-line-clamp: 3;\r\n  -webkit-box-orient: vertical;\r\n}\r\n\r\n/* 修改确认框的按钮颜色 */\r\n.wd-message {\r\n  .wd-message__button--confirm {\r\n    color: #07C160 !important;\r\n  }\r\n}\r\n\r\n/* 全局按钮样式覆盖 */\r\n.wd-button--primary {\r\n  background-color: #07C160 !important;\r\n  border-color: #07C160 !important;\r\n}\r\n</style>\r\n", "import '@/style/index.scss'\r\nimport '@/style/custom/main.css'\r\nimport '@/style/custom/icon.css'\r\nimport '@/style/custom/animation.css'\r\nimport { VueQueryPlugin } from '@tanstack/vue-query'\r\nimport 'virtual:uno.css'\r\nimport { createSSRApp } from 'vue'\r\n\r\nimport App from './App.vue'\r\nimport { prototypeInterceptor, requestInterceptor, routeInterceptor } from './interceptors'\r\nimport { registerGlobComp } from '@/components/registerGlobComp';\r\nimport store from './store'\r\nimport router from './router'\r\n\r\nexport function createApp() {\r\n  const app = createSSRApp(App)\r\n  app.use(store)\r\n  app.use(router)\r\n  app.use(routeInterceptor)\r\n  app.use(requestInterceptor)\r\n  app.use(prototypeInterceptor)\r\n  app.use(VueQueryPlugin)\r\n  //#ifndef MP-WEIXIN\r\n  // 注册全局组件\r\n  registerGlobComp(app);\r\n  // #endif\r\n  return {\r\n    app,\r\n  }\r\n}\r\n"], "names": ["befor<PERSON>ach", "uni", "createSSRApp", "App", "store", "router", "routeInterceptor", "requestInterceptor", "prototypeInterceptor", "<PERSON><PERSON><PERSON>ueryP<PERSON>in"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,MAAe,YAAA;AAAA,EACb,UAAU,SAAU,SAAS;AAC3B,YAAQ,IAAI,YAAY;AAChB,YAAA,IAAI,WAAW,QAAQ,IAAI;AAAA,EACrC;AAAA,EACA,QAAQ,SAAU,SAAS;AACzB,YAAQ,IAAI,UAAU;AACd,YAAA,IAAI,WAAW,QAAQ,IAAI;AAEnC,eAAW,MAAM;AACK,cAAQ;AAClBA,mBAAA,UAAA,EAAE,MAAM,IAAO,GAAA,CAA2C,GAAG,CAAC,SAAS;AAC/E,YAAI,6BAAM,MAAM;AACdC,wBAAAA,MAAI,WAAW,EAAE,KAAK,KAAK,MAAM;AAAA,QAAA;AAAA,MACnC,CACD;AAAA,OACA,GAAG;AAAA,EACR;AAAA,EACA,QAAQ,WAAY;AAClB,YAAQ,IAAI,UAAU;AAAA,EACxB;AAAA;AAAA,EAEA,YAAY;AAAA,IACV,eAAe;AAAA,IACf,YAAYA,oBAAI,kBAAkB;AAAA,IAClC,WAAW;AAAA,EAAA;AAEf;ACjBgB,SAAA,YAAA;AAAA,QAAA,MAAAC,2BAAAC,SAAA;AAAA,MAAA,IAAAC,iBAAA;AAAA,MAAA,IAAAC,mBAAA;AAAA,MAAA,IAAAC,mCAAA;AAAA,MAAA,IAAAC,uCAAA;AAAA,MAAA,IAAAC,2CAAA;AAAA,MAAA,IAAAC,4BAAA;AAAA,MAAA,UAAA,sBAAA,kBAAA;AAAA,MAAA,UAAA,mBAAA,eAAA;AAAA,SAAA;AAAA,IAaT;AAAA,EACP;AACA;;;;;"}