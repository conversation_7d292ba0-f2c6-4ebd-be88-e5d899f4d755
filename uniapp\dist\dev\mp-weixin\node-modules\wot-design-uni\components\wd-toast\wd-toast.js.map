{"version": 3, "file": "wd-toast.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-toast/wd-toast.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC10b2FzdC93ZC10b2FzdC52dWU"], "sourcesContent": ["<template>\n  <wd-overlay v-if=\"cover\" :z-index=\"zIndex\" lock-scroll :show=\"show\" custom-style=\"background-color: transparent;pointer-events: auto;\"></wd-overlay>\n  <wd-transition name=\"fade\" :show=\"show\" :custom-style=\"transitionStyle\" @after-enter=\"handleAfterEnter\" @after-leave=\"handleAfterLeave\">\n    <view :class=\"rootClass\">\n      <!--iconName优先级更高-->\n      <wd-loading\n        v-if=\"iconName === 'loading'\"\n        :type=\"loadingType\"\n        :color=\"loadingColor\"\n        :size=\"loadingSize\"\n        :custom-class=\"`wd-toast__icon ${direction === 'vertical' ? 'is-vertical' : ''}`\"\n      />\n      <view\n        :class=\"`wd-toast__iconWrap wd-toast__icon ${direction === 'vertical' ? 'is-vertical' : ''}`\"\n        v-else-if=\"iconName === 'success' || iconName === 'warning' || iconName === 'info' || iconName === 'error'\"\n      >\n        <view class=\"wd-toast__iconBox\">\n          <view class=\"wd-toast__iconSvg\" :style=\"svgStyle\"></view>\n        </view>\n      </view>\n      <wd-icon\n        v-else-if=\"iconClass\"\n        :custom-class=\"`wd-toast__icon ${direction === 'vertical' ? 'is-vertical' : ''}`\"\n        :size=\"iconSize\"\n        :class-prefix=\"classPrefix\"\n        :name=\"iconClass\"\n      ></wd-icon>\n      <!--文本-->\n      <view v-if=\"msg\" class=\"wd-toast__msg\">{{ msg }}</view>\n    </view>\n  </wd-transition>\n</template>\n\n<script lang=\"ts\">\nexport default {\n  name: 'wd-toast',\n  options: {\n    addGlobalClass: true,\n    virtualHost: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport wdIcon from '../wd-icon/wd-icon.vue'\nimport wdLoading from '../wd-loading/wd-loading.vue'\nimport wdOverlay from '../wd-overlay/wd-overlay.vue'\nimport wdTransition from '../wd-transition/wd-transition.vue'\n\nimport { computed, inject, onBeforeMount, ref, watch, type CSSProperties } from 'vue'\nimport base64 from '../common/base64'\nimport { defaultOptions, getToastOptionKey, toastIcon } from '.'\nimport { toastProps, type ToastDirection, type ToastLoadingType, type ToastOptions, type ToastProps } from './types'\nimport { addUnit, isDef, isFunction, objToStyle } from '../common/util'\n\nconst props = defineProps(toastProps)\nconst iconName = ref<string>('') // 图标类型\nconst msg = ref<string>('') // 消息内容\nconst position = ref<string>('middle')\nconst show = ref<boolean>(false)\nconst zIndex = ref<number>(100)\nconst loadingType = ref<ToastLoadingType>('outline')\nconst loadingColor = ref<string>('#4D80F0')\nconst iconSize = ref<string>() // 图标大小\nconst loadingSize = ref<string>() // loading大小\nconst svgStr = ref<string>('') // 图标\nconst cover = ref<boolean>(false) // 是否存在遮罩层\nconst classPrefix = ref<string>('wd-icon') // 图标前缀\nconst iconClass = ref<string>('') // 图标类名\nconst direction = ref<ToastDirection>('horizontal') // toast布局方向\n\nlet opened: (() => void) | null = null\n\nlet closed: (() => void) | null = null\n\nconst toastOptionKey = getToastOptionKey(props.selector)\nconst toastOption = inject(toastOptionKey, ref<ToastOptions>(defaultOptions)) // toast选项\n\n// 监听options变化展示\nwatch(\n  () => toastOption.value,\n  (newVal: ToastOptions) => {\n    reset(newVal)\n  },\n  {\n    deep: true,\n    immediate: true\n  }\n)\n\n// 监听options变化展示\nwatch(\n  () => iconName.value,\n  () => {\n    buildSvg()\n  },\n  {\n    deep: true,\n    immediate: true\n  }\n)\n\n/**\n * 动画自定义样式\n */\nconst transitionStyle = computed(() => {\n  const style: CSSProperties = {\n    'z-index': zIndex.value,\n    position: 'fixed',\n    top: '50%',\n    left: 0,\n    width: '100%',\n    transform: 'translate(0, -50%)',\n    'text-align': 'center',\n    'pointer-events': 'none'\n  }\n  return objToStyle(style)\n})\n\nconst rootClass = computed(() => {\n  return `wd-toast ${props.customClass} wd-toast--${position.value} ${\n    (iconName.value !== 'loading' || msg.value) && (iconName.value || iconClass.value) ? 'wd-toast--with-icon' : ''\n  } ${iconName.value === 'loading' && !msg.value ? 'wd-toast--loading' : ''} ${direction.value === 'vertical' ? 'is-vertical' : ''}`\n})\n\nconst svgStyle = computed(() => {\n  const style: CSSProperties = {\n    backgroundImage: `url(${svgStr.value})`\n  }\n  if (isDef(iconSize.value)) {\n    style.width = iconSize.value\n    style.height = iconSize.value\n  }\n  return objToStyle(style)\n})\n\nonBeforeMount(() => {\n  buildSvg()\n})\n\nfunction handleAfterEnter() {\n  if (isFunction(opened)) {\n    opened()\n  }\n}\n\nfunction handleAfterLeave() {\n  if (isFunction(closed)) {\n    closed()\n  }\n}\n\nfunction buildSvg() {\n  if (iconName.value !== 'success' && iconName.value !== 'warning' && iconName.value !== 'info' && iconName.value !== 'error') return\n  const iconSvg = toastIcon[iconName.value]()\n  const iconSvgStr = `\"data:image/svg+xml;base64,${base64(iconSvg)}\"`\n  svgStr.value = iconSvgStr\n}\n\n/**\n * 重置toast选项值\n * @param option toast选项值\n */\nfunction reset(option: ToastOptions) {\n  show.value = isDef(option.show) ? option.show : false\n\n  if (show.value) {\n    mergeOptionsWithProps(option, props)\n  }\n}\n\nfunction mergeOptionsWithProps(option: ToastOptions, props: ToastProps) {\n  iconName.value = isDef(option.iconName!) ? option.iconName! : props.iconName\n  iconClass.value = isDef(option.iconClass!) ? option.iconClass! : props.iconClass\n  msg.value = isDef(option.msg!) ? option.msg! : props.msg\n  position.value = isDef(option.position!) ? option.position! : props.position\n  zIndex.value = isDef(option.zIndex!) ? option.zIndex! : props.zIndex\n  loadingType.value = isDef(option.loadingType!) ? option.loadingType! : props.loadingType\n  loadingColor.value = isDef(option.loadingColor!) ? option.loadingColor! : props.loadingColor\n  iconSize.value = isDef(option.iconSize) ? addUnit(option.iconSize) : isDef(props.iconSize) ? addUnit(props.iconSize) : undefined\n  loadingSize.value = isDef(option.loadingSize) ? addUnit(option.loadingSize) : isDef(props.loadingSize) ? addUnit(props.loadingSize) : undefined\n  cover.value = isDef(option.cover!) ? option.cover! : props.cover\n  classPrefix.value = isDef(option.classPrefix) ? option.classPrefix : props.classPrefix\n  direction.value = isDef(option.direction) ? option.direction : props.direction\n  closed = isFunction(option.closed) ? option.closed : isFunction(props.closed) ? props.closed : null\n  opened = isFunction(option.opened) ? option.opened : isFunction(props.opened) ? props.opened : null\n}\n</script>\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-toast/wd-toast.vue'\nwx.createComponent(Component)"], "names": ["ref", "getToastOptionKey", "inject", "defaultOptions", "watch", "computed", "objToStyle", "isDef", "onBeforeMount", "isFunction", "toastIcon", "base64", "props", "addUnit"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AA6CA,MAAA,SAAmB,MAAA;AACnB,MAAA,YAAsB,MAAA;AACtB,MAAA,YAAsB,MAAA;AACtB,MAAA,eAAyB,MAAA;AAdzB,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,gBAAgB;AAAA,EAAA;AAEpB;;;;AAeA,UAAM,QAAQ;AACR,UAAA,WAAWA,kBAAY,EAAE;AACzB,UAAA,MAAMA,kBAAY,EAAE;AACpB,UAAA,WAAWA,kBAAY,QAAQ;AAC/B,UAAA,OAAOA,kBAAa,KAAK;AACzB,UAAA,SAASA,kBAAY,GAAG;AACxB,UAAA,cAAcA,kBAAsB,SAAS;AAC7C,UAAA,eAAeA,kBAAY,SAAS;AAC1C,UAAM,WAAWA,cAAAA,IAAY;AAC7B,UAAM,cAAcA,cAAAA,IAAY;AAC1B,UAAA,SAASA,kBAAY,EAAE;AACvB,UAAA,QAAQA,kBAAa,KAAK;AAC1B,UAAA,cAAcA,kBAAY,SAAS;AACnC,UAAA,YAAYA,kBAAY,EAAE;AAC1B,UAAA,YAAYA,kBAAoB,YAAY;AAElD,QAAI,SAA8B;AAElC,QAAI,SAA8B;AAE5B,UAAA,iBAAiBC,cAAAA,kBAAkB,MAAM,QAAQ;AACvD,UAAM,cAAcC,cAAA,OAAO,gBAAgBF,cAAA,IAAkBG,cAAc,cAAA,CAAC;AAG5EC,kBAAA;AAAA,MACE,MAAM,YAAY;AAAA,MAClB,CAAC,WAAyB;AACxB,cAAM,MAAM;AAAA,MACd;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,WAAW;AAAA,MAAA;AAAA,IAEf;AAGAA,kBAAA;AAAA,MACE,MAAM,SAAS;AAAA,MACf,MAAM;AACK,iBAAA;AAAA,MACX;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,WAAW;AAAA,MAAA;AAAA,IAEf;AAKM,UAAA,kBAAkBC,cAAAA,SAAS,MAAM;AACrC,YAAM,QAAuB;AAAA,QAC3B,WAAW,OAAO;AAAA,QAClB,UAAU;AAAA,QACV,KAAK;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,QACP,WAAW;AAAA,QACX,cAAc;AAAA,QACd,kBAAkB;AAAA,MACpB;AACA,aAAOC,cAAAA,WAAW,KAAK;AAAA,IAAA,CACxB;AAEK,UAAA,YAAYD,cAAAA,SAAS,MAAM;AAC/B,aAAO,YAAY,MAAM,WAAW,cAAc,SAAS,KAAK,KAC7D,SAAS,UAAU,aAAa,IAAI,WAAW,SAAS,SAAS,UAAU,SAAS,wBAAwB,EAC/G,IAAI,SAAS,UAAU,aAAa,CAAC,IAAI,QAAQ,sBAAsB,EAAE,IAAI,UAAU,UAAU,aAAa,gBAAgB,EAAE;AAAA,IAAA,CACjI;AAEK,UAAA,WAAWA,cAAAA,SAAS,MAAM;AAC9B,YAAM,QAAuB;AAAA,QAC3B,iBAAiB,OAAO,OAAO,KAAK;AAAA,MACtC;AACI,UAAAE,cAAA,MAAM,SAAS,KAAK,GAAG;AACzB,cAAM,QAAQ,SAAS;AACvB,cAAM,SAAS,SAAS;AAAA,MAAA;AAE1B,aAAOD,cAAAA,WAAW,KAAK;AAAA,IAAA,CACxB;AAEDE,kBAAAA,cAAc,MAAM;AACT,eAAA;AAAA,IAAA,CACV;AAED,aAAS,mBAAmB;AACtB,UAAAC,cAAAA,WAAW,MAAM,GAAG;AACf,eAAA;AAAA,MAAA;AAAA,IACT;AAGF,aAAS,mBAAmB;AACtB,UAAAA,cAAAA,WAAW,MAAM,GAAG;AACf,eAAA;AAAA,MAAA;AAAA,IACT;AAGF,aAAS,WAAW;AACd,UAAA,SAAS,UAAU,aAAa,SAAS,UAAU,aAAa,SAAS,UAAU,UAAU,SAAS,UAAU;AAAS;AAC7H,YAAM,UAAUC,cAAAA,UAAU,SAAS,KAAK,EAAE;AAC1C,YAAM,aAAa,8BAA8BC,cAAO,OAAA,OAAO,CAAC;AAChE,aAAO,QAAQ;AAAA,IAAA;AAOjB,aAAS,MAAM,QAAsB;AACnC,WAAK,QAAQJ,oBAAM,OAAO,IAAI,IAAI,OAAO,OAAO;AAEhD,UAAI,KAAK,OAAO;AACd,8BAAsB,QAAQ,KAAK;AAAA,MAAA;AAAA,IACrC;AAGO,aAAA,sBAAsB,QAAsBK,QAAmB;AACtE,eAAS,QAAQL,cAAAA,MAAM,OAAO,QAAS,IAAI,OAAO,WAAYK,OAAM;AACpE,gBAAU,QAAQL,cAAAA,MAAM,OAAO,SAAU,IAAI,OAAO,YAAaK,OAAM;AACvE,UAAI,QAAQL,cAAAA,MAAM,OAAO,GAAI,IAAI,OAAO,MAAOK,OAAM;AACrD,eAAS,QAAQL,cAAAA,MAAM,OAAO,QAAS,IAAI,OAAO,WAAYK,OAAM;AACpE,aAAO,QAAQL,cAAAA,MAAM,OAAO,MAAO,IAAI,OAAO,SAAUK,OAAM;AAC9D,kBAAY,QAAQL,cAAAA,MAAM,OAAO,WAAY,IAAI,OAAO,cAAeK,OAAM;AAC7E,mBAAa,QAAQL,cAAAA,MAAM,OAAO,YAAa,IAAI,OAAO,eAAgBK,OAAM;AAChF,eAAS,QAAQL,oBAAM,OAAO,QAAQ,IAAIM,sBAAQ,OAAO,QAAQ,IAAIN,cAAA,MAAMK,OAAM,QAAQ,IAAIC,cAAQD,QAAAA,OAAM,QAAQ,IAAI;AACvH,kBAAY,QAAQL,oBAAM,OAAO,WAAW,IAAIM,sBAAQ,OAAO,WAAW,IAAIN,cAAA,MAAMK,OAAM,WAAW,IAAIC,cAAQD,QAAAA,OAAM,WAAW,IAAI;AACtI,YAAM,QAAQL,cAAAA,MAAM,OAAO,KAAM,IAAI,OAAO,QAASK,OAAM;AAC3D,kBAAY,QAAQL,cAAAA,MAAM,OAAO,WAAW,IAAI,OAAO,cAAcK,OAAM;AAC3E,gBAAU,QAAQL,cAAAA,MAAM,OAAO,SAAS,IAAI,OAAO,YAAYK,OAAM;AAC5D,eAAAH,cAAAA,WAAW,OAAO,MAAM,IAAI,OAAO,SAASA,cAAA,WAAWG,OAAM,MAAM,IAAIA,OAAM,SAAS;AACtF,eAAAH,cAAAA,WAAW,OAAO,MAAM,IAAI,OAAO,SAASA,cAAA,WAAWG,OAAM,MAAM,IAAIA,OAAM,SAAS;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzLjG,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}