{"version": 3, "file": "route.js", "sources": ["../../../../src/interceptors/route.ts"], "sourcesContent": ["/**\r\n * 路由拦截，通常也是登录拦截\r\n * 可以设置路由白名单，或者黑名单，看业务需要选哪一个\r\n * 我这里应为大部分都可以随便进入，所以使用黑名单\r\n */\r\nimport { useUserStore } from '@/store'\r\nimport { needLoginPages as _needLoginPages, getNeedLoginPages } from '@/utils'\r\n\r\n// TODO Check\r\nconst loginRoute = '/pages/login/index'\r\n\r\nconst isLogined = () => {\r\n  const userStore = useUserStore()\r\n  return userStore.isLogined\r\n}\r\n\r\nconst isDev = import.meta.env.DEV\r\n\r\n// 黑名单登录拦截器 - （适用于大部分页面不需要登录，少部分页面需要登录）\r\nconst navigateToInterceptor = {\r\n  // 注意，这里的url是 '/' 开头的，如 '/pages/index/index'，跟 'pages.json' 里面的 path 不同\r\n  invoke({ url }: { url: string }) {\r\n    // console.log(url) // /pages/route-interceptor/index?name=feige&age=30\r\n    const path = url.split('?')[0]\r\n    let needLoginPages: string[] = []\r\n    // 为了防止开发时出现BUG，这里每次都获取一下。生产环境可以移到函数外，性能更好\r\n    if (isDev) {\r\n      needLoginPages = getNeedLoginPages()\r\n    } else {\r\n      needLoginPages = _needLoginPages\r\n    }\r\n    const isNeedLogin = needLoginPages.includes(path)\r\n    if (!isNeedLogin) {\r\n      return true\r\n    }\r\n    const hasLogin = isLogined()\r\n    if (hasLogin) {\r\n      return true\r\n    }\r\n    const redirectRoute = `${loginRoute}?redirect=${encodeURIComponent(url)}`\r\n    uni.navigateTo({ url: redirectRoute })\r\n    return false\r\n  },\r\n}\r\n\r\nexport const routeInterceptor = {\r\n  install() {\r\n    uni.addInterceptor('navigateTo', navigateToInterceptor)\r\n    uni.addInterceptor('reLaunch', navigateToInterceptor)\r\n    uni.addInterceptor('redirectTo', navigateToInterceptor)\r\n    uni.addInterceptor('switchTab', navigateToInterceptor)\r\n  },\r\n}\r\n"], "names": ["useUserStore", "getNeedLoginPages", "uni"], "mappings": ";;;;;AASA,MAAM,aAAa;AAEnB,MAAM,YAAY,MAAM;AACtB,QAAM,YAAYA,WAAAA,aAAa;AAC/B,SAAO,UAAU;AACnB;AAKA,MAAM,wBAAwB;AAAA;AAAA,EAE5B,OAAO,EAAE,OAAwB;AAE/B,UAAM,OAAO,IAAI,MAAM,GAAG,EAAE,CAAC;AAC7B,QAAI,iBAA2B,CAAC;AAErB;AACT,uBAAiBC,YAAAA,kBAAkB;AAAA,IAAA;AAI/B,UAAA,cAAc,eAAe,SAAS,IAAI;AAChD,QAAI,CAAC,aAAa;AACT,aAAA;AAAA,IAAA;AAET,UAAM,WAAW,UAAU;AAC3B,QAAI,UAAU;AACL,aAAA;AAAA,IAAA;AAET,UAAM,gBAAgB,GAAG,UAAU,aAAa,mBAAmB,GAAG,CAAC;AACvEC,kBAAAA,MAAI,WAAW,EAAE,KAAK,cAAA,CAAe;AAC9B,WAAA;AAAA,EAAA;AAEX;AAEO,MAAM,mBAAmB;AAAA,EAC9B,UAAU;AACJA,wBAAA,eAAe,cAAc,qBAAqB;AAClDA,wBAAA,eAAe,YAAY,qBAAqB;AAChDA,wBAAA,eAAe,cAAc,qBAAqB;AAClDA,wBAAA,eAAe,aAAa,qBAAqB;AAAA,EAAA;AAEzD;;"}