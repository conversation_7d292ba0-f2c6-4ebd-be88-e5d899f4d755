{"version": 3, "file": "index.js", "sources": ["../../../../src/utils/index.ts"], "sourcesContent": ["import { pages, subPackages, tabBar } from '@/pages.json'\r\nimport { isMpWeixin } from './platform'\r\n\r\nconst getLastPage = () => {\r\n  // getCurrentPages() 至少有1个元素，所以不再额外判断\r\n  // const lastPage = getCurrentPages().at(-1)\r\n  // 上面那个在低版本安卓中打包回报错，所以改用下面这个【虽然我加了src/interceptions/prototype.ts，但依然报错】\r\n  const pages = getCurrentPages()\r\n  return pages[pages.length - 1]\r\n}\r\n\r\n/** 判断当前页面是否是tabbar页  */\r\nexport const getIsTabbar = () => {\r\n  if (!tabBar) {\r\n    return false\r\n  }\r\n  if (!tabBar.list.length) {\r\n    // 通常有tabBar的话，list不能有空，且至少有2个元素，这里其实不用处理\r\n    return false\r\n  }\r\n  const lastPage = getLastPage()\r\n  const currPath = lastPage.route\r\n  return !!tabBar.list.find((e) => e.pagePath === currPath)\r\n}\r\n\r\n/**\r\n * 获取当前页面路由的 path 路径和 redirectPath 路径\r\n * path 如 ‘/pages/login/index’\r\n * redirectPath 如 ‘/pages/demo/base/route-interceptor’\r\n */\r\nexport const currRoute = () => {\r\n  const lastPage = getLastPage()\r\n  const currRoute = (lastPage as any).$page\r\n  // console.log('lastPage.$page:', currRoute)\r\n  // console.log('lastPage.$page.fullpath:', currRoute.fullPath)\r\n  // console.log('lastPage.$page.options:', currRoute.options)\r\n  // console.log('lastPage.options:', (lastPage as any).options)\r\n  // 经过多端测试，只有 fullPath 靠谱，其他都不靠谱\r\n  const { fullPath } = currRoute as { fullPath: string }\r\n  // console.log(fullPath)\r\n  // eg: /pages/login/index?redirect=%2Fpages%2Fdemo%2Fbase%2Froute-interceptor (小程序)\r\n  // eg: /pages/login/index?redirect=%2Fpages%2Froute-interceptor%2Findex%3Fname%3Dfeige%26age%3D30(h5)\r\n  return getUrlObj(fullPath)\r\n}\r\n\r\nconst ensureDecodeURIComponent = (url: string) => {\r\n  if (url.startsWith('%')) {\r\n    return ensureDecodeURIComponent(decodeURIComponent(url))\r\n  }\r\n  return url\r\n}\r\n/**\r\n * 解析 url 得到 path 和 query\r\n * 比如输入url: /pages/login/index?redirect=%2Fpages%2Fdemo%2Fbase%2Froute-interceptor\r\n * 输出: {path: /pages/login/index, query: {redirect: /pages/demo/base/route-interceptor}}\r\n */\r\nexport const getUrlObj = (url: string) => {\r\n  const [path, queryStr] = url.split('?')\r\n  // console.log(path, queryStr)\r\n\r\n  if (!queryStr) {\r\n    return {\r\n      path,\r\n      query: {},\r\n    }\r\n  }\r\n  const query: Record<string, string> = {}\r\n  queryStr.split('&').forEach((item) => {\r\n    const [key, value] = item.split('=')\r\n    // console.log(key, value)\r\n    query[key] = ensureDecodeURIComponent(value) // 这里需要统一 decodeURIComponent 一下，可以兼容h5和微信y\r\n  })\r\n  return { path, query }\r\n}\r\n/**\r\n * 得到所有的需要登录的pages，包括主包和分包的\r\n * 这里设计得通用一点，可以传递key作为判断依据，默认是 needLogin, 与 route-block 配对使用\r\n * 如果没有传 key，则表示所有的pages，如果传递了 key, 则表示通过 key 过滤\r\n */\r\nexport const getAllPages = (key = 'needLogin') => {\r\n  // 这里处理主包\r\n  const mainPages = [\r\n    ...pages\r\n      .filter((page) => !key || page[key])\r\n      .map((page) => ({\r\n        ...page,\r\n        path: `/${page.path}`,\r\n      })),\r\n  ]\r\n  // 这里处理分包\r\n  const subPages: any[] = []\r\n  subPackages.forEach((subPageObj) => {\r\n    // console.log(subPageObj)\r\n    const { root } = subPageObj\r\n\r\n    subPageObj.pages\r\n      .filter((page) => !key || page[key])\r\n      .forEach((page: { path: string } & Record<string, any>) => {\r\n        subPages.push({\r\n          ...page,\r\n          path: `/${root}/${page.path}`,\r\n        })\r\n      })\r\n  })\r\n  const result = [...mainPages, ...subPages]\r\n  // console.log(`getAllPages by ${key} result: `, result)\r\n  return result\r\n}\r\n\r\n/**\r\n * 得到所有的需要登录的pages，包括主包和分包的\r\n * 只得到 path 数组\r\n */\r\nexport const getNeedLoginPages = (): string[] => getAllPages('needLogin').map((page) => page.path)\r\n\r\n/**\r\n * 得到所有的需要登录的pages，包括主包和分包的\r\n * 只得到 path 数组\r\n */\r\nexport const needLoginPages: string[] = getAllPages('needLogin').map((page) => page.path)\r\n\r\n/**\r\n * 根据微信小程序当前环境，判断应该获取的BaseUrl\r\n */\r\nexport const getEnvBaseUrl = () => {\r\n  // 请求基准地址\r\n  let baseUrl = import.meta.env.VITE_SERVER_BASEURL\r\n\r\n  // 微信小程序端环境区分\r\n  if (isMpWeixin) {\r\n    const {\r\n      miniProgram: { envVersion },\r\n    } = uni.getAccountInfoSync()\r\n\r\n    switch (envVersion) {\r\n      case 'develop':\r\n        baseUrl = import.meta.env.VITE_SERVER_BASEURL__WEIXIN_DEVELOP || baseUrl\r\n        break\r\n      case 'trial':\r\n        baseUrl = import.meta.env.VITE_SERVER_BASEURL__WEIXIN_TRIAL || baseUrl\r\n        break\r\n      case 'release':\r\n        baseUrl = import.meta.env.VITE_SERVER_BASEURL__WEIXIN_RELEASE || baseUrl\r\n        break\r\n    }\r\n  }\r\n\r\n  return baseUrl\r\n}\r\n\r\n/**\r\n * 根据微信小程序当前环境，判断应该获取的UPLOAD_BASEURL\r\n */\r\nexport const getEnvBaseUploadUrl = () => {\r\n  // 请求基准地址\r\n  let baseUploadUrl = import.meta.env.VITE_UPLOAD_BASEURL\r\n\r\n  // 微信小程序端环境区分\r\n  if (isMpWeixin) {\r\n    const {\r\n      miniProgram: { envVersion },\r\n    } = uni.getAccountInfoSync()\r\n\r\n    switch (envVersion) {\r\n      case 'develop':\r\n        baseUploadUrl = import.meta.env.VITE_UPLOAD_BASEURL__WEIXIN_DEVELOP || baseUploadUrl\r\n        break\r\n      case 'trial':\r\n        baseUploadUrl = import.meta.env.VITE_UPLOAD_BASEURL__WEIXIN_TRIAL || baseUploadUrl\r\n        break\r\n      case 'release':\r\n        baseUploadUrl = import.meta.env.VITE_UPLOAD_BASEURL__WEIXIN_RELEASE || baseUploadUrl\r\n        break\r\n    }\r\n  }\r\n\r\n  return baseUploadUrl\r\n}\r\n/**\r\n * 时间格式化\r\n * @param value\r\n * @param fmt\r\n * @returns {*}\r\n */\r\nexport function formatDate(value, fmt) {\r\n  var regPos = /^\\d+(\\.\\d+)?$/;\r\n  if(regPos.test(value)){\r\n    //如果是数字\r\n    let getDate = new Date(value);\r\n    let o = {\r\n      'M+': getDate.getMonth() + 1,\r\n      'd+': getDate.getDate(),\r\n      'h+': getDate.getHours(),\r\n      'H+': getDate.getHours(),\r\n      'm+': getDate.getMinutes(),\r\n      's+': getDate.getSeconds(),\r\n      'q+': Math.floor((getDate.getMonth() + 3) / 3),\r\n      'S': getDate.getMilliseconds()\r\n    };\r\n    if (/(y+)/.test(fmt)) {\r\n      fmt = fmt.replace(RegExp.$1, (getDate.getFullYear() + '').substr(4 - RegExp.$1.length))\r\n    }\r\n    for (let k in o) {\r\n      if (new RegExp('(' + k + ')').test(fmt)) {\r\n        fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)))\r\n      }\r\n    }\r\n    return fmt;\r\n  }else{\r\n    //TODO\r\n    if(value && value.length>0){\r\n      value = value.trim();\r\n      return value.substr(0,fmt.length);\r\n    }\r\n    return value\r\n  }\r\n}\r\n"], "names": ["pages", "subPackages", "isMpWeixin", "uni"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AA+Ea,MAAA,cAAc,CAAC,MAAM,gBAAgB;AAEhD,QAAM,YAAY;AAAA,IAChB,GAAGA,mBACA,OAAO,CAAC,SAAS,CAAC,OAAO,KAAK,GAAG,CAAC,EAClC,IAAI,CAAC,SAAU,iCACX,OADW;AAAA,MAEd,MAAM,IAAI,KAAK,IAAI;AAAA,IAAA,EACnB;AAAA,EACN;AAEA,QAAM,WAAkB,CAAC;AACbC,2BAAA,QAAQ,CAAC,eAAe;AAE5B,UAAA,EAAE,SAAS;AAEjB,eAAW,MACR,OAAO,CAAC,SAAS,CAAC,OAAO,KAAK,GAAG,CAAC,EAClC,QAAQ,CAAC,SAAiD;AACzD,eAAS,KAAK,iCACT,OADS;AAAA,QAEZ,MAAM,IAAI,IAAI,IAAI,KAAK,IAAI;AAAA,MAAA,EAC5B;AAAA,IAAA,CACF;AAAA,EAAA,CACJ;AACD,QAAM,SAAS,CAAC,GAAG,WAAW,GAAG,QAAQ;AAElC,SAAA;AACT;AAMa,MAAA,oBAAoB,MAAgB,YAAY,WAAW,EAAE,IAAI,CAAC,SAAS,KAAK,IAAI;AAMzD,YAAY,WAAW,EAAE,IAAI,CAAC,SAAS,KAAK,IAAI;AAKjF,MAAM,gBAAgB,MAAM;AAE7B,MAAA,UAAU;AAGd,MAAIC,2BAAY;AACR,UAAA;AAAA,MACJ,aAAa,EAAE,WAAW;AAAA,IAAA,IACxBC,cAAAA,MAAI,mBAAmB;AAE3B,YAAQ,YAAY;AAAA,MAClB,KAAK;AAC8D,kBAAA;AACjE;AAAA,MACF,KAAK;AAC4D,kBAAA;AAC/D;AAAA,MACF,KAAK;AAC8D,kBAAA;AACjE;AAAA,IAAA;AAAA,EACJ;AAGK,SAAA;AACT;AAKO,MAAM,sBAAsB,MAAM;AAEnC,MAAA,gBAAgB;AAGpB,MAAID,2BAAY;AACR,UAAA;AAAA,MACJ,aAAa,EAAE,WAAW;AAAA,IAAA,IACxBC,cAAAA,MAAI,mBAAmB;AAE3B,YAAQ,YAAY;AAAA,MAClB,KAAK;AACoE,wBAAA;AACvE;AAAA,MACF,KAAK;AACkE,wBAAA;AACrE;AAAA,MACF,KAAK;AACoE,wBAAA;AACvE;AAAA,IAAA;AAAA,EACJ;AAGK,SAAA;AACT;AAOgB,SAAA,WAAW,OAAO,KAAK;AACrC,MAAI,SAAS;AACV,MAAA,OAAO,KAAK,KAAK,GAAE;AAEhB,QAAA,UAAU,IAAI,KAAK,KAAK;AAC5B,QAAI,IAAI;AAAA,MACN,MAAM,QAAQ,SAAA,IAAa;AAAA,MAC3B,MAAM,QAAQ,QAAQ;AAAA,MACtB,MAAM,QAAQ,SAAS;AAAA,MACvB,MAAM,QAAQ,SAAS;AAAA,MACvB,MAAM,QAAQ,WAAW;AAAA,MACzB,MAAM,QAAQ,WAAW;AAAA,MACzB,MAAM,KAAK,OAAO,QAAQ,SAAS,IAAI,KAAK,CAAC;AAAA,MAC7C,KAAK,QAAQ,gBAAgB;AAAA,IAC/B;AACI,QAAA,OAAO,KAAK,GAAG,GAAG;AACpB,YAAM,IAAI,QAAQ,OAAO,KAAK,QAAQ,YAAgB,IAAA,IAAI,OAAO,IAAI,OAAO,GAAG,MAAM,CAAC;AAAA,IAAA;AAExF,aAAS,KAAK,GAAG;AACX,UAAA,IAAI,OAAO,MAAM,IAAI,GAAG,EAAE,KAAK,GAAG,GAAG;AACjC,cAAA,IAAI,QAAQ,OAAO,IAAK,OAAO,GAAG,WAAW,IAAM,EAAE,CAAC,KAAO,OAAO,EAAE,CAAC,GAAG,QAAQ,KAAK,EAAE,CAAC,GAAG,MAAM,CAAE;AAAA,MAAA;AAAA,IAC7G;AAEK,WAAA;AAAA,EAAA,OACJ;AAEA,QAAA,SAAS,MAAM,SAAO,GAAE;AACzB,cAAQ,MAAM,KAAK;AACnB,aAAO,MAAM,OAAO,GAAE,IAAI,MAAM;AAAA,IAAA;AAE3B,WAAA;AAAA,EAAA;AAEX;;;;;"}