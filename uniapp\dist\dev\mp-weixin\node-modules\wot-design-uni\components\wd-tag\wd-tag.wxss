/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 辅助函数
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/* 转换成字符串 */
/* 判断是否存在 Modifier */
/* 判断是否存在伪类 */
/**
 * 主题色切换
 * @params $theme-color 主题色
 * @params $type 变暗’dark‘ 变亮 'light'
 * @params $mix-color 自己设置的混色
 */
/**
 * 颜色结果切换， 如果开启线性渐变色 使用渐变色，如果没有开启，那么使用主题色
 * @params $open-linear 是否开启线性渐变色
 * @params $deg 渐变色角度
 * @params $theme-color 当前配色
 * @params [Array] $set 主题色明暗设置，与 $color-list 数量对应
 * @params [Array] $color-list 渐变色顺序， $color-list 和 $per-list 数量相同
 * @params [Array] $per-list 渐变色比例
 */
/**
 * UI规范基础变量
 */
/*----------------------------------------- Theme color. start ----------------------------------------*/
/* 主题颜色 */
/* 辅助色 */
/* 文字颜色（默认浅色背景下 */
/* 暗黑模式 */
/* 图形颜色 */
/*----------------------------------------- Theme color. end -------------------------------------------*/
/*-------------------------------- Theme color application size.  start --------------------------------*/
/* 文字字号 */
/* 文字字重 */
/* 尺寸 */
/*-------------------------------- Theme color application size.  end --------------------------------*/
/* component var */
/* action-sheet */
/* badge */
/* button */
/* cell */
/* calendar */
/* checkbox */
/* collapse */
/* divider */
/* drop-menu */
/* input-number */
/* input */
/* textarea */
/* loadmore */
/* message-box */
/* notice-bar */
/* pagination */
/* picker */
/* col-picker */
/* overlay */
/* popup */
/* progress */
/* radio */
/* search */
/* slider */
/* sort-button */
/* steps */
/* switch */
/* tabs */
/* tag */
/* toast */
/* loading */
/* tooltip */
/* popover */
/* grid-item */
/* statustip */
/* card */
/* upload */
/* curtain */
/* notify */
/* skeleton */
/* circle */
/* swiper */
/* swiper-nav */
/* segmented */
/* tabbar */
/* tabbar-item */
/* navbar */
/* navbar-capsule */
/* table */
/* sidebar */
/* sidebar-item */
/* fab */
/* count-down */
/* keyboard */
/* number-keyboard */
/* passwod-input */
/* form-item */
/* backtop */
/* index-bar */
/* text */
/* video-preview */
/* img-cropper */
/* floating-panel */
/* signature */
/**
 * 混合宏
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/**
 * 辅助函数
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/* 转换成字符串 */
/* 判断是否存在 Modifier */
/* 判断是否存在伪类 */
/**
 * 主题色切换
 * @params $theme-color 主题色
 * @params $type 变暗’dark‘ 变亮 'light'
 * @params $mix-color 自己设置的混色
 */
/**
 * 颜色结果切换， 如果开启线性渐变色 使用渐变色，如果没有开启，那么使用主题色
 * @params $open-linear 是否开启线性渐变色
 * @params $deg 渐变色角度
 * @params $theme-color 当前配色
 * @params [Array] $set 主题色明暗设置，与 $color-list 数量对应
 * @params [Array] $color-list 渐变色顺序， $color-list 和 $per-list 数量相同
 * @params [Array] $per-list 渐变色比例
 */
/**
  * BEM，定义块（b)
  */
/* 定义元素（e），对于伪类，会自动将 e 嵌套在 伪类 底下 */
/* 此方法用于生成穿透样式 */
/* 定义元素（e），对于伪类，会自动将 e 嵌套在 伪类 底下 */
/* 定义状态（m） */
/* 定义状态（m） */
/* 对于需要需要嵌套在 m 底下的 e，调用这个混合宏，一般在切换整个组件的状态，如切换颜色的时候 */
/* 状态，生成 is-$state 类名 */
/**
  * 常用混合宏
  */
/* 单行超出隐藏 */
/* 多行超出隐藏 */
/* 清除浮动 */
/* 0.5px 边框 指定方向*/
/* 0.5px 边框 环绕 */
/**
  * 三角形实现尖角样式，适用于背景透明情况
  * @param $size 三角形高，底边为 $size * 2
  * @param $bg 三角形背景颜色
  */
/**
  * 正方形实现尖角样式，适用于背景不透明情况
  * @param $size 正方形边长
  * @param $bg 正方形背景颜色
  * @param $z-index z-index属性值，不得大于外部包裹器
  * @param $box-shadow 阴影
*/
.wd-tag.data-v-14921b29 {
  font-size: var(--wot-tag-small-fs, var(--wot-fs-aid, 10px));
  display: inline-block;
  color: var(--wot-tag-color, var(--wot-color-white, rgb(255, 255, 255)));
  padding: 0 3px;
  border-radius: 2px;
  transition: opacity 0.3s;
  vertical-align: middle;
  line-height: initial;
}
.wd-tag.is-default.data-v-14921b29 {
  background: var(--wot-tag-info-bg, linear-gradient(49deg,#808080 0%,#999999 100%));
}
.wd-tag.is-default.is-plain.data-v-14921b29 {
  background: transparent;
  color: var(--wot-tag-info-color, #585858);
  border: 1px solid var(--wot-tag-info-color, #585858);
  padding: 0 4px;
}
.wd-tag.is-default.is-round.data-v-14921b29 {
  line-height: 1.2;
  font-size: var(--wot-tag-fs, var(--wot-fs-secondary, 12px));
  padding: 4px 11px;
  background: transparent;
  color: var(--wot-tag-round-color, rgb(102, 102, 102));
  border: 1px solid var(--wot-tag-round-border-color, rgb(225, 225, 225));
  border-radius: var(--wot-tag-round-radius, 12px);
}
.wd-tag.is-default.is-mark.data-v-14921b29 {
  padding: 1px 6px;
  border-radius: var(--wot-tag-mark-radius, 6px 2px 6px 2px);
}
.wd-tag.is-default.is-mark.is-plain.data-v-14921b29 {
  padding: 0 6px;
}
.wd-tag.is-default.is-active.data-v-14921b29 {
  color: var(--wot-tag-primary-color, var(--wot-color-theme, #4d80f0));
  border-color: var(--wot-tag-primary-color, var(--wot-color-theme, #4d80f0));
}
.wd-tag.is-primary.data-v-14921b29 {
  background: var(--wot-tag-primary-bg, var(--wot-color-theme, #4d80f0));
}
.wd-tag.is-primary.is-plain.data-v-14921b29 {
  background: transparent;
  color: var(--wot-tag-primary-color, var(--wot-color-theme, #4d80f0));
  border: 1px solid var(--wot-tag-primary-color, var(--wot-color-theme, #4d80f0));
  padding: 0 4px;
}
.wd-tag.is-primary.is-round.data-v-14921b29 {
  line-height: 1.2;
  font-size: var(--wot-tag-fs, var(--wot-fs-secondary, 12px));
  padding: 4px 11px;
  background: transparent;
  color: var(--wot-tag-primary-color, var(--wot-color-theme, #4d80f0));
  border: 1px solid var(--wot-tag-primary-color, var(--wot-color-theme, #4d80f0));
  border-radius: var(--wot-tag-round-radius, 12px);
}
.wd-tag.is-primary.is-mark.data-v-14921b29 {
  padding: 1px 6px;
  border-radius: var(--wot-tag-mark-radius, 6px 2px 6px 2px);
}
.wd-tag.is-primary.is-mark.is-plain.data-v-14921b29 {
  padding: 0 6px;
}
.wd-tag.is-primary.is-active.data-v-14921b29 {
  color: var(--wot-tag-primary-color, var(--wot-color-theme, #4d80f0));
  border-color: var(--wot-tag-primary-color, var(--wot-color-theme, #4d80f0));
}
.wd-tag.is-danger.data-v-14921b29 {
  background: var(--wot-tag-danger-bg, var(--wot-color-danger, #fa4350));
}
.wd-tag.is-danger.is-plain.data-v-14921b29 {
  background: transparent;
  color: var(--wot-tag-danger-color, var(--wot-color-danger, #fa4350));
  border: 1px solid var(--wot-tag-danger-color, var(--wot-color-danger, #fa4350));
  padding: 0 4px;
}
.wd-tag.is-danger.is-round.data-v-14921b29 {
  line-height: 1.2;
  font-size: var(--wot-tag-fs, var(--wot-fs-secondary, 12px));
  padding: 4px 11px;
  background: transparent;
  color: var(--wot-tag-danger-color, var(--wot-color-danger, #fa4350));
  border: 1px solid var(--wot-tag-danger-color, var(--wot-color-danger, #fa4350));
  border-radius: var(--wot-tag-round-radius, 12px);
}
.wd-tag.is-danger.is-mark.data-v-14921b29 {
  padding: 1px 6px;
  border-radius: var(--wot-tag-mark-radius, 6px 2px 6px 2px);
}
.wd-tag.is-danger.is-mark.is-plain.data-v-14921b29 {
  padding: 0 6px;
}
.wd-tag.is-danger.is-active.data-v-14921b29 {
  color: var(--wot-tag-primary-color, var(--wot-color-theme, #4d80f0));
  border-color: var(--wot-tag-primary-color, var(--wot-color-theme, #4d80f0));
}
.wd-tag.is-warning.data-v-14921b29 {
  background: var(--wot-tag-warning-bg, var(--wot-color-warning, #f0883a));
}
.wd-tag.is-warning.is-plain.data-v-14921b29 {
  background: transparent;
  color: var(--wot-tag-warning-color, var(--wot-color-warning, #f0883a));
  border: 1px solid var(--wot-tag-warning-color, var(--wot-color-warning, #f0883a));
  padding: 0 4px;
}
.wd-tag.is-warning.is-round.data-v-14921b29 {
  line-height: 1.2;
  font-size: var(--wot-tag-fs, var(--wot-fs-secondary, 12px));
  padding: 4px 11px;
  background: transparent;
  color: var(--wot-tag-warning-color, var(--wot-color-warning, #f0883a));
  border: 1px solid var(--wot-tag-warning-color, var(--wot-color-warning, #f0883a));
  border-radius: var(--wot-tag-round-radius, 12px);
}
.wd-tag.is-warning.is-mark.data-v-14921b29 {
  padding: 1px 6px;
  border-radius: var(--wot-tag-mark-radius, 6px 2px 6px 2px);
}
.wd-tag.is-warning.is-mark.is-plain.data-v-14921b29 {
  padding: 0 6px;
}
.wd-tag.is-warning.is-active.data-v-14921b29 {
  color: var(--wot-tag-primary-color, var(--wot-color-theme, #4d80f0));
  border-color: var(--wot-tag-primary-color, var(--wot-color-theme, #4d80f0));
}
.wd-tag.is-success.data-v-14921b29 {
  background: var(--wot-tag-success-bg, var(--wot-color-success, #34d19d));
}
.wd-tag.is-success.is-plain.data-v-14921b29 {
  background: transparent;
  color: var(--wot-tag-success-color, var(--wot-color-success, #34d19d));
  border: 1px solid var(--wot-tag-success-color, var(--wot-color-success, #34d19d));
  padding: 0 4px;
}
.wd-tag.is-success.is-round.data-v-14921b29 {
  line-height: 1.2;
  font-size: var(--wot-tag-fs, var(--wot-fs-secondary, 12px));
  padding: 4px 11px;
  background: transparent;
  color: var(--wot-tag-success-color, var(--wot-color-success, #34d19d));
  border: 1px solid var(--wot-tag-success-color, var(--wot-color-success, #34d19d));
  border-radius: var(--wot-tag-round-radius, 12px);
}
.wd-tag.is-success.is-mark.data-v-14921b29 {
  padding: 1px 6px;
  border-radius: var(--wot-tag-mark-radius, 6px 2px 6px 2px);
}
.wd-tag.is-success.is-mark.is-plain.data-v-14921b29 {
  padding: 0 6px;
}
.wd-tag.is-success.is-active.data-v-14921b29 {
  color: var(--wot-tag-primary-color, var(--wot-color-theme, #4d80f0));
  border-color: var(--wot-tag-primary-color, var(--wot-color-theme, #4d80f0));
}
.wd-tag.is-icon.data-v-14921b29 {
  font-size: var(--wot-tag-fs, var(--wot-fs-secondary, 12px));
  line-height: 1.2;
  padding: 2px 5px;
}
.wd-tag.is-dynamic.data-v-14921b29 {
  box-sizing: border-box;
  width: 88px;
  transition: 0.3s;
}
.wd-tag.is-dynamic.data-v-14921b29:active {
  color: var(--wot-tag-primary-color, var(--wot-color-theme, #4d80f0));
  border-color: var(--wot-tag-primary-color, var(--wot-color-theme, #4d80f0));
}
.wd-tag.is-dynamic-input.data-v-14921b29 {
  border-color: var(--wot-tag-primary-color, var(--wot-color-theme, #4d80f0));
}
.data-v-14921b29  .wd-tag__icon {
  display: inline-block;
  margin-right: 4px;
  font-size: var(--wot-tag-fs, var(--wot-fs-secondary, 12px));
  line-height: 1.2;
  vertical-align: baseline;
}
.wd-tag__text.data-v-14921b29 {
  display: inline-block;
  vertical-align: text-top;
}
.wd-tag__add-text.data-v-14921b29 {
  width: 60px;
  height: 14px;
  min-height: 14px;
  display: inline-block;
  font-size: var(--wot-tag-fs, var(--wot-fs-secondary, 12px));
  vertical-align: middle;
  padding: 0;
}
.wd-tag__close.data-v-14921b29 {
  display: inline-block;
  margin-left: 24px;
  margin-right: -4px;
  font-size: var(--wot-tag-close-size, 14px);
  height: 14px;
  line-height: 1.1;
  vertical-align: text-bottom;
  color: var(--wot-tag-close-color, var(--wot-tag-info-color, #585858));
}
.wd-tag__close.data-v-14921b29:active {
  color: var(--wot-tag-close-active-color, rgba(0, 0, 0, 0.45));
}
.data-v-14921b29  .wd-tag__add {
  vertical-align: bottom;
}