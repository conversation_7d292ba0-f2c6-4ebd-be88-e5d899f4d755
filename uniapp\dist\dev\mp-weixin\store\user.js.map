{"version": 3, "file": "user.js", "sources": ["../../../../src/store/user.ts"], "sourcesContent": ["import { defineStore } from 'pinia'\r\nimport { ref } from 'vue'\r\n\r\nconst initState = {\r\n  token: '',\r\n  userid: '',\r\n  username: '',\r\n  realname: '',\r\n  welcome: '',\r\n  avatar: '',\r\n  tenantId: 0,\r\n  phone: '',\r\n  email: '',\r\n  sex: 1,\r\n  userCategory: '', // 用户类型：0医生 1患者\r\n  roleList: [],\r\n  caseNumber: '', // 病案号，仅患者端使用\r\n  // 本地存储时间\r\n  localStorageTime: 0,\r\n}\r\n\r\nexport const useUserStore = defineStore(\r\n  'user',\r\n  () => {\r\n    const userInfo = ref<IUserInfo>({ ...initState })\r\n    const setUserInfo = (val: IUserInfo) => {\r\n      userInfo.value = val\r\n    }\r\n    const clearUserInfo = () => {\r\n      userInfo.value = { ...initState }\r\n    }\r\n    const editUserInfo = (options) => {\r\n      userInfo.value = { ...userInfo.value, ...options }\r\n    }\r\n    // 一般没有reset需求，不需要的可以删除\r\n    const reset = () => {\r\n      userInfo.value = { ...initState }\r\n    }\r\n    const isLogined = computed(() => !!userInfo.value.token)\r\n    return {\r\n      userInfo,\r\n      setUserInfo,\r\n      clearUserInfo,\r\n      isLogined,\r\n      editUserInfo,\r\n      reset,\r\n    }\r\n  },\r\n  {\r\n    // 如果需要持久化就写 true, 不需要持久化就写 false（或者去掉这个配置项）\r\n    persist: true,\r\n  },\r\n)\r\n"], "names": ["defineStore", "ref", "computed"], "mappings": ";;;;;;;;;;;;;;;;;;AAGA,MAAM,YAAY;AAAA,EAChB,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO;AAAA,EACP,OAAO;AAAA,EACP,KAAK;AAAA,EACL,cAAc;AAAA;AAAA,EACd,UAAU,CAAC;AAAA,EACX,YAAY;AAAA;AAAA;AAAA,EAEZ,kBAAkB;AACpB;AAEO,MAAM,eAAeA,cAAA;AAAA,EAC1B;AAAA,EACA,MAAM;AACJ,UAAM,WAAWC,cAAAA,IAAe,mBAAK,UAAW;AAC1C,UAAA,cAAc,CAAC,QAAmB;AACtC,eAAS,QAAQ;AAAA,IACnB;AACA,UAAM,gBAAgB,MAAM;AACjB,eAAA,QAAQ,mBAAK;AAAA,IACxB;AACM,UAAA,eAAe,CAAC,YAAY;AAChC,eAAS,QAAQ,kCAAK,SAAS,QAAU;AAAA,IAC3C;AAEA,UAAM,QAAQ,MAAM;AACT,eAAA,QAAQ,mBAAK;AAAA,IACxB;AACA,UAAM,YAAYC,cAAAA,SAAS,MAAM,CAAC,CAAC,SAAS,MAAM,KAAK;AAChD,WAAA;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA;AAAA,IAEE,SAAS;AAAA,EAAA;AAEb;;"}