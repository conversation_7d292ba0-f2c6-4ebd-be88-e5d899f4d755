"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
const utils_http = require("../../utils/http.js");
const utils_is = require("../../utils/is.js");
if (!Array) {
  const _easycom_wd_input2 = common_vendor.resolveComponent("wd-input");
  const _easycom_wd_popup2 = common_vendor.resolveComponent("wd-popup");
  (_easycom_wd_input2 + _easycom_wd_popup2)();
}
const _easycom_wd_input = () => "../../node-modules/wot-design-uni/components/wd-input/wd-input.js";
const _easycom_wd_popup = () => "../../node-modules/wot-design-uni/components/wd-popup/wd-popup.js";
if (!Math) {
  (_easycom_wd_input + DaTree + _easycom_wd_popup)();
}
const DaTree = () => "../../uni_modules/da-tree/index.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "TreeSelect",
  options: {
    styleIsolation: "shared"
  }
}), {
  __name: "TreeSelect",
  props: {
    modelValue: {
      type: [Array, String]
    },
    dict: {
      type: String,
      default: "id"
    },
    pidValue: {
      type: String,
      default: ""
    },
    pidField: {
      type: String,
      default: "pid"
    },
    hasChildField: {
      type: String,
      default: ""
    },
    condition: {
      type: String,
      default: "",
      required: false
    },
    converIsLeafVal: {
      type: Number,
      default: 1
    },
    // 是否支持多选
    multiple: {
      type: Boolean,
      default: true
    },
    hiddenNodeKey: {
      type: String,
      default: ""
    }
    // url: {
    //   type: String,
    //   default: '',
    // },
    // params: {
    //   type: Object,
    //   default: () => ({}),
    // },
  },
  emits: ["change", "update:modelValue"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const toast = common_vendor.useToast();
    const api = {
      loadTreeData: "/sys/dict/loadTreeData",
      view: "/sys/dict/loadDictItem/"
    };
    const showText = common_vendor.ref("");
    const popupShow = common_vendor.ref(false);
    const treeData = common_vendor.ref([]);
    const treeValue = common_vendor.ref([]);
    const tableName = common_vendor.ref("");
    const text = common_vendor.ref("");
    const code = common_vendor.ref("");
    const handleClick = () => {
      popupShow.value = true;
    };
    const cancel = () => {
      popupShow.value = false;
    };
    const confirm = () => {
      const titles = treeValue.value.map((item) => item.title);
      const keys = treeValue.value.map((item) => item.key).join(",");
      showText.value = titles.join(",");
      popupShow.value = false;
      emit("update:modelValue", keys);
      emit("change", keys);
    };
    const handleTreeChange = (value, record) => {
      const { originItem, checkedStatus } = record;
      const { key, title } = originItem;
      if (checkedStatus) {
        if (props.multiple) {
          treeValue.value.push({ key, title });
        } else {
          treeValue.value = [{ key, title }];
        }
      } else {
        if (props.multiple) {
          const findIndex = treeValue.value.findIndex((item) => item.key == key);
          if (findIndex != -1) {
            treeValue.value.splice(findIndex, 1);
          }
        } else {
          treeValue.value = [];
        }
      }
    };
    const transformField = (result) => {
      for (let i of result) {
        i.value = i.key;
        if (i.leaf == false) {
          i.isLeaf = false;
        } else if (i.leaf == true) {
          i.isLeaf = true;
        }
      }
    };
    const asyncLoadTreeData = ({ originItem }) => {
      return new Promise((resolve) => {
        let param = {
          pid: originItem.key,
          pidField: props.pidField,
          hasChildField: props.hasChildField,
          converIsLeafVal: props.converIsLeafVal,
          condition: props.condition,
          tableName: common_vendor.unref(tableName),
          text: common_vendor.unref(text),
          code: common_vendor.unref(code)
        };
        utils_http.http.get(api.loadTreeData, param).then((res) => {
          if (res.success) {
            const { result } = res;
            transformField(result);
            resolve(result);
          } else {
            resolve(null);
          }
        }).catch((err) => resolve(null));
      });
    };
    function loadRoot() {
      let param = {
        pid: props.pidValue,
        pidField: props.pidField,
        hasChildField: props.hasChildField,
        condition: props.condition,
        converIsLeafVal: props.converIsLeafVal,
        tableName: common_vendor.unref(tableName),
        text: common_vendor.unref(text),
        code: common_vendor.unref(code)
      };
      utils_http.http.get(api.loadTreeData, param).then((res) => {
        if (res.success) {
          const { result } = res;
          if (result && result.length > 0) {
            transformField(result);
            handleHiddenNode(result);
            treeData.value = result;
          }
        } else {
          toast.warning("自定义树组件根节点数据加载失败~");
        }
      }).catch((err) => {
        toast.warning("自定义树组件根节点数据加载失败~");
      });
    }
    function loadItemByCode() {
      let value = props.modelValue;
      if (utils_is.isArray(props.modelValue)) {
        value = value.join();
      }
      if (value === treeData.value.map((item) => item.key).join(",")) {
        return;
      }
      utils_http.http.get(`${api.view}${props.dict}`, { key: value }).then((res) => {
        if (res.success) {
          const { result = [] } = res;
          showText.value = result.join(",");
        }
      }).catch((err) => {
      });
    }
    const initDictInfo = () => {
      var _a;
      let arr = (_a = props.dict) == null ? void 0 : _a.split(",");
      tableName.value = arr[0];
      text.value = arr[1];
      code.value = arr[2];
    };
    const handleHiddenNode = (data) => {
      if (props.hiddenNodeKey && (data == null ? void 0 : data.length)) {
        for (let i = 0, len = data.length; i < len; i++) {
          const item = data[i];
          if (item.key == props.hiddenNodeKey) {
            data.splice(i, 1);
            i--;
            len--;
            return;
          }
        }
      }
    };
    const validateProp = () => {
      let mycondition = props.condition;
      return new Promise((resolve, reject) => {
        if (!mycondition) {
          resolve();
        } else {
          try {
            let test = JSON.parse(mycondition);
            if (typeof test == "object" && test) {
              resolve();
            } else {
              toast.error("组件TreeSelect-condition传值有误，需要一个json字符串!");
              reject();
            }
          } catch (e) {
            toast.error("组件TreeSelect-condition传值有误，需要一个json字符串!");
            reject();
          }
        }
      });
    };
    common_vendor.watch(
      () => props.modelValue,
      () => loadItemByCode(),
      { deep: true, immediate: true }
    );
    common_vendor.watch(
      () => props.dict,
      () => {
        initDictInfo();
        loadRoot();
      }
    );
    common_vendor.watch(
      () => props.hiddenNodeKey,
      () => {
        var _a;
        if (((_a = treeData.value) == null ? void 0 : _a.length) && props.hiddenNodeKey) {
          handleHiddenNode(treeData.value);
          treeData.value = [...treeData.value];
        }
      }
    );
    validateProp().then(() => {
      initDictInfo();
      loadRoot();
      loadItemByCode();
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(($event) => showText.value = $event),
        b: common_vendor.p(__spreadProps(__spreadValues({
          placeholder: `请选择${_ctx.$attrs.label}`
        }, _ctx.$attrs), {
          readonly: true,
          modelValue: showText.value
        })),
        c: common_vendor.o(handleClick),
        d: common_vendor.o(cancel),
        e: common_vendor.o(confirm),
        f: common_vendor.o(handleTreeChange),
        g: common_vendor.p({
          data: treeData.value,
          labelField: "title",
          valueField: "key",
          loadMode: true,
          showCheckbox: __props.multiple,
          showRadioIcon: false,
          checkStrictly: true,
          loadApi: asyncLoadTreeData
        }),
        h: common_vendor.o(($event) => popupShow.value = $event),
        i: common_vendor.p({
          position: "bottom",
          modelValue: popupShow.value
        })
      };
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-67facf17"]]);
wx.createComponent(Component);
//# sourceMappingURL=TreeSelect.js.map
