"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _easycom_wd_text2 = common_vendor.resolveComponent("wd-text");
  const _easycom_wd_cell2 = common_vendor.resolveComponent("wd-cell");
  const _easycom_wd_cell_group2 = common_vendor.resolveComponent("wd-cell-group");
  const _easycom_wd_popup2 = common_vendor.resolveComponent("wd-popup");
  (_easycom_wd_text2 + _easycom_wd_cell2 + _easycom_wd_cell_group2 + _easycom_wd_popup2)();
}
const _easycom_wd_text = () => "../../node-modules/wot-design-uni/components/wd-text/wd-text.js";
const _easycom_wd_cell = () => "../../node-modules/wot-design-uni/components/wd-cell/wd-cell.js";
const _easycom_wd_cell_group = () => "../../node-modules/wot-design-uni/components/wd-cell-group/wd-cell-group.js";
const _easycom_wd_popup = () => "../../node-modules/wot-design-uni/components/wd-popup/wd-popup.js";
if (!Math) {
  (_easycom_wd_text + _easycom_wd_cell + _easycom_wd_cell_group + _easycom_wd_popup)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "BottomOperate",
  options: {
    styleIsolation: "shared"
  }
}), {
  __name: "BottomOperate",
  props: ["title", "data", "options"],
  emits: ["change", "close"],
  setup(__props, { emit: __emit }) {
    const eimt = __emit;
    const show = common_vendor.ref(true);
    const props = __props;
    const handleClose = () => {
      show.value = false;
      setTimeout(() => {
        eimt("close");
      }, 300);
    };
    const handleClick = (item) => {
      eimt("change", { option: item, data: props.data });
      handleClose();
    };
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: __props.title
      }, __props.title ? {
        b: common_vendor.p({
          text: __props.title
        })
      } : {}, {
        c: common_vendor.f(__props.options, (item, index, i0) => {
          return {
            a: common_vendor.o(($event) => handleClick(item)),
            b: "3576d6b1-3-" + i0 + ",3576d6b1-2",
            c: common_vendor.p({
              icon: item.icon,
              label: item.label,
              ["custom-class"]: item.color,
              clickable: true
            })
          };
        }),
        d: common_vendor.p({
          border: true
        }),
        e: common_vendor.o(handleClose),
        f: common_vendor.o(($event) => show.value = $event),
        g: common_vendor.p({
          position: "bottom",
          modelValue: show.value
        })
      });
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-3576d6b1"]]);
wx.createComponent(Component);
//# sourceMappingURL=BottomOperate.js.map
