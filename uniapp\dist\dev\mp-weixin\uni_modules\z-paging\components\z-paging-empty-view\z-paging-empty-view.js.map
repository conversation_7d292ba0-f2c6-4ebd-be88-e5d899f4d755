{"version": 3, "file": "z-paging-empty-view.js", "sources": ["../../../../../../../src/uni_modules/z-paging/components/z-paging-empty-view/z-paging-empty-view.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvdW5pX21vZHVsZXMvei1wYWdpbmcvY29tcG9uZW50cy96LXBhZ2luZy1lbXB0eS12aWV3L3otcGFnaW5nLWVtcHR5LXZpZXcudnVl"], "sourcesContent": ["<!-- z-paging -->\r\n<!-- github地址:https://github.com/SmileZXLee/uni-z-paging -->\r\n<!-- dcloud地址:https://ext.dcloud.net.cn/plugin?id=3935 -->\r\n<!-- 反馈QQ群：790460711 -->\r\n\r\n<!-- 空数据占位view，此组件支持easycom规范，可以在项目中直接引用 -->\r\n<template>\r\n\t<view :class=\"{'zp-container':true,'zp-container-fixed':emptyViewFixed}\" :style=\"[finalEmptyViewStyle]\" @click=\"emptyViewClick\">\r\n\t\t<view class=\"zp-main\">\r\n\t\t\t<image v-if=\"!emptyViewImg.length\" :class=\"{'zp-main-image-rpx':unit==='rpx','zp-main-image-px':unit==='px'}\" :style=\"[emptyViewImgStyle]\" :src=\"emptyImg\" />\r\n\t\t\t<image v-else :class=\"{'zp-main-image-rpx':unit==='rpx','zp-main-image-px':unit==='px'}\" mode=\"aspectFit\" :style=\"[emptyViewImgStyle]\" :src=\"emptyViewImg\" />\r\n\t\t\t<text class=\"zp-main-title\" :class=\"{'zp-main-title-rpx':unit==='rpx','zp-main-title-px':unit==='px'}\" :style=\"[emptyViewTitleStyle]\">{{emptyViewText}}</text>\r\n\t\t\t<text v-if=\"showEmptyViewReload\" :class=\"{'zp-main-error-btn':true,'zp-main-error-btn-rpx':unit==='rpx','zp-main-error-btn-px':unit==='px'}\" :style=\"[emptyViewReloadStyle]\" @click.stop=\"reloadClick\">{{emptyViewReloadText}}</text>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport zStatic from '../z-paging/js/z-paging-static'\r\n\texport default {\r\n\t\tname: \"z-paging-empty-view\",\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t\r\n\t\t\t};\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\t// 空数据描述文字\r\n\t\t\temptyViewText: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '没有数据哦~'\r\n\t\t\t},\r\n\t\t\t// 空数据图片\r\n\t\t\temptyViewImg: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 是否显示空数据图重新加载按钮\r\n\t\t\tshowEmptyViewReload: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 空数据点击重新加载文字\r\n\t\t\temptyViewReloadText: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '重新加载'\r\n\t\t\t},\r\n\t\t\t// 是否是加载失败\r\n\t\t\tisLoadFailed: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 空数据图样式\r\n\t\t\temptyViewStyle: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault: function() {\r\n                    return {}\r\n                }\r\n\t\t\t},\r\n\t\t\t// 空数据图img样式\r\n\t\t\temptyViewImgStyle: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault: function() {\r\n\t\t\t\t    return {}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 空数据图描述文字样式\r\n\t\t\temptyViewTitleStyle: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault: function() {\r\n\t\t\t\t    return {}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 空数据图重新加载按钮样式\r\n\t\t\temptyViewReloadStyle: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault: function() {\r\n\t\t\t\t    return {}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 空数据图z-index\r\n\t\t\temptyViewZIndex: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 9\r\n\t\t\t},\r\n\t\t\t// 空数据图片是否使用fixed布局并铺满z-paging\r\n\t\t\temptyViewFixed: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 空数据图中布局的单位，默认为rpx\r\n\t\t\tunit: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'rpx'\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\temptyImg() {\r\n                return this.isLoadFailed ? zStatic.base64Error : zStatic.base64Empty;\r\n\t\t\t},\r\n\t\t\tfinalEmptyViewStyle(){\r\n\t\t\t\tthis.emptyViewStyle['z-index'] = this.emptyViewZIndex;\r\n\t\t\t\treturn this.emptyViewStyle;\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 点击了reload按钮\r\n\t\t\treloadClick() {\r\n\t\t\t\tthis.$emit('reload');\r\n\t\t\t},\r\n\t\t\t// 点击了空数据view\r\n\t\t\temptyViewClick() {\r\n\t\t\t\tthis.$emit('viewClick');\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped>\r\n\t.zp-container{\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\t.zp-container-fixed {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\t/* #endif */\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\tflex: 1;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.zp-main{\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n        padding: 50rpx 0rpx;\r\n\t}\r\n\r\n\t.zp-main-image-rpx {\r\n\t\twidth: 240rpx;\r\n\t\theight: 240rpx;\r\n\t}\r\n\t.zp-main-image-px {\r\n\t\twidth: 120px;\r\n\t\theight: 120px;\r\n\t}\r\n\r\n\t.zp-main-title {\r\n\t\tcolor: #aaaaaa;\r\n\t\ttext-align: center;\r\n\t}\r\n\t.zp-main-title-rpx {\r\n\t\tfont-size: 28rpx;\r\n\t\tmargin-top: 10rpx;\r\n\t\tpadding: 0rpx 20rpx;\r\n\t}\r\n\t.zp-main-title-px {\r\n\t\tfont-size: 14px;\r\n\t\tmargin-top: 5px;\r\n\t\tpadding: 0px 10px;\r\n\t}\r\n\r\n\t.zp-main-error-btn {\r\n\t\tborder: solid 1px #dddddd;\r\n\t\tcolor: #aaaaaa;\r\n\t}\r\n\t.zp-main-error-btn-rpx {\r\n\t\tfont-size: 28rpx;\r\n\t\tpadding: 8rpx 24rpx;\r\n\t\tborder-radius: 6rpx;\r\n\t\tmargin-top: 50rpx;\r\n\t}\r\n\t.zp-main-error-btn-px {\r\n\t\tfont-size: 14px;\r\n\t\tpadding: 4px 12px;\r\n\t\tborder-radius: 3px;\r\n\t\tmargin-top: 25px;\r\n\t}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/uni_modules/z-paging/components/z-paging-empty-view/z-paging-empty-view.vue'\nwx.createComponent(Component)"], "names": ["zStatic"], "mappings": ";;;AAmBC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,OAAO;AACN,WAAO;EAGP;AAAA,EACD,OAAO;AAAA;AAAA,IAEN,eAAe;AAAA,MACd,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,cAAc;AAAA,MACb,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,qBAAqB;AAAA,MACpB,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,qBAAqB;AAAA,MACpB,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,cAAc;AAAA,MACb,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,gBAAgB;AAAA,MACf,MAAM;AAAA,MACN,SAAS,WAAW;AACJ,eAAO,CAAC;AAAA,MACZ;AAAA,IACZ;AAAA;AAAA,IAED,mBAAmB;AAAA,MAClB,MAAM;AAAA,MACN,SAAS,WAAW;AAChB,eAAO,CAAC;AAAA,MACZ;AAAA,IACA;AAAA;AAAA,IAED,qBAAqB;AAAA,MACpB,MAAM;AAAA,MACN,SAAS,WAAW;AAChB,eAAO,CAAC;AAAA,MACZ;AAAA,IACA;AAAA;AAAA,IAED,sBAAsB;AAAA,MACrB,MAAM;AAAA,MACN,SAAS,WAAW;AAChB,eAAO,CAAC;AAAA,MACZ;AAAA,IACA;AAAA;AAAA,IAED,iBAAiB;AAAA,MAChB,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,gBAAgB;AAAA,MACf,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,MAAM;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,WAAW;AACE,aAAO,KAAK,eAAeA,wDAAAA,QAAQ,cAAcA,wDAAAA,QAAQ;AAAA,IACrE;AAAA,IACD,sBAAqB;AACpB,WAAK,eAAe,SAAS,IAAI,KAAK;AACtC,aAAO,KAAK;AAAA,IACb;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA,IAER,cAAc;AACb,WAAK,MAAM,QAAQ;AAAA,IACnB;AAAA;AAAA,IAED,iBAAiB;AAChB,WAAK,MAAM,WAAW;AAAA,IACvB;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClHD,GAAG,gBAAgB,SAAS;"}