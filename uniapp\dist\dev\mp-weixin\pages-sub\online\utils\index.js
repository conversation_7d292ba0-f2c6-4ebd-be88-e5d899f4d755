"use strict";
const common_uitls = require("../../../common/uitls.js");
function getFormatDate(text, column) {
  if (!text) {
    return "";
  }
  let a = text;
  if (a.length > 10) {
    a = a.substring(0, 10);
  }
  let fieldExtendJson = column == null ? void 0 : column.fieldExtendJson;
  if (fieldExtendJson) {
    fieldExtendJson = JSON.parse(fieldExtendJson);
    if (fieldExtendJson.picker && fieldExtendJson.picker != "default") {
      const result = common_uitls.getWeekMonthQuarterYear(a);
      return result[fieldExtendJson.picker];
    }
  }
  return a;
}
function filterMultiDictText(dictOptions, text) {
  if (text === 0 || text === "0") {
    if (dictOptions) {
      for (let dictItem of dictOptions) {
        if (text == dictItem.value) {
          return dictItem.text;
        }
      }
    }
  }
  if (!text || text == "undefined" || text == "null" || !dictOptions || dictOptions.length == 0) {
    return "";
  }
  let re = "";
  text = text.toString();
  let arr = text.split(",");
  dictOptions.forEach(function(option) {
    if (option) {
      for (let i = 0; i < arr.length; i++) {
        if (arr[i] === option.value) {
          re += option.text + ",";
          break;
        }
      }
    }
  });
  if (re == "") {
    return text;
  }
  return re.substring(0, re.length - 1);
}
exports.filterMultiDictText = filterMultiDictText;
exports.getFormatDate = getFormatDate;
//# sourceMappingURL=index.js.map
