"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
const utils_http = require("../../utils/http.js");
const utils_is = require("../../utils/is.js");
const common_uitls = require("../../common/uitls.js");
if (!Array) {
  const _easycom_wd_input2 = common_vendor.resolveComponent("wd-input");
  const _easycom_wd_popup2 = common_vendor.resolveComponent("wd-popup");
  (_easycom_wd_input2 + _easycom_wd_popup2)();
}
const _easycom_wd_input = () => "../../node-modules/wot-design-uni/components/wd-input/wd-input.js";
const _easycom_wd_popup = () => "../../node-modules/wot-design-uni/components/wd-popup/wd-popup.js";
if (!Math) {
  (_easycom_wd_input + DaTree + _easycom_wd_popup)();
}
const DaTree = () => "../../uni_modules/da-tree/index.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "SelectDept"
}), {
  __name: "SelectDept",
  props: {
    modelValue: {
      type: [Array, String]
    },
    // 是否支持多选
    multiple: {
      type: Boolean,
      default: true
    },
    rowKey: {
      type: String,
      default: "key"
    },
    labelKey: {
      type: String,
      default: "title"
    }
  },
  emits: ["change", "update:modelValue"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    common_vendor.useToast();
    const api = {
      queryDepartTreeSync: "/sys/sysDepart/queryDepartTreeSync"
    };
    const showText = common_vendor.ref("");
    const popupShow = common_vendor.ref(false);
    const treeData = common_vendor.ref([]);
    const treeValue = common_vendor.ref([]);
    const handleClick = () => {
      popupShow.value = true;
    };
    const cancel = () => {
      popupShow.value = false;
    };
    const confirm = () => {
      const titles = treeValue.value.map((item) => item.title);
      const keys = treeValue.value.map((item) => item.key).join(",");
      showText.value = titles.join(",");
      popupShow.value = false;
      emit("update:modelValue", keys);
      emit("change", keys);
    };
    const handleTreeChange = (value, record) => {
      const { originItem, checkedStatus } = record;
      if (checkedStatus) {
        if (props.multiple) {
          treeValue.value.push({ key: originItem[props.rowKey], title: originItem[props.labelKey] });
        } else {
          treeValue.value = [{ key: originItem[props.rowKey], title: originItem[props.labelKey] }];
        }
      } else {
        if (props.multiple) {
          const findIndex = treeValue.value.findIndex(
            (item) => item[props.rowKey] == originItem[props.rowKey]
          );
          if (findIndex != -1) {
            treeValue.value.splice(findIndex, 1);
          }
        } else {
          treeValue.value = [];
        }
      }
    };
    const transformField = (result) => {
      for (let i of result) {
        i.value = i.key;
        if (i.isLeaf == false) {
          i.leaf = false;
        } else if (i.isLeaf == true) {
          i.leaf = true;
        }
      }
    };
    const asyncLoadTreeData = ({ originItem }) => {
      return new Promise((resolve) => {
        let param = {
          pid: originItem.key,
          primaryKey: props.rowKey
        };
        utils_http.http.get(api.queryDepartTreeSync, param).then((res) => {
          if (res.success) {
            const { result } = res;
            transformField(result);
            resolve(result);
          } else {
            resolve(null);
          }
        }).catch((err) => resolve(null));
      });
    };
    function loadRoot() {
      let param = {
        primaryKey: props.rowKey
      };
      utils_http.http.get(api.queryDepartTreeSync, param).then((res) => {
        if (res.success) {
          const { result } = res;
          if (result && result.length > 0) {
            transformField(result);
            treeData.value = result;
          }
        } else {
          console.error("部门组件加载根节点数据失败~");
        }
      }).catch((err) => {
        console.error("部门组件加载根节点数据失败~");
      });
    }
    function loadItemByCode() {
      let value = props.modelValue;
      console.log("部门组件翻译props.modelValue", props.modelValue);
      if (utils_is.isArray(props.modelValue)) {
        value = value.join(",");
      }
      if (value === treeData.value.map((item) => item.key).join(",")) {
        return;
      }
      utils_http.http.get(api.queryDepartTreeSync, { ids: value }).then((res) => {
        if (res.success) {
          const { result = [] } = res;
          showText.value = result.map((item) => item[props.labelKey]).join(",");
        }
      }).catch((err) => {
      });
    }
    common_vendor.watch(
      () => props.modelValue,
      () => {
        loadItemByCode();
      },
      { deep: true, immediate: true }
    );
    common_vendor.watch(
      () => props.pcode,
      () => {
        loadRoot();
      },
      { deep: true, immediate: true }
    );
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(($event) => showText.value = $event),
        b: common_vendor.p(__spreadProps(__spreadValues({
          placeholder: common_vendor.unref(common_uitls.getPlaceholder)(_ctx.$attrs)
        }, _ctx.$attrs), {
          clearable: true,
          readonly: true,
          modelValue: showText.value
        })),
        c: common_vendor.o(handleClick),
        d: common_vendor.o(cancel),
        e: common_vendor.o(confirm),
        f: common_vendor.o(handleTreeChange),
        g: common_vendor.p({
          data: treeData.value,
          labelField: __props.labelKey,
          valueField: __props.rowKey,
          loadMode: true,
          showCheckbox: __props.multiple,
          showRadioIcon: false,
          checkStrictly: true,
          loadApi: asyncLoadTreeData
        }),
        h: common_vendor.o(($event) => popupShow.value = $event),
        i: common_vendor.p({
          position: "bottom",
          modelValue: popupShow.value
        })
      };
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-db30ad64"]]);
wx.createComponent(Component);
//# sourceMappingURL=SelectDept.js.map
