"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../common/vendor.js");
const utils_http = require("../../../utils/http.js");
const utils_is = require("../../../utils/is.js");
const common_uitls = require("../../../common/uitls.js");
const common_assets = require("../../../common/assets.js");
if (!Array) {
  const _easycom_wd_search2 = common_vendor.resolveComponent("wd-search");
  const _easycom_wd_img2 = common_vendor.resolveComponent("wd-img");
  const _easycom_wd_checkbox2 = common_vendor.resolveComponent("wd-checkbox");
  const _easycom_wd_checkbox_group2 = common_vendor.resolveComponent("wd-checkbox-group");
  const _easycom_wd_radio2 = common_vendor.resolveComponent("wd-radio");
  const _easycom_wd_cell2 = common_vendor.resolveComponent("wd-cell");
  const _easycom_wd_radio_group2 = common_vendor.resolveComponent("wd-radio-group");
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _easycom_wd_popup2 = common_vendor.resolveComponent("wd-popup");
  (_easycom_wd_search2 + _easycom_wd_img2 + _easycom_wd_checkbox2 + _easycom_wd_checkbox_group2 + _easycom_wd_radio2 + _easycom_wd_cell2 + _easycom_wd_radio_group2 + _easycom_z_paging2 + _easycom_PageLayout2 + _easycom_wd_popup2)();
}
const _easycom_wd_search = () => "../../../node-modules/wot-design-uni/components/wd-search/wd-search.js";
const _easycom_wd_img = () => "../../../node-modules/wot-design-uni/components/wd-img/wd-img.js";
const _easycom_wd_checkbox = () => "../../../node-modules/wot-design-uni/components/wd-checkbox/wd-checkbox.js";
const _easycom_wd_checkbox_group = () => "../../../node-modules/wot-design-uni/components/wd-checkbox-group/wd-checkbox-group.js";
const _easycom_wd_radio = () => "../../../node-modules/wot-design-uni/components/wd-radio/wd-radio.js";
const _easycom_wd_cell = () => "../../../node-modules/wot-design-uni/components/wd-cell/wd-cell.js";
const _easycom_wd_radio_group = () => "../../../node-modules/wot-design-uni/components/wd-radio-group/wd-radio-group.js";
const _easycom_z_paging = () => "../../../uni_modules/z-paging/components/z-paging/z-paging.js";
const _easycom_PageLayout = () => "../../PageLayout/PageLayout.js";
const _easycom_wd_popup = () => "../../../node-modules/wot-design-uni/components/wd-popup/wd-popup.js";
if (!Math) {
  (_easycom_wd_search + _easycom_wd_img + _easycom_wd_checkbox + _easycom_wd_checkbox_group + _easycom_wd_radio + _easycom_wd_cell + _easycom_wd_radio_group + _easycom_z_paging + _easycom_PageLayout + _easycom_wd_popup)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "SelectUserModal",
  options: {
    styleIsolation: "shared"
  }
}), {
  __name: "SelectUserModal",
  props: {
    multi: {
      type: Boolean,
      default: true
    },
    modalTitle: {
      type: String,
      default: "选择用户"
    },
    maxSelectCount: {
      type: Number
    },
    selected: {
      type: [Array, String],
      default: ""
    }
  },
  emits: ["change", "close"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const toast = common_vendor.useToast();
    const show = common_vendor.ref(true);
    const api = {
      userlist: "/sys/user/list"
    };
    const paging = common_vendor.ref(null);
    const dataList = common_vendor.ref([]);
    const checkedValue = common_vendor.ref(props.multi ? [] : "");
    const checkboxRef = common_vendor.ref(null);
    const search = common_vendor.reactive({
      keyword: "",
      placeholder: "输入姓名可搜索",
      field: "realname"
    });
    const handleClose = () => {
      setTimeout(() => {
        emit("close");
      }, 400);
    };
    const handleConfirm = () => {
      if (checkedValue.value.length == 0) {
        toast.warning("还没选择~");
        return;
      }
      const result = [];
      let value = checkedValue.value;
      if (!Array.isArray(checkedValue.value)) {
        value = [checkedValue.value];
      }
      value.forEach((username, index) => {
        const findIndex = dataList.value.findIndex((item) => item["username"] === username);
        result.push(dataList.value[findIndex]);
      });
      show.value = false;
      emit("change", result);
      handleClose();
    };
    const handleCancel = () => {
      show.value = false;
      handleClose();
      console.log("取消了~");
    };
    function handleSearch() {
      paging.value.reload();
    }
    function handleClear() {
      search.keyword = "";
      handleSearch();
    }
    const hanldeCheck = (index, username) => {
      if (props.multi) {
        if (Array.isArray(checkboxRef.value)) {
          checkboxRef.value[index].toggle();
          common_vendor.nextTick$1(() => {
            if (props.maxSelectCount) {
              if (checkedValue.value.length > props.maxSelectCount) {
                toast.warning(`最多可选择${props.maxSelectCount}个用户`);
                checkboxRef.value[index].toggle();
              }
            }
          });
        }
      } else {
        checkedValue.value = username;
      }
    };
    const getAvatar = (url) => {
      let result = common_uitls.getFileAccessHttpUrl(url);
      if (result.length) {
        return result;
      } else {
        return common_assets.defaultAvatar;
      }
    };
    const queryList = (pageNo, pageSize) => {
      const pararms = { pageNo, pageSize, column: "createTime", order: "desc" };
      if (search.keyword) {
        pararms[search.field] = `*${search.keyword}*`;
      }
      utils_http.http.get(`${api.userlist}`, pararms).then((res) => {
        var _a;
        if (res.success && res.result.records) {
          paging.value.complete((_a = res.result.records) != null ? _a : []);
        } else {
          paging.value.complete(false);
        }
      }).catch((err) => {
      });
    };
    const init = () => {
      if (props.selected.length) {
        if (props.multi) {
          if (utils_is.isArray(props.selected)) {
            checkedValue.value = props.selected;
          } else if (utils_is.isString(props.selected)) {
            checkedValue.value = props.selected.split(",");
          }
        } else {
          if (utils_is.isString(props.selected)) {
            checkedValue.value = props.selected;
          } else if (utils_is.isArray(props.selected)) {
            checkedValue.value = props.selected.join(",");
          }
        }
      }
    };
    init();
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.o(handleSearch),
        b: common_vendor.o(handleClear),
        c: common_vendor.o(($event) => search.keyword = $event),
        d: common_vendor.p({
          ["hide-cancel"]: true,
          placeholder: search.placeholder,
          modelValue: search.keyword
        }),
        e: __props.multi
      }, __props.multi ? {
        f: common_vendor.f(dataList.value, (item, index, i0) => {
          return {
            a: "a2802460-5-" + i0 + ",a2802460-4",
            b: common_vendor.p({
              ["custom-class"]: "avatar",
              radius: "50%",
              height: "40",
              width: "40",
              src: getAvatar(item.avatar)
            }),
            c: common_vendor.t(item.username),
            d: common_vendor.t(item.realname),
            e: common_vendor.sr(checkboxRef, "a2802460-6-" + i0 + ",a2802460-4", {
              "k": "checkboxRef",
              "f": 1
            }),
            f: "a2802460-6-" + i0 + ",a2802460-4",
            g: common_vendor.p({
              modelValue: item.username
            }),
            h: common_vendor.o(() => {
            }, index),
            i: common_vendor.o(($event) => hanldeCheck(index, item.username), index),
            j: index
          };
        }),
        g: common_vendor.o(($event) => checkedValue.value = $event),
        h: common_vendor.p({
          shape: "square",
          modelValue: checkedValue.value
        })
      } : {
        i: common_vendor.f(dataList.value, (item, index, i0) => {
          return {
            a: "a2802460-9-" + i0 + "," + ("a2802460-8-" + i0),
            b: common_vendor.p({
              ["custom-class"]: "avatar",
              radius: "50%",
              height: "40",
              width: "40",
              src: getAvatar(item.avatar)
            }),
            c: common_vendor.t(item.username),
            d: common_vendor.t(item.realname),
            e: "a2802460-10-" + i0 + "," + ("a2802460-8-" + i0),
            f: common_vendor.p({
              value: item.username
            }),
            g: common_vendor.o(() => {
            }, index),
            h: common_vendor.o(($event) => hanldeCheck(index, item.username), index),
            i: "a2802460-8-" + i0 + ",a2802460-7",
            j: index
          };
        }),
        j: common_vendor.o(($event) => checkedValue.value = $event),
        k: common_vendor.p({
          shape: "dot",
          modelValue: checkedValue.value
        })
      }, {
        l: common_vendor.sr(paging, "a2802460-2,a2802460-1", {
          "k": "paging"
        }),
        m: common_vendor.o(queryList),
        n: common_vendor.o(($event) => dataList.value = $event),
        o: common_vendor.p({
          fixed: false,
          ["default-page-size"]: 15,
          modelValue: dataList.value
        }),
        p: common_vendor.o(handleConfirm),
        q: common_vendor.o(handleCancel),
        r: common_vendor.p({
          navTitle: __props.modalTitle,
          type: "popup",
          navRightText: "确定"
        }),
        s: common_vendor.o(($event) => show.value = $event),
        t: common_vendor.p({
          position: "bottom",
          modelValue: show.value
        })
      });
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-a2802460"]]);
wx.createComponent(Component);
//# sourceMappingURL=SelectUserModal.js.map
