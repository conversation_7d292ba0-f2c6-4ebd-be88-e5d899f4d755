"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../../common/vendor.js");
const __default__ = {
  name: "wd-swipe-action",
  options: {
    addGlobalClass: true,
    virtualHost: true,
    styleIsolation: "shared"
  }
};
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, __default__), {
  props: common_vendor.swipeActionProps,
  emits: ["click", "update:modelValue"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const queue = common_vendor.inject(common_vendor.queueKey, null);
    const wrapperStyle = common_vendor.ref("");
    const originOffset = common_vendor.ref(0);
    const wrapperOffset = common_vendor.ref(0);
    const touching = common_vendor.ref(false);
    const touch = common_vendor.useTouch();
    const { proxy } = common_vendor.getCurrentInstance();
    common_vendor.watch(
      () => props.modelValue,
      (value, old) => {
        changeState(value, old);
      },
      {
        deep: true
      }
    );
    common_vendor.onBeforeMount(() => {
      if (queue && queue.pushToQueue) {
        queue.pushToQueue(proxy);
      } else {
        common_vendor.pushToQueue(proxy);
      }
      originOffset.value = 0;
      wrapperOffset.value = 0;
      touching.value = false;
    });
    common_vendor.onMounted(() => {
      touching.value = true;
      changeState(props.modelValue);
      touching.value = false;
    });
    common_vendor.onBeforeUnmount(() => {
      if (queue && queue.removeFromQueue) {
        queue.removeFromQueue(proxy);
      } else {
        common_vendor.removeFromQueue(proxy);
      }
    });
    function changeState(value, old) {
      if (props.disabled) {
        return;
      }
      getWidths().then(([leftWidth, rightWidth]) => {
        switch (value) {
          case "close":
            if (wrapperOffset.value === 0)
              return;
            close("value", old);
            break;
          case "left":
            swipeMove(leftWidth);
            break;
          case "right":
            swipeMove(-rightWidth);
            break;
        }
      });
    }
    function getWidths() {
      return Promise.all([
        common_vendor.getRect(".wd-swipe-action__left", false, proxy).then((rect) => {
          return rect.width ? rect.width : 0;
        }),
        common_vendor.getRect(".wd-swipe-action__right", false, proxy).then((rect) => {
          return rect.width ? rect.width : 0;
        })
      ]);
    }
    function swipeMove(offset = 0) {
      const transform = `translate3d(${offset}px, 0, 0)`;
      const transition = touching.value ? "none" : ".6s cubic-bezier(0.18, 0.89, 0.32, 1)";
      wrapperStyle.value = `
        -webkit-transform: ${transform};
        -webkit-transition: ${transition};
        transform: ${transform};
        transition: ${transition};
      `;
      wrapperOffset.value = offset;
    }
    function onClick(position) {
      if (props.disabled || wrapperOffset.value === 0) {
        return;
      }
      position = position || "inside";
      close("click", position);
      emit("click", {
        value: position
      });
    }
    function startDrag(event) {
      if (props.disabled)
        return;
      originOffset.value = wrapperOffset.value;
      touch.touchStart(event);
      if (queue && queue.closeOther) {
        queue.closeOther(proxy);
      } else {
        common_vendor.closeOther(proxy);
      }
    }
    function onDrag(event) {
      if (props.disabled)
        return;
      touch.touchMove(event);
      if (touch.direction.value === "vertical") {
        return;
      } else {
        event.preventDefault();
        event.stopPropagation();
      }
      touching.value = true;
      const offset = originOffset.value + touch.deltaX.value;
      getWidths().then(([leftWidth, rightWidth]) => {
        if (leftWidth === 0 && offset > 0 || rightWidth === 0 && offset < 0) {
          swipeMove(0);
          return startDrag(event);
        }
        if (leftWidth !== 0 && offset >= leftWidth) {
          swipeMove(leftWidth);
          return startDrag(event);
        } else if (rightWidth !== 0 && -offset >= rightWidth) {
          swipeMove(-rightWidth);
          return startDrag(event);
        }
        swipeMove(offset);
      });
    }
    function endDrag() {
      if (props.disabled)
        return;
      const THRESHOLD = 0.3;
      touching.value = false;
      getWidths().then(([leftWidth, rightWidth]) => {
        if (originOffset.value < 0 && // 之前展示的是右按钮
        wrapperOffset.value < 0 && // 目前仍然是右按钮
        wrapperOffset.value - originOffset.value < rightWidth * THRESHOLD) {
          swipeMove(-rightWidth);
          emit("update:modelValue", "right");
        } else if (originOffset.value > 0 && // 之前展示的是左按钮
        wrapperOffset.value > 0 && // 现在仍然是左按钮
        originOffset.value - wrapperOffset.value < leftWidth * THRESHOLD) {
          swipeMove(leftWidth);
          emit("update:modelValue", "left");
        } else if (rightWidth > 0 && originOffset.value >= 0 && // 之前是初始状态或者展示左按钮显
        wrapperOffset.value < 0 && // 现在展示右按钮
        Math.abs(wrapperOffset.value) > rightWidth * THRESHOLD) {
          swipeMove(-rightWidth);
          emit("update:modelValue", "right");
        } else if (leftWidth > 0 && originOffset.value <= 0 && // 之前初始状态或者右按钮显示
        wrapperOffset.value > 0 && // 现在左按钮
        Math.abs(wrapperOffset.value) > leftWidth * THRESHOLD) {
          swipeMove(leftWidth);
          emit("update:modelValue", "left");
        } else {
          close("swipe");
        }
      });
    }
    function close(reason, position) {
      if (reason === "swipe" && originOffset.value === 0) {
        return swipeMove(0);
      } else if (reason === "swipe" && originOffset.value > 0) {
        position = "left";
      } else if (reason === "swipe" && originOffset.value < 0) {
        position = "right";
      }
      if (reason && position) {
        props.beforeClose && props.beforeClose(reason, position);
      }
      swipeMove(0);
      if (props.modelValue !== "close") {
        emit("update:modelValue", "close");
      }
    }
    __expose({ close });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(($event) => onClick("left")),
        b: common_vendor.o(($event) => onClick("right")),
        c: common_vendor.s(wrapperStyle.value),
        d: common_vendor.n(`wd-swipe-action ${_ctx.customClass}`),
        e: common_vendor.s(_ctx.customStyle),
        f: common_vendor.o(($event) => onClick()),
        g: common_vendor.o(startDrag),
        h: common_vendor.o(onDrag),
        i: common_vendor.o(endDrag),
        j: common_vendor.o(endDrag)
      };
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-81d4f698"]]);
wx.createComponent(Component);
//# sourceMappingURL=wd-swipe-action.js.map
