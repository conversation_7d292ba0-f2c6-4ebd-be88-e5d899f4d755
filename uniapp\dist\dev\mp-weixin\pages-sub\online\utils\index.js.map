{"version": 3, "file": "index.js", "sources": ["../../../../../../src/pages-sub/online/utils/index.ts"], "sourcesContent": ["import { getWeekMonthQuarterYear } from '@/common/uitls'\r\n\r\n/**\r\n * 日期格式化\r\n * @param text\r\n */\r\nexport function getFormatDate(text, column) {\r\n  if (!text) {\r\n    return ''\r\n  }\r\n  let a = text\r\n  if (a.length > 10) {\r\n    a = a.substring(0, 10)\r\n  }\r\n  let fieldExtendJson = column?.fieldExtendJson\r\n  if (fieldExtendJson) {\r\n    fieldExtendJson = JSON.parse(fieldExtendJson)\r\n    if (fieldExtendJson.picker && fieldExtendJson.picker != 'default') {\r\n      const result = getWeekMonthQuarterYear(a)\r\n      return result[fieldExtendJson.picker]\r\n    }\r\n  }\r\n  return a\r\n}\r\n\r\n/**\r\n * 字典值替换文本通用方法(多选)\r\n * @param dictOptions  字典数组\r\n * @param text  字典值\r\n * @return String\r\n */\r\nexport function filterMultiDictText(dictOptions, text) {\r\n  //js “!text” 认为0为空，所以做提前处理\r\n  if (text === 0 || text === '0') {\r\n    if (dictOptions) {\r\n      for (let dictItem of dictOptions) {\r\n        if (text == dictItem.value) {\r\n          return dictItem.text\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  if (!text || text == 'undefined' || text == 'null' || !dictOptions || dictOptions.length == 0) {\r\n    return ''\r\n  }\r\n  let re = ''\r\n  text = text.toString()\r\n  let arr = text.split(',')\r\n  dictOptions.forEach(function (option) {\r\n    if (option) {\r\n      for (let i = 0; i < arr.length; i++) {\r\n        if (arr[i] === option.value) {\r\n          re += option.text + ','\r\n          break\r\n        }\r\n      }\r\n    }\r\n  })\r\n  if (re == '') {\r\n    return text\r\n  }\r\n  return re.substring(0, re.length - 1)\r\n}\r\n"], "names": ["getWeekMonthQuarterYear"], "mappings": ";;AAMgB,SAAA,cAAc,MAAM,QAAQ;AAC1C,MAAI,CAAC,MAAM;AACF,WAAA;AAAA,EAAA;AAET,MAAI,IAAI;AACJ,MAAA,EAAE,SAAS,IAAI;AACb,QAAA,EAAE,UAAU,GAAG,EAAE;AAAA,EAAA;AAEvB,MAAI,kBAAkB,iCAAQ;AAC9B,MAAI,iBAAiB;AACD,sBAAA,KAAK,MAAM,eAAe;AAC5C,QAAI,gBAAgB,UAAU,gBAAgB,UAAU,WAAW;AAC3D,YAAA,SAASA,qCAAwB,CAAC;AACjC,aAAA,OAAO,gBAAgB,MAAM;AAAA,IAAA;AAAA,EACtC;AAEK,SAAA;AACT;AAQgB,SAAA,oBAAoB,aAAa,MAAM;AAEjD,MAAA,SAAS,KAAK,SAAS,KAAK;AAC9B,QAAI,aAAa;AACf,eAAS,YAAY,aAAa;AAC5B,YAAA,QAAQ,SAAS,OAAO;AAC1B,iBAAO,SAAS;AAAA,QAAA;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AAGE,MAAA,CAAC,QAAQ,QAAQ,eAAe,QAAQ,UAAU,CAAC,eAAe,YAAY,UAAU,GAAG;AACtF,WAAA;AAAA,EAAA;AAET,MAAI,KAAK;AACT,SAAO,KAAK,SAAS;AACjB,MAAA,MAAM,KAAK,MAAM,GAAG;AACZ,cAAA,QAAQ,SAAU,QAAQ;AACpC,QAAI,QAAQ;AACV,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,YAAI,IAAI,CAAC,MAAM,OAAO,OAAO;AAC3B,gBAAM,OAAO,OAAO;AACpB;AAAA,QAAA;AAAA,MACF;AAAA,IACF;AAAA,EACF,CACD;AACD,MAAI,MAAM,IAAI;AACL,WAAA;AAAA,EAAA;AAET,SAAO,GAAG,UAAU,GAAG,GAAG,SAAS,CAAC;AACtC;;;"}