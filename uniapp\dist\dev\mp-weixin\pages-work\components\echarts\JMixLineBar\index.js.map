{"version": 3, "file": "index.js", "sources": ["../../../../../../../src/pages-work/components/echarts/JMixLineBar/index.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvcGFnZXMtd29yay9jb21wb25lbnRzL2VjaGFydHMvSk1peExpbmVCYXIvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"content\">\r\n    <statusTip v-if=\"pageTips.show\" :status=\"pageTips.status\"></statusTip>\r\n\t<echartsUniapp v-else :option=\"option\"></echartsUniapp>\r\n  </view>\r\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { echartProps } from '@/pages-work/components/echarts/props';\r\nimport {deepMerge, handleTotalAndUnit, disposeGridLayout, getCustomColor} from '../../common/echartUtil';\r\nimport { isNumber } from '@/utils/is';\r\nimport useChartHook from '@/pages-work/components/hooks/useEchart';\r\nimport { deepClone } from '@/uni_modules/da-tree/utils';\r\nimport echartsUniapp from '@/pages-work/components/echarts/index.vue';\r\nimport statusTip from '@/pages-work/components/statusTip.vue';\r\nimport {merge, pull} from \"lodash-es\";\r\n//组件传参\r\nconst props = defineProps({\r\n\t...echartProps\r\n})\r\n\r\n//最终图表配置项\r\nconst option = ref({});\r\n//获取默认配置\r\nlet chartOption: any = {\r\n  title: {\r\n    show: true,\r\n  },\r\n  legend: {\r\n    show: true,\r\n    data: [],\r\n  },\r\n  xAxis: {\r\n    type: 'category',\r\n    axisLabel:{\r\n      formatter:function(value, index){\r\n        return value;\r\n      }\r\n    }\r\n  },\r\n  yAxis: {\r\n    type: 'value',\r\n    nameTextStyle: {\r\n      align:\"right\"\r\n    },\r\n    axisLabel:{\r\n      formatter:function(value, index){\r\n        return value;\r\n      }\r\n    },\r\n    axisLine: {\r\n      show: true\r\n    }\r\n  },\r\n  series: [],\r\n  dataset: {\r\n    dimensions: [],\r\n    source: [],\r\n  },\r\n};\r\n//图表数据查询\r\nlet [{ dataSource, reload, pageTips, config }, { queryData }] = useChartHook(\r\n  props,\r\n  initOption\r\n)\r\n\r\n\r\n//初始化图表配置项\r\nfunction initOption(data) {\r\n  let chartData: any = dataSource.value\r\n  if (typeof chartData === 'string') {\r\n    chartData = JSON.parse(chartData)\r\n  }\r\n  initSeriesType(chartData);\r\n  if (chartData && chartData.length > 0) {\r\n    //1.将{name，type，value}转换成dataset\r\n    const colors = getCustomColor(config.option?.customColor);\r\n    let configOption = props.config.option;\r\n    let dataset = getDataSet(chartData);\r\n    chartOption.dataset = dataset;\r\n    chartOption.series = [];\r\n    dataset.dimensions.forEach((series, index) => {\r\n      if (index > 0) {\r\n        let seriesType = props.config.seriesType.filter((item) => item.series == series);\r\n        chartOption.series.push({\r\n          type: seriesType && seriesType.length > 0 ? seriesType[0]['type'] : 'bar',\r\n          color: colors[index-1]?.color?colors[index-1]?.color:\"\",\r\n          series: series,\r\n        });\r\n      }\r\n    });\r\n    //update-begin-author:liusq---date:20230517--for: 图表切换报错，因为type类型图表，字段未设置，导致echart报错，所有包含type图表类似 ---\r\n      chartOption.legend.data = chartOption.series.map((item) => item.series).filter(type=>type);\r\n    //update-end-author:liusq---date:20230517--for: 图表切换报错，因为type类型图表，字段未设置，导致echart报错，所有包含type图表类似 ---\r\n    //2.类目轴和数值轴赋值\r\n    chartOption.yAxis.type = pull(['value', 'category'], configOption?.xAxis?.type)[0];\r\n    if (chartOption.yAxis.type == 'category') {\r\n      chartOption.yAxis.average = dataset.average;\r\n    } else {\r\n      chartOption.xAxis.average = dataset.average;\r\n    }\r\n    // 合并配置\r\n    if (props.config && config.option) {\r\n      merge(chartOption, config.option)\r\n      chartOption = handleTotalAndUnit(props.compName, chartOption, config, chartData)\r\n      chartOption = disposeGridLayout(props.compName, chartOption, config, chartData)\r\n\t\t  option.value = deepClone(chartOption)\r\n\t\t  pageTips.show = false\r\n    }\r\n  } else {\r\n    pageTips.status = 1\r\n    pageTips.show = true\r\n  }\r\n}\r\n/**\r\n * 获取图表系列，设置图例类型\r\n * @param chartData\r\n */\r\nfunction initSeriesType(chartData) {\r\n  //获取数据系列\r\n  //@ts-ignore\r\n  let seriesArr = [...new Set(chartData.map((item) => item['type']))];\r\n  //当前配置项的数据系列\r\n  let configSeriesArr = props.config.seriesType || [];\r\n  //@ts-ignore\r\n  let oldSeriesArr = [...new Set(configSeriesArr.map((item) => item['series']))];\r\n  //判断是否相等，不相等才赋新值\r\n  if (!isArrayEqual(seriesArr, oldSeriesArr)) {\r\n    let newSeriesType = seriesArr.map((series) => {\r\n      return { series, type: 'bar' };\r\n    });\r\n    props.config.seriesType = newSeriesType;\r\n  }\r\n}\r\n/**\r\n * 计算获取dataset\r\n */\r\nfunction getDataSet(chartData) {\r\n  let dataObj = { dimensions: [], source: [],average:0  };\r\n  let dataList = [];\r\n  //获取系列\r\n  //@ts-ignore\r\n  let dimensions = ['stack', ...new Set(chartData.map((item) => item['type']))];\r\n  //获取name集合\r\n  //@ts-ignore\r\n  let nameArr = [...new Set(chartData.map((item) => item['name']))];\r\n  //遍历name获取value\r\n  nameArr.forEach((name) => {\r\n    //筛选出指定name的对象集合\r\n    let arr = chartData.filter((item) => item['name'] == name);\r\n    //获取对象集合的value\r\n    let valueList = arr.map((item) => item['value']);\r\n    //首位置存放的是当前name\r\n    valueList.unshift(name);\r\n    dataList.push(valueList);\r\n  });\r\n  dataObj.dimensions = dimensions;\r\n  dataObj.source = dataList;\r\n  let allValue = chartData.filter(chart=>chart.value>0).map((item) => item['value']);\r\n  dataObj.average =  allValue.length>0?allValue.reduce((a, b) => a + b) / allValue.length:0;\r\n  return dataObj;\r\n}\r\n\r\n/**\r\n * 判断两个数组是否相等\r\n * @param arr1\r\n * @param arr2\r\n */\r\nfunction isArrayEqual(arr1, arr2) {\r\n  const a1 = arr1.map((i) => i);\r\n  let a2 = arr2.map((i) => i);\r\n  let tempArr = [];\r\n  if (a1.length !== a2.length) {\r\n    return false;\r\n  } else {\r\n    for (let i = 0; i < a1.length; i++) {\r\n      if (a2.indexOf(a1[i]) !== -1) {\r\n        a2.splice(a2.indexOf(a1[i]), 1);\r\n        tempArr.push(a1[i]);\r\n      } else {\r\n        tempArr = [];\r\n        break;\r\n      }\r\n    }\r\n    return tempArr.length === arr2.length;\r\n  }\r\n}\r\nonMounted(()=>{\r\n\tqueryData();\r\n})\r\n\r\n</script>\r\n<style>\r\n.content {\r\n  padding: 10px;\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-work/components/echarts/JMixLineBar/index.vue'\nwx.createComponent(Component)"], "names": ["ref", "useChartHook", "getCustomColor", "_a", "_b", "pull", "merge", "handleTotalAndUnit", "disposeGridLayout", "deepClone", "onMounted", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAaA,MAAA,gBAA0B,MAAA;AAC1B,MAAA,YAAsB,MAAA;;;;;AAGtB,UAAM,QAAQ;AAKR,UAAA,SAASA,cAAI,IAAA,EAAE;AAErB,QAAI,cAAmB;AAAA,MACrB,OAAO;AAAA,QACL,MAAM;AAAA,MACR;AAAA,MACA,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,MAAM,CAAA;AAAA,MACR;AAAA,MACA,OAAO;AAAA,QACL,MAAM;AAAA,QACN,WAAU;AAAA,UACR,WAAU,SAAS,OAAO,OAAM;AACvB,mBAAA;AAAA,UAAA;AAAA,QACT;AAAA,MAEJ;AAAA,MACA,OAAO;AAAA,QACL,MAAM;AAAA,QACN,eAAe;AAAA,UACb,OAAM;AAAA,QACR;AAAA,QACA,WAAU;AAAA,UACR,WAAU,SAAS,OAAO,OAAM;AACvB,mBAAA;AAAA,UAAA;AAAA,QAEX;AAAA,QACA,UAAU;AAAA,UACR,MAAM;AAAA,QAAA;AAAA,MAEV;AAAA,MACA,QAAQ,CAAC;AAAA,MACT,SAAS;AAAA,QACP,YAAY,CAAC;AAAA,QACb,QAAQ,CAAA;AAAA,MAAC;AAAA,IAEb;AAEI,QAAA,CAAC,EAAE,YAAY,QAAQ,UAAU,UAAU,EAAE,UAAW,CAAA,IAAIC,qCAAA;AAAA,MAC9D;AAAA,MACA;AAAA,IACF;AAIA,aAAS,WAAW,MAAM;;AACxB,UAAI,YAAiB,WAAW;AAC5B,UAAA,OAAO,cAAc,UAAU;AACrB,oBAAA,KAAK,MAAM,SAAS;AAAA,MAAA;AAElC,qBAAe,SAAS;AACpB,UAAA,aAAa,UAAU,SAAS,GAAG;AAErC,cAAM,SAASC,uCAAA,gBAAe,YAAO,WAAP,mBAAe,WAAW;AACpD,YAAA,eAAe,MAAM,OAAO;AAC5B,YAAA,UAAU,WAAW,SAAS;AAClC,oBAAY,UAAU;AACtB,oBAAY,SAAS,CAAC;AACtB,gBAAQ,WAAW,QAAQ,CAAC,QAAQ,UAAU;;AAC5C,cAAI,QAAQ,GAAG;AACT,gBAAA,aAAa,MAAM,OAAO,WAAW,OAAO,CAAC,SAAS,KAAK,UAAU,MAAM;AAC/E,wBAAY,OAAO,KAAK;AAAA,cACtB,MAAM,cAAc,WAAW,SAAS,IAAI,WAAW,CAAC,EAAE,MAAM,IAAI;AAAA,cACpE,SAAOC,MAAA,OAAO,QAAM,CAAC,MAAd,gBAAAA,IAAiB,UAAMC,MAAA,OAAO,QAAM,CAAC,MAAd,gBAAAA,IAAiB,QAAM;AAAA,cACrD;AAAA,YAAA,CACD;AAAA,UAAA;AAAA,QACH,CACD;AAEC,oBAAY,OAAO,OAAO,YAAY,OAAO,IAAI,CAAC,SAAS,KAAK,MAAM,EAAE,OAAO,CAAA,SAAM,IAAI;AAG/E,oBAAA,MAAM,OAAOC,cAAAA,KAAK,CAAC,SAAS,UAAU,IAAG,kDAAc,UAAd,mBAAqB,IAAI,EAAE,CAAC;AAC7E,YAAA,YAAY,MAAM,QAAQ,YAAY;AAC5B,sBAAA,MAAM,UAAU,QAAQ;AAAA,QAAA,OAC/B;AACO,sBAAA,MAAM,UAAU,QAAQ;AAAA,QAAA;AAGlC,YAAA,MAAM,UAAU,OAAO,QAAQ;AAC3BC,8BAAA,aAAa,OAAO,MAAM;AAChC,wBAAcC,uCAAmB,mBAAA,MAAM,UAAU,aAAa,QAAQ,SAAS;AAC/E,wBAAcC,uCAAkB,kBAAA,MAAM,UAAU,WAA8B;AACzE,iBAAA,QAAQC,mCAAU,WAAW;AACpC,mBAAS,OAAO;AAAA,QAAA;AAAA,MAChB,OACK;AACL,iBAAS,SAAS;AAClB,iBAAS,OAAO;AAAA,MAAA;AAAA,IAClB;AAMF,aAAS,eAAe,WAAW;AAGjC,UAAI,YAAY,CAAC,GAAG,IAAI,IAAI,UAAU,IAAI,CAAC,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC;AAElE,UAAI,kBAAkB,MAAM,OAAO,cAAc,CAAC;AAElD,UAAI,eAAe,CAAC,GAAG,IAAI,IAAI,gBAAgB,IAAI,CAAC,SAAS,KAAK,QAAQ,CAAC,CAAC,CAAC;AAE7E,UAAI,CAAC,aAAa,WAAW,YAAY,GAAG;AAC1C,YAAI,gBAAgB,UAAU,IAAI,CAAC,WAAW;AACrC,iBAAA,EAAE,QAAQ,MAAM,MAAM;AAAA,QAAA,CAC9B;AACD,cAAM,OAAO,aAAa;AAAA,MAAA;AAAA,IAC5B;AAKF,aAAS,WAAW,WAAW;AACzB,UAAA,UAAU,EAAE,YAAY,CAAA,GAAI,QAAQ,CAAG,GAAA,SAAQ,EAAG;AACtD,UAAI,WAAW,CAAC;AAGhB,UAAI,aAAa,CAAC,SAAS,GAAG,IAAI,IAAI,UAAU,IAAI,CAAC,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC;AAG5E,UAAI,UAAU,CAAC,GAAG,IAAI,IAAI,UAAU,IAAI,CAAC,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC;AAExD,cAAA,QAAQ,CAAC,SAAS;AAEpB,YAAA,MAAM,UAAU,OAAO,CAAC,SAAS,KAAK,MAAM,KAAK,IAAI;AAEzD,YAAI,YAAY,IAAI,IAAI,CAAC,SAAS,KAAK,OAAO,CAAC;AAE/C,kBAAU,QAAQ,IAAI;AACtB,iBAAS,KAAK,SAAS;AAAA,MAAA,CACxB;AACD,cAAQ,aAAa;AACrB,cAAQ,SAAS;AACjB,UAAI,WAAW,UAAU,OAAO,CAAA,UAAO,MAAM,QAAM,CAAC,EAAE,IAAI,CAAC,SAAS,KAAK,OAAO,CAAC;AACjF,cAAQ,UAAW,SAAS,SAAO,IAAE,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,CAAC,IAAI,SAAS,SAAO;AACjF,aAAA;AAAA,IAAA;AAQA,aAAA,aAAa,MAAM,MAAM;AAChC,YAAM,KAAK,KAAK,IAAI,CAAC,MAAM,CAAC;AAC5B,UAAI,KAAK,KAAK,IAAI,CAAC,MAAM,CAAC;AAC1B,UAAI,UAAU,CAAC;AACX,UAAA,GAAG,WAAW,GAAG,QAAQ;AACpB,eAAA;AAAA,MAAA,OACF;AACL,iBAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAClC,cAAI,GAAG,QAAQ,GAAG,CAAC,CAAC,MAAM,IAAI;AAC5B,eAAG,OAAO,GAAG,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC;AACtB,oBAAA,KAAK,GAAG,CAAC,CAAC;AAAA,UAAA,OACb;AACL,sBAAU,CAAC;AACX;AAAA,UAAA;AAAA,QACF;AAEK,eAAA,QAAQ,WAAW,KAAK;AAAA,MAAA;AAAA,IACjC;AAEFC,kBAAAA,UAAU,MAAI;AACH,gBAAA;AAAA,IAAA,CACV;;;;;;;;;;;;;;;;AC5LD,GAAG,gBAAgBC,SAAS;"}