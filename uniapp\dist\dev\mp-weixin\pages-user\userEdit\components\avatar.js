"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../../common/vendor.js");
const utils_index = require("../../../utils/index.js");
require("../../../hooks/useUpload.js");
const store_user = require("../../../store/user.js");
const utils_request = require("../../../utils/request.js");
if (!Array) {
  const _easycom_wd_img2 = common_vendor.resolveComponent("wd-img");
  const _easycom_wd_message_box2 = common_vendor.resolveComponent("wd-message-box");
  (_easycom_wd_img2 + _easycom_wd_message_box2)();
}
const _easycom_wd_img = () => "../../../node-modules/wot-design-uni/components/wd-img/wd-img.js";
const _easycom_wd_message_box = () => "../../../node-modules/wot-design-uni/components/wd-message-box/wd-message-box.js";
if (!Math) {
  (_easycom_wd_img + _easycom_wd_message_box)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "avatar",
  options: {
    // apply-shared‌：当前页面样式会影响到子组件样式.(小程序)
    // shared‌：当前页面样式影响到子组件，子组件样式也会影响到当前页面.(小程序)
    styleIsolation: "shared"
  }
}), {
  __name: "avatar",
  props: ["modelValue"],
  emits: ["update:modelValue", "update:fileName"],
  setup(__props, { emit: __emit }) {
    const userStore = store_user.useUserStore();
    const props = __props;
    const emit = __emit;
    const toast = common_vendor.useToast();
    const message = common_vendor.useMessage();
    common_vendor.ref(false);
    const api = {
      uploadUrl: `${utils_index.getEnvBaseUrl()}/file/uploadavatar`
    };
    const fileName = common_vendor.ref("");
    const fetchedImgSrc = common_vendor.ref("");
    const imgSrc = common_vendor.computed(() => {
      if (props.modelValue) {
        if (props.modelValue.startsWith("http")) {
          return props.modelValue;
        }
        return fetchedImgSrc.value;
      }
      return "";
    });
    const fetchFileUrl = (objectName) => __async(this, null, function* () {
      var _a;
      try {
        const response = yield utils_request.request(`/file/url`, {
          method: "GET",
          params: {
            objectName,
            token: userStore.userInfo.token
          }
        });
        if ((response == null ? void 0 : response.success) && ((_a = response == null ? void 0 : response.result) == null ? void 0 : _a.fileUrl)) {
          fetchedImgSrc.value = response.result.fileUrl;
        } else {
          console.warn("响应数据格式异常:", response);
          fetchedImgSrc.value = "";
        }
        return response;
      } catch (error) {
        console.error("获取文件URL失败:", error);
        fetchedImgSrc.value = "";
        return null;
      }
    });
    common_vendor.watch(() => props.modelValue, (newValue) => {
      if (newValue && !newValue.startsWith("http")) {
        fetchFileUrl(newValue);
      } else {
        fetchedImgSrc.value = "";
      }
    }, { immediate: true });
    const handleDel = () => {
      message.confirm({
        msg: "确定要删除这个头像吗？",
        title: "提示"
      }).then(() => {
        emit("update:modelValue", "");
        fileName.value = "";
        emit("update:fileName", "");
      }).catch(() => {
        console.log("点击了取消按钮");
      });
    };
    const onChooseWechatAvatar = (e) => {
      console.log("微信头像选择回调:", e);
      const { avatarUrl } = e.detail;
      if (!avatarUrl) {
        toast.warning("未获取到头像信息");
        return;
      }
      uploadWechatAvatar(avatarUrl);
    };
    const uploadWechatAvatar = (avatarUrl) => {
      console.log("开始上传微信头像:", avatarUrl);
      if (!avatarUrl.includes("tmp") && !avatarUrl.includes("wxfile://")) {
        toast.warning("头像文件格式不正确");
        return;
      }
      common_vendor.index.showLoading({
        title: "上传头像中...",
        mask: true
      });
      common_vendor.index.uploadFile({
        url: api.uploadUrl,
        filePath: avatarUrl,
        name: "file",
        formData: {
          type: "avatar"
          // 标识为头像上传
        },
        header: {
          "X-Access-Token": userStore.userInfo.token
        },
        success: (res) => {
          console.log("微信头像上传响应:", res);
          try {
            const data = JSON.parse(res.data);
            if (data && data.success && data.result && data.result.fileUrl) {
              emit("update:modelValue", data.result.fileUrl);
              fileName.value = data.result.fileName;
              emit("update:fileName", data.result.fileName);
              toast.success("头像上传成功");
            } else {
              console.error("上传失败:", data);
              let errorMessage = (data == null ? void 0 : data.message) || "头像上传失败";
              try {
                if (typeof errorMessage === "string" && /[^\u0000-\u007f]/.test(errorMessage)) {
                  console.error("可能的编码问题:", errorMessage);
                  errorMessage = "头像上传失败";
                }
              } catch (e) {
                console.error("处理错误消息异常:", e);
              }
              toast.warning(errorMessage);
            }
          } catch (error) {
            console.error("解析上传响应失败:", error);
            toast.warning("头像上传失败");
          }
        },
        fail: (error) => {
          console.error("微信头像上传失败:", error);
          toast.warning("头像上传失败，请重试");
        },
        complete: () => {
          common_vendor.index.hideLoading();
        }
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: imgSrc.value
      }, imgSrc.value ? {
        b: common_vendor.o(handleDel),
        c: common_vendor.p({
          radius: 4,
          ["enable-preview"]: true,
          label: "头像",
          width: "60px",
          height: "60px",
          src: imgSrc.value
        }),
        d: common_vendor.o(onChooseWechatAvatar)
      } : {
        e: common_vendor.o(onChooseWechatAvatar)
      });
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-94e9fe44"]]);
wx.createComponent(Component);
//# sourceMappingURL=avatar.js.map
