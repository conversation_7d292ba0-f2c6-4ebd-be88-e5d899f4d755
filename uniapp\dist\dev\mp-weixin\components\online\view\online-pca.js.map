{"version": 3, "file": "online-pca.js", "sources": ["../../../../../../src/components/online/view/online-pca.vue", "../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvY29tcG9uZW50cy9vbmxpbmUvdmlldy9vbmxpbmUtcGNhLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view>\r\n    <wd-picker\r\n      :columns=\"columns\"\r\n      :label-width=\"labelWidth\"\r\n      :label=\"label\"\r\n      :required=\"required\"\r\n      v-model=\"selected\"\r\n      :column-change=\"onChangeDistrict\"\r\n      @confirm=\"handleConfirm\"\r\n    />\r\n  </view>\r\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { areaData } from '@/components/online/area-picker-data'\r\nimport {getAreaArrByCode} from '@/common/areaData/Area'\r\nimport {isString} from \"@/utils/is\";\r\n// 接收 props\r\nconst props = defineProps({\r\n  label: {\r\n    type: String,\r\n    default: '',\r\n    required: false,\r\n  },\r\n  labelWidth: {\r\n    type: String,\r\n    default: '80px',\r\n    required: false,\r\n  },\r\n  value: {\r\n    type: [String,Array],\r\n    required: false,\r\n  },\r\n  placeholder: {\r\n    type: String,\r\n    required: false,\r\n    default: '请选择省市区',\r\n  },\r\n  disabled: {\r\n    type: Boolean,\r\n    default: false,\r\n    required: false,\r\n  },\r\n  required: {\r\n    type: Boolean,\r\n    default: false,\r\n    required: false,\r\n  },\r\n})\r\n// 定义 emits\r\nconst emits = defineEmits(['input', 'change', 'update:value'])\r\n\r\n// 定义响应式数据\r\nconst selected = ref([])\r\nconst district = { ...areaData }\r\nconst columns = ref([\r\n  district[0],\r\n  district[district[0][0].value],\r\n  district[district[district[0][0].value][0].value]\r\n])\r\nconst onChangeDistrict = (pickerView, value, columnIndex, resolve) => {\r\n  const item = value[columnIndex]\r\n  if (columnIndex === 0) {\r\n    pickerView.setColumnData(1, district[item.value])\r\n    pickerView.setColumnData(2, district[district[item.value][0].value])\r\n  } else if (columnIndex === 1) {\r\n    pickerView.setColumnData(2, district[item.value])\r\n  }\r\n  resolve()\r\n}\r\n\r\nconst handleConfirm = ({value}) => {\r\n  emits('update:value', value);\r\n}\r\n// 监听 value 变化\r\nwatch(\r\n  () => props.value,\r\n  async (val) => {\r\n    if(props.value && isString(props.value)){\r\n      let arr = getAreaArrByCode(props.value);\r\n      selected.value = arr;\r\n      await initColumnData(arr);\r\n    }\r\n  },\r\n  { immediate: true },\r\n)\r\n/**\r\n * 初始化列数据\r\n * @param val\r\n */\r\nfunction initColumnData(val){\r\n  if(val && val.length){\r\n    let first = district[0];\r\n    let second = district[selected.value[0]];\r\n    let third = district[selected.value[1]];\r\n    columns.value = [first, second, third]\r\n  }\r\n}\r\n</script>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/components/online/view/online-pca.vue'\nwx.createComponent(Component)"], "names": ["ref", "areaData", "watch", "isString", "getAreaArrByCode", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBA,UAAM,QAAQ;AAgCd,UAAM,QAAQ;AAGR,UAAA,WAAWA,cAAI,IAAA,EAAE;AACjB,UAAA,WAAW,mBAAKC;AACtB,UAAM,UAAUD,cAAAA,IAAI;AAAA,MAClB,SAAS,CAAC;AAAA,MACV,SAAS,SAAS,CAAC,EAAE,CAAC,EAAE,KAAK;AAAA,MAC7B,SAAS,SAAS,SAAS,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK;AAAA,IAAA,CACjD;AACD,UAAM,mBAAmB,CAAC,YAAY,OAAO,aAAa,YAAY;AAC9D,YAAA,OAAO,MAAM,WAAW;AAC9B,UAAI,gBAAgB,GAAG;AACrB,mBAAW,cAAc,GAAG,SAAS,KAAK,KAAK,CAAC;AACrC,mBAAA,cAAc,GAAG,SAAS,SAAS,KAAK,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC;AAAA,MAAA,WAC1D,gBAAgB,GAAG;AAC5B,mBAAW,cAAc,GAAG,SAAS,KAAK,KAAK,CAAC;AAAA,MAAA;AAE1C,cAAA;AAAA,IACV;AAEA,UAAM,gBAAgB,CAAC,EAAC,YAAW;AACjC,YAAM,gBAAgB,KAAK;AAAA,IAC7B;AAEAE,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAO,QAAQ;AACb,YAAG,MAAM,SAASC,SAAS,SAAA,MAAM,KAAK,GAAE;AAClC,cAAA,MAAMC,qBAAAA,iBAAiB,MAAM,KAAK;AACtC,mBAAS,QAAQ;AACjB,gBAAM,eAAe,GAAG;AAAA,QAAA;AAAA,MAE5B;AAAA,MACA,EAAE,WAAW,KAAK;AAAA,IACpB;AAKA,aAAS,eAAe,KAAI;AACvB,UAAA,OAAO,IAAI,QAAO;AACf,YAAA,QAAQ,SAAS,CAAC;AACtB,YAAI,SAAS,SAAS,SAAS,MAAM,CAAC,CAAC;AACvC,YAAI,QAAQ,SAAS,SAAS,MAAM,CAAC,CAAC;AACtC,gBAAQ,QAAQ,CAAC,OAAO,QAAQ,KAAK;AAAA,MAAA;AAAA,IACvC;;;;;;;;;;;;;;;;;AChGF,GAAG,gBAAgBC,SAAS;"}