{"version": 3, "file": "demo.js", "sources": ["../../../../src/layouts/demo.vue", "../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvbGF5b3V0cy9kZW1vLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <wd-config-provider :themeVars=\"themeVars\">\r\n    <slot />\r\n    <wd-toast />\r\n    <wd-message-box />\r\n  </wd-config-provider>\r\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport type { ConfigProviderThemeVars } from 'wot-design-uni'\r\n\r\nconst themeVars: ConfigProviderThemeVars = {\r\n  // colorTheme: 'red',\r\n  // buttonPrimaryBgColor: '#07c160',\r\n  // buttonPrimaryColor: '#07c160',\r\n}\r\n</script>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/layouts/demo.vue'\nwx.createComponent(Component)"], "names": ["Component"], "mappings": ";;;;;;;;;;;;;;;;;AAWA,UAAM,YAAqC;AAAA;AAAA;AAAA;AAAA,IAI3C;;;;;;;;;;ACdA,GAAG,gBAAgBA,SAAS;"}