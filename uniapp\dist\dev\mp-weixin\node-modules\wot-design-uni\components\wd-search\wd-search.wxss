/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 混合宏
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/**
 * 辅助函数
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/* 转换成字符串 */
/* 判断是否存在 Modifier */
/* 判断是否存在伪类 */
/**
 * 主题色切换
 * @params $theme-color 主题色
 * @params $type 变暗’dark‘ 变亮 'light'
 * @params $mix-color 自己设置的混色
 */
/**
 * 颜色结果切换， 如果开启线性渐变色 使用渐变色，如果没有开启，那么使用主题色
 * @params $open-linear 是否开启线性渐变色
 * @params $deg 渐变色角度
 * @params $theme-color 当前配色
 * @params [Array] $set 主题色明暗设置，与 $color-list 数量对应
 * @params [Array] $color-list 渐变色顺序， $color-list 和 $per-list 数量相同
 * @params [Array] $per-list 渐变色比例
 */
/**
  * BEM，定义块（b)
  */
/* 定义元素（e），对于伪类，会自动将 e 嵌套在 伪类 底下 */
/* 此方法用于生成穿透样式 */
/* 定义元素（e），对于伪类，会自动将 e 嵌套在 伪类 底下 */
/* 定义状态（m） */
/* 定义状态（m） */
/* 对于需要需要嵌套在 m 底下的 e，调用这个混合宏，一般在切换整个组件的状态，如切换颜色的时候 */
/* 状态，生成 is-$state 类名 */
/**
  * 常用混合宏
  */
/* 单行超出隐藏 */
/* 多行超出隐藏 */
/* 清除浮动 */
/* 0.5px 边框 指定方向*/
/* 0.5px 边框 环绕 */
/**
  * 三角形实现尖角样式，适用于背景透明情况
  * @param $size 三角形高，底边为 $size * 2
  * @param $bg 三角形背景颜色
  */
/**
  * 正方形实现尖角样式，适用于背景不透明情况
  * @param $size 正方形边长
  * @param $bg 正方形背景颜色
  * @param $z-index z-index属性值，不得大于外部包裹器
  * @param $box-shadow 阴影
*/
/**
 * 辅助函数
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/* 转换成字符串 */
/* 判断是否存在 Modifier */
/* 判断是否存在伪类 */
/**
 * 主题色切换
 * @params $theme-color 主题色
 * @params $type 变暗’dark‘ 变亮 'light'
 * @params $mix-color 自己设置的混色
 */
/**
 * 颜色结果切换， 如果开启线性渐变色 使用渐变色，如果没有开启，那么使用主题色
 * @params $open-linear 是否开启线性渐变色
 * @params $deg 渐变色角度
 * @params $theme-color 当前配色
 * @params [Array] $set 主题色明暗设置，与 $color-list 数量对应
 * @params [Array] $color-list 渐变色顺序， $color-list 和 $per-list 数量相同
 * @params [Array] $per-list 渐变色比例
 */
/**
 * UI规范基础变量
 */
/*----------------------------------------- Theme color. start ----------------------------------------*/
/* 主题颜色 */
/* 辅助色 */
/* 文字颜色（默认浅色背景下 */
/* 暗黑模式 */
/* 图形颜色 */
/*----------------------------------------- Theme color. end -------------------------------------------*/
/*-------------------------------- Theme color application size.  start --------------------------------*/
/* 文字字号 */
/* 文字字重 */
/* 尺寸 */
/*-------------------------------- Theme color application size.  end --------------------------------*/
/* component var */
/* action-sheet */
/* badge */
/* button */
/* cell */
/* calendar */
/* checkbox */
/* collapse */
/* divider */
/* drop-menu */
/* input-number */
/* input */
/* textarea */
/* loadmore */
/* message-box */
/* notice-bar */
/* pagination */
/* picker */
/* col-picker */
/* overlay */
/* popup */
/* progress */
/* radio */
/* search */
/* slider */
/* sort-button */
/* steps */
/* switch */
/* tabs */
/* tag */
/* toast */
/* loading */
/* tooltip */
/* popover */
/* grid-item */
/* statustip */
/* card */
/* upload */
/* curtain */
/* notify */
/* skeleton */
/* circle */
/* swiper */
/* swiper-nav */
/* segmented */
/* tabbar */
/* tabbar-item */
/* navbar */
/* navbar-capsule */
/* table */
/* sidebar */
/* sidebar-item */
/* fab */
/* count-down */
/* keyboard */
/* number-keyboard */
/* passwod-input */
/* form-item */
/* backtop */
/* index-bar */
/* text */
/* video-preview */
/* img-cropper */
/* floating-panel */
/* signature */
.wot-theme-dark .wd-search.data-v-4ebafd4c {
  background: var(--wot-dark-background4, #323233);
}
.wot-theme-dark .wd-search__block.data-v-4ebafd4c {
  background-color: var(--wot-dark-background, #131313);
}
.wot-theme-dark .wd-search__input.data-v-4ebafd4c {
  color: var(--wot-dark-color, var(--wot-color-white, rgb(255, 255, 255)));
}
.wot-theme-dark .wd-search__cover.data-v-4ebafd4c {
  background-color: var(--wot-dark-background, #131313);
}
.wot-theme-dark .wd-search__search-icon.data-v-4ebafd4c {
  color: var(--wot-dark-color, var(--wot-color-white, rgb(255, 255, 255)));
}
.wot-theme-dark .wd-search__search-left-icon.data-v-4ebafd4c {
  color: var(--wot-dark-color, var(--wot-color-white, rgb(255, 255, 255)));
}
.wot-theme-dark .wd-search__clear.data-v-4ebafd4c {
  color: var(--wot-dark-color, var(--wot-color-white, rgb(255, 255, 255)));
}
.wot-theme-dark .wd-search__cancel.data-v-4ebafd4c {
  color: var(--wot-dark-color, var(--wot-color-white, rgb(255, 255, 255)));
}
.wot-theme-dark .wd-search.is-light.data-v-4ebafd4c {
  background: var(--wot-dark-background4, #323233);
}
.wot-theme-dark .wd-search.is-light .wd-search__block.data-v-4ebafd4c {
  background: var(--wot-dark-background7, #707070);
}
.wot-theme-dark .wd-search.is-light .wd-search__cover.data-v-4ebafd4c {
  background: var(--wot-dark-background7, #707070);
}
.wd-search.data-v-4ebafd4c {
  display: flex;
  padding: var(--wot-search-padding, 10px 0 10px var(--wot-search-side-padding, var(--wot-size-side-padding, 15px)));
  align-items: center;
  background: #fff;
}
.wd-search__block.data-v-4ebafd4c {
  flex: 1;
  background-color: var(--wot-search-input-bg, var(--wot-color-bg, #f5f5f5));
  border-radius: var(--wot-search-input-radius, 15px);
  display: flex;
  flex-direction: row;
  align-items: center;
  position: relative;
}
.wd-search__field.data-v-4ebafd4c {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  position: relative;
}
.wd-search__input.data-v-4ebafd4c {
  flex: 1;
  height: var(--wot-search-input-height, 30px);
  box-sizing: border-box;
  padding: var(--wot-search-input-padding, 0 32px 0 42px);
  border: none;
  background: transparent;
  font-size: var(--wot-search-input-fs, var(--wot-fs-content, 14px));
  -webkit-appearance: none;
  outline: none;
  color: var(--wot-search-input-color, #262626);
  z-index: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.wd-search__input.data-v-4ebafd4c::-webkit-search-cancel-button {
  -webkit-appearance: none;
}
.wd-search__cover.data-v-4ebafd4c {
  width: 100%;
  height: var(--wot-search-input-height, 30px);
  background-color: var(--wot-search-input-bg, var(--wot-color-bg, #f5f5f5));
  line-height: var(--wot-search-input-height, 30px);
  font-size: var(--wot-search-input-fs, var(--wot-fs-content, 14px));
  border-radius: var(--wot-search-input-radius, 15px);
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.data-v-4ebafd4c  .wd-search__search-icon {
  margin-right: 8px;
  color: var(--wot-search-icon-color, var(--wot-color-icon, #d9d9d9));
  font-size: var(--wot-search-icon-size, 18px);
}
.data-v-4ebafd4c  .wd-search__search-left-icon {
  position: absolute;
  font-size: var(--wot-search-icon-size, 18px);
  top: 50%;
  left: 16px;
  transform: translateY(-50%);
  color: var(--wot-search-icon-color, var(--wot-color-icon, #d9d9d9));
}
.wd-search__placeholder-txt.data-v-4ebafd4c {
  color: var(--wot-search-placeholder-color, #bfbfbf);
  font-size: var(--wot-search-input-fs, var(--wot-fs-content, 14px));
}
.data-v-4ebafd4c  .wd-search__clear {
  position: absolute;
  right: 0;
  padding: 6px 9px 6px 7px;
  color: var(--wot-search-cancel-color, rgba(0, 0, 0, 0.65));
}
.data-v-4ebafd4c  .wd-search__clear-icon {
  vertical-align: middle;
  font-size: var(--wot-search-clear-icon-size, var(--wot-fs-title, 16px));
}
.wd-search__cancel.data-v-4ebafd4c {
  padding: var(--wot-search-cancel-padding, 0 var(--wot-search-side-padding, var(--wot-size-side-padding, 15px)) 0 10px);
  height: var(--wot-search-input-height, 30px);
  line-height: var(--wot-search-input-height, 30px);
  font-size: var(--wot-search-cancel-fs, var(--wot-fs-title, 16px));
  color: var(--wot-search-cancel-color, rgba(0, 0, 0, 0.65));
  -webkit-tap-highlight-color: transparent;
}
.wd-search.is-light.data-v-4ebafd4c {
  background: var(--wot-search-light-bg, var(--wot-color-bg, #f5f5f5));
}
.wd-search.is-light .wd-search__block.data-v-4ebafd4c {
  background: #fff;
}
.wd-search.is-light .wd-search__cover.data-v-4ebafd4c {
  background: #fff;
}
.wd-search.is-without-cancel.data-v-4ebafd4c {
  padding-right: var(--wot-search-side-padding, var(--wot-size-side-padding, 15px));
}