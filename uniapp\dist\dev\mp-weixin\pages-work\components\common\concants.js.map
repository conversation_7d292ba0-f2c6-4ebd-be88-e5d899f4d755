{"version": 3, "file": "concants.js", "sources": ["../../../../../../src/pages-work/components/common/concants.ts"], "sourcesContent": ["//仪表盘组件\r\nexport const compList = [\r\n \t\"JBar\",\r\n \t\"JDynamicBar\",\r\n \t\"JBackgroundBar\",\r\n \t\"JStackBar\",\r\n \t\"JMultipleBar\",\r\n \t\"JNegativeBar\",\r\n\t\"JHorizontalBar\",\r\n\t\"JMixLineBar\",\r\n\r\n \t\"JLine\",\r\n\t\"JArea\",\r\n \t\"JMultipleLine\",\r\n \t\"DoubleLineBar\",\r\n \t\"JSmoothLine\",\r\n \t\"JStepLine\",\r\n\r\n \t\"JPie\",\r\n\t\"JRose\",\r\n \t\"JRing\",\r\n\r\n \t\"JFunnel\",\r\n \t\"JPyramidFunnel\",\r\n\r\n \t\"JRadar\",\r\n \t\"JCircleRadar\",\r\n\r\n \t\"JGauge\",\r\n \t\"JColorGauge\",\r\n\r\n \t\"JScatter\",\r\n \t\"JBubble\",\r\n\r\n    \"JPictorial\",\r\n    \"JPictorialBar\",\r\n\r\n\t\"JDragEditor\",\r\n\t\"JCarousel\",\r\n\t\"JIframe\",\r\n\t\"JNumber\",\r\n\t\"JCustomButton\",\r\n\t\"JPivotTable\",\r\n\t\"JText\",\r\n\t\"JImg\",\r\n\t\"JCalendar\",\r\n\t\"JCurrentTime\",\r\n\t\"JList\",\r\n\t\"JRadioButton\",\r\n\t\"JCommonTable\",\r\n\t\"JQuickNav\",\r\n\t\"JFilterQuery\",\r\n\r\n\t\"JBubbleMap\",\r\n\t\"JBarMap\",\r\n\t\"JHeatMap\",\r\n\t\"JAreaMap\",\r\n\t\"JFlyLineMap\",\r\n ];\r\n //不包含操作的组件\r\nexport const noActionList = [\r\n    \"JCustomButton\",\r\n\t\"JIframe\",\r\n\t\"JCarousel\",\r\n\t\"JDragEditor\",\r\n\t\"JText\",\r\n\t\"JNumber\",\r\n\t\"JFilterQuery\",\r\n];\r\n/**\r\n * 组件名称前缀\r\n */\r\nexport const COMP_NAME_PREFIX = 'jeecg-drag';\r\n\r\n/**\r\n *省略显示\r\n */\r\nexport const tableEllipsis = \"--\";\r\n/**\r\n *下拉选择控件类型\r\n */\r\nexport const selectType = ['list','radio','list_multi','checkbox','sel_user','sel_depart','select-user','select-depart',\"select-tree\",\"table-dict\",\"link-record\"];\r\n\r\n/**\r\n * 筛选条件\r\n */\r\nexport const conditionOptions = {\r\n  text: [\r\n    {\r\n      label: '是',\r\n      value: '1',\r\n      expression:\"=\"\r\n    },\r\n    {\r\n      label: '不是',\r\n      value: '2',\r\n      expression:\"!=\"\r\n    },\r\n    {\r\n      label: '包含',\r\n      value: '4',\r\n      expression:\"like\"\r\n    },\r\n    {\r\n      label: '开头为',\r\n      value: '5',\r\n      expression:\"like\"\r\n    },\r\n    {\r\n      label: '结尾为',\r\n      value: '6',\r\n      expression:\"like\"\r\n    },\r\n    {\r\n      label: '为空',\r\n      value: '7',\r\n      expression:\"is null\"\r\n    },\r\n    {\r\n      label: '不为空',\r\n      value: '8',\r\n      expression:\"is not null\"\r\n    },\r\n  ],\r\n  select:[\r\n    {\r\n      label: '包含',\r\n      value: '3',\r\n      expression:\"in\"\r\n    },\r\n    {\r\n      label: '不包含',\r\n      value: '4',\r\n      expression:\"not in\"\r\n    },\r\n    {\r\n      label: '为空',\r\n      value: '7',\r\n      expression:\"is null\"\r\n    },\r\n    {\r\n      label: '不为空',\r\n      value: '8',\r\n      expression:\"is not null\"\r\n    },\r\n  ],\r\n  number: [\r\n    {\r\n      label: '=',\r\n      value: '1',\r\n      expression:\"=\"\r\n    },\r\n    {\r\n      label: '≠',\r\n      value: '2',\r\n      expression:\"!=\"\r\n    },\r\n    {\r\n      label: '>',\r\n      value: '3',\r\n      expression:\">\"\r\n    },\r\n    {\r\n      label: '<',\r\n      value: '4',\r\n      expression:\"<\"\r\n    },\r\n    {\r\n      label: '≥',\r\n      value: '5',\r\n      expression:\">=\"\r\n    },\r\n    {\r\n      label: '≤',\r\n      value: '6',\r\n      expression:\"<=\"\r\n    },\r\n    {\r\n      label: '在范围内',\r\n      value: '9',\r\n      expression: 'between',\r\n    },\r\n    {\r\n      label: '不在范围内',\r\n      value: '10',\r\n      expression: 'not between',\r\n    },\r\n    {\r\n      label: '为空',\r\n      value: '7',\r\n      expression:\"is null\",\r\n    },\r\n    {\r\n      label: '不为空',\r\n      value: '8',\r\n      expression:\"is not null\"\r\n    },\r\n  ],\r\n  time:[\r\n    {\r\n      label: '是',\r\n      value: '1',\r\n      expression:\"=\"\r\n    },\r\n    {\r\n      label: '不是',\r\n      value: '2',\r\n      expression:\"!=\"\r\n    },\r\n    {\r\n      label: '在范围内',\r\n      value: '9',\r\n      expression: 'between',\r\n    },\r\n    {\r\n      label: '不在范围内',\r\n      value: '10',\r\n      expression: 'not between',\r\n    },\r\n    {\r\n      label: '为空',\r\n      value: '7',\r\n      expression:\"is null\",\r\n    },\r\n    {\r\n      label: '不为空',\r\n      value: '8',\r\n      expression:\"is not null\",\r\n    }\r\n  ],\r\n  pca:[\r\n    {\r\n      label: '是',\r\n      value: '1',\r\n      expression:\"=\"\r\n    },\r\n    {\r\n      label: '不是',\r\n      value: '2',\r\n      expression:\"!=\"\r\n    },\r\n    {\r\n      label: '为空',\r\n      value: '7',\r\n      expression:\"is null\",\r\n    },\r\n    {\r\n      label: '不为空',\r\n      value: '8',\r\n      expression:\"is not null\",\r\n    }\r\n  ],\r\n  'sub-table-design':[\r\n    {\r\n      label: '为空',\r\n      value: '7',\r\n      expression:\"is null\",\r\n    },\r\n    {\r\n      label: '不为空',\r\n      value: '8',\r\n      expression:\"is not null\",\r\n    }\r\n  ],\r\n  'link-record':[\r\n    {\r\n      label: '是 ',\r\n      value: '1',\r\n      expression:\"=\",\r\n    },\r\n    {\r\n      label: '包含',\r\n      value: '3',\r\n      expression:\"in\",\r\n    },\r\n    {\r\n      label: '为空',\r\n      value: '7',\r\n      expression:\"is null\",\r\n    },\r\n    {\r\n      label: '不为空',\r\n      value: '8',\r\n      expression:\"is not null\",\r\n    }\r\n  ],\r\n  date: [\r\n    {\r\n      label: '是',\r\n      value: '1',\r\n      expression:\"=\"\r\n    },\r\n    {\r\n      label: '不是',\r\n      value: '2',\r\n      expression:\"!=\"\r\n    },\r\n    {\r\n      label: '晚于',\r\n      value: '3',\r\n      expression:\">\"\r\n    },\r\n    {\r\n      label: '早于',\r\n      value: '4',\r\n      expression:\"<\"\r\n    },\r\n    {\r\n      label: '晚于等于',\r\n      value: '5',\r\n      expression:\">=\"\r\n    },\r\n    {\r\n      label: '早于等于',\r\n      value: '6',\r\n      expression:\"<=\"\r\n    },\r\n    {\r\n      label: '在范围内',\r\n      value: '9',\r\n      expression: 'between',\r\n    },\r\n    {\r\n      label: '不在范围内',\r\n      value: '10',\r\n      expression: 'not between',\r\n    },\r\n    {\r\n      label: '为空',\r\n      value: '7',\r\n      expression:\"is null\",\r\n    },\r\n    {\r\n      label: '不为空',\r\n      value: '8',\r\n      expression:\"is not null\",\r\n    }\r\n  ],\r\n};\r\n/**\r\n * 颜色板\r\n * classic：经典\r\n * technology：科技\r\n * business：商务\r\n * botany：植物\r\n * natural：自然\r\n * colour：彩色\r\n */\r\nexport const colorPanel = {\r\n  classic:[\"#64b5f6\",\"#4db6ac\",\"#ffb74d\",\"#e57373\",\"#9575cd\",\"#a1887f\",\"#90a4ae\",\"#4dd0e1\",\"#81c784\",\"#ff8a65\"],\r\n  technology:[\"#3a5b84\",\"#4d6e98\",\"#7594b9\",\"#bfd7f2\",\"#18619f\",\"#408aca\",\"#5ea8e9\",\"#81c3fc\",\"#71a5cb\",\"#a1cae4\"],\r\n  business:[\"#ccedf7\",\"#b9dcf0\",\"#12a0e7\",\"#0663a4\",\"#458890\",\"#97d9cd\",\"#4bb8bf\",\"#20899c\",\"#f44336  \",\"#a2c7d9\"],\r\n  botany:[\"#34b392\",\"#4ac2a6\",\"#8ed1c0\",\"#ccdec6\",\"#61bdb5\",\"#7993a1\",\"#93a889\",\"#5e8d83\",\"#115040\",\"#bcc5b4\"],\r\n  natural:[\"#85cacd\",\"#a7d676\",\"#fee159\",\"#fbc78e\",\"#ef918b\",\"#a9b5ff\",\"#e7daca\",\"#fc803a\",\"#fea1ac\",\"#c2a3cd\"],\r\n  colour:[\"#fddb9c\",\"#f9ae91\",\"#f59193\",\"#d47f97\",\"#bd86a6\",\"#f595a1\",\"#624772\",\"#fe7156\",\"#ffbda3\",\"#877fa8\"]\r\n};\r\n/**\r\n * 映射关系数组\r\n */\r\nexport const fieldMappings = [\r\n  {\r\n    label: '分组',\r\n    key: 'type',\r\n  },\r\n  {\r\n    label: '维度',\r\n    key: 'name',\r\n  },\r\n  {\r\n    label: '名称',\r\n    key: 'name',\r\n  },\r\n  {\r\n    label: '文本',\r\n    key: 'label',\r\n  },\r\n  {\r\n    label: '数值',\r\n    key: 'value',\r\n  },\r\n  {\r\n    label: '标题',\r\n    key: 'title',\r\n  },\r\n  {\r\n    label: '时间',\r\n    key: 'date',\r\n  },\r\n  {\r\n    label: '年份',\r\n    key: 'year',\r\n  },\r\n  {\r\n    label: '头像',\r\n    key: 'avatar',\r\n  },\r\n  {\r\n    label: '封面',\r\n    key: 'avatar',\r\n  },\r\n  {\r\n    label: '描述',\r\n    key: 'desc',\r\n  },\r\n  {\r\n    label: '路径',\r\n    key: 'src',\r\n  },\r\n  {\r\n    label: '开始',\r\n    key: 'start',\r\n  },\r\n  {\r\n    label: '结束',\r\n    key: 'end',\r\n  },\r\n  {\r\n    label: '全天',\r\n    key: 'allday',\r\n  },\r\n  {\r\n    label: '颜色',\r\n    key: 'color',\r\n  },\r\n  {\r\n    label: '区域',\r\n    key: 'name',\r\n  },\r\n  {\r\n    label: '图标',\r\n    key: 'icon',\r\n  },\r\n  {\r\n    label: '总计',\r\n    key: 'total',\r\n  },\r\n  {\r\n    label: '前缀',\r\n    key: 'prefix',\r\n  },\r\n  {\r\n    label: '后缀',\r\n    key: 'suffix',\r\n  },\r\n  {\r\n    label: '背景色',\r\n    key: 'backgroundColor',\r\n  },\r\n  {\r\n    label: '单位',\r\n    key: 'action',\r\n  },\r\n  {\r\n    label: '描述',\r\n    key: 'desc',\r\n  },\r\n  {\r\n    label: '分组',\r\n    key: 'group',\r\n  },\r\n  {\r\n    label: '内容',\r\n    key: 'content',\r\n  },\r\n  {\r\n    label: '跳转',\r\n    key: 'href',\r\n  },\r\n  {\r\n    label: '男',\r\n    key: 'man',\r\n  },\r\n  {\r\n    label: '女',\r\n    key: 'woman',\r\n  },\r\n  {\r\n    label: '起点名称',\r\n    key: 'fromName',\r\n  },\r\n  {\r\n    label: '终点名称',\r\n    key: 'toName',\r\n  },\r\n  {\r\n    label: '起点经度',\r\n    key: 'fromLng',\r\n  },\r\n  {\r\n    label: '起点纬度',\r\n    key: 'fromLat',\r\n  },\r\n  {\r\n    label: '终点经度',\r\n    key: 'toLng',\r\n  },\r\n  {\r\n    label: '终点纬度',\r\n    key: 'toLat',\r\n  },\r\n  {\r\n    label: '经度',\r\n    key: 'lng',\r\n  },\r\n  {\r\n    label: '纬度',\r\n    key: 'lat',\r\n  }\r\n];\r\n"], "names": [], "mappings": ";AACO,MAAM,WAAW;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACD;AAAA,EACA;AAAA,EAEC;AAAA,EACD;AAAA,EACC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAEA;AAAA,EACD;AAAA,EACC;AAAA,EAEA;AAAA,EACA;AAAA,EAEA;AAAA,EACA;AAAA,EAEA;AAAA,EACA;AAAA,EAEA;AAAA,EACA;AAAA,EAEE;AAAA,EACA;AAAA,EAEH;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACA;AAuBM,MAAM,aAAa,CAAC,QAAO,SAAQ,cAAa,YAAW,YAAW,cAAa,eAAc,iBAAgB,eAAc,cAAa,aAAa;AAKzJ,MAAM,mBAAmB;AAAA,EAC9B,MAAM;AAAA,IACJ;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IAAA;AAAA,EAEf;AAAA,EACA,QAAO;AAAA,IACL;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IAAA;AAAA,EAEf;AAAA,EACA,QAAQ;AAAA,IACN;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAY;AAAA,IACd;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAY;AAAA,IACd;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IAAA;AAAA,EAEf;AAAA,EACA,MAAK;AAAA,IACH;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAY;AAAA,IACd;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAY;AAAA,IACd;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IAAA;AAAA,EAEf;AAAA,EACA,KAAI;AAAA,IACF;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IAAA;AAAA,EAEf;AAAA,EACA,oBAAmB;AAAA,IACjB;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IAAA;AAAA,EAEf;AAAA,EACA,eAAc;AAAA,IACZ;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IAAA;AAAA,EAEf;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAY;AAAA,IACd;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAY;AAAA,IACd;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAW;AAAA,IAAA;AAAA,EACb;AAEJ;AAUO,MAAM,aAAa;AAAA,EACxB,SAAQ,CAAC,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,SAAS;AAM9G;AAIO,MAAM,gBAAgB;AAAA,EAC3B;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,OAAO;AAAA,IACP,KAAK;AAAA,EAAA;AAET;;;;;;"}