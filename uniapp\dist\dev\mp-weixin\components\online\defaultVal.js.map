{"version": 3, "file": "defaultVal.js", "sources": ["../../../../../src/components/online/defaultVal.ts"], "sourcesContent": ["\r\n// 移动端不支持自定义表达式设置的默认值\r\nimport {formatDate} from \"@/utils\";\r\nimport {http} from \"@/utils/http\";\r\nimport {useUserStore} from \"@/store\";\r\n\r\nconst CustomExpression = {}\r\n// 获取所有用户自定义表达式的Key\r\nconst ceKeys = Object.keys(CustomExpression)\r\n// 将key用逗号拼接，可以拼接成方法参数，例：a,b,c --> function(a,b,c){}\r\nconst ceJoin = ceKeys.join(',')\r\n// 将用户自定义的表达式按key的顺序放到数组中，可以使用 apply 传递给方法直接调用\r\nconst $CE$ = ceKeys.map(key => CustomExpression[key])\r\n\r\n/** 普通规则表达式 #{...} */\r\nconst normalRegExp = /#{([^}]+)?}/g\r\n/** 用户自定义规则表达式 {{...}} */\r\nconst customRegExp = /{{([^}]+)?}}/g\r\n/** 填值规则表达式 ${...} */\r\nconst fillRuleRegExp = /\\${([^}]+)?}/g\r\n\r\n/** action 类型 */\r\nexport const ACTION_TYPES = { ADD: 'add', EDIT: 'edit', DETAIL: 'detail', RELOAD: 'reload' }\r\n\r\n/**\r\n * 获取单个字段的默认值-通过回调函数返回值\r\n * @param {Object} defVal\r\n * @param {Object} type\r\n * @param {Object} callback\r\n */\r\nexport async function loadOneFieldDefVal(defVal, type, callback) {\r\n  if(hasEffectiveValue(defVal)){\r\n    let value = await handleDefaultValue(defVal, ACTION_TYPES.ADD, {});\r\n    if ('number' === type && value) {\r\n      value = Number.parseFloat(value)\r\n    }\r\n\tcallback(value)\r\n  }\r\n}\r\n\r\n/**\r\n * 判断给定的值是不是有效的\r\n */\r\nfunction hasEffectiveValue(val) {\r\n  if(val || val === 0){\r\n    return true;\r\n  }\r\n  return false;\r\n}\r\n\r\n/**\r\n * 获取默认值\r\n * @param {Object} defVal\r\n * @param {Object} action\r\n * @param {Object} getFormData\r\n */\r\nasync function handleDefaultValue(defVal, action, getFormData) {\r\n  if (defVal != null) {\r\n    // 检查类型，如果类型错误则不继续运行\r\n    if (checkExpressionType(defVal)) {\r\n      let value = await getDefaultValue(defVal, action, getFormData)\r\n      if (value != null) {\r\n        return value\r\n      }\r\n    }\r\n  }\r\n  return defVal;\r\n}\r\n\r\n/**\r\n * 加载form组件默认值\r\n * @param form Form对象\r\n * @param properties 字段配置\r\n * @param action 操作类型（ACTION_TYPES），除填值规则外，其他表达式只在add下才执行\r\n * @param getFormData 获取数据的方法，用于填值规则向后台传值\r\n */\r\nexport function loadFieldDefVal({ form, properties, action, getFormData }) {\r\n  if (Array.isArray(properties) && properties.length > 0) {\r\n    properties.forEach(async prop => {\r\n      let { defVal, type } = prop._formSchem\r\n      // key取值错误导致 树形表 表单默认值未生效  【online】树列表不支持控件默认值表达式配置 （博威）\r\n      let key = prop.key\r\n      // 2021年5月21日 Tree类型表单，系统编码不生效。【issues/I3NR39】\r\n      if (!key) {\r\n        key = prop._propertyId\r\n      }\r\n      eachHandler(key, defVal, action, (value) => {\r\n        // 处理数字类型，如果type=number并且value有值\r\n        if ('number' === type && value) {\r\n          // parseFloat() 可以直接处理字符串、整数、小数、null和undefined，\r\n          // 非数字类型直接返回NaN，不必担心报错\r\n          value = Number.parseFloat(value)\r\n        }\r\n        form.setFieldsValue({ [key]: value })\r\n      }, getFormData)\r\n    })\r\n  }\r\n}\r\n\r\n/** 加载JEditableTable组件默认值 */\r\nexport function loadFieldDefValForSubTable({ subForms, subTable, row, action, getFormData }) {\r\n  if (subTable && Array.isArray(subTable.columns) && subTable.columns.length > 0) {\r\n    subTable.columns.forEach(async column => {\r\n      let { key, fieldDefaultValue: defVal } = column\r\n      eachHandler(key, defVal, action, (value) => {\r\n        if (subForms.form) {\r\n          subForms.form.setFieldsValue({ [key]: value })\r\n        } else {\r\n          // update-begin---author:sunjianlei  Date:20200725 for：online功能测试，行操作切换成新的行编辑-----------\r\n          let v = [{rowKey: row.id, values: {[key]: value}}];\r\n          (subForms.jvt || subForms.jet).setValues(v)\r\n          // update-end---author:sunjianlei    Date:20200725 for：online功能测试，行操作切换成新的行编辑------------\r\n        }\r\n      }, getFormData)\r\n    })\r\n  }\r\n}\r\n\r\nasync function eachHandler(key, defVal, action, callback, getFormData) {\r\n  if (defVal != null) {\r\n    // 检查类型，如果类型错误则不继续运行\r\n    if (checkExpressionType(defVal)) {\r\n      let value = await getDefaultValue(defVal, action, getFormData)\r\n      if (value != null) {\r\n        callback(value)\r\n      }\r\n    } else {\r\n      // 不合法的表达式直接返回不解析\r\n      callback(defVal)\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * 检查表达式类型是否合法，规则：\r\n * 1、填值规则表达式不能和其他表达式混用\r\n * 2、每次只能填写一个填值规则表达式\r\n * 3、普通表达式和用户自定义表达式可以混用\r\n */\r\nexport function checkExpressionType(defVal) {\r\n  // 获取各个表达式的数量\r\n  let normalCount = 0, customCount = 0, fillRuleCount = 0\r\n  defVal.replace(fillRuleRegExp, () => fillRuleCount++)\r\n  if (fillRuleCount > 1) {\r\n    logWarn(`表达式[${defVal}]不合法：只能同时填写一个填值规则表达式！`)\r\n    return false\r\n  }\r\n  defVal.replace(normalRegExp, () => normalCount++)\r\n  defVal.replace(customRegExp, () => customCount++)\r\n  // 除填值规则外其他规则的数量\r\n  let fillRuleOtherCount = normalCount + customCount\r\n  if (fillRuleCount > 0 && fillRuleOtherCount > 0) {\r\n    logWarn(`表达式[${defVal}]不合法：填值规则表达式不能和其他表达式混用！`)\r\n    return false\r\n  }\r\n  return true\r\n}\r\n\r\n/** 获取所有匹配的表达式 */\r\nfunction getRegExpMap(text, exp) {\r\n  let map = new Map()\r\n  if(text && text.length>0){\r\n\ttext.replace(exp, function (match, param, offset, string) {\r\n\t   map.set(match, param.trim())\r\n\t   return match\r\n\t})\r\n  }\r\n  return map\r\n}\r\n\r\n/** 获取默认值，可以执行表达式，可以执行用户自定义方法，可以异步获取用户信息等 */\r\nasync function getDefaultValue(defVal, action, getFormData) {\r\n  // 只有在 add 和 reload 模式下才执行填值规则\r\n  if (action === ACTION_TYPES.ADD || action === ACTION_TYPES.RELOAD) {\r\n    // 判断是否是填值规则表达式，如果是就执行填值规则\r\n    if (fillRuleRegExp.test(defVal)) {\r\n      return await executeRegExp(defVal, fillRuleRegExp, executeFillRuleExpression, [getFormData])\r\n    }\r\n  }\r\n  // 只有在 add 模式下才执行其他表达式\r\n  if (action === ACTION_TYPES.ADD) {\r\n    // 获取并替换所有常规表达式\r\n    defVal = await executeRegExp(defVal, normalRegExp, executeNormalExpression)\r\n    // 获取并替换所有用户自定义表达式\r\n    defVal = await executeRegExp(defVal, customRegExp, executeCustomExpression)\r\n    return defVal\r\n  }\r\n  return null\r\n}\r\n\r\nasync function executeRegExp(defVal, regExp, execFun, otherParams = []) {\r\n  let map = getRegExpMap(defVal, regExp)\r\n  // @ts-ignore\r\n  for (let origin of map.keys()) {\r\n    let exp = map.get(origin)\r\n    let result = await execFun.apply(null, [exp, origin, ...otherParams])\r\n    // 如果只有一个表达式，那么就不替换（因为一旦替换，类型就会被转成String），直接返回执行结果，保证返回的类型不变\r\n    if (origin === defVal) {\r\n      return result\r\n    }\r\n    defVal = replaceAll(defVal, origin, result)\r\n  }\r\n  return defVal\r\n}\r\n\r\n/** 执行【普通表达式】#{xxx} */\r\nasync function executeNormalExpression(expression, origin) {\r\n  let temp = new Date();\r\n  switch (expression) {\r\n    case 'date':\r\n      return formatDate(temp, 'yyyy-MM-dd');\r\n    case 'time':\r\n      return formatDate(temp, 'HH:mm:ss');\r\n    case 'datetime':\r\n      return formatDate(temp, 'yyyy-MM-dd HH:mm:ss');\r\n    default:\r\n      // 获取当前登录用户的信息\r\n      let result = getUserInfoByExpression(expression)\r\n      if (result != null) {\r\n        return result\r\n      }\r\n      // 没有符合条件的表达式，返回原始值\r\n      return origin\r\n  }\r\n}\r\n\r\n/** 根据表达式获取相应的用户信息 */\r\nfunction getUserInfoByExpression(expression) {\r\n  let userInfo:any = useUserStore().userInfo;\r\n  if (userInfo) {\r\n    switch (expression) {\r\n      case 'sysUserId':\r\n    return userInfo.id\r\n    // 当前登录用户登录账号\r\n  case 'sysUserCode':\r\n    return userInfo.username\r\n    // 当前登录用户真实名称\r\n  case 'sysUserName':\r\n    return userInfo.realname\r\n    // 当前登录用户部门编号\r\n  case 'sysOrgCode':\r\n    return userInfo.orgCode\r\n  }\r\n  }\r\n  return null\r\n}\r\n\r\n/** 执行【用户自定义表达式】 {{xxx}} 移动端不支持 */\r\nasync function executeCustomExpression(expression, origin) {\r\n\treturn expression;\r\n  // 利用 eval 生成一个方法，这个方法的参数就是用户自定义的所有的表达式\r\n/*  let fn = eval(`(function (${ceJoin}){ return ${expression} })`)\r\n  try {\r\n    // 然后调用这个方法，并把表达式传递进去，从而完成表达式的执行\r\n    return fn.apply(null, $CE$)\r\n  } catch (e) {\r\n    // 执行失败，输出错误并返回原始值\r\n    logError(e)\r\n    return origin\r\n  } */\r\n}\r\n\r\n/** 执行【填值规则表达式】 ${xxx} */\r\nasync function executeFillRuleExpression(expression, origin, getFormData) {\r\n  let url = `/sys/fillRule/executeRuleByCode/${expression}`\r\n  let formData = {}\r\n  if (typeof getFormData === 'function') {\r\n    formData = getFormData()\r\n  }\r\n  let res:any = await http.put(url, formData)\r\n  let { success, message, result } = res;\r\n  console.log(success, message, result)\r\n  if (success) {\r\n    return result\r\n  } else {\r\n    logError(`填值规则（${expression}）执行失败：${message}`)\r\n    return origin\r\n  }\r\n}\r\n\r\nfunction logWarn(message) {\r\n  console.warn('[loadFieldDefVal]:', message)\r\n}\r\n\r\nfunction logError(message) {\r\n  console.error('[loadFieldDefVal]:', message)\r\n}\r\n\r\nfunction replaceAll(text, checker, replacer) {\r\n  let lastText = text\r\n  text = text.replace(checker, replacer)\r\n  if (lastText !== text) {\r\n    return replaceAll(text, checker, replacer)\r\n  }\r\n  return text\r\n}\r\n\r\n"], "names": ["formatDate", "useUserStore", "http"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAMA,MAAM,mBAAmB,CAAC;AAE1B,MAAM,SAAS,OAAO,KAAK,gBAAgB;AAE5B,OAAO,KAAK,GAAG;AAEjB,OAAO,IAAI,CAAO,QAAA,iBAAiB,GAAG,CAAC;AAGpD,MAAM,eAAe;AAErB,MAAM,eAAe;AAErB,MAAM,iBAAiB;AAGV,MAAA,eAAe,EAAE,KAAK,MAAwD;AAQrE,SAAA,mBAAmB,QAAQ,MAAM,UAAU;AAAA;AAC5D,QAAA,kBAAkB,MAAM,GAAE;AAC3B,UAAI,QAAQ,MAAM,mBAAmB,QAAQ,aAAa,KAAK,EAAE;AAC7D,UAAA,aAAa,QAAQ,OAAO;AACtB,gBAAA,OAAO,WAAW,KAAK;AAAA,MAAA;AAEpC,eAAS,KAAK;AAAA,IAAA;AAAA,EAEf;AAAA;AAKA,SAAS,kBAAkB,KAAK;AAC3B,MAAA,OAAO,QAAQ,GAAE;AACX,WAAA;AAAA,EAAA;AAEF,SAAA;AACT;AAQA,SAAe,mBAAmB,QAAQ,QAAQ,aAAa;AAAA;AAC7D,QAAI,UAAU,MAAM;AAEd,UAAA,oBAAoB,MAAM,GAAG;AAC/B,YAAI,QAAQ,MAAM,gBAAgB,QAAQ,QAAQ,WAAW;AAC7D,YAAI,SAAS,MAAM;AACV,iBAAA;AAAA,QAAA;AAAA,MACT;AAAA,IACF;AAEK,WAAA;AAAA,EACT;AAAA;AAwEO,SAAS,oBAAoB,QAAQ;AAE1C,MAAI,cAAc,GAAG,cAAc,GAAG,gBAAgB;AAC/C,SAAA,QAAQ,gBAAgB,MAAM,eAAe;AACpD,MAAI,gBAAgB,GAAG;AACb,YAAA,OAAO,MAAM,uBAAuB;AACrC,WAAA;AAAA,EAAA;AAEF,SAAA,QAAQ,cAAc,MAAM,aAAa;AACzC,SAAA,QAAQ,cAAc,MAAM,aAAa;AAEhD,MAAI,qBAAqB,cAAc;AACnC,MAAA,gBAAgB,KAAK,qBAAqB,GAAG;AACvC,YAAA,OAAO,MAAM,yBAAyB;AACvC,WAAA;AAAA,EAAA;AAEF,SAAA;AACT;AAGA,SAAS,aAAa,MAAM,KAAK;AAC3B,MAAA,0BAAU,IAAI;AACf,MAAA,QAAQ,KAAK,SAAO,GAAE;AAC1B,SAAK,QAAQ,KAAK,SAAU,OAAO,OAAO,QAAQ,QAAQ;AACvD,UAAI,IAAI,OAAO,MAAM,KAAA,CAAM;AACpB,aAAA;AAAA,IAAA,CACT;AAAA,EAAA;AAEO,SAAA;AACT;AAGA,SAAe,gBAAgB,QAAQ,QAAQ,aAAa;AAAA;AAES;AAE7D,UAAA,eAAe,KAAK,MAAM,GAAG;AAC/B,eAAO,MAAM,cAAc,QAAQ,gBAAgB,2BAA2B,CAAC,WAAW,CAAC;AAAA,MAAA;AAAA,IAC7F;AAG+B;AAE/B,eAAS,MAAM,cAAc,QAAQ,cAAc,uBAAuB;AAE1E,eAAS,MAAM,cAAc,QAAQ,cAAc,uBAAuB;AACnE,aAAA;AAAA,IAAA;AAAA,EAGX;AAAA;AAEA,SAAe,cAAc,IAAQ,IAAQ,IAA2B;AAAA,6CAA3C,QAAQ,QAAQ,SAAS,cAAc,CAAA,GAAI;AAClE,QAAA,MAAM,aAAa,QAAQ,MAAM;AAE5B,aAAA,UAAU,IAAI,QAAQ;AACzB,UAAA,MAAM,IAAI,IAAI,MAAM;AACpB,UAAA,SAAS,MAAM,QAAQ,MAAM,MAAM,CAAC,KAAK,QAAQ,GAAG,WAAW,CAAC;AAEpE,UAAI,WAAW,QAAQ;AACd,eAAA;AAAA,MAAA;AAEA,eAAA,WAAW,QAAQ,QAAQ,MAAM;AAAA,IAAA;AAErC,WAAA;AAAA,EACT;AAAA;AAGA,SAAe,wBAAwB,YAAY,QAAQ;AAAA;AACrD,QAAA,2BAAW,KAAK;AACpB,YAAQ,YAAY;AAAA,MAClB,KAAK;AACI,eAAAA,YAAA,WAAW,MAAM,YAAY;AAAA,MACtC,KAAK;AACI,eAAAA,YAAA,WAAW,MAAM,UAAU;AAAA,MACpC,KAAK;AACI,eAAAA,YAAA,WAAW,MAAM,qBAAqB;AAAA,MAC/C;AAEM,YAAA,SAAS,wBAAwB,UAAU;AAC/C,YAAI,UAAU,MAAM;AACX,iBAAA;AAAA,QAAA;AAGF,eAAA;AAAA,IAAA;AAAA,EAEb;AAAA;AAGA,SAAS,wBAAwB,YAAY;AACvC,MAAA,WAAeC,0BAAe;AAClC,MAAI,UAAU;AACZ,YAAQ,YAAY;AAAA,MAClB,KAAK;AACP,eAAO,SAAS;AAAA,MAElB,KAAK;AACH,eAAO,SAAS;AAAA,MAElB,KAAK;AACH,eAAO,SAAS;AAAA,MAElB,KAAK;AACH,eAAO,SAAS;AAAA,IAAA;AAAA,EAClB;AAEO,SAAA;AACT;AAGA,SAAe,wBAAwB,YAAY,QAAQ;AAAA;AACnD,WAAA;AAAA,EAWR;AAAA;AAGA,SAAe,0BAA0B,YAAY,QAAQ,aAAa;AAAA;AACpE,QAAA,MAAM,mCAAmC,UAAU;AACvD,QAAI,WAAW,CAAC;AACZ,QAAA,OAAO,gBAAgB,YAAY;AACrC,iBAAW,YAAY;AAAA,IAAA;AAEzB,QAAI,MAAU,MAAMC,WAAAA,KAAK,IAAI,KAAK,QAAQ;AAC1C,QAAI,EAAE,SAAS,SAAS,OAAW,IAAA;AAC3B,YAAA,IAAI,SAAS,SAAS,MAAM;AACpC,QAAI,SAAS;AACJ,aAAA;AAAA,IAAA,OACF;AACL,eAAS,QAAQ,UAAU,SAAS,OAAO,EAAE;AACtC,aAAA;AAAA,IAAA;AAAA,EAEX;AAAA;AAEA,SAAS,QAAQ,SAAS;AAChB,UAAA,KAAK,sBAAsB,OAAO;AAC5C;AAEA,SAAS,SAAS,SAAS;AACjB,UAAA,MAAM,sBAAsB,OAAO;AAC7C;AAEA,SAAS,WAAW,MAAM,SAAS,UAAU;AAC3C,MAAI,WAAW;AACR,SAAA,KAAK,QAAQ,SAAS,QAAQ;AACrC,MAAI,aAAa,MAAM;AACd,WAAA,WAAW,MAAM,SAAS,QAAQ;AAAA,EAAA;AAEpC,SAAA;AACT;;"}