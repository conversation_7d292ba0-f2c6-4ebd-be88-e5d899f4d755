{"version": 3, "file": "year-panel.js", "sources": ["../../../../../../../../node_modules/wot-design-uni/components/wd-calendar-view/yearPanel/year-panel.vue", "../../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1jYWxlbmRhci12aWV3L3llYXJQYW5lbC95ZWFyLXBhbmVsLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"wd-year-panel\">\n    <view v-if=\"showPanelTitle\" class=\"wd-year-panel__title\">{{ title }}</view>\n    <scroll-view class=\"wd-year-panel__container\" :style=\"`height: ${scrollHeight}px`\" scroll-y @scroll=\"yearScroll\" :scroll-top=\"scrollTop\">\n      <view v-for=\"(item, index) in years\" :key=\"index\" :id=\"`year${index}`\">\n        <year\n          :type=\"type\"\n          :date=\"item.date\"\n          :value=\"value\"\n          :min-date=\"minDate\"\n          :max-date=\"maxDate\"\n          :max-range=\"maxRange\"\n          :formatter=\"formatter\"\n          :range-prompt=\"rangePrompt\"\n          :allow-same-day=\"allowSameDay\"\n          :default-time=\"defaultTime\"\n          :showTitle=\"index !== 0\"\n          @change=\"handleDateChange\"\n        />\n      </view>\n    </scroll-view>\n  </view>\n</template>\n<script lang=\"ts\">\nexport default {\n  options: {\n    addGlobalClass: true,\n    virtualHost: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport { computed, ref, onMounted } from 'vue'\nimport { compareYear, formatYearTitle, getYears } from '../utils'\nimport { isArray, isNumber, pause } from '../../common/util'\nimport Year from '../year/year.vue'\nimport { yearPanelProps, type YearInfo, type YearPanelExpose } from './types'\n\nconst props = defineProps(yearPanelProps)\nconst emit = defineEmits(['change'])\n\nconst scrollTop = ref<number>(0) // 滚动位置\nconst scrollIndex = ref<number>(0) // 当前显示的年份索引\n\n// 滚动区域的高度\nconst scrollHeight = computed(() => {\n  const scrollHeight: number = props.panelHeight + (props.showPanelTitle ? 26 : 16)\n  return scrollHeight\n})\n\n// 年份信息\nconst years = computed<YearInfo[]>(() => {\n  return getYears(props.minDate, props.maxDate).map((year, index) => {\n    return {\n      date: year,\n      height: index === 0 ? 200 : 245\n    }\n  })\n})\n\n// 标题\nconst title = computed(() => {\n  return formatYearTitle(years.value[scrollIndex.value].date)\n})\n\nonMounted(() => {\n  scrollIntoView()\n})\n\nasync function scrollIntoView() {\n  await pause()\n  let activeDate: number | null = null\n  if (isArray(props.value)) {\n    activeDate = props.value![0]\n  } else if (isNumber(props.value)) {\n    activeDate = props.value\n  }\n\n  if (!activeDate) {\n    activeDate = Date.now()\n  }\n\n  let top: number = 0\n  for (let index = 0; index < years.value.length; index++) {\n    if (compareYear(years.value[index].date, activeDate) === 0) {\n      break\n    }\n    top += years.value[index] ? Number(years.value[index].height) : 0\n  }\n  scrollTop.value = 0\n  if (top > 0) {\n    await pause()\n    scrollTop.value = top + 45\n  }\n}\n\nconst yearScroll = (event: { detail: { scrollTop: number } }) => {\n  if (years.value.length <= 1) {\n    return\n  }\n  const scrollTop = Math.max(0, event.detail.scrollTop)\n  doSetSubtitle(scrollTop)\n}\n\n/**\n * 设置小标题\n * scrollTop 滚动条位置\n */\nfunction doSetSubtitle(scrollTop: number) {\n  let height: number = 0 // 月份高度和\n  for (let index = 0; index < years.value.length; index++) {\n    height = height + years.value[index].height\n    if (scrollTop < height) {\n      scrollIndex.value = index\n      return\n    }\n  }\n}\n\nfunction handleDateChange({ value }: { value: number[] }) {\n  emit('change', {\n    value\n  })\n}\n\ndefineExpose<YearPanelExpose>({\n  scrollIntoView\n})\n</script>\n\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-calendar-view/yearPanel/year-panel.vue'\nwx.createComponent(Component)"], "names": ["ref", "computed", "scrollHeight", "getYears", "formatYearTitle", "onMounted", "pause", "isArray", "isNumber", "compareYear", "scrollTop"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA,MAAA,OAAiB,MAAA;AAbjB,MAAe,cAAA;AAAA,EACb,SAAS;AAAA,IACP,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,gBAAgB;AAAA,EAAA;AAEpB;;;;;;AAUA,UAAM,QAAQ;AACd,UAAM,OAAO;AAEP,UAAA,YAAYA,kBAAY,CAAC;AACzB,UAAA,cAAcA,kBAAY,CAAC;AAG3B,UAAA,eAAeC,cAAAA,SAAS,MAAM;AAClC,YAAMC,gBAAuB,MAAM,eAAe,MAAM,iBAAiB,KAAK;AACvEA,aAAAA;AAAAA,IAAA,CACR;AAGK,UAAA,QAAQD,cAAAA,SAAqB,MAAM;AAChC,aAAAE,cAAA,SAAS,MAAM,SAAS,MAAM,OAAO,EAAE,IAAI,CAAC,MAAM,UAAU;AAC1D,eAAA;AAAA,UACL,MAAM;AAAA,UACN,QAAQ,UAAU,IAAI,MAAM;AAAA,QAC9B;AAAA,MAAA,CACD;AAAA,IAAA,CACF;AAGK,UAAA,QAAQF,cAAAA,SAAS,MAAM;AAC3B,aAAOG,cAAAA,gBAAgB,MAAM,MAAM,YAAY,KAAK,EAAE,IAAI;AAAA,IAAA,CAC3D;AAEDC,kBAAAA,UAAU,MAAM;AACC,qBAAA;AAAA,IAAA,CAChB;AAED,aAAe,iBAAiB;AAAA;AAC9B,cAAMC,oBAAM;AACZ,YAAI,aAA4B;AAC5B,YAAAC,cAAA,QAAQ,MAAM,KAAK,GAAG;AACX,uBAAA,MAAM,MAAO,CAAC;AAAA,QAClB,WAAAC,cAAA,SAAS,MAAM,KAAK,GAAG;AAChC,uBAAa,MAAM;AAAA,QAAA;AAGrB,YAAI,CAAC,YAAY;AACf,uBAAa,KAAK,IAAI;AAAA,QAAA;AAGxB,YAAI,MAAc;AAClB,iBAAS,QAAQ,GAAG,QAAQ,MAAM,MAAM,QAAQ,SAAS;AACnD,cAAAC,cAAA,YAAY,MAAM,MAAM,KAAK,EAAE,MAAM,UAAU,MAAM,GAAG;AAC1D;AAAA,UAAA;AAEK,iBAAA,MAAM,MAAM,KAAK,IAAI,OAAO,MAAM,MAAM,KAAK,EAAE,MAAM,IAAI;AAAA,QAAA;AAElE,kBAAU,QAAQ;AAClB,YAAI,MAAM,GAAG;AACX,gBAAMH,oBAAM;AACZ,oBAAU,QAAQ,MAAM;AAAA,QAAA;AAAA,MAC1B;AAAA;AAGI,UAAA,aAAa,CAAC,UAA6C;AAC3D,UAAA,MAAM,MAAM,UAAU,GAAG;AAC3B;AAAA,MAAA;AAEF,YAAMI,aAAY,KAAK,IAAI,GAAG,MAAM,OAAO,SAAS;AACpD,oBAAcA,UAAS;AAAA,IACzB;AAMA,aAAS,cAAcA,YAAmB;AACxC,UAAI,SAAiB;AACrB,eAAS,QAAQ,GAAG,QAAQ,MAAM,MAAM,QAAQ,SAAS;AACvD,iBAAS,SAAS,MAAM,MAAM,KAAK,EAAE;AACrC,YAAIA,aAAY,QAAQ;AACtB,sBAAY,QAAQ;AACpB;AAAA,QAAA;AAAA,MACF;AAAA,IACF;AAGO,aAAA,iBAAiB,EAAE,SAA8B;AACxD,WAAK,UAAU;AAAA,QACb;AAAA,MAAA,CACD;AAAA,IAAA;AAG2B,aAAA;AAAA,MAC5B;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChID,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}