/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 辅助函数
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/* 转换成字符串 */
/* 判断是否存在 Modifier */
/* 判断是否存在伪类 */
/**
 * 主题色切换
 * @params $theme-color 主题色
 * @params $type 变暗’dark‘ 变亮 'light'
 * @params $mix-color 自己设置的混色
 */
/**
 * 颜色结果切换， 如果开启线性渐变色 使用渐变色，如果没有开启，那么使用主题色
 * @params $open-linear 是否开启线性渐变色
 * @params $deg 渐变色角度
 * @params $theme-color 当前配色
 * @params [Array] $set 主题色明暗设置，与 $color-list 数量对应
 * @params [Array] $color-list 渐变色顺序， $color-list 和 $per-list 数量相同
 * @params [Array] $per-list 渐变色比例
 */
/**
 * UI规范基础变量
 */
/*----------------------------------------- Theme color. start ----------------------------------------*/
/* 主题颜色 */
/* 辅助色 */
/* 文字颜色（默认浅色背景下 */
/* 暗黑模式 */
/* 图形颜色 */
/*----------------------------------------- Theme color. end -------------------------------------------*/
/*-------------------------------- Theme color application size.  start --------------------------------*/
/* 文字字号 */
/* 文字字重 */
/* 尺寸 */
/*-------------------------------- Theme color application size.  end --------------------------------*/
/* component var */
/* action-sheet */
/* badge */
/* button */
/* cell */
/* calendar */
/* checkbox */
/* collapse */
/* divider */
/* drop-menu */
/* input-number */
/* input */
/* textarea */
/* loadmore */
/* message-box */
/* notice-bar */
/* pagination */
/* picker */
/* col-picker */
/* overlay */
/* popup */
/* progress */
/* radio */
/* search */
/* slider */
/* sort-button */
/* steps */
/* switch */
/* tabs */
/* tag */
/* toast */
/* loading */
/* tooltip */
/* popover */
/* grid-item */
/* statustip */
/* card */
/* upload */
/* curtain */
/* notify */
/* skeleton */
/* circle */
/* swiper */
/* swiper-nav */
/* segmented */
/* tabbar */
/* tabbar-item */
/* navbar */
/* navbar-capsule */
/* table */
/* sidebar */
/* sidebar-item */
/* fab */
/* count-down */
/* keyboard */
/* number-keyboard */
/* passwod-input */
/* form-item */
/* backtop */
/* index-bar */
/* text */
/* video-preview */
/* img-cropper */
/* floating-panel */
/* signature */
/**
 * 混合宏
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/**
 * 辅助函数
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/* 转换成字符串 */
/* 判断是否存在 Modifier */
/* 判断是否存在伪类 */
/**
 * 主题色切换
 * @params $theme-color 主题色
 * @params $type 变暗’dark‘ 变亮 'light'
 * @params $mix-color 自己设置的混色
 */
/**
 * 颜色结果切换， 如果开启线性渐变色 使用渐变色，如果没有开启，那么使用主题色
 * @params $open-linear 是否开启线性渐变色
 * @params $deg 渐变色角度
 * @params $theme-color 当前配色
 * @params [Array] $set 主题色明暗设置，与 $color-list 数量对应
 * @params [Array] $color-list 渐变色顺序， $color-list 和 $per-list 数量相同
 * @params [Array] $per-list 渐变色比例
 */
/**
  * BEM，定义块（b)
  */
/* 定义元素（e），对于伪类，会自动将 e 嵌套在 伪类 底下 */
/* 此方法用于生成穿透样式 */
/* 定义元素（e），对于伪类，会自动将 e 嵌套在 伪类 底下 */
/* 定义状态（m） */
/* 定义状态（m） */
/* 对于需要需要嵌套在 m 底下的 e，调用这个混合宏，一般在切换整个组件的状态，如切换颜色的时候 */
/* 状态，生成 is-$state 类名 */
/**
  * 常用混合宏
  */
/* 单行超出隐藏 */
/* 多行超出隐藏 */
/* 清除浮动 */
/* 0.5px 边框 指定方向*/
/* 0.5px 边框 环绕 */
/**
  * 三角形实现尖角样式，适用于背景透明情况
  * @param $size 三角形高，底边为 $size * 2
  * @param $bg 三角形背景颜色
  */
/**
  * 正方形实现尖角样式，适用于背景不透明情况
  * @param $size 正方形边长
  * @param $bg 正方形背景颜色
  * @param $z-index z-index属性值，不得大于外部包裹器
  * @param $box-shadow 阴影
*/
.wot-theme-dark .wd-textarea.data-v-324b46d3 {
  background: var(--wot-dark-background2, #1b1b1b);
}
.wot-theme-dark .wd-textarea.data-v-324b46d3::after {
  background: var(--wot-dark-color-gray, var(--wot-color-secondary, #595959));
}
.wot-theme-dark .wd-textarea.is-not-empty.data-v-324b46d3:not(.is-disabled)::after {
  background-color: var(--wot-dark-color, var(--wot-color-white, rgb(255, 255, 255)));
}
.wot-theme-dark .wd-textarea__value.data-v-324b46d3 {
  background: var(--wot-dark-background2, #1b1b1b);
}
.wot-theme-dark .wd-textarea__inner.data-v-324b46d3 {
  color: var(--wot-dark-color, var(--wot-color-white, rgb(255, 255, 255)));
}
.wot-theme-dark .wd-textarea__inner.data-v-324b46d3::-webkit-input-placeholder {
  color: var(--wot-dark-color3, rgba(232, 230, 227, 0.8));
}
.wot-theme-dark .wd-textarea__count.data-v-324b46d3 {
  color: var(--wot-dark-color3, rgba(232, 230, 227, 0.8));
  background: transparent;
}
.wot-theme-dark .wd-textarea__count-current.data-v-324b46d3 {
  color: var(--wot-dark-color, var(--wot-color-white, rgb(255, 255, 255)));
}
.wot-theme-dark .wd-textarea.data-v-324b46d3 .wd-textarea__icon,
.wot-theme-dark .wd-textarea.data-v-324b46d3 .wd-textarea__clear {
  color: var(--wot-dark-color, var(--wot-color-white, rgb(255, 255, 255)));
  background: transparent;
}
.wot-theme-dark .wd-textarea.is-cell.data-v-324b46d3 {
  background-color: var(--wot-dark-background2, #1b1b1b);
}
.wot-theme-dark .wd-textarea.is-cell.is-border.data-v-324b46d3 {
  position: relative;
}
.wot-theme-dark .wd-textarea.is-cell.is-border.data-v-324b46d3::after {
  position: absolute;
  display: block;
  content: "";
  width: calc(100% - var(--wot-textarea-cell-padding, 10px));
  height: 1px;
  left: var(--wot-textarea-cell-padding, 10px);
  top: 0;
  transform: scaleY(0.5);
  background: var(--wot-dark-border-color, #3a3a3c);
}
.wot-theme-dark .wd-textarea.is-disabled .wd-textarea__inner.data-v-324b46d3 {
  color: var(--wot-dark-color-gray, var(--wot-color-secondary, #595959));
  background: transparent;
}
.wot-theme-dark .wd-textarea__label.data-v-324b46d3 {
  color: var(--wot-dark-color, var(--wot-color-white, rgb(255, 255, 255)));
}
.wd-textarea.data-v-324b46d3 {
  position: relative;
  -webkit-tap-highlight-color: transparent;
  text-align: left;
  background: var(--wot-textarea-bg, var(--wot-color-white, rgb(255, 255, 255)));
  padding: var(--wot-textarea-cell-padding, 10px) var(--wot-textarea-padding, var(--wot-size-side-padding, 15px));
}
.wd-textarea.data-v-324b46d3::after {
  position: absolute;
  display: none;
  content: "";
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--wot-textarea-border-color, #dadada);
  transform: scaleY(0.5);
  transition: background-color 0.2s ease-in-out;
}
.wd-textarea__label.data-v-324b46d3 {
  position: relative;
  display: flex;
  width: var(--wot-input-cell-label-width, 33%);
  color: var(--wot-cell-title-color, rgba(0, 0, 0, 0.85));
  margin-right: var(--wot-cell-padding, var(--wot-size-side-padding, 15px));
  box-sizing: border-box;
  font-size: var(--wot-textarea-fs, var(--wot-cell-title-fs, 14px));
  flex-shrink: 0;
}
.wd-textarea__label.is-required.data-v-324b46d3 {
  padding-left: 12px;
}
.wd-textarea__label.is-required.data-v-324b46d3::after {
  position: absolute;
  left: 0;
  top: 2px;
  content: "*";
  font-size: var(--wot-cell-required-size, 18px);
  line-height: 1.1;
  color: var(--wot-cell-required-color, var(--wot-color-danger, #fa4350));
}
.wd-textarea__label-inner.data-v-324b46d3 {
  display: inline-block;
  line-height: var(--wot-cell-line-height, 24px);
  font-size: var(--wot-textarea-fs, var(--wot-cell-title-fs, 14px));
}
.wd-textarea__prefix.data-v-324b46d3 {
  margin-right: var(--wot-textarea-icon-margin, 8px);
  font-size: var(--wot-textarea-fs, var(--wot-cell-title-fs, 14px));
  line-height: initial;
}
.wd-textarea__prefix.data-v-324b46d3 .wd-textarea__icon {
  margin-left: 0;
}
.wd-textarea__suffix.data-v-324b46d3 {
  flex-shrink: 0;
  line-height: initial;
}
.wd-textarea__value.data-v-324b46d3 {
  position: relative;
  padding: 0;
  font-size: 0;
  background: var(--wot-textarea-bg, var(--wot-color-white, rgb(255, 255, 255)));
  box-sizing: border-box;
}
.wd-textarea__value.is-show-limit.data-v-324b46d3 {
  padding-bottom: 36px;
}
.wd-textarea__value.is-suffix.data-v-324b46d3 {
  padding-right: calc(var(--wot-textarea-icon-size, 16px) + 8px);
}
.wd-textarea__inner.data-v-324b46d3 {
  padding: 0;
  width: 100%;
  font-size: var(--wot-textarea-fs, var(--wot-cell-title-fs, 14px));
  line-height: var(--wot-cell-line-height, 24px);
  color: var(--wot-textarea-color, #262626);
  outline: none;
  background: none;
  border: none;
  box-sizing: border-box;
  word-break: break-word;
  min-height: 24px;
}
.wd-textarea__inner.data-v-324b46d3::-webkit-input-placeholder {
  color: var(--wot-input-placeholder-color, #bfbfbf);
}
.wd-textarea__suffix.data-v-324b46d3 {
  position: absolute;
  z-index: 1;
  right: 0;
  top: 0;
  bottom: 0;
}
.data-v-324b46d3  .wd-textarea__icon {
  margin-left: var(--wot-textarea-icon-margin, 8px);
  font-size: var(--wot-textarea-icon-size, 16px);
  color: var(--wot-textarea-icon-color, #bfbfbf);
  background: var(--wot-textarea-bg, var(--wot-color-white, rgb(255, 255, 255)));
}
.data-v-324b46d3  .wd-textarea__clear {
  margin-left: var(--wot-textarea-icon-margin, 8px);
  font-size: var(--wot-textarea-icon-size, 16px);
  color: var(--wot-textarea-clear-color, #585858);
  vertical-align: middle;
  background: var(--wot-textarea-bg, var(--wot-color-white, rgb(255, 255, 255)));
  line-height: var(--wot-cell-line-height, 24px);
}
.wd-textarea__count.data-v-324b46d3 {
  position: absolute;
  bottom: 8px;
  right: 0;
  font-size: var(--wot-textarea-count-fs, 14px);
  color: var(--wot-textarea-count-color, #bfbfbf);
  background: var(--wot-textarea-bg, var(--wot-color-white, rgb(255, 255, 255)));
  line-height: 20px;
  display: inline-flex;
}
.wd-textarea__count-current.data-v-324b46d3 {
  color: var(--wot-textarea-count-current-color, #262626);
}
.wd-textarea__count-current.is-error.data-v-324b46d3 {
  color: var(--wot-input-error-color, var(--wot-color-danger, #fa4350));
}
.wd-textarea__readonly-mask.data-v-324b46d3 {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  width: 100%;
  height: 100%;
}
.wd-textarea__error-message.data-v-324b46d3 {
  color: var(--wot-form-item-error-message-color, var(--wot-color-danger, #fa4350));
  font-size: var(--wot-form-item-error-message-font-size, var(--wot-fs-secondary, 12px));
  line-height: var(--wot-form-item-error-message-line-height, 24px);
  text-align: left;
  vertical-align: middle;
}
.wd-textarea.is-not-empty.data-v-324b46d3:not(.is-disabled)::after {
  background-color: var(--wot-textarea-not-empty-border-color, #262626);
}
.wd-textarea.is-disabled .wd-textarea__inner.data-v-324b46d3 {
  color: var(--wot-input-disabled-color, #d9d9d9);
  background: transparent;
}
.wd-textarea.is-error .wd-textarea__inner.data-v-324b46d3 {
  color: var(--wot-input-error-color, var(--wot-color-danger, #fa4350));
  background: transparent;
}
.wd-textarea.is-auto-height.data-v-324b46d3:not(.is-cell) {
  padding: 5px 0;
}
.wd-textarea.is-auto-height.data-v-324b46d3::after {
  display: block;
}
.wd-textarea.is-no-border.data-v-324b46d3::after {
  display: none;
}
.wd-textarea.is-cell.data-v-324b46d3 {
  display: flex;
  line-height: var(--wot-cell-line-height, 24px);
}
.wd-textarea.is-cell.is-error.data-v-324b46d3::after {
  background: var(--wot-textarea-cell-border-color, var(--wot-color-border-light, #e8e8e8));
}
.wd-textarea.is-cell .wd-textarea__value.data-v-324b46d3 {
  flex: 1;
}
.wd-textarea.is-cell.data-v-324b46d3 .wd-textarea__icon {
  display: inline-flex;
  align-items: center;
  height: var(--wot-textarea-cell-height, 24px);
  line-height: var(--wot-textarea-cell-height, 24px);
}
.wd-textarea.is-cell .wd-textarea__prefix.data-v-324b46d3 {
  display: inline-block;
  margin-right: var(--wot-cell-icon-right, 4px);
}
.wd-textarea.is-cell.wd-textarea.data-v-324b46d3::after {
  display: none;
}
.wd-textarea.is-cell .wd-textarea__suffix.data-v-324b46d3 {
  right: 0;
}
.wd-textarea.is-cell.is-center.data-v-324b46d3 {
  align-items: center;
}
.wd-textarea.is-cell.is-border.data-v-324b46d3 {
  position: relative;
}
.wd-textarea.is-cell.is-border.data-v-324b46d3::after {
  position: absolute;
  display: block;
  content: "";
  width: calc(100% - var(--wot-textarea-cell-padding, 10px));
  height: 1px;
  left: var(--wot-textarea-cell-padding, 10px);
  top: 0;
  transform: scaleY(0.5);
  background: var(--wot-color-border-light, #e8e8e8);
}
.wd-textarea.is-large.data-v-324b46d3 {
  padding: var(--wot-textarea-cell-padding-large, 12px);
}
.wd-textarea.is-large .wd-textarea__prefix.data-v-324b46d3 {
  font-size: var(--wot-textarea-fs-large, var(--wot-cell-title-fs-large, 16px));
}
.wd-textarea.is-large .wd-textarea__label-inner.data-v-324b46d3 {
  font-size: var(--wot-textarea-fs-large, var(--wot-cell-title-fs-large, 16px));
}
.wd-textarea.is-large .wd-textarea__inner.data-v-324b46d3 {
  font-size: var(--wot-textarea-fs-large, var(--wot-cell-title-fs-large, 16px));
}
.wd-textarea.is-large .wd-textarea__count.data-v-324b46d3 {
  font-size: var(--wot-textarea-count-fs-large, 14px);
}
.wd-textarea.is-large.data-v-324b46d3 .wd-textarea__icon,
.wd-textarea.is-large.data-v-324b46d3 .wd-textarea__clear {
  font-size: var(--wot-textarea-icon-size-large, 18px);
}
/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 辅助函数
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/* 转换成字符串 */
/* 判断是否存在 Modifier */
/* 判断是否存在伪类 */
/**
 * 主题色切换
 * @params $theme-color 主题色
 * @params $type 变暗’dark‘ 变亮 'light'
 * @params $mix-color 自己设置的混色
 */
/**
 * 颜色结果切换， 如果开启线性渐变色 使用渐变色，如果没有开启，那么使用主题色
 * @params $open-linear 是否开启线性渐变色
 * @params $deg 渐变色角度
 * @params $theme-color 当前配色
 * @params [Array] $set 主题色明暗设置，与 $color-list 数量对应
 * @params [Array] $color-list 渐变色顺序， $color-list 和 $per-list 数量相同
 * @params [Array] $per-list 渐变色比例
 */
/**
 * UI规范基础变量
 */
/*----------------------------------------- Theme color. start ----------------------------------------*/
/* 主题颜色 */
/* 辅助色 */
/* 文字颜色（默认浅色背景下 */
/* 暗黑模式 */
/* 图形颜色 */
/*----------------------------------------- Theme color. end -------------------------------------------*/
/*-------------------------------- Theme color application size.  start --------------------------------*/
/* 文字字号 */
/* 文字字重 */
/* 尺寸 */
/*-------------------------------- Theme color application size.  end --------------------------------*/
/* component var */
/* action-sheet */
/* badge */
/* button */
/* cell */
/* calendar */
/* checkbox */
/* collapse */
/* divider */
/* drop-menu */
/* input-number */
/* input */
/* textarea */
/* loadmore */
/* message-box */
/* notice-bar */
/* pagination */
/* picker */
/* col-picker */
/* overlay */
/* popup */
/* progress */
/* radio */
/* search */
/* slider */
/* sort-button */
/* steps */
/* switch */
/* tabs */
/* tag */
/* toast */
/* loading */
/* tooltip */
/* popover */
/* grid-item */
/* statustip */
/* card */
/* upload */
/* curtain */
/* notify */
/* skeleton */
/* circle */
/* swiper */
/* swiper-nav */
/* segmented */
/* tabbar */
/* tabbar-item */
/* navbar */
/* navbar-capsule */
/* table */
/* sidebar */
/* sidebar-item */
/* fab */
/* count-down */
/* keyboard */
/* number-keyboard */
/* passwod-input */
/* form-item */
/* backtop */
/* index-bar */
/* text */
/* video-preview */
/* img-cropper */
/* floating-panel */
/* signature */
/**
 * 混合宏
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/**
 * 辅助函数
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/* 转换成字符串 */
/* 判断是否存在 Modifier */
/* 判断是否存在伪类 */
/**
 * 主题色切换
 * @params $theme-color 主题色
 * @params $type 变暗’dark‘ 变亮 'light'
 * @params $mix-color 自己设置的混色
 */
/**
 * 颜色结果切换， 如果开启线性渐变色 使用渐变色，如果没有开启，那么使用主题色
 * @params $open-linear 是否开启线性渐变色
 * @params $deg 渐变色角度
 * @params $theme-color 当前配色
 * @params [Array] $set 主题色明暗设置，与 $color-list 数量对应
 * @params [Array] $color-list 渐变色顺序， $color-list 和 $per-list 数量相同
 * @params [Array] $per-list 渐变色比例
 */
/**
  * BEM，定义块（b)
  */
/* 定义元素（e），对于伪类，会自动将 e 嵌套在 伪类 底下 */
/* 此方法用于生成穿透样式 */
/* 定义元素（e），对于伪类，会自动将 e 嵌套在 伪类 底下 */
/* 定义状态（m） */
/* 定义状态（m） */
/* 对于需要需要嵌套在 m 底下的 e，调用这个混合宏，一般在切换整个组件的状态，如切换颜色的时候 */
/* 状态，生成 is-$state 类名 */
/**
  * 常用混合宏
  */
/* 单行超出隐藏 */
/* 多行超出隐藏 */
/* 清除浮动 */
/* 0.5px 边框 指定方向*/
/* 0.5px 边框 环绕 */
/**
  * 三角形实现尖角样式，适用于背景透明情况
  * @param $size 三角形高，底边为 $size * 2
  * @param $bg 三角形背景颜色
  */
/**
  * 正方形实现尖角样式，适用于背景不透明情况
  * @param $size 正方形边长
  * @param $bg 正方形背景颜色
  * @param $z-index z-index属性值，不得大于外部包裹器
  * @param $box-shadow 阴影
*/
.wot-theme-dark .wd-textarea__placeholder {
  color: var(--wot-dark-color3, rgba(232, 230, 227, 0.8));
}
.wd-textarea__placeholder {
  color: var(--wot-input-placeholder-color, #bfbfbf);
}
.wd-textarea__placeholder.is-error {
  color: var(--wot-input-error-color, var(--wot-color-danger, #fa4350));
}