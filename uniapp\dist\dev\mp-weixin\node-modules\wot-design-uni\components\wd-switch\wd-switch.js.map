{"version": 3, "file": "wd-switch.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-switch/wd-switch.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1zd2l0Y2gvd2Qtc3dpdGNoLnZ1ZQ"], "sourcesContent": ["<template>\n  <view :class=\"rootClass\" :style=\"rootStyle\" @click=\"switchValue\">\n    <view class=\"wd-switch__circle\" :style=\"circleStyle\"></view>\n  </view>\n</template>\n<script lang=\"ts\">\nexport default {\n  name: 'wd-switch',\n  options: {\n    addGlobalClass: true,\n    virtualHost: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport { computed, type CSSProperties, onBeforeMount } from 'vue'\nimport { addUnit, isFunction, objToStyle } from '../common/util'\nimport { switchProps } from './types'\n\nconst props = defineProps(switchProps)\nconst emit = defineEmits(['change', 'update:modelValue'])\n\nconst rootClass = computed(() => {\n  return `wd-switch ${props.customClass} ${props.disabled ? 'is-disabled' : ''} ${props.modelValue === props.activeValue ? 'is-checked' : ''}`\n})\n\nconst rootStyle = computed(() => {\n  const rootStyle: CSSProperties = {\n    background: props.modelValue === props.activeValue ? props.activeColor : props.inactiveColor,\n    'border-color': props.modelValue === props.activeValue ? props.activeColor : props.inactiveColor\n  }\n  if (props.size) {\n    rootStyle['font-size'] = addUnit(props.size)\n  }\n  return `${objToStyle(rootStyle)}${props.customStyle}`\n})\n\nconst circleStyle = computed(() => {\n  const circleStyle: string =\n    (props.modelValue === props.activeValue && props.activeColor) || (props.modelValue !== props.activeValue && props.inactiveColor)\n      ? 'box-shadow: none;'\n      : ''\n  return circleStyle\n})\n\nfunction switchValue() {\n  if (props.disabled) return\n  const newVal = props.modelValue === props.activeValue ? props.inactiveValue : props.activeValue\n\n  if (props.beforeChange && isFunction(props.beforeChange)) {\n    props.beforeChange({\n      value: newVal,\n      resolve: (pass: boolean) => {\n        if (pass) {\n          emit('update:modelValue', newVal)\n          emit('change', {\n            value: newVal\n          })\n        }\n      }\n    })\n  } else {\n    emit('update:modelValue', newVal)\n    emit('change', {\n      value: newVal\n    })\n  }\n}\n\nonBeforeMount(() => {\n  if ([props.activeValue, props.inactiveValue].indexOf(props.modelValue) === -1) {\n    emit('update:modelValue', props.inactiveValue)\n    emit('change', {\n      value: props.inactiveValue\n    })\n  }\n})\n</script>\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-switch/wd-switch.vue'\nwx.createComponent(Component)"], "names": ["computed", "rootStyle", "addUnit", "objToStyle", "circleStyle", "isFunction", "onBeforeMount"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAMA,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,gBAAgB;AAAA,EAAA;AAEpB;;;;;AAQA,UAAM,QAAQ;AACd,UAAM,OAAO;AAEP,UAAA,YAAYA,cAAAA,SAAS,MAAM;AAC/B,aAAO,aAAa,MAAM,WAAW,IAAI,MAAM,WAAW,gBAAgB,EAAE,IAAI,MAAM,eAAe,MAAM,cAAc,eAAe,EAAE;AAAA,IAAA,CAC3I;AAEK,UAAA,YAAYA,cAAAA,SAAS,MAAM;AAC/B,YAAMC,aAA2B;AAAA,QAC/B,YAAY,MAAM,eAAe,MAAM,cAAc,MAAM,cAAc,MAAM;AAAA,QAC/E,gBAAgB,MAAM,eAAe,MAAM,cAAc,MAAM,cAAc,MAAM;AAAA,MACrF;AACA,UAAI,MAAM,MAAM;AACdA,mBAAU,WAAW,IAAIC,sBAAQ,MAAM,IAAI;AAAA,MAAA;AAE7C,aAAO,GAAGC,cAAAA,WAAWF,UAAS,CAAC,GAAG,MAAM,WAAW;AAAA,IAAA,CACpD;AAEK,UAAA,cAAcD,cAAAA,SAAS,MAAM;AACjC,YAAMI,eACH,MAAM,eAAe,MAAM,eAAe,MAAM,eAAiB,MAAM,eAAe,MAAM,eAAe,MAAM,gBAC9G,sBACA;AACCA,aAAAA;AAAAA,IAAA,CACR;AAED,aAAS,cAAc;AACrB,UAAI,MAAM;AAAU;AACpB,YAAM,SAAS,MAAM,eAAe,MAAM,cAAc,MAAM,gBAAgB,MAAM;AAEpF,UAAI,MAAM,gBAAgBC,cAAW,WAAA,MAAM,YAAY,GAAG;AACxD,cAAM,aAAa;AAAA,UACjB,OAAO;AAAA,UACP,SAAS,CAAC,SAAkB;AAC1B,gBAAI,MAAM;AACR,mBAAK,qBAAqB,MAAM;AAChC,mBAAK,UAAU;AAAA,gBACb,OAAO;AAAA,cAAA,CACR;AAAA,YAAA;AAAA,UACH;AAAA,QACF,CACD;AAAA,MAAA,OACI;AACL,aAAK,qBAAqB,MAAM;AAChC,aAAK,UAAU;AAAA,UACb,OAAO;AAAA,QAAA,CACR;AAAA,MAAA;AAAA,IACH;AAGFC,kBAAAA,cAAc,MAAM;AACd,UAAA,CAAC,MAAM,aAAa,MAAM,aAAa,EAAE,QAAQ,MAAM,UAAU,MAAM,IAAI;AACxE,aAAA,qBAAqB,MAAM,aAAa;AAC7C,aAAK,UAAU;AAAA,UACb,OAAO,MAAM;AAAA,QAAA,CACd;AAAA,MAAA;AAAA,IACH,CACD;;;;;;;;;;;;AC7ED,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}