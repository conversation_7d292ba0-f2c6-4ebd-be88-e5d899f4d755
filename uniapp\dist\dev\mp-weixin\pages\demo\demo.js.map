{"version": 3, "file": "demo.js", "sources": ["../../../../../src/pages/demo/demo.vue", "../../../../../uniPage:/cGFnZXMvZGVtby9kZW1vLnZ1ZQ"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n  <PageLayout backRouteName=\"index\" navTitle=\"组件示例\" routeMethod=\"pushTab\">\r\n    <scroll-view scroll-y>\r\n      <view class=\"box shadow-warp\">\r\n        <div class=\"content\">\r\n          <SelectUser label=\"用户：\" :required=\"true\" v-model=\"user\"></SelectUser>\r\n        </div>\r\n      </view>\r\n      <view class=\"box shadow-warp\">\r\n        <div class=\"content\">\r\n          <SelectDept label=\"部门：\" :required=\"true\" v-model=\"dept\"></SelectDept>\r\n        </div>\r\n      </view>\r\n      <view class=\"box shadow-warp\">\r\n        <div class=\"content\">\r\n          <!-- <view class=\"title mb-2\">流程进度图组件</view> -->\r\n          <ProgressMap title=\"流程历史跟踪\" :dataSource=\"proDataSource\"></ProgressMap>\r\n        </div>\r\n      </view>\r\n      <view class=\"box shadow-warp\" v-for=\"(item, index) in dataSource\">\r\n        <view class=\"content\">\r\n          <template v-if=\"index === 0\">\r\n            <!-- <view class=\"title\">万年历组件</view> -->\r\n            <uni-calendar\r\n              :showMonth=\"true\"\r\n              @change=\"change\"\r\n              @monthChange=\"monthChange\"\r\n              :selected=\"selected\"\r\n            />\r\n          </template>\r\n          <template v-else>\r\n            <view class=\"title\">{{ item.title }}</view>\r\n            <template v-if=\"['图片预览'].includes(item.title)\">\r\n              <wd-img\r\n                custom-class=\"imgView\"\r\n                :width=\"220\"\r\n                :height=\"120\"\r\n                src=\"https://jeecgos.oss-cn-beijing.aliyuncs.com/files/site/projectCase/mini/banner/10bdc1.jpg\"\r\n                @click=\"() => (imgPreview.show = true)\"\r\n              ></wd-img>\r\n              <ImgPreview\r\n                v-if=\"imgPreview.show\"\r\n                :urls=\"imgPreview.urls\"\r\n                @close=\"() => (imgPreview.show = false)\"\r\n              ></ImgPreview>\r\n            </template>\r\n            <wd-button v-else @click=\"handleSkip(item.path)\">跳转页面</wd-button>\r\n          </template>\r\n        </view>\r\n      </view>\r\n      <view class=\"box router shadow-warp\">\r\n        <wd-button @click=\"handleSkip('/pages/demo/tree')\">树组件</wd-button>\r\n        <wd-button @click=\"handleSkip('/pages/demo/indexBar')\">通讯录</wd-button>\r\n        <wd-button @click=\"handleSkip('/pages/demo/selectPicker')\">单选多选</wd-button>\r\n        <wd-button @click=\"handleSkip('/pages/demo/form')\">表单</wd-button>\r\n      </view>\r\n      <view class=\"box gridBox shadow-warp\">\r\n        <view class=\"content\">\r\n          <!-- <view class=\"title\">九宫格</view> -->\r\n          <Grid\r\n            v-model=\"gridData\"\r\n            :column=\"3\"\r\n            @itemClik=\"(item) => toast.info(`点击了${item.text}`)\"\r\n          ></Grid>\r\n        </view>\r\n      </view>\r\n      <wd-cell-group border clickable custom-class=\"shadow-warp\">\r\n        <wd-cell title=\"组织管理\" is-link icon=\"computer\"></wd-cell>\r\n        <wd-cell title=\"安全设置\" is-link icon=\"setting\"></wd-cell>\r\n        <wd-cell title=\"个人设置\" is-link icon=\"user\"></wd-cell>\r\n        <wd-cell title=\"退出登录\" is-link icon=\"login\"></wd-cell>\r\n      </wd-cell-group>\r\n      <view class=\"box shadow-warp p-3\">\r\n        <view class=\"content\">\r\n          <!-- <view class=\"title\">提示</view> -->\r\n          <view class=\"flex flex-col\">\r\n            <wd-button custom-class=\"mb-2 info\" @click=\"handleToast(0)\">常规</wd-button>\r\n            <wd-button custom-class=\"mb-2 warning\" @click=\"handleToast(1)\">警告</wd-button>\r\n            <wd-button custom-class=\"mb-2 success\" @click=\"handleToast(2)\">成功</wd-button>\r\n            <wd-button custom-class=\"mb-2 error\" @click=\"handleToast(3)\">错误</wd-button>\r\n            <wd-button custom-class=\"mb-2 basic\" @click=\"handleToast(4)\">\r\n              基本用法(无icon)\r\n            </wd-button>\r\n            <wd-button @click=\"handleConfirm\">确认提示</wd-button>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </scroll-view>\r\n  </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\n//\r\nimport { ref } from 'vue'\r\nimport { useRouter } from '@/plugin/uni-mini-router'\r\nimport { useToast, useMessage, useNotify } from 'wot-design-uni'\r\nimport { us, os } from '@/common/work'\r\nimport Grid from '@/components/Grid/Grid.vue'\r\n\r\nconst toast = useToast()\r\nconst user = ref('')\r\nconst dept = ref('')\r\nconst message = useMessage()\r\nconst { showNotify, closeNotify } = useNotify()\r\n\r\nconst router = useRouter()\r\nconst selected = ref([])\r\nconst gridData = ref([])\r\nus.data.forEach((item: any, index) => {\r\n  if (index < 9) {\r\n    gridData.value.push({ text: item.title, img: item.icon, itemKey: index })\r\n  }\r\n})\r\n// 图片预览\r\nconst imgPreview = ref({\r\n  show: false,\r\n  urls: [\r\n    'https://jeecgos.oss-cn-beijing.aliyuncs.com/files/site/projectCase/mini/banner/10bdc1.jpg',\r\n  ],\r\n})\r\nconst change = () => {}\r\nconst monthChange = () => {}\r\n\r\nconst proDataSource = [\r\n  {\r\n    activeStep: true,\r\n    data: [\r\n      { label: '流程节点：start' },\r\n      { label: '申请人：神经蛙02' },\r\n      { label: '申请时间：2023-12-06 16:15:14' },\r\n      { label: '已完成' },\r\n    ],\r\n  },\r\n  {\r\n    activeStep: false,\r\n    data: [\r\n      { label: '流程节点：填写' },\r\n      { label: '申请人：神经蛙01' },\r\n      { label: '申请时间：2023-12-06 16:15:14' },\r\n    ],\r\n  },\r\n  {\r\n    activeStep: false,\r\n    data: [\r\n      { label: '流程节点：填写' },\r\n      { label: '申请人：神经蛙03' },\r\n      { label: '申请时间：2023-12-06 16:15:14' },\r\n    ],\r\n  },\r\n]\r\nconst dataSource = ref([\r\n  { title: '万年历组件' },\r\n  { title: '图片预览' },\r\n  // {\r\n  //   group: [\r\n  //     { title: '树组件', path: '/pages/demo/tree' },\r\n  //     { title: '通讯录', path: '/pages/demo/indexBar' },\r\n  //     { title: '单选多选', path: '/pages/demo/selectPicker' },\r\n  //     { title: '表单', path: '/pages/demo/form' },\r\n  //   ],\r\n  // },\r\n])\r\nconst handleSkip = (path) => {\r\n  router.push({ path: path })\r\n}\r\nconst handleToast = (value) => {\r\n  switch (value) {\r\n    case 0:\r\n      // 909cb8\r\n      toast.info({ msg: '常规提示信息', duration: 10000 })\r\n      break\r\n    case 1:\r\n      // f0863b\r\n      toast.warning({ msg: '提示信息', duration: 10000 })\r\n      break\r\n    case 2:\r\n      // 33d19d\r\n      toast.success({ msg: '操作成功', duration: 10000 })\r\n      break\r\n    case 3:\r\n      // f04550\r\n      toast.error({ msg: '手机验证码输入错误，请重新输入', duration: 10000 })\r\n      break\r\n    case 4:\r\n      toast.show({ msg: '手机验证码输入错误，请重新输入', duration: 10000 })\r\n      break\r\n  }\r\n}\r\nconst handleConfirm = (params) => {\r\n  message\r\n    .confirm({\r\n      msg: '提示文案',\r\n      title: '标题',\r\n    })\r\n    .then(() => {\r\n      showNotify({ type: 'success', message: '点击了确认按钮' })\r\n    })\r\n    .catch(() => {\r\n      showNotify({ type: 'warning', message: '点击了取消按钮' })\r\n    })\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n//\r\n.mb-2 {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.box {\r\n  background-color: #fff;\r\n  margin: 25px 16px;\r\n  .title {\r\n    padding: 10px;\r\n    padding-bottom: 0;\r\n    font-size: 15;\r\n    color: #666666;\r\n    margin-bottom: 20upx;\r\n  }\r\n\r\n  .content {\r\n    :deep(.wd-button),\r\n    :deep(.imgView) {\r\n      margin: 10px;\r\n    }\r\n    :deep(.wd-button) {\r\n      &.info {\r\n        background-color: #909cb8;\r\n      }\r\n      &.warning {\r\n        background-color: #f0863b;\r\n      }\r\n      &.success {\r\n        background-color: #33d19d;\r\n      }\r\n      &.error {\r\n        background-color: #f04550;\r\n      }\r\n    }\r\n  }\r\n}\r\n.router {\r\n  padding: 30px 15px;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  .wd-button {\r\n    margin-bottom: 15px;\r\n    &:nth-child(3),\r\n    &:nth-child(4) {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n:deep(.wd-cell-group) {\r\n  margin: 0 26upx;\r\n  border-radius: 18upx;\r\n  overflow: hidden;\r\n  --wot-cell-line-height: 32px;\r\n  .wd-icon {\r\n    margin-right: 10px;\r\n  }\r\n  .wd-cell {\r\n    --wot-cell-title-fs: 15px;\r\n    --wot-cell-title-color: var(--color-gray);\r\n    .wd-cell__left {\r\n      font-size: 15px;\r\n      font-weight: 300;\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages/demo/demo.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useToast", "ref", "useMessage", "useNotify", "useRouter", "us"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqGA,MAAA,OAAiB,MAAA;;;;AAEjB,UAAM,QAAQA,cAAAA,SAAS;AACjB,UAAA,OAAOC,kBAAI,EAAE;AACb,UAAA,OAAOA,kBAAI,EAAE;AACnB,UAAM,UAAUC,cAAAA,WAAW;AAC3B,UAAM,EAAE,WAAwB,IAAIC,wBAAU;AAE9C,UAAM,SAASC,gCAAAA,UAAU;AACnB,UAAA,WAAWH,cAAI,IAAA,EAAE;AACjB,UAAA,WAAWA,cAAI,IAAA,EAAE;AACvBI,gBAAAA,GAAG,KAAK,QAAQ,CAAC,MAAW,UAAU;AACpC,UAAI,QAAQ,GAAG;AACJ,iBAAA,MAAM,KAAK,EAAE,MAAM,KAAK,OAAO,KAAK,KAAK,MAAM,SAAS,MAAA,CAAO;AAAA,MAAA;AAAA,IAC1E,CACD;AAED,UAAM,aAAaJ,cAAAA,IAAI;AAAA,MACrB,MAAM;AAAA,MACN,MAAM;AAAA,QACJ;AAAA,MAAA;AAAA,IACF,CACD;AACD,UAAM,SAAS,MAAM;AAAA,IAAC;AACtB,UAAM,cAAc,MAAM;AAAA,IAAC;AAE3B,UAAM,gBAAgB;AAAA,MACpB;AAAA,QACE,YAAY;AAAA,QACZ,MAAM;AAAA,UACJ,EAAE,OAAO,aAAa;AAAA,UACtB,EAAE,OAAO,YAAY;AAAA,UACrB,EAAE,OAAO,2BAA2B;AAAA,UACpC,EAAE,OAAO,MAAM;AAAA,QAAA;AAAA,MAEnB;AAAA,MACA;AAAA,QACE,YAAY;AAAA,QACZ,MAAM;AAAA,UACJ,EAAE,OAAO,UAAU;AAAA,UACnB,EAAE,OAAO,YAAY;AAAA,UACrB,EAAE,OAAO,2BAA2B;AAAA,QAAA;AAAA,MAExC;AAAA,MACA;AAAA,QACE,YAAY;AAAA,QACZ,MAAM;AAAA,UACJ,EAAE,OAAO,UAAU;AAAA,UACnB,EAAE,OAAO,YAAY;AAAA,UACrB,EAAE,OAAO,2BAA2B;AAAA,QAAA;AAAA,MACtC;AAAA,IAEJ;AACA,UAAM,aAAaA,cAAAA,IAAI;AAAA,MACrB,EAAE,OAAO,QAAQ;AAAA,MACjB,EAAE,OAAO,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAA,CASjB;AACK,UAAA,aAAa,CAAC,SAAS;AACpB,aAAA,KAAK,EAAE,MAAY;AAAA,IAC5B;AACM,UAAA,cAAc,CAAC,UAAU;AAC7B,cAAQ,OAAO;AAAA,QACb,KAAK;AAEH,gBAAM,KAAK,EAAE,KAAK,UAAU,UAAU,KAAO;AAC7C;AAAA,QACF,KAAK;AAEH,gBAAM,QAAQ,EAAE,KAAK,QAAQ,UAAU,KAAO;AAC9C;AAAA,QACF,KAAK;AAEH,gBAAM,QAAQ,EAAE,KAAK,QAAQ,UAAU,KAAO;AAC9C;AAAA,QACF,KAAK;AAEH,gBAAM,MAAM,EAAE,KAAK,mBAAmB,UAAU,KAAO;AACvD;AAAA,QACF,KAAK;AACH,gBAAM,KAAK,EAAE,KAAK,mBAAmB,UAAU,KAAO;AACtD;AAAA,MAAA;AAAA,IAEN;AACM,UAAA,gBAAgB,CAAC,WAAW;AAChC,cACG,QAAQ;AAAA,QACP,KAAK;AAAA,QACL,OAAO;AAAA,MAAA,CACR,EACA,KAAK,MAAM;AACV,mBAAW,EAAE,MAAM,WAAW,SAAS,WAAW;AAAA,MAAA,CACnD,EACA,MAAM,MAAM;AACX,mBAAW,EAAE,MAAM,WAAW,SAAS,WAAW;AAAA,MAAA,CACnD;AAAA,IACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3MA,GAAG,WAAW,eAAe;"}