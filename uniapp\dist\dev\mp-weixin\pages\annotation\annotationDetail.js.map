{"version": 3, "file": "annotationDetail.js", "sources": ["../../../../../src/pages/annotation/annotationDetail.vue", "../../../../../uniPage:/cGFnZXMvYW5ub3RhdGlvbi9hbm5vdGF0aW9uRGV0YWlsLnZ1ZQ"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n  <PageLayout navTitle=\"详情\" backRouteName=\"annotationList\">\r\n    <view class=\"p-10px\">\r\n      <view class=\"mb-8px\">\r\n        <wd-text custom-class=\"title font-size-20px\" :text=\"annotation.titile\"></wd-text>\r\n      </view>\r\n      <view class=\"flex mb-16px\">\r\n        <wd-text custom-class=\"sender mr-8px\" :text=\"annotation.sender\"></wd-text>\r\n        <wd-text class=\"sendTime\" :text=\"annotation.sendTime\"></wd-text>\r\n      </view>\r\n      <view class=\"content mb-16px\">\r\n        <view class=\"text-content\" v-html=\"annotation.msgContent\"></view>\r\n      </view>\r\n      <view class=\"flex\">\r\n        <wd-text\r\n          custom-class=\"cIcon cuIcon-attentionfill mr-10px\"\r\n          text=\"10\"\r\n          @click=\"numberPlus\"\r\n        ></wd-text>\r\n        <wd-text class=\"cIcon cuIcon-appreciatefill\" text=\"20\" @click=\"numberPlus\"></wd-text>\r\n      </view>\r\n    </view>\r\n  </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { reactive } from 'vue'\r\nimport { onLaunch, onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'\r\nimport { http } from '@/utils/http'\r\n//\r\nconst annotation = reactive({\r\n  id: '',\r\n  titile: '',\r\n  startTime: '',\r\n  sender: '',\r\n  msgContent: '',\r\n  anntId: '',\r\n  sendTime: '',\r\n})\r\nconst goodNumber = ref(null)\r\nconst flg = ref(true)\r\nconst init = (option) => {\r\n  const annItem = JSON.parse(decodeURIComponent(option.item))\r\n  console.log('ann', annItem)\r\n  Object.assign(annotation, annItem)\r\n  readOk()\r\n}\r\nconst readOk = () => {\r\n  let param = { anntId: annotation.anntId }\r\n  http.put('/sys/sysAnnouncementSend/editByAnntIdAndUserId', param)\r\n}\r\n\r\nconst numberPlus = () => {\r\n  if (flg.value) {\r\n    goodNumber.value++\r\n    flg.value = false\r\n  } else {\r\n    goodNumber.value--\r\n    if (goodNumber.value == 0) {\r\n      goodNumber.value = null\r\n    }\r\n    flg.value = true\r\n  }\r\n}\r\nonLoad((option) => {\r\n  init(option)\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n//\r\n:deep(.wd-text) {\r\n   --wot-text-info-color: var(--color-grey);\r\n  &.cIcon {\r\n    &::before {\r\n      margin-right: 4px;\r\n    }\r\n  }\r\n}\r\n.title {\r\n  --wot-text-info-color: #333;\r\n  margin-bottom: 10px;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages/annotation/annotationDetail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["reactive", "ref", "http", "onLoad"], "mappings": ";;;;;;;;;;;;;;;;;AAkCA,UAAM,aAAaA,cAAAA,SAAS;AAAA,MAC1B,IAAI;AAAA,MACJ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,UAAU;AAAA,IAAA,CACX;AACK,UAAA,aAAaC,kBAAI,IAAI;AACrB,UAAA,MAAMA,kBAAI,IAAI;AACd,UAAA,OAAO,CAAC,WAAW;AACvB,YAAM,UAAU,KAAK,MAAM,mBAAmB,OAAO,IAAI,CAAC;AAClD,cAAA,IAAI,OAAO,OAAO;AACnB,aAAA,OAAO,YAAY,OAAO;AAC1B,aAAA;AAAA,IACT;AACA,UAAM,SAAS,MAAM;AACnB,UAAI,QAAQ,EAAE,QAAQ,WAAW,OAAO;AACnCC,sBAAA,IAAI,kDAAkD,KAAK;AAAA,IAClE;AAEA,UAAM,aAAa,MAAM;AACvB,UAAI,IAAI,OAAO;AACF,mBAAA;AACX,YAAI,QAAQ;AAAA,MAAA,OACP;AACM,mBAAA;AACP,YAAA,WAAW,SAAS,GAAG;AACzB,qBAAW,QAAQ;AAAA,QAAA;AAErB,YAAI,QAAQ;AAAA,MAAA;AAAA,IAEhB;AACAC,kBAAA,OAAO,CAAC,WAAW;AACjB,WAAK,MAAM;AAAA,IAAA,CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrED,GAAG,WAAW,eAAe;"}