{"version": 3, "file": "form.js", "sources": ["../../../../../src/pages-data/registration/form.vue", "../../../../../uniPage:/cGFnZXMtZGF0YVxyZWdpc3RyYXRpb25cZm9ybS52dWU"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n  <PageLayout>\r\n    <template #navbar>\r\n      <NavBar :title=\"mode === 'add' ? '新增登记表' : '查看登记表'\" :showBack=\"true\" />\r\n    </template>\r\n\r\n    <scroll-view class=\"page-scroll-view\" scroll-y=\"true\">\r\n      <view class=\"form-container\">\r\n        <view class=\"form-header\">\r\n          <text class=\"form-title\">慢性心衰患者知情同意与登记</text>\r\n        </view>\r\n\r\n        <view class=\"form-section\">\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">1.基本信息</text>\r\n            <view class=\"inline-form-item\">\r\n              <text class=\"inline-form-label\"><text class=\"required\">*</text>姓名：</text>\r\n              <input class=\"inline-form-input\" v-model=\"formData.name\" :disabled=\"isViewMode\" placeholder=\"请输入姓名\" />\r\n            </view>\r\n          </view>\r\n          <view class=\"form-item\">\r\n            <view class=\"inline-form-item\">\r\n              <text class=\"inline-form-label\"><text class=\"required\">*</text>联系电话：</text>\r\n              <input class=\"inline-form-input\" v-model=\"formData.phone\" type=\"number\" placeholder=\"请输入联系电话\"\r\n                :disabled=\"isViewMode\" />\r\n            </view>\r\n          </view>\r\n\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\"><text class=\"required\">*</text>2.性别</text>\r\n            <view class=\"radio-group\">\r\n              <view class=\"radio-row\">\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.sex = 1)\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.sex === 1 }\"></view>\r\n                  <text>男</text>\r\n                </view>\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.sex = 2)\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.sex === 2 }\"></view>\r\n                  <text>女</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\"><text class=\"required\">*</text>3.婚姻状况</text>\r\n            <view class=\"radio-group\">\r\n              <view class=\"radio-row\">\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.maritalStatus = '未婚')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.maritalStatus === '未婚' }\"></view>\r\n                  <text>未婚</text>\r\n                </view>\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.maritalStatus = '已婚')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.maritalStatus === '已婚' }\"></view>\r\n                  <text>已婚</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"radio-row\">\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.maritalStatus = '离异')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.maritalStatus === '离异' }\"></view>\r\n                  <text>离异</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\"><text class=\"required\">*</text>4.出生日期</text>\r\n            <picker mode=\"date\" :value=\"formData.birthDate\" :end=\"formDateString\" @change=\"onBirthDateChange\"\r\n              :disabled=\"isViewMode\">\r\n              <view class=\"date-picker\">\r\n                <text v-if=\"formData.birthDate\">{{ formData.birthDate }}</text>\r\n                <text v-else class=\"placeholder\">请选择出生日期</text>\r\n                <uni-icons v-if=\"!isViewMode\" type=\"calendar\" size=\"18\"></uni-icons>\r\n              </view>\r\n            </picker>\r\n          </view>\r\n\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\"><text class=\"required\">*</text>5.您的年龄段</text>\r\n            <view class=\"radio-group\">\r\n              <view class=\"radio-row\">\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.ageGroup = '18岁以下')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.ageGroup === '18岁以下' }\"></view>\r\n                  <text>18岁以下</text>\r\n                </view>\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.ageGroup = '18~25岁')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.ageGroup === '18~25岁' }\"></view>\r\n                  <text>18~25岁</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"radio-row\">\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.ageGroup = '26~30岁')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.ageGroup === '26~30岁' }\"></view>\r\n                  <text>26~30岁</text>\r\n                </view>\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.ageGroup = '31~40岁')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.ageGroup === '31~40岁' }\"></view>\r\n                  <text>31~40岁</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"radio-row\">\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.ageGroup = '41~50岁')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.ageGroup === '41~50岁' }\"></view>\r\n                  <text>41~50岁</text>\r\n                </view>\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.ageGroup = '51~60岁')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.ageGroup === '51~60岁' }\"></view>\r\n                  <text>51~60岁</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"radio-row\">\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.ageGroup = '60岁以上')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.ageGroup === '60岁以上' }\"></view>\r\n                  <text>60岁以上</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\"><text class=\"required\">*</text>6.职业状态</text>\r\n            <view class=\"radio-group\">\r\n              <view class=\"radio-row\">\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.occupationalStatus = '在职')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.occupationalStatus === '在职' }\"></view>\r\n                  <text>在职</text>\r\n                </view>\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.occupationalStatus = '退休')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.occupationalStatus === '退休' }\"></view>\r\n                  <text>退休</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\"><text class=\"required\">*</text>7.年度经济收入状况</text>\r\n            <view class=\"radio-group\">\r\n              <view class=\"radio-row\">\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.income = '5万以下')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.income === '5万以下' }\"></view>\r\n                  <text>5万以下</text>\r\n                </view>\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.income = '5万以上')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.income === '5万以上' }\"></view>\r\n                  <text>5万以上</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"radio-row\">\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.income = '10万以上')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.income === '10万以上' }\"></view>\r\n                  <text>10万以上</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\"><text class=\"required\">*</text>8.您目前从事的职业</text>\r\n            <view class=\"radio-group\">\r\n              <view class=\"radio-row\">\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.occupation = '市场/销售/商务')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.occupation === '市场/销售/商务' }\"></view>\r\n                  <text>市场/销售/商务</text>\r\n                </view>\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.occupation = '采购')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.occupation === '采购' }\"></view>\r\n                  <text>采购</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"radio-row\">\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.occupation = '行政')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.occupation === '行政' }\"></view>\r\n                  <text>行政</text>\r\n                </view>\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.occupation = '人力')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.occupation === '人力' }\"></view>\r\n                  <text>人力</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"radio-row\">\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.occupation = '产品/运营人员')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.occupation === '产品/运营人员' }\"></view>\r\n                  <text>产品/运营人员</text>\r\n                </view>\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.occupation = '个体经营者')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.occupation === '个体经营者' }\"></view>\r\n                  <text>个体经营者</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"radio-row\">\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.occupation = '财务/会计/出纳/审计')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.occupation === '财务/会计/出纳/审计' }\"></view>\r\n                  <text>财务/会计/出纳/审计</text>\r\n                </view>\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.occupation = '企业管理者')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.occupation === '企业管理者' }\"></view>\r\n                  <text>企业管理者</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"radio-row\">\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.occupation = '律师/法务')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.occupation === '律师/法务' }\"></view>\r\n                  <text>律师/法务</text>\r\n                </view>\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.occupation = '设计从业者')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.occupation === '设计从业者' }\"></view>\r\n                  <text>设计从业者</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"radio-row\">\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.occupation = '服务业人员')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.occupation === '服务业人员' }\"></view>\r\n                  <text>服务业人员</text>\r\n                </view>\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.occupation = '技术开发/工程师')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.occupation === '技术开发/工程师' }\"></view>\r\n                  <text>技术开发/工程师</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"radio-row\">\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.occupation = '农林牧渔劳动者')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.occupation === '农林牧渔劳动者' }\"></view>\r\n                  <text>农林牧渔劳动者</text>\r\n                </view>\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.occupation = '工人劳动者')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.occupation === '工人劳动者' }\"></view>\r\n                  <text>工人劳动者</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"radio-row\">\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.occupation = '全职家庭主妇/夫')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.occupation === '全职家庭主妇/夫' }\"></view>\r\n                  <text>全职家庭主妇/夫</text>\r\n                </view>\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.occupation = '自由职业')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.occupation === '自由职业' }\"></view>\r\n                  <text>自由职业</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"radio-row\">\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.occupation = '离休/退休')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.occupation === '离休/退休' }\"></view>\r\n                  <text>离休/退休</text>\r\n                </view>\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.occupation = '学生')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.occupation === '学生' }\"></view>\r\n                  <text>学生</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"radio-row\">\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.occupation = '老师')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.occupation === '老师' }\"></view>\r\n                  <text>老师</text>\r\n                </view>\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.occupation = '医护人员')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.occupation === '医护人员' }\"></view>\r\n                  <text>医护人员</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"radio-row\">\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.occupation = '科研人员')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.occupation === '科研人员' }\"></view>\r\n                  <text>科研人员</text>\r\n                </view>\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.occupation = '党政机关人员')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.occupation === '党政机关人员' }\"></view>\r\n                  <text>党政机关人员</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\"><text class=\"required\">*</text>9.您的学历</text>\r\n            <view class=\"radio-group\">\r\n              <view class=\"radio-row\">\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.education = '初中及以下')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.education === '初中及以下' }\"></view>\r\n                  <text>初中及以下</text>\r\n                </view>\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.education = '高中/中专')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.education === '高中/中专' }\"></view>\r\n                  <text>高中/中专</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"radio-row\">\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.education = '大学专科')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.education === '大学专科' }\"></view>\r\n                  <text>大学专科</text>\r\n                </view>\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.education = '大学本科')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.education === '大学本科' }\"></view>\r\n                  <text>大学本科</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"radio-row\">\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.education = '研究生及以上')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.education === '研究生及以上' }\"></view>\r\n                  <text>研究生及以上</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">10.请输入您的联系地址</text>\r\n            <view class=\"inline-form-item\">\r\n              <text class=\"inline-form-label\"><text class=\"required\">*</text>所在地区：</text>\r\n              <view class=\"inline-picker-wrapper\">\r\n                <wd-picker v-model=\"tempSelectedArea\" :columns=\"areaColumns\" :column-change=\"onAreaColumnChange\"\r\n                  @confirm=\"onAreaConfirm\" :disabled=\"isViewMode\" style=\"width:100%; height:100%;\" label-key=\"label\">\r\n                  <view\r\n                    style=\"background-color: #F7F7F7; border-radius: 8rpx; padding: 20rpx; font-size: 28rpx; color: #333; min-height: 80rpx; width: 100%; box-sizing: border-box; display: flex; justify-content: space-between; align-items: center;\">\r\n                    <text>{{ displayAreaText || '请选择' }}</text>\r\n                    <uni-icons v-if=\"!isViewMode\" type=\"arrow-right\" size=\"16\" color=\"#666\"></uni-icons>\r\n                  </view>\r\n                </wd-picker>\r\n              </view>\r\n            </view>\r\n          </view>\r\n\r\n          <!-- 街道地址 -->\r\n          <view class=\"form-item\">\r\n            <view class=\"inline-form-item\">\r\n              <text class=\"inline-form-label\"><text class=\"required\">*</text>街道地址：</text>\r\n              <input class=\"inline-form-input\" v-model=\"formData.address\" :disabled=\"isViewMode\"\r\n                placeholder=\"请输入街道地址\" />\r\n            </view>\r\n          </view>\r\n\r\n          <view class=\"form-item\">\r\n            <view class=\"form-item\">\r\n              <text class=\"form-label\">（注意：请在平静状态下（非剧烈运动后）测量并记录以下数据。）</text>\r\n            </view>\r\n          </view>\r\n\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\"><text class=\"required\">*</text>11.患者各项体征监测情况【多选题】</text>\r\n            <view class=\"checkbox-group\">\r\n              <view class=\"checkbox-row\">\r\n                <view class=\"checkbox-item\" @click=\"!isViewMode && toggleVitalSign('bodyPart')\">\r\n                  <view class=\"checkbox-btn\"\r\n                    :class=\"{ 'checked': formData.vitalSigns && formData.vitalSigns.bodyPart }\">\r\n                  </view>\r\n                  <text>体温°C</text>\r\n                </view>\r\n\r\n                <view class=\"checkbox-item\" @click=\"!isViewMode && toggleVitalSign('temperature')\">\r\n                  <view class=\"checkbox-btn\"\r\n                    :class=\"{ 'checked': formData.vitalSigns && formData.vitalSigns.temperature }\">\r\n                  </view>\r\n                  <text>°C</text>\r\n                </view>\r\n              </view>\r\n\r\n              <!-- 体温下拉框和输入框一行显示 -->\r\n              <view class=\"sub-form-items-row\"\r\n                v-if=\"formData.vitalSigns && (formData.vitalSigns.bodyPart || formData.vitalSigns.temperature)\"\r\n                style=\"display: flex; gap: 20rpx;\">\r\n                <view style=\"flex: 1; width: 50%;\">\r\n                  <view v-if=\"formData.vitalSigns && formData.vitalSigns.bodyPart\" style=\"width: 100%;\">\r\n                    <picker :value=\"formData.temperatureSiteIndex\" :range=\"temperatureSites\"\r\n                      @change=\"onTemperatureSiteChange\" :disabled=\"isViewMode\" style=\"width: 100%;\">\r\n                      <view\r\n                        style=\"display: flex; justify-content: space-between; align-items: center; width: 100%; background-color: #F7F7F7; border-radius: 8rpx; padding: 15rpx; font-size: 26rpx; color: #333; min-height: 70rpx; box-sizing: border-box;\">\r\n                        <text>{{ temperatureSites[formData.temperatureSiteIndex] }}</text>\r\n                        <uni-icons v-if=\"!isViewMode\" type=\"arrow-down\" size=\"14\"></uni-icons>\r\n                      </view>\r\n                    </picker>\r\n                  </view>\r\n                </view>\r\n                <view style=\"flex: 1; width: 50%;\">\r\n                  <input v-if=\"formData.vitalSigns && formData.vitalSigns.temperature\" class=\"sub-form-input\"\r\n                    v-model=\"formData.tempertureValue\" :disabled=\"isViewMode\"\r\n                    style=\"background-color: #F7F7F7; border-radius: 8rpx; padding: 15rpx; width: 100%; box-sizing: border-box;min-height: 70rpx;\" />\r\n                </view>\r\n              </view>\r\n\r\n              <view class=\"checkbox-row\">\r\n                <view class=\"checkbox-item\" @click=\"!isViewMode && toggleVitalSign('heartRate')\">\r\n                  <view class=\"checkbox-btn\"\r\n                    :class=\"{ 'checked': formData.vitalSigns && formData.vitalSigns.heartRate }\">\r\n                  </view>\r\n                  <text>心率</text>\r\n                </view>\r\n                <view class=\"checkbox-item\" @click=\"!isViewMode && toggleVitalSign('breathing')\">\r\n                  <view class=\"checkbox-btn\"\r\n                    :class=\"{ 'checked': formData.vitalSigns && formData.vitalSigns.breathing }\">\r\n                  </view>\r\n                  <text>呼吸</text>\r\n                </view>\r\n              </view>\r\n\r\n              <!-- 心率和呼吸输入框一行显示 -->\r\n              <view class=\"sub-form-items-row\"\r\n                v-if=\"formData.vitalSigns && (formData.vitalSigns.heartRate || formData.vitalSigns.breathing)\"\r\n                style=\"display: flex; gap: 20rpx;\">\r\n                <view style=\"flex: 1; width: 50%;\">\r\n                  <input v-if=\"formData.vitalSigns && formData.vitalSigns.heartRate\" class=\"sub-form-input\"\r\n                    v-model=\"formData.heartRateValue\" type=\"number\" :disabled=\"isViewMode\"\r\n                    style=\"background-color: #F7F7F7; border-radius: 8rpx; padding: 15rpx; width: 100%; box-sizing: border-box;min-height: 70rpx;\" />\r\n                </view>\r\n                <view style=\"flex: 1; width: 50%;\">\r\n                  <input v-if=\"formData.vitalSigns && formData.vitalSigns.breathing\" class=\"sub-form-input\"\r\n                    v-model=\"formData.breathingValue\" type=\"number\" :disabled=\"isViewMode\"\r\n                    style=\"background-color: #F7F7F7; border-radius: 8rpx; padding: 15rpx; width: 100%; box-sizing: border-box;min-height: 70rpx;\" />\r\n                </view>\r\n              </view>\r\n\r\n              <view class=\"checkbox-row\">\r\n                <view class=\"checkbox-item\" @click=\"!isViewMode && toggleVitalSign('bloodPressure')\">\r\n                  <view class=\"checkbox-btn\"\r\n                    :class=\"{ 'checked': formData.vitalSigns && formData.vitalSigns.bloodPressure }\">\r\n                  </view>\r\n                  <text>血压</text>\r\n                </view>\r\n                <view class=\"checkbox-item\" @click=\"!isViewMode && toggleVitalSign('oxygen')\">\r\n                  <view class=\"checkbox-btn\" :class=\"{ 'checked': formData.vitalSigns && formData.vitalSigns.oxygen }\">\r\n                  </view>\r\n                  <text>血氧饱和度</text>\r\n                </view>\r\n              </view>\r\n\r\n              <!-- 血压和血氧饱和度输入框一行显示 -->\r\n              <view class=\"sub-form-items-row\"\r\n                v-if=\"formData.vitalSigns && (formData.vitalSigns.bloodPressure || formData.vitalSigns.oxygen)\"\r\n                style=\"display: flex; gap: 20rpx;\">\r\n                <view style=\"flex: 1; width: 50%;\">\r\n                  <input v-if=\"formData.vitalSigns && formData.vitalSigns.bloodPressure\" class=\"sub-form-input\"\r\n                    v-model=\"formData.bloodPressureValue\" type=\"number\" :disabled=\"isViewMode\"\r\n                    style=\"background-color: #F7F7F7; border-radius: 8rpx; padding: 15rpx; width: 100%; box-sizing: border-box;min-height: 70rpx;\" />\r\n                </view>\r\n                <view style=\"flex: 1; width: 50%;\">\r\n                  <input v-if=\"formData.vitalSigns && formData.vitalSigns.oxygen\" class=\"sub-form-input\"\r\n                    v-model=\"formData.oxygenValue\" type=\"number\" :disabled=\"isViewMode\"\r\n                    style=\"background-color: #F7F7F7; border-radius: 8rpx; padding: 15rpx; width: 100%; box-sizing: border-box;min-height: 70rpx;\" />\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\"><text class=\"required\">*</text>12.最近是否服用药物或接受特殊治疗</text>\r\n            <view class=\"radio-group\">\r\n              <view class=\"radio-row\">\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.medicationStatus = '是')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.medicationStatus === '是' }\"></view>\r\n                  <text>是</text>\r\n                </view>\r\n              </view>\r\n              <!-- 药物或治疗说明输入框 -->\r\n              <view class=\"medication-input-row\" v-if=\"formData.medicationStatus === '是'\">\r\n                <input class=\"full-width-input\" v-model=\"formData.medicationDesc\" placeholder=\"请列出药物名称或治疗类型\"\r\n                  :disabled=\"isViewMode\"\r\n                  style=\"background-color: #F7F7F7; border-radius: 8rpx; padding: 15rpx; width: 100%; box-sizing: border-box; min-height: 70rpx;\" />\r\n              </view>\r\n              <view class=\"radio-row\">\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.medicationStatus = '否')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.medicationStatus === '否' }\"></view>\r\n                  <text>否</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\"><text class=\"required\">*</text>13.目前身体状况描述</text>\r\n            <textarea class=\"form-textarea\" v-model=\"formData.conditions\" placeholder=\"请输入目前身体状况描述\"\r\n              :disabled=\"isViewMode\" />\r\n          </view>\r\n\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\"><text class=\"required\">*</text>14.是否有已知的健康问题或慢性疾病</text>\r\n            <view class=\"radio-group\">\r\n              <view class=\"radio-row\">\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.healthProblem = '是')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.healthProblem === '是' }\"></view>\r\n                  <text>是</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"medication-input-row\" v-if=\"formData.healthProblem === '是'\">\r\n                <input class=\"full-width-input\" v-model=\"formData.problemDesc\" placeholder=\"请列出\" :disabled=\"isViewMode\"\r\n                  style=\"background-color: #F7F7F7; border-radius: 8rpx; padding: 15rpx; width: 100%; box-sizing: border-box; min-height: 70rpx;\" />\r\n              </view>\r\n              <view class=\"radio-row\">\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.healthProblem = '否')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.healthProblem === '否' }\"></view>\r\n                  <text>否</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\"><text class=\"required\">*</text>15.患者依从性</text>\r\n            <view class=\"radio-group\">\r\n              <view class=\"radio-row\">\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.compliance = '好')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.compliance === '好' }\"></view>\r\n                  <text>好</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"radio-row\">\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.compliance = '一般')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.compliance === '一般' }\"></view>\r\n                  <text>一般</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"radio-row\">\r\n                <view class=\"radio-item\" @click=\"!isViewMode && (formData.compliance = '不好')\">\r\n                  <view class=\"radio-btn\" :class=\"{ 'checked': formData.compliance === '不好' }\"></view>\r\n                  <text>不好</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\"><text class=\"required\">*</text>16.疾病【多选题】</text>\r\n            <view class=\"checkbox-group\">\r\n              <view class=\"checkbox-row\">\r\n                <view class=\"checkbox-item\" @click=\"!isViewMode && toggleDisease('smoking')\">\r\n                  <view class=\"checkbox-btn\" :class=\"{ 'checked': formData.diseases && formData.diseases.smoking }\">\r\n                  </view>\r\n                  <text>吸烟</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"checkbox-row\">\r\n                <view class=\"checkbox-item\" @click=\"!isViewMode && toggleDisease('drinking')\">\r\n                  <view class=\"checkbox-btn\" :class=\"{ 'checked': formData.diseases && formData.diseases.drinking }\">\r\n                  </view>\r\n                  <text>喝酒</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"checkbox-row\">\r\n                <view class=\"checkbox-item\" @click=\"!isViewMode && toggleDisease('hypertension')\">\r\n                  <view class=\"checkbox-btn\"\r\n                    :class=\"{ 'checked': formData.diseases && formData.diseases.hypertension }\">\r\n                  </view>\r\n                  <text>高血压</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"checkbox-row\">\r\n                <view class=\"checkbox-item\" @click=\"!isViewMode && toggleDisease('diabetes')\">\r\n                  <view class=\"checkbox-btn\" :class=\"{ 'checked': formData.diseases && formData.diseases.diabetes }\">\r\n                  </view>\r\n                  <text>糖尿病</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"checkbox-row\">\r\n                <view class=\"checkbox-item\" @click=\"!isViewMode && toggleDisease('hyperlipidemia')\">\r\n                  <view class=\"checkbox-btn\"\r\n                    :class=\"{ 'checked': formData.diseases && formData.diseases.hyperlipidemia }\">\r\n                  </view>\r\n                  <text>高血脂</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"checkbox-row\">\r\n                <view class=\"checkbox-item\" @click=\"!isViewMode && toggleDisease('chronicKidney')\">\r\n                  <view class=\"checkbox-btn\"\r\n                    :class=\"{ 'checked': formData.diseases && formData.diseases.chronicKidney }\">\r\n                  </view>\r\n                  <text>慢性肾病</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"checkbox-row\">\r\n                <view class=\"checkbox-item\" @click=\"!isViewMode && toggleDisease('chronicLung')\">\r\n                  <view class=\"checkbox-btn\" :class=\"{ 'checked': formData.diseases && formData.diseases.chronicLung }\">\r\n                  </view>\r\n                  <text>慢性肺病</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"checkbox-row\">\r\n                <view class=\"checkbox-item\" @click=\"!isViewMode && toggleDisease('tumor')\">\r\n                  <view class=\"checkbox-btn\" :class=\"{ 'checked': formData.diseases && formData.diseases.tumor }\">\r\n                  </view>\r\n                  <text>肿瘤</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"checkbox-row\">\r\n                <view class=\"checkbox-item\" @click=\"!isViewMode && toggleDisease('mainDiagnosis')\">\r\n                  <view class=\"checkbox-btn\"\r\n                    :class=\"{ 'checked': formData.diseases && formData.diseases.mainDiagnosis }\">\r\n                  </view>\r\n                  <text>主要疾病诊断</text>\r\n                </view>\r\n              </view>\r\n              <view v-if=\"formData.diseases && formData.diseases.mainDiagnosis\" class=\"medication-input-row\">\r\n                <input class=\"full-width-input\" v-model=\"formData.mainDiagnosisDesc\" placeholder=\"请输入主要疾病诊断\"\r\n                  :disabled=\"isViewMode\"\r\n                  style=\"background-color: #F7F7F7; border-radius: 8rpx; padding: 15rpx; width: 100%; box-sizing: border-box; min-height: 70rpx;\" />\r\n              </view>\r\n              <view class=\"checkbox-row\">\r\n                <view class=\"checkbox-item\" @click=\"!isViewMode && toggleDisease('diseaseSummary')\">\r\n                  <view class=\"checkbox-btn\"\r\n                    :class=\"{ 'checked': formData.diseases && formData.diseases.diseaseSummary }\">\r\n                  </view>\r\n                  <text>简述疾病情况</text>\r\n                </view>\r\n              </view>\r\n              <view v-if=\"formData.diseases && formData.diseases.diseaseSummary\" class=\"medication-input-row\">\r\n                <input class=\"full-width-input\" v-model=\"formData.diseaseSummaryDesc\" placeholder=\"请简述疾病情况\"\r\n                  :disabled=\"isViewMode\"\r\n                  style=\"background-color: #F7F7F7; border-radius: 8rpx; padding: 15rpx; width: 100%; box-sizing: border-box; min-height: 70rpx;\" />\r\n              </view>\r\n            </view>\r\n          </view>\r\n\r\n          <view class=\"form-item\">\r\n            <view class=\"form-item\">\r\n              <text class=\"form-label\">隐私声明：\r\n                我们承诺对您的个人信息及健康数据保密，仅用于健康评估及必要的医疗目的。未经您明确同意，我们不会将您的信息泄露给第三方。\r\n                请根据实际情况调整问卷内容，确保它符合您的具体需求和数据收集目的。同时，如果这份问卷用于医疗或研究目的，请确保遵循相关的隐私保护和伦理审查规定。</text>\r\n            </view>\r\n          </view>\r\n\r\n        </view>\r\n\r\n        <!-- 提交按钮，仅在新增模式显示 -->\r\n        <view class=\"submit-section\" v-if=\"!isViewMode\">\r\n          <button class=\"submit-btn\" @click=\"submitForm\">提交</button>\r\n        </view>\r\n      </view>\r\n    </scroll-view>\r\n  </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { ref, computed, onMounted, watch, nextTick } from 'vue'\r\nimport { areaData } from '@/components/online/area-picker-data'\r\nimport { fastLerp } from 'zrender/lib/tool/color'\r\nimport { useUserStore } from '@/store/user'\r\n\r\ndefineOptions({\r\n  name: 'RegistrationForm',\r\n})\r\n\r\nconst userStore = useUserStore()\r\n// 获取页面参数\r\nconst query = ref<any>({})\r\nonMounted(() => {\r\n  const pages = getCurrentPages()\r\n  const currentPage = pages[pages.length - 1]\r\n  query.value = (currentPage as any).options || {}\r\n  // 自动填充用户联系电话\r\n  if (userStore.userInfo.phone) {\r\n    formData.value.phone = userStore.userInfo.phone\r\n  }\r\n})\r\n\r\n// 表单模式：add 或 view\r\nconst mode = computed(() => query.value.mode || 'add')\r\nconst isViewMode = computed(() => mode.value === 'view')\r\n\r\n// 当前日期（用于限制日期选择器）\r\nconst formDateString = new Date().toISOString().split('T')[0]\r\n\r\n// 添加省市区选择器相关代码\r\n// 省市区选择器数据\r\nconst selectedArea = ref([])\r\n// 临时存储选择过程中的值，但不立即更新显示\r\nconst tempSelectedArea = ref([])\r\n// 准备省市区数据\r\nconst district = { ...areaData }\r\nconst areaColumns = ref([\r\n  district[0],\r\n  district[district[0][0].value],\r\n  district[district[district[0][0].value][0].value]\r\n])\r\n\r\n// 处理省市区选择器列变化\r\nconst onAreaColumnChange = (pickerView, value, columnIndex, resolve) => {\r\n  const item = value[columnIndex]\r\n  if (columnIndex === 0) {\r\n    // 更新省份选择后的市级数据\r\n    pickerView.setColumnData(1, district[item.value])\r\n    // 更新市级默认第一个的区数据\r\n    pickerView.setColumnData(2, district[district[item.value][0].value])\r\n\r\n    // 更新临时选择数据中的省份数据，但不立即更新显示\r\n    if (!tempSelectedArea.value[0] || tempSelectedArea.value[0]?.value !== item.value) {\r\n      tempSelectedArea.value[0] = item\r\n      // 重置市和区的选择，避免数据不一致\r\n      tempSelectedArea.value[1] = district[item.value][0]\r\n      tempSelectedArea.value[2] = district[district[item.value][0].value][0]\r\n    }\r\n  } else if (columnIndex === 1) {\r\n    // 更新市级选择后的区级数据\r\n    pickerView.setColumnData(2, district[item.value])\r\n\r\n    // 更新临时选择数据中的市级数据，但不立即更新显示\r\n    if (!tempSelectedArea.value[1] || tempSelectedArea.value[1]?.value !== item.value) {\r\n      tempSelectedArea.value[1] = item\r\n      // 重置区的选择，避免数据不一致\r\n      tempSelectedArea.value[2] = district[item.value][0]\r\n    }\r\n  } else if (columnIndex === 2) {\r\n    // 更新临时选择数据中的区级数据，但不立即更新显示\r\n    if (!tempSelectedArea.value[2] || tempSelectedArea.value[2]?.value !== item.value) {\r\n      tempSelectedArea.value[2] = item\r\n    }\r\n  }\r\n  resolve()\r\n}\r\n\r\n// 处理省市区选择确认\r\nconst onAreaConfirm = ({ value, selectedItems }) => {\r\n  console.log('onAreaConfirm调用，value:', JSON.stringify(value))\r\n  console.log('onAreaConfirm调用，selectedItems:', JSON.stringify(selectedItems))\r\n  console.log('onAreaConfirm调用，临时selectedArea:', JSON.stringify(tempSelectedArea.value))\r\n\r\n  // 使用picker传递的selectedItems参数，这里包含完整的对象数据\r\n  if (selectedItems && selectedItems.length === 3) {\r\n    // 从selectedItems中提取省市区文字\r\n    const province = selectedItems[0]?.label || ''\r\n    const city = selectedItems[1]?.label || ''\r\n    const district = selectedItems[2]?.label || ''\r\n\r\n    console.log('最终提取的省市区文字:', province, city, district)\r\n\r\n    // 将临时选择的值应用到实际选择的值中\r\n    selectedArea.value = [...tempSelectedArea.value]\r\n\r\n    // 设置表单数据，使用逗号分隔，与显示的格式一致\r\n    formData.value.area = `${province},${city},${district}`\r\n    formData.value.areaCode = selectedItems[2]?.value || ''\r\n\r\n    // 更新显示值，强制刷新界面\r\n    nextTick(() => {\r\n      console.log('区域选择更新后的值:', formData.value.area)\r\n    })\r\n\r\n    console.log('最终设置的地区值(文字):', formData.value.area)\r\n    console.log('最终设置的区域编码:', formData.value.areaCode)\r\n  } else {\r\n    console.error('selectedItems数据不完整:', selectedItems)\r\n    console.error('当前selectedArea:', selectedArea.value)\r\n    // 如果selectedItems不可用，尝试从value数组和地区数据中查找\r\n    if (value && Array.isArray(value) && value.length === 3) {\r\n      try {\r\n        const provinceCode = value[0]\r\n        const cityCode = value[1]\r\n        const districtCode = value[2]\r\n\r\n        // 从areaData中查找对应的label\r\n        const provinceItem = district[0].find(item => item.value === provinceCode)\r\n        const cityItem = district[provinceCode]?.find(item => item.value === cityCode)\r\n        const districtItem = district[cityCode]?.find(item => item.value === districtCode)\r\n\r\n        if (provinceItem && cityItem && districtItem) {\r\n          formData.value.area = `${provinceItem.label},${cityItem.label},${districtItem.label}`\r\n          formData.value.areaCode = districtCode\r\n          console.log('通过value数组查找成功，地区值:', formData.value.area)\r\n        }\r\n      } catch (error) {\r\n        console.error('通过value查找地区信息失败:', error)\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 在script部分顶部添加temperatureSites常量\r\nconst temperatureSites = ['请选择', '口腔', '腋下', '直肠', '其他']\r\n\r\n// 表单数据\r\nconst formData = ref({\r\n  name: '',\r\n  phone: '',\r\n  sex: null,\r\n  maritalStatus: '',\r\n  ageGroup: '',\r\n  birthDate: '',\r\n  occupationalStatus: '',\r\n  income: '',\r\n  occupation: '',\r\n  education: '',\r\n  area: '',\r\n  address: '',\r\n  areaCode: '', // 添加区域编码字段\r\n  medicalNumber: '',\r\n  conditions: '',\r\n  hasConsent: true,\r\n  vitalSigns: {\r\n    bodyPart: false,\r\n    temperature: false,\r\n    heartRate: false,\r\n    breathing: false,\r\n    bloodPressure: false,\r\n    oxygen: false,\r\n  },\r\n  temperatureSiteIndex: 0,\r\n  temperatureSite: '请选择', // 添加体温测量部位文字\r\n  tempertureValue: '',\r\n  heartRateValue: '',\r\n  breathingValue: '',\r\n  bloodPressureValue: '',\r\n  oxygenValue: '',\r\n  medicationStatus: '',\r\n  medicationDesc: '',\r\n  healthProblem: '',\r\n  problemDesc: '',\r\n  compliance: '',\r\n  diseases: {\r\n    smoking: false,\r\n    drinking: false,\r\n    hypertension: false,\r\n    diabetes: false,\r\n    hyperlipidemia: false,\r\n    chronicKidney: false,\r\n    chronicLung: false,\r\n    tumor: false,\r\n    mainDiagnosis: false,\r\n    diseaseSummary: false,\r\n  },\r\n  mainDiagnosisDesc: '',\r\n  diseaseSummaryDesc: '',\r\n})\r\n\r\n// 处理出生日期选择\r\nconst onBirthDateChange = (e: any) => {\r\n  formData.value.birthDate = e.detail.value\r\n  // 根据出生日期计算年龄\r\n  if (e.detail.value) {\r\n    const birthYear = new Date(e.detail.value).getFullYear()\r\n    const currentYear = new Date().getFullYear()\r\n    // formData.value.age = String(currentYear - birthYear)\r\n  }\r\n}\r\n\r\n// 提交表单\r\nconst submitForm = () => {\r\n\r\n  console.log('提交表单数据:', formData.value)\r\n  console.log('formData.area:', formData.value.area)\r\n  console.log('selectedArea:', selectedArea.value)\r\n  // 确保formData.area中有正确的地区数据\r\n  if (selectedArea.value && Array.isArray(selectedArea.value) && selectedArea.value.length === 3) {\r\n    try {\r\n      console.log('检查区域信息，selectedArea:', JSON.stringify(selectedArea.value))\r\n\r\n      // 再次确认selectedArea中的每个项都有label属性\r\n      if (selectedArea.value && selectedArea.value.length === 3 &&\r\n        selectedArea.value[0]?.label &&\r\n        selectedArea.value[1]?.label &&\r\n        selectedArea.value[2]?.label) {\r\n        // 提取省市区文字\r\n        const province = selectedArea.value[0].label\r\n        const city = selectedArea.value[1].label\r\n        const district = selectedArea.value[2].label\r\n\r\n        console.log('提交前确认的省市区文字:', province, city, district)        // 无论如何都重新设置area值，确保一定有值，与其它地方的格式保持一致\r\n        formData.value.area = `${province},${city},${district}`\r\n        formData.value.areaCode = selectedArea.value[2].value\r\n        console.log('提交前确认的地区值:', formData.value.area)\r\n      } else {\r\n        console.log('selectedArea数据格式不正确或不完整:', selectedArea.value)\r\n        // 如果selectedArea格式不对，尝试保持原有的area值\r\n        if (!formData.value.area) {\r\n          console.warn('地区信息缺失，请重新选择地区')\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('设置地区值出错:', error, selectedArea.value)\r\n    }\r\n  }\r\n\r\n  // 表单验证\r\n  if (!formData.value.name) {\r\n    uni.showToast({\r\n      title: '请输入患者姓名',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  if (!formData.value.phone) {\r\n    uni.showToast({\r\n      title: '请输入联系电话',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  if (!formData.value.sex) {\r\n    uni.showToast({\r\n      title: '请选择性别',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  if (!formData.value.maritalStatus) {\r\n    uni.showToast({\r\n      title: '请选择婚姻状况',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  if (!formData.value.birthDate) {\r\n    uni.showToast({\r\n      title: '请选择出生日期',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  if (!formData.value.ageGroup) {\r\n    uni.showToast({\r\n      title: '请选择年龄段',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  if (!formData.value.occupationalStatus) {\r\n    uni.showToast({\r\n      title: '请选择职业状态',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  if (!formData.value.income) {\r\n    uni.showToast({\r\n      title: '请选择年度经济收入状况',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  if (!formData.value.occupation) {\r\n    uni.showToast({\r\n      title: '请选择您目前从事的职业',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  if (!formData.value.education) {\r\n    uni.showToast({\r\n      title: '请选择您的学历',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  if (!formData.value.area) {\r\n    console.log('地区为空，检查选择器:', selectedArea.value)\r\n    uni.showToast({\r\n      title: '请选择所在地区',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  if (!formData.value.address) {\r\n    uni.showToast({\r\n      title: '请输入街道地址',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  if (!formData.value.vitalSigns ||\r\n    (!formData.value.vitalSigns.bodyPart &&\r\n      !formData.value.vitalSigns.temperature &&\r\n      !formData.value.vitalSigns.heartRate &&\r\n      !formData.value.vitalSigns.breathing &&\r\n      !formData.value.vitalSigns.bloodPressure &&\r\n      !formData.value.vitalSigns.oxygen)) {\r\n    uni.showToast({\r\n      title: '请选择至少一项体征监测情况',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  // 验证第11点 - 如果选择了体温，则必须选择部位和输入值\r\n  if (formData.value.vitalSigns.bodyPart && formData.value.temperatureSiteIndex === 0) {\r\n    uni.showToast({\r\n      title: '请选择体温测量部位',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  if (formData.value.vitalSigns.temperature && !formData.value.tempertureValue) {\r\n    uni.showToast({\r\n      title: '请输入体温值',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  // 验证心率\r\n  if (formData.value.vitalSigns.heartRate && !formData.value.heartRateValue) {\r\n    uni.showToast({\r\n      title: '请输入心率值',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  // 验证呼吸\r\n  if (formData.value.vitalSigns.breathing && !formData.value.breathingValue) {\r\n    uni.showToast({\r\n      title: '请输入呼吸值',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  // 验证血压\r\n  if (formData.value.vitalSigns.bloodPressure && !formData.value.bloodPressureValue) {\r\n    uni.showToast({\r\n      title: '请输入血压值',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  // 验证血氧\r\n  if (formData.value.vitalSigns.oxygen && !formData.value.oxygenValue) {\r\n    uni.showToast({\r\n      title: '请输入血氧饱和度值',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  if (!formData.value.medicationStatus) {\r\n    uni.showToast({\r\n      title: '请选择是否服用药物或接受特殊治疗',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  // 验证第12点 - 如果选择了\"是\"，则药物或治疗说明必填\r\n  if (formData.value.medicationStatus === '是' && !formData.value.medicationDesc) {\r\n    uni.showToast({\r\n      title: '请输入药物名称或治疗类型',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  if (!formData.value.conditions) {\r\n    uni.showToast({\r\n      title: '请输入身体状况描述',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  if (!formData.value.healthProblem) {\r\n    uni.showToast({\r\n      title: '请选择是否有已知的健康问题或慢性疾病',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  // 验证第14点 - 如果选择了\"是\"，则健康问题说明必填\r\n  if (formData.value.healthProblem === '是' && !formData.value.problemDesc) {\r\n    uni.showToast({\r\n      title: '请列出健康问题或慢性疾病',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  if (!formData.value.compliance) {\r\n    uni.showToast({\r\n      title: '请选择患者依从性',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  if (!formData.value.diseases ||\r\n    (!formData.value.diseases.smoking &&\r\n      !formData.value.diseases.drinking &&\r\n      !formData.value.diseases.hypertension &&\r\n      !formData.value.diseases.diabetes &&\r\n      !formData.value.diseases.hyperlipidemia &&\r\n      !formData.value.diseases.chronicKidney &&\r\n      !formData.value.diseases.chronicLung &&\r\n      !formData.value.diseases.tumor &&\r\n      !formData.value.diseases.mainDiagnosis &&\r\n      !formData.value.diseases.diseaseSummary)) {\r\n    uni.showToast({\r\n      title: '请至少选择一项疾病情况',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  // 验证第16点 - 如果选择了主要疾病诊断，则必须填写描述\r\n  if (formData.value.diseases.mainDiagnosis && !formData.value.mainDiagnosisDesc) {\r\n    uni.showToast({\r\n      title: '请输入主要疾病诊断',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  // 验证第16点 - 如果选择了简述疾病情况，则必须填写描述\r\n  if (formData.value.diseases.diseaseSummary && !formData.value.diseaseSummaryDesc) {\r\n    uni.showToast({\r\n      title: '请简述疾病情况',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  // 显示加载提示\r\n  uni.showLoading({\r\n    title: '提交中...'\r\n  })\r\n\r\n  const requestData = {\r\n    name: formData.value.name,\r\n    phone: formData.value.phone,\r\n    sex: formData.value.sex,\r\n    maritalStatus: formData.value.maritalStatus,\r\n    birthDate: formData.value.birthDate,\r\n    ageGroup: formData.value.ageGroup,\r\n    occupationalStatus: formData.value.occupationalStatus,\r\n    income: formData.value.income,\r\n    occupation: formData.value.occupation,\r\n    education: formData.value.education,\r\n    area: formData.value.area,\r\n    address: formData.value.address,\r\n    temperatureSite: formData.value.temperatureSite,\r\n    tempertureValue: formData.value.tempertureValue,\r\n    heartRateValue: formData.value.heartRateValue,\r\n    breathingValue: formData.value.breathingValue,\r\n    bloodPressureValue: formData.value.bloodPressureValue,\r\n    oxygenValue: formData.value.oxygenValue,\r\n    medicationDesc: formData.value.medicationDesc,\r\n    conditions: formData.value.conditions,\r\n    problemDesc: formData.value.problemDesc,\r\n    compliance: formData.value.compliance,\r\n    diseases: formData.value.diseases,\r\n    mainDiagnosisDesc: formData.value.mainDiagnosisDesc,\r\n    diseaseSummaryDesc: formData.value.diseaseSummaryDesc,\r\n    userId: userStore.userInfo.userid\r\n  }\r\n\r\n  console.log('formData为：', formData.value)\r\n  console.log('requestData为', requestData)\r\n\r\n  // 调用保存接口\r\n  uni.request({\r\n    url: `${import.meta.env.VITE_SERVER_BASEURL}/patient/saveregistration`,\r\n    method: 'POST',\r\n    data: requestData,\r\n    success: (res) => {\r\n      uni.hideLoading();\r\n\r\n      if (res.data?.success) {\r\n        userStore.editUserInfo({ realname: formData.value.name });\r\n        userStore.editUserInfo({ sex: formData.value.sex });\r\n\r\n        callAddRoleAPI();\r\n\r\n        uni.showModal({\r\n          title: '提交成功',\r\n          showCancel: false,\r\n          success: () => {\r\n            uni.switchTab({\r\n              url: '/pages/index/index'\r\n            });\r\n          }\r\n        });\r\n      } else {\r\n        const errorMsg = res.data?.message || '提交失败，未知错误';\r\n        uni.showModal({\r\n          title: '提交失败',\r\n          content: errorMsg,\r\n          showCancel: false\r\n        });\r\n      }\r\n    },\r\n    fail: (err) => {\r\n      uni.hideLoading();\r\n      const errorMsg = err.errMsg || '网络错误，请稍后重试';\r\n      uni.showModal({\r\n        title: '提交失败',\r\n        content: errorMsg,\r\n        showCancel: false\r\n      });\r\n    }\r\n  })\r\n}\r\n\r\nconst callAddRoleAPI = () => {\r\n  const userId = userStore.userInfo.userid;\r\n  const userCategory = userStore.userInfo.userCategory;\r\n  \r\n  console.log('调用 addRole 接口', { userId, userCategory });\r\n  \r\n  uni.request({\r\n    url: `${import.meta.env.VITE_SERVER_BASEURL}/sys/role/addRole`,\r\n    method: 'POST',\r\n    header: {\r\n      'X-Access-Token': userStore.userInfo.token\r\n    },\r\n    data: {\r\n      userId: userId,\r\n      userCategory: userCategory\r\n    }, success: (res) => {\r\n      console.log('addRole 接口调用成功', res.data);\r\n      // 在用户的角色数组中添加\"患者\"角色\r\n      const currentRoles = userStore.userInfo.roleList || [];\r\n      if (!currentRoles.includes('1')) {\r\n        userStore.editUserInfo({ roleList: [...currentRoles, '1'] });\r\n      }\r\n    },\r\n    fail: (err) => {\r\n      console.error('addRole 接口调用失败', err);\r\n      // 这里可以根据需要处理错误\r\n    }\r\n  });\r\n};\r\n\r\n// 添加initAreaPicker函数\r\n// 初始化省市区选择器数据\r\nconst initAreaPicker = (areaCode) => {\r\n  if (!areaCode) return\r\n\r\n  // 查找区县对应的记录\r\n  const findAreaInfo = (code) => {\r\n    // 先找到对应的区县信息\r\n    let provinceValue, cityValue, countyValue\r\n    let provinceItem, cityItem, countyItem\r\n\r\n    // 从区县编码中提取省市编码\r\n    const provinceCode = code.substring(0, 2) + '0000'\r\n    const cityCode = code.substring(0, 4) + '00'\r\n\r\n    // 在district[0]查找对应的省\r\n    for (const province of district[0]) {\r\n      if (province.value.substring(0, 2) === code.substring(0, 2)) {\r\n        provinceValue = province.value\r\n        provinceItem = province\r\n        break\r\n      }\r\n    }\r\n\r\n    if (provinceValue) {\r\n      // 在district[provinceValue]查找对应的市\r\n      const cities = district[provinceValue]\r\n      if (cities) {\r\n        for (const city of cities) {\r\n          if (city.value.substring(0, 4) === code.substring(0, 4)) {\r\n            cityValue = city.value\r\n            cityItem = city\r\n            break\r\n          }\r\n        }\r\n\r\n        if (cityValue) {\r\n          // 在district[cityValue]查找对应的区县\r\n          const counties = district[cityValue]\r\n          if (counties) {\r\n            for (const county of counties) {\r\n              if (county.value === code) {\r\n                countyValue = county.value\r\n                countyItem = county\r\n                break\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    return { provinceItem, cityItem, countyItem }\r\n  }\r\n\r\n  const { provinceItem, cityItem, countyItem } = findAreaInfo(areaCode)\r\n  if (provinceItem && cityItem && countyItem) {\r\n    // 设置选择的值\r\n    selectedArea.value = [provinceItem, cityItem, countyItem]\r\n    // 同时初始化临时选择区域值\r\n    tempSelectedArea.value = [provinceItem, cityItem, countyItem]    // 设置表单数据，与onAreaConfirm函数中的格式保持一致\r\n    formData.value.area = `${provinceItem.label},${cityItem.label},${countyItem.label}`\r\n    formData.value.areaCode = areaCode\r\n\r\n    console.log('初始化省市区成功:', formData.value.area)\r\n  } else {\r\n    console.error('初始化省市区失败，未找到匹配的区域信息:', areaCode)\r\n  }\r\n}\r\n\r\n// 处理体温下拉框变化\r\nconst onTemperatureSiteChange = (e: any) => {\r\n  const index = parseInt(e.detail.value)\r\n  formData.value.temperatureSiteIndex = index\r\n  formData.value.temperatureSite = temperatureSites[index] // 保存文字值\r\n}\r\n\r\n// 监听药物状态变化\r\nwatch(() => formData.value.medicationStatus, (newVal) => {\r\n  if (newVal === '否') {\r\n    formData.value.medicationDesc = ''\r\n  }\r\n})\r\n\r\n// 监听健康问题状态变化\r\nwatch(() => formData.value.healthProblem, (newVal) => {\r\n  if (newVal === '否') {\r\n    formData.value.problemDesc = ''\r\n  }\r\n})\r\n\r\n// 处理多选题\r\nconst toggleVitalSign = (sign: string) => {\r\n  if (!formData.value.vitalSigns) {\r\n    formData.value.vitalSigns = {\r\n      bodyPart: false,\r\n      temperature: false,\r\n      heartRate: false,\r\n      breathing: false,\r\n      bloodPressure: false,\r\n      oxygen: false\r\n    }\r\n  }\r\n\r\n  // 更新选中状态（切换）\r\n  const newStatus = !formData.value.vitalSigns[sign]\r\n  formData.value.vitalSigns = {\r\n    ...formData.value.vitalSigns,\r\n    [sign]: newStatus\r\n  }\r\n\r\n  // 如果取消选择，清空对应的输入值\r\n  if (!newStatus) {\r\n    switch (sign) {\r\n      case 'bodyPart':\r\n        formData.value.temperatureSiteIndex = 0 // 重置为初始选项\"请选择\"\r\n        formData.value.temperatureSite = temperatureSites[0]\r\n        break\r\n      case 'temperature':\r\n        formData.value.tempertureValue = ''\r\n        break\r\n      case 'heartRate':\r\n        formData.value.heartRateValue = ''\r\n        break\r\n      case 'breathing':\r\n        formData.value.breathingValue = ''\r\n        break\r\n      case 'bloodPressure':\r\n        formData.value.bloodPressureValue = ''\r\n        break\r\n      case 'oxygen':\r\n        formData.value.oxygenValue = ''\r\n        break\r\n    }\r\n  }\r\n}\r\n\r\n// 处理疾病多选题\r\nconst toggleDisease = (disease: string) => {\r\n  if (!formData.value.diseases) {\r\n    formData.value.diseases = {\r\n      smoking: false,\r\n      drinking: false,\r\n      hypertension: false,\r\n      diabetes: false,\r\n      hyperlipidemia: false,\r\n      chronicKidney: false,\r\n      chronicLung: false,\r\n      tumor: false,\r\n      mainDiagnosis: false,\r\n      diseaseSummary: false,\r\n    }\r\n  }\r\n\r\n  // 更新选中状态（切换）\r\n  const newStatus = !formData.value.diseases[disease]\r\n  formData.value.diseases = {\r\n    ...formData.value.diseases,\r\n    [disease]: newStatus\r\n  }\r\n\r\n  // 如果取消选择，清空对应的输入值\r\n  if (!newStatus) {\r\n    switch (disease) {\r\n      case 'mainDiagnosis':\r\n        formData.value.mainDiagnosisDesc = ''\r\n        break\r\n      case 'diseaseSummary':\r\n        formData.value.diseaseSummaryDesc = ''\r\n        break\r\n    }\r\n  }\r\n}\r\n\r\n// 计算地区显示文字\r\nconst displayAreaText = computed(() => {\r\n  // 只使用formData.area显示文字，不再从selectedArea中获取\r\n  // 这样只有在点击确定按钮后，displayAreaText才会更新\r\n  if (formData.value.area) {\r\n    return formData.value.area\r\n  }\r\n\r\n  return ''\r\n})\r\n\r\n// 监听selectedArea变化，用于调试\r\nwatch(selectedArea, (newVal) => {\r\n  console.log('selectedArea变化:', JSON.stringify(newVal))\r\n  console.log('selectedArea类型:', typeof newVal, Array.isArray(newVal))\r\n  if (Array.isArray(newVal) && newVal.length > 0) {\r\n    console.log('第一个元素类型:', typeof newVal[0], newVal[0])\r\n  }\r\n}, { deep: true })\r\n\r\n// 监听tempSelectedArea变化，用于调试\r\nwatch(tempSelectedArea, (newVal) => {\r\n  console.log('tempSelectedArea变化:', JSON.stringify(newVal))\r\n}, { deep: true })\r\n\r\n// 监听formData.area变化\r\nwatch(() => formData.value.area, (newVal) => {\r\n  console.log('formData.area变化:', newVal)\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-scroll-view {\r\n  height: calc(100vh - 44px);\r\n  /* 减去导航栏高度 */\r\n  width: 100%;\r\n}\r\n\r\n.form-container {\r\n  padding: 20rpx;\r\n  padding-bottom: 120rpx;\r\n  /* 增加底部内边距，防止内容被遮挡 */\r\n\r\n  .form-header {\r\n    margin-bottom: 20rpx;\r\n    text-align: center;\r\n    /* 标题居中 */\r\n\r\n    .form-title {\r\n      font-size: 32rpx;\r\n      font-weight: bold;\r\n      color: #333;\r\n    }\r\n  }\r\n\r\n  .form-section {\r\n    background-color: #FFFFFF;\r\n    border-radius: 12rpx;\r\n    padding: 20rpx;\r\n    margin-bottom: 20rpx;\r\n\r\n    .form-item {\r\n      margin-bottom: 30rpx;\r\n\r\n      .form-label {\r\n        display: block;\r\n        font-size: 28rpx;\r\n        color: #333;\r\n        margin-bottom: 20rpx;\r\n      }\r\n\r\n      .inline-form-item {\r\n        display: flex;\r\n        align-items: center;\r\n        margin-bottom: 20rpx;\r\n\r\n        .inline-form-label {\r\n          font-size: 28rpx;\r\n          color: #333;\r\n          min-width: 160rpx;\r\n        }\r\n\r\n        .inline-form-input {\r\n          flex: 1;\r\n          background-color: #F7F7F7;\r\n          border-radius: 8rpx;\r\n          padding: 20rpx;\r\n          font-size: 28rpx;\r\n          color: #333;\r\n          box-sizing: border-box;\r\n          min-height: 80rpx;\r\n\r\n          &:disabled {\r\n            background-color: #F5F5F5;\r\n            color: #666;\r\n          }\r\n        }\r\n      }\r\n\r\n      .date-picker {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        background-color: #F7F7F7;\r\n        border-radius: 8rpx;\r\n        padding: 20rpx;\r\n        font-size: 28rpx;\r\n        color: #333;\r\n        box-sizing: border-box;\r\n        min-height: 80rpx;\r\n\r\n        .placeholder {\r\n          color: #999;\r\n        }\r\n      }\r\n\r\n      .form-input,\r\n      .form-textarea {\r\n        width: 100%;\r\n        background-color: #F7F7F7;\r\n        border-radius: 8rpx;\r\n        padding: 20rpx;\r\n        font-size: 28rpx;\r\n        color: #333;\r\n        box-sizing: border-box;\r\n        min-height: 80rpx;\r\n\r\n        &:disabled {\r\n          background-color: #F5F5F5;\r\n          color: #666;\r\n        }\r\n      }\r\n\r\n      .form-textarea {\r\n        height: 180rpx;\r\n      }\r\n\r\n      .radio-group {\r\n        display: flex;\r\n        flex-direction: column;\r\n        gap: 30rpx;\r\n        /* 增加间距 */\r\n\r\n        .radio-row {\r\n          display: flex;\r\n          gap: 150rpx;\r\n          /* 增加间距 */\r\n          margin-left: 20rpx;\r\n          /* 增加左边距，实现对齐 */\r\n          justify-content: flex-start;\r\n          /* 添加左对齐 */\r\n        }\r\n\r\n        .radio-item {\r\n          display: flex;\r\n          align-items: center;\r\n          min-width: 180rpx;\r\n          width: 240rpx;\r\n          /* 确保宽度一致，增大宽度值以容纳更长的文本 */\r\n\r\n          .radio-btn {\r\n            width: 36rpx;\r\n            height: 36rpx;\r\n            border-radius: 50%;\r\n            border: 2rpx solid #CCCCCC;\r\n            margin-right: 10rpx;\r\n            position: relative;\r\n\r\n            &.checked {\r\n              border-color: #07C160;\r\n\r\n              &:after {\r\n                content: '';\r\n                position: absolute;\r\n                width: 24rpx;\r\n                height: 24rpx;\r\n                background-color: #07C160;\r\n                border-radius: 50%;\r\n                top: 50%;\r\n                left: 50%;\r\n                transform: translate(-50%, -50%);\r\n              }\r\n            }\r\n          }\r\n\r\n          text {\r\n            font-size: 28rpx;\r\n            color: #333;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 添加省市区选择器样式\r\n  .area-picker-container {\r\n    display: flex;\r\n    flex-direction: column;\r\n    margin-bottom: 20rpx;\r\n\r\n    .inline-form-label {\r\n      font-size: 28rpx;\r\n      color: #333;\r\n      margin-bottom: 10rpx;\r\n      display: block;\r\n    }\r\n\r\n    .area-picker-wrapper {\r\n      width: 100%;\r\n      background-color: #F7F7F7;\r\n      border-radius: 8rpx;\r\n      box-sizing: border-box;\r\n      min-height: 80rpx;\r\n\r\n      :deep(.wd-picker__value) {\r\n        text-align: left;\r\n        color: #333;\r\n      }\r\n\r\n      :deep(.wd-picker__action) {\r\n        display: none;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 行内选择器样式\r\n  .inline-picker-wrapper {\r\n    flex: 1;\r\n    width: 100%;\r\n  }\r\n\r\n  .required {\r\n    color: #FF0000;\r\n    margin-right: 4rpx;\r\n  }\r\n\r\n  .submit-section {\r\n    margin-top: 40rpx;\r\n    margin-bottom: 60rpx;\r\n    /* 增加底部间距 */\r\n\r\n    .submit-btn {\r\n      width: 100%;\r\n      background-color: #07C160;\r\n      color: #FFFFFF;\r\n      border-radius: 8rpx;\r\n      font-size: 32rpx;\r\n      padding: 20rpx 0;\r\n    }\r\n  }\r\n}\r\n\r\n.checkbox-group {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 30rpx;\r\n\r\n  .checkbox-row {\r\n    display: flex;\r\n    gap: 150rpx;\r\n    margin-left: 20rpx;\r\n    justify-content: flex-start;\r\n  }\r\n\r\n  .checkbox-item {\r\n    display: flex;\r\n    align-items: center;\r\n    min-width: 180rpx;\r\n    width: 240rpx;\r\n\r\n    .checkbox-btn {\r\n      width: 36rpx;\r\n      height: 36rpx;\r\n      border-radius: 4rpx;\r\n      border: 2rpx solid #CCCCCC;\r\n      margin-right: 10rpx;\r\n      position: relative;\r\n\r\n      &.checked {\r\n        border-color: #07C160;\r\n        background-color: #07C160;\r\n\r\n        &:after {\r\n          content: '';\r\n          position: absolute;\r\n          width: 20rpx;\r\n          height: 10rpx;\r\n          border-left: 4rpx solid #FFFFFF;\r\n          border-bottom: 4rpx solid #FFFFFF;\r\n          top: 45%;\r\n          left: 50%;\r\n          transform: translate(-50%, -50%) rotate(-45deg);\r\n        }\r\n      }\r\n    }\r\n\r\n    text {\r\n      font-size: 28rpx;\r\n      color: #333;\r\n    }\r\n  }\r\n\r\n  .sub-form-item {\r\n    margin-left: 50rpx;\r\n    margin-top: -10rpx;\r\n    margin-bottom: 20rpx;\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    .sub-form-label {\r\n      font-size: 26rpx;\r\n      color: #666;\r\n      min-width: 180rpx;\r\n    }\r\n\r\n    .sub-form-input {\r\n      flex: 1;\r\n      background-color: #F7F7F7;\r\n      border-radius: 8rpx;\r\n      padding: 15rpx;\r\n      font-size: 26rpx;\r\n      color: #333;\r\n      min-height: 70rpx;\r\n      width: 100%;\r\n      box-sizing: border-box;\r\n    }\r\n\r\n    .picker {\r\n      flex: 1;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      background-color: #F7F7F7;\r\n      border-radius: 8rpx;\r\n      padding: 15rpx;\r\n      font-size: 26rpx;\r\n      color: #333;\r\n      min-height: 70rpx;\r\n      width: 100%;\r\n      box-sizing: border-box;\r\n    }\r\n  }\r\n\r\n  /* 添加新的样式以支持两列布局 */\r\n  .sub-form-items-row {\r\n    display: flex;\r\n    margin-left: 50rpx;\r\n    gap: 20rpx;\r\n    margin-top: -10rpx;\r\n    margin-bottom: 20rpx;\r\n\r\n    .sub-form-item {\r\n      flex: 1;\r\n      width: 50%;\r\n      min-width: calc(50% - 10rpx);\r\n      /* 确保即使只有一个元素也占据一半宽度 */\r\n      margin: 0;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .picker-container {\r\n        width: 100%;\r\n        flex: 1;\r\n        display: block;\r\n      }\r\n\r\n      .temp-picker {\r\n        width: 100%;\r\n        flex: 1;\r\n        display: block;\r\n      }\r\n\r\n      .sub-form-input {\r\n        flex: 1;\r\n        background-color: #F7F7F7;\r\n        border-radius: 8rpx;\r\n        padding: 15rpx;\r\n        font-size: 26rpx;\r\n        color: #333;\r\n        min-height: 70rpx;\r\n        width: 100%;\r\n        box-sizing: border-box;\r\n      }\r\n\r\n      .picker {\r\n        flex: 1;\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        background-color: #F7F7F7;\r\n        border-radius: 8rpx;\r\n        padding: 15rpx;\r\n        font-size: 26rpx;\r\n        color: #333;\r\n        min-height: 70rpx;\r\n        width: 100%;\r\n        box-sizing: border-box;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-data/registration/form.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "ref", "onMounted", "computed", "areaData", "district", "nextTick", "uni", "_a", "_b", "watch"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmoBA,UAAM,YAAYA,WAAAA,aAAa;AAEzB,UAAA,QAAQC,cAAS,IAAA,EAAE;AACzBC,kBAAAA,UAAU,MAAM;AACd,YAAM,QAAQ,gBAAgB;AAC9B,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AACpC,YAAA,QAAS,YAAoB,WAAW,CAAC;AAE3C,UAAA,UAAU,SAAS,OAAO;AACnB,iBAAA,MAAM,QAAQ,UAAU,SAAS;AAAA,MAAA;AAAA,IAC5C,CACD;AAGD,UAAM,OAAOC,cAAAA,SAAS,MAAM,MAAM,MAAM,QAAQ,KAAK;AACrD,UAAM,aAAaA,cAAA,SAAS,MAAM,KAAK,UAAU,MAAM;AAGjD,UAAA,sCAAqB,KAAK,GAAE,cAAc,MAAM,GAAG,EAAE,CAAC;AAItD,UAAA,eAAeF,cAAI,IAAA,EAAE;AAErB,UAAA,mBAAmBA,cAAI,IAAA,EAAE;AAEzB,UAAA,WAAW,mBAAKG;AACtB,UAAM,cAAcH,cAAAA,IAAI;AAAA,MACtB,SAAS,CAAC;AAAA,MACV,SAAS,SAAS,CAAC,EAAE,CAAC,EAAE,KAAK;AAAA,MAC7B,SAAS,SAAS,SAAS,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK;AAAA,IAAA,CACjD;AAGD,UAAM,qBAAqB,CAAC,YAAY,OAAO,aAAa,YAAY;;AAChE,YAAA,OAAO,MAAM,WAAW;AAC9B,UAAI,gBAAgB,GAAG;AAErB,mBAAW,cAAc,GAAG,SAAS,KAAK,KAAK,CAAC;AAErC,mBAAA,cAAc,GAAG,SAAS,SAAS,KAAK,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC;AAG/D,YAAA,CAAC,iBAAiB,MAAM,CAAC,OAAK,sBAAiB,MAAM,CAAC,MAAxB,mBAA2B,WAAU,KAAK,OAAO;AAChE,2BAAA,MAAM,CAAC,IAAI;AAE5B,2BAAiB,MAAM,CAAC,IAAI,SAAS,KAAK,KAAK,EAAE,CAAC;AAClD,2BAAiB,MAAM,CAAC,IAAI,SAAS,SAAS,KAAK,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC;AAAA,QAAA;AAAA,MACvE,WACS,gBAAgB,GAAG;AAE5B,mBAAW,cAAc,GAAG,SAAS,KAAK,KAAK,CAAC;AAG5C,YAAA,CAAC,iBAAiB,MAAM,CAAC,OAAK,sBAAiB,MAAM,CAAC,MAAxB,mBAA2B,WAAU,KAAK,OAAO;AAChE,2BAAA,MAAM,CAAC,IAAI;AAE5B,2BAAiB,MAAM,CAAC,IAAI,SAAS,KAAK,KAAK,EAAE,CAAC;AAAA,QAAA;AAAA,MACpD,WACS,gBAAgB,GAAG;AAExB,YAAA,CAAC,iBAAiB,MAAM,CAAC,OAAK,sBAAiB,MAAM,CAAC,MAAxB,mBAA2B,WAAU,KAAK,OAAO;AAChE,2BAAA,MAAM,CAAC,IAAI;AAAA,QAAA;AAAA,MAC9B;AAEM,cAAA;AAAA,IACV;AAGA,UAAM,gBAAgB,CAAC,EAAE,OAAO,oBAAoB;;AAClD,cAAQ,IAAI,0BAA0B,KAAK,UAAU,KAAK,CAAC;AAC3D,cAAQ,IAAI,kCAAkC,KAAK,UAAU,aAAa,CAAC;AAC3E,cAAQ,IAAI,mCAAmC,KAAK,UAAU,iBAAiB,KAAK,CAAC;AAGjF,UAAA,iBAAiB,cAAc,WAAW,GAAG;AAE/C,cAAM,aAAW,mBAAc,CAAC,MAAf,mBAAkB,UAAS;AAC5C,cAAM,SAAO,mBAAc,CAAC,MAAf,mBAAkB,UAAS;AACxC,cAAMI,cAAW,mBAAc,CAAC,MAAf,mBAAkB,UAAS;AAE5C,gBAAQ,IAAI,eAAe,UAAU,MAAMA,SAAQ;AAGnD,qBAAa,QAAQ,CAAC,GAAG,iBAAiB,KAAK;AAG/C,iBAAS,MAAM,OAAO,GAAG,QAAQ,IAAI,IAAI,IAAIA,SAAQ;AACrD,iBAAS,MAAM,aAAW,mBAAc,CAAC,MAAf,mBAAkB,UAAS;AAGrDC,sBAAAA,WAAS,MAAM;AACb,kBAAQ,IAAI,cAAc,SAAS,MAAM,IAAI;AAAA,QAAA,CAC9C;AAED,gBAAQ,IAAI,iBAAiB,SAAS,MAAM,IAAI;AAChD,gBAAQ,IAAI,cAAc,SAAS,MAAM,QAAQ;AAAA,MAAA,OAC5C;AACG,gBAAA,MAAM,uBAAuB,aAAa;AAC1C,gBAAA,MAAM,mBAAmB,aAAa,KAAK;AAEnD,YAAI,SAAS,MAAM,QAAQ,KAAK,KAAK,MAAM,WAAW,GAAG;AACnD,cAAA;AACI,kBAAA,eAAe,MAAM,CAAC;AACtB,kBAAA,WAAW,MAAM,CAAC;AAClB,kBAAA,eAAe,MAAM,CAAC;AAGtB,kBAAA,eAAe,SAAS,CAAC,EAAE,KAAK,CAAQ,SAAA,KAAK,UAAU,YAAY;AACnE,kBAAA,YAAW,cAAS,YAAY,MAArB,mBAAwB,KAAK,CAAQ,SAAA,KAAK,UAAU;AAC/D,kBAAA,gBAAe,cAAS,QAAQ,MAAjB,mBAAoB,KAAK,CAAQ,SAAA,KAAK,UAAU;AAEjE,gBAAA,gBAAgB,YAAY,cAAc;AACnC,uBAAA,MAAM,OAAO,GAAG,aAAa,KAAK,IAAI,SAAS,KAAK,IAAI,aAAa,KAAK;AACnF,uBAAS,MAAM,WAAW;AAC1B,sBAAQ,IAAI,sBAAsB,SAAS,MAAM,IAAI;AAAA,YAAA;AAAA,mBAEhD,OAAO;AACN,oBAAA,MAAM,oBAAoB,KAAK;AAAA,UAAA;AAAA,QACzC;AAAA,MACF;AAAA,IAEJ;AAGA,UAAM,mBAAmB,CAAC,OAAO,MAAM,MAAM,MAAM,IAAI;AAGvD,UAAM,WAAWL,cAAAA,IAAI;AAAA,MACnB,MAAM;AAAA,MACN,OAAO;AAAA,MACP,KAAK;AAAA,MACL,eAAe;AAAA,MACf,UAAU;AAAA,MACV,WAAW;AAAA,MACX,oBAAoB;AAAA,MACpB,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,MACT,UAAU;AAAA;AAAA,MACV,eAAe;AAAA,MACf,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,QACV,UAAU;AAAA,QACV,aAAa;AAAA,QACb,WAAW;AAAA,QACX,WAAW;AAAA,QACX,eAAe;AAAA,QACf,QAAQ;AAAA,MACV;AAAA,MACA,sBAAsB;AAAA,MACtB,iBAAiB;AAAA;AAAA,MACjB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,oBAAoB;AAAA,MACpB,aAAa;AAAA,MACb,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,UAAU;AAAA,QACR,SAAS;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,eAAe;AAAA,QACf,aAAa;AAAA,QACb,OAAO;AAAA,QACP,eAAe;AAAA,QACf,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,MACnB,oBAAoB;AAAA,IAAA,CACrB;AAGK,UAAA,oBAAoB,CAAC,MAAW;AAC3B,eAAA,MAAM,YAAY,EAAE,OAAO;AAEhC,UAAA,EAAE,OAAO,OAAO;AACA,YAAI,KAAK,EAAE,OAAO,KAAK,EAAE,YAAY;AACnC,SAAA,oBAAI,KAAK,GAAE,YAAY;AAAA,MAAA;AAAA,IAG/C;AAGA,UAAM,aAAa,MAAM;;AAEf,cAAA,IAAI,WAAW,SAAS,KAAK;AACrC,cAAQ,IAAI,kBAAkB,SAAS,MAAM,IAAI;AACzC,cAAA,IAAI,iBAAiB,aAAa,KAAK;AAE3C,UAAA,aAAa,SAAS,MAAM,QAAQ,aAAa,KAAK,KAAK,aAAa,MAAM,WAAW,GAAG;AAC1F,YAAA;AACF,kBAAQ,IAAI,wBAAwB,KAAK,UAAU,aAAa,KAAK,CAAC;AAGlE,cAAA,aAAa,SAAS,aAAa,MAAM,WAAW,OACtD,kBAAa,MAAM,CAAC,MAApB,mBAAuB,YACvB,kBAAa,MAAM,CAAC,MAApB,mBAAuB,YACvB,kBAAa,MAAM,CAAC,MAApB,mBAAuB,QAAO;AAE9B,kBAAM,WAAW,aAAa,MAAM,CAAC,EAAE;AACvC,kBAAM,OAAO,aAAa,MAAM,CAAC,EAAE;AACnC,kBAAMI,YAAW,aAAa,MAAM,CAAC,EAAE;AAEvC,oBAAQ,IAAI,gBAAgB,UAAU,MAAMA,SAAQ;AACpD,qBAAS,MAAM,OAAO,GAAG,QAAQ,IAAI,IAAI,IAAIA,SAAQ;AACrD,qBAAS,MAAM,WAAW,aAAa,MAAM,CAAC,EAAE;AAChD,oBAAQ,IAAI,cAAc,SAAS,MAAM,IAAI;AAAA,UAAA,OACxC;AACG,oBAAA,IAAI,4BAA4B,aAAa,KAAK;AAEtD,gBAAA,CAAC,SAAS,MAAM,MAAM;AACxB,sBAAQ,KAAK,gBAAgB;AAAA,YAAA;AAAA,UAC/B;AAAA,iBAEK,OAAO;AACd,kBAAQ,MAAM,YAAY,OAAO,aAAa,KAAK;AAAA,QAAA;AAAA,MACrD;AAIE,UAAA,CAAC,SAAS,MAAM,MAAM;AACxBE,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MAAA;AAGE,UAAA,CAAC,SAAS,MAAM,OAAO;AACzBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MAAA;AAGE,UAAA,CAAC,SAAS,MAAM,KAAK;AACvBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MAAA;AAGE,UAAA,CAAC,SAAS,MAAM,eAAe;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MAAA;AAGE,UAAA,CAAC,SAAS,MAAM,WAAW;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MAAA;AAGE,UAAA,CAAC,SAAS,MAAM,UAAU;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MAAA;AAGE,UAAA,CAAC,SAAS,MAAM,oBAAoB;AACtCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MAAA;AAGE,UAAA,CAAC,SAAS,MAAM,QAAQ;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MAAA;AAGE,UAAA,CAAC,SAAS,MAAM,YAAY;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MAAA;AAGE,UAAA,CAAC,SAAS,MAAM,WAAW;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MAAA;AAGE,UAAA,CAAC,SAAS,MAAM,MAAM;AAChB,gBAAA,IAAI,eAAe,aAAa,KAAK;AAC7CA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MAAA;AAGE,UAAA,CAAC,SAAS,MAAM,SAAS;AAC3BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MAAA;AAGF,UAAI,CAAC,SAAS,MAAM,cACjB,CAAC,SAAS,MAAM,WAAW,YAC1B,CAAC,SAAS,MAAM,WAAW,eAC3B,CAAC,SAAS,MAAM,WAAW,aAC3B,CAAC,SAAS,MAAM,WAAW,aAC3B,CAAC,SAAS,MAAM,WAAW,iBAC3B,CAAC,SAAS,MAAM,WAAW,QAAS;AACtCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MAAA;AAIF,UAAI,SAAS,MAAM,WAAW,YAAY,SAAS,MAAM,yBAAyB,GAAG;AACnFA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MAAA;AAGF,UAAI,SAAS,MAAM,WAAW,eAAe,CAAC,SAAS,MAAM,iBAAiB;AAC5EA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MAAA;AAIF,UAAI,SAAS,MAAM,WAAW,aAAa,CAAC,SAAS,MAAM,gBAAgB;AACzEA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MAAA;AAIF,UAAI,SAAS,MAAM,WAAW,aAAa,CAAC,SAAS,MAAM,gBAAgB;AACzEA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MAAA;AAIF,UAAI,SAAS,MAAM,WAAW,iBAAiB,CAAC,SAAS,MAAM,oBAAoB;AACjFA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MAAA;AAIF,UAAI,SAAS,MAAM,WAAW,UAAU,CAAC,SAAS,MAAM,aAAa;AACnEA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MAAA;AAGE,UAAA,CAAC,SAAS,MAAM,kBAAkB;AACpCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MAAA;AAIF,UAAI,SAAS,MAAM,qBAAqB,OAAO,CAAC,SAAS,MAAM,gBAAgB;AAC7EA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MAAA;AAGE,UAAA,CAAC,SAAS,MAAM,YAAY;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MAAA;AAGE,UAAA,CAAC,SAAS,MAAM,eAAe;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MAAA;AAIF,UAAI,SAAS,MAAM,kBAAkB,OAAO,CAAC,SAAS,MAAM,aAAa;AACvEA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MAAA;AAGE,UAAA,CAAC,SAAS,MAAM,YAAY;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MAAA;AAGE,UAAA,CAAC,SAAS,MAAM,YACjB,CAAC,SAAS,MAAM,SAAS,WACxB,CAAC,SAAS,MAAM,SAAS,YACzB,CAAC,SAAS,MAAM,SAAS,gBACzB,CAAC,SAAS,MAAM,SAAS,YACzB,CAAC,SAAS,MAAM,SAAS,kBACzB,CAAC,SAAS,MAAM,SAAS,iBACzB,CAAC,SAAS,MAAM,SAAS,eACzB,CAAC,SAAS,MAAM,SAAS,SACzB,CAAC,SAAS,MAAM,SAAS,iBACzB,CAAC,SAAS,MAAM,SAAS,gBAAiB;AAC5CA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MAAA;AAIF,UAAI,SAAS,MAAM,SAAS,iBAAiB,CAAC,SAAS,MAAM,mBAAmB;AAC9EA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MAAA;AAIF,UAAI,SAAS,MAAM,SAAS,kBAAkB,CAAC,SAAS,MAAM,oBAAoB;AAChFA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MAAA;AAIFA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MAAA,CACR;AAED,YAAM,cAAc;AAAA,QAClB,MAAM,SAAS,MAAM;AAAA,QACrB,OAAO,SAAS,MAAM;AAAA,QACtB,KAAK,SAAS,MAAM;AAAA,QACpB,eAAe,SAAS,MAAM;AAAA,QAC9B,WAAW,SAAS,MAAM;AAAA,QAC1B,UAAU,SAAS,MAAM;AAAA,QACzB,oBAAoB,SAAS,MAAM;AAAA,QACnC,QAAQ,SAAS,MAAM;AAAA,QACvB,YAAY,SAAS,MAAM;AAAA,QAC3B,WAAW,SAAS,MAAM;AAAA,QAC1B,MAAM,SAAS,MAAM;AAAA,QACrB,SAAS,SAAS,MAAM;AAAA,QACxB,iBAAiB,SAAS,MAAM;AAAA,QAChC,iBAAiB,SAAS,MAAM;AAAA,QAChC,gBAAgB,SAAS,MAAM;AAAA,QAC/B,gBAAgB,SAAS,MAAM;AAAA,QAC/B,oBAAoB,SAAS,MAAM;AAAA,QACnC,aAAa,SAAS,MAAM;AAAA,QAC5B,gBAAgB,SAAS,MAAM;AAAA,QAC/B,YAAY,SAAS,MAAM;AAAA,QAC3B,aAAa,SAAS,MAAM;AAAA,QAC5B,YAAY,SAAS,MAAM;AAAA,QAC3B,UAAU,SAAS,MAAM;AAAA,QACzB,mBAAmB,SAAS,MAAM;AAAA,QAClC,oBAAoB,SAAS,MAAM;AAAA,QACnC,QAAQ,UAAU,SAAS;AAAA,MAC7B;AAEQ,cAAA,IAAI,cAAc,SAAS,KAAK;AAChC,cAAA,IAAI,gBAAgB,WAAW;AAGvCA,oBAAAA,MAAI,QAAQ;AAAA,QACV,KAAK,GAAG,6BAAmC;AAAA,QAC3C,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,SAAS,CAAC,QAAQ;;AAChBA,wBAAAA,MAAI,YAAY;AAEZ,eAAAC,MAAA,IAAI,SAAJ,gBAAAA,IAAU,SAAS;AACrB,sBAAU,aAAa,EAAE,UAAU,SAAS,MAAM,MAAM;AACxD,sBAAU,aAAa,EAAE,KAAK,SAAS,MAAM,KAAK;AAEnC,2BAAA;AAEfD,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,YAAY;AAAA,cACZ,SAAS,MAAM;AACbA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,KAAK;AAAA,gBAAA,CACN;AAAA,cAAA;AAAA,YACH,CACD;AAAA,UAAA,OACI;AACC,kBAAA,aAAWE,MAAA,IAAI,SAAJ,gBAAAA,IAAU,YAAW;AACtCF,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,SAAS;AAAA,cACT,YAAY;AAAA,YAAA,CACb;AAAA,UAAA;AAAA,QAEL;AAAA,QACA,MAAM,CAAC,QAAQ;AACbA,wBAAAA,MAAI,YAAY;AACV,gBAAA,WAAW,IAAI,UAAU;AAC/BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,UAAA,CACb;AAAA,QAAA;AAAA,MACH,CACD;AAAA,IACH;AAEA,UAAM,iBAAiB,MAAM;AACrB,YAAA,SAAS,UAAU,SAAS;AAC5B,YAAA,eAAe,UAAU,SAAS;AAExC,cAAQ,IAAI,iBAAiB,EAAE,QAAQ,cAAc;AAErDA,oBAAAA,MAAI,QAAQ;AAAA,QACV,KAAK,GAAG,6BAAmC;AAAA,QAC3C,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN,kBAAkB,UAAU,SAAS;AAAA,QACvC;AAAA,QACA,MAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF;AAAA,QAAG,SAAS,CAAC,QAAQ;AACX,kBAAA,IAAI,kBAAkB,IAAI,IAAI;AAEtC,gBAAM,eAAe,UAAU,SAAS,YAAY,CAAC;AACrD,cAAI,CAAC,aAAa,SAAS,GAAG,GAAG;AACrB,sBAAA,aAAa,EAAE,UAAU,CAAC,GAAG,cAAc,GAAG,GAAG;AAAA,UAAA;AAAA,QAE/D;AAAA,QACA,MAAM,CAAC,QAAQ;AACL,kBAAA,MAAM,kBAAkB,GAAG;AAAA,QAAA;AAAA,MAErC,CACD;AAAA,IACH;AAyEM,UAAA,0BAA0B,CAAC,MAAW;AAC1C,YAAM,QAAQ,SAAS,EAAE,OAAO,KAAK;AACrC,eAAS,MAAM,uBAAuB;AAC7B,eAAA,MAAM,kBAAkB,iBAAiB,KAAK;AAAA,IACzD;AAGAG,kBAAAA,MAAM,MAAM,SAAS,MAAM,kBAAkB,CAAC,WAAW;AACvD,UAAI,WAAW,KAAK;AAClB,iBAAS,MAAM,iBAAiB;AAAA,MAAA;AAAA,IAClC,CACD;AAGDA,kBAAAA,MAAM,MAAM,SAAS,MAAM,eAAe,CAAC,WAAW;AACpD,UAAI,WAAW,KAAK;AAClB,iBAAS,MAAM,cAAc;AAAA,MAAA;AAAA,IAC/B,CACD;AAGK,UAAA,kBAAkB,CAAC,SAAiB;AACpC,UAAA,CAAC,SAAS,MAAM,YAAY;AAC9B,iBAAS,MAAM,aAAa;AAAA,UAC1B,UAAU;AAAA,UACV,aAAa;AAAA,UACb,WAAW;AAAA,UACX,WAAW;AAAA,UACX,eAAe;AAAA,UACf,QAAQ;AAAA,QACV;AAAA,MAAA;AAIF,YAAM,YAAY,CAAC,SAAS,MAAM,WAAW,IAAI;AACjD,eAAS,MAAM,aAAa,iCACvB,SAAS,MAAM,aADQ;AAAA,QAE1B,CAAC,IAAI,GAAG;AAAA,MACV;AAGA,UAAI,CAAC,WAAW;AACd,gBAAQ,MAAM;AAAA,UACZ,KAAK;AACH,qBAAS,MAAM,uBAAuB;AAC7B,qBAAA,MAAM,kBAAkB,iBAAiB,CAAC;AACnD;AAAA,UACF,KAAK;AACH,qBAAS,MAAM,kBAAkB;AACjC;AAAA,UACF,KAAK;AACH,qBAAS,MAAM,iBAAiB;AAChC;AAAA,UACF,KAAK;AACH,qBAAS,MAAM,iBAAiB;AAChC;AAAA,UACF,KAAK;AACH,qBAAS,MAAM,qBAAqB;AACpC;AAAA,UACF,KAAK;AACH,qBAAS,MAAM,cAAc;AAC7B;AAAA,QAAA;AAAA,MACJ;AAAA,IAEJ;AAGM,UAAA,gBAAgB,CAAC,YAAoB;AACrC,UAAA,CAAC,SAAS,MAAM,UAAU;AAC5B,iBAAS,MAAM,WAAW;AAAA,UACxB,SAAS;AAAA,UACT,UAAU;AAAA,UACV,cAAc;AAAA,UACd,UAAU;AAAA,UACV,gBAAgB;AAAA,UAChB,eAAe;AAAA,UACf,aAAa;AAAA,UACb,OAAO;AAAA,UACP,eAAe;AAAA,UACf,gBAAgB;AAAA,QAClB;AAAA,MAAA;AAIF,YAAM,YAAY,CAAC,SAAS,MAAM,SAAS,OAAO;AAClD,eAAS,MAAM,WAAW,iCACrB,SAAS,MAAM,WADM;AAAA,QAExB,CAAC,OAAO,GAAG;AAAA,MACb;AAGA,UAAI,CAAC,WAAW;AACd,gBAAQ,SAAS;AAAA,UACf,KAAK;AACH,qBAAS,MAAM,oBAAoB;AACnC;AAAA,UACF,KAAK;AACH,qBAAS,MAAM,qBAAqB;AACpC;AAAA,QAAA;AAAA,MACJ;AAAA,IAEJ;AAGM,UAAA,kBAAkBP,cAAAA,SAAS,MAAM;AAGjC,UAAA,SAAS,MAAM,MAAM;AACvB,eAAO,SAAS,MAAM;AAAA,MAAA;AAGjB,aAAA;AAAA,IAAA,CACR;AAGKO,wBAAA,cAAc,CAAC,WAAW;AAC9B,cAAQ,IAAI,mBAAmB,KAAK,UAAU,MAAM,CAAC;AACrD,cAAQ,IAAI,mBAAmB,OAAO,QAAQ,MAAM,QAAQ,MAAM,CAAC;AACnE,UAAI,MAAM,QAAQ,MAAM,KAAK,OAAO,SAAS,GAAG;AACtC,gBAAA,IAAI,YAAY,OAAO,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,MAAA;AAAA,IACrD,GACC,EAAE,MAAM,MAAM;AAGXA,wBAAA,kBAAkB,CAAC,WAAW;AAClC,cAAQ,IAAI,uBAAuB,KAAK,UAAU,MAAM,CAAC;AAAA,IAAA,GACxD,EAAE,MAAM,MAAM;AAGjBA,kBAAAA,MAAM,MAAM,SAAS,MAAM,MAAM,CAAC,WAAW;AACnC,cAAA,IAAI,oBAAoB,MAAM;AAAA,IAAA,CACvC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC35CD,GAAG,WAAW,eAAe;"}