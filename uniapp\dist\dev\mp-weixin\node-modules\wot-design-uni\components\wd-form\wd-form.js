"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __objRest = (source, exclude) => {
  var target = {};
  for (var prop in source)
    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)
      target[prop] = source[prop];
  if (source != null && __getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(source)) {
      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))
        target[prop] = source[prop];
    }
  return target;
};
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../../../common/vendor.js");
if (!Math) {
  wdToast();
}
const wdToast = () => "../wd-toast/wd-toast.js";
const __default__ = {
  name: "wd-form",
  options: {
    addGlobalClass: true,
    virtualHost: true,
    styleIsolation: "shared"
  }
};
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, __default__), {
  props: common_vendor.formProps,
  setup(__props, { expose: __expose }) {
    const { show: showToast } = common_vendor.useToast("wd-form-toast");
    const props = __props;
    const { children, linkChildren } = common_vendor.useChildren(common_vendor.FORM_KEY);
    let errorMessages = common_vendor.reactive({});
    linkChildren({ props, errorMessages });
    common_vendor.watch(
      () => props.model,
      () => {
        if (props.resetOnChange) {
          clearMessage();
        }
      },
      { immediate: true, deep: true }
    );
    function validate(prop) {
      return __async(this, null, function* () {
        const errors = [];
        let valid = true;
        const promises = [];
        const formRules = getMergeRules();
        const propsToValidate = common_vendor.isArray(prop) ? prop : common_vendor.isDef(prop) ? [prop] : [];
        const rulesToValidate = propsToValidate.length > 0 ? propsToValidate.reduce((acc, key) => {
          if (formRules[key]) {
            acc[key] = formRules[key];
          }
          return acc;
        }, {}) : formRules;
        for (const propName in rulesToValidate) {
          const rules = rulesToValidate[propName];
          const value = common_vendor.getPropByPath(props.model, propName);
          if (rules && rules.length > 0) {
            for (const rule of rules) {
              if (rule.required && (!common_vendor.isDef(value) || value === "")) {
                errors.push({
                  prop: propName,
                  message: rule.message
                });
                valid = false;
                break;
              }
              if (rule.pattern && !rule.pattern.test(value)) {
                errors.push({
                  prop: propName,
                  message: rule.message
                });
                valid = false;
                break;
              }
              const _a = rule, { validator } = _a, ruleWithoutValidator = __objRest(_a, ["validator"]);
              if (validator) {
                const result = validator(value, ruleWithoutValidator);
                if (common_vendor.isPromise(result)) {
                  promises.push(
                    result.then((res) => {
                      if (typeof res === "string") {
                        errors.push({
                          prop: propName,
                          message: res
                        });
                        valid = false;
                      } else if (typeof res === "boolean" && !res) {
                        errors.push({
                          prop: propName,
                          message: rule.message
                        });
                        valid = false;
                      }
                    }).catch((error) => {
                      const message = common_vendor.isDef(error) ? common_vendor.isString(error) ? error : error.message || rule.message : rule.message;
                      errors.push({ prop: propName, message });
                      valid = false;
                    })
                  );
                } else {
                  if (!result) {
                    errors.push({
                      prop: propName,
                      message: rule.message
                    });
                    valid = false;
                  }
                }
              }
            }
          }
        }
        yield Promise.all(promises);
        showMessage(errors);
        if (valid) {
          if (propsToValidate.length) {
            propsToValidate.forEach(clearMessage);
          } else {
            clearMessage();
          }
        }
        return {
          valid,
          errors
        };
      });
    }
    function getMergeRules() {
      const mergedRules = common_vendor.deepClone(props.rules);
      const childrenProps = children.map((child) => child.prop);
      Object.keys(mergedRules).forEach((key) => {
        if (!childrenProps.includes(key)) {
          delete mergedRules[key];
        }
      });
      children.forEach((item) => {
        if (common_vendor.isDef(item.prop) && common_vendor.isDef(item.rules) && item.rules.length) {
          if (mergedRules[item.prop]) {
            mergedRules[item.prop] = [...mergedRules[item.prop], ...item.rules];
          } else {
            mergedRules[item.prop] = item.rules;
          }
        }
      });
      return mergedRules;
    }
    function showMessage(errors) {
      const childrenProps = children.map((e) => e.prop).filter(Boolean);
      const messages = errors.filter((error) => error.message && childrenProps.includes(error.prop));
      if (messages.length) {
        messages.sort((a, b) => {
          return childrenProps.indexOf(a.prop) - childrenProps.indexOf(b.prop);
        });
        if (props.errorType === "toast") {
          showToast(messages[0].message);
        } else if (props.errorType === "message") {
          messages.forEach((error) => {
            errorMessages[error.prop] = error.message;
          });
        }
      }
    }
    function clearMessage(prop) {
      if (prop) {
        errorMessages[prop] = "";
      } else {
        Object.keys(errorMessages).forEach((key) => {
          errorMessages[key] = "";
        });
      }
    }
    function reset() {
      clearMessage();
    }
    __expose({ validate, reset });
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: props.errorType === "toast"
      }, props.errorType === "toast" ? {
        b: common_vendor.p({
          selector: "wd-form-toast"
        })
      } : {}, {
        c: common_vendor.n(`wd-form ${_ctx.customClass}`),
        d: common_vendor.s(_ctx.customStyle)
      });
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d124091c"]]);
wx.createComponent(Component);
//# sourceMappingURL=wd-form.js.map
