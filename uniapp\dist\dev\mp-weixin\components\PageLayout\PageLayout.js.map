{"version": 3, "file": "PageLayout.js", "sources": ["../../../../../src/components/PageLayout/PageLayout.vue", "../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvY29tcG9uZW50cy9QYWdlTGF5b3V0L1BhZ2VMYXlvdXQudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"pageLayout\">\r\n    <view\r\n      v-if=\"navbarShow\"\r\n      :class=\"{ pageNav: true, transparent: navBgTransparent, fixed: navFixed }\"\r\n      :style=\"{ height: `${statusBarHeight + navHeight}px` }\"\r\n    >\r\n      <view class=\"statusBar\" :style=\"{ height: `${statusBarHeight}px` }\"></view>\r\n      <wd-navbar\r\n        :bordered=\"!navBgTransparent\"\r\n        :title=\"navTitle || '心衰院外管理'\"\r\n        :leftText=\"navLeftText\"\r\n        :leftArrow=\"navLeftArrow\"\r\n        :rightText=\"navRightText\"\r\n        @clickLeft=\"handleClickLeft\"\r\n        @clickRight=\"handleClickRight\"\r\n        custom-class=\"nav\"\r\n      >\r\n        <template v-if=\"$slots.navRight\" #right>\r\n          <slot name=\"navRight\"></slot>\r\n        </template>\r\n      </wd-navbar>\r\n    </view>\r\n    <view class=\"pageContent\">\r\n      <slot></slot>\r\n    </view>\r\n    <view class=\"tabbar\"></view>\r\n    <wd-toast></wd-toast>\r\n    <wd-message-box></wd-message-box>\r\n    <wd-notify></wd-notify>\r\n  </view>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { useSlots } from 'vue'\r\nimport { useRouter } from '@/plugin/uni-mini-router'\r\nimport { useParamsStore } from '@/store/page-params'\r\n\r\ndefineOptions({\r\n  name: 'pageLayout',\r\n  options: {\r\n    // apply-shared‌：当前页面样式会影响到子组件样式.(小程序)\r\n    // shared‌：当前页面样式影响到子组件，子组件样式也会影响到当前页面.(小程序)\r\n    styleIsolation: 'shared',\r\n  },\r\n})\r\nconst paramsStore = useParamsStore()\r\nconst router = useRouter()\r\nconst props = defineProps({\r\n  backRouteName: {\r\n    type: String,\r\n    default: '',\r\n  },\r\n  backRoutePath: {\r\n    type: String,\r\n    default: '',\r\n  },\r\n  routeParams: {\r\n    type: Object,\r\n    default: () => {},\r\n  },\r\n  routeQuery: {\r\n    type: Object,\r\n    default: () => {},\r\n  },\r\n  routeMethod: {\r\n    type: String,\r\n    default: 'replace',\r\n  },\r\n  navbarShow: {\r\n    type: Boolean,\r\n    default: true,\r\n  },\r\n  navBgTransparent: {\r\n    type: Boolean,\r\n    default: false,\r\n  },\r\n  navFixed: {\r\n    type: Boolean,\r\n    default: false,\r\n  },\r\n  type: {\r\n    type: String,\r\n    default: 'page', //'page','popup'\r\n  },\r\n  navTitle: {\r\n    type: String,\r\n    default: '',\r\n  },\r\n  navLeftText: {\r\n    type: String,\r\n    default: '返回',\r\n  },\r\n  navLeftArrow: {\r\n    typeof: Boolean,\r\n    default: true,\r\n  },\r\n  navRightText: {\r\n    typeof: String,\r\n    default: '',\r\n  },\r\n})\r\nconst slot = useSlots()\r\nconst globalData = getApp().globalData\r\nconst { systemInfo, navHeight } = globalData\r\nconst { statusBarHeight } = systemInfo\r\nconst emit = defineEmits(['navBack', 'navRight'])\r\nconst handleClickLeft = () => {\r\n  emit('navBack')\r\n  // 只有在页面中才默认返回，弹层中不返回\r\n  if (props.type === 'page') {\r\n    const pages = getCurrentPages()\r\n    if (props.backRouteName || props.backRoutePath) {\r\n      const prevPage = pages[pages.length - 2]\r\n      if (prevPage) {\r\n        const route = prevPage.route\r\n        const name = route.split('/').pop()\r\n        if (route === props.backRoutePath || props.backRouteName === name) {\r\n          router.back()\r\n          clearPageParamsCache()\r\n          return\r\n        }\r\n      }\r\n      if (props.backRouteName) {\r\n        router[props.routeMethod]({ name: props.backRouteName, params: props.routeParams })\r\n        clearPageParamsCache()\r\n      } else {\r\n        router[props.routeMethod]({ name: props.backRoutePath, query: props.routeQuery })\r\n        clearPageParamsCache()\r\n      }\r\n    } else {\r\n      router.back()\r\n      clearPageParamsCache()\r\n    }\r\n  }\r\n}\r\nconst clearPageParamsCache = () => {\r\n  // 清除页面传参缓存\r\n  const pages = getCurrentPages()\r\n  const curPage = pages[pages.length - 1]\r\n  const curRoute = curPage.route\r\n  const name = curRoute.split('/').pop()\r\n  paramsStore.clearPageParams(name)\r\n}\r\nconst handleClickRight = () => {\r\n  emit('navRight')\r\n}\r\nconsole.log('props:', props)\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.pageLayout {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n  width: 100vw;\r\n  .pageNav {\r\n    background-image: linear-gradient(135deg, #2DCB70 0%, #07C160 100%);\r\n    &.transparent {\r\n      background-image: none;\r\n    }\r\n    &.fixed {\r\n      position: fixed;\r\n      top: 0;\r\n      left: 0;\r\n      width: 100%;\r\n    }\r\n    .statusBar {\r\n      width: 100%;\r\n      height: 0;\r\n    }\r\n    :deep(.wd-navbar) {\r\n      background-color: transparent;\r\n      --wot-navbar-title-font-weight: 700;\r\n      --wot-navbar-title-color: #ffffff;\r\n      --wot-navbar-arrow-size: 18px;\r\n      --wot-navbar-desc-font-size: 14px;\r\n      --wot-navbar-title-font-size: 16px;\r\n      \r\n      .wd-navbar__title {\r\n        color: #ffffff !important;\r\n        font-weight: 700 !important;\r\n      }\r\n    }\r\n  }\r\n  .pageContent {\r\n    flex: 1;\r\n    overflow: hidden;\r\n    display: flex;\r\n    flex-direction: column;\r\n    background-color: #f1f1f1;\r\n  }\r\n  .tabbar {\r\n\r\n\r\n\r\n  }\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/components/PageLayout/PageLayout.vue'\nwx.createComponent(Component)"], "names": ["useParamsStore", "useRouter", "useSlots"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CA,UAAM,cAAcA,iBAAAA,eAAe;AACnC,UAAM,SAASC,gCAAAA,UAAU;AACzB,UAAM,QAAQ;AAsDDC,kBAAS,SAAA;AAChB,UAAA,aAAa,SAAS;AACtB,UAAA,EAAE,YAAY,UAAA,IAAc;AAC5B,UAAA,EAAE,oBAAoB;AAC5B,UAAM,OAAO;AACb,UAAM,kBAAkB,MAAM;AAC5B,WAAK,SAAS;AAEV,UAAA,MAAM,SAAS,QAAQ;AACzB,cAAM,QAAQ,gBAAgB;AAC1B,YAAA,MAAM,iBAAiB,MAAM,eAAe;AAC9C,gBAAM,WAAW,MAAM,MAAM,SAAS,CAAC;AACvC,cAAI,UAAU;AACZ,kBAAM,QAAQ,SAAS;AACvB,kBAAM,OAAO,MAAM,MAAM,GAAG,EAAE,IAAI;AAClC,gBAAI,UAAU,MAAM,iBAAiB,MAAM,kBAAkB,MAAM;AACjE,qBAAO,KAAK;AACS,mCAAA;AACrB;AAAA,YAAA;AAAA,UACF;AAEF,cAAI,MAAM,eAAe;AAChB,mBAAA,MAAM,WAAW,EAAE,EAAE,MAAM,MAAM,eAAe,QAAQ,MAAM,aAAa;AAC7D,iCAAA;AAAA,UAAA,OAChB;AACE,mBAAA,MAAM,WAAW,EAAE,EAAE,MAAM,MAAM,eAAe,OAAO,MAAM,YAAY;AAC3D,iCAAA;AAAA,UAAA;AAAA,QACvB,OACK;AACL,iBAAO,KAAK;AACS,+BAAA;AAAA,QAAA;AAAA,MACvB;AAAA,IAEJ;AACA,UAAM,uBAAuB,MAAM;AAEjC,YAAM,QAAQ,gBAAgB;AAC9B,YAAM,UAAU,MAAM,MAAM,SAAS,CAAC;AACtC,YAAM,WAAW,QAAQ;AACzB,YAAM,OAAO,SAAS,MAAM,GAAG,EAAE,IAAI;AACrC,kBAAY,gBAAgB,IAAI;AAAA,IAClC;AACA,UAAM,mBAAmB,MAAM;AAC7B,WAAK,UAAU;AAAA,IACjB;AACQ,YAAA,IAAI,UAAU,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;AClJ3B,GAAG,gBAAgB,SAAS;"}