{"version": 3, "file": "index.js", "sources": ["../../../../../../../src/pages-work/components/echarts/JPictorialBar/index.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvcGFnZXMtd29yay9jb21wb25lbnRzL2VjaGFydHMvSlBpY3RvcmlhbEJhci9pbmRleC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"content\">\r\n    <statusTip v-if=\"pageTips.show\" :status=\"pageTips.status\"></statusTip>\r\n\t<echartsUniapp v-else :option=\"option\"></echartsUniapp>\r\n  </view>\r\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { echartProps } from '@/pages-work/components/echarts/props';\r\nimport {deepMerge, handleTotalAndUnit, disposeGridLayout, addImgPrefix} from '../../common/echartUtil';\r\nimport { merge, omit } from 'lodash-es';\r\nimport useChartHook from '@/pages-work/components/hooks/useEchart';\r\nimport { deepClone } from '@/uni_modules/da-tree/utils';\r\nimport echartsUniapp from '@/pages-work/components/echarts/index.vue';\r\nimport statusTip from '@/pages-work/components/statusTip.vue';\r\n//组件传参\r\nconst props = defineProps({\r\n\t...echartProps\r\n})\r\n//最终图表配置项\r\nconst option = ref({});\r\n//获取默认配置\r\nlet chartOption: any = {\r\n  title: {\r\n    show: true,\r\n  },\r\n  xAxis: {\r\n    data: [],\r\n    axisTick: { show: false },\r\n    axisLine: { show: false },\r\n    axisLabel: {\r\n      color: '#e54035',\r\n    },\r\n  },\r\n  yAxis: {\r\n    splitLine: { show: false },\r\n    axisTick: { show: false },\r\n    axisLine: { show: false },\r\n    axisLabel: { show: false },\r\n    nameTextStyle: {\r\n      align:\"right\"\r\n    },\r\n  },\r\n  color: ['#e54035'],\r\n  series: [\r\n    {\r\n      name: 'hill',\r\n      type: 'pictorialBar',\r\n      barCategoryGap: '-150%',\r\n      symbol: 'path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z',\r\n      itemStyle: {\r\n        opacity: 0.5,\r\n      },\r\n      emphasis: {\r\n        itemStyle: {\r\n          opacity: 1,\r\n        },\r\n      },\r\n      data: [],\r\n      z: 10,\r\n    },\r\n    {\r\n      name: 'glyph',\r\n      type: 'pictorialBar',\r\n      barGap: '-100%',\r\n      symbolPosition: 'end',\r\n      symbolSize: 50,\r\n      symbolOffset: [0, '-120%'],\r\n      data: [],\r\n    },\r\n  ],\r\n};\r\n\r\n\r\n//图表数据查询\r\nlet [{ dataSource, reload, pageTips, config }, { queryData }] = useChartHook(\r\n  props,\r\n  initOption\r\n)\r\n\r\n\r\n//初始化图表配置项\r\nfunction initOption(data) {\r\n  let chartData: any = dataSource.value\r\n  if (typeof chartData === 'string') {\r\n    chartData = JSON.parse(chartData)\r\n  }\r\n  if (chartData && chartData.length > 0) {\r\n    let seriesValueData = chartData.map((item) => {\r\n      return item.value;\r\n    });\r\n    let seriesObjData = chartData.map((item) => {\r\n      return { ...omit(item, ['name']) };\r\n    });\r\n    let xAxisData = chartData.map((item) => {\r\n      return item.name;\r\n    });\r\n    chartOption.series[0].data = seriesValueData;\r\n    chartOption.series[0].itemStyle.opacity = props.config.option.barOpacity || 0.5;\r\n    chartOption.series[1].data = seriesObjData;\r\n    chartOption.xAxis.data = xAxisData;\r\n    chartOption.color = [props.config.option?.barColor || '#e54035'];\r\n    chartOption.series[0].itemStyle.color = props.config.option?.barColor || '#e54035';\r\n    // 合并配置\r\n    if (props.config && config.option) {\r\n      merge(chartOption, config.option)\r\n      chartOption = handleTotalAndUnit(props.compName, chartOption, config, chartData)\r\n      chartOption = disposeGridLayout(props.compName, chartOption, config, chartData)\r\n\t\t  option.value = deepClone(chartOption)\r\n\t\t  pageTips.show = false\r\n    }\r\n  } else {\r\n    pageTips.status = 1\r\n    pageTips.show = true\r\n  }\r\n}\r\n\r\nonMounted(()=>{\r\n\tqueryData();\r\n})\r\n\r\n</script>\r\n<style>\r\n.content {\r\n  padding: 10px;\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-work/components/echarts/JPictorialBar/index.vue'\nwx.createComponent(Component)"], "names": ["ref", "useChartHook", "omit", "merge", "handleTotalAndUnit", "disposeGridLayout", "deepClone", "onMounted", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAaA,MAAA,gBAA0B,MAAA;AAC1B,MAAA,YAAsB,MAAA;;;;;AAEtB,UAAM,QAAQ;AAIR,UAAA,SAASA,cAAI,IAAA,EAAE;AAErB,QAAI,cAAmB;AAAA,MACrB,OAAO;AAAA,QACL,MAAM;AAAA,MACR;AAAA,MACA,OAAO;AAAA,QACL,MAAM,CAAC;AAAA,QACP,UAAU,EAAE,MAAM,MAAM;AAAA,QACxB,UAAU,EAAE,MAAM,MAAM;AAAA,QACxB,WAAW;AAAA,UACT,OAAO;AAAA,QAAA;AAAA,MAEX;AAAA,MACA,OAAO;AAAA,QACL,WAAW,EAAE,MAAM,MAAM;AAAA,QACzB,UAAU,EAAE,MAAM,MAAM;AAAA,QACxB,UAAU,EAAE,MAAM,MAAM;AAAA,QACxB,WAAW,EAAE,MAAM,MAAM;AAAA,QACzB,eAAe;AAAA,UACb,OAAM;AAAA,QAAA;AAAA,MAEV;AAAA,MACA,OAAO,CAAC,SAAS;AAAA,MACjB,QAAQ;AAAA,QACN;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,gBAAgB;AAAA,UAChB,QAAQ;AAAA,UACR,WAAW;AAAA,YACT,SAAS;AAAA,UACX;AAAA,UACA,UAAU;AAAA,YACR,WAAW;AAAA,cACT,SAAS;AAAA,YAAA;AAAA,UAEb;AAAA,UACA,MAAM,CAAC;AAAA,UACP,GAAG;AAAA,QACL;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,gBAAgB;AAAA,UAChB,YAAY;AAAA,UACZ,cAAc,CAAC,GAAG,OAAO;AAAA,UACzB,MAAM,CAAA;AAAA,QAAC;AAAA,MACT;AAAA,IAEJ;AAII,QAAA,CAAC,EAAE,YAAY,QAAQ,UAAU,UAAU,EAAE,UAAW,CAAA,IAAIC,qCAAA;AAAA,MAC9D;AAAA,MACA;AAAA,IACF;AAIA,aAAS,WAAW,MAAM;;AACxB,UAAI,YAAiB,WAAW;AAC5B,UAAA,OAAO,cAAc,UAAU;AACrB,oBAAA,KAAK,MAAM,SAAS;AAAA,MAAA;AAE9B,UAAA,aAAa,UAAU,SAAS,GAAG;AACrC,YAAI,kBAAkB,UAAU,IAAI,CAAC,SAAS;AAC5C,iBAAO,KAAK;AAAA,QAAA,CACb;AACD,YAAI,gBAAgB,UAAU,IAAI,CAAC,SAAS;AAC1C,iBAAO,mBAAKC,cAAA,KAAK,MAAM,CAAC,MAAM,CAAC;AAAA,QAAE,CAClC;AACD,YAAI,YAAY,UAAU,IAAI,CAAC,SAAS;AACtC,iBAAO,KAAK;AAAA,QAAA,CACb;AACW,oBAAA,OAAO,CAAC,EAAE,OAAO;AACjB,oBAAA,OAAO,CAAC,EAAE,UAAU,UAAU,MAAM,OAAO,OAAO,cAAc;AAChE,oBAAA,OAAO,CAAC,EAAE,OAAO;AAC7B,oBAAY,MAAM,OAAO;AACzB,oBAAY,QAAQ,GAAC,WAAM,OAAO,WAAb,mBAAqB,aAAY,SAAS;AACnD,oBAAA,OAAO,CAAC,EAAE,UAAU,UAAQ,WAAM,OAAO,WAAb,mBAAqB,aAAY;AAErE,YAAA,MAAM,UAAU,OAAO,QAAQ;AAC3BC,8BAAA,aAAa,OAAO,MAAM;AAChC,wBAAcC,uCAAmB,mBAAA,MAAM,UAAU,aAAa,QAAQ,SAAS;AAC/E,wBAAcC,uCAAkB,kBAAA,MAAM,UAAU,WAA8B;AACzE,iBAAA,QAAQC,mCAAU,WAAW;AACpC,mBAAS,OAAO;AAAA,QAAA;AAAA,MAChB,OACK;AACL,iBAAS,SAAS;AAClB,iBAAS,OAAO;AAAA,MAAA;AAAA,IAClB;AAGFC,kBAAAA,UAAU,MAAI;AACH,gBAAA;AAAA,IAAA,CACV;;;;;;;;;;;;;;;;ACtHD,GAAG,gBAAgBC,SAAS;"}