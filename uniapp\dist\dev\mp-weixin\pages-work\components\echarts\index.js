"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../../common/vendor.js");
if (!Array) {
  const _component_l_echart = common_vendor.resolveComponent("l-echart");
  _component_l_echart();
}
const _sfc_main = {
  __name: "index",
  props: {
    option: {
      type: Object,
      default: () => {
        return {};
      }
    },
    mapName: {
      type: String,
      default: "china"
    },
    mapData: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  setup(__props) {
    const uniEchartsWx = require("../../../pages-work/static/echarts.min");
    const props = __props;
    const chartRef = common_vendor.ref(null);
    common_vendor.watchEffect(() => {
      props.option && init(props.option);
    });
    function init(finalOption) {
      if (finalOption) {
        setTimeout(() => __async(this, null, function* () {
          if (!chartRef.value)
            return;
          const myChartWx = yield chartRef.value.init(uniEchartsWx);
          if (props.mapName) {
            uniEchartsWx.registerMap(props.mapName, props.mapData);
          }
          myChartWx.setOption(finalOption);
        }), 300);
      }
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.sr(chartRef, "523aca39-0", {
          "k": "chartRef"
        })
      };
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-523aca39"]]);
wx.createComponent(Component);
//# sourceMappingURL=index.js.map
