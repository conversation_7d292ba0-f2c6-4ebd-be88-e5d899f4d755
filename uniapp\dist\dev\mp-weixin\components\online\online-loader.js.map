{"version": 3, "file": "online-loader.js", "sources": ["../../../../../src/components/online/online-loader.vue", "../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvY29tcG9uZW50cy9vbmxpbmUvb25saW5lLWxvYWRlci52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"onlineLoader-container\">\r\n    <view class=\"form-container\">\r\n      <wd-form ref=\"form\" :model=\"formData\">\r\n        <wd-cell-group border>\r\n          <view\r\n            class=\"onlineLoader-form\"\r\n            v-for=\"(item, index) in rootProperties\"\r\n            :key=\"index\"\r\n            :class=\"{ 'mt-14px': index % 2 == 0 }\"\r\n          >\r\n            <!-- 图片 -->\r\n            <wd-cell\r\n              v-if=\"item.type == 'image'\"\r\n              :name=\"item.key\"\r\n              :title=\"get4Label(item.label)\"\r\n              :title-width=\"labelWidth\"\r\n              :required=\"fieldRequired(item)\"\r\n            >\r\n              <online-image\r\n                v-model:value=\"formData[item.key]\"\r\n                :name=\"item.key\"\r\n                :disabled=\"componentDisabled(item)\"\r\n                :key=\"index\"\r\n              ></online-image>\r\n            </wd-cell>\r\n\r\n            <!-- 文件 -->\r\n            <wd-cell\r\n              v-else-if=\"item.type == 'file'\"\r\n              :name=\"item.key\"\r\n              :title=\"get4Label(item.label)\"\r\n              :title-width=\"labelWidth\"\r\n              :required=\"fieldRequired(item)\"\r\n            >\r\n              <online-image\r\n                v-model:value=\"formData[item.key]\"\r\n                uploadFileType=\"all\"\r\n                :name=\"item.key\"\r\n                :disabled=\"componentDisabled(item)\"\r\n                :key=\"index\"\r\n              ></online-image>\r\n            </wd-cell>\r\n\r\n            <!-- 日期时间 -->\r\n            <online-datetime\r\n              v-else-if=\"item.type === 'datetime'\"\r\n              :label=\"get4Label(item.label)\"\r\n              :labelWidth=\"labelWidth\"\r\n              :name=\"item.key\"\r\n              :disabled=\"componentDisabled(item)\"\r\n              v-model:value=\"formData[item.key]\"\r\n              :required=\"fieldRequired(item)\"\r\n            ></online-datetime>\r\n\r\n            <!-- 日期 -->\r\n            <online-date\r\n              v-else-if=\"item.type === 'date'\"\r\n              :label=\"get4Label(item.label)\"\r\n              :labelWidth=\"labelWidth\"\r\n              :name=\"item.key\"\r\n              :type=\"getDateExtendType(item.formSchema)\"\r\n              :disabled=\"componentDisabled(item)\"\r\n              v-model:value=\"formData[item.key]\"\r\n              :required=\"fieldRequired(item)\"\r\n            ></online-date>\r\n\r\n            <!-- 时间 -->\r\n            <online-time\r\n              v-else-if=\"item.type === 'time'\"\r\n              :label=\"get4Label(item.label)\"\r\n              :labelWidth=\"labelWidth\"\r\n              :name=\"item.key\"\r\n              :disabled=\"componentDisabled(item)\"\r\n              v-model:value=\"formData[item.key]\"\r\n              :required=\"fieldRequired(item)\"\r\n            ></online-time>\r\n\r\n            <!-- 下拉选择 -->\r\n            <online-select\r\n              v-else-if=\"item.type === 'list' || item.type === 'sel_search'\"\r\n              :label=\"get4Label(item.label)\"\r\n              :labelWidth=\"labelWidth\"\r\n              :name=\"item.key\"\r\n              :type=\"item.type\"\r\n              :dict=\"item.listSource\"\r\n              :dictStr=\"item.dictStr\"\r\n              :disabled=\"componentDisabled(item)\"\r\n              v-model=\"formData[item.key]\"\r\n              :required=\"fieldRequired(item)\"\r\n            ></online-select>\r\n\r\n            <!-- checkbox   -->\r\n            <online-checkbox\r\n              v-else-if=\"item.type === 'checkbox'\"\r\n              :name=\"item.key\"\r\n              :type=\"item.type\"\r\n              :label=\"get4Label(item.label)\"\r\n              :labelWidth=\"labelWidth\"\r\n              :dict=\"item.listSource\"\r\n              :disabled=\"componentDisabled(item)\"\r\n              :required=\"fieldRequired(item)\"\r\n              v-model=\"formData[item.key]\"\r\n            ></online-checkbox>\r\n\r\n            <!-- radio  -->\r\n            <online-radio\r\n              v-else-if=\"item.type === 'radio'\"\r\n              :name=\"item.key\"\r\n              :label=\"get4Label(item.label)\"\r\n              :labelWidth=\"labelWidth\"\r\n              :type=\"item.type\"\r\n              :dict=\"item.listSource\"\r\n              :disabled=\"componentDisabled(item)\"\r\n              :required=\"fieldRequired(item)\"\r\n              v-model=\"formData[item.key]\"\r\n            ></online-radio>\r\n\r\n            <!-- 下拉多选  -->\r\n            <online-multi\r\n              v-else-if=\"item.type === 'list_multi'\"\r\n              :label=\"get4Label(item.label)\"\r\n              :labelWidth=\"labelWidth\"\r\n              :name=\"item.key\"\r\n              :dict=\"item.listSource\"\r\n              :disabled=\"componentDisabled(item)\"\r\n              :required=\"fieldRequired(item)\"\r\n              v-model=\"formData[item.key]\"\r\n            ></online-multi>\r\n\r\n            <!-- 省市区   -->\r\n            <online-pca\r\n              v-else-if=\"item.type === 'pca'\"\r\n              :name=\"item.key\"\r\n              :label=\"get4Label(item.label)\"\r\n              :labelWidth=\"labelWidth\"\r\n              :disabled=\"componentDisabled(item)\"\r\n              :required=\"fieldRequired(item)\"\r\n              v-model:value=\"formData[item.key]\"\r\n            ></online-pca>\r\n\r\n            <!-- 数字框 小数 -->\r\n            <wd-input\r\n              v-else-if=\"item.type === 'number' && (!item.onlyInteger || item.onlyInteger == false)\"\r\n              :label-width=\"labelWidth\"\r\n              v-model=\"formData[item.key]\"\r\n              :label=\"get4Label(item.label)\"\r\n              :name=\"item.key\"\r\n              inputMode=\"decimal\"\r\n              :disabled=\"componentDisabled(item)\"\r\n              :placeholder=\"item.placeholder\"\r\n              :rules=\"item.rules\"\r\n            />\r\n\r\n            <!-- 数字框 整数 -->\r\n            <wd-input\r\n              v-else-if=\"item.type === 'number' && item.onlyInteger === true\"\r\n              :label-width=\"labelWidth\"\r\n              :label=\"get4Label(item.label)\"\r\n              :name=\"item.key\"\r\n              v-model=\"formData[item.key]\"\r\n              inputMode=\"numeric\"\r\n              :disabled=\"componentDisabled(item)\"\r\n              :placeholder=\"item.placeholder\"\r\n              :rules=\"item.rules\"\r\n            />\r\n\r\n            <!-- 开关 -->\r\n            <wd-cell\r\n              v-else-if=\"item.type == 'switch'\"\r\n              :name=\"item.key\"\r\n              :title=\"get4Label(item.label)\"\r\n              :title-width=\"labelWidth\"\r\n              center\r\n              :required=\"fieldRequired(item)\"\r\n            >\r\n              <view style=\"text-align: left\">\r\n                <wd-switch\r\n                  :label=\"get4Label(item.label)\"\r\n                  :name=\"item.key\"\r\n                  size=\"18px\"\r\n                  :disabled=\"componentDisabled(item)\"\r\n                  v-model=\"formData[item.key]\"\r\n                  :active-value=\"switchOpt(item.formSchema?.extendOption, 0)\"\r\n                  :inactive-value=\"switchOpt(item.formSchema?.extendOption, 1)\"\r\n                />\r\n              </view>\r\n            </wd-cell>\r\n\r\n            <!-- 多行文本 -->\r\n            <wd-textarea\r\n              v-else-if=\"['textarea', 'markdown', 'umeditor'].includes(item.type)\"\r\n              :label-width=\"labelWidth\"\r\n              :label=\"get4Label(item.label)\"\r\n              :name=\"item.key\"\r\n              v-model=\"formData[item.key]\"\r\n              clearable\r\n              :maxlength=\"300\"\r\n              :disabled=\"componentDisabled(item)\"\r\n              :placeholder=\"item.placeholder\"\r\n              :rules=\"item.rules\"\r\n            />\r\n            <!-- 密码输入框 -->\r\n            <wd-input\r\n              v-else-if=\"item.type === 'password'\"\r\n              :label-width=\"labelWidth\"\r\n              v-model=\"formData[item.key]\"\r\n              :disabled=\"componentDisabled(item)\"\r\n              :label=\"get4Label(item.label)\"\r\n              :name=\"item.key\"\r\n              :placeholder=\"item.placeholder\"\r\n              :rules=\"item.rules\"\r\n              show-password\r\n            />\r\n            <!-- popup字典 -->\r\n            <PopupDict\r\n              v-else-if=\"item.type === 'popup_dict'\"\r\n              :label-width=\"labelWidth\"\r\n              :label=\"get4Label(item.label)\"\r\n              :disabled=\"componentDisabled(item)\"\r\n              :required=\"fieldRequired(item)\"\r\n              v-model=\"formData[item.key]\"\r\n              :multi=\"item.formSchema.popupMulti\"\r\n              :dictCode=\"`${item.formSchema.code},${item.formSchema['destFields']},${item.formSchema['orgFields']}`\"\r\n            ></PopupDict>\r\n            <!-- popup -->\r\n            <Popup\r\n              v-else-if=\"item.type === 'popup'\"\r\n              :label-width=\"labelWidth\"\r\n              :label=\"get4Label(item.label)\"\r\n              :disabled=\"componentDisabled(item)\"\r\n              :required=\"fieldRequired(item)\"\r\n              v-model=\"formData[item.key]\"\r\n              :multi=\"item.formSchema.popupMulti\"\r\n              :code=\"`${item.formSchema.code}`\"\r\n              :setFieldsValue=\"setFieldsValue\"\r\n              :fieldConfig=\"getPopupFieldConfig(item)\"\r\n            ></Popup>\r\n            <!-- 关联记录 -->\r\n            <online-popup-link-record\r\n              v-else-if=\"item.type === 'link_table'\"\r\n              :label-width=\"labelWidth\"\r\n              :label=\"get4Label(item.label)\"\r\n              :name=\"item.key\"\r\n              v-model:formSchema=\"item.formSchema\"\r\n              :disabled=\"componentDisabled(item)\"\r\n              :required=\"fieldRequired(item)\"\r\n              v-model:value=\"formData[item.key]\"\r\n              @selected=\"linkRecordChange\"\r\n            ></online-popup-link-record>\r\n            <!-- 他表字段 -->\r\n            <wd-input\r\n              v-else-if=\"item.type === 'link_table_field'\"\r\n              :label-width=\"labelWidth\"\r\n              v-model=\"formData[item.key]\"\r\n              :disabled=\"true\"\r\n              :label=\"get4Label(item.label)\"\r\n              :name=\"item.key\"\r\n            />\r\n            <!-- 用户选择   -->\r\n            <select-user\r\n              v-else-if=\"item.type === 'sel_user'\"\r\n              :label-width=\"labelWidth\"\r\n              :name=\"item.key\"\r\n              :label=\"get4Label(item.label)\"\r\n              :disabled=\"componentDisabled(item)\"\r\n              :required=\"fieldRequired(item)\"\r\n              v-model=\"formData[item.key]\"\r\n            ></select-user>\r\n\r\n            <!-- 部门选择   -->\r\n            <select-dept\r\n              v-else-if=\"item.type === 'sel_depart'\"\r\n              :label-width=\"labelWidth\"\r\n              :name=\"item.key\"\r\n              :label=\"get4Label(item.label)\"\r\n              labelKey=\"departName\"\r\n              :disabled=\"componentDisabled(item)\"\r\n              :required=\"fieldRequired(item)\"\r\n              v-model=\"formData[item.key]\"\r\n            ></select-dept>\r\n            <!-- 分类字典树 -->\r\n            <CategorySelect\r\n              v-else-if=\"item.type === 'cat_tree'\"\r\n              :label-width=\"labelWidth\"\r\n              :label=\"get4Label(item.label)\"\r\n              :disabled=\"componentDisabled(item)\"\r\n              :required=\"fieldRequired(item)\"\r\n              v-model=\"formData[item.key]\"\r\n              :pid=\"`${item.formSchema.pidValue}`\"\r\n            ></CategorySelect>\r\n            <!-- 自定义树 -->\r\n            <TreeSelect\r\n              v-else-if=\"item.type === 'sel_tree'\"\r\n              :label-width=\"labelWidth\"\r\n              :label=\"get4Label(item.label)\"\r\n              :disabled=\"componentDisabled(item)\"\r\n              :required=\"fieldRequired(item)\"\r\n              v-model=\"formData[item.key]\"\r\n              :dict=\"`${item.formSchema.dict}`\"\r\n              :pidField=\"`${item.formSchema.pidField}`\"\r\n              :pidValue=\"`${item.formSchema.pidValue}`\"\r\n              :hasChildField=\"`${item.formSchema.hasChildField}`\"\r\n            ></TreeSelect>\r\n            <!-- 普通输入框 -->\r\n            <wd-input\r\n              v-else\r\n              :label-width=\"labelWidth\"\r\n              v-model=\"formData[item.key]\"\r\n              :disabled=\"componentDisabled(item)\"\r\n              :label=\"get4Label(item.label)\"\r\n              :name=\"item.key\"\r\n              :placeholder=\"item.placeholder\"\r\n              :rules=\"item.rules\"\r\n              clearable\r\n            />\r\n          </view>\r\n        </wd-cell-group>\r\n      </wd-form>\r\n    </view>\r\n    <view class=\"footer\" v-if=\"showFooter\">\r\n      <wd-button :disabled=\"loading\" block :loading=\"loading\" @click=\"formSubmit\">提交</wd-button>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport FormProperty from '@/components/online/FormProperty'\r\nimport OnlineImage from '@/components/online/view/online-image.vue'\r\nimport OnlineSelect from '@/components/online/view/online-select.vue'\r\nimport OnlineTime from '@/components/online/view/online-time.vue'\r\nimport OnlineDatetime from '@/components/online/view/online-datetime.vue'\r\nimport OnlineDate from '@/components/online/view/online-date.vue'\r\nimport OnlineRadio from '@/components/online/view/online-radio.vue'\r\nimport OnlineCheckbox from '@/components/online/view/online-checkbox.vue'\r\nimport OnlineMulti from '@/components/online/view/online-multi.vue'\r\nimport OnlinePca from '@/components/online/view/online-pca.vue'\r\nimport OnlinePopupLinkRecord from '@/components/online/view/online-popup-link-record.vue'\r\nimport SelectDept from '@/components/SelectDept/SelectDept.vue'\r\nimport SelectUser from '@/components/SelectUser/SelectUser.vue'\r\nimport { loadOneFieldDefVal } from './defaultVal'\r\nimport { useToast, useMessage, useNotify } from 'wot-design-uni'\r\nimport { http } from '@/utils/http'\r\nimport { deepClone } from 'wot-design-uni/components/common/util'\r\nimport { isArray, isNumber } from '@/utils/is'\r\nimport { formatDate } from '@/common/uitls'\r\nimport { duplicateCheck } from '@/service/api'\r\n\r\n// 接收 props\r\nconst props = defineProps({\r\n  showHeader: {\r\n    type: Boolean,\r\n    required: false,\r\n    default: false,\r\n  },\r\n  table: {\r\n    type: String,\r\n    default: '',\r\n    required: false,\r\n  },\r\n  taskId: {\r\n    type: String,\r\n    default: '',\r\n    required: false,\r\n  },\r\n  showFooter: {\r\n    type: Boolean,\r\n    required: false,\r\n    default: true,\r\n  },\r\n  edit: {\r\n    type: Boolean,\r\n    default: false,\r\n    required: false,\r\n  },\r\n  flowEdit: {\r\n    type: Boolean,\r\n    default: false,\r\n    required: false,\r\n  },\r\n  dataId: {\r\n    type: String,\r\n    default: '',\r\n    required: false,\r\n  },\r\n  title: {\r\n    type: String,\r\n    default: '',\r\n    required: false,\r\n  },\r\n  disabled: {\r\n    type: Boolean,\r\n    default: false,\r\n    required: false,\r\n  },\r\n  onlyBackData: {\r\n    type: Boolean,\r\n    default: false,\r\n    required: false,\r\n  },\r\n})\r\n\r\n// 定义 emits\r\nconst emits = defineEmits(['back', 'success', 'formSuccess'])\r\nconst toast = useToast()\r\n// 定义响应式数据\r\nconst onlinekey = ref(1)\r\n//刷新状态\r\nconst reFresh = ref(false)\r\n//请求地址\r\nconst url = ref({\r\n  load: '/online/cgform/api/getFormItemBytbname/',\r\n  optPre: '/online/cgform/api/form/',\r\n})\r\n// 表单ID\r\nconst code = ref('')\r\n//表类型，1：单表，2：一对多\r\nconst single = ref(false)\r\n//是否树表\r\nconst treeForm = ref(false)\r\n//表描述\r\nconst tableTxt = ref('')\r\n//表名称\r\nconst tableName = ref('')\r\n//表名称\r\nconst tableType = ref(1)\r\n//表数据Data\r\nconst formData = ref<any>({})\r\n//是否填值规则字段\r\nconst hasFillRuleFields = ref('')\r\n//是否必填字段\r\nconst hasRequiredFields = ref([])\r\n//字段属性\r\nconst rootProperties = ref<any>([])\r\n//小程序字段属性\r\nconst wxProperties = ref<any>([])\r\n//加载状态\r\nconst loading = ref(false)\r\n//表单数据ID\r\nconst formDataId = ref('')\r\n\r\n// 导航标题\r\nconst navTitle = computed(() => {\r\n  if (!props.title || props.title.length === 0) {\r\n    return tableTxt.value\r\n  } else {\r\n    return props.title\r\n  }\r\n})\r\n// 标题宽度\r\nconst labelWidth = computed(() => {\r\n  return '100px'\r\n})\r\n// 导航标题\r\nconst get4Label = computed(() => {\r\n  return (lable) => {\r\n    return `${lable && lable.length > 4 ? lable.substring(0, 4) : lable}：`\r\n  }\r\n})\r\n\r\nonMounted(() => {\r\n  console.log('开始渲染online表单')\r\n})\r\n\r\n/**\r\n * 获取日期控件的扩展类型\r\n * @param formSchema\r\n * @returns {string}\r\n */\r\nconst getDateExtendType = (formSchema: any) => {\r\n  if (formSchema.fieldExtendJson) {\r\n    let fieldExtendJson = JSON.parse(formSchema.fieldExtendJson)\r\n    let mapField = {\r\n      month: 'year-month',\r\n      year: 'year',\r\n      quarter: 'date',\r\n      week: 'date',\r\n      day: 'date',\r\n    }\r\n    return fieldExtendJson?.picker && mapField[fieldExtendJson?.picker]\r\n      ? mapField[fieldExtendJson?.picker]\r\n      : 'date'\r\n  }\r\n  return 'date'\r\n}\r\n/**\r\n * 判断是否选中\r\n * @param opts\r\n * @param value\r\n * @returns {boolean|boolean}\r\n */\r\nconst isChecked = (opts: any, value: any) => {\r\n  return opts && opts.length > 0 ? value === opts[0] : false\r\n}\r\n/**\r\n * 开关选项\r\n * @param opts\r\n * @param value\r\n * @returns {boolean|boolean}\r\n */\r\nconst switchOpt = (opts: any, index: any) => {\r\n  const options = Array.isArray(opts) && opts.length > 0 ? opts : ['Y', 'N']\r\n  return options[index] + ''\r\n}\r\n/**\r\n *\r\n * @param item\r\n * @returns {*|boolean}\r\n */\r\nconst componentDisabled = (item: any) => {\r\n  if (props.disabled === true || (!props.showFooter && !props.onlyBackData)) {\r\n    return true\r\n  }\r\n  return item.disabled\r\n}\r\n\r\n/**\r\n * 判断字段是否必填\r\n * @param item\r\n * @returns {boolean}\r\n */\r\nconst fieldRequired = (item: any) => {\r\n  return item?.key && hasRequiredFields.value.includes(item.key)\r\n}\r\n/**\r\n * 关联记录同步修改他表字段\r\n * @param linkRecord\r\n * @param key\r\n */\r\nconst linkRecordChange = (linkRecord, key) => {\r\n  let linkFieldArr = rootProperties.value.filter(\r\n    (item) => item.type === 'link_table_field' && item?.formSchema?.dictTable == key,\r\n  )\r\n  linkFieldArr.forEach((field) => {\r\n    let value = linkRecord.map((record) => record[field.formSchema.dictText]).join(',')\r\n    nextTick(() => {\r\n      formData.value[field.key] = value\r\n    })\r\n  })\r\n}\r\n/**\r\n * 返回按钮点击事件\r\n */\r\nconst backRoute = () => {\r\n  emits('back')\r\n}\r\n\r\n/**\r\n * 处理多选字段\r\n * @param value\r\n */\r\nfunction handleMultiOrDateField() {\r\n  let finalData = deepClone(formData.value)\r\n  //日期字段\r\n  let dateFieldArr = rootProperties.value.filter(\r\n    (item) => item.type === 'date' || item.type === 'datetime',\r\n  )\r\n  //省市区字段\r\n  let pcaArr = rootProperties.value.filter((item) => item.type === 'pca')\r\n  finalData = Object.keys(finalData).reduce((acc, key) => {\r\n    let value = finalData[key]\r\n    //省市区获取最后一位\r\n    if (value && pcaArr.length > 0 && pcaArr.map((item) => item.key).includes(key)) {\r\n      console.log('省市区获取最后一位value', value)\r\n      value = isArray(value) ? value[2] : value.split(',')[2]\r\n    }\r\n    //是数组的就转换成字符串\r\n    if (value && isArray(value)) {\r\n      value = value.join(',')\r\n    }\r\n    //时间戳类型的日期转具体格式字符串\r\n    if (dateFieldArr.length > 0) {\r\n      const dateField = dateFieldArr.find((obj) => obj.key === key)\r\n      if (dateField) {\r\n        value =\r\n          value && isNumber(value)\r\n            ? formatDate(\r\n                value,\r\n                dateField.type === 'datetime' ? 'yyyy-MM-dd HH:mm:ss' : 'yyyy-MM-dd',\r\n              )\r\n            : value\r\n      }\r\n    }\r\n    acc[key] = value\r\n    return acc\r\n  }, {})\r\n  return finalData\r\n}\r\n\r\n/**\r\n * 表单提交事件\r\n * @param e\r\n */\r\nconst formSubmit = async (e) => {\r\n  // 判断字段必填和正则\r\n  if (await fieldCheck(formData.value)) {\r\n    return\r\n  }\r\n  // 处理多选字段\r\n  let finalData = await handleMultiOrDateField()\r\n  // 判断是否只返回数据\r\n  if (props.onlyBackData) {\r\n    emits('success', finalData)\r\n    return\r\n  }\r\n  if (props.flowEdit === true) {\r\n    // 原代码未实现该逻辑，可按需补充\r\n  } else if (props.edit === true) {\r\n    //表单编辑\r\n    await handleEdit(finalData)\r\n  } else {\r\n    //表单新增\r\n    await handleSave(finalData)\r\n  }\r\n}\r\n/**\r\n * 校验字段\r\n * @param values\r\n * @returns {boolean}\r\n */\r\nconst fieldCheck = async (values: any) => {\r\n  // 校验字段\r\n  let flag = false\r\n  for (const item of rootProperties.value) {\r\n    // 校验提示\r\n    const tip = (msg) => {\r\n      // 提示校验未通过\r\n      toast.warning(msg)\r\n      flag = true\r\n    }\r\n    // 校验必填\r\n    if (fieldRequired(item) && !values[item.key]) {\r\n      tip(`${item.label}不能为空！`)\r\n      break\r\n    }\r\n    // 校验正则\r\n    let pattern = item?.formSchema?.pattern\r\n    if (pattern) {\r\n      if (pattern == 'only') {\r\n        const res: any = await duplicateCheck({\r\n          tableName: tableName.value,\r\n          fieldName: item.key,\r\n          fieldVal: values[item.key],\r\n          dataId: formDataId.value,\r\n        })\r\n        if (!res.success) {\r\n          tip(`${item.label} ${res.message}`)\r\n          break\r\n        }\r\n      } else {\r\n        const regex = new RegExp(pattern)\r\n        if (values[item.key] && pattern && !regex.test(values[item.key])) {\r\n          let errorInfo = item?.formSchema?.errorInfo || '格式不正确!'\r\n          tip(`${item.label}${errorInfo}`)\r\n          break\r\n        }\r\n      }\r\n    }\r\n  }\r\n  return flag\r\n}\r\n/**\r\n * 新增保存\r\n */\r\nconst handleSave = (finalData: any) => {\r\n  if (finalData?.bpm_status) {\r\n    finalData.bpm_status = '1'\r\n  }\r\n  console.log('===onlineForm表单组件走新增保存  handleSave===', finalData)\r\n  http\r\n    .post(`${url.value.optPre}${code.value}?tabletype=${tableType.value}`, finalData)\r\n    .then((res: any) => {\r\n      if (res.success) {\r\n        emits('success', res.result)\r\n      } else {\r\n        toast.warning(res.message)\r\n      }\r\n    })\r\n}\r\n/**\r\n * 编辑保存\r\n */\r\nconst handleEdit = (finalData: any) => {\r\n  http\r\n    .put(`${url.value.optPre}${code.value}?tabletype=${tableType.value}`, finalData)\r\n    .then((res: any) => {\r\n      if (res.success) {\r\n        emits('success', formData.value.id)\r\n      } else {\r\n        toast.warning(res.message)\r\n      }\r\n    })\r\n}\r\n/**\r\n * 根据表名加载表单数据\r\n * @param dataID\r\n */\r\nconst loadByTableName = (dataID: any) => {\r\n  formDataId.value = props.dataId\r\n\r\n  if (dataID && dataID.length > 0) {\r\n    formDataId.value = dataID\r\n  }\r\n\r\n  let urlStr = url.value.load + props.table\r\n  http.get(urlStr, { taskId: props.taskId }).then((res: any) => {\r\n    if (res.success) {\r\n      console.log('===onlineForm加载表单数据 schema===', res.result.schema)\r\n      let config = res.result\r\n      code.value = config.head.id\r\n      single.value = config.head.tableType === 1\r\n      tableType.value = config.head.tableType\r\n      createRootProperties(config.schema)\r\n      treeForm.value = config.head.isTree === 'Y'\r\n      tableTxt.value = config.head.tableTxt\r\n      if (props.edit === true || props.flowEdit === true) {\r\n        loadFormData()\r\n      } else {\r\n        // 新增页面处理表单默认值\r\n        handleDefaultValue()\r\n      }\r\n    } else {\r\n      // 假设 $tip 是全局方法，可按需修改\r\n      toast.info(res.message)\r\n    }\r\n  })\r\n}\r\n/**\r\n * 创建根属性\r\n * @param formSchema\r\n */\r\nconst createRootProperties = (formSchema: any) => {\r\n  tableName.value = formSchema.table\r\n  formData.value = {}\r\n  hasFillRuleFields.value = formSchema.hasFillRuleFields\r\n  hasRequiredFields.value = formSchema?.required ?? []\r\n  const properties = formSchema.properties\r\n  let rootProps = [],\r\n    subInfo = []\r\n  console.log('===onlineForm表单配置项 properties===', properties)\r\n  Object.keys(properties).map((key) => {\r\n    if (key) {\r\n      const item = properties[key]\r\n      //TODO 配置主子表的\r\n      if (item.view === 'tab') {\r\n        subInfo.push(item)\r\n      } else {\r\n        formData.value[key] = ''\r\n        let fp = FormProperty(key, item, formSchema.required)\r\n        rootProps.push(fp)\r\n      }\r\n    }\r\n  })\r\n  rootProps.sort((one, next) => {\r\n    return one.formSchema.order - next.formSchema.order\r\n  })\r\n  rootProperties.value = [...rootProps]\r\n  console.log('--rootProperties--', rootProps)\r\n\r\n\r\n  // wxProperties.value = rootProperties.value.map((item) => {\r\n  //   let {\r\n  //     popupCode,\r\n  //     formSchema,\r\n  //     dest,\r\n  //     ogn,\r\n  //     key,\r\n  //     label,\r\n  //     placeholder,\r\n  //     type,\r\n  //     rules,\r\n  //     listSource,\r\n  //     dictStr,\r\n  //     disabled,\r\n  //   } = item\r\n  //   let event = {\r\n  //     formSchema,\r\n  //     dest,\r\n  //     key,\r\n  //     type,\r\n  //     rules,\r\n  //     listSource,\r\n  //     label,\r\n  //     dictStr,\r\n  //     disabled,\r\n  //     placeholder,\r\n  //     ogn,\r\n  //     popupCode,\r\n  //   }\r\n  //   return event\r\n  // })\r\n  // console.log('this.wxProperties', wxProperties.value)\r\n\r\n  nextTick(() => {\r\n    reFresh.value = true\r\n    onlinekey.value += 1\r\n  })\r\n\r\n  emits('formSuccess', true)\r\n}\r\n/**\r\n * 获取字段类型\r\n * @param item\r\n * @returns {string}\r\n */\r\nconst getFieldNumberType = (item: any) => {\r\n  return item.onlyInteger === true ? 'digit' : 'number'\r\n}\r\n/**\r\n * 获取表单数据\r\n */\r\nconst loadFormData = () => {\r\n  let urlStr = url.value.optPre + code.value + '/' + formDataId.value\r\n  urlStr = urlStr.replace(/\"/g, '')\r\n  http.get(urlStr).then((res: any) => {\r\n    if (res.success) {\r\n      formData.value = { ...res.result }\r\n      console.log('===onlineForm表单组件走获取表单数据 loadFormData===', formData.value)\r\n\r\n      reFresh.value = false\r\n      nextTick(() => {\r\n        reFresh.value = true\r\n      })\r\n\r\n    }\r\n  })\r\n}\r\n/**\r\n * 获取默认值\r\n */\r\nconst handleDefaultValue = () => {\r\n  console.log('===onlineForm表单组件走默认值 handleDefaultValue===')\r\n  rootProperties.value.forEach((item) => {\r\n    let field = item.key\r\n    let { defVal, type } = item.formSchema\r\n    loadOneFieldDefVal(defVal, type, (value) => {\r\n      formData.value[field] = value\r\n    })\r\n  })\r\n}\r\nconst setFieldsValue = (data) => {\r\n  formData.value = { ...formData.value, ...data }\r\n}\r\nconst getPopupFieldConfig = (item) => {\r\n  const { formSchema } = item\r\n  const { destFields = '', orgFields = '' } = formSchema\r\n  const result = orgFields.split(',').map((oField, index) => {\r\n    return {\r\n      source: oField,\r\n      target: destFields.split(',')[index],\r\n    }\r\n  })\r\n  return result\r\n}\r\ndefineExpose({\r\n  navTitle,\r\n  getDateExtendType,\r\n  isChecked,\r\n  componentDisabled,\r\n  fieldRequired,\r\n  backRoute,\r\n  formSubmit,\r\n  loadByTableName,\r\n  getFieldNumberType,\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.onlineLoader-container {\r\n  .form-container {\r\n    :deep(.wd-cell-group__body){\r\n      background: #F1F1F1;\r\n    }\r\n    .onlineLoader-form{\r\n      :deep(.wd-input__label-inner){\r\n        font-size: 16px;\r\n      }\r\n      :deep(.wd-picker__label){\r\n        font-size: 16px;\r\n      }\r\n      :deep(.wd-select-picker__label){\r\n        font-size: 16px;\r\n      }\r\n      :deep(.wd-cell__title){\r\n        font-size: 16px;\r\n      }\r\n      :deep(.wd-textarea__label-inner){\r\n        font-size: 16px;\r\n      }\r\n      :deep(.wd-input__label.is-required){\r\n         padding-left: 0px;\r\n       }\r\n      :deep(.wd-input__label.is-required::after){\r\n        left: -10px;\r\n      }\r\n      :deep(.wd-textarea__clear){\r\n        color: #bfbfbf;\r\n      }\r\n      :deep(.wd-select-picker__clear){\r\n        color: #bfbfbf;\r\n      }\r\n      :deep(.wd-input__clear){\r\n        color: #bfbfbf;\r\n      }\r\n      :deep(.wd-upload__close){\r\n        color: #bfbfbf;\r\n      }\r\n    }\r\n  }\r\n  .footer {\r\n    padding: 12px;\r\n  }\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/components/online/online-loader.vue'\nwx.createComponent(Component)"], "names": ["useToast", "ref", "computed", "onMounted", "nextTick", "deepClone", "isArray", "isNumber", "formatDate", "duplicate<PERSON><PERSON><PERSON>", "http", "FormProperty", "loadOneFieldDefVal"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwUA,MAAA,cAAwB,MAAA;AACxB,MAAA,eAAyB,MAAA;AACzB,MAAA,aAAuB,MAAA;AACvB,MAAA,iBAA2B,MAAA;AAC3B,MAAA,aAAuB,MAAA;AACvB,MAAA,cAAwB,MAAA;AACxB,MAAA,iBAA2B,MAAA;AAC3B,MAAA,cAAwB,MAAA;AACxB,MAAA,YAAsB,MAAA;AACtB,MAAA,wBAAkC,MAAA;AAClC,MAAA,aAAuB,MAAA;AACvB,MAAA,aAAuB,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvB,UAAM,QAAQ;AAsDd,UAAM,QAAQ;AACd,UAAM,QAAQA,cAAAA,SAAS;AAEjB,UAAA,YAAYC,kBAAI,CAAC;AAEjB,UAAA,UAAUA,kBAAI,KAAK;AAEzB,UAAM,MAAMA,cAAAA,IAAI;AAAA,MACd,MAAM;AAAA,MACN,QAAQ;AAAA,IAAA,CACT;AAEK,UAAA,OAAOA,kBAAI,EAAE;AAEb,UAAA,SAASA,kBAAI,KAAK;AAElB,UAAA,WAAWA,kBAAI,KAAK;AAEpB,UAAA,WAAWA,kBAAI,EAAE;AAEjB,UAAA,YAAYA,kBAAI,EAAE;AAElB,UAAA,YAAYA,kBAAI,CAAC;AAEjB,UAAA,WAAWA,cAAS,IAAA,EAAE;AAEtB,UAAA,oBAAoBA,kBAAI,EAAE;AAE1B,UAAA,oBAAoBA,cAAI,IAAA,EAAE;AAE1B,UAAA,iBAAiBA,cAAS,IAAA,EAAE;AAEbA,kBAAAA,IAAS,CAAE,CAAA;AAE1B,UAAA,UAAUA,kBAAI,KAAK;AAEnB,UAAA,aAAaA,kBAAI,EAAE;AAGnB,UAAA,WAAWC,cAAAA,SAAS,MAAM;AAC9B,UAAI,CAAC,MAAM,SAAS,MAAM,MAAM,WAAW,GAAG;AAC5C,eAAO,SAAS;AAAA,MAAA,OACX;AACL,eAAO,MAAM;AAAA,MAAA;AAAA,IACf,CACD;AAEK,UAAA,aAAaA,cAAAA,SAAS,MAAM;AACzB,aAAA;AAAA,IAAA,CACR;AAEK,UAAA,YAAYA,cAAAA,SAAS,MAAM;AAC/B,aAAO,CAAC,UAAU;AACT,eAAA,GAAG,SAAS,MAAM,SAAS,IAAI,MAAM,UAAU,GAAG,CAAC,IAAI,KAAK;AAAA,MACrE;AAAA,IAAA,CACD;AAEDC,kBAAAA,UAAU,MAAM;AACd,cAAQ,IAAI,cAAc;AAAA,IAAA,CAC3B;AAOK,UAAA,oBAAoB,CAAC,eAAoB;AAC7C,UAAI,WAAW,iBAAiB;AAC9B,YAAI,kBAAkB,KAAK,MAAM,WAAW,eAAe;AAC3D,YAAI,WAAW;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM;AAAA,UACN,KAAK;AAAA,QACP;AACO,gBAAA,mDAAiB,WAAU,SAAS,mDAAiB,MAAM,IAC9D,SAAS,mDAAiB,MAAM,IAChC;AAAA,MAAA;AAEC,aAAA;AAAA,IACT;AAOM,UAAA,YAAY,CAAC,MAAW,UAAe;AAC3C,aAAO,QAAQ,KAAK,SAAS,IAAI,UAAU,KAAK,CAAC,IAAI;AAAA,IACvD;AAOM,UAAA,YAAY,CAAC,MAAW,UAAe;AACrC,YAAA,UAAU,MAAM,QAAQ,IAAI,KAAK,KAAK,SAAS,IAAI,OAAO,CAAC,KAAK,GAAG;AAClE,aAAA,QAAQ,KAAK,IAAI;AAAA,IAC1B;AAMM,UAAA,oBAAoB,CAAC,SAAc;AACnC,UAAA,MAAM,aAAa,QAAS,CAAC,MAAM,cAAc,CAAC,MAAM,cAAe;AAClE,eAAA;AAAA,MAAA;AAET,aAAO,KAAK;AAAA,IACd;AAOM,UAAA,gBAAgB,CAAC,SAAc;AACnC,cAAO,6BAAM,QAAO,kBAAkB,MAAM,SAAS,KAAK,GAAG;AAAA,IAC/D;AAMM,UAAA,mBAAmB,CAAC,YAAY,QAAQ;AACxC,UAAA,eAAe,eAAe,MAAM;AAAA,QACtC,CAAC,SAAS;;AAAA,sBAAK,SAAS,wBAAsB,kCAAM,eAAN,mBAAkB,cAAa;AAAA;AAAA,MAC/E;AACa,mBAAA,QAAQ,CAAC,UAAU;AAC9B,YAAI,QAAQ,WAAW,IAAI,CAAC,WAAW,OAAO,MAAM,WAAW,QAAQ,CAAC,EAAE,KAAK,GAAG;AAClFC,sBAAAA,WAAS,MAAM;AACJ,mBAAA,MAAM,MAAM,GAAG,IAAI;AAAA,QAAA,CAC7B;AAAA,MAAA,CACF;AAAA,IACH;AAIA,UAAM,YAAY,MAAM;AACtB,YAAM,MAAM;AAAA,IACd;AAMA,aAAS,yBAAyB;AAC5B,UAAA,YAAYC,cAAAA,UAAU,SAAS,KAAK;AAEpC,UAAA,eAAe,eAAe,MAAM;AAAA,QACtC,CAAC,SAAS,KAAK,SAAS,UAAU,KAAK,SAAS;AAAA,MAClD;AAEI,UAAA,SAAS,eAAe,MAAM,OAAO,CAAC,SAAS,KAAK,SAAS,KAAK;AACtE,kBAAY,OAAO,KAAK,SAAS,EAAE,OAAO,CAAC,KAAK,QAAQ;AAClD,YAAA,QAAQ,UAAU,GAAG;AAEzB,YAAI,SAAS,OAAO,SAAS,KAAK,OAAO,IAAI,CAAC,SAAS,KAAK,GAAG,EAAE,SAAS,GAAG,GAAG;AACtE,kBAAA,IAAI,kBAAkB,KAAK;AAC3B,kBAAAC,SAAA,QAAQ,KAAK,IAAI,MAAM,CAAC,IAAI,MAAM,MAAM,GAAG,EAAE,CAAC;AAAA,QAAA;AAGpD,YAAA,SAASA,iBAAQ,KAAK,GAAG;AACnB,kBAAA,MAAM,KAAK,GAAG;AAAA,QAAA;AAGpB,YAAA,aAAa,SAAS,GAAG;AAC3B,gBAAM,YAAY,aAAa,KAAK,CAAC,QAAQ,IAAI,QAAQ,GAAG;AAC5D,cAAI,WAAW;AAEX,oBAAA,SAASC,kBAAS,KAAK,IACnBC,aAAA;AAAA,cACE;AAAA,cACA,UAAU,SAAS,aAAa,wBAAwB;AAAA,YAAA,IAE1D;AAAA,UAAA;AAAA,QACR;AAEF,YAAI,GAAG,IAAI;AACJ,eAAA;AAAA,MACT,GAAG,EAAE;AACE,aAAA;AAAA,IAAA;AAOH,UAAA,aAAa,CAAO,MAAM;AAE9B,UAAI,MAAM,WAAW,SAAS,KAAK,GAAG;AACpC;AAAA,MAAA;AAGE,UAAA,YAAY,MAAM,uBAAuB;AAE7C,UAAI,MAAM,cAAc;AACtB,cAAM,WAAW,SAAS;AAC1B;AAAA,MAAA;AAEE,UAAA,MAAM,aAAa;AAAM;AAAA,eAElB,MAAM,SAAS,MAAM;AAE9B,cAAM,WAAW,SAAS;AAAA,MAAA,OACrB;AAEL,cAAM,WAAW,SAAS;AAAA,MAAA;AAAA,IAE9B;AAMM,UAAA,aAAa,CAAO,WAAgB;;AAExC,UAAI,OAAO;AACA,iBAAA,QAAQ,eAAe,OAAO;AAEjC,cAAA,MAAM,CAAC,QAAQ;AAEnB,gBAAM,QAAQ,GAAG;AACV,iBAAA;AAAA,QACT;AAEA,YAAI,cAAc,IAAI,KAAK,CAAC,OAAO,KAAK,GAAG,GAAG;AACxC,cAAA,GAAG,KAAK,KAAK,OAAO;AACxB;AAAA,QAAA;AAGE,YAAA,WAAU,kCAAM,eAAN,mBAAkB;AAChC,YAAI,SAAS;AACX,cAAI,WAAW,QAAQ;AACf,kBAAA,MAAW,MAAMC,2BAAe;AAAA,cACpC,WAAW,UAAU;AAAA,cACrB,WAAW,KAAK;AAAA,cAChB,UAAU,OAAO,KAAK,GAAG;AAAA,cACzB,QAAQ,WAAW;AAAA,YAAA,CACpB;AACG,gBAAA,CAAC,IAAI,SAAS;AAChB,kBAAI,GAAG,KAAK,KAAK,IAAI,IAAI,OAAO,EAAE;AAClC;AAAA,YAAA;AAAA,UACF,OACK;AACC,kBAAA,QAAQ,IAAI,OAAO,OAAO;AAChC,gBAAI,OAAO,KAAK,GAAG,KAAK,WAAW,CAAC,MAAM,KAAK,OAAO,KAAK,GAAG,CAAC,GAAG;AAC5D,kBAAA,cAAY,kCAAM,eAAN,mBAAkB,cAAa;AAC/C,kBAAI,GAAG,KAAK,KAAK,GAAG,SAAS,EAAE;AAC/B;AAAA,YAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEK,aAAA;AAAA,IACT;AAIM,UAAA,aAAa,CAAC,cAAmB;AACrC,UAAI,uCAAW,YAAY;AACzB,kBAAU,aAAa;AAAA,MAAA;AAEjB,cAAA,IAAI,yCAAyC,SAAS;AAC9DC,iBAAA,KACG,KAAK,GAAG,IAAI,MAAM,MAAM,GAAG,KAAK,KAAK,cAAc,UAAU,KAAK,IAAI,SAAS,EAC/E,KAAK,CAAC,QAAa;AAClB,YAAI,IAAI,SAAS;AACT,gBAAA,WAAW,IAAI,MAAM;AAAA,QAAA,OACtB;AACC,gBAAA,QAAQ,IAAI,OAAO;AAAA,QAAA;AAAA,MAC3B,CACD;AAAA,IACL;AAIM,UAAA,aAAa,CAAC,cAAmB;AACrCA,iBAAA,KACG,IAAI,GAAG,IAAI,MAAM,MAAM,GAAG,KAAK,KAAK,cAAc,UAAU,KAAK,IAAI,SAAS,EAC9E,KAAK,CAAC,QAAa;AAClB,YAAI,IAAI,SAAS;AACT,gBAAA,WAAW,SAAS,MAAM,EAAE;AAAA,QAAA,OAC7B;AACC,gBAAA,QAAQ,IAAI,OAAO;AAAA,QAAA;AAAA,MAC3B,CACD;AAAA,IACL;AAKM,UAAA,kBAAkB,CAAC,WAAgB;AACvC,iBAAW,QAAQ,MAAM;AAErB,UAAA,UAAU,OAAO,SAAS,GAAG;AAC/B,mBAAW,QAAQ;AAAA,MAAA;AAGrB,UAAI,SAAS,IAAI,MAAM,OAAO,MAAM;AAC/BA,sBAAA,IAAI,QAAQ,EAAE,QAAQ,MAAM,QAAQ,EAAE,KAAK,CAAC,QAAa;AAC5D,YAAI,IAAI,SAAS;AACf,kBAAQ,IAAI,iCAAiC,IAAI,OAAO,MAAM;AAC9D,cAAI,SAAS,IAAI;AACZ,eAAA,QAAQ,OAAO,KAAK;AAClB,iBAAA,QAAQ,OAAO,KAAK,cAAc;AAC/B,oBAAA,QAAQ,OAAO,KAAK;AAC9B,+BAAqB,OAAO,MAAM;AACzB,mBAAA,QAAQ,OAAO,KAAK,WAAW;AAC/B,mBAAA,QAAQ,OAAO,KAAK;AAC7B,cAAI,MAAM,SAAS,QAAQ,MAAM,aAAa,MAAM;AACrC,yBAAA;AAAA,UAAA,OACR;AAEc,+BAAA;AAAA,UAAA;AAAA,QACrB,OACK;AAEC,gBAAA,KAAK,IAAI,OAAO;AAAA,QAAA;AAAA,MACxB,CACD;AAAA,IACH;AAKM,UAAA,uBAAuB,CAAC,eAAoB;;AAChD,gBAAU,QAAQ,WAAW;AAC7B,eAAS,QAAQ,CAAC;AAClB,wBAAkB,QAAQ,WAAW;AACnB,wBAAA,SAAQ,8CAAY,aAAZ,YAAwB,CAAC;AACnD,YAAM,aAAa,WAAW;AAC9B,UAAI,YAAY,CACd;AACM,cAAA,IAAI,oCAAoC,UAAU;AAC1D,aAAO,KAAK,UAAU,EAAE,IAAI,CAAC,QAAQ;AACnC,YAAI,KAAK;AACD,gBAAA,OAAO,WAAW,GAAG;AAEvB,cAAA,KAAK,SAAS;AAAO;AAAA,eAElB;AACI,qBAAA,MAAM,GAAG,IAAI;AACtB,gBAAI,KAAKC,+BAAAA,aAAa,KAAK,MAAM,WAAW,QAAQ;AACpD,sBAAU,KAAK,EAAE;AAAA,UAAA;AAAA,QACnB;AAAA,MACF,CACD;AACS,gBAAA,KAAK,CAAC,KAAK,SAAS;AAC5B,eAAO,IAAI,WAAW,QAAQ,KAAK,WAAW;AAAA,MAAA,CAC/C;AACc,qBAAA,QAAQ,CAAC,GAAG,SAAS;AAC5B,cAAA,IAAI,sBAAsB,SAAS;AAoC3CP,oBAAAA,WAAS,MAAM;AACb,gBAAQ,QAAQ;AAChB,kBAAU,SAAS;AAAA,MAAA,CACpB;AAED,YAAM,eAAe,IAAI;AAAA,IAC3B;AAMM,UAAA,qBAAqB,CAAC,SAAc;AACjC,aAAA,KAAK,gBAAgB,OAAO,UAAU;AAAA,IAC/C;AAIA,UAAM,eAAe,MAAM;AACzB,UAAI,SAAS,IAAI,MAAM,SAAS,KAAK,QAAQ,MAAM,WAAW;AACrD,eAAA,OAAO,QAAQ,MAAM,EAAE;AAChCM,iBAAAA,KAAK,IAAI,MAAM,EAAE,KAAK,CAAC,QAAa;AAClC,YAAI,IAAI,SAAS;AACf,mBAAS,QAAQ,mBAAK,IAAI;AAClB,kBAAA,IAAI,4CAA4C,SAAS,KAAK;AAEtE,kBAAQ,QAAQ;AAChBN,wBAAAA,WAAS,MAAM;AACb,oBAAQ,QAAQ;AAAA,UAAA,CACjB;AAAA,QAAA;AAAA,MAEH,CACD;AAAA,IACH;AAIA,UAAM,qBAAqB,MAAM;AAC/B,cAAQ,IAAI,6CAA6C;AAC1C,qBAAA,MAAM,QAAQ,CAAC,SAAS;AACrC,YAAI,QAAQ,KAAK;AACjB,YAAI,EAAE,QAAQ,KAAK,IAAI,KAAK;AACTQ,qCAAAA,mBAAA,QAAQ,MAAM,CAAC,UAAU;AACjC,mBAAA,MAAM,KAAK,IAAI;AAAA,QAAA,CACzB;AAAA,MAAA,CACF;AAAA,IACH;AACM,UAAA,iBAAiB,CAAC,SAAS;AAC/B,eAAS,QAAQ,kCAAK,SAAS,QAAU;AAAA,IAC3C;AACM,UAAA,sBAAsB,CAAC,SAAS;AAC9B,YAAA,EAAE,eAAe;AACvB,YAAM,EAAE,aAAa,IAAI,YAAY,GAAO,IAAA;AACtC,YAAA,SAAS,UAAU,MAAM,GAAG,EAAE,IAAI,CAAC,QAAQ,UAAU;AAClD,eAAA;AAAA,UACL,QAAQ;AAAA,UACR,QAAQ,WAAW,MAAM,GAAG,EAAE,KAAK;AAAA,QACrC;AAAA,MAAA,CACD;AACM,aAAA;AAAA,IACT;AACa,aAAA;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/1BD,GAAG,gBAAgB,SAAS;"}