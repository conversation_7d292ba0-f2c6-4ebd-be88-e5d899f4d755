{"version": 3, "file": "location.js", "sources": ["../../../../../src/pages-user/location/location.vue", "../../../../../uniPage:/cGFnZXMtdXNlclxsb2NhdGlvblxsb2NhdGlvbi52dWU"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n  <PageLayout navTitle=\"定位\" backRouteName=\"people\" routeMethod=\"pushTab\">\r\n    <map\r\n      style=\"width: 100%; height: 100%\"\r\n      :latitude=\"latitude\"\r\n      :longitude=\"longitude\"\r\n      :markers=\"marker\"\r\n      :scale=\"scale\"\r\n    ></map>\r\n  </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { ref } from 'vue'\r\nimport { onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app' \r\n\r\nconst latitude = ref(40.009704)\r\nconst longitude = ref(116.374999)\r\nconst marker = reactive([\r\n  {\r\n    id: 0,\r\n    latitude: latitude.value, //纬度\r\n    longitude: longitude.value, //经度\r\n    iconPath: '/static/location.png', //显示的图标\r\n    rotate: 0, // 旋转度数\r\n    width: 20, //宽\r\n    height: 20, //高\r\n    title: '你在哪了', //标注点名\r\n    alpha: 0.5, //透明度\r\n    /* label:{//为标记点旁边增加标签   //因背景颜色H5不支持\r\n\t\t\t\t  　　 content:'北京国炬公司',//文本\r\n\t\t\t\t　　　 color:'red',//文本颜色\r\n\t\t\t\t\t　 fontSize:24,//文字大小\r\n\t\t\t\t\t   x:5,//label的坐标，原点是 marker 对应的经纬度\r\n\t\t\t\t\t   y:1,//label的坐标，原点是 marker 对应的经纬度 \r\n\t\t\t\t\t   borderWidth:12,//边框宽度\r\n\t\t\t\t\t   borderColor:'pink',//边框颜色    \r\n\t\t\t\t\t　 borderRadius:20,//边框圆角                        \r\n\t\t\t\t\t　 bgColor:'black',//背景色\r\n\t\t\t\t\t　 padding:5,//文本边缘留白\r\n\t\t\t\t\t   textAlign:'right'//文本对齐方式。\r\n\t\t\t   }, */\r\n    callout: {\r\n      //自定义标记点上方的气泡窗口 点击有效\r\n      content: '北京国炬公司', //文本\r\n      color: '#ffffff', //文字颜色\r\n      fontSize: 14, //文本大小\r\n      borderRadius: 2, //边框圆角\r\n      bgColor: '#00c16f', //背景颜色\r\n      display: 'ALWAYS', //常显\r\n    },\r\n    // anchor:{//经纬度在标注图标的锚点，默认底边中点\r\n    //     x:0,    原点为给出的经纬度\r\n    //     y:0,\r\n    // }\r\n  },\r\n])\r\nconst scale = 16\r\n\r\nconst getLocation = () => {\r\n  uni.getLocation({\r\n    type: 'gcj02',\r\n    success: function (res) {\r\n      console.log('当前位置的经度：' + res.longitude)\r\n      console.log('当前位置的纬度：' + res.latitude)\r\n    },\r\n    fail: function (res) {\r\n      console.log('当前位置的经度')\r\n    },\r\n  })\r\n}\r\nonLoad(() => {\r\n  getLocation()\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n//\r\n</style>\r\n", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-user/location/location.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "uni", "onLoad"], "mappings": ";;;;;;;;;;;AA6DA,MAAM,QAAQ;;;;AAzCR,UAAA,WAAWA,kBAAI,SAAS;AACxB,UAAA,YAAYA,kBAAI,UAAU;AAChC,UAAM,SAASC,cAAAA,SAAS;AAAA,MACtB;AAAA,QACE,IAAI;AAAA,QACJ,UAAU,SAAS;AAAA;AAAA,QACnB,WAAW,UAAU;AAAA;AAAA,QACrB,UAAU;AAAA;AAAA,QACV,QAAQ;AAAA;AAAA,QACR,OAAO;AAAA;AAAA,QACP,QAAQ;AAAA;AAAA,QACR,OAAO;AAAA;AAAA,QACP,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAcP,SAAS;AAAA;AAAA,UAEP,SAAS;AAAA;AAAA,UACT,OAAO;AAAA;AAAA,UACP,UAAU;AAAA;AAAA,UACV,cAAc;AAAA;AAAA,UACd,SAAS;AAAA;AAAA,UACT,SAAS;AAAA;AAAA,QAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACX;AAAA,IAKF,CACD;AAGD,UAAM,cAAc,MAAM;AACxBC,oBAAAA,MAAI,YAAY;AAAA,QACd,MAAM;AAAA,QACN,SAAS,SAAU,KAAK;AACd,kBAAA,IAAI,aAAa,IAAI,SAAS;AAC9B,kBAAA,IAAI,aAAa,IAAI,QAAQ;AAAA,QACvC;AAAA,QACA,MAAM,SAAU,KAAK;AACnB,kBAAQ,IAAI,SAAS;AAAA,QAAA;AAAA,MACvB,CACD;AAAA,IACH;AACAC,kBAAAA,OAAO,MAAM;AACC,kBAAA;AAAA,IAAA,CACb;;;;;;;;;;;;;;;;;AC5ED,GAAG,WAAW,eAAe;"}