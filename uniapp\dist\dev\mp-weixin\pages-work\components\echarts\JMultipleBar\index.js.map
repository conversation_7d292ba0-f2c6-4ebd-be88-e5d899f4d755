{"version": 3, "file": "index.js", "sources": ["../../../../../../../src/pages-work/components/echarts/JMultipleBar/index.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvcGFnZXMtd29yay9jb21wb25lbnRzL2VjaGFydHMvSk11bHRpcGxlQmFyL2luZGV4LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"content\">\r\n    <statusTip v-if=\"pageTips.show\" :status=\"pageTips.status\"></statusTip>\r\n\t<echartsUniapp v-else :option=\"option\"></echartsUniapp>\r\n  </view>\r\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { echartProps } from '@/pages-work/components/echarts/props';\r\nimport {deepMerge, handleTotalAndUnit, disposeGridLayout, getCustomColor, getDataSet} from '../../common/echartUtil';\r\nimport { isNumber } from '@/utils/is';\r\nimport useChartHook from '@/pages-work/components/hooks/useEchart';\r\nimport { deepClone } from '@/uni_modules/da-tree/utils';\r\nimport echartsUniapp from '@/pages-work/components/echarts/index.vue';\r\nimport statusTip from '@/pages-work/components/statusTip.vue';\r\nimport {merge} from \"lodash-es\";\r\n//组件传参\r\nconst props = defineProps({\r\n\t...echartProps\r\n})\r\n\r\n//最终图表配置项\r\nconst option = ref({});\r\nlet chartOption = {\r\n    title: {\r\n        show: true,\r\n    },\r\n    legend: {\r\n        show: true,\r\n        data: [],\r\n    },\r\n    xAxis: {\r\n        type: 'category',\r\n    },\r\n    yAxis: {\r\n        type: 'value',\r\n    },\r\n    series: [],\r\n    dataset: {\r\n        dimensions: [],\r\n        source: [],\r\n    }\r\n}\r\n//图表数据查询\r\nlet [{ dataSource, reload, pageTips, config }, { queryData }] = useChartHook(\r\n  props,\r\n  initOption\r\n)\r\n\r\n\r\n//初始化图表配置项\r\nfunction initOption(data) {\r\n  let chartData: any = dataSource.value\r\n  if (typeof chartData === 'string') {\r\n    chartData = JSON.parse(chartData)\r\n  }\r\n  if (chartData && chartData.length > 0) {\r\n      const colors = getCustomColor(config.option.customColor);\r\n      let configOption = config.option;\r\n      let dataset = getDataSet(chartData,config);\r\n      let label = configOption.series.length>0 && configOption.series[0].label?configOption.series[0].label:{};\r\n      chartOption.dataset = dataset;\r\n      chartOption.series = [];\r\n      dataset.dimensions.forEach((series, index) => {\r\n          if (index > 0) {\r\n              let legengColor = configOption.series.length>0 && configOption.series[0].color?configOption.series[0].color[index-1]:null\r\n              let color = colors&&colors[index-1]?colors[index-1].color:\"\"\r\n              chartOption.series.push({\r\n                  type: 'bar', //TODO 自定义图表类型\r\n                  color: legengColor || color, //TODO 自定义颜色\r\n                  series: series, //TODO 系列，冗余数据，只是table展示使用\r\n                  label: label\r\n              });\r\n          }\r\n      });\r\n      chartOption.legend.data = chartOption.series.map((item) => item.series);\r\n      //2.类目轴和数值轴赋值\r\n      if(config.option.xAxis && config.option.xAxis.type){\r\n          let type = config.option.xAxis['type'] =='value'?'category':'value';\r\n          chartOption.yAxis['type'] = type;\r\n      }\r\n    // 合并配置\r\n    if (props.config && config.option) {\r\n      merge(chartOption, config.option)\r\n      chartOption = handleTotalAndUnit(props.compName, chartOption, config, chartData)\r\n      chartOption = disposeGridLayout(props.compName, chartOption, config, chartData)\r\n\t\t  option.value = deepClone(chartOption)\r\n\t\t  pageTips.show = false\r\n    }\r\n  } else {\r\n    pageTips.status = 1\r\n    pageTips.show = true\r\n  }\r\n}\r\n\r\nonMounted(()=>{\r\n\tqueryData();\r\n})\r\n\r\n</script>\r\n<style>\r\n.content {\r\n  padding: 10px;\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-work/components/echarts/JMultipleBar/index.vue'\nwx.createComponent(Component)"], "names": ["ref", "useChartHook", "getCustomColor", "getDataSet", "merge", "handleTotalAndUnit", "disposeGridLayout", "deepClone", "onMounted", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAaA,MAAA,gBAA0B,MAAA;AAC1B,MAAA,YAAsB,MAAA;;;;;AAGtB,UAAM,QAAQ;AAKR,UAAA,SAASA,cAAI,IAAA,EAAE;AACrB,QAAI,cAAc;AAAA,MACd,OAAO;AAAA,QACH,MAAM;AAAA,MACV;AAAA,MACA,QAAQ;AAAA,QACJ,MAAM;AAAA,QACN,MAAM,CAAA;AAAA,MACV;AAAA,MACA,OAAO;AAAA,QACH,MAAM;AAAA,MACV;AAAA,MACA,OAAO;AAAA,QACH,MAAM;AAAA,MACV;AAAA,MACA,QAAQ,CAAC;AAAA,MACT,SAAS;AAAA,QACL,YAAY,CAAC;AAAA,QACb,QAAQ,CAAA;AAAA,MAAC;AAAA,IAEjB;AAEI,QAAA,CAAC,EAAE,YAAY,QAAQ,UAAU,UAAU,EAAE,UAAW,CAAA,IAAIC,qCAAA;AAAA,MAC9D;AAAA,MACA;AAAA,IACF;AAIA,aAAS,WAAW,MAAM;AACxB,UAAI,YAAiB,WAAW;AAC5B,UAAA,OAAO,cAAc,UAAU;AACrB,oBAAA,KAAK,MAAM,SAAS;AAAA,MAAA;AAE9B,UAAA,aAAa,UAAU,SAAS,GAAG;AACnC,cAAM,SAASC,uCAAA,eAAe,OAAO,OAAO,WAAW;AACvD,YAAI,eAAe,OAAO;AACtB,YAAA,UAAUC,uCAAAA,WAAW,WAAU,MAAM;AACzC,YAAI,QAAQ,aAAa,OAAO,SAAO,KAAK,aAAa,OAAO,CAAC,EAAE,QAAM,aAAa,OAAO,CAAC,EAAE,QAAM,CAAC;AACvG,oBAAY,UAAU;AACtB,oBAAY,SAAS,CAAC;AACtB,gBAAQ,WAAW,QAAQ,CAAC,QAAQ,UAAU;AAC1C,cAAI,QAAQ,GAAG;AACX,gBAAI,cAAc,aAAa,OAAO,SAAO,KAAK,aAAa,OAAO,CAAC,EAAE,QAAM,aAAa,OAAO,CAAC,EAAE,MAAM,QAAM,CAAC,IAAE;AACjH,gBAAA,QAAQ,UAAQ,OAAO,QAAM,CAAC,IAAE,OAAO,QAAM,CAAC,EAAE,QAAM;AAC1D,wBAAY,OAAO,KAAK;AAAA,cACpB,MAAM;AAAA;AAAA,cACN,OAAO,eAAe;AAAA;AAAA,cACtB;AAAA;AAAA,cACA;AAAA,YAAA,CACH;AAAA,UAAA;AAAA,QACL,CACH;AACW,oBAAA,OAAO,OAAO,YAAY,OAAO,IAAI,CAAC,SAAS,KAAK,MAAM;AAEtE,YAAG,OAAO,OAAO,SAAS,OAAO,OAAO,MAAM,MAAK;AAC/C,cAAI,OAAO,OAAO,OAAO,MAAM,MAAM,KAAI,UAAQ,aAAW;AAChD,sBAAA,MAAM,MAAM,IAAI;AAAA,QAAA;AAG9B,YAAA,MAAM,UAAU,OAAO,QAAQ;AAC3BC,8BAAA,aAAa,OAAO,MAAM;AAChC,wBAAcC,uCAAmB,mBAAA,MAAM,UAAU,aAAa,QAAQ,SAAS;AAC/E,wBAAcC,uCAAkB,kBAAA,MAAM,UAAU,WAA8B;AACzE,iBAAA,QAAQC,mCAAU,WAAW;AACpC,mBAAS,OAAO;AAAA,QAAA;AAAA,MAChB,OACK;AACL,iBAAS,SAAS;AAClB,iBAAS,OAAO;AAAA,MAAA;AAAA,IAClB;AAGFC,kBAAAA,UAAU,MAAI;AACH,gBAAA;AAAA,IAAA,CACV;;;;;;;;;;;;;;;;AChGD,GAAG,gBAAgBC,SAAS;"}