{"version": 3, "file": "virtual-list.js", "sources": ["../../../../../../../../../src/uni_modules/z-paging/components/z-paging/js/modules/virtual-list.js"], "sourcesContent": ["// [z-paging]虚拟列表模块\r\nimport u from '.././z-paging-utils'\r\nimport c from '.././z-paging-constant'\r\nimport Enum from '.././z-paging-enum'\r\n\r\nexport default {\r\n\tprops: {\r\n\t\t// 是否使用虚拟列表，默认为否\r\n\t\tuseVirtualList: {\r\n\t\t\ttype: Bo<PERSON>an,\r\n\t\t\tdefault: u.gc('useVirtualList', false)\r\n\t\t},\r\n\t\t// 在使用虚拟列表时，是否使用兼容模式，默认为否\r\n\t\tuseCompatibilityMode: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('useCompatibilityMode', false)\r\n\t\t},\r\n\t\t// 使用兼容模式时传递的附加数据\r\n\t\textraData: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault: u.gc('extraData', {})\r\n\t\t},\r\n\t\t// 是否在z-paging内部循环渲染列表(内置列表)，默认为否。若use-virtual-list为true，则此项恒为true\r\n\t\tuseInnerList: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('useInnerList', false)\r\n\t\t},\r\n\t\t// 强制关闭inner-list，默认为false，如果为true将强制关闭innerList，适用于开启了虚拟列表后需要强制关闭inner-list的情况\r\n\t\tforceCloseInnerList: {\r\n\t\t\ttype: <PERSON><PERSON>an,\r\n\t\t\tdefault: u.gc('forceCloseInnerList', false)\r\n\t\t},\r\n\t\t// 内置列表cell的key名称，仅nvue有效，在nvue中开启use-inner-list时必须填此项\r\n\t\tcellKeyName: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('cellKeyName', '')\r\n\t\t},\r\n\t\t// innerList样式\r\n\t\tinnerListStyle: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault: u.gc('innerListStyle', {})\r\n\t\t},\r\n\t\t// innerCell样式\r\n\t\tinnerCellStyle: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault: u.gc('innerCellStyle', {})\r\n\t\t},\r\n\t\t// 预加载的列表可视范围(列表高度)页数，默认为12，即预加载当前页及上下各12页的cell。此数值越大，则虚拟列表中加载的dom越多，内存消耗越大(会维持在一个稳定值)，但增加预加载页面数量可缓解快速滚动短暂白屏问题\r\n\t\tpreloadPage: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: u.gc('preloadPage', 12),\r\n\t\t\tvalidator: (value) => {\r\n\t\t\t\tif (value <= 0) u.consoleErr('preload-page必须大于0！');\r\n\t\t\t\treturn value > 0;\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 虚拟列表cell高度模式，默认为fixed，也就是每个cell高度完全相同，将以第一个cell高度为准进行计算。可选值【dynamic】，即代表高度是动态非固定的，【dynamic】性能低于【fixed】。\r\n\t\tcellHeightMode: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('cellHeightMode', Enum.CellHeightMode.Fixed)\r\n\t\t},\r\n\t\t// 固定的cell高度，cellHeightMode=fixed才有效，若设置了值，则不计算第一个cell高度而使用设置的cell高度\r\n\t\tfixedCellHeight: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: u.gc('fixedCellHeight', 0)\r\n\t\t},\r\n\t\t// 虚拟列表列数，默认为1。常用于每行有多列的情况，例如每行有2列数据，需要将此值设置为2\r\n\t\tvirtualListCol: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: u.gc('virtualListCol', 1)\r\n\t\t},\r\n\t\t// 虚拟列表scroll取样帧率，默认为80，过低容易出现白屏问题，过高容易出现卡顿问题\r\n\t\tvirtualScrollFps: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: u.gc('virtualScrollFps', 80)\r\n\t\t},\r\n\t\t// 虚拟列表cell id的前缀，适用于一个页面有多个虚拟列表的情况，用以区分不同虚拟列表cell的id，注意：请勿传数字或以数字开头的字符串。如设置为list1，则cell的id应为：list1-zp-id-${item.zp_index}\r\n\t\tvirtualCellIdPrefix: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('virtualCellIdPrefix', '')\r\n\t\t},\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tvirtualListKey: u.getInstanceId(),\r\n\t\t\tvirtualPageHeight: 0,\r\n\t\t\tvirtualCellHeight: 0,\r\n\t\t\tvirtualScrollTimeStamp: 0,\r\n\t\t\t\r\n\t\t\tvirtualList: [],\r\n\t\t\tvirtualPlaceholderTopHeight: 0,\r\n\t\t\tvirtualPlaceholderBottomHeight: 0,\r\n\t\t\tvirtualTopRangeIndex: 0,\r\n\t\t\tvirtualBottomRangeIndex: 0,\r\n\t\t\tlastVirtualTopRangeIndex: 0,\r\n\t\t\tlastVirtualBottomRangeIndex: 0,\r\n\t\t\tvirtualItemInsertedCount: 0,\r\n\t\t\t\r\n\t\t\tvirtualHeightCacheList: [],\r\n\t\t\t\r\n\t\t\tgetCellHeightRetryCount: {\r\n\t\t\t\tfixed: 0,\r\n\t\t\t\tdynamic: 0\r\n\t\t\t},\r\n\t\t\tpagingOrgTop: -1,\r\n\t\t\tupdateVirtualListFromDataChange: false\r\n\t\t}\r\n\t},\r\n\twatch: {\r\n\t\t// 监听总数据的改变，刷新虚拟列表布局\r\n\t\trealTotalData() {\r\n\t\t\tthis.updateVirtualListRender();\r\n\t\t},\r\n\t\t// 监听虚拟列表渲染数组的改变并emit\r\n\t\tvirtualList(newVal){\r\n\t\t\tthis.$emit('update:virtualList', newVal);\r\n\t\t\tthis.$emit('virtualListChange', newVal);\r\n\t\t},\r\n\t\t// 监听虚拟列表顶部占位高度改变并emit\r\n\t\tvirtualPlaceholderTopHeight(newVal) {\r\n\t\t\tthis.$emit('virtualTopHeightChange', newVal);\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\tvirtualCellIndexKey() {\r\n\t\t\treturn c.listCellIndexKey;\r\n\t\t},\r\n\t\tfinalUseVirtualList() {\r\n\t\t\tif (this.useVirtualList && this.usePageScroll){\r\n\t\t\t\tu.consoleErr('使用页面滚动时，开启虚拟列表无效！');\r\n\t\t\t}\r\n\t\t\treturn this.useVirtualList && !this.usePageScroll;\r\n\t\t},\r\n\t\tfinalUseInnerList() {\r\n\t\t\treturn this.useInnerList || (this.finalUseVirtualList && !this.forceCloseInnerList);\r\n\t\t},\r\n\t\tfinalCellKeyName() {\r\n\t\t\t// #ifdef APP-NVUE\r\n\t\t\tif (this.finalUseVirtualList && !this.cellKeyName.length){\r\n\t\t\t\tu.consoleErr('在nvue中开启use-virtual-list必须设置cell-key-name，否则将可能导致列表渲染错误！');\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t\treturn this.cellKeyName;\r\n\t\t},\r\n\t\tfinalVirtualPageHeight(){\r\n\t\t\treturn this.virtualPageHeight > 0 ? this.virtualPageHeight : this.windowHeight;\r\n\t\t},\r\n\t\tfinalFixedCellHeight() {\r\n\t\t\treturn u.convertToPx(this.fixedCellHeight);\r\n\t\t},\r\n\t\tfianlVirtualCellIdPrefix() {\r\n\t\t\tconst prefix = this.virtualCellIdPrefix ? this.virtualCellIdPrefix + '-' : '';\r\n\t\t\treturn prefix + 'zp-id';\r\n\t\t},\r\n\t\tfinalPlaceholderTopHeightStyle() {\r\n\t\t\t// #ifdef VUE2\r\n\t\t\treturn { transform: this.virtualPlaceholderTopHeight > 0 ? `translateY(${this.virtualPlaceholderTopHeight}px)` : 'none' };\r\n\t\t\t// #endif\r\n\t\t\treturn {};\r\n\t\t},\r\n\t\tvirtualRangePageHeight(){\r\n\t\t\treturn this.finalVirtualPageHeight * this.preloadPage;\r\n\t\t},\r\n\t\tvirtualScrollDisTimeStamp() {\r\n\t\t\treturn 1000 / this.virtualScrollFps;\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\t// 在使用动态高度虚拟列表时，若在列表数组中需要插入某个item，需要调用此方法；item:需要插入的item，index:插入的cell位置，若index为2，则插入的item在原list的index=1之后，index从0开始\r\n\t\tdoInsertVirtualListItem(item, index) {\r\n\t\t\tif (this.cellHeightMode !== Enum.CellHeightMode.Dynamic) return;\r\n\t\t\tthis.realTotalData.splice(index, 0, item);\r\n\t\t\t// #ifdef VUE3\r\n\t\t\tthis.realTotalData = [...this.realTotalData];\r\n\t\t\t// #endif\r\n\t\t\tthis.virtualItemInsertedCount ++;\r\n\t\t\tif (!item || Object.prototype.toString.call(item) !== '[object Object]') {\r\n\t\t\t\titem = { item };\r\n\t\t\t}\r\n\t\t\tconst cellIndexKey = this.virtualCellIndexKey;\r\n\t\t\titem[cellIndexKey] = `custom-${this.virtualItemInsertedCount}`;\r\n\t\t\titem[c.listCellIndexUniqueKey] = `${this.virtualListKey}-${item[cellIndexKey]}`;\r\n\t\t\tthis.$nextTick(async () => {\r\n\t\t\t\tlet retryCount = 0;\r\n\t\t\t\twhile (retryCount <= 10) {\r\n\t\t\t\t\tawait u.wait(c.delayTime);\r\n\t\t\t\t\t\r\n\t\t\t\t\tconst cellNode = await this._getVirtualCellNodeByIndex(item[cellIndexKey]);\r\n\t\t\t\t\t// 如果获取当前cell的节点信息失败，则重试（不超过10次）\r\n\t\t\t\t\tif (!cellNode) {\r\n\t\t\t\t\t\tretryCount ++;\r\n\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t} \r\n\t\t\t\t\t\r\n\t\t\t\t\tconst currentHeight = cellNode ? cellNode[0].height : 0;\r\n\t\t\t\t\tconst lastHeightCache = this.virtualHeightCacheList[index - 1];\r\n\t\t\t\t\tconst lastTotalHeight = lastHeightCache ? lastHeightCache.totalHeight : 0;\r\n\t\t\t\t\t// 在缓存的cell高度数组中，插入此cell高度信息\r\n\t\t\t\t\tthis.virtualHeightCacheList.splice(index, 0, {\r\n\t\t\t\t\t\theight: currentHeight,\r\n\t\t\t\t\t\tlastTotalHeight,\r\n\t\t\t\t\t\ttotalHeight: lastTotalHeight + currentHeight\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 从当前index起后续的cell缓存高度的lastTotalHeight和totalHeight需要加上当前cell的高度\r\n\t\t\t\t\tfor (let i = index + 1; i < this.virtualHeightCacheList.length; i++) {\r\n\t\t\t\t\t\tconst thisNode = this.virtualHeightCacheList[i];\r\n\t\t\t\t\t\tthisNode.lastTotalHeight += currentHeight;\r\n\t\t\t\t\t\tthisNode.totalHeight += currentHeight;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis._updateVirtualScroll(this.oldScrollTop);\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 在使用动态高度虚拟列表时，手动更新指定cell的缓存高度(当cell高度在初始化之后再次改变后调用)；index:需要更新的cell在列表中的位置，从0开始\r\n\t\tdidUpdateVirtualListCell(index) {\r\n\t\t\tif (this.cellHeightMode !== Enum.CellHeightMode.Dynamic) return;\r\n\t\t\tconst currentNode = this.virtualHeightCacheList[index];\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis._getVirtualCellNodeByIndex(index).then(cellNode => {\r\n\t\t\t\t\t// 更新当前cell的高度\r\n\t\t\t\t\tconst cellNodeHeight = cellNode ? cellNode[0].height : 0;\r\n\t\t\t\t\tconst heightDis = cellNodeHeight - currentNode.height;\r\n\t\t\t\t\tcurrentNode.height = cellNodeHeight;\r\n\t\t\t\t\tcurrentNode.totalHeight = currentNode.lastTotalHeight + cellNodeHeight;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 从当前index起后续的cell缓存高度的lastTotalHeight和totalHeight需要加上当前cell变化的高度\r\n\t\t\t\t\tfor (let i = index + 1; i < this.virtualHeightCacheList.length; i++) {\r\n\t\t\t\t\t\tconst thisNode = this.virtualHeightCacheList[i];\r\n\t\t\t\t\t\tthisNode.totalHeight += heightDis;\r\n\t\t\t\t\t\tthisNode.lastTotalHeight += heightDis;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 在使用动态高度虚拟列表时，若删除了列表数组中的某个item，需要调用此方法以更新高度缓存数组；index:删除的cell在列表中的位置，从0开始\r\n\t\tdidDeleteVirtualListCell(index) {\r\n\t\t\tif (this.cellHeightMode !== Enum.CellHeightMode.Dynamic) return;\r\n\t\t\tconst currentNode = this.virtualHeightCacheList[index];\r\n\t\t\t// 从当前index起后续的cell缓存高度的lastTotalHeight和totalHeight需要减去当前cell的高度\r\n\t\t\tfor (let i = index + 1; i < this.virtualHeightCacheList.length; i++) {\r\n\t\t\t\tconst thisNode = this.virtualHeightCacheList[i];\r\n\t\t\t\tthisNode.totalHeight -= currentNode.height;\r\n\t\t\t\tthisNode.lastTotalHeight -= currentNode.height;\r\n\t\t\t}\r\n\t\t\t// 将当前cell的高度信息从高度缓存数组中删除\r\n\t\t\tthis.virtualHeightCacheList.splice(index, 1);\r\n\t\t},\r\n\t\t// 手动触发虚拟列表渲染更新，可用于解决例如修改了虚拟列表数组中元素，但展示未更新的情况\r\n\t\tupdateVirtualListRender() {\r\n\t\t\t// #ifndef APP-NVUE\r\n\t\t\tif (this.finalUseVirtualList) {\r\n\t\t\t\tthis.updateVirtualListFromDataChange = true;\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis.getCellHeightRetryCount.fixed = 0;\r\n\t\t\t\t\tif (this.realTotalData.length) {\r\n\t\t\t\t\t\tthis.cellHeightMode === Enum.CellHeightMode.Fixed && this.isFirstPage && this._updateFixedCellHeight()\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis._resetDynamicListState(!this.isUserPullDown);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis._updateVirtualScroll(this.oldScrollTop);\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\t// 初始化虚拟列表\r\n\t\t_virtualListInit() {\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tu.delay(() => {\r\n\t\t\t\t\t// 获取虚拟列表滚动区域的高度\r\n\t\t\t\t\tthis._getNodeClientRect('.zp-scroll-view').then(node => {\r\n\t\t\t\t\t\tif (node) {\r\n\t\t\t\t\t\t\tthis.pagingOrgTop = node[0].top;\r\n\t\t\t\t\t\t\tthis.virtualPageHeight = node[0].height;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t})\r\n\t\t},\r\n\t\t// cellHeightMode为fixed时获取第一个cell高度\r\n\t\t_updateFixedCellHeight() {\r\n\t\t\tif (!this.finalFixedCellHeight) {\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tu.delay(() => {\r\n\t\t\t\t\t\tthis._getVirtualCellNodeByIndex(0).then(cellNode => {\r\n\t\t\t\t\t\t\tif (!cellNode) {\r\n\t\t\t\t\t\t\t\tif (this.getCellHeightRetryCount.fixed > 10) return;\r\n\t\t\t\t\t\t\t\tthis.getCellHeightRetryCount.fixed ++;\r\n\t\t\t\t\t\t\t\t// 如果获取第一个cell的节点信息失败，则重试（不超过10次）\r\n\t\t\t\t\t\t\t\tthis._updateFixedCellHeight();\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.virtualCellHeight = cellNode[0].height;\r\n\t\t\t\t\t\t\t\tthis._updateVirtualScroll(this.oldScrollTop);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}, c.delayTime, 'updateFixedCellHeightDelay');\r\n\t\t\t\t})\r\n\t\t\t} else {\r\n\t\t\t\tthis.virtualCellHeight = this.finalFixedCellHeight;\r\n\t\t\t}\r\n\t\t},\r\n\t\t// cellHeightMode为dynamic时获取每个cell高度\r\n\t\t_updateDynamicCellHeight(list, dataFrom = 'bottom') {\r\n\t\t\tconst dataFromTop = dataFrom === 'top';\r\n\t\t\tconst heightCacheList = this.virtualHeightCacheList;\r\n\t\t\tconst currentCacheList = dataFromTop ?  [] : heightCacheList;\r\n\t\t\tlet listTotalHeight = 0;\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tu.delay(async () => {\r\n\t\t\t\t\tfor (let i = 0; i < list.length; i++) {\r\n\t\t\t\t\t\tconst cellNode = await this._getVirtualCellNodeByIndex(list[i][this.virtualCellIndexKey]);\r\n\t\t\t\t\t\tconst currentHeight = cellNode ? cellNode[0].height : 0;\r\n\t\t\t\t\t\tif (!cellNode) {\r\n\t\t\t\t\t\t\tif (this.getCellHeightRetryCount.dynamic <= 10) {\r\n\t\t\t\t\t\t\t\theightCacheList.splice(heightCacheList.length - i, i);\r\n\t\t\t\t\t\t\t\tthis.getCellHeightRetryCount.dynamic ++;\r\n\t\t\t\t\t\t\t\t// 如果获取当前cell的节点信息失败，则重试（不超过10次）\r\n\t\t\t\t\t\t\t\tthis._updateDynamicCellHeight(list, dataFrom);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t} \r\n\t\t\t\t\t\tconst lastHeightCache = currentCacheList.length ? currentCacheList.slice(-1)[0] : null;\r\n\t\t\t\t\t\tconst lastTotalHeight = lastHeightCache ? lastHeightCache.totalHeight : 0;\r\n\t\t\t\t\t\t// 缓存当前cell的高度信息：height-当前cell高度；lastTotalHeight-前面所有cell的高度总和；totalHeight-包含当前cell的所有高度总和\r\n\t\t\t\t\t\tcurrentCacheList.push({\r\n\t\t\t\t\t\t\theight: currentHeight,\r\n\t\t\t\t\t\t\tlastTotalHeight,\r\n\t\t\t\t\t\t\ttotalHeight: lastTotalHeight + currentHeight\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tif (dataFromTop) {\r\n\t\t\t\t\t\t\tlistTotalHeight += currentHeight;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 如果数据是从顶部拼接的\r\n\t\t\t\t\tif (dataFromTop && list.length) {\r\n\t\t\t\t\t\tfor (let i = 0; i < heightCacheList.length; i++) {\r\n\t\t\t\t\t\t\t// 更新之前所有项的缓存高度，需要加上此次插入的所有cell高度之和（因为是从顶部插入的cell）\r\n\t\t\t\t\t\t\tconst heightCacheItem = heightCacheList[i];\r\n\t\t\t\t\t\t\theightCacheItem.lastTotalHeight += listTotalHeight;\r\n\t\t\t\t\t\t\theightCacheItem.totalHeight += listTotalHeight;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.virtualHeightCacheList = currentCacheList.concat(heightCacheList);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis._updateVirtualScroll(this.oldScrollTop);\r\n\t\t\t\t}, c.delayTime, 'updateDynamicCellHeightDelay')\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 设置cellItem的index\r\n\t\t_setCellIndex(list, dataFrom = 'bottom') {\r\n\t\t\tlet currentItemIndex = 0;\r\n\t\t\tconst cellIndexKey = this.virtualCellIndexKey;\r\n\t\t\tdataFrom === 'bottom' && ([Enum.QueryFrom.Refresh, Enum.QueryFrom.Reload].indexOf(this.queryFrom) >= 0) && this._resetDynamicListState();\r\n\t\t\tif (this.totalData.length && this.queryFrom !== Enum.QueryFrom.Refresh) {\r\n\t\t\t\tif (dataFrom === 'bottom') {\r\n\t\t\t\t\tcurrentItemIndex = this.realTotalData.length;\r\n\t\t\t\t\tconst lastItem = this.realTotalData.length ? this.realTotalData.slice(-1)[0] : null;\r\n\t\t\t\t\tif (lastItem && lastItem[cellIndexKey] !== undefined) {\r\n\t\t\t\t\t\tcurrentItemIndex = lastItem[cellIndexKey] + 1;\r\n\t\t\t\t\t}\r\n\t\t\t\t} else if (dataFrom === 'top') {\r\n\t\t\t\t\tconst firstItem = this.realTotalData.length ? this.realTotalData[0] : null;\r\n\t\t\t\t\tif (firstItem && firstItem[cellIndexKey] !== undefined) {\r\n\t\t\t\t\t\tcurrentItemIndex = firstItem[cellIndexKey] - list.length;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tthis._resetDynamicListState();\r\n\t\t\t}\r\n\t\t\tfor (let i = 0; i < list.length; i++) {\r\n\t\t\t\tlet item = list[i];\r\n\t\t\t\tif (!item || Object.prototype.toString.call(item) !== '[object Object]') {\r\n\t\t\t\t\titem = { item };\r\n\t\t\t\t}\r\n\t\t\t\tif (item[c.listCellIndexUniqueKey]) {\r\n\t\t\t\t\titem = u.deepCopy(item);\r\n\t\t\t\t}\r\n\t\t\t\titem[cellIndexKey] = currentItemIndex + i;\r\n\t\t\t\titem[c.listCellIndexUniqueKey] = `${this.virtualListKey}-${item[cellIndexKey]}`;\r\n\t\t\t\tlist[i] = item;\r\n\t\t\t}\r\n\t\t\tthis.getCellHeightRetryCount.dynamic = 0;\r\n\t\t\tthis.cellHeightMode === Enum.CellHeightMode.Dynamic && this._updateDynamicCellHeight(list, dataFrom);\r\n\t\t},\r\n\t\t// 更新scroll滚动（虚拟列表滚动时触发）\r\n\t\t_updateVirtualScroll(scrollTop, scrollDiff = 0) {\r\n\t\t\tconst currentTimeStamp = u.getTime();\r\n\t\t\tscrollTop === 0 && this._resetTopRange();\r\n\t\t\tif (scrollTop !== 0 && this.virtualScrollTimeStamp && currentTimeStamp - this.virtualScrollTimeStamp <= this.virtualScrollDisTimeStamp) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tthis.virtualScrollTimeStamp = currentTimeStamp;\r\n\t\t\t\r\n\t\t\tlet scrollIndex = 0;\r\n\t\t\tconst cellHeightMode = this.cellHeightMode;\r\n\t\t\tif (cellHeightMode === Enum.CellHeightMode.Fixed) {\r\n\t\t\t\t// 如果是固定高度的虚拟列表\r\n\t\t\t\t// 计算当前滚动到的cell的index = scrollTop / 虚拟列表cell的固定高度\r\n\t\t\t\tscrollIndex = parseInt(scrollTop / this.virtualCellHeight) || 0;\r\n\t\t\t\t// 更新顶部和底部占位view的高度（为兼容考虑，顶部采用transformY的方式占位)\r\n\t\t\t\tthis._updateFixedTopRangeIndex(scrollIndex);\r\n\t\t\t\tthis._updateFixedBottomRangeIndex(scrollIndex);\r\n\t\t\t} else if(cellHeightMode === Enum.CellHeightMode.Dynamic) {\r\n\t\t\t\t// 如果是不固定高度的虚拟列表\r\n\t\t\t\t// 当前滚动的方向\r\n\t\t\t\tconst scrollDirection = scrollDiff > 0 ? 'top' : 'bottom';\r\n\t\t\t\t// 视图区域的高度\r\n\t\t\t\tconst rangePageHeight = this.virtualRangePageHeight;\r\n\t\t\t\t// 顶部视图区域外的高度（顶部不需要渲染而是需要占位部分的高度）\r\n\t\t\t\tconst topRangePageOffset = scrollTop - rangePageHeight;\r\n\t\t\t\t// 底部视图区域外的高度（底部不需要渲染而是需要占位部分的高度）\r\n\t\t\t\tconst bottomRangePageOffset = scrollTop + this.finalVirtualPageHeight + rangePageHeight;\r\n\t\t\t\t\r\n\t\t\t\tlet virtualBottomRangeIndex = 0;\r\n\t\t\t\tlet virtualPlaceholderBottomHeight = 0;\r\n\t\t\t\tlet reachedLimitBottom = false;\r\n\t\t\t\tconst heightCacheList = this.virtualHeightCacheList;\r\n\t\t\t\tconst lastHeightCache = !!heightCacheList ? heightCacheList.slice(-1)[0] : null;\r\n\t\t\t\t\r\n\t\t\t\tlet startTopRangeIndex = this.virtualTopRangeIndex;\r\n\t\t\t\t// 如果是向底部滚动（顶部占位的高度不断增大，顶部的实际渲染cell数量不断减少）\r\n\t\t\t\tif (scrollDirection === 'bottom') {\r\n\t\t\t\t\t// 从顶部视图边缘的cell的位置开始向后查找\r\n\t\t\t\t\tfor (let i = startTopRangeIndex; i < heightCacheList.length; i++){\r\n\t\t\t\t\t\tconst heightCacheItem = heightCacheList[i];\r\n\t\t\t\t\t\t// 如果查找到某个cell对应的totalHeight大于顶部视图区域外的高度，则此cell为顶部视图边缘的cell\r\n\t\t\t\t\t\tif (heightCacheItem && heightCacheItem.totalHeight > topRangePageOffset) {\r\n\t\t\t\t\t\t\t// 记录顶部视图边缘cell的index并更新顶部占位区域的高度并停止继续查找\r\n\t\t\t\t\t\t\tthis.virtualTopRangeIndex = i;\r\n\t\t\t\t\t\t\tthis.virtualPlaceholderTopHeight = heightCacheItem.lastTotalHeight;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 如果是向顶部滚动（顶部占位的高度不断减少，顶部的实际渲染cell数量不断增加）\r\n\t\t\t\t\tlet topRangeMatched = false;\r\n\t\t\t\t\t// 从顶部视图边缘的cell的位置开始向前查找\r\n\t\t\t\t\tfor (let i = startTopRangeIndex; i >= 0; i--){\r\n\t\t\t\t\t\tconst heightCacheItem = heightCacheList[i];\r\n\t\t\t\t\t\t// 如果查找到某个cell对应的totalHeight小于顶部视图区域外的高度，则此cell为顶部视图边缘的cell\r\n\t\t\t\t\t\tif (heightCacheItem && heightCacheItem.totalHeight < topRangePageOffset) {\r\n\t\t\t\t\t\t\t// 记录顶部视图边缘cell的index并更新顶部占位区域的高度并停止继续查找\r\n\t\t\t\t\t\t\tthis.virtualTopRangeIndex = i;\r\n\t\t\t\t\t\t\tthis.virtualPlaceholderTopHeight = heightCacheItem.lastTotalHeight;\r\n\t\t\t\t\t\t\ttopRangeMatched = true;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 如果查找不到，则认为顶部占位高度为0了，顶部cell不需要继续复用，重置topRangeIndex和placeholderTopHeight\r\n\t\t\t\t\t!topRangeMatched && this._resetTopRange();\r\n\t\t\t\t}\r\n\t\t\t\t// 从顶部视图边缘的cell的位置开始向后查找\r\n\t\t\t\tfor (let i = this.virtualTopRangeIndex; i < heightCacheList.length; i++){\r\n\t\t\t\t\tconst heightCacheItem = heightCacheList[i];\r\n\t\t\t\t\t// 如果查找到某个cell对应的totalHeight大于底部视图区域外的高度，则此cell为底部视图边缘的cell\r\n\t\t\t\t\tif (heightCacheItem && heightCacheItem.totalHeight > bottomRangePageOffset) {\r\n\t\t\t\t\t\t// 记录底部视图边缘cell的index并更新底部占位区域的高度并停止继续查找\r\n\t\t\t\t\t\tvirtualBottomRangeIndex = i;\r\n\t\t\t\t\t\tvirtualPlaceholderBottomHeight = lastHeightCache.totalHeight - heightCacheItem.totalHeight;\r\n\t\t\t\t\t\treachedLimitBottom = true;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (!reachedLimitBottom || this.virtualBottomRangeIndex === 0) {\r\n\t\t\t\t\tthis.virtualBottomRangeIndex = this.realTotalData.length ? this.realTotalData.length - 1 : this.pageSize;\r\n\t\t\t\t\tthis.virtualPlaceholderBottomHeight = 0;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.virtualBottomRangeIndex = virtualBottomRangeIndex;\r\n\t\t\t\t\tthis.virtualPlaceholderBottomHeight = virtualPlaceholderBottomHeight;\r\n\t\t\t\t}\r\n\t\t\t\tthis._updateVirtualList();\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 更新fixedCell模式下topRangeIndex&placeholderTopHeight\r\n\t\t_updateFixedTopRangeIndex(scrollIndex) {\r\n\t\t\tlet virtualTopRangeIndex = this.virtualCellHeight === 0 ? 0 : scrollIndex - (parseInt(this.finalVirtualPageHeight / this.virtualCellHeight) || 1) * this.preloadPage;\r\n\t\t\tvirtualTopRangeIndex *= this.virtualListCol;\r\n\t\t\tvirtualTopRangeIndex = Math.max(0, virtualTopRangeIndex);\r\n\t\t\tthis.virtualTopRangeIndex = virtualTopRangeIndex;\r\n\t\t\tthis.virtualPlaceholderTopHeight = (virtualTopRangeIndex / this.virtualListCol) * this.virtualCellHeight;\r\n\t\t},\r\n\t\t// 更新fixedCell模式下bottomRangeIndex&placeholderBottomHeight\r\n\t\t_updateFixedBottomRangeIndex(scrollIndex) {\r\n\t\t\tlet virtualBottomRangeIndex = this.virtualCellHeight === 0 ? this.pageSize : scrollIndex + (parseInt(this.finalVirtualPageHeight / this.virtualCellHeight) || 1) * (this.preloadPage + 1);\r\n\t\t\tvirtualBottomRangeIndex *= this.virtualListCol;\r\n\t\t\tvirtualBottomRangeIndex = Math.min(this.realTotalData.length, virtualBottomRangeIndex);\r\n\t\t\tthis.virtualBottomRangeIndex = virtualBottomRangeIndex;\r\n\t\t\tthis.virtualPlaceholderBottomHeight = (this.realTotalData.length - virtualBottomRangeIndex) * this.virtualCellHeight / this.virtualListCol;\r\n\t\t\tthis._updateVirtualList();\r\n\t\t},\r\n\t\t// 更新virtualList\r\n\t\t_updateVirtualList() {\r\n\t\t\tconst shouldUpdateList = this.updateVirtualListFromDataChange || (this.lastVirtualTopRangeIndex !== this.virtualTopRangeIndex || this.lastVirtualBottomRangeIndex !== this.virtualBottomRangeIndex);\r\n\t\t\tif (shouldUpdateList) {\r\n\t\t\t\tthis.updateVirtualListFromDataChange = false;\r\n\t\t\t\tthis.lastVirtualTopRangeIndex =  this.virtualTopRangeIndex;\r\n\t\t\t\tthis.lastVirtualBottomRangeIndex = this.virtualBottomRangeIndex;\r\n\t\t\t\tthis.virtualList = this.realTotalData.slice(this.virtualTopRangeIndex, this.virtualBottomRangeIndex + 1);\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 重置动态cell模式下的高度缓存数据、虚拟列表和滚动状态\r\n\t\t_resetDynamicListState(resetVirtualList = false) {\r\n\t\t\tthis.virtualHeightCacheList = [];\r\n\t\t\tif (resetVirtualList) {\r\n\t\t\t\tthis.virtualList = [];\r\n\t\t\t}\r\n\t\t\tthis.virtualTopRangeIndex = 0;\r\n\t\t\tthis.virtualPlaceholderTopHeight = 0;\r\n\t\t},\r\n\t\t// 重置topRangeIndex和placeholderTopHeight\r\n\t\t_resetTopRange() {\r\n\t\t\tthis.virtualTopRangeIndex = 0;\r\n\t\t\tthis.virtualPlaceholderTopHeight = 0;\r\n\t\t\tthis._updateVirtualList();\r\n\t\t},\r\n\t\t// 检测虚拟列表当前滚动位置，如发现滚动位置不正确则重新计算虚拟列表相关参数(为解决在App中可能出现的长时间进入后台后打开App白屏的问题)\r\n\t\t_checkVirtualListScroll() {\r\n\t\t\tif (this.finalUseVirtualList) {\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis._getNodeClientRect('.zp-paging-touch-view').then(node => {\r\n\t\t\t\t\t\tconst currentTop = node ? node[0].top : 0;\r\n\t\t\t\t\t\tif (!node || (currentTop === this.pagingOrgTop && this.virtualPlaceholderTopHeight !== 0)) {\r\n\t\t\t\t\t\t\tthis._updateVirtualScroll(0);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 获取对应index的虚拟列表cell节点信息\r\n\t\t_getVirtualCellNodeByIndex(index) {\r\n\t\t\tlet inParent = false;\r\n\t\t\t// 在vue3+(微信小程序或QQ小程序)中，使用非内置列表写法时，若z-paging在swiper-item内存在无法获取slot插入的cell高度的问题\r\n\t\t\t// 通过uni.createSelectorQuery().in(this.$parent)来解决此问题\r\n\t\t\t// #ifdef VUE3\r\n\t\t\t// #ifdef MP-WEIXIN || MP-QQ\r\n\t\t\tif (this.forceCloseInnerList) {\r\n\t\t\t\tinParent = true;\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t\t// #endif\r\n\t\t\treturn this._getNodeClientRect(`#${this.fianlVirtualCellIdPrefix}-${index}`, this.finalUseInnerList, false, inParent);\r\n\t\t},\r\n\t\t// 处理使用内置列表时点击了cell事件\r\n\t\t_innerCellClick(item, index) {\r\n\t\t\tthis.$emit('innerCellClick', item, index);\r\n\t\t}\r\n\t}\r\n}\r\n"], "names": ["u", "Enum", "c"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAKA,MAAe,oBAAA;AAAA,EACd,OAAO;AAAA;AAAA,IAEN,gBAAgB;AAAA,MACf,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,kBAAkB,KAAK;AAAA,IACrC;AAAA;AAAA,IAED,sBAAsB;AAAA,MACrB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,wBAAwB,KAAK;AAAA,IAC3C;AAAA;AAAA,IAED,WAAW;AAAA,MACV,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,aAAa,CAAA,CAAE;AAAA,IAC7B;AAAA;AAAA,IAED,cAAc;AAAA,MACb,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,gBAAgB,KAAK;AAAA,IACnC;AAAA;AAAA,IAED,qBAAqB;AAAA,MACpB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,uBAAuB,KAAK;AAAA,IAC1C;AAAA;AAAA,IAED,aAAa;AAAA,MACZ,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,eAAe,EAAE;AAAA,IAC/B;AAAA;AAAA,IAED,gBAAgB;AAAA,MACf,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,kBAAkB,CAAA,CAAE;AAAA,IAClC;AAAA;AAAA,IAED,gBAAgB;AAAA,MACf,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,kBAAkB,CAAA,CAAE;AAAA,IAClC;AAAA;AAAA,IAED,aAAa;AAAA,MACZ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,eAAe,EAAE;AAAA,MAC/B,WAAW,CAAC,UAAU;AACrB,YAAI,SAAS;AAAGA,iEAAC,EAAC,WAAW,oBAAoB;AACjD,eAAO,QAAQ;AAAA,MACf;AAAA,IACD;AAAA;AAAA,IAED,gBAAgB;AAAA,MACf,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,kBAAkBC,sDAAI,KAAC,eAAe,KAAK;AAAA,IACzD;AAAA;AAAA,IAED,iBAAiB;AAAA,MAChB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASD,uDAAC,EAAC,GAAG,mBAAmB,CAAC;AAAA,IAClC;AAAA;AAAA,IAED,gBAAgB;AAAA,MACf,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,kBAAkB,CAAC;AAAA,IACjC;AAAA;AAAA,IAED,kBAAkB;AAAA,MACjB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,oBAAoB,EAAE;AAAA,IACpC;AAAA;AAAA,IAED,qBAAqB;AAAA,MACpB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,uBAAuB,EAAE;AAAA,IACvC;AAAA,EACD;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,gBAAgBA,uDAAC,EAAC,cAAe;AAAA,MACjC,mBAAmB;AAAA,MACnB,mBAAmB;AAAA,MACnB,wBAAwB;AAAA,MAExB,aAAa,CAAE;AAAA,MACf,6BAA6B;AAAA,MAC7B,gCAAgC;AAAA,MAChC,sBAAsB;AAAA,MACtB,yBAAyB;AAAA,MACzB,0BAA0B;AAAA,MAC1B,6BAA6B;AAAA,MAC7B,0BAA0B;AAAA,MAE1B,wBAAwB,CAAE;AAAA,MAE1B,yBAAyB;AAAA,QACxB,OAAO;AAAA,QACP,SAAS;AAAA,MACT;AAAA,MACD,cAAc;AAAA,MACd,iCAAiC;AAAA,IACjC;AAAA,EACD;AAAA,EACD,OAAO;AAAA;AAAA,IAEN,gBAAgB;AACf,WAAK,wBAAuB;AAAA,IAC5B;AAAA;AAAA,IAED,YAAY,QAAO;AAClB,WAAK,MAAM,sBAAsB,MAAM;AACvC,WAAK,MAAM,qBAAqB,MAAM;AAAA,IACtC;AAAA;AAAA,IAED,4BAA4B,QAAQ;AACnC,WAAK,MAAM,0BAA0B,MAAM;AAAA,IAC3C;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACT,sBAAsB;AACrB,aAAOE,0DAAAA,EAAE;AAAA,IACT;AAAA,IACD,sBAAsB;AACrB,UAAI,KAAK,kBAAkB,KAAK,eAAc;AAC7CF,iEAAE,WAAW,mBAAmB;AAAA,MAChC;AACD,aAAO,KAAK,kBAAkB,CAAC,KAAK;AAAA,IACpC;AAAA,IACD,oBAAoB;AACnB,aAAO,KAAK,gBAAiB,KAAK,uBAAuB,CAAC,KAAK;AAAA,IAC/D;AAAA,IACD,mBAAmB;AAMlB,aAAO,KAAK;AAAA,IACZ;AAAA,IACD,yBAAwB;AACvB,aAAO,KAAK,oBAAoB,IAAI,KAAK,oBAAoB,KAAK;AAAA,IAClE;AAAA,IACD,uBAAuB;AACtB,aAAOA,yDAAE,YAAY,KAAK,eAAe;AAAA,IACzC;AAAA,IACD,2BAA2B;AAC1B,YAAM,SAAS,KAAK,sBAAsB,KAAK,sBAAsB,MAAM;AAC3E,aAAO,SAAS;AAAA,IAChB;AAAA,IACD,iCAAiC;AAIhC,aAAO;IACP;AAAA,IACD,yBAAwB;AACvB,aAAO,KAAK,yBAAyB,KAAK;AAAA,IAC1C;AAAA,IACD,4BAA4B;AAC3B,aAAO,MAAO,KAAK;AAAA,IACnB;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAER,wBAAwB,MAAM,OAAO;AACpC,UAAI,KAAK,mBAAmBC,sDAAI,KAAC,eAAe;AAAS;AACzD,WAAK,cAAc,OAAO,OAAO,GAAG,IAAI;AAExC,WAAK,gBAAgB,CAAC,GAAG,KAAK,aAAa;AAE3C,WAAK;AACL,UAAI,CAAC,QAAQ,OAAO,UAAU,SAAS,KAAK,IAAI,MAAM,mBAAmB;AACxE,eAAO,EAAE;MACT;AACD,YAAM,eAAe,KAAK;AAC1B,WAAK,YAAY,IAAI,UAAU,KAAK,wBAAwB;AAC5D,WAAKC,0DAAC,EAAC,sBAAsB,IAAI,GAAG,KAAK,cAAc,IAAI,KAAK,YAAY,CAAC;AAC7E,WAAK,UAAU,MAAY;AAC1B,YAAI,aAAa;AACjB,eAAO,cAAc,IAAI;AACxB,gBAAMF,yDAAE,KAAKE,0DAAC,EAAC,SAAS;AAExB,gBAAM,WAAW,MAAM,KAAK,2BAA2B,KAAK,YAAY,CAAC;AAEzE,cAAI,CAAC,UAAU;AACd;AACA;AAAA,UACA;AAED,gBAAM,gBAAgB,WAAW,SAAS,CAAC,EAAE,SAAS;AACtD,gBAAM,kBAAkB,KAAK,uBAAuB,QAAQ,CAAC;AAC7D,gBAAM,kBAAkB,kBAAkB,gBAAgB,cAAc;AAExE,eAAK,uBAAuB,OAAO,OAAO,GAAG;AAAA,YAC5C,QAAQ;AAAA,YACR;AAAA,YACA,aAAa,kBAAkB;AAAA,UACrC,CAAM;AAGD,mBAAS,IAAI,QAAQ,GAAG,IAAI,KAAK,uBAAuB,QAAQ,KAAK;AACpE,kBAAM,WAAW,KAAK,uBAAuB,CAAC;AAC9C,qBAAS,mBAAmB;AAC5B,qBAAS,eAAe;AAAA,UACxB;AAED,eAAK,qBAAqB,KAAK,YAAY;AAC3C;AAAA,QACA;AAAA,MACL,EAAI;AAAA,IACD;AAAA;AAAA,IAED,yBAAyB,OAAO;AAC/B,UAAI,KAAK,mBAAmBD,sDAAI,KAAC,eAAe;AAAS;AACzD,YAAM,cAAc,KAAK,uBAAuB,KAAK;AACrD,WAAK,UAAU,MAAM;AACpB,aAAK,2BAA2B,KAAK,EAAE,KAAK,cAAY;AAEvD,gBAAM,iBAAiB,WAAW,SAAS,CAAC,EAAE,SAAS;AACvD,gBAAM,YAAY,iBAAiB,YAAY;AAC/C,sBAAY,SAAS;AACrB,sBAAY,cAAc,YAAY,kBAAkB;AAGxD,mBAAS,IAAI,QAAQ,GAAG,IAAI,KAAK,uBAAuB,QAAQ,KAAK;AACpE,kBAAM,WAAW,KAAK,uBAAuB,CAAC;AAC9C,qBAAS,eAAe;AACxB,qBAAS,mBAAmB;AAAA,UAC5B;AAAA,QACN,CAAK;AAAA,MACL,CAAI;AAAA,IACD;AAAA;AAAA,IAED,yBAAyB,OAAO;AAC/B,UAAI,KAAK,mBAAmBA,sDAAI,KAAC,eAAe;AAAS;AACzD,YAAM,cAAc,KAAK,uBAAuB,KAAK;AAErD,eAAS,IAAI,QAAQ,GAAG,IAAI,KAAK,uBAAuB,QAAQ,KAAK;AACpE,cAAM,WAAW,KAAK,uBAAuB,CAAC;AAC9C,iBAAS,eAAe,YAAY;AACpC,iBAAS,mBAAmB,YAAY;AAAA,MACxC;AAED,WAAK,uBAAuB,OAAO,OAAO,CAAC;AAAA,IAC3C;AAAA;AAAA,IAED,0BAA0B;AAEzB,UAAI,KAAK,qBAAqB;AAC7B,aAAK,kCAAkC;AACvC,aAAK,UAAU,MAAM;AACpB,eAAK,wBAAwB,QAAQ;AACrC,cAAI,KAAK,cAAc,QAAQ;AAC9B,iBAAK,mBAAmBA,sDAAAA,KAAK,eAAe,SAAS,KAAK,eAAe,KAAK,uBAAwB;AAAA,UAC5G,OAAY;AACN,iBAAK,uBAAuB,CAAC,KAAK,cAAc;AAAA,UAChD;AACD,eAAK,qBAAqB,KAAK,YAAY;AAAA,QAChD,CAAK;AAAA,MACD;AAAA,IAED;AAAA;AAAA,IAED,mBAAmB;AAClB,WAAK,UAAU,MAAM;AACpBD,+DAAC,EAAC,MAAM,MAAM;AAEb,eAAK,mBAAmB,iBAAiB,EAAE,KAAK,UAAQ;AACvD,gBAAI,MAAM;AACT,mBAAK,eAAe,KAAK,CAAC,EAAE;AAC5B,mBAAK,oBAAoB,KAAK,CAAC,EAAE;AAAA,YACjC;AAAA,UACP,CAAM;AAAA,QACN,CAAK;AAAA,MACL,CAAI;AAAA,IACD;AAAA;AAAA,IAED,yBAAyB;AACxB,UAAI,CAAC,KAAK,sBAAsB;AAC/B,aAAK,UAAU,MAAM;AACpBA,iEAAC,EAAC,MAAM,MAAM;AACb,iBAAK,2BAA2B,CAAC,EAAE,KAAK,cAAY;AACnD,kBAAI,CAAC,UAAU;AACd,oBAAI,KAAK,wBAAwB,QAAQ;AAAI;AAC7C,qBAAK,wBAAwB;AAE7B,qBAAK,uBAAsB;AAAA,cACnC,OAAc;AACN,qBAAK,oBAAoB,SAAS,CAAC,EAAE;AACrC,qBAAK,qBAAqB,KAAK,YAAY;AAAA,cAC3C;AAAA,YACR,CAAO;AAAA,UACP,GAAQE,0DAAC,EAAC,WAAW,4BAA4B;AAAA,QACjD,CAAK;AAAA,MACL,OAAU;AACN,aAAK,oBAAoB,KAAK;AAAA,MAC9B;AAAA,IACD;AAAA;AAAA,IAED,yBAAyB,MAAM,WAAW,UAAU;AACnD,YAAM,cAAc,aAAa;AACjC,YAAM,kBAAkB,KAAK;AAC7B,YAAM,mBAAmB,cAAe,CAAE,IAAG;AAC7C,UAAI,kBAAkB;AACtB,WAAK,UAAU,MAAM;AACpBF,+DAAC,EAAC,MAAM,MAAY;AACnB,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACrC,kBAAM,WAAW,MAAM,KAAK,2BAA2B,KAAK,CAAC,EAAE,KAAK,mBAAmB,CAAC;AACxF,kBAAM,gBAAgB,WAAW,SAAS,CAAC,EAAE,SAAS;AACtD,gBAAI,CAAC,UAAU;AACd,kBAAI,KAAK,wBAAwB,WAAW,IAAI;AAC/C,gCAAgB,OAAO,gBAAgB,SAAS,GAAG,CAAC;AACpD,qBAAK,wBAAwB;AAE7B,qBAAK,yBAAyB,MAAM,QAAQ;AAAA,cAC5C;AACD;AAAA,YACA;AACD,kBAAM,kBAAkB,iBAAiB,SAAS,iBAAiB,MAAM,EAAE,EAAE,CAAC,IAAI;AAClF,kBAAM,kBAAkB,kBAAkB,gBAAgB,cAAc;AAExE,6BAAiB,KAAK;AAAA,cACrB,QAAQ;AAAA,cACR;AAAA,cACA,aAAa,kBAAkB;AAAA,YACtC,CAAO;AACD,gBAAI,aAAa;AAChB,iCAAmB;AAAA,YACnB;AAAA,UACD;AAED,cAAI,eAAe,KAAK,QAAQ;AAC/B,qBAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAEhD,oBAAM,kBAAkB,gBAAgB,CAAC;AACzC,8BAAgB,mBAAmB;AACnC,8BAAgB,eAAe;AAAA,YAC/B;AACD,iBAAK,yBAAyB,iBAAiB,OAAO,eAAe;AAAA,UACrE;AACD,eAAK,qBAAqB,KAAK,YAAY;AAAA,QAChD,IAAOE,0DAAC,EAAC,WAAW,8BAA8B;AAAA,MAClD,CAAI;AAAA,IACD;AAAA;AAAA,IAED,cAAc,MAAM,WAAW,UAAU;AACxC,UAAI,mBAAmB;AACvB,YAAM,eAAe,KAAK;AAC1B,mBAAa,YAAa,CAACD,sDAAI,KAAC,UAAU,SAASA,sDAAI,KAAC,UAAU,MAAM,EAAE,QAAQ,KAAK,SAAS,KAAK,KAAM,KAAK;AAChH,UAAI,KAAK,UAAU,UAAU,KAAK,cAAcA,sDAAI,KAAC,UAAU,SAAS;AACvE,YAAI,aAAa,UAAU;AAC1B,6BAAmB,KAAK,cAAc;AACtC,gBAAM,WAAW,KAAK,cAAc,SAAS,KAAK,cAAc,MAAM,EAAE,EAAE,CAAC,IAAI;AAC/E,cAAI,YAAY,SAAS,YAAY,MAAM,QAAW;AACrD,+BAAmB,SAAS,YAAY,IAAI;AAAA,UAC5C;AAAA,QACN,WAAe,aAAa,OAAO;AAC9B,gBAAM,YAAY,KAAK,cAAc,SAAS,KAAK,cAAc,CAAC,IAAI;AACtE,cAAI,aAAa,UAAU,YAAY,MAAM,QAAW;AACvD,+BAAmB,UAAU,YAAY,IAAI,KAAK;AAAA,UAClD;AAAA,QACD;AAAA,MACL,OAAU;AACN,aAAK,uBAAsB;AAAA,MAC3B;AACD,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACrC,YAAI,OAAO,KAAK,CAAC;AACjB,YAAI,CAAC,QAAQ,OAAO,UAAU,SAAS,KAAK,IAAI,MAAM,mBAAmB;AACxE,iBAAO,EAAE;QACT;AACD,YAAI,KAAKC,4DAAE,sBAAsB,GAAG;AACnC,iBAAOF,uDAAC,EAAC,SAAS,IAAI;AAAA,QACtB;AACD,aAAK,YAAY,IAAI,mBAAmB;AACxC,aAAKE,0DAAC,EAAC,sBAAsB,IAAI,GAAG,KAAK,cAAc,IAAI,KAAK,YAAY,CAAC;AAC7E,aAAK,CAAC,IAAI;AAAA,MACV;AACD,WAAK,wBAAwB,UAAU;AACvC,WAAK,mBAAmBD,sDAAI,KAAC,eAAe,WAAW,KAAK,yBAAyB,MAAM,QAAQ;AAAA,IACnG;AAAA;AAAA,IAED,qBAAqB,WAAW,aAAa,GAAG;AAC/C,YAAM,mBAAmBD,yDAAE;AAC3B,oBAAc,KAAK,KAAK;AACxB,UAAI,cAAc,KAAK,KAAK,0BAA0B,mBAAmB,KAAK,0BAA0B,KAAK,2BAA2B;AACvI;AAAA,MACA;AACD,WAAK,yBAAyB;AAE9B,UAAI,cAAc;AAClB,YAAM,iBAAiB,KAAK;AAC5B,UAAI,mBAAmBC,sDAAAA,KAAK,eAAe,OAAO;AAGjD,sBAAc,SAAS,YAAY,KAAK,iBAAiB,KAAK;AAE9D,aAAK,0BAA0B,WAAW;AAC1C,aAAK,6BAA6B,WAAW;AAAA,MAC7C,WAAS,mBAAmBA,2DAAK,eAAe,SAAS;AAGzD,cAAM,kBAAkB,aAAa,IAAI,QAAQ;AAEjD,cAAM,kBAAkB,KAAK;AAE7B,cAAM,qBAAqB,YAAY;AAEvC,cAAM,wBAAwB,YAAY,KAAK,yBAAyB;AAExE,YAAI,0BAA0B;AAC9B,YAAI,iCAAiC;AACrC,YAAI,qBAAqB;AACzB,cAAM,kBAAkB,KAAK;AAC7B,cAAM,kBAAkB,CAAC,CAAC,kBAAkB,gBAAgB,MAAM,EAAE,EAAE,CAAC,IAAI;AAE3E,YAAI,qBAAqB,KAAK;AAE9B,YAAI,oBAAoB,UAAU;AAEjC,mBAAS,IAAI,oBAAoB,IAAI,gBAAgB,QAAQ,KAAI;AAChE,kBAAM,kBAAkB,gBAAgB,CAAC;AAEzC,gBAAI,mBAAmB,gBAAgB,cAAc,oBAAoB;AAExE,mBAAK,uBAAuB;AAC5B,mBAAK,8BAA8B,gBAAgB;AACnD;AAAA,YACA;AAAA,UACD;AAAA,QACN,OAAW;AAEN,cAAI,kBAAkB;AAEtB,mBAAS,IAAI,oBAAoB,KAAK,GAAG,KAAI;AAC5C,kBAAM,kBAAkB,gBAAgB,CAAC;AAEzC,gBAAI,mBAAmB,gBAAgB,cAAc,oBAAoB;AAExE,mBAAK,uBAAuB;AAC5B,mBAAK,8BAA8B,gBAAgB;AACnD,gCAAkB;AAClB;AAAA,YACA;AAAA,UACD;AAED,WAAC,mBAAmB,KAAK;QACzB;AAED,iBAAS,IAAI,KAAK,sBAAsB,IAAI,gBAAgB,QAAQ,KAAI;AACvE,gBAAM,kBAAkB,gBAAgB,CAAC;AAEzC,cAAI,mBAAmB,gBAAgB,cAAc,uBAAuB;AAE3E,sCAA0B;AAC1B,6CAAiC,gBAAgB,cAAc,gBAAgB;AAC/E,iCAAqB;AACrB;AAAA,UACA;AAAA,QACD;AACD,YAAI,CAAC,sBAAsB,KAAK,4BAA4B,GAAG;AAC9D,eAAK,0BAA0B,KAAK,cAAc,SAAS,KAAK,cAAc,SAAS,IAAI,KAAK;AAChG,eAAK,iCAAiC;AAAA,QAC3C,OAAW;AACN,eAAK,0BAA0B;AAC/B,eAAK,iCAAiC;AAAA,QACtC;AACD,aAAK,mBAAkB;AAAA,MACvB;AAAA,IACD;AAAA;AAAA,IAED,0BAA0B,aAAa;AACtC,UAAI,uBAAuB,KAAK,sBAAsB,IAAI,IAAI,eAAe,SAAS,KAAK,yBAAyB,KAAK,iBAAiB,KAAK,KAAK,KAAK;AACzJ,8BAAwB,KAAK;AAC7B,6BAAuB,KAAK,IAAI,GAAG,oBAAoB;AACvD,WAAK,uBAAuB;AAC5B,WAAK,8BAA+B,uBAAuB,KAAK,iBAAkB,KAAK;AAAA,IACvF;AAAA;AAAA,IAED,6BAA6B,aAAa;AACzC,UAAI,0BAA0B,KAAK,sBAAsB,IAAI,KAAK,WAAW,eAAe,SAAS,KAAK,yBAAyB,KAAK,iBAAiB,KAAK,MAAM,KAAK,cAAc;AACvL,iCAA2B,KAAK;AAChC,gCAA0B,KAAK,IAAI,KAAK,cAAc,QAAQ,uBAAuB;AACrF,WAAK,0BAA0B;AAC/B,WAAK,kCAAkC,KAAK,cAAc,SAAS,2BAA2B,KAAK,oBAAoB,KAAK;AAC5H,WAAK,mBAAkB;AAAA,IACvB;AAAA;AAAA,IAED,qBAAqB;AACpB,YAAM,mBAAmB,KAAK,oCAAoC,KAAK,6BAA6B,KAAK,wBAAwB,KAAK,gCAAgC,KAAK;AAC3K,UAAI,kBAAkB;AACrB,aAAK,kCAAkC;AACvC,aAAK,2BAA4B,KAAK;AACtC,aAAK,8BAA8B,KAAK;AACxC,aAAK,cAAc,KAAK,cAAc,MAAM,KAAK,sBAAsB,KAAK,0BAA0B,CAAC;AAAA,MACvG;AAAA,IACD;AAAA;AAAA,IAED,uBAAuB,mBAAmB,OAAO;AAChD,WAAK,yBAAyB;AAC9B,UAAI,kBAAkB;AACrB,aAAK,cAAc;MACnB;AACD,WAAK,uBAAuB;AAC5B,WAAK,8BAA8B;AAAA,IACnC;AAAA;AAAA,IAED,iBAAiB;AAChB,WAAK,uBAAuB;AAC5B,WAAK,8BAA8B;AACnC,WAAK,mBAAkB;AAAA,IACvB;AAAA;AAAA,IAED,0BAA0B;AACzB,UAAI,KAAK,qBAAqB;AAC7B,aAAK,UAAU,MAAM;AACpB,eAAK,mBAAmB,uBAAuB,EAAE,KAAK,UAAQ;AAC7D,kBAAM,aAAa,OAAO,KAAK,CAAC,EAAE,MAAM;AACxC,gBAAI,CAAC,QAAS,eAAe,KAAK,gBAAgB,KAAK,gCAAgC,GAAI;AAC1F,mBAAK,qBAAqB,CAAC;AAAA,YAC3B;AAAA,UACP,CAAM;AAAA,QACN,CAAK;AAAA,MACD;AAAA,IACD;AAAA;AAAA,IAED,2BAA2B,OAAO;AACjC,UAAI,WAAW;AAKf,UAAI,KAAK,qBAAqB;AAC7B,mBAAW;AAAA,MACX;AAGD,aAAO,KAAK,mBAAmB,IAAI,KAAK,wBAAwB,IAAI,KAAK,IAAI,KAAK,mBAAmB,OAAO,QAAQ;AAAA,IACpH;AAAA;AAAA,IAED,gBAAgB,MAAM,OAAO;AAC5B,WAAK,MAAM,kBAAkB,MAAM,KAAK;AAAA,IACxC;AAAA,EACD;AACF;;"}