{"version": 3, "file": "onlineCard.js", "sources": ["../../../../../src/pages-sub/online/onlineCard.vue", "../../../../../uniPage:/cGFnZXMtc3ViXG9ubGluZVxvbmxpbmVDYXJkLnZ1ZQ"], "sourcesContent": ["\r\n<template>\n<layout-default-uni >\r\n  <PageLayout backRouteName=\"online\" navTitle=\"online在线表单\">\r\n    <view class=\"wrap\">\r\n      <z-paging\r\n        ref=\"paging\"\r\n        :fixed=\"false\"\r\n        v-model=\"dataList\"\r\n        @query=\"queryList\"\r\n        :default-page-size=\"15\"\r\n      >\r\n        <template v-for=\"(item, index) in dataList\" :key=\"item.id\">\r\n          <wd-swipe-action>\r\n            <view class=\"list\" @click=\"handleEdit(item)\">\r\n              <template v-for=\"(cItem, cIndex) in columns\" :key=\"index\">\r\n                <view v-if=\"cIndex < 3\" class=\"box\" :style=\"getBoxStyle\">\r\n                  <view class=\"field ellipsis\">{{ cItem['title'] }}</view>\r\n                  <view class=\"value text-grey\">\r\n                    <onlineTableCell\r\n                      :columnsInfo=\"columnsInfo\"\r\n                      :record=\"item\"\r\n                      :column=\"cItem\"\r\n                      :key=\"item.id\"\r\n                    ></onlineTableCell>\r\n                  </view>\r\n                </view>\r\n              </template>\r\n            </view>\r\n            <template #right>\r\n              <view class=\"action\">\r\n                <view class=\"button\" @click=\"handleAction('del', item)\">删除</view>\r\n                <!-- <view class=\"button\" @click=\"handleAction('view', item)\">查看</view> -->\r\n              </view>\r\n            </template>\r\n          </wd-swipe-action>\r\n        </template>\r\n      </z-paging>\r\n      <view class=\"add u-iconfont u-icon-add\" @click=\"handleAdd\"></view>\r\n      <view class=\"goTable u-iconfont u-icon-table\" @click=\"handleGoTable\"></view>\r\n    </view>\r\n  </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, onMounted, computed } from 'vue'\r\nimport { http } from '@/utils/http'\r\nimport { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'\r\nimport { useRouter } from '@/plugin/uni-mini-router'\r\nimport { useUserStore } from '@/store/user'\r\nimport { useParamsStore } from '@/store/page-params'\r\nimport onlineTableCell from './components/onlineTableCell.vue'\r\ndefineOptions({\r\n  name: 'onlineCard',\r\n  options: {\r\n    styleIsolation: 'shared',\r\n  },\r\n})\r\nconst toast = useToast()\r\nconst router = useRouter()\r\nconst userStore = useUserStore()\r\nconst paramsStore = useParamsStore()\r\nconst globalData = getApp().globalData\r\nconst { systemInfo } = globalData\r\nconst { safeArea } = systemInfo\r\nconst paging = ref(null)\r\nconst columns = ref([])\r\nconst columnsInfo = ref({})\r\nconst pageNo = ref(1)\r\nconst pageSize = ref(10)\r\nconst pageTotal = ref(1)\r\nlet pageParams = paramsStore.getPageParams('onlineCard')?.data ?? {}\r\nconst dataList = ref([])\r\nconst getBoxStyle = computed(() => {\r\n  let len = columns.value.length\r\n  if (len > 3) len = 3\r\n  return { width: `calc(${100 / len}% - 5px)` }\r\n})\r\nconst queryParams = () => {\r\n  return {\r\n    pageNo: pageNo.value,\r\n    pageSize: pageSize.value,\r\n    order: 'asc',\r\n    column: 'id',\r\n    hasQuery: true,\r\n  }\r\n}\r\nconst getColumns = () => {\r\n  return new Promise<void>((resove, reject) => {\r\n    if (columns.value.length) {\r\n      resove()\r\n      return\r\n    }\r\n    const analysis = (data) => {\r\n      const len = data.length\r\n      const maxShowColumn = 3\r\n      let space = 1\r\n      if (len == 1) {\r\n        space = 2\r\n      }\r\n      const width = safeArea.width / (len > maxShowColumn ? maxShowColumn : len) - space\r\n      columns.value = data.map((item) => {\r\n        return {\r\n          ...item,\r\n          prop: item.dataIndex,\r\n          align: item.align,\r\n          label: item.title,\r\n          width,\r\n        }\r\n      })\r\n    }\r\n    http\r\n      .get(`/online/cgform/api/getColumns/${pageParams.id}`)\r\n      .then((res: any) => {\r\n        if (res.success) {\r\n          if (res.result?.columns?.length) {\r\n            columnsInfo.value = res.result\r\n            analysis(res.result.columns)\r\n            resove()\r\n          }\r\n        } else {\r\n          toast.warning(res.message)\r\n          reject()\r\n        }\r\n      })\r\n      .catch((res) => {\r\n        toast.error('加载列头失败~')\r\n        reject()\r\n      })\r\n  })\r\n}\r\nconst getData = () => {\r\n  http\r\n    .get(`/online/cgform/api/getData/${pageParams.id}`, { ...queryParams() })\r\n    .then((res: any) => {\r\n      if (res.success) {\r\n        paging.value.complete(res.result?.records ?? [])\r\n      } else {\r\n        toast.warning(res.message)\r\n      }\r\n    })\r\n    .catch((res) => {\r\n      toast.error('加载表格数据失败~')\r\n    })\r\n}\r\nconst handleAction = (val, item) => {\r\n  if (val == 'del') {\r\n    http.delete(`/online/cgform/api/form/${pageParams.id}/${item.id}`).then((res) => {\r\n      toast.success('删除成功~')\r\n      paging.value.reload()\r\n    })\r\n  }\r\n}\r\nconst queryList = (_pageNo, _pageSize) => {\r\n  pageNo.value = _pageNo\r\n  pageSize.value = _pageSize\r\n  getColumns().then(() => {\r\n    getData()\r\n  })\r\n}\r\n// go 新增页\r\nconst handleAdd = () => {\r\n  router.push({\r\n    name: 'onlineAdd',\r\n    params: {\r\n      desformCode: pageParams.tableName,\r\n      desformName: pageParams.tableTxt,\r\n      backRouteName: 'onlineCard',\r\n    },\r\n  })\r\n}\r\n// go table页\r\nconst handleGoTable = (params) => {\r\n  paramsStore.setPageParams('onlineTable', { data: pageParams })\r\n  router.push({ name: 'onlineTable' })\r\n}\r\n//go 编辑页\r\nconst handleEdit = (record) => {\r\n  router.push({\r\n    name: 'onlineEdit',\r\n    params: {\r\n      desformCode: pageParams.tableName,\r\n      desformName: pageParams.tableTxt,\r\n      id: record.id,\r\n      backRouteName: 'onlineCard',\r\n    },\r\n  })\r\n}\r\nonMounted(() => {\r\n  // 监听刷新列表事件\r\n  uni.$on('refreshList', () => {\r\n    getData()\r\n  })\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.wrap {\r\n  height: 100%;\r\n}\r\n:deep(.wd-swipe-action) {\r\n  margin-top: 10px;\r\n  background-color: #fff;\r\n}\r\n.list {\r\n  padding: 10px 10px;\r\n  width: 100%;\r\n  text-align: left;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  .box {\r\n    width: 33%;\r\n    .field {\r\n      margin-bottom: 10px;\r\n      line-height: 20px;\r\n    }\r\n  }\r\n}\r\n.action {\r\n  width: 60px;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  .button {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    flex: 1;\r\n    height: 100%;\r\n    color: #fff;\r\n    &:first-child {\r\n      background-color: #fa4350;\r\n    }\r\n    &:last-child {\r\n      // background-color: #f0883a;\r\n    }\r\n  }\r\n}\r\n.add,\r\n.goTable {\r\n  height: 70upx;\r\n  width: 70upx;\r\n  text-align: center;\r\n  line-height: 70upx;\r\n  background-color: #fff;\r\n  border-radius: 50%;\r\n  position: fixed;\r\n  bottom: 80upx;\r\n  right: 30upx;\r\n  box-shadow: 0 0 5px 2px rgba(0, 0, 0, 0.1);\r\n  color: #666;\r\n}\r\n.goTable {\r\n  bottom: 180upx;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-sub/online/onlineCard.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useToast", "useRouter", "useUserStore", "useParamsStore", "ref", "computed", "http", "_b", "_a", "onMounted", "uni"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoDA,MAAA,kBAA4B,MAAA;;;;;;;;;;AAO5B,UAAM,QAAQA,cAAAA,SAAS;AACvB,UAAM,SAASC,gCAAAA,UAAU;AACPC,eAAa,aAAA;AAC/B,UAAM,cAAcC,iBAAAA,eAAe;AAC7B,UAAA,aAAa,SAAS;AACtB,UAAA,EAAE,eAAe;AACjB,UAAA,EAAE,aAAa;AACf,UAAA,SAASC,kBAAI,IAAI;AACjB,UAAA,UAAUA,cAAI,IAAA,EAAE;AAChB,UAAA,cAAcA,cAAI,IAAA,EAAE;AACpB,UAAA,SAASA,kBAAI,CAAC;AACd,UAAA,WAAWA,kBAAI,EAAE;AACLA,kBAAAA,IAAI,CAAC;AACvB,QAAI,cAAa,uBAAY,cAAc,YAAY,MAAtC,mBAAyC,SAAzC,YAAiD,CAAC;AAC7D,UAAA,WAAWA,cAAI,IAAA,EAAE;AACjB,UAAA,cAAcC,cAAAA,SAAS,MAAM;AAC7B,UAAA,MAAM,QAAQ,MAAM;AACxB,UAAI,MAAM;AAAS,cAAA;AACnB,aAAO,EAAE,OAAO,QAAQ,MAAM,GAAG,WAAW;AAAA,IAAA,CAC7C;AACD,UAAM,cAAc,MAAM;AACjB,aAAA;AAAA,QACL,QAAQ,OAAO;AAAA,QACf,UAAU,SAAS;AAAA,QACnB,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,UAAU;AAAA,MACZ;AAAA,IACF;AACA,UAAM,aAAa,MAAM;AACvB,aAAO,IAAI,QAAc,CAAC,QAAQ,WAAW;AACvC,YAAA,QAAQ,MAAM,QAAQ;AACjB,iBAAA;AACP;AAAA,QAAA;AAEI,cAAA,WAAW,CAAC,SAAS;AACzB,gBAAM,MAAM,KAAK;AACjB,gBAAM,gBAAgB;AACtB,cAAI,QAAQ;AACZ,cAAI,OAAO,GAAG;AACJ,oBAAA;AAAA,UAAA;AAEV,gBAAM,QAAQ,SAAS,SAAS,MAAM,gBAAgB,gBAAgB,OAAO;AAC7E,kBAAQ,QAAQ,KAAK,IAAI,CAAC,SAAS;AAC1B,mBAAA,iCACF,OADE;AAAA,cAEL,MAAM,KAAK;AAAA,cACX,OAAO,KAAK;AAAA,cACZ,OAAO,KAAK;AAAA,cACZ;AAAA,YACF;AAAA,UAAA,CACD;AAAA,QACH;AAEGC,wBAAA,IAAI,iCAAiC,WAAW,EAAE,EAAE,EACpD,KAAK,CAAC,QAAa;;AAClB,cAAI,IAAI,SAAS;AACX,iBAAAC,OAAAC,MAAA,IAAI,WAAJ,gBAAAA,IAAY,YAAZ,gBAAAD,IAAqB,QAAQ;AAC/B,0BAAY,QAAQ,IAAI;AACf,uBAAA,IAAI,OAAO,OAAO;AACpB,qBAAA;AAAA,YAAA;AAAA,UACT,OACK;AACC,kBAAA,QAAQ,IAAI,OAAO;AAClB,mBAAA;AAAA,UAAA;AAAA,QACT,CACD,EACA,MAAM,CAAC,QAAQ;AACd,gBAAM,MAAM,SAAS;AACd,iBAAA;AAAA,QAAA,CACR;AAAA,MAAA,CACJ;AAAA,IACH;AACA,UAAM,UAAU,MAAM;AACpBD,iBAAAA,KACG,IAAI,8BAA8B,WAAW,EAAE,IAAI,mBAAK,YAAA,EAAe,EACvE,KAAK,CAAC,QAAa;;AAClB,YAAI,IAAI,SAAS;AACf,iBAAO,MAAM,UAASC,OAAAC,MAAA,IAAI,WAAJ,gBAAAA,IAAY,YAAZ,OAAAD,MAAuB,EAAE;AAAA,QAAA,OAC1C;AACC,gBAAA,QAAQ,IAAI,OAAO;AAAA,QAAA;AAAA,MAC3B,CACD,EACA,MAAM,CAAC,QAAQ;AACd,cAAM,MAAM,WAAW;AAAA,MAAA,CACxB;AAAA,IACL;AACM,UAAA,eAAe,CAAC,KAAK,SAAS;AAChB;AACXD,mBAAAA,KAAA,OAAO,2BAA2B,WAAW,EAAE,IAAI,KAAK,EAAE,EAAE,EAAE,KAAK,CAAC,QAAQ;AAC/E,gBAAM,QAAQ,OAAO;AACrB,iBAAO,MAAM,OAAO;AAAA,QAAA,CACrB;AAAA,MAAA;AAAA,IAEL;AACM,UAAA,YAAY,CAAC,SAAS,cAAc;AACxC,aAAO,QAAQ;AACf,eAAS,QAAQ;AACN,iBAAA,EAAE,KAAK,MAAM;AACd,gBAAA;AAAA,MAAA,CACT;AAAA,IACH;AAEA,UAAM,YAAY,MAAM;AACtB,aAAO,KAAK;AAAA,QACV,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,aAAa,WAAW;AAAA,UACxB,aAAa,WAAW;AAAA,UACxB,eAAe;AAAA,QAAA;AAAA,MACjB,CACD;AAAA,IACH;AAEM,UAAA,gBAAgB,CAAC,WAAW;AAChC,kBAAY,cAAc,eAAe,EAAE,MAAM,YAAY;AAC7D,aAAO,KAAK,EAAE,MAAM,cAAA,CAAe;AAAA,IACrC;AAEM,UAAA,aAAa,CAAC,WAAW;AAC7B,aAAO,KAAK;AAAA,QACV,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,aAAa,WAAW;AAAA,UACxB,aAAa,WAAW;AAAA,UACxB,IAAI,OAAO;AAAA,UACX,eAAe;AAAA,QAAA;AAAA,MACjB,CACD;AAAA,IACH;AACAG,kBAAAA,UAAU,MAAM;AAEVC,0BAAA,IAAI,eAAe,MAAM;AACnB,gBAAA;AAAA,MAAA,CACT;AAAA,IAAA,CACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjMD,GAAG,WAAW,eAAe;"}