{"version": 3, "file": "index.js", "sources": ["../../../../../../src/plugin/uni-mini-router/core/index.ts"], "sourcesContent": ["/*\r\n * @Author: 徐庆凯\r\n * @Date: 2023-03-13 19:02:05\r\n * @LastEditTime: 2023-04-27 13:16:26\r\n * @LastEditors: weisheng\r\n * @Description:\r\n * @FilePath: \\uni-mini-router\\src\\core\\index.ts\r\n * 记得注释\r\n */\r\nimport type { Route, Router } from '../interfaces'\r\nimport { inject, reactive, watch } from 'vue'\r\nimport { routeKey, routerKey } from '../symbols'\r\n/**\r\n * 返回router实例，在template的仍然可以使用$Router方法\r\n */\r\nexport function useRouter(): Router {\r\n  const router = inject(routerKey)\r\n  if (router) {\r\n    return router\r\n  } else {\r\n    throw new Error('useRouter 只可以在 Vue 上下文中使用，请确保你已经正确地注册了 \"uni-mini-router\" 并且当前正处于 Vue 上下文中')\r\n    // throw new Error(\r\n    //   'Error: useRouter can only be used within a Vue component context. Make sure you have registered the \"uni-mini-router\" correctly and it is being used inside a Vue component'\r\n    // )\r\n  }\r\n}\r\n\r\n/**\r\n * 返回当前页面路由信息route，在template的仍然可以使用$Route方法\r\n */\r\nexport function useRoute(): Route {\r\n  const currentRoute = inject(routeKey)\r\n  if (currentRoute) {\r\n    const route = reactive(currentRoute.value)\r\n    watch(currentRoute, (to) => {\r\n      Object.assign(route, to)\r\n    })\r\n    return route\r\n  } else {\r\n    throw new Error('useRoute 只可以在 Vue 上下文中使用，请确保你已经正确地注册了 \"uni-mini-router\" 并且当前正处于 Vue 上下文中')\r\n    // throw new Error(\r\n    //   'Error: useRoute can only be used within a Vue component context. Make sure you have registered the \"uni-mini-router\" correctly and it is being used inside a Vue component'\r\n    // )\r\n  }\r\n}\r\n"], "names": ["inject", "routerKey"], "mappings": ";;;AAeO,SAAS,YAAoB;AAC5B,QAAA,SAASA,qBAAOC,4CAAS;AAC/B,MAAI,QAAQ;AACH,WAAA;AAAA,EAAA,OACF;AACC,UAAA,IAAI,MAAM,2EAA2E;AAAA,EAAA;AAK/F;;"}