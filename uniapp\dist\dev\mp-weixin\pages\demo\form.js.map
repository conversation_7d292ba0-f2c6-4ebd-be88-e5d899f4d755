{"version": 3, "file": "form.js", "sources": ["../../../../../src/pages/demo/form.vue", "../../../../../uniPage:/cGFnZXMvZGVtby9mb3JtLnZ1ZQ"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n  <PageLayout navTitle=\"表单\">\r\n    <wd-form ref=\"form\" :model=\"model\">\r\n      <wd-cell-group border class=\"mt-10px mb-10px\">\r\n        <wd-input\r\n          label=\"姓名\"\r\n          label-width=\"80px\"\r\n          prop=\"value1\"\r\n          clearable\r\n          v-model=\"model.value1\"\r\n          placeholder=\"姓名\"\r\n          :rules=\"[{ required: true, message: '请填写姓名' }]\"\r\n        />\r\n        <wd-picker\r\n            label=\"性别\"\r\n            label-width=\"80px\"\r\n            prop=\"value3\"\r\n            :columns=\"columns\"\r\n            v-model=\"model.value3\"\r\n            :rules=\"[{ required: true, message: '请选择性别' }]\"\r\n        />\r\n      </wd-cell-group>\r\n      <wd-cell-group border class=\"mb-10px\">\r\n        <wd-input\r\n            label=\"密码\"\r\n            label-width=\"80px\"\r\n            prop=\"value2\"\r\n            show-password\r\n            clearable\r\n            v-model=\"model.value2\"\r\n            placeholder=\"请输入密码\"\r\n            :rules=\"[{ required: true, message: '请填写密码' }]\"\r\n        />\r\n\r\n        <wd-select-picker\r\n            label=\"爱好\"\r\n            label-width=\"80px\"\r\n            prop=\"value4\"\r\n            v-model=\"model.value4\"\r\n            :columns=\"selectColumns\"\r\n            :rules=\"[{ required: true, message: '请选择爱好' }]\"\r\n        ></wd-select-picker>\r\n      </wd-cell-group class=\"mb-10px\">\r\n\r\n      <wd-cell-group border>\r\n        <wd-calendar  label-width=\"80px\" prop=\"value5\" v-model=\"model.value5\" label=\"&nbsp;&nbsp;&nbsp;出生\" />\r\n      </wd-cell-group>\r\n      <view class=\"footer\">\r\n        <wd-button type=\"primary\" size=\"large\" @click=\"handleSubmit\" block>提交</wd-button>\r\n      </view>\r\n    </wd-form>\r\n  </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { useToast } from 'wot-design-uni'\r\nconst { success: showSuccess } = useToast()\r\nconst columns = ref([\r\n  { value: '1', label: '男' },\r\n  { value: '2', label: '女' },\r\n])\r\nconst selectColumns = ref<any>([\r\n  {\r\n    value: '101',\r\n    label: '篮球',\r\n  },\r\n  {\r\n    value: '102',\r\n    label: '足球',\r\n  },\r\n  {\r\n    value: '103',\r\n    label: '棒球',\r\n  },\r\n])\r\nconst model = reactive<{\r\n  value1: string\r\n  value2: string\r\n  value3: string\r\n  value4: any\r\n  value5: number\r\n}>({\r\n  value1: '',\r\n  value2: '',\r\n  value3: '',\r\n  value4: [],\r\n  value5: 0,\r\n})\r\n\r\nconst form = ref()\r\n\r\nfunction handleSubmit() {\r\n  form.value\r\n    .validate()\r\n    .then(({ valid, errors }) => {\r\n      if (valid) {\r\n        console.log(\"model:\",model)\r\n        showSuccess({\r\n          msg: '校验通过',\r\n        })\r\n      }\r\n    })\r\n    .catch((error) => {\r\n      console.log(error, 'error')\r\n    })\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.footer{\r\n  margin: 10px;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages/demo/form.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useToast", "ref", "reactive"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4DA,UAAM,EAAE,SAAS,YAAY,IAAIA,uBAAS;AAC1C,UAAM,UAAUC,cAAAA,IAAI;AAAA,MAClB,EAAE,OAAO,KAAK,OAAO,IAAI;AAAA,MACzB,EAAE,OAAO,KAAK,OAAO,IAAI;AAAA,IAAA,CAC1B;AACD,UAAM,gBAAgBA,cAAAA,IAAS;AAAA,MAC7B;AAAA,QACE,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,OAAO;AAAA,MAAA;AAAA,IACT,CACD;AACD,UAAM,QAAQC,cAAAA,SAMX;AAAA,MACD,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ,CAAC;AAAA,MACT,QAAQ;AAAA,IAAA,CACT;AAED,UAAM,OAAOD,cAAAA,IAAI;AAEjB,aAAS,eAAe;AACjB,WAAA,MACF,SACA,EAAA,KAAK,CAAC,EAAE,OAAO,aAAa;AAC3B,YAAI,OAAO;AACD,kBAAA,IAAI,UAAS,KAAK;AACd,sBAAA;AAAA,YACV,KAAK;AAAA,UAAA,CACN;AAAA,QAAA;AAAA,MACH,CACD,EACA,MAAM,CAAC,UAAU;AACR,gBAAA,IAAI,OAAO,OAAO;AAAA,MAAA,CAC3B;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3GL,GAAG,WAAW,eAAe;"}