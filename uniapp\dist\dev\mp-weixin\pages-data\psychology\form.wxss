/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-scroll-view.data-v-6d258234 {
  height: calc(100vh - 44px);
  /* 减去导航栏高度 */
  width: 100%;
}
.form-container.data-v-6d258234 {
  padding: 20rpx;
  padding-bottom: 120rpx;
  /* 增加底部内边距，防止内容被遮挡 */
}
.form-container .form-header.data-v-6d258234 {
  margin-bottom: 20rpx;
  text-align: center;
  /* 标题居中 */
}
.form-container .form-header .form-title.data-v-6d258234 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.form-container .form-section.data-v-6d258234 {
  background-color: #FFFFFF;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}
.form-container .form-section .form-item.data-v-6d258234 {
  margin-bottom: 30rpx;
}
.form-container .form-section .form-item .form-label.data-v-6d258234 {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}
.form-container .form-section .form-item .indent-text.data-v-6d258234 {
  text-indent: 2em;
}
.form-container .form-section .form-item .inline-form-item.data-v-6d258234 {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.form-container .form-section .form-item .inline-form-item .inline-form-label.data-v-6d258234 {
  font-size: 28rpx;
  color: #333;
  min-width: 160rpx;
}
.form-container .form-section .form-item .inline-form-item .inline-form-input.data-v-6d258234 {
  flex: 1;
  background-color: #F7F7F7;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  min-height: 80rpx;
}
.form-container .form-section .form-item .inline-form-item .inline-form-input.data-v-6d258234:disabled {
  background-color: #F5F5F5;
  color: #666;
}
.form-container .form-section .form-item .date-picker.data-v-6d258234 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #F7F7F7;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  min-height: 80rpx;
}
.form-container .form-section .form-item .date-picker .placeholder.data-v-6d258234 {
  color: #999;
}
.form-container .form-section .form-item .form-input.data-v-6d258234,
.form-container .form-section .form-item .form-textarea.data-v-6d258234 {
  width: 100%;
  background-color: #F7F7F7;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  min-height: 80rpx;
}
.form-container .form-section .form-item .form-input.data-v-6d258234:disabled,
.form-container .form-section .form-item .form-textarea.data-v-6d258234:disabled {
  background-color: #F5F5F5;
  color: #666;
}
.form-container .form-section .form-item .form-textarea.data-v-6d258234 {
  height: 180rpx;
}
.form-container .form-section .form-item .radio-group.data-v-6d258234 {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  /* 增加间距 */
}
.form-container .form-section .form-item .radio-group .radio-row.data-v-6d258234 {
  display: flex;
  gap: 150rpx;
  /* 增加间距 */
  margin-left: 20rpx;
  /* 增加左边距，实现对齐 */
  justify-content: flex-start;
  /* 添加左对齐 */
}
.form-container .form-section .form-item .radio-group .radio-item.data-v-6d258234 {
  display: flex;
  align-items: center;
  min-width: 180rpx;
  width: 240rpx;
  /* 确保宽度一致，增大宽度值以容纳更长的文本 */
}
.form-container .form-section .form-item .radio-group .radio-item .radio-btn.data-v-6d258234 {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 2rpx solid #CCCCCC;
  margin-right: 10rpx;
  position: relative;
}
.form-container .form-section .form-item .radio-group .radio-item .radio-btn.checked.data-v-6d258234 {
  border-color: #07C160;
}
.form-container .form-section .form-item .radio-group .radio-item .radio-btn.checked.data-v-6d258234:after {
  content: "";
  position: absolute;
  width: 24rpx;
  height: 24rpx;
  background-color: #07C160;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.form-container .form-section .form-item .radio-group .radio-item text.data-v-6d258234 {
  font-size: 28rpx;
  color: #333;
}
.form-container .area-picker-container.data-v-6d258234 {
  display: flex;
  flex-direction: column;
  margin-bottom: 20rpx;
}
.form-container .area-picker-container .inline-form-label.data-v-6d258234 {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}
.form-container .area-picker-container .area-picker-wrapper.data-v-6d258234 {
  width: 100%;
  background-color: #F7F7F7;
  border-radius: 8rpx;
  box-sizing: border-box;
  min-height: 80rpx;
}
.form-container .area-picker-container .area-picker-wrapper.data-v-6d258234 .wd-picker__value {
  text-align: left;
  color: #333;
}
.form-container .area-picker-container .area-picker-wrapper.data-v-6d258234 .wd-picker__action {
  display: none;
}
.form-container .inline-picker-wrapper.data-v-6d258234 {
  flex: 1;
  width: 100%;
}
.form-container .required.data-v-6d258234 {
  color: #FF0000;
  margin-right: 4rpx;
}
.form-container .submit-section.data-v-6d258234 {
  margin-top: 40rpx;
  margin-bottom: 60rpx;
  /* 增加底部间距 */
}
.form-container .submit-section .submit-btn.data-v-6d258234 {
  width: 100%;
  background-color: #07C160;
  color: #FFFFFF;
  border-radius: 8rpx;
  font-size: 32rpx;
  padding: 20rpx 0;
}
.checkbox-group.data-v-6d258234 {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  /* 添加新的样式以支持两列布局 */
}
.checkbox-group .checkbox-row.data-v-6d258234 {
  display: flex;
  gap: 150rpx;
  margin-left: 20rpx;
  justify-content: flex-start;
}
.checkbox-group .checkbox-item.data-v-6d258234 {
  display: flex;
  align-items: center;
  min-width: 180rpx;
  width: 240rpx;
}
.checkbox-group .checkbox-item .checkbox-btn.data-v-6d258234 {
  width: 36rpx;
  height: 36rpx;
  border-radius: 4rpx;
  border: 2rpx solid #CCCCCC;
  margin-right: 10rpx;
  position: relative;
}
.checkbox-group .checkbox-item .checkbox-btn.checked.data-v-6d258234 {
  border-color: #07C160;
  background-color: #07C160;
}
.checkbox-group .checkbox-item .checkbox-btn.checked.data-v-6d258234:after {
  content: "";
  position: absolute;
  width: 20rpx;
  height: 10rpx;
  border-left: 4rpx solid #FFFFFF;
  border-bottom: 4rpx solid #FFFFFF;
  top: 45%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
}
.checkbox-group .checkbox-item text.data-v-6d258234 {
  font-size: 28rpx;
  color: #333;
}
.checkbox-group .sub-form-item.data-v-6d258234 {
  margin-left: 50rpx;
  margin-top: -10rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}
.checkbox-group .sub-form-item .sub-form-label.data-v-6d258234 {
  font-size: 26rpx;
  color: #666;
  min-width: 180rpx;
}
.checkbox-group .sub-form-item .sub-form-input.data-v-6d258234 {
  flex: 1;
  background-color: #F7F7F7;
  border-radius: 8rpx;
  padding: 15rpx;
  font-size: 26rpx;
  color: #333;
  min-height: 70rpx;
  width: 100%;
  box-sizing: border-box;
}
.checkbox-group .sub-form-item .picker.data-v-6d258234 {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #F7F7F7;
  border-radius: 8rpx;
  padding: 15rpx;
  font-size: 26rpx;
  color: #333;
  min-height: 70rpx;
  width: 100%;
  box-sizing: border-box;
}
.checkbox-group .sub-form-items-row.data-v-6d258234 {
  display: flex;
  margin-left: 50rpx;
  gap: 20rpx;
  margin-top: -10rpx;
  margin-bottom: 20rpx;
}
.checkbox-group .sub-form-items-row .sub-form-item.data-v-6d258234 {
  flex: 1;
  width: 50%;
  min-width: calc(50% - 10rpx);
  /* 确保即使只有一个元素也占据一半宽度 */
  margin: 0;
  display: flex;
  align-items: center;
}
.checkbox-group .sub-form-items-row .sub-form-item .picker-container.data-v-6d258234 {
  width: 100%;
  flex: 1;
  display: block;
}
.checkbox-group .sub-form-items-row .sub-form-item .temp-picker.data-v-6d258234 {
  width: 100%;
  flex: 1;
  display: block;
}
.checkbox-group .sub-form-items-row .sub-form-item .sub-form-input.data-v-6d258234 {
  flex: 1;
  background-color: #F7F7F7;
  border-radius: 8rpx;
  padding: 15rpx;
  font-size: 26rpx;
  color: #333;
  min-height: 70rpx;
  width: 100%;
  box-sizing: border-box;
}
.checkbox-group .sub-form-items-row .sub-form-item .picker.data-v-6d258234 {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #F7F7F7;
  border-radius: 8rpx;
  padding: 15rpx;
  font-size: 26rpx;
  color: #333;
  min-height: 70rpx;
  width: 100%;
  box-sizing: border-box;
}
.location-box.data-v-6d258234 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  background-color: #F7F7F7;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  min-height: 80rpx;
}
.location-icon.data-v-6d258234 {
  color: #07C160;
  font-size: 36rpx;
}