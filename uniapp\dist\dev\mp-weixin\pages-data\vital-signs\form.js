"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
const store_user = require("../../store/user.js");
if (!Array) {
  const _component_NavBar = common_vendor.resolveComponent("NavBar");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_component_NavBar + _easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  _easycom_PageLayout();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "vitalSignsForm"
}), {
  __name: "form",
  setup(__props) {
    const userStore = store_user.useUserStore();
    const query = common_vendor.ref({});
    common_vendor.onMounted(() => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      query.value = currentPage.options || {};
      if (query.value.mode === "view" && query.value.id) {
        loadFormData(query.value.id);
      }
    });
    const mode = common_vendor.computed(() => query.value.mode || "add");
    common_vendor.computed(() => mode.value === "view");
    const formData = common_vendor.ref({
      bloodPressure: "",
      heartRate: "",
      weight: ""
    });
    const loadFormData = (id) => {
      console.log("开始加载体征详情数据，ID:", id);
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      const url = `${"https://www.mograine.cn/api"}/patient/${id}/dailysigns`;
      console.log("请求详情URL:", url);
      common_vendor.index.request({
        url,
        method: "GET",
        success: (res) => {
          const response = res.data;
          console.log("详情接口返回数据:", response);
          if (response && response.code === 200) {
            if (response.result) {
              formData.value = {
                bloodPressure: response.result.bloodPressure || "",
                heartRate: response.result.heartRate || "",
                weight: response.result.weight || ""
              };
              console.log("解析后的详情数据:", formData.value);
            } else {
              console.log("没有找到详情数据");
              common_vendor.index.showToast({
                title: "未找到记录详情",
                icon: "none"
              });
            }
          } else {
            common_vendor.index.showToast({
              title: response.msg || "获取详情失败",
              icon: "none"
            });
          }
        },
        fail: (err) => {
          console.error("获取体征详情失败:", err);
          common_vendor.index.showToast({
            title: "网络异常，请稍后重试",
            icon: "none"
          });
        },
        complete: () => {
          common_vendor.index.hideLoading();
        }
      });
    };
    const submitForm = () => {
      console.log("提交表单数据:", formData.value);
      if (!formData.value.bloodPressure) {
        common_vendor.index.showToast({
          title: "请输入血压",
          icon: "none"
        });
        return;
      }
      if (!formData.value.heartRate) {
        common_vendor.index.showToast({
          title: "请输入心率",
          icon: "none"
        });
        return;
      }
      if (!formData.value.weight) {
        common_vendor.index.showToast({
          title: "请输入体重",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: mode.value === "add" ? "提交中..." : "保存中..."
      });
      const requestData = {
        bloodPressure: formData.value.bloodPressure,
        heartRate: formData.value.heartRate,
        weight: formData.value.weight,
        userId: userStore.userInfo.userid
      };
      if (mode.value === "view") {
        requestData.id = query.value.id;
        requestData.updateUserId = userStore.userInfo.userid;
      }
      console.log("formData为：", formData.value);
      console.log("requestData为", requestData);
      common_vendor.index.request({
        url: `${"https://www.mograine.cn/api"}/patient/savedailysigns`,
        method: "POST",
        data: requestData,
        success: (res) => {
          var _a, _b;
          common_vendor.index.hideLoading();
          if ((_a = res.data) == null ? void 0 : _a.success) {
            common_vendor.index.showModal({
              title: mode.value === "add" ? "提交成功" : "保存成功",
              showCancel: false,
              success: () => {
                common_vendor.index.navigateBack();
              }
            });
          } else {
            const errorMsg = ((_b = res.data) == null ? void 0 : _b.message) || "提交失败，未知错误";
            common_vendor.index.showModal({
              title: mode.value === "add" ? "提交失败" : "保存失败",
              content: errorMsg,
              showCancel: false
            });
          }
        },
        fail: (err) => {
          common_vendor.index.hideLoading();
          const errorMsg = err.errMsg || "网络错误，请稍后重试";
          common_vendor.index.showModal({
            title: "提交失败",
            content: errorMsg,
            showCancel: false
          });
        }
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          title: mode.value === "add" ? "新增" : "编辑",
          showBack: true
        }),
        b: formData.value.bloodPressure,
        c: common_vendor.o(($event) => formData.value.bloodPressure = $event.detail.value),
        d: formData.value.heartRate,
        e: common_vendor.o(($event) => formData.value.heartRate = $event.detail.value),
        f: formData.value.weight,
        g: common_vendor.o(($event) => formData.value.weight = $event.detail.value),
        h: common_vendor.t(mode.value === "add" ? "提交" : "保存"),
        i: common_vendor.o(submitForm)
      };
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-107d7006"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=form.js.map
