{"version": 3, "file": "wd-config-provider.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-config-provider/wd-config-provider.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1jb25maWctcHJvdmlkZXIvd2QtY29uZmlnLXByb3ZpZGVyLnZ1ZQ"], "sourcesContent": ["<!--\n * @Author: weish<PERSON>\n * @Date: 2023-06-13 11:34:35\n * @LastEditTime: 2025-04-28 22:26:25\n * @LastEditors: weisheng\n * @Description: \n * @FilePath: /wot-design-uni/src/uni_modules/wot-design-uni/components/wd-config-provider/wd-config-provider.vue\n * 记得注释\n-->\n<template>\n  <view :class=\"themeClass\" :style=\"themeStyle\">\n    <slot />\n  </view>\n</template>\n\n<script lang=\"ts\">\nexport default {\n  name: 'wd-config-provider',\n  options: {\n    virtualHost: true,\n    addGlobalClass: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { configProviderProps } from './types'\nimport { objToStyle } from '../common/util'\n\nconst props = defineProps(configProviderProps)\n\nconst themeClass = computed(() => {\n  return `wot-theme-${props.theme} ${props.customClass}`\n})\n\nconst themeStyle = computed(() => {\n  const styleObj = mapThemeVarsToCSSVars(props.themeVars)\n  return styleObj ? `${objToStyle(styleObj)}${props.customStyle}` : props.customStyle\n})\n\nconst kebabCase = (str: string): string => {\n  str = str.replace(str.charAt(0), str.charAt(0).toLocaleLowerCase())\n  return str.replace(/([a-z])([A-Z])/g, (_, p1, p2) => p1 + '-' + p2.toLowerCase())\n}\nconst colorRgb = (str: string) => {\n  if (!str) return\n  var sColor = str.toLowerCase()\n  //十六进制颜色值的正则表达式\n  var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/\n  // 如果是16进制颜色\n  if (sColor && reg.test(sColor)) {\n    if (sColor.length === 4) {\n      var sColorNew = '#'\n      for (let i = 1; i < 4; i += 1) {\n        sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1))\n      }\n      sColor = sColorNew\n    }\n    //处理六位的颜色值\n    var sColorChange: number[] = []\n    for (let i = 1; i < 7; i += 2) {\n      sColorChange.push(parseInt('0x' + sColor.slice(i, i + 2)))\n    }\n    return sColorChange.join(',')\n  }\n  return null\n}\n\nconst mapThemeVarsToCSSVars = (themeVars: Record<string, string>) => {\n  if (!themeVars) return\n  const cssVars: Record<string, string> = {}\n  Object.keys(themeVars).forEach((key) => {\n    cssVars[`--wot-${kebabCase(key)}`] = themeVars[key]\n  })\n\n  return cssVars\n}\n</script>\n\n<style lang=\"scss\" scoped></style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-config-provider/wd-config-provider.vue'\nwx.createComponent(Component)"], "names": ["computed", "objToStyle", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAgBA,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,EAAA;AAEpB;;;;AAQA,UAAM,QAAQ;AAER,UAAA,aAAaA,cAAAA,SAAS,MAAM;AAChC,aAAO,aAAa,MAAM,KAAK,IAAI,MAAM,WAAW;AAAA,IAAA,CACrD;AAEK,UAAA,aAAaA,cAAAA,SAAS,MAAM;AAC1B,YAAA,WAAW,sBAAsB,MAAM,SAAS;AAC/C,aAAA,WAAW,GAAGC,cAAAA,WAAW,QAAQ,CAAC,GAAG,MAAM,WAAW,KAAK,MAAM;AAAA,IAAA,CACzE;AAEK,UAAA,YAAY,CAAC,QAAwB;AACnC,YAAA,IAAI,QAAQ,IAAI,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,EAAE,kBAAA,CAAmB;AAC3D,aAAA,IAAI,QAAQ,mBAAmB,CAAC,GAAG,IAAI,OAAO,KAAK,MAAM,GAAG,YAAA,CAAa;AAAA,IAClF;AAyBM,UAAA,wBAAwB,CAAC,cAAsC;AACnE,UAAI,CAAC;AAAW;AAChB,YAAM,UAAkC,CAAC;AACzC,aAAO,KAAK,SAAS,EAAE,QAAQ,CAAC,QAAQ;AACtC,gBAAQ,SAAS,UAAU,GAAG,CAAC,EAAE,IAAI,UAAU,GAAG;AAAA,MAAA,CACnD;AAEM,aAAA;AAAA,IACT;;;;;;;;;AC7EA,GAAG,gBAAgBC,SAAS;", "x_google_ignoreList": [0]}