"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_uitls = require("../../../common/uitls.js");
const common_constants = require("../../../common/constants.js");
const utils_http = require("../../../utils/http.js");
require("../../../plugin/uni-mini-router/router/index.js");
const plugin_uniMiniRouter_core_index = require("../../../plugin/uni-mini-router/core/index.js");
if (!Array) {
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  const _easycom_wd_toast2 = common_vendor.resolveComponent("wd-toast");
  (_easycom_z_paging2 + _easycom_wd_toast2)();
}
const _easycom_z_paging = () => "../../../uni_modules/z-paging/components/z-paging/z-paging.js";
const _easycom_wd_toast = () => "../../../node-modules/wot-design-uni/components/wd-toast/wd-toast.js";
if (!Math) {
  (_easycom_z_paging + _easycom_wd_toast)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "addressBookList",
  setup(__props) {
    const toast = common_vendor.useToast();
    const router = plugin_uniMiniRouter_core_index.useRouter();
    const paging = common_vendor.ref(null);
    const dataList = common_vendor.ref([]);
    const dataSource = common_vendor.computed(() => {
      return [
        {
          name: "联系人",
          icon: "addressbook",
          type: "friend",
          color: "blue",
          path: "contacts",
          value: "1"
        },
        { name: "我的群组", icon: "group", color: "azure-green", path: "myGroup", value: "2" },
        { name: "更多功能", icon: "moreandroid", color: "orange", path: "msgMore", value: "3" },
        ...dataList.value.map((item) => {
          return {
            label: item.name,
            name: item.name,
            value: item.id,
            key: item.id,
            color: "",
            icon: "list",
            path: "tenant"
          };
        })
      ];
    });
    const queryList = () => {
      utils_http.http.get("/sys/tenant/getCurrentUserTenant").then((res) => {
        var _a, _b;
        if (res.success && ((_b = (_a = res.result) == null ? void 0 : _a.list) == null ? void 0 : _b.length)) {
          paging.value.complete(res.result.list);
          common_uitls.cache(common_constants.TENANT_LIST, res.result.list);
        } else {
          paging.value.complete(false);
        }
      }).catch((res) => {
        paging.value.complete(false);
      });
    };
    const handleGo = (item) => {
      if (!common_uitls.hasRoute({ name: item.path })) {
        toast.warning("还未开发~");
        return;
      }
      router.push({
        name: item.path,
        params: {
          id: item.value,
          title: item.name
        }
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.f(common_vendor.unref(dataSource), (item, index, i0) => {
          return {
            a: common_vendor.n(`cuIcon-${item.icon}`),
            b: common_vendor.n(item.color),
            c: common_vendor.t(item.name),
            d: index,
            e: common_vendor.o(($event) => handleGo(item), index)
          };
        }),
        b: common_vendor.sr(paging, "b42d73c8-0", {
          "k": "paging"
        }),
        c: common_vendor.o(queryList),
        d: common_vendor.o(($event) => common_vendor.isRef(dataList) ? dataList.value = $event : null),
        e: common_vendor.p({
          fixed: false,
          modelValue: common_vendor.unref(dataList)
        })
      };
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-b42d73c8"]]);
wx.createComponent(Component);
//# sourceMappingURL=addressBookList.js.map
