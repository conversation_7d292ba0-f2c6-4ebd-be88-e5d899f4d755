"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../../common/vendor.js");
const __default__ = {
  name: "wd-row",
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: "shared"
  }
};
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, __default__), {
  props: common_vendor.rowProps,
  setup(__props) {
    const props = __props;
    const { linkChildren } = common_vendor.useChildren(common_vendor.ROW_KEY);
    linkChildren({ props });
    const rowStyle = common_vendor.computed(() => {
      const style = {};
      const { gutter } = props;
      if (gutter < 0) {
        console.error("[wot design] warning(wd-row): attribute gutter must be greater than or equal to 0");
      } else if (gutter) {
        style.marginLeft = common_vendor.addUnit(gutter / 2);
        style.marginRight = common_vendor.addUnit(gutter / 2);
      }
      return `${common_vendor.objToStyle(style)}${props.customStyle}`;
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.n(`wd-row ${_ctx.customClass}`),
        b: common_vendor.s(rowStyle.value)
      };
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-2e7bbf40"]]);
wx.createComponent(Component);
//# sourceMappingURL=wd-row.js.map
