{"version": 3, "file": "wd-notify.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-notify/wd-notify.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1ub3RpZnkvd2Qtbm90aWZ5LnZ1ZQ"], "sourcesContent": ["<template>\n  <wd-popup\n    v-model=\"state.visible\"\n    :custom-style=\"customStyle\"\n    :position=\"state.position\"\n    :z-index=\"state.zIndex\"\n    :duration=\"250\"\n    :modal=\"false\"\n    @leave=\"onClosed\"\n    @enter=\"onOpened\"\n  >\n    <view class=\"wd-notify\" :class=\"[`wd-notify--${state.type}`]\" :style=\"{ color: state.color, background: state.background }\" @click=\"onClick\">\n      <slot>{{ state.message }}</slot>\n    </view>\n  </wd-popup>\n</template>\n<script lang=\"ts\">\nexport default {\n  name: 'wd-notify',\n  options: {\n    virtualHost: true,\n    addGlobalClass: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport wdPopup from '../wd-popup/wd-popup.vue'\nimport { inject, computed, watch, ref } from 'vue'\nimport { notifyProps, type NotifyProps } from './types'\nimport { getNotifyOptionKey } from '.'\nimport { addUnit, isFunction } from '../common/util'\n\nconst props = defineProps(notifyProps)\nconst emits = defineEmits<{\n  (e: 'update:visible', value: boolean): void\n  (e: 'click', event: MouseEvent): void\n  (e: 'closed'): void\n  (e: 'opened'): void\n}>()\nconst state = inject(getNotifyOptionKey(props.selector), ref<NotifyProps>(props))\n\nconst customStyle = computed(() => {\n  const { safeHeight, position } = state.value\n  let customStyle: string = ''\n  switch (position) {\n    case 'top':\n      customStyle = `top: calc(var(--window-top) + ${addUnit(safeHeight || 0)})`\n      break\n    case 'bottom':\n      customStyle = 'bottom: var(--window-bottom)'\n      break\n    default:\n      break\n  }\n  return customStyle\n})\n\nconst onClick = (event: MouseEvent) => {\n  if (isFunction(state.value.onClick)) return state.value.onClick(event)\n  emits('click', event)\n}\nconst onClosed = () => {\n  if (isFunction(state.value.onClosed)) return state.value.onClosed()\n  emits('closed')\n}\nconst onOpened = () => {\n  if (isFunction(state.value.onOpened)) return state.value.onOpened()\n  emits('opened')\n}\n\nwatch(\n  () => state.value.visible,\n  (visible) => {\n    emits('update:visible', visible as boolean)\n  },\n  { deep: true }\n)\n</script>\n\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-notify/wd-notify.vue'\nwx.createComponent(Component)"], "names": ["inject", "getNotifyOptionKey", "ref", "computed", "customStyle", "addUnit", "isFunction", "watch"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AA4BA,MAAA,UAAoB,MAAA;AAXpB,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,EAAA;AAEpB;;;;;AAUA,UAAM,QAAQ;AACd,UAAM,QAAQ;AAMR,UAAA,QAAQA,qBAAOC,iCAAmB,MAAM,QAAQ,GAAGC,cAAAA,IAAiB,KAAK,CAAC;AAE1E,UAAA,cAAcC,cAAAA,SAAS,MAAM;AACjC,YAAM,EAAE,YAAY,SAAS,IAAI,MAAM;AACvC,UAAIC,eAAsB;AAC1B,cAAQ,UAAU;AAAA,QAChB,KAAK;AACHA,yBAAc,iCAAiCC,cAAAA,QAAQ,cAAc,CAAC,CAAC;AACvE;AAAA,QACF,KAAK;AACHD,yBAAc;AACd;AAAA,MAEA;AAEGA,aAAAA;AAAAA,IAAA,CACR;AAEK,UAAA,UAAU,CAAC,UAAsB;AACjC,UAAAE,yBAAW,MAAM,MAAM,OAAO;AAAU,eAAA,MAAM,MAAM,QAAQ,KAAK;AACrE,YAAM,SAAS,KAAK;AAAA,IACtB;AACA,UAAM,WAAW,MAAM;AACjB,UAAAA,yBAAW,MAAM,MAAM,QAAQ;AAAU,eAAA,MAAM,MAAM,SAAS;AAClE,YAAM,QAAQ;AAAA,IAChB;AACA,UAAM,WAAW,MAAM;AACjB,UAAAA,yBAAW,MAAM,MAAM,QAAQ;AAAU,eAAA,MAAM,MAAM,SAAS;AAClE,YAAM,QAAQ;AAAA,IAChB;AAEAC,kBAAA;AAAA,MACE,MAAM,MAAM,MAAM;AAAA,MAClB,CAAC,YAAY;AACX,cAAM,kBAAkB,OAAkB;AAAA,MAC5C;AAAA,MACA,EAAE,MAAM,KAAK;AAAA,IACf;;;;;;;;;;;;;;;;;;;;;;;;AC7EA,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}