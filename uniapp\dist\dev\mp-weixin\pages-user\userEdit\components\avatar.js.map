{"version": 3, "file": "avatar.js", "sources": ["../../../../../../src/pages-user/userEdit/components/avatar.vue", "../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvcGFnZXMtdXNlci91c2VyRWRpdC9jb21wb25lbnRzL2F2YXRhci52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"wrap\"> <template v-if=\"imgSrc\">\r\n      <view class=\"showArea\">\r\n        <view class=\"iconBox\" @click=\"handleDel\">\r\n          <view class=\"cuIcon-close\"></view>\r\n        </view>\r\n\r\n        <!-- 微信小程序直接使用button的chooseAvatar功能 -->\r\n        <button class=\"avatar-button\" open-type=\"chooseAvatar\" @chooseavatar=\"onChooseWechatAvatar\">\r\n          <wd-img :radius=\"4\" enable-preview label=\"头像\" width=\"60px\" height=\"60px\" :src=\"imgSrc\"></wd-img>\r\n        </button>\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n      </view>\r\n    </template><template v-else>\r\n\r\n      <!-- 微信小程序直接使用button的chooseAvatar功能 -->\r\n      <button class=\"uploadArea\" open-type=\"chooseAvatar\" @chooseavatar=\"onChooseWechatAvatar\"\r\n        hover-class=\"uploadArea-hover\">\r\n        <view class=\"iconBox\">\r\n          <view class=\"cuIcon-cameraadd\"></view>\r\n        </view>\r\n      </button>\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n    </template>\r\n\r\n    <!-- 非微信环境的头像选择弹窗 -->\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n    <wd-message-box></wd-message-box>\r\n  </view>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { useMessage, useToast } from 'wot-design-uni'\r\nimport { computed, watch, ref, onMounted } from 'vue'\r\nimport { getEnvBaseUrl } from '@/utils'\r\nimport useUpload from '@/hooks/useUpload'\r\nimport { useUserStore } from '@/store/user'\r\nimport request from '@/utils/request'\r\n\r\nconst userStore = useUserStore()\r\n\r\ndefineOptions({\r\n  name: 'avatar',\r\n  options: {\r\n    // apply-shared‌：当前页面样式会影响到子组件样式.(小程序)\r\n    // shared‌：当前页面样式影响到子组件，子组件样式也会影响到当前页面.(小程序)\r\n    styleIsolation: 'shared',\r\n  },\r\n})\r\nconst props = defineProps(['modelValue'])\r\nconst emit = defineEmits(['update:modelValue', 'update:fileName'])\r\n\r\nconst toast = useToast()\r\nconst message = useMessage()\r\nconst showOptions = ref(false)\r\nconst api = {\r\n  uploadUrl: `${getEnvBaseUrl()}/file/uploadavatar`,\r\n}\r\nlet stopWatch\r\n\r\n// 保存文件名，用于提交给后端\r\nconst fileName = ref('')\r\n// 保存通过请求获取的图片URL\r\nconst fetchedImgSrc = ref('')\r\n\r\nconst imgSrc = computed(() => {\r\n  if (props.modelValue) {\r\n    // 检查是否已经是完整URL（以http开头）\r\n    if (props.modelValue.startsWith('http')) {\r\n      return props.modelValue\r\n    }\r\n    // 如果是文件名，返回通过请求获取的URL\r\n    return fetchedImgSrc.value\r\n  }\r\n  return ''\r\n})\r\n\r\n// 获取文件URL的函数\r\nconst fetchFileUrl = async (objectName: string) => {\r\n  try {\r\n    const response = await request(`/file/url`, {\r\n      method: 'GET',\r\n      params: {\r\n        objectName: objectName,\r\n        token: userStore.userInfo.token\r\n      }\r\n    })\r\n\r\n    // 根据接口返回的数据结构：response.result.fileUrl\r\n    if (response?.success && response?.result?.fileUrl) {\r\n      fetchedImgSrc.value = response.result.fileUrl\r\n    } else {\r\n      console.warn('响应数据格式异常:', response)\r\n      fetchedImgSrc.value = ''\r\n    }\r\n    return response\r\n  } catch (error) {\r\n    console.error('获取文件URL失败:', error)\r\n    fetchedImgSrc.value = ''\r\n    return null\r\n  }\r\n}\r\n\r\n// 监听 modelValue 变化，当有文件名时发起请求\r\nwatch(() => props.modelValue, (newValue) => {\r\n  if (newValue && !newValue.startsWith('http')) {\r\n    // 如果是文件名，发起请求获取URL\r\n    fetchFileUrl(newValue)\r\n  } else {\r\n    // 如果是完整URL或空值，清空请求获取的URL\r\n    fetchedImgSrc.value = ''\r\n  }\r\n}, { immediate: true })\r\n\r\nconst handleDel = () => {\r\n  message\r\n    .confirm({\r\n      msg: '确定要删除这个头像吗？',\r\n      title: '提示',\r\n    })\r\n    .then(() => {\r\n      emit('update:modelValue', '')\r\n      fileName.value = ''\r\n      emit('update:fileName', '')\r\n    })\r\n    .catch(() => {\r\n      console.log('点击了取消按钮')\r\n    })\r\n}\r\n\r\n// 显示头像选择选项（仅非微信环境使用）\r\nconst showAvatarOptions = () => {\r\n\r\n\r\n\r\n}\r\n\r\n// 微信头像选择回调\r\nconst onChooseWechatAvatar = (e: any) => {\r\n  console.log('微信头像选择回调:', e)\r\n  const { avatarUrl } = e.detail\r\n\r\n  if (!avatarUrl) {\r\n    toast.warning('未获取到头像信息')\r\n    return\r\n  }\r\n\r\n  // 将微信头像上传到服务器\r\n  uploadWechatAvatar(avatarUrl)\r\n}\r\n\r\n// 上传微信头像到服务器\r\nconst uploadWechatAvatar = (avatarUrl: string) => {\r\n  console.log('开始上传微信头像:', avatarUrl)\r\n\r\n  // 检查文件路径是否为微信临时文件\r\n  if (!avatarUrl.includes('tmp') && !avatarUrl.includes('wxfile://')) {\r\n    toast.warning('头像文件格式不正确')\r\n    return\r\n  }\r\n\r\n  uni.showLoading({\r\n    title: '上传头像中...',\r\n    mask: true\r\n  })\r\n\r\n  uni.uploadFile({\r\n    url: api.uploadUrl,\r\n    filePath: avatarUrl,\r\n    name: 'file',\r\n    formData: {\r\n      type: 'avatar' // 标识为头像上传\r\n    },\r\n    header: {\r\n      'X-Access-Token': userStore.userInfo.token\r\n    },\r\n    success: (res) => {\r\n      console.log('微信头像上传响应:', res)\r\n      try {\r\n        const data = JSON.parse(res.data)\r\n        if (data && data.success && data.result && data.result.fileUrl) {\r\n          // 直接使用服务端返回的完整fileUrl\r\n          emit('update:modelValue', data.result.fileUrl)\r\n          // 发送fileName用于提交给后端\r\n          fileName.value = data.result.fileName\r\n          emit('update:fileName', data.result.fileName)\r\n          toast.success('头像上传成功')\r\n        } else {\r\n          console.error('上传失败:', data)\r\n          // 尝试解码错误信息\r\n          let errorMessage = data?.message || '头像上传失败'\r\n          try {\r\n            if (typeof errorMessage === 'string' && /[^\\u0000-\\u007f]/.test(errorMessage)) {\r\n              // 可能是编码问题，记录原始消息但显示通用错误\r\n              console.error('可能的编码问题:', errorMessage)\r\n              errorMessage = '头像上传失败'\r\n            }\r\n          } catch (e) {\r\n            console.error('处理错误消息异常:', e)\r\n          }\r\n          toast.warning(errorMessage)\r\n        }\r\n      } catch (error) {\r\n        console.error('解析上传响应失败:', error)\r\n        toast.warning('头像上传失败')\r\n      }\r\n    },\r\n    fail: (error) => {\r\n      console.error('微信头像上传失败:', error)\r\n      toast.warning('头像上传失败，请重试')\r\n    },\r\n    complete: () => {\r\n      uni.hideLoading()\r\n    }\r\n  })\r\n}\r\n\r\n// 从相册选择\r\nconst handleUploadFromAlbum = () => {\r\n  showOptions.value = false\r\n  const { loading, data, error, run } = useUpload<any>({ name: 'file', type: 'avatar' }, {\r\n    url: api.uploadUrl,\r\n    sourceType: ['album']\r\n  })\r\n\r\n  if (stopWatch) stopWatch()\r\n  run()\r\n\r\n  stopWatch = watch(\r\n    () => [loading.value, error.value, data.value],\r\n    ([loading, err, uploadData]) => {\r\n      if (loading == false) {\r\n        if (err) {\r\n          toast.warning('上传失败')\r\n        } else {\r\n          if (uploadData && uploadData.success && uploadData.result && uploadData.result.fileUrl) {\r\n            // 直接使用服务端返回的完整fileUrl\r\n            emit('update:modelValue', uploadData.result.fileUrl)\r\n            // 发送fileName用于提交给后端\r\n            fileName.value = uploadData.result.fileName\r\n            emit('update:fileName', uploadData.result.fileName)\r\n            toast.success('头像上传成功')\r\n          } else {\r\n            console.error('上传数据格式不正确:', uploadData)\r\n            toast.warning('头像上传失败')\r\n          }\r\n        }\r\n      }\r\n    },\r\n  )\r\n}\r\n\r\n// 拍照\r\nconst handleUploadFromCamera = () => {\r\n  showOptions.value = false\r\n  const { loading, data, error, run } = useUpload<any>({ name: 'file', type: 'avatar' }, {\r\n    url: api.uploadUrl,\r\n    sourceType: ['camera']\r\n  })\r\n\r\n  if (stopWatch) stopWatch()\r\n  run()\r\n\r\n  stopWatch = watch(\r\n    () => [loading.value, error.value, data.value],\r\n    ([loading, err, uploadData]) => {\r\n      if (loading == false) {\r\n        if (err) {\r\n          toast.warning('上传失败')\r\n        } else {\r\n          if (uploadData && uploadData.success && uploadData.result && uploadData.result.fileUrl) {\r\n            // 直接使用服务端返回的完整fileUrl\r\n            emit('update:modelValue', uploadData.result.fileUrl)\r\n            // 发送fileName用于提交给后端\r\n            fileName.value = uploadData.result.fileName\r\n            emit('update:fileName', uploadData.result.fileName)\r\n            toast.success('头像上传成功')\r\n          } else {\r\n            console.error('上传数据格式不正确:', uploadData)\r\n            toast.warning('头像上传失败')\r\n          }\r\n        }\r\n      }\r\n    },\r\n  )\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.wrap {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-start;\r\n}\r\n\r\n.showArea {\r\n  position: relative;\r\n  width: fit-content;\r\n\r\n  .iconBox {\r\n    position: absolute;\r\n    right: 0;\r\n    top: 0;\r\n    border-bottom-left-radius: 3px;\r\n    padding: 3px 6px;\r\n    height: auto;\r\n    background-color: rgba(0, 0, 0, 0.5);\r\n    z-index: 1;\r\n    color: #fff;\r\n    font-size: 12px;\r\n    line-height: 1;\r\n    border-radius: 2px;\r\n  }\r\n\r\n  .avatar-button {\r\n    padding: 0;\r\n    border: none;\r\n    background: transparent;\r\n    display: block;\r\n  }\r\n}\r\n\r\n.uploadArea {\r\n  width: 60px;\r\n  height: 60px;\r\n  border: none;\r\n  background: transparent;\r\n  padding: 0;\r\n\r\n  .iconBox {\r\n    height: 100%;\r\n    width: 100%;\r\n    border: 2px solid #eee;\r\n    color: #8799a3;\r\n    margin: auto;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    flex-direction: column;\r\n    font-size: 27px;\r\n  }\r\n\r\n  &.uploadArea-hover .iconBox {\r\n    background-color: #f5f5f5;\r\n  }\r\n}\r\n\r\n// 头像选择弹窗样式\r\n.avatar-options {\r\n  background: #fff;\r\n  border-radius: 12px 12px 0 0;\r\n  padding: 0;\r\n\r\n  .options-title {\r\n    text-align: center;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n    color: #333;\r\n    padding: 20px 0 10px;\r\n    border-bottom: 1px solid #f0f0f0;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .options-list {\r\n    padding: 0 20px;\r\n\r\n    .option-item {\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 15px 0;\r\n      border: none;\r\n      background: transparent;\r\n      width: 100%;\r\n      text-align: left;\r\n      font-size: 16px;\r\n      color: #333;\r\n      border-bottom: 1px solid #f5f5f5;\r\n\r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n\r\n      &.option-item-hover {\r\n        background-color: #f5f5f5;\r\n      }\r\n\r\n      .option-icon {\r\n        width: 24px;\r\n        height: 24px;\r\n        margin-right: 12px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        .cuIcon-weixin {\r\n          color: #07C160;\r\n          font-size: 20px;\r\n        }\r\n\r\n        .cuIcon-pic {\r\n          color: #576b95;\r\n          font-size: 20px;\r\n        }\r\n\r\n        .cuIcon-camera {\r\n          color: #ff6b6b;\r\n          font-size: 20px;\r\n        }\r\n      }\r\n\r\n      .option-text {\r\n        flex: 1;\r\n        font-size: 16px;\r\n        color: #333;\r\n      }\r\n    }\r\n  }\r\n\r\n  .options-cancel {\r\n    text-align: center;\r\n    padding: 15px 0;\r\n    font-size: 16px;\r\n    color: #666;\r\n    border-top: 8px solid #f5f5f5;\r\n    cursor: pointer;\r\n\r\n    &:active {\r\n      background-color: #f0f0f0;\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-user/userEdit/components/avatar.vue'\nwx.createComponent(Component)"], "names": ["useUserStore", "useToast", "useMessage", "ref", "getEnvBaseUrl", "computed", "request", "watch", "uni"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkFA,UAAM,YAAYA,WAAAA,aAAa;AAU/B,UAAM,QAAQ;AACd,UAAM,OAAO;AAEb,UAAM,QAAQC,cAAAA,SAAS;AACvB,UAAM,UAAUC,cAAAA,WAAW;AACPC,kBAAAA,IAAI,KAAK;AAC7B,UAAM,MAAM;AAAA,MACV,WAAW,GAAGC,YAAA,cAAA,CAAe;AAAA,IAC/B;AAIM,UAAA,WAAWD,kBAAI,EAAE;AAEjB,UAAA,gBAAgBA,kBAAI,EAAE;AAEtB,UAAA,SAASE,cAAAA,SAAS,MAAM;AAC5B,UAAI,MAAM,YAAY;AAEpB,YAAI,MAAM,WAAW,WAAW,MAAM,GAAG;AACvC,iBAAO,MAAM;AAAA,QAAA;AAGf,eAAO,cAAc;AAAA,MAAA;AAEhB,aAAA;AAAA,IAAA,CACR;AAGK,UAAA,eAAe,CAAO,eAAuB;;AAC7C,UAAA;AACI,cAAA,WAAW,MAAMC,sBAAQ,aAAa;AAAA,UAC1C,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN;AAAA,YACA,OAAO,UAAU,SAAS;AAAA,UAAA;AAAA,QAC5B,CACD;AAGD,aAAI,qCAAU,cAAW,0CAAU,WAAV,mBAAkB,UAAS;AACpC,wBAAA,QAAQ,SAAS,OAAO;AAAA,QAAA,OACjC;AACG,kBAAA,KAAK,aAAa,QAAQ;AAClC,wBAAc,QAAQ;AAAA,QAAA;AAEjB,eAAA;AAAA,eACA,OAAO;AACN,gBAAA,MAAM,cAAc,KAAK;AACjC,sBAAc,QAAQ;AACf,eAAA;AAAA,MAAA;AAAA,IAEX;AAGAC,kBAAAA,MAAM,MAAM,MAAM,YAAY,CAAC,aAAa;AAC1C,UAAI,YAAY,CAAC,SAAS,WAAW,MAAM,GAAG;AAE5C,qBAAa,QAAQ;AAAA,MAAA,OAChB;AAEL,sBAAc,QAAQ;AAAA,MAAA;AAAA,IACxB,GACC,EAAE,WAAW,MAAM;AAEtB,UAAM,YAAY,MAAM;AACtB,cACG,QAAQ;AAAA,QACP,KAAK;AAAA,QACL,OAAO;AAAA,MAAA,CACR,EACA,KAAK,MAAM;AACV,aAAK,qBAAqB,EAAE;AAC5B,iBAAS,QAAQ;AACjB,aAAK,mBAAmB,EAAE;AAAA,MAAA,CAC3B,EACA,MAAM,MAAM;AACX,gBAAQ,IAAI,SAAS;AAAA,MAAA,CACtB;AAAA,IACL;AAUM,UAAA,uBAAuB,CAAC,MAAW;AAC/B,cAAA,IAAI,aAAa,CAAC;AACpB,YAAA,EAAE,cAAc,EAAE;AAExB,UAAI,CAAC,WAAW;AACd,cAAM,QAAQ,UAAU;AACxB;AAAA,MAAA;AAIF,yBAAmB,SAAS;AAAA,IAC9B;AAGM,UAAA,qBAAqB,CAAC,cAAsB;AACxC,cAAA,IAAI,aAAa,SAAS;AAG9B,UAAA,CAAC,UAAU,SAAS,KAAK,KAAK,CAAC,UAAU,SAAS,WAAW,GAAG;AAClE,cAAM,QAAQ,WAAW;AACzB;AAAA,MAAA;AAGFC,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,MAAM;AAAA,MAAA,CACP;AAEDA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,IAAI;AAAA,QACT,UAAU;AAAA,QACV,MAAM;AAAA,QACN,UAAU;AAAA,UACR,MAAM;AAAA;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,UACN,kBAAkB,UAAU,SAAS;AAAA,QACvC;AAAA,QACA,SAAS,CAAC,QAAQ;AACR,kBAAA,IAAI,aAAa,GAAG;AACxB,cAAA;AACF,kBAAM,OAAO,KAAK,MAAM,IAAI,IAAI;AAChC,gBAAI,QAAQ,KAAK,WAAW,KAAK,UAAU,KAAK,OAAO,SAAS;AAEzD,mBAAA,qBAAqB,KAAK,OAAO,OAAO;AAEpC,uBAAA,QAAQ,KAAK,OAAO;AACxB,mBAAA,mBAAmB,KAAK,OAAO,QAAQ;AAC5C,oBAAM,QAAQ,QAAQ;AAAA,YAAA,OACjB;AACG,sBAAA,MAAM,SAAS,IAAI;AAEvB,kBAAA,gBAAe,6BAAM,YAAW;AAChC,kBAAA;AACF,oBAAI,OAAO,iBAAiB,YAAY,mBAAmB,KAAK,YAAY,GAAG;AAErE,0BAAA,MAAM,YAAY,YAAY;AACvB,iCAAA;AAAA,gBAAA;AAAA,uBAEV,GAAG;AACF,wBAAA,MAAM,aAAa,CAAC;AAAA,cAAA;AAE9B,oBAAM,QAAQ,YAAY;AAAA,YAAA;AAAA,mBAErB,OAAO;AACN,oBAAA,MAAM,aAAa,KAAK;AAChC,kBAAM,QAAQ,QAAQ;AAAA,UAAA;AAAA,QAE1B;AAAA,QACA,MAAM,CAAC,UAAU;AACP,kBAAA,MAAM,aAAa,KAAK;AAChC,gBAAM,QAAQ,YAAY;AAAA,QAC5B;AAAA,QACA,UAAU,MAAM;AACdA,wBAAAA,MAAI,YAAY;AAAA,QAAA;AAAA,MAClB,CACD;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;ACjQA,GAAG,gBAAgB,SAAS;"}