"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../common/vendor.js");
const utils_http = require("../../../utils/http.js");
if (!Array) {
  const _easycom_wd_input2 = common_vendor.resolveComponent("wd-input");
  _easycom_wd_input2();
}
const _easycom_wd_input = () => "../../../node-modules/wot-design-uni/components/wd-input/wd-input.js";
if (!Math) {
  (_easycom_wd_input + LinkRecordsModal)();
}
const LinkRecordsModal = () => "./link-records-modal.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "onlinePopupLinkRecord.vue",
  options: {
    styleIsolation: "shared"
  }
}), {
  __name: "online-popup-link-record",
  props: {
    value: {
      type: String,
      required: false
    },
    name: {
      type: String,
      required: false
    },
    disabled: {
      type: Boolean,
      default: true,
      required: false
    },
    required: {
      type: Boolean,
      default: true,
      required: false
    },
    formSchema: {
      type: Object,
      required: true
    }
  },
  emits: ["change", "update:value", "selected"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    common_vendor.useToast();
    const lrmRef = common_vendor.ref();
    const showText = common_vendor.ref("");
    const selectVal = common_vendor.ref([]);
    const attrs = common_vendor.useAttrs();
    const reportModal = common_vendor.reactive({
      show: false
    });
    const dictCode = common_vendor.computed(() => {
      var _a;
      return (_a = props.formSchema) == null ? void 0 : _a.dictCode;
    });
    const dictTable = common_vendor.computed(() => {
      var _a;
      return (_a = props.formSchema) == null ? void 0 : _a.dictTable;
    });
    const dictText = common_vendor.computed(() => {
      var _a;
      return (_a = props.formSchema) == null ? void 0 : _a.dictText;
    });
    const multi = common_vendor.computed(() => {
      var _a;
      if ((_a = props.formSchema) == null ? void 0 : _a.fieldExtendJson) {
        const extendJson = JSON.parse(props.formSchema.fieldExtendJson);
        return extendJson == null ? void 0 : extendJson.multiSelect;
      }
      return false;
    });
    const imageField = common_vendor.computed(() => {
      var _a;
      if ((_a = props.formSchema) == null ? void 0 : _a.fieldExtendJson) {
        const extendJson = JSON.parse(props.formSchema.fieldExtendJson);
        return extendJson == null ? void 0 : extendJson.imageField;
      }
      return "";
    });
    const firstLoad = common_vendor.ref(true);
    common_vendor.watch(
      () => props.value,
      (val) => {
        val && loadValue();
      },
      { immediate: true }
    );
    function loadValue() {
      var _a, _b;
      console.log("关联记录loadValue", firstLoad.value);
      if (!firstLoad.value) {
        return;
      }
      let linkTableSelectFields = dictCode.value + "," + dictText.value;
      let superQueryParams = [{ "field": "id", "rule": "in", "val": props.value }];
      let param = {
        linkTableSelectFields,
        superQueryMatchType: "and",
        superQueryParams: encodeURI(JSON.stringify(superQueryParams))
      };
      let titleField = ((_a = props.formSchema) == null ? void 0 : _a.dictText) && ((_b = props.formSchema) == null ? void 0 : _b.dictText.split(",")[0]);
      utils_http.http.get(`/online/cgform/api/getData/${dictTable.value}`, param).then((res) => {
        if (res.success) {
          let selectedList = res.result.records || [];
          let labels = [];
          let values = [];
          selectedList.forEach((item) => {
            if (item.id) {
              values.push(item.id);
              labels.push(item[titleField]);
            }
          });
          showText.value = labels.join(",");
          selectVal.value = values;
          emit("selected", selectedList, props.name);
        }
      });
      firstLoad.value = false;
    }
    function callBack(rows) {
      var _a, _b;
      let values = [];
      let labels = [];
      let titleField = ((_a = props.formSchema) == null ? void 0 : _a.dictText) && ((_b = props.formSchema) == null ? void 0 : _b.dictText.split(",")[0]);
      rows.forEach((item) => {
        if (item.id) {
          values.push(item.id);
          labels.push(item[titleField]);
        }
      });
      showText.value = labels.join(",");
      selectVal.value = values;
      emit("selected", rows, props.name);
      emit("change", values.join(","));
      emit("update:value", values.join(","));
    }
    const handleClick = () => {
      if (!attrs.disabled) {
        reportModal.show = true;
      }
    };
    const handleClose = () => {
      reportModal.show = false;
    };
    const handleChange = (data) => {
      console.log("选中的值：", data);
      callBack(data);
    };
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.o(($event) => showText.value = $event),
        b: common_vendor.p(__spreadProps(__spreadValues({
          placeholder: `请选择${_ctx.$attrs.label}`,
          type: "text",
          readonly: true,
          clearable: true
        }, _ctx.$attrs), {
          modelValue: showText.value
        })),
        c: common_vendor.o(handleClick),
        d: common_vendor.unref(reportModal).show
      }, common_vendor.unref(reportModal).show ? {
        e: common_vendor.sr(lrmRef, "17100774-1", {
          "k": "lrmRef"
        }),
        f: common_vendor.o(handleClose),
        g: common_vendor.o(handleChange),
        h: common_vendor.p({
          dictCode: common_vendor.unref(dictCode),
          dictTable: common_vendor.unref(dictTable),
          dictText: common_vendor.unref(dictText),
          multi: common_vendor.unref(multi),
          imageField: common_vendor.unref(imageField)
        })
      } : {});
    };
  }
}));
wx.createComponent(_sfc_main);
//# sourceMappingURL=online-popup-link-record.js.map
