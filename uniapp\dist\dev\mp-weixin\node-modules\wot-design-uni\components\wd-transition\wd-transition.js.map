{"version": 3, "file": "wd-transition.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-transition/wd-transition.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC10cmFuc2l0aW9uL3dkLXRyYW5zaXRpb24udnVl"], "sourcesContent": ["<template>\n  <view v-if=\"!lazyRender || inited\" :class=\"rootClass\" :style=\"style\" @transitionend=\"onTransitionEnd\" @click=\"handleClick\">\n    <slot />\n  </view>\n</template>\n\n<script lang=\"ts\">\nexport default {\n  name: 'wd-transition',\n  options: {\n    addGlobalClass: true,\n    virtualHost: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport { computed, onBeforeMount, ref, watch } from 'vue'\nimport { isObj, isPromise, pause } from '../common/util'\nimport { transitionProps, type TransitionName } from './types'\nimport { AbortablePromise } from '../common/AbortablePromise'\n\nconst getClassNames = (name?: TransitionName | TransitionName[]) => {\n  let enter: string = `${props.enterClass} ${props.enterActiveClass}`\n  let enterTo: string = `${props.enterToClass} ${props.enterActiveClass}`\n  let leave: string = `${props.leaveClass} ${props.leaveActiveClass}`\n  let leaveTo: string = `${props.leaveToClass} ${props.leaveActiveClass}`\n\n  if (Array.isArray(name)) {\n    for (let index = 0; index < name.length; index++) {\n      enter = `wd-${name[index]}-enter wd-${name[index]}-enter-active ${enter}`\n      enterTo = `wd-${name[index]}-enter-to wd-${name[index]}-enter-active ${enterTo}`\n      leave = `wd-${name[index]}-leave wd-${name[index]}-leave-active ${leave}`\n      leaveTo = `wd-${name[index]}-leave-to wd-${name[index]}-leave-active ${leaveTo}`\n    }\n  } else if (name) {\n    enter = `wd-${name}-enter wd-${name}-enter-active ${enter}`\n    enterTo = `wd-${name}-enter-to wd-${name}-enter-active ${enterTo}`\n    leave = `wd-${name}-leave wd-${name}-leave-active ${leave}`\n    leaveTo = `wd-${name}-leave-to wd-${name}-leave-active ${leaveTo}`\n  }\n  return {\n    enter: enter,\n    'enter-to': enterTo,\n    leave: leave,\n    'leave-to': leaveTo\n  }\n}\n\nconst props = defineProps(transitionProps)\nconst emit = defineEmits(['click', 'before-enter', 'enter', 'before-leave', 'leave', 'after-leave', 'after-enter'])\n\n// 初始化是否完成\nconst inited = ref<boolean>(false)\n// 是否显示\nconst display = ref<boolean>(false)\n// 当前动画状态\nconst status = ref<string>('')\n// 动画是否结束\nconst transitionEnded = ref<boolean>(false)\n// 动画持续时间\nconst currentDuration = ref<number>(300)\n// 类名\nconst classes = ref<string>('')\n// 用于控制enter和leave的顺序执行\nconst enterPromise = ref<AbortablePromise<void> | null>(null)\n\n// 动画进入的生命周期\nconst enterLifeCyclePromises = ref<AbortablePromise<unknown> | null>(null)\n\n// 动画离开的生命周期\nconst leaveLifeCyclePromises = ref<AbortablePromise<unknown> | null>(null)\n\nconst style = computed(() => {\n  return `-webkit-transition-duration:${currentDuration.value}ms;transition-duration:${currentDuration.value}ms;${\n    display.value || !props.destroy ? '' : 'display: none;'\n  }${props.customStyle}`\n})\n\nconst rootClass = computed(() => {\n  return `wd-transition ${props.customClass}  ${classes.value}`\n})\n\nonBeforeMount(() => {\n  if (props.show) {\n    enter()\n  }\n})\n\nwatch(\n  () => props.show,\n  (newVal) => {\n    handleShow(newVal)\n  },\n  { deep: true }\n)\n\nfunction handleClick() {\n  emit('click')\n}\n\nfunction handleShow(value: boolean) {\n  if (value) {\n    handleAbortPromise()\n    enter()\n  } else {\n    leave()\n  }\n}\n/**\n * 取消所有的promise\n */\nfunction handleAbortPromise() {\n  isPromise(enterPromise.value) && enterPromise.value.abort()\n  isPromise(enterLifeCyclePromises.value) && enterLifeCyclePromises.value.abort()\n  isPromise(leaveLifeCyclePromises.value) && leaveLifeCyclePromises.value.abort()\n  enterPromise.value = null\n  enterLifeCyclePromises.value = null\n  leaveLifeCyclePromises.value = null\n}\n\nfunction enter() {\n  enterPromise.value = new AbortablePromise(async (resolve) => {\n    try {\n      const classNames = getClassNames(props.name)\n      const duration = isObj(props.duration) ? (props.duration as any).enter : props.duration\n      status.value = 'enter'\n      emit('before-enter')\n      enterLifeCyclePromises.value = pause()\n      await enterLifeCyclePromises.value\n      emit('enter')\n      classes.value = classNames.enter\n      currentDuration.value = duration\n      enterLifeCyclePromises.value = pause()\n      await enterLifeCyclePromises.value\n      inited.value = true\n      display.value = true\n      enterLifeCyclePromises.value = pause()\n      await enterLifeCyclePromises.value\n      enterLifeCyclePromises.value = null\n      transitionEnded.value = false\n      classes.value = classNames['enter-to']\n      resolve()\n    } catch (error) {\n      /**\n       *\n       */\n    }\n  })\n}\nasync function leave() {\n  if (!enterPromise.value) {\n    transitionEnded.value = false\n    return onTransitionEnd()\n  }\n  try {\n    await enterPromise.value\n    if (!display.value) return\n    const classNames = getClassNames(props.name)\n    const duration = isObj(props.duration) ? (props.duration as any).leave : props.duration\n    status.value = 'leave'\n    emit('before-leave')\n    currentDuration.value = duration\n    leaveLifeCyclePromises.value = pause()\n    await leaveLifeCyclePromises.value\n    emit('leave')\n    classes.value = classNames.leave\n    leaveLifeCyclePromises.value = pause()\n    await leaveLifeCyclePromises.value\n    transitionEnded.value = false\n    classes.value = classNames['leave-to']\n    leaveLifeCyclePromises.value = setPromise(currentDuration.value)\n    await leaveLifeCyclePromises.value\n    leaveLifeCyclePromises.value = null\n    onTransitionEnd()\n    enterPromise.value = null\n  } catch (error) {\n    /**\n     *\n     */\n  }\n}\n\n/**\n * 定时器promise化\n * @param duration 持续时间ms\n */\nfunction setPromise(duration: number) {\n  return new AbortablePromise<void>((resolve) => {\n    const timer = setTimeout(() => {\n      clearTimeout(timer)\n      resolve()\n    }, duration)\n  })\n}\nfunction onTransitionEnd() {\n  if (transitionEnded.value) return\n\n  transitionEnded.value = true\n  if (status.value === 'leave') {\n    // 离开后触发\n    emit('after-leave')\n  } else if (status.value === 'enter') {\n    // 进入后触发\n    emit('after-enter')\n  }\n\n  if (!props.show && display.value) {\n    display.value = false\n  }\n}\n</script>\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-transition/wd-transition.vue'\nwx.createComponent(Component)"], "names": ["enter", "leave", "ref", "computed", "onBeforeMount", "watch", "isPromise", "AbortablePromise", "isObj", "pause"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,gBAAgB;AAAA,EAAA;AAEpB;;;;;AASM,UAAA,gBAAgB,CAAC,SAA6C;AAClE,UAAIA,SAAgB,GAAG,MAAM,UAAU,IAAI,MAAM,gBAAgB;AACjE,UAAI,UAAkB,GAAG,MAAM,YAAY,IAAI,MAAM,gBAAgB;AACrE,UAAIC,SAAgB,GAAG,MAAM,UAAU,IAAI,MAAM,gBAAgB;AACjE,UAAI,UAAkB,GAAG,MAAM,YAAY,IAAI,MAAM,gBAAgB;AAEjE,UAAA,MAAM,QAAQ,IAAI,GAAG;AACvB,iBAAS,QAAQ,GAAG,QAAQ,KAAK,QAAQ,SAAS;AAChDD,mBAAQ,MAAM,KAAK,KAAK,CAAC,aAAa,KAAK,KAAK,CAAC,iBAAiBA,MAAK;AAC7D,oBAAA,MAAM,KAAK,KAAK,CAAC,gBAAgB,KAAK,KAAK,CAAC,iBAAiB,OAAO;AAC9EC,mBAAQ,MAAM,KAAK,KAAK,CAAC,aAAa,KAAK,KAAK,CAAC,iBAAiBA,MAAK;AAC7D,oBAAA,MAAM,KAAK,KAAK,CAAC,gBAAgB,KAAK,KAAK,CAAC,iBAAiB,OAAO;AAAA,QAAA;AAAA,iBAEvE,MAAM;AACfD,iBAAQ,MAAM,IAAI,aAAa,IAAI,iBAAiBA,MAAK;AACzD,kBAAU,MAAM,IAAI,gBAAgB,IAAI,iBAAiB,OAAO;AAChEC,iBAAQ,MAAM,IAAI,aAAa,IAAI,iBAAiBA,MAAK;AACzD,kBAAU,MAAM,IAAI,gBAAgB,IAAI,iBAAiB,OAAO;AAAA,MAAA;AAE3D,aAAA;AAAA,QACL,OAAOD;AAAAA,QACP,YAAY;AAAA,QACZ,OAAOC;AAAAA,QACP,YAAY;AAAA,MACd;AAAA,IACF;AAEA,UAAM,QAAQ;AACd,UAAM,OAAO;AAGP,UAAA,SAASC,kBAAa,KAAK;AAE3B,UAAA,UAAUA,kBAAa,KAAK;AAE5B,UAAA,SAASA,kBAAY,EAAE;AAEvB,UAAA,kBAAkBA,kBAAa,KAAK;AAEpC,UAAA,kBAAkBA,kBAAY,GAAG;AAEjC,UAAA,UAAUA,kBAAY,EAAE;AAExB,UAAA,eAAeA,kBAAmC,IAAI;AAGtD,UAAA,yBAAyBA,kBAAsC,IAAI;AAGnE,UAAA,yBAAyBA,kBAAsC,IAAI;AAEnE,UAAA,QAAQC,cAAAA,SAAS,MAAM;AAC3B,aAAO,+BAA+B,gBAAgB,KAAK,0BAA0B,gBAAgB,KAAK,MACxG,QAAQ,SAAS,CAAC,MAAM,UAAU,KAAK,gBACzC,GAAG,MAAM,WAAW;AAAA,IAAA,CACrB;AAEK,UAAA,YAAYA,cAAAA,SAAS,MAAM;AAC/B,aAAO,iBAAiB,MAAM,WAAW,KAAK,QAAQ,KAAK;AAAA,IAAA,CAC5D;AAEDC,kBAAAA,cAAc,MAAM;AAClB,UAAI,MAAM,MAAM;AACR,cAAA;AAAA,MAAA;AAAA,IACR,CACD;AAEDC,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,WAAW;AACV,mBAAW,MAAM;AAAA,MACnB;AAAA,MACA,EAAE,MAAM,KAAK;AAAA,IACf;AAEA,aAAS,cAAc;AACrB,WAAK,OAAO;AAAA,IAAA;AAGd,aAAS,WAAW,OAAgB;AAClC,UAAI,OAAO;AACU,2BAAA;AACb,cAAA;AAAA,MAAA,OACD;AACC,cAAA;AAAA,MAAA;AAAA,IACR;AAKF,aAAS,qBAAqB;AAC5BC,oBAAA,UAAU,aAAa,KAAK,KAAK,aAAa,MAAM,MAAM;AAC1DA,oBAAA,UAAU,uBAAuB,KAAK,KAAK,uBAAuB,MAAM,MAAM;AAC9EA,oBAAA,UAAU,uBAAuB,KAAK,KAAK,uBAAuB,MAAM,MAAM;AAC9E,mBAAa,QAAQ;AACrB,6BAAuB,QAAQ;AAC/B,6BAAuB,QAAQ;AAAA,IAAA;AAGjC,aAAS,QAAQ;AACf,mBAAa,QAAQ,IAAIC,cAAiB,iBAAA,CAAO,YAAY;AACvD,YAAA;AACI,gBAAA,aAAa,cAAc,MAAM,IAAI;AACrC,gBAAA,WAAWC,oBAAM,MAAM,QAAQ,IAAK,MAAM,SAAiB,QAAQ,MAAM;AAC/E,iBAAO,QAAQ;AACf,eAAK,cAAc;AACnB,iCAAuB,QAAQC,oBAAM;AACrC,gBAAM,uBAAuB;AAC7B,eAAK,OAAO;AACZ,kBAAQ,QAAQ,WAAW;AAC3B,0BAAgB,QAAQ;AACxB,iCAAuB,QAAQA,oBAAM;AACrC,gBAAM,uBAAuB;AAC7B,iBAAO,QAAQ;AACf,kBAAQ,QAAQ;AAChB,iCAAuB,QAAQA,oBAAM;AACrC,gBAAM,uBAAuB;AAC7B,iCAAuB,QAAQ;AAC/B,0BAAgB,QAAQ;AAChB,kBAAA,QAAQ,WAAW,UAAU;AAC7B,kBAAA;AAAA,iBACD,OAAO;AAAA,QAAA;AAAA,MAIhB,EACD;AAAA,IAAA;AAEH,aAAe,QAAQ;AAAA;AACjB,YAAA,CAAC,aAAa,OAAO;AACvB,0BAAgB,QAAQ;AACxB,iBAAO,gBAAgB;AAAA,QAAA;AAErB,YAAA;AACF,gBAAM,aAAa;AACnB,cAAI,CAAC,QAAQ;AAAO;AACd,gBAAA,aAAa,cAAc,MAAM,IAAI;AACrC,gBAAA,WAAWD,oBAAM,MAAM,QAAQ,IAAK,MAAM,SAAiB,QAAQ,MAAM;AAC/E,iBAAO,QAAQ;AACf,eAAK,cAAc;AACnB,0BAAgB,QAAQ;AACxB,iCAAuB,QAAQC,oBAAM;AACrC,gBAAM,uBAAuB;AAC7B,eAAK,OAAO;AACZ,kBAAQ,QAAQ,WAAW;AAC3B,iCAAuB,QAAQA,oBAAM;AACrC,gBAAM,uBAAuB;AAC7B,0BAAgB,QAAQ;AAChB,kBAAA,QAAQ,WAAW,UAAU;AACd,iCAAA,QAAQ,WAAW,gBAAgB,KAAK;AAC/D,gBAAM,uBAAuB;AAC7B,iCAAuB,QAAQ;AACf,0BAAA;AAChB,uBAAa,QAAQ;AAAA,iBACd,OAAO;AAAA,QAAA;AAAA,MAIhB;AAAA;AAOF,aAAS,WAAW,UAAkB;AAC7B,aAAA,IAAIF,cAAAA,iBAAuB,CAAC,YAAY;AACvC,cAAA,QAAQ,WAAW,MAAM;AAC7B,uBAAa,KAAK;AACV,kBAAA;AAAA,WACP,QAAQ;AAAA,MAAA,CACZ;AAAA,IAAA;AAEH,aAAS,kBAAkB;AACzB,UAAI,gBAAgB;AAAO;AAE3B,sBAAgB,QAAQ;AACpB,UAAA,OAAO,UAAU,SAAS;AAE5B,aAAK,aAAa;AAAA,MAAA,WACT,OAAO,UAAU,SAAS;AAEnC,aAAK,aAAa;AAAA,MAAA;AAGpB,UAAI,CAAC,MAAM,QAAQ,QAAQ,OAAO;AAChC,gBAAQ,QAAQ;AAAA,MAAA;AAAA,IAClB;;;;;;;;;;;;;;ACjNF,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}