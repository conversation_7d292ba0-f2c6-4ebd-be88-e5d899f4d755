"use strict";
const common_vendor = require("../common/vendor.js");
if (!Array) {
  const _easycom_wd_config_provider2 = common_vendor.resolveComponent("wd-config-provider");
  _easycom_wd_config_provider2();
}
const _easycom_wd_config_provider = () => "../node-modules/wot-design-uni/components/wd-config-provider/wd-config-provider.js";
if (!Math) {
  _easycom_wd_config_provider();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "default",
  setup(__props) {
    const themeVars = {
      // colorTheme: 'red',
      // buttonPrimaryBgColor: '#07c160',
      // buttonPrimaryColor: '#07c160',
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          themeVars
        })
      };
    };
  }
});
wx.createComponent(_sfc_main);
//# sourceMappingURL=default.js.map
