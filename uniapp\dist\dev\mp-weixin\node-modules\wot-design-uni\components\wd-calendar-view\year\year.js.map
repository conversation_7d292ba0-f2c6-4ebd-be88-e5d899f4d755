{"version": 3, "file": "year.js", "sources": ["../../../../../../../../node_modules/wot-design-uni/components/wd-calendar-view/year/year.vue", "../../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1jYWxlbmRhci12aWV3L3llYXIveWVhci52dWU"], "sourcesContent": ["<template>\n  <wd-toast selector=\"wd-year\" />\n\n  <view class=\"wd-year year\">\n    <view class=\"wd-year__title\" v-if=\"showTitle\">{{ yearTitle(date) }}</view>\n    <view class=\"wd-year__months\">\n      <view\n        v-for=\"(item, index) in months\"\n        :key=\"index\"\n        :class=\"`wd-year__month ${item.disabled ? 'is-disabled' : ''} ${item.isLastRow ? 'is-last-row' : ''} ${\n          item.type ? monthTypeClass(item.type) : ''\n        }`\"\n        @click=\"handleDateClick(index)\"\n      >\n        <view class=\"wd-year__month-top\">{{ item.topInfo }}</view>\n        <view class=\"wd-year__month-text\">{{ getMonthLabel(item.date) }}</view>\n        <view class=\"wd-year__month-bottom\">{{ item.bottomInfo }}</view>\n      </view>\n    </view>\n  </view>\n</template>\n<script lang=\"ts\">\nexport default {\n  options: {\n    addGlobalClass: true,\n    virtualHost: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport wdToast from '../../wd-toast/wd-toast.vue'\nimport { computed, ref, watch } from 'vue'\nimport { deepClone, isArray, isFunction } from '../../common/util'\nimport { compareMonth, formatYearTitle, getDateByDefaultTime, getItemClass, getMonthByOffset, getMonthOffset } from '../utils'\nimport { useToast } from '../../wd-toast'\nimport { useTranslate } from '../../composables/useTranslate'\nimport { dayjs } from '../../common/dayjs'\nimport { yearProps } from './types'\nimport type { CalendarDayItem, CalendarDayType } from '../types'\n\nconst props = defineProps(yearProps)\nconst emit = defineEmits(['change'])\n\nconst toast = useToast('wd-year')\nconst { translate } = useTranslate('calendar-view')\n\nconst months = ref<CalendarDayItem[]>([])\n\nconst monthTypeClass = computed(() => {\n  return (monthType: CalendarDayType) => {\n    return getItemClass(monthType, props.value, props.type)\n  }\n})\n\nconst yearTitle = computed(() => {\n  return (date: number) => {\n    return formatYearTitle(date)\n  }\n})\n\nwatch(\n  [() => props.type, () => props.date, () => props.value, () => props.minDate, () => props.maxDate, () => props.formatter],\n  () => {\n    setMonths()\n  },\n  {\n    deep: true,\n    immediate: true\n  }\n)\n\nfunction getMonthLabel(date: number) {\n  return dayjs(date).format(translate('month', date))\n}\n\nfunction setMonths() {\n  const monthList: CalendarDayItem[] = []\n  const date = new Date(props.date)\n  const year = date.getFullYear()\n  const value = props.value\n\n  if (props.type.indexOf('range') > -1 && value && !isArray(value)) {\n    console.error('[wot-design] value should be array when type is range')\n    return\n  }\n\n  for (let month = 0; month < 12; month++) {\n    const date = new Date(year, month, 1).getTime()\n    let type: CalendarDayType = getMonthType(date)\n    if (!type && compareMonth(date, Date.now()) === 0) {\n      type = 'current'\n    }\n    const monthObj = getFormatterDate(date, month, type)\n    monthList.push(monthObj)\n  }\n\n  months.value = deepClone(monthList)\n}\nfunction getMonthType(date: number) {\n  if (props.type === 'monthrange' && isArray(props.value)) {\n    const [startDate, endDate] = props.value || []\n\n    if (startDate && compareMonth(date, startDate) === 0) {\n      if (endDate && compareMonth(startDate, endDate) === 0) {\n        return 'same'\n      }\n      return 'start'\n    } else if (endDate && compareMonth(date, endDate) === 0) {\n      return 'end'\n    } else if (startDate && endDate && compareMonth(date, startDate) === 1 && compareMonth(date, endDate) === -1) {\n      return 'middle'\n    } else {\n      return ''\n    }\n  } else {\n    if (props.value && compareMonth(date, props.value as number) === 0) {\n      return 'selected'\n    } else {\n      return ''\n    }\n  }\n}\nfunction handleDateClick(index: number) {\n  const date = months.value[index]\n\n  if (date.disabled) return\n\n  switch (props.type) {\n    case 'month':\n      handleMonthChange(date)\n      break\n    case 'monthrange':\n      handleMonthRangeChange(date)\n      break\n    default:\n      handleMonthChange(date)\n  }\n}\nfunction getDate(date: number) {\n  return props.defaultTime && props.defaultTime.length > 0 ? getDateByDefaultTime(date, props.defaultTime[0]) : date\n}\nfunction handleMonthChange(date: CalendarDayItem) {\n  if (date.type !== 'selected') {\n    emit('change', {\n      value: getDate(date.date)\n    })\n  }\n}\nfunction handleMonthRangeChange(date: CalendarDayItem) {\n  let value: (number | null)[] = []\n  const [startDate, endDate] = isArray(props.value) ? props.value || [] : []\n  const compare = compareMonth(date.date, startDate!)\n\n  // 禁止选择同个日期\n  if (!props.allowSameDay && !endDate && compare === 0) return\n\n  if (startDate && !endDate && compare > -1) {\n    if (props.maxRange && getMonthOffset(date.date, startDate) > props.maxRange) {\n      const maxEndDate = getMonthByOffset(startDate, props.maxRange - 1)\n      value = [startDate, getDate(maxEndDate)]\n      toast.show({\n        msg: props.rangePrompt || translate('rangePromptMonth', props.maxRange)\n      })\n    } else {\n      value = [startDate, getDate(date.date)]\n    }\n  } else {\n    value = [getDate(date.date), null]\n  }\n  emit('change', {\n    value\n  })\n}\n\nfunction getFormatterDate(date: number, month: number, type?: CalendarDayType) {\n  let monthObj: CalendarDayItem = {\n    date: date,\n    text: month + 1,\n    topInfo: '',\n    bottomInfo: '',\n    type,\n    disabled: compareMonth(date, props.minDate) === -1 || compareMonth(date, props.maxDate) === 1,\n    isLastRow: month >= 8\n  }\n\n  if (props.formatter) {\n    if (isFunction(props.formatter)) {\n      monthObj = props.formatter(monthObj)\n    } else {\n      console.error('[wot-design] error(wd-calendar-view): the formatter prop of wd-calendar-view should be a function')\n    }\n  }\n\n  return monthObj\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-calendar-view/year/year.vue'\nwx.createComponent(Component)"], "names": ["useToast", "useTranslate", "ref", "computed", "getItemClass", "formatYearTitle", "watch", "dayjs", "isArray", "date", "compareMonth", "deepClone", "getDateByDefaultTime", "getMonthOffset", "getMonthByOffset", "isFunction"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAgCA,MAAA,UAAoB,MAAA;AAVpB,MAAe,cAAA;AAAA,EACb,SAAS;AAAA,IACP,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,gBAAgB;AAAA,EAAA;AAEpB;;;;;;AAcA,UAAM,QAAQ;AACd,UAAM,OAAO;AAEP,UAAA,QAAQA,uBAAS,SAAS;AAChC,UAAM,EAAE,UAAA,IAAcC,cAAA,aAAa,eAAe;AAE5C,UAAA,SAASC,cAAuB,IAAA,EAAE;AAElC,UAAA,iBAAiBC,cAAAA,SAAS,MAAM;AACpC,aAAO,CAAC,cAA+B;AACrC,eAAOC,cAAAA,aAAa,WAAW,MAAM,OAAO,MAAM,IAAI;AAAA,MACxD;AAAA,IAAA,CACD;AAEK,UAAA,YAAYD,cAAAA,SAAS,MAAM;AAC/B,aAAO,CAAC,SAAiB;AACvB,eAAOE,cAAAA,gBAAgB,IAAI;AAAA,MAC7B;AAAA,IAAA,CACD;AAEDC,kBAAA;AAAA,MACE,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,SAAS,MAAM,MAAM,SAAS,MAAM,MAAM,SAAS;AAAA,MACvH,MAAM;AACM,kBAAA;AAAA,MACZ;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,WAAW;AAAA,MAAA;AAAA,IAEf;AAEA,aAAS,cAAc,MAAc;AACnC,aAAOC,cAAAA,QAAM,IAAI,EAAE,OAAO,UAAU,SAAS,IAAI,CAAC;AAAA,IAAA;AAGpD,aAAS,YAAY;AACnB,YAAM,YAA+B,CAAC;AACtC,YAAM,OAAO,IAAI,KAAK,MAAM,IAAI;AAC1B,YAAA,OAAO,KAAK,YAAY;AAC9B,YAAM,QAAQ,MAAM;AAEhB,UAAA,MAAM,KAAK,QAAQ,OAAO,IAAI,MAAM,SAAS,CAACC,sBAAQ,KAAK,GAAG;AAChE,gBAAQ,MAAM,uDAAuD;AACrE;AAAA,MAAA;AAGF,eAAS,QAAQ,GAAG,QAAQ,IAAI,SAAS;AACvC,cAAMC,QAAO,IAAI,KAAK,MAAM,OAAO,CAAC,EAAE,QAAQ;AAC1C,YAAA,OAAwB,aAAaA,KAAI;AACzC,YAAA,CAAC,QAAQC,2BAAaD,OAAM,KAAK,IAAI,CAAC,MAAM,GAAG;AAC1C,iBAAA;AAAA,QAAA;AAET,cAAM,WAAW,iBAAiBA,OAAM,OAAO,IAAI;AACnD,kBAAU,KAAK,QAAQ;AAAA,MAAA;AAGlB,aAAA,QAAQE,wBAAU,SAAS;AAAA,IAAA;AAEpC,aAAS,aAAa,MAAc;AAClC,UAAI,MAAM,SAAS,gBAAgBH,cAAAA,QAAQ,MAAM,KAAK,GAAG;AACvD,cAAM,CAAC,WAAW,OAAO,IAAI,MAAM,SAAS,CAAC;AAE7C,YAAI,aAAaE,cAAA,aAAa,MAAM,SAAS,MAAM,GAAG;AACpD,cAAI,WAAWA,cAAA,aAAa,WAAW,OAAO,MAAM,GAAG;AAC9C,mBAAA;AAAA,UAAA;AAEF,iBAAA;AAAA,QAAA,WACE,WAAWA,cAAA,aAAa,MAAM,OAAO,MAAM,GAAG;AAChD,iBAAA;AAAA,QACE,WAAA,aAAa,WAAWA,cAAAA,aAAa,MAAM,SAAS,MAAM,KAAKA,cAAAA,aAAa,MAAM,OAAO,MAAM,IAAI;AACrG,iBAAA;AAAA,QAAA,OACF;AACE,iBAAA;AAAA,QAAA;AAAA,MACT,OACK;AACL,YAAI,MAAM,SAASA,cAAA,aAAa,MAAM,MAAM,KAAe,MAAM,GAAG;AAC3D,iBAAA;AAAA,QAAA,OACF;AACE,iBAAA;AAAA,QAAA;AAAA,MACT;AAAA,IACF;AAEF,aAAS,gBAAgB,OAAe;AAChC,YAAA,OAAO,OAAO,MAAM,KAAK;AAE/B,UAAI,KAAK;AAAU;AAEnB,cAAQ,MAAM,MAAM;AAAA,QAClB,KAAK;AACH,4BAAkB,IAAI;AACtB;AAAA,QACF,KAAK;AACH,iCAAuB,IAAI;AAC3B;AAAA,QACF;AACE,4BAAkB,IAAI;AAAA,MAAA;AAAA,IAC1B;AAEF,aAAS,QAAQ,MAAc;AAC7B,aAAO,MAAM,eAAe,MAAM,YAAY,SAAS,IAAIE,cAAqB,qBAAA,MAAM,MAAM,YAAY,CAAC,CAAC,IAAI;AAAA,IAAA;AAEhH,aAAS,kBAAkB,MAAuB;AAC5C,UAAA,KAAK,SAAS,YAAY;AAC5B,aAAK,UAAU;AAAA,UACb,OAAO,QAAQ,KAAK,IAAI;AAAA,QAAA,CACzB;AAAA,MAAA;AAAA,IACH;AAEF,aAAS,uBAAuB,MAAuB;AACrD,UAAI,QAA2B,CAAC;AAChC,YAAM,CAAC,WAAW,OAAO,IAAIJ,sBAAQ,MAAM,KAAK,IAAI,MAAM,SAAS,CAAA,IAAK,CAAC;AACzE,YAAM,UAAUE,cAAA,aAAa,KAAK,MAAM,SAAU;AAGlD,UAAI,CAAC,MAAM,gBAAgB,CAAC,WAAW,YAAY;AAAG;AAEtD,UAAI,aAAa,CAAC,WAAW,UAAU,IAAI;AACrC,YAAA,MAAM,YAAYG,6BAAe,KAAK,MAAM,SAAS,IAAI,MAAM,UAAU;AAC3E,gBAAM,aAAaC,cAAAA,iBAAiB,WAAW,MAAM,WAAW,CAAC;AACjE,kBAAQ,CAAC,WAAW,QAAQ,UAAU,CAAC;AACvC,gBAAM,KAAK;AAAA,YACT,KAAK,MAAM,eAAe,UAAU,oBAAoB,MAAM,QAAQ;AAAA,UAAA,CACvE;AAAA,QAAA,OACI;AACL,kBAAQ,CAAC,WAAW,QAAQ,KAAK,IAAI,CAAC;AAAA,QAAA;AAAA,MACxC,OACK;AACL,gBAAQ,CAAC,QAAQ,KAAK,IAAI,GAAG,IAAI;AAAA,MAAA;AAEnC,WAAK,UAAU;AAAA,QACb;AAAA,MAAA,CACD;AAAA,IAAA;AAGM,aAAA,iBAAiB,MAAc,OAAe,MAAwB;AAC7E,UAAI,WAA4B;AAAA,QAC9B;AAAA,QACA,MAAM,QAAQ;AAAA,QACd,SAAS;AAAA,QACT,YAAY;AAAA,QACZ;AAAA,QACA,UAAUJ,cAAa,aAAA,MAAM,MAAM,OAAO,MAAM,MAAMA,cAAAA,aAAa,MAAM,MAAM,OAAO,MAAM;AAAA,QAC5F,WAAW,SAAS;AAAA,MACtB;AAEA,UAAI,MAAM,WAAW;AACf,YAAAK,cAAA,WAAW,MAAM,SAAS,GAAG;AACpB,qBAAA,MAAM,UAAU,QAAQ;AAAA,QAAA,OAC9B;AACL,kBAAQ,MAAM,mGAAmG;AAAA,QAAA;AAAA,MACnH;AAGK,aAAA;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;AClMT,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}