{"version": 3, "file": "ImgPreview.js", "sources": ["../../../../../src/components/ImgPreview/ImgPreview.vue", "../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvY29tcG9uZW50cy9JbWdQcmV2aWV3L0ltZ1ByZXZpZXcudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"previewImage\" @tap=\"close\">\r\n    <view class=\"page\" v-if=\"urls.length > 0\">\r\n      <text class=\"text\">{{ current + 1 }} / {{ urls.length }}</text>\r\n    </view>\r\n    <swiper\r\n      class=\"swiper\"\r\n      :current=\"current\"\r\n      @change=\"swiperChange\"\r\n      @touchstart=\"handleTouchStart\"\r\n      @touchend=\"handleTouchEnd\"\r\n    >\r\n      <swiper-item class=\"swiperItem\" v-for=\"(item, index) in urls\" :key=\"index\">\r\n        <movable-area class=\"movable-area\" scale-area>\r\n          <movable-view\r\n            class=\"movable-view\"\r\n            direction=\"all\"\r\n            :inertia=\"true\"\r\n            damping=\"100\"\r\n            scale=\"true\"\r\n            scale-min=\"1\"\r\n            scale-max=\"4\"\r\n            :scale-value=\"scale\"\r\n          >\r\n            <scroll-view scroll-y=\"true\" class=\"uni-scroll-view\">\r\n              <view class=\"scroll-view\">\r\n                <image\r\n                  :key=\"index\"\r\n                  class=\"image\"\r\n                  :src=\"item\"\r\n                  mode=\"widthFix\"\r\n                  @longpress=\"onLongpress(item)\"\r\n                />\r\n              </view>\r\n            </scroll-view>\r\n          </movable-view>\r\n        </movable-area>\r\n      </swiper-item>\r\n    </swiper>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  props: {\r\n    urls: {\r\n      type: Array,\r\n      required: true,\r\n      default: () => {\r\n        return []\r\n      },\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      show: false,\r\n      current: 0, //当前页\r\n      scale: 1,\r\n      isZooming: false, // 是否处于缩放状态\r\n    }\r\n  },\r\n  methods: {\r\n    open(current) {\r\n      this.current = this.urls.findIndex((item) => item === current)\r\n    },\r\n    //关闭\r\n    close() {\r\n      if (!this.isZooming) {\r\n        this.show = false\r\n        this.current = 0\r\n        this.$emit('close')\r\n      }\r\n    },\r\n    //图片改变\r\n    swiperChange(e) {\r\n      this.current = e.detail.current\r\n    },\r\n    //监听长按\r\n    onLongpress(e) {\r\n      this.$emit('onLongpress', e)\r\n    },\r\n    handleTouchStart() {\r\n      this.isZooming = true\r\n    },\r\n    handleTouchEnd() {\r\n      this.isZooming = false\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.previewImage {\r\n  z-index: 9999;\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: #000000;\r\n  .swiper {\r\n    width: 100%;\r\n    height: 100vh;\r\n    .swiperItem {\r\n      .movable-area {\r\n        height: 100%;\r\n        width: 100%;\r\n        .movable-view {\r\n          width: 100%;\r\n          min-height: 100%;\r\n          .uni-scroll-view {\r\n            height: 100vh;\r\n          }\r\n          .scroll-view {\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            min-height: 100vh;\r\n            .image {\r\n              width: 100%;\r\n              height: auto;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .page {\r\n    position: absolute;\r\n    z-index: 9999;\r\n    width: 100%;\r\n    top: 60rpx;\r\n    text-align: center;\r\n    .text {\r\n      color: #fff;\r\n      font-size: 32rpx;\r\n      background-color: rgba(0, 0, 0, 0.5);\r\n      padding: 3rpx 16rpx;\r\n      border-radius: 20rpx;\r\n      user-select: none;\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/components/ImgPreview/ImgPreview.vue'\nwx.createComponent(Component)"], "names": [], "mappings": ";;AA2CA,MAAK,YAAU;AAAA,EACb,OAAO;AAAA,IACL,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,UAAU;AAAA,MACV,SAAS,MAAM;AACb,eAAO,CAAC;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA;AAAA,MACT,OAAO;AAAA,MACP,WAAW;AAAA;AAAA,IACb;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,KAAK,SAAS;AACZ,WAAK,UAAU,KAAK,KAAK,UAAU,CAAC,SAAS,SAAS,OAAO;AAAA,IAC9D;AAAA;AAAA,IAED,QAAQ;AACN,UAAI,CAAC,KAAK,WAAW;AACnB,aAAK,OAAO;AACZ,aAAK,UAAU;AACf,aAAK,MAAM,OAAO;AAAA,MACpB;AAAA,IACD;AAAA;AAAA,IAED,aAAa,GAAG;AACd,WAAK,UAAU,EAAE,OAAO;AAAA,IACzB;AAAA;AAAA,IAED,YAAY,GAAG;AACb,WAAK,MAAM,eAAe,CAAC;AAAA,IAC5B;AAAA,IACD,mBAAmB;AACjB,WAAK,YAAY;AAAA,IAClB;AAAA,IACD,iBAAiB;AACf,WAAK,YAAY;AAAA,IAClB;AAAA,EACF;AACH;;;;;;;;;;;;;;;;;;;;;;;;;ACvFA,GAAG,gBAAgB,SAAS;"}