"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
const store_user = require("../../store/user.js");
if (!Array) {
  const _component_NavBar = common_vendor.resolveComponent("NavBar");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_component_NavBar + _easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  _easycom_PageLayout();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "monitorForm"
}), {
  __name: "form",
  setup(__props) {
    const userStore = store_user.useUserStore();
    const query = common_vendor.ref({});
    common_vendor.onMounted(() => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      query.value = currentPage.options || {};
      if (query.value.mode === "view" && query.value.id) {
        loadFormData(query.value.id);
      }
      if (userStore.userInfo.realname) {
        formData.value.userName = userStore.userInfo.realname;
      }
    });
    const mode = common_vendor.computed(() => query.value.mode || "add");
    const formData = common_vendor.ref({
      userName: "",
      location: "",
      weight: "",
      bloodPressure: "",
      heartRate: "",
      smock: "",
      medicineOnTime: "",
      missingMedicine: "",
      reduceMedicine: "",
      discomfort: {
        comfort: false,
        fatigue: false,
        syncope: false,
        dyspnea: false,
        anginaPectoris: false,
        else: false
      },
      selfCareStatus: "",
      heartFunction: "",
      selfAppraisal: "",
      swelling: "",
      forcedRest: "",
      dysbasia: "",
      workWithDifficulty: "",
      goOutWithDifficulty: "",
      sleepWithDifficulty: "",
      doingWithDifficulty: "",
      incomeWithDifficulty: "",
      entertainmentWithDifficulty: "",
      sexWithDifficulty: "",
      eatLess: "",
      breathWithDifficulty: "",
      tired: "",
      inHospital: "",
      spendMoney: "",
      sideEffect: "",
      beBburden: "",
      uncontrolled: "",
      anxiety: "",
      decreasedMemory: "",
      depression: "",
      preventIllness: "",
      exercise: "",
      lowSalt: "",
      seeDoctor: "",
      doctorMedicine: "",
      lowSaltGoOut: "",
      vaccinated: "",
      visitFriends: "",
      takeMedicine: "",
      consultMedicine: "",
      weighOneself: "",
      focusOneself: "",
      checkTheSideEffects: "",
      beProneToFatigue: "",
      consultWorkers: "",
      monitorSymptoms: "",
      ankleEdema: "",
      shortnessOfBreath: "",
      checkOwnSymptoms: "",
      timelyAwareness: "",
      knowInTime: "",
      reduceSalt: "",
      reduceFluids: "",
      takeMedicine2: "",
      consultByPhone: "",
      seekHelp: "",
      tryToFind: "",
      reduceActivity: "",
      feelBetter: "",
      keepStable: "",
      followPlan: "",
      followPlanWithDifficluty: "",
      regularMonitor: "",
      monitorWithDifficulty: "",
      realizeChange: "",
      assessSymptom: "",
      relieveSymptom: "",
      relieveWithDifficulty: "",
      judgmentMethod: ""
    });
    const loadFormData = (id) => {
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      const url = `${"https://www.mograine.cn/api"}/patient/${id}/monitoring`;
      console.log("请求详情URL:", url);
      common_vendor.index.request({
        url,
        method: "GET",
        success: (res) => {
          const response = res.data;
          console.log("详情接口返回数据:", response.result.details.doingWithDifficulty);
          if (response && response.code === 200) {
            if (response.result) {
              formData.value = {
                bloodPressure: response.result.details.bloodPressure || "",
                heartRate: response.result.details.heartRate || "",
                weight: response.result.details.weight || "",
                userName: response.result.userName || "",
                location: response.result.details.location || "",
                smock: response.result.details.smock || "",
                medicineOnTime: response.result.details.medicineOnTime || "",
                missingMedicine: response.result.details.missingMedicine || "",
                reduceMedicine: response.result.details.reduceMedicine || "",
                discomfort: response.result.details.discomfort || "",
                selfCareStatus: response.result.details.selfCareStatus || "",
                heartFunction: response.result.details.heartFunction || "",
                selfAppraisal: response.result.details.selfAppraisal || "",
                swelling: response.result.details.swelling || "",
                forcedRest: response.result.details.forcedRest || "",
                dysbasia: response.result.details.dysbasia || "",
                workWithDifficulty: response.result.details.workWithDifficulty || "",
                goOutWithDifficulty: response.result.details.goOutWithDifficulty || "",
                sleepWithDifficulty: response.result.details.sleepWithDifficulty || "",
                doingWithDifficulty: response.result.details.doingWithDifficulty || "",
                incomeWithDifficulty: response.result.details.incomeWithDifficulty || "",
                entertainmentWithDifficulty: response.result.details.entertainmentWithDifficulty || "",
                sexWithDifficulty: response.result.details.sexWithDifficulty || "",
                eatLess: response.result.details.eatLess || "",
                breathWithDifficulty: response.result.details.breathWithDifficulty || "",
                tired: response.result.details.tired || "",
                inHospital: response.result.details.inHospital || "",
                spendMoney: response.result.details.spendMoney || "",
                sideEffect: response.result.details.sideEffect || "",
                beBburden: response.result.details.beBburden || "",
                uncontrolled: response.result.details.uncontrolled || "",
                anxiety: response.result.details.anxiety || "",
                decreasedMemory: response.result.details.decreasedMemory || "",
                depression: response.result.details.depression || "",
                preventIllness: response.result.details.preventIllness || "",
                exercise: response.result.details.exercise || "",
                lowSalt: response.result.details.lowSalt || "",
                seeDoctor: response.result.details.seeDoctor || "",
                doctorMedicine: response.result.details.doctorMedicine || "",
                lowSaltGoOut: response.result.details.lowSaltGoOut || "",
                vaccinated: response.result.details.vaccinated || "",
                visitFriends: response.result.details.visitFriends || "",
                takeMedicine: response.result.details.takeMedicine || "",
                consultMedicine: response.result.details.consultMedicine || "",
                weighOneself: response.result.details.weighOneself || "",
                focusOneself: response.result.details.focusOneself || "",
                checkTheSideEffects: response.result.details.checkTheSideEffects || "",
                beProneToFatigue: response.result.details.beProneToFatigue || "",
                consultWorkers: response.result.details.consultWorkers || "",
                monitorSymptoms: response.result.details.monitorSymptoms || "",
                ankleEdema: response.result.details.ankleEdema || "",
                shortnessOfBreath: response.result.details.shortnessOfBreath || "",
                checkOwnSymptoms: response.result.details.checkOwnSymptoms || "",
                timelyAwareness: response.result.details.timelyAwareness || "",
                knowInTime: response.result.details.knowInTime || "",
                reduceSalt: response.result.details.reduceSalt || "",
                reduceFluids: response.result.details.reduceFluids || "",
                takeMedicine2: response.result.details.takeMedicine2 || "",
                consultByPhone: response.result.details.consultByPhone || "",
                seekHelp: response.result.details.seekHelp || "",
                tryToFind: response.result.details.tryToFind || "",
                reduceActivity: response.result.details.reduceActivity || "",
                feelBetter: response.result.details.feelBetter || "",
                keepStable: response.result.details.keepStable || "",
                followPlan: response.result.details.followPlan || "",
                followPlanWithDifficluty: response.result.details.followPlanWithDifficluty || "",
                regularMonitor: response.result.details.regularMonitor || "",
                monitorWithDifficulty: response.result.details.monitorWithDifficulty || "",
                realizeChange: response.result.details.realizeChange || "",
                assessSymptom: response.result.details.assessSymptom || "",
                relieveSymptom: response.result.details.relieveSymptom || "",
                relieveWithDifficulty: response.result.details.relieveWithDifficulty || "",
                judgmentMethod: response.result.details.judgmentMethod || ""
              };
              console.log("解析后的详情数据:", formData.value);
            } else {
              console.log("没有找到详情数据");
              common_vendor.index.showToast({
                title: "未找到记录详情",
                icon: "none"
              });
            }
          } else {
            common_vendor.index.showToast({
              title: response.msg || "获取详情失败",
              icon: "none"
            });
          }
        },
        fail: (err) => {
          console.error("获取体征详情失败:", err);
          common_vendor.index.showToast({
            title: "网络异常，请稍后重试",
            icon: "none"
          });
        },
        complete: () => {
          common_vendor.index.hideLoading();
        }
      });
    };
    const toggleUncomfortable = (discomfort) => {
      if (!formData.value.discomfort) {
        formData.value.discomfort = {
          comfort: false,
          fatigue: false,
          syncope: false,
          dyspnea: false,
          anginaPectoris: false,
          else: false
        };
      }
      const newStatus = !formData.value.discomfort[discomfort];
      formData.value.discomfort = __spreadProps(__spreadValues({}, formData.value.discomfort), {
        [discomfort]: newStatus
      });
    };
    const getLocation = () => {
      common_vendor.index.chooseLocation({
        success: function(res) {
          console.log("位置名称：" + res.name);
          console.log("详细地址：" + res.address);
          console.log("纬度：" + res.latitude);
          console.log("经度：" + res.longitude);
          formData.value.location = res.address;
        },
        fail: function(err) {
          console.error("选择位置失败", err);
          common_vendor.index.showToast({
            title: "选择位置失败，请检查定位权限",
            icon: "none"
          });
        }
      });
    };
    const submitForm = () => {
      console.log("提交表单数据:", formData.value);
      if (!formData.value.location) {
        common_vendor.index.showToast({
          title: "请获取您的地理位置",
          icon: "none"
        });
        return;
      }
      if (!formData.value.weight) {
        common_vendor.index.showToast({
          title: "请输入体重",
          icon: "none"
        });
        return;
      }
      if (!formData.value.bloodPressure) {
        common_vendor.index.showToast({
          title: "请输入血压",
          icon: "none"
        });
        return;
      }
      if (!formData.value.heartRate) {
        common_vendor.index.showToast({
          title: "请输入心率",
          icon: "none"
        });
        return;
      }
      if (!formData.value.smock) {
        common_vendor.index.showToast({
          title: "请选择是否吸烟",
          icon: "none"
        });
        return;
      }
      if (!formData.value.medicineOnTime) {
        common_vendor.index.showToast({
          title: "请选择是否按时服药",
          icon: "none"
        });
        return;
      }
      if (!formData.value.missingMedicine) {
        common_vendor.index.showToast({
          title: "请选择是否漏服药",
          icon: "none"
        });
        return;
      }
      if (!formData.value.reduceMedicine) {
        common_vendor.index.showToast({
          title: "请选择是否擅自减药",
          icon: "none"
        });
        return;
      }
      if (!formData.value.discomfort || !formData.value.discomfort.comfort && !formData.value.discomfort.fatigue && !formData.value.discomfort.syncope && !formData.value.discomfort.dyspnea && !formData.value.discomfort.anginaPectoris && !formData.value.discomfort.else) {
        common_vendor.index.showToast({
          title: "请选择现在正在遭受的不适",
          icon: "none"
        });
        return;
      }
      if (!formData.value.selfCareStatus) {
        common_vendor.index.showToast({
          title: "请选择自理情况",
          icon: "none"
        });
        return;
      }
      if (!formData.value.heartFunction) {
        common_vendor.index.showToast({
          title: "请选择心功能分级",
          icon: "none"
        });
        return;
      }
      if (!formData.value.selfAppraisal) {
        common_vendor.index.showToast({
          title: "请选择自我评定",
          icon: "none"
        });
        return;
      }
      if (!formData.value.swelling) {
        common_vendor.index.showToast({
          title: "请选择是否您的踝关节或腿出现肿胀",
          icon: "none"
        });
        return;
      }
      if (!formData.value.forcedRest) {
        common_vendor.index.showToast({
          title: "请选择是否使您在白天被迫坐下或躺下休息",
          icon: "none"
        });
        return;
      }
      if (!formData.value.dysbasia) {
        common_vendor.index.showToast({
          title: "请选择是否使您在步行或上楼梯困难",
          icon: "none"
        });
        return;
      }
      if (!formData.value.workWithDifficulty) {
        common_vendor.index.showToast({
          title: "请选择是否使您在家中或院子里工作困难",
          icon: "none"
        });
        return;
      }
      if (!formData.value.goOutWithDifficulty) {
        common_vendor.index.showToast({
          title: "请选择是否使您离开家出门困难",
          icon: "none"
        });
        return;
      }
      if (!formData.value.sleepWithDifficulty) {
        common_vendor.index.showToast({
          title: "请选择是否使您晚上睡眠状况困难",
          icon: "none"
        });
        return;
      }
      if (!formData.value.doingWithDifficulty) {
        common_vendor.index.showToast({
          title: "请选择是否使您和您的朋友或家人一起做事困难",
          icon: "none"
        });
        return;
      }
      if (!formData.value.incomeWithDifficulty) {
        common_vendor.index.showToast({
          title: "请选择是否使您做获得收入的工作困难",
          icon: "none"
        });
        return;
      }
      if (!formData.value.entertainmentWithDifficulty) {
        common_vendor.index.showToast({
          title: "请选择是否使您的做娱乐、体育活动或喜好的事情困难",
          icon: "none"
        });
        return;
      }
      if (!formData.value.sexWithDifficulty) {
        common_vendor.index.showToast({
          title: "请选择是否使您的性生活困难",
          icon: "none"
        });
        return;
      }
      if (!formData.value.eatLess) {
        common_vendor.index.showToast({
          title: "请选择是否使您对您喜欢的事物也吃得很少",
          icon: "none"
        });
        return;
      }
      if (!formData.value.breathWithDifficulty) {
        common_vendor.index.showToast({
          title: "请选择是否使您有呼吸困难",
          icon: "none"
        });
        return;
      }
      if (!formData.value.tired) {
        common_vendor.index.showToast({
          title: "请选择是否使您疲劳、乏力、或没有精力",
          icon: "none"
        });
        return;
      }
      if (!formData.value.inHospital) {
        common_vendor.index.showToast({
          title: "请选择是否使您在医院住院",
          icon: "none"
        });
        return;
      }
      if (!formData.value.spendMoney) {
        common_vendor.index.showToast({
          title: "请选择是否使您因就医花钱",
          icon: "none"
        });
        return;
      }
      if (!formData.value.sideEffect) {
        common_vendor.index.showToast({
          title: "请选择是否使您因为治疗出现了副作用",
          icon: "none"
        });
        return;
      }
      if (!formData.value.beBburden) {
        common_vendor.index.showToast({
          title: "请选择是否使您觉得自己是家人或朋友的负担",
          icon: "none"
        });
        return;
      }
      if (!formData.value.uncontrolled) {
        common_vendor.index.showToast({
          title: "请选择是否使您觉得不能控制自己的生活",
          icon: "none"
        });
        return;
      }
      if (!formData.value.anxiety) {
        common_vendor.index.showToast({
          title: "请选择是否使得您焦虑",
          icon: "none"
        });
        return;
      }
      if (!formData.value.decreasedMemory) {
        common_vendor.index.showToast({
          title: "请选择是否使您不能集中注意力或记忆力下降",
          icon: "none"
        });
        return;
      }
      if (!formData.value.depression) {
        common_vendor.index.showToast({
          title: "请选择是否使您情绪低落",
          icon: "none"
        });
        return;
      }
      if (!formData.value.preventIllness) {
        common_vendor.index.showToast({
          title: "请选择是否尽量避免生病",
          icon: "none"
        });
        return;
      }
      if (!formData.value.exercise) {
        common_vendor.index.showToast({
          title: "请选择是否锻炼身体",
          icon: "none"
        });
        return;
      }
      if (!formData.value.lowSalt) {
        common_vendor.index.showToast({
          title: "请选择是否低盐饮食",
          icon: "none"
        });
        return;
      }
      if (!formData.value.seeDoctor) {
        common_vendor.index.showToast({
          title: "请选择是否定期看医生",
          icon: "none"
        });
        return;
      }
      if (!formData.value.doctorMedicine) {
        common_vendor.index.showToast({
          title: "请选择是否吃医生开的药，不漏吃少吃",
          icon: "none"
        });
        return;
      }
      if (!formData.value.lowSaltGoOut) {
        common_vendor.index.showToast({
          title: "请选择是否外出就餐选择低盐饮食",
          icon: "none"
        });
        return;
      }
      if (!formData.value.vaccinated) {
        common_vendor.index.showToast({
          title: "请选择是否确保每年接种疫苗",
          icon: "none"
        });
        return;
      }
      if (!formData.value.visitFriends) {
        common_vendor.index.showToast({
          title: "请选择是否拜访亲朋好友时要求吃低盐食品",
          icon: "none"
        });
        return;
      }
      if (!formData.value.takeMedicine) {
        common_vendor.index.showToast({
          title: "请选择是否采取措施提醒自己吃药",
          icon: "none"
        });
        return;
      }
      if (!formData.value.consultMedicine) {
        common_vendor.index.showToast({
          title: "请选择是否向医护人员咨询自己吃的药",
          icon: "none"
        });
        return;
      }
      if (!formData.value.weighOneself) {
        common_vendor.index.showToast({
          title: "请选择是否每天称体重",
          icon: "none"
        });
        return;
      }
      if (!formData.value.focusOneself) {
        common_vendor.index.showToast({
          title: "请选择是否关注自身感觉的变化",
          icon: "none"
        });
        return;
      }
      if (!formData.value.checkTheSideEffects) {
        common_vendor.index.showToast({
          title: "请选择是否查询药物的副作用",
          icon: "none"
        });
        return;
      }
      if (!formData.value.beProneToFatigue) {
        common_vendor.index.showToast({
          title: "请选择从事日常活动，关注自己是否比平常更容易疲惫",
          icon: "none"
        });
        return;
      }
      if (!formData.value.consultWorkers) {
        common_vendor.index.showToast({
          title: "请选择是否向医护人员咨询自己的身体状况",
          icon: "none"
        });
        return;
      }
      if (!formData.value.monitorSymptoms) {
        common_vendor.index.showToast({
          title: "请选择是否密切监测有没有症状出现",
          icon: "none"
        });
        return;
      }
      if (!formData.value.ankleEdema) {
        common_vendor.index.showToast({
          title: "请选择是否检查脚踝有没有水肿",
          icon: "none"
        });
        return;
      }
      if (!formData.value.shortnessOfBreath) {
        common_vendor.index.showToast({
          title: "请选择在进行洗澡、穿衣等日常活动时检查是否有气短、喘不上气的情况",
          icon: "none"
        });
        return;
      }
      if (!formData.value.checkOwnSymptoms) {
        common_vendor.index.showToast({
          title: "请选择记录自己的症状",
          icon: "none"
        });
        return;
      }
      if (!formData.value.timelyAwareness) {
        common_vendor.index.showToast({
          title: "请选择多快意识到自己出现了症状",
          icon: "none"
        });
        return;
      }
      if (!formData.value.knowInTime) {
        common_vendor.index.showToast({
          title: "请选择多快知道这个症状是由心衰引起的",
          icon: "none"
        });
        return;
      }
      if (!formData.value.reduceSalt) {
        common_vendor.index.showToast({
          title: "请选择是否进一步减少当天饮食中的盐",
          icon: "none"
        });
        return;
      }
      if (!formData.value.reduceFluids) {
        common_vendor.index.showToast({
          title: "请选择是否减少液体的摄入量",
          icon: "none"
        });
        return;
      }
      if (!formData.value.takeMedicine2) {
        common_vendor.index.showToast({
          title: "请选择是否吃药",
          icon: "none"
        });
        return;
      }
      if (!formData.value.consultByPhone) {
        common_vendor.index.showToast({
          title: "请选择是否电话咨询医护人员获取指导",
          icon: "none"
        });
        return;
      }
      if (!formData.value.seekHelp) {
        common_vendor.index.showToast({
          title: "请选择是否电话咨询寻求家人或朋友的帮助",
          icon: "none"
        });
        return;
      }
      if (!formData.value.tryToFind) {
        common_vendor.index.showToast({
          title: "请选择是否试着找出自己出现症状的原因",
          icon: "none"
        });
        return;
      }
      if (!formData.value.reduceActivity) {
        common_vendor.index.showToast({
          title: "请选择是否减少活动直到感觉好一些",
          icon: "none"
        });
        return;
      }
      if (!formData.value.feelBetter) {
        common_vendor.index.showToast({
          title: "请选择是否您采取的措施有让您感觉好一些吗",
          icon: "none"
        });
        return;
      }
      if (!formData.value.keepStable) {
        common_vendor.index.showToast({
          title: "请选择是否保持身体状况稳定且没有症状",
          icon: "none"
        });
        return;
      }
      if (!formData.value.followPlan) {
        common_vendor.index.showToast({
          title: "请选择是否遵从医护人员为您制订的治疗方案",
          icon: "none"
        });
        return;
      }
      if (!formData.value.followPlanWithDifficluty) {
        common_vendor.index.showToast({
          title: "请选择是否在有困难时依然坚持遵从治疗方案",
          icon: "none"
        });
        return;
      }
      if (!formData.value.regularMonitor) {
        common_vendor.index.showToast({
          title: "请选择是否定期监测自己的身体状况",
          icon: "none"
        });
        return;
      }
      if (!formData.value.monitorWithDifficulty) {
        common_vendor.index.showToast({
          title: "请选择是否在有困难时依然坚持监测自己的身体状况",
          icon: "none"
        });
        return;
      }
      if (!formData.value.realizeChange) {
        common_vendor.index.showToast({
          title: "请选择是否意识到自己身体健康状况出现变化",
          icon: "none"
        });
        return;
      }
      if (!formData.value.assessSymptom) {
        common_vendor.index.showToast({
          title: "请选择是否评估自己的症状是否重要或是否需要引起重视",
          icon: "none"
        });
        return;
      }
      if (!formData.value.relieveSymptom) {
        common_vendor.index.showToast({
          title: "请选择是否采取一些措施来缓解自己的症状",
          icon: "none"
        });
        return;
      }
      if (!formData.value.relieveWithDifficulty) {
        common_vendor.index.showToast({
          title: "请选择是否在困难时以然坚持寻找一个能够缓解自己症状的治疗方法",
          icon: "none"
        });
        return;
      }
      if (!formData.value.judgmentMethod) {
        common_vendor.index.showToast({
          title: "判断所采取的缓解症状的方法是否有效",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: mode.value === "add" ? "提交中..." : "保存中..."
      });
      const requestData = {
        userName: formData.value.userName,
        location: formData.value.location,
        weight: formData.value.weight,
        bloodPressure: formData.value.bloodPressure,
        heartRate: formData.value.heartRate,
        smock: formData.value.smock,
        medicineOnTime: formData.value.medicineOnTime,
        missingMedicine: formData.value.missingMedicine,
        reduceMedicine: formData.value.reduceMedicine,
        discomfort: formData.value.discomfort,
        selfCareStatus: formData.value.selfCareStatus,
        heartFunction: formData.value.heartFunction,
        selfAppraisal: formData.value.selfAppraisal,
        swelling: formData.value.swelling,
        forcedRest: formData.value.forcedRest,
        dysbasia: formData.value.dysbasia,
        workWithDifficulty: formData.value.workWithDifficulty,
        goOutWithDifficulty: formData.value.goOutWithDifficulty,
        sleepWithDifficulty: formData.value.sleepWithDifficulty,
        doingWithDifficulty: formData.value.doingWithDifficulty,
        incomeWithDifficulty: formData.value.incomeWithDifficulty,
        entertainmentWithDifficulty: formData.value.entertainmentWithDifficulty,
        sexWithDifficulty: formData.value.sexWithDifficulty,
        eatLess: formData.value.eatLess,
        breathWithDifficulty: formData.value.breathWithDifficulty,
        tired: formData.value.tired,
        inHospital: formData.value.inHospital,
        spendMoney: formData.value.spendMoney,
        sideEffect: formData.value.sideEffect,
        beBburden: formData.value.beBburden,
        uncontrolled: formData.value.uncontrolled,
        anxiety: formData.value.anxiety,
        decreasedMemory: formData.value.decreasedMemory,
        depression: formData.value.depression,
        preventIllness: formData.value.preventIllness,
        exercise: formData.value.exercise,
        lowSalt: formData.value.lowSalt,
        seeDoctor: formData.value.seeDoctor,
        doctorMedicine: formData.value.doctorMedicine,
        lowSaltGoOut: formData.value.lowSaltGoOut,
        vaccinated: formData.value.vaccinated,
        visitFriends: formData.value.visitFriends,
        takeMedicine: formData.value.takeMedicine,
        consultMedicine: formData.value.consultMedicine,
        timelyAwareness: formData.value.timelyAwareness,
        knowInTime: formData.value.knowInTime,
        weighOneself: formData.value.weighOneself,
        focusOneself: formData.value.focusOneself,
        checkTheSideEffects: formData.value.checkTheSideEffects,
        beProneToFatigue: formData.value.beProneToFatigue,
        consultWorkers: formData.value.consultWorkers,
        monitorSymptoms: formData.value.monitorSymptoms,
        ankleEdema: formData.value.ankleEdema,
        shortnessOfBreath: formData.value.shortnessOfBreath,
        checkOwnSymptoms: formData.value.checkOwnSymptoms,
        reduceSalt: formData.value.reduceSalt,
        reduceFluids: formData.value.reduceFluids,
        takeMedicine2: formData.value.takeMedicine2,
        consultByPhone: formData.value.consultByPhone,
        seekHelp: formData.value.seekHelp,
        tryToFind: formData.value.tryToFind,
        reduceActivity: formData.value.reduceActivity,
        feelBetter: formData.value.feelBetter,
        keepStable: formData.value.keepStable,
        followPlan: formData.value.followPlan,
        followPlanWithDifficluty: formData.value.followPlanWithDifficluty,
        regularMonitor: formData.value.regularMonitor,
        monitorWithDifficulty: formData.value.monitorWithDifficulty,
        realizeChange: formData.value.realizeChange,
        assessSymptom: formData.value.assessSymptom,
        relieveSymptom: formData.value.relieveSymptom,
        relieveWithDifficulty: formData.value.relieveWithDifficulty,
        judgmentMethod: formData.value.judgmentMethod,
        userId: userStore.userInfo.userid
      };
      if (mode.value === "view") {
        requestData.id = query.value.id;
        requestData.updateUserId = userStore.userInfo.userid;
      }
      console.log("formData为：", formData.value);
      console.log("requestData为", requestData);
      common_vendor.index.request({
        url: `${"https://www.mograine.cn/api"}/patient/savemonitoring`,
        method: "POST",
        data: requestData,
        success: (res) => {
          var _a, _b;
          common_vendor.index.hideLoading();
          if ((_a = res.data) == null ? void 0 : _a.success) {
            common_vendor.index.showModal({
              title: mode.value === "add" ? "提交成功" : "保存成功",
              showCancel: false,
              success: () => {
                common_vendor.index.navigateBack();
              }
            });
          } else {
            const errorMsg = ((_b = res.data) == null ? void 0 : _b.message) || "提交失败，未知错误";
            common_vendor.index.showModal({
              title: mode.value === "add" ? "提交失败" : "保存失败",
              content: errorMsg,
              showCancel: false
            });
          }
        },
        fail: (err) => {
          common_vendor.index.hideLoading();
          const errorMsg = err.errMsg || "网络错误，请稍后重试";
          common_vendor.index.showModal({
            title: "提交失败",
            content: errorMsg,
            showCancel: false
          });
        }
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.p({
          title: mode.value === "add" ? "新增监测表" : "查看监测表",
          showBack: true
        }),
        b: formData.value.userName,
        c: common_vendor.o(($event) => formData.value.userName = $event.detail.value),
        d: !formData.value.location
      }, !formData.value.location ? {} : {
        e: common_vendor.t(formData.value.location)
      }, {
        f: common_vendor.o(getLocation),
        g: formData.value.weight,
        h: common_vendor.o(($event) => formData.value.weight = $event.detail.value),
        i: formData.value.bloodPressure,
        j: common_vendor.o(($event) => formData.value.bloodPressure = $event.detail.value),
        k: formData.value.heartRate,
        l: common_vendor.o(($event) => formData.value.heartRate = $event.detail.value),
        m: formData.value.smock === "是" ? 1 : "",
        n: common_vendor.o(($event) => formData.value.smock = "是"),
        o: formData.value.smock === "否" ? 1 : "",
        p: common_vendor.o(($event) => formData.value.smock = "否"),
        q: formData.value.medicineOnTime === "是" ? 1 : "",
        r: common_vendor.o(($event) => formData.value.medicineOnTime = "是"),
        s: formData.value.medicineOnTime === "否" ? 1 : "",
        t: common_vendor.o(($event) => formData.value.medicineOnTime = "否"),
        v: formData.value.missingMedicine === "是" ? 1 : "",
        w: common_vendor.o(($event) => formData.value.missingMedicine = "是"),
        x: formData.value.missingMedicine === "否" ? 1 : "",
        y: common_vendor.o(($event) => formData.value.missingMedicine = "否"),
        z: formData.value.reduceMedicine === "是" ? 1 : "",
        A: common_vendor.o(($event) => formData.value.reduceMedicine = "是"),
        B: formData.value.reduceMedicine === "否" ? 1 : "",
        C: common_vendor.o(($event) => formData.value.reduceMedicine = "否"),
        D: formData.value.discomfort && formData.value.discomfort.comfort ? 1 : "",
        E: common_vendor.o(($event) => toggleUncomfortable("comfort")),
        F: formData.value.discomfort && formData.value.discomfort.fatigue ? 1 : "",
        G: common_vendor.o(($event) => toggleUncomfortable("fatigue")),
        H: formData.value.discomfort && formData.value.discomfort.syncope ? 1 : "",
        I: common_vendor.o(($event) => toggleUncomfortable("syncope")),
        J: formData.value.discomfort && formData.value.discomfort.dyspnea ? 1 : "",
        K: common_vendor.o(($event) => toggleUncomfortable("dyspnea")),
        L: formData.value.discomfort && formData.value.discomfort.anginaPectoris ? 1 : "",
        M: common_vendor.o(($event) => toggleUncomfortable("anginaPectoris")),
        N: formData.value.discomfort && formData.value.discomfort.else ? 1 : "",
        O: common_vendor.o(($event) => toggleUncomfortable("else")),
        P: formData.value.selfCareStatus === "能够自理" ? 1 : "",
        Q: common_vendor.o(($event) => formData.value.selfCareStatus = "能够自理"),
        R: formData.value.selfCareStatus === "穿衣、洗澡部分困难" ? 1 : "",
        S: common_vendor.o(($event) => formData.value.selfCareStatus = "穿衣，洗澡部分困难"),
        T: formData.value.selfCareStatus === "不能自行穿衣、洗澡" ? 1 : "",
        U: common_vendor.o(($event) => formData.value.selfCareStatus = "不能自行穿衣，洗澡"),
        V: formData.value.heartFunction === "Ⅰ级（日常活动不受限）" ? 1 : "",
        W: common_vendor.o(($event) => formData.value.heartFunction = "Ⅰ级（日常活动不受限）"),
        X: formData.value.heartFunction === "Ⅱ级（轻度受限）" ? 1 : "",
        Y: common_vendor.o(($event) => formData.value.heartFunction = "Ⅱ级（轻度受限）"),
        Z: formData.value.heartFunction === "Ⅲ级（明显受限）" ? 1 : "",
        aa: common_vendor.o(($event) => formData.value.heartFunction = "Ⅲ级（明显受限）"),
        ab: formData.value.heartFunction === "Ⅳ级（不能从事任何体力活动）" ? 1 : "",
        ac: common_vendor.o(($event) => formData.value.heartFunction = "Ⅳ级（不能从事任何体力活动）"),
        ad: formData.value.selfAppraisal === "自我监测好" ? 1 : "",
        ae: common_vendor.o(($event) => formData.value.selfAppraisal = "自我监测好"),
        af: formData.value.selfAppraisal === "自我监测一般" ? 1 : "",
        ag: common_vendor.o(($event) => formData.value.selfAppraisal = "自我监测一般"),
        ah: formData.value.selfAppraisal === "自我监测不好" ? 1 : "",
        ai: common_vendor.o(($event) => formData.value.selfAppraisal = "自我监测不好"),
        aj: formData.value.swelling === "无" ? 1 : "",
        ak: common_vendor.o(($event) => formData.value.swelling = "无"),
        al: formData.value.swelling === "很轻" ? 1 : "",
        am: common_vendor.o(($event) => formData.value.swelling = "很轻"),
        an: formData.value.swelling === "轻" ? 1 : "",
        ao: common_vendor.o(($event) => formData.value.swelling = "轻"),
        ap: formData.value.swelling === "中" ? 1 : "",
        aq: common_vendor.o(($event) => formData.value.swelling = "中"),
        ar: formData.value.swelling === "重" ? 1 : "",
        as: common_vendor.o(($event) => formData.value.swelling = "重"),
        at: formData.value.swelling === "很重" ? 1 : "",
        av: common_vendor.o(($event) => formData.value.swelling = "很重"),
        aw: formData.value.forcedRest === "无" ? 1 : "",
        ax: common_vendor.o(($event) => formData.value.forcedRest = "无"),
        ay: formData.value.forcedRest === "很轻" ? 1 : "",
        az: common_vendor.o(($event) => formData.value.forcedRest = "很轻"),
        aA: formData.value.forcedRest === "轻" ? 1 : "",
        aB: common_vendor.o(($event) => formData.value.forcedRest = "轻"),
        aC: formData.value.forcedRest === "中" ? 1 : "",
        aD: common_vendor.o(($event) => formData.value.forcedRest = "中"),
        aE: formData.value.forcedRest == "重" ? 1 : "",
        aF: common_vendor.o(($event) => formData.value.forcedRest = "重"),
        aG: formData.value.forcedRest === "很重" ? 1 : "",
        aH: common_vendor.o(($event) => formData.value.forcedRest = "很重"),
        aI: formData.value.dysbasia === "无" ? 1 : "",
        aJ: common_vendor.o(($event) => formData.value.dysbasia = "无"),
        aK: formData.value.dysbasia === "很轻" ? 1 : "",
        aL: common_vendor.o(($event) => formData.value.dysbasia = "很轻"),
        aM: formData.value.dysbasia === "轻" ? 1 : "",
        aN: common_vendor.o(($event) => formData.value.dysbasia = "轻"),
        aO: formData.value.dysbasia === "中" ? 1 : "",
        aP: common_vendor.o(($event) => formData.value.dysbasia = "中"),
        aQ: formData.value.dysbasia == "重" ? 1 : "",
        aR: common_vendor.o(($event) => formData.value.dysbasia = "重"),
        aS: formData.value.dysbasia === "很重" ? 1 : "",
        aT: common_vendor.o(($event) => formData.value.dysbasia = "很重"),
        aU: formData.value.workWithDifficulty === "无" ? 1 : "",
        aV: common_vendor.o(($event) => formData.value.workWithDifficulty = "无"),
        aW: formData.value.workWithDifficulty === "很轻" ? 1 : "",
        aX: common_vendor.o(($event) => formData.value.workWithDifficulty = "很轻"),
        aY: formData.value.workWithDifficulty === "轻" ? 1 : "",
        aZ: common_vendor.o(($event) => formData.value.workWithDifficulty = "轻"),
        ba: formData.value.workWithDifficulty === "中" ? 1 : "",
        bb: common_vendor.o(($event) => formData.value.workWithDifficulty = "中"),
        bc: formData.value.workWithDifficulty == "重" ? 1 : "",
        bd: common_vendor.o(($event) => formData.value.workWithDifficulty = "重"),
        be: formData.value.workWithDifficulty === "很重" ? 1 : "",
        bf: common_vendor.o(($event) => formData.value.workWithDifficulty = "很重"),
        bg: formData.value.goOutWithDifficulty === "无" ? 1 : "",
        bh: common_vendor.o(($event) => formData.value.goOutWithDifficulty = "无"),
        bi: formData.value.goOutWithDifficulty === "很轻" ? 1 : "",
        bj: common_vendor.o(($event) => formData.value.goOutWithDifficulty = "很轻"),
        bk: formData.value.goOutWithDifficulty === "轻" ? 1 : "",
        bl: common_vendor.o(($event) => formData.value.goOutWithDifficulty = "轻"),
        bm: formData.value.goOutWithDifficulty === "中" ? 1 : "",
        bn: common_vendor.o(($event) => formData.value.goOutWithDifficulty = "中"),
        bo: formData.value.goOutWithDifficulty == "重" ? 1 : "",
        bp: common_vendor.o(($event) => formData.value.goOutWithDifficulty = "重"),
        bq: formData.value.goOutWithDifficulty === "很重" ? 1 : "",
        br: common_vendor.o(($event) => formData.value.goOutWithDifficulty = "很重"),
        bs: formData.value.sleepWithDifficulty === "无" ? 1 : "",
        bt: common_vendor.o(($event) => formData.value.sleepWithDifficulty = "无"),
        bv: formData.value.sleepWithDifficulty === "很轻" ? 1 : "",
        bw: common_vendor.o(($event) => formData.value.sleepWithDifficulty = "很轻"),
        bx: formData.value.sleepWithDifficulty === "轻" ? 1 : "",
        by: common_vendor.o(($event) => formData.value.sleepWithDifficulty = "轻"),
        bz: formData.value.sleepWithDifficulty === "中" ? 1 : "",
        bA: common_vendor.o(($event) => formData.value.sleepWithDifficulty = "中"),
        bB: formData.value.sleepWithDifficulty == "重" ? 1 : "",
        bC: common_vendor.o(($event) => formData.value.sleepWithDifficulty = "重"),
        bD: formData.value.sleepWithDifficulty === "很重" ? 1 : "",
        bE: common_vendor.o(($event) => formData.value.sleepWithDifficulty = "很重"),
        bF: formData.value.doingWithDifficulty === "无" ? 1 : "",
        bG: common_vendor.o(($event) => formData.value.doingWithDifficulty = "无"),
        bH: formData.value.doingWithDifficulty === "很轻" ? 1 : "",
        bI: common_vendor.o(($event) => formData.value.doingWithDifficulty = "很轻"),
        bJ: formData.value.doingWithDifficulty === "轻" ? 1 : "",
        bK: common_vendor.o(($event) => formData.value.doingWithDifficulty = "轻"),
        bL: formData.value.doingWithDifficulty === "中" ? 1 : "",
        bM: common_vendor.o(($event) => formData.value.doingWithDifficulty = "中"),
        bN: formData.value.doingWithDifficulty == "重" ? 1 : "",
        bO: common_vendor.o(($event) => formData.value.doingWithDifficulty = "重"),
        bP: formData.value.doingWithDifficulty === "很重" ? 1 : "",
        bQ: common_vendor.o(($event) => formData.value.doingWithDifficulty = "很重"),
        bR: formData.value.incomeWithDifficulty === "无" ? 1 : "",
        bS: common_vendor.o(($event) => formData.value.incomeWithDifficulty = "无"),
        bT: formData.value.incomeWithDifficulty === "很轻" ? 1 : "",
        bU: common_vendor.o(($event) => formData.value.incomeWithDifficulty = "很轻"),
        bV: formData.value.incomeWithDifficulty === "轻" ? 1 : "",
        bW: common_vendor.o(($event) => formData.value.incomeWithDifficulty = "轻"),
        bX: formData.value.incomeWithDifficulty === "中" ? 1 : "",
        bY: common_vendor.o(($event) => formData.value.incomeWithDifficulty = "中"),
        bZ: formData.value.incomeWithDifficulty == "重" ? 1 : "",
        ca: common_vendor.o(($event) => formData.value.incomeWithDifficulty = "重"),
        cb: formData.value.incomeWithDifficulty === "很重" ? 1 : "",
        cc: common_vendor.o(($event) => formData.value.incomeWithDifficulty = "很重"),
        cd: formData.value.entertainmentWithDifficulty === "无" ? 1 : "",
        ce: common_vendor.o(($event) => formData.value.entertainmentWithDifficulty = "无"),
        cf: formData.value.entertainmentWithDifficulty === "很轻" ? 1 : "",
        cg: common_vendor.o(($event) => formData.value.entertainmentWithDifficulty = "很轻"),
        ch: formData.value.entertainmentWithDifficulty === "轻" ? 1 : "",
        ci: common_vendor.o(($event) => formData.value.entertainmentWithDifficulty = "轻"),
        cj: formData.value.entertainmentWithDifficulty === "中" ? 1 : "",
        ck: common_vendor.o(($event) => formData.value.entertainmentWithDifficulty = "中"),
        cl: formData.value.entertainmentWithDifficulty == "重" ? 1 : "",
        cm: common_vendor.o(($event) => formData.value.entertainmentWithDifficulty = "重"),
        cn: formData.value.entertainmentWithDifficulty === "很重" ? 1 : "",
        co: common_vendor.o(($event) => formData.value.entertainmentWithDifficulty = "很重"),
        cp: formData.value.sexWithDifficulty === "无" ? 1 : "",
        cq: common_vendor.o(($event) => formData.value.sexWithDifficulty = "无"),
        cr: formData.value.sexWithDifficulty === "很轻" ? 1 : "",
        cs: common_vendor.o(($event) => formData.value.sexWithDifficulty = "很轻"),
        ct: formData.value.sexWithDifficulty === "轻" ? 1 : "",
        cv: common_vendor.o(($event) => formData.value.sexWithDifficulty = "轻"),
        cw: formData.value.sexWithDifficulty === "中" ? 1 : "",
        cx: common_vendor.o(($event) => formData.value.sexWithDifficulty = "中"),
        cy: formData.value.sexWithDifficulty == "重" ? 1 : "",
        cz: common_vendor.o(($event) => formData.value.sexWithDifficulty = "重"),
        cA: formData.value.sexWithDifficulty === "很重" ? 1 : "",
        cB: common_vendor.o(($event) => formData.value.sexWithDifficulty = "很重"),
        cC: formData.value.eatLess === "无" ? 1 : "",
        cD: common_vendor.o(($event) => formData.value.eatLess = "无"),
        cE: formData.value.eatLess === "很轻" ? 1 : "",
        cF: common_vendor.o(($event) => formData.value.eatLess = "很轻"),
        cG: formData.value.eatLess === "轻" ? 1 : "",
        cH: common_vendor.o(($event) => formData.value.eatLess = "轻"),
        cI: formData.value.eatLess === "中" ? 1 : "",
        cJ: common_vendor.o(($event) => formData.value.eatLess = "中"),
        cK: formData.value.eatLess == "重" ? 1 : "",
        cL: common_vendor.o(($event) => formData.value.eatLess = "重"),
        cM: formData.value.eatLess === "很重" ? 1 : "",
        cN: common_vendor.o(($event) => formData.value.eatLess = "很重"),
        cO: formData.value.breathWithDifficulty === "无" ? 1 : "",
        cP: common_vendor.o(($event) => formData.value.breathWithDifficulty = "无"),
        cQ: formData.value.breathWithDifficulty === "很轻" ? 1 : "",
        cR: common_vendor.o(($event) => formData.value.breathWithDifficulty = "很轻"),
        cS: formData.value.breathWithDifficulty === "轻" ? 1 : "",
        cT: common_vendor.o(($event) => formData.value.breathWithDifficulty = "轻"),
        cU: formData.value.breathWithDifficulty === "中" ? 1 : "",
        cV: common_vendor.o(($event) => formData.value.breathWithDifficulty = "中"),
        cW: formData.value.breathWithDifficulty == "重" ? 1 : "",
        cX: common_vendor.o(($event) => formData.value.breathWithDifficulty = "重"),
        cY: formData.value.breathWithDifficulty === "很重" ? 1 : "",
        cZ: common_vendor.o(($event) => formData.value.breathWithDifficulty = "很重"),
        da: formData.value.tired === "无" ? 1 : "",
        db: common_vendor.o(($event) => formData.value.tired = "无"),
        dc: formData.value.tired === "很轻" ? 1 : "",
        dd: common_vendor.o(($event) => formData.value.tired = "很轻"),
        de: formData.value.tired === "轻" ? 1 : "",
        df: common_vendor.o(($event) => formData.value.tired = "轻"),
        dg: formData.value.tired === "中" ? 1 : "",
        dh: common_vendor.o(($event) => formData.value.tired = "中"),
        di: formData.value.tired == "重" ? 1 : "",
        dj: common_vendor.o(($event) => formData.value.tired = "重"),
        dk: formData.value.tired === "很重" ? 1 : "",
        dl: common_vendor.o(($event) => formData.value.tired = "很重"),
        dm: formData.value.inHospital === "无" ? 1 : "",
        dn: common_vendor.o(($event) => formData.value.inHospital = "无"),
        dp: formData.value.inHospital === "很轻" ? 1 : "",
        dq: common_vendor.o(($event) => formData.value.inHospital = "很轻"),
        dr: formData.value.inHospital === "轻" ? 1 : "",
        ds: common_vendor.o(($event) => formData.value.inHospital = "轻"),
        dt: formData.value.inHospital === "中" ? 1 : "",
        dv: common_vendor.o(($event) => formData.value.inHospital = "中"),
        dw: formData.value.inHospital == "重" ? 1 : "",
        dx: common_vendor.o(($event) => formData.value.inHospital = "重"),
        dy: formData.value.inHospital === "很重" ? 1 : "",
        dz: common_vendor.o(($event) => formData.value.inHospital = "很重"),
        dA: formData.value.spendMoney === "无" ? 1 : "",
        dB: common_vendor.o(($event) => formData.value.spendMoney = "无"),
        dC: formData.value.spendMoney === "很轻" ? 1 : "",
        dD: common_vendor.o(($event) => formData.value.spendMoney = "很轻"),
        dE: formData.value.spendMoney === "轻" ? 1 : "",
        dF: common_vendor.o(($event) => formData.value.spendMoney = "轻"),
        dG: formData.value.spendMoney === "中" ? 1 : "",
        dH: common_vendor.o(($event) => formData.value.spendMoney = "中"),
        dI: formData.value.spendMoney == "重" ? 1 : "",
        dJ: common_vendor.o(($event) => formData.value.spendMoney = "重"),
        dK: formData.value.spendMoney === "很重" ? 1 : "",
        dL: common_vendor.o(($event) => formData.value.spendMoney = "很重"),
        dM: formData.value.sideEffect === "无" ? 1 : "",
        dN: common_vendor.o(($event) => formData.value.sideEffect = "无"),
        dO: formData.value.sideEffect === "很轻" ? 1 : "",
        dP: common_vendor.o(($event) => formData.value.sideEffect = "很轻"),
        dQ: formData.value.sideEffect === "轻" ? 1 : "",
        dR: common_vendor.o(($event) => formData.value.sideEffect = "轻"),
        dS: formData.value.sideEffect === "中" ? 1 : "",
        dT: common_vendor.o(($event) => formData.value.sideEffect = "中"),
        dU: formData.value.sideEffect == "重" ? 1 : "",
        dV: common_vendor.o(($event) => formData.value.sideEffect = "重"),
        dW: formData.value.sideEffect === "很重" ? 1 : "",
        dX: common_vendor.o(($event) => formData.value.sideEffect = "很重"),
        dY: formData.value.beBburden === "无" ? 1 : "",
        dZ: common_vendor.o(($event) => formData.value.beBburden = "无"),
        ea: formData.value.beBburden === "很轻" ? 1 : "",
        eb: common_vendor.o(($event) => formData.value.beBburden = "很轻"),
        ec: formData.value.beBburden === "轻" ? 1 : "",
        ed: common_vendor.o(($event) => formData.value.beBburden = "轻"),
        ee: formData.value.beBburden === "中" ? 1 : "",
        ef: common_vendor.o(($event) => formData.value.beBburden = "中"),
        eg: formData.value.beBburden == "重" ? 1 : "",
        eh: common_vendor.o(($event) => formData.value.beBburden = "重"),
        ei: formData.value.beBburden === "很重" ? 1 : "",
        ej: common_vendor.o(($event) => formData.value.beBburden = "很重"),
        ek: formData.value.uncontrolled === "无" ? 1 : "",
        el: common_vendor.o(($event) => formData.value.uncontrolled = "无"),
        em: formData.value.uncontrolled === "很轻" ? 1 : "",
        en: common_vendor.o(($event) => formData.value.uncontrolled = "很轻"),
        eo: formData.value.uncontrolled === "轻" ? 1 : "",
        ep: common_vendor.o(($event) => formData.value.uncontrolled = "轻"),
        eq: formData.value.uncontrolled === "中" ? 1 : "",
        er: common_vendor.o(($event) => formData.value.uncontrolled = "中"),
        es: formData.value.uncontrolled == "重" ? 1 : "",
        et: common_vendor.o(($event) => formData.value.uncontrolled = "重"),
        ev: formData.value.uncontrolled === "很重" ? 1 : "",
        ew: common_vendor.o(($event) => formData.value.uncontrolled = "很重"),
        ex: formData.value.anxiety === "无" ? 1 : "",
        ey: common_vendor.o(($event) => formData.value.anxiety = "无"),
        ez: formData.value.anxiety === "很轻" ? 1 : "",
        eA: common_vendor.o(($event) => formData.value.anxiety = "很轻"),
        eB: formData.value.anxiety === "轻" ? 1 : "",
        eC: common_vendor.o(($event) => formData.value.anxiety = "轻"),
        eD: formData.value.anxiety === "中" ? 1 : "",
        eE: common_vendor.o(($event) => formData.value.anxiety = "中"),
        eF: formData.value.anxiety == "重" ? 1 : "",
        eG: common_vendor.o(($event) => formData.value.anxiety = "重"),
        eH: formData.value.anxiety === "很重" ? 1 : "",
        eI: common_vendor.o(($event) => formData.value.anxiety = "很重"),
        eJ: formData.value.decreasedMemory === "无" ? 1 : "",
        eK: common_vendor.o(($event) => formData.value.decreasedMemory = "无"),
        eL: formData.value.decreasedMemory === "很轻" ? 1 : "",
        eM: common_vendor.o(($event) => formData.value.decreasedMemory = "很轻"),
        eN: formData.value.decreasedMemory === "轻" ? 1 : "",
        eO: common_vendor.o(($event) => formData.value.decreasedMemory = "轻"),
        eP: formData.value.decreasedMemory === "中" ? 1 : "",
        eQ: common_vendor.o(($event) => formData.value.decreasedMemory = "中"),
        eR: formData.value.decreasedMemory == "重" ? 1 : "",
        eS: common_vendor.o(($event) => formData.value.decreasedMemory = "重"),
        eT: formData.value.decreasedMemory === "很重" ? 1 : "",
        eU: common_vendor.o(($event) => formData.value.decreasedMemory = "很重"),
        eV: formData.value.depression === "无" ? 1 : "",
        eW: common_vendor.o(($event) => formData.value.depression = "无"),
        eX: formData.value.depression === "很轻" ? 1 : "",
        eY: common_vendor.o(($event) => formData.value.depression = "很轻"),
        eZ: formData.value.depression === "轻" ? 1 : "",
        fa: common_vendor.o(($event) => formData.value.depression = "轻"),
        fb: formData.value.depression === "中" ? 1 : "",
        fc: common_vendor.o(($event) => formData.value.depression = "中"),
        fd: formData.value.depression == "重" ? 1 : "",
        fe: common_vendor.o(($event) => formData.value.depression = "重"),
        ff: formData.value.depression === "很重" ? 1 : "",
        fg: common_vendor.o(($event) => formData.value.depression = "很重"),
        fh: formData.value.preventIllness === "从不" ? 1 : "",
        fi: common_vendor.o(($event) => formData.value.preventIllness = "从不"),
        fj: formData.value.preventIllness === "偶尔" ? 1 : "",
        fk: common_vendor.o(($event) => formData.value.preventIllness = "偶尔"),
        fl: formData.value.preventIllness === "有时" ? 1 : "",
        fm: common_vendor.o(($event) => formData.value.preventIllness = "有时"),
        fn: formData.value.preventIllness === "经常" ? 1 : "",
        fo: common_vendor.o(($event) => formData.value.preventIllness = "经常"),
        fp: formData.value.preventIllness === "总是" ? 1 : "",
        fq: common_vendor.o(($event) => formData.value.preventIllness = "总是"),
        fr: formData.value.exercise === "从不" ? 1 : "",
        fs: common_vendor.o(($event) => formData.value.exercise = "从不"),
        ft: formData.value.exercise === "偶尔" ? 1 : "",
        fv: common_vendor.o(($event) => formData.value.exercise = "偶尔"),
        fw: formData.value.exercise === "有时" ? 1 : "",
        fx: common_vendor.o(($event) => formData.value.exercise = "有时"),
        fy: formData.value.exercise === "经常" ? 1 : "",
        fz: common_vendor.o(($event) => formData.value.exercise = "经常"),
        fA: formData.value.exercise === "总是" ? 1 : "",
        fB: common_vendor.o(($event) => formData.value.exercise = "总是"),
        fC: formData.value.lowSalt === "从不" ? 1 : "",
        fD: common_vendor.o(($event) => formData.value.lowSalt = "从不"),
        fE: formData.value.lowSalt === "偶尔" ? 1 : "",
        fF: common_vendor.o(($event) => formData.value.lowSalt = "偶尔"),
        fG: formData.value.lowSalt === "有时" ? 1 : "",
        fH: common_vendor.o(($event) => formData.value.lowSalt = "有时"),
        fI: formData.value.lowSalt === "经常" ? 1 : "",
        fJ: common_vendor.o(($event) => formData.value.lowSalt = "经常"),
        fK: formData.value.lowSalt === "总是" ? 1 : "",
        fL: common_vendor.o(($event) => formData.value.lowSalt = "总是"),
        fM: formData.value.seeDoctor === "从不" ? 1 : "",
        fN: common_vendor.o(($event) => formData.value.seeDoctor = "从不"),
        fO: formData.value.seeDoctor === "偶尔" ? 1 : "",
        fP: common_vendor.o(($event) => formData.value.seeDoctor = "偶尔"),
        fQ: formData.value.seeDoctor === "有时" ? 1 : "",
        fR: common_vendor.o(($event) => formData.value.seeDoctor = "有时"),
        fS: formData.value.seeDoctor === "经常" ? 1 : "",
        fT: common_vendor.o(($event) => formData.value.seeDoctor = "经常"),
        fU: formData.value.seeDoctor === "总是" ? 1 : "",
        fV: common_vendor.o(($event) => formData.value.seeDoctor = "总是"),
        fW: formData.value.doctorMedicine === "从不" ? 1 : "",
        fX: common_vendor.o(($event) => formData.value.doctorMedicine = "从不"),
        fY: formData.value.doctorMedicine === "偶尔" ? 1 : "",
        fZ: common_vendor.o(($event) => formData.value.doctorMedicine = "偶尔"),
        ga: formData.value.doctorMedicine === "有时" ? 1 : "",
        gb: common_vendor.o(($event) => formData.value.doctorMedicine = "有时"),
        gc: formData.value.doctorMedicine === "经常" ? 1 : "",
        gd: common_vendor.o(($event) => formData.value.doctorMedicine = "经常"),
        ge: formData.value.doctorMedicine === "总是" ? 1 : "",
        gf: common_vendor.o(($event) => formData.value.doctorMedicine = "总是"),
        gg: formData.value.lowSaltGoOut === "从不" ? 1 : "",
        gh: common_vendor.o(($event) => formData.value.lowSaltGoOut = "从不"),
        gi: formData.value.lowSaltGoOut === "偶尔" ? 1 : "",
        gj: common_vendor.o(($event) => formData.value.lowSaltGoOut = "偶尔"),
        gk: formData.value.lowSaltGoOut === "有时" ? 1 : "",
        gl: common_vendor.o(($event) => formData.value.lowSaltGoOut = "有时"),
        gm: formData.value.lowSaltGoOut === "经常" ? 1 : "",
        gn: common_vendor.o(($event) => formData.value.lowSaltGoOut = "经常"),
        go: formData.value.lowSaltGoOut === "总是" ? 1 : "",
        gp: common_vendor.o(($event) => formData.value.lowSaltGoOut = "总是"),
        gq: formData.value.vaccinated === "从不" ? 1 : "",
        gr: common_vendor.o(($event) => formData.value.vaccinated = "从不"),
        gs: formData.value.vaccinated === "偶尔" ? 1 : "",
        gt: common_vendor.o(($event) => formData.value.vaccinated = "偶尔"),
        gv: formData.value.vaccinated === "有时" ? 1 : "",
        gw: common_vendor.o(($event) => formData.value.vaccinated = "有时"),
        gx: formData.value.vaccinated === "经常" ? 1 : "",
        gy: common_vendor.o(($event) => formData.value.vaccinated = "经常"),
        gz: formData.value.vaccinated === "总是" ? 1 : "",
        gA: common_vendor.o(($event) => formData.value.vaccinated = "总是"),
        gB: formData.value.visitFriends === "从不" ? 1 : "",
        gC: common_vendor.o(($event) => formData.value.visitFriends = "从不"),
        gD: formData.value.visitFriends === "偶尔" ? 1 : "",
        gE: common_vendor.o(($event) => formData.value.visitFriends = "偶尔"),
        gF: formData.value.visitFriends === "有时" ? 1 : "",
        gG: common_vendor.o(($event) => formData.value.visitFriends = "有时"),
        gH: formData.value.visitFriends === "经常" ? 1 : "",
        gI: common_vendor.o(($event) => formData.value.visitFriends = "经常"),
        gJ: formData.value.visitFriends === "总是" ? 1 : "",
        gK: common_vendor.o(($event) => formData.value.visitFriends = "总是"),
        gL: formData.value.takeMedicine === "从不" ? 1 : "",
        gM: common_vendor.o(($event) => formData.value.takeMedicine = "从不"),
        gN: formData.value.takeMedicine === "偶尔" ? 1 : "",
        gO: common_vendor.o(($event) => formData.value.takeMedicine = "偶尔"),
        gP: formData.value.takeMedicine === "有时" ? 1 : "",
        gQ: common_vendor.o(($event) => formData.value.takeMedicine = "有时"),
        gR: formData.value.takeMedicine === "经常" ? 1 : "",
        gS: common_vendor.o(($event) => formData.value.takeMedicine = "经常"),
        gT: formData.value.takeMedicine === "总是" ? 1 : "",
        gU: common_vendor.o(($event) => formData.value.takeMedicine = "总是"),
        gV: formData.value.consultMedicine === "从不" ? 1 : "",
        gW: common_vendor.o(($event) => formData.value.consultMedicine = "从不"),
        gX: formData.value.consultMedicine === "偶尔" ? 1 : "",
        gY: common_vendor.o(($event) => formData.value.consultMedicine = "偶尔"),
        gZ: formData.value.consultMedicine === "有时" ? 1 : "",
        ha: common_vendor.o(($event) => formData.value.consultMedicine = "有时"),
        hb: formData.value.consultMedicine === "经常" ? 1 : "",
        hc: common_vendor.o(($event) => formData.value.consultMedicine = "经常"),
        hd: formData.value.consultMedicine === "总是" ? 1 : "",
        he: common_vendor.o(($event) => formData.value.consultMedicine = "总是"),
        hf: formData.value.weighOneself === "从不" ? 1 : "",
        hg: common_vendor.o(($event) => formData.value.weighOneself = "从不"),
        hh: formData.value.weighOneself === "偶尔" ? 1 : "",
        hi: common_vendor.o(($event) => formData.value.weighOneself = "偶尔"),
        hj: formData.value.weighOneself === "有时" ? 1 : "",
        hk: common_vendor.o(($event) => formData.value.weighOneself = "有时"),
        hl: formData.value.weighOneself === "经常" ? 1 : "",
        hm: common_vendor.o(($event) => formData.value.weighOneself = "经常"),
        hn: formData.value.weighOneself === "总是" ? 1 : "",
        ho: common_vendor.o(($event) => formData.value.weighOneself = "总是"),
        hp: formData.value.focusOneself === "从不" ? 1 : "",
        hq: common_vendor.o(($event) => formData.value.focusOneself = "从不"),
        hr: formData.value.focusOneself === "偶尔" ? 1 : "",
        hs: common_vendor.o(($event) => formData.value.focusOneself = "偶尔"),
        ht: formData.value.focusOneself === "有时" ? 1 : "",
        hv: common_vendor.o(($event) => formData.value.focusOneself = "有时"),
        hw: formData.value.focusOneself === "经常" ? 1 : "",
        hx: common_vendor.o(($event) => formData.value.focusOneself = "经常"),
        hy: formData.value.focusOneself === "总是" ? 1 : "",
        hz: common_vendor.o(($event) => formData.value.focusOneself = "总是"),
        hA: formData.value.checkTheSideEffects === "从不" ? 1 : "",
        hB: common_vendor.o(($event) => formData.value.checkTheSideEffects = "从不"),
        hC: formData.value.checkTheSideEffects === "偶尔" ? 1 : "",
        hD: common_vendor.o(($event) => formData.value.checkTheSideEffects = "偶尔"),
        hE: formData.value.checkTheSideEffects === "有时" ? 1 : "",
        hF: common_vendor.o(($event) => formData.value.checkTheSideEffects = "有时"),
        hG: formData.value.checkTheSideEffects === "经常" ? 1 : "",
        hH: common_vendor.o(($event) => formData.value.checkTheSideEffects = "经常"),
        hI: formData.value.checkTheSideEffects === "总是" ? 1 : "",
        hJ: common_vendor.o(($event) => formData.value.checkTheSideEffects = "总是"),
        hK: formData.value.beProneToFatigue === "从不" ? 1 : "",
        hL: common_vendor.o(($event) => formData.value.beProneToFatigue = "从不"),
        hM: formData.value.beProneToFatigue === "偶尔" ? 1 : "",
        hN: common_vendor.o(($event) => formData.value.beProneToFatigue = "偶尔"),
        hO: formData.value.beProneToFatigue === "有时" ? 1 : "",
        hP: common_vendor.o(($event) => formData.value.beProneToFatigue = "有时"),
        hQ: formData.value.beProneToFatigue === "经常" ? 1 : "",
        hR: common_vendor.o(($event) => formData.value.beProneToFatigue = "经常"),
        hS: formData.value.beProneToFatigue === "总是" ? 1 : "",
        hT: common_vendor.o(($event) => formData.value.beProneToFatigue = "总是"),
        hU: formData.value.consultWorkers === "从不" ? 1 : "",
        hV: common_vendor.o(($event) => formData.value.consultWorkers = "从不"),
        hW: formData.value.consultWorkers === "偶尔" ? 1 : "",
        hX: common_vendor.o(($event) => formData.value.consultWorkers = "偶尔"),
        hY: formData.value.consultWorkers === "有时" ? 1 : "",
        hZ: common_vendor.o(($event) => formData.value.consultWorkers = "有时"),
        ia: formData.value.consultWorkers === "经常" ? 1 : "",
        ib: common_vendor.o(($event) => formData.value.consultWorkers = "经常"),
        ic: formData.value.consultWorkers === "总是" ? 1 : "",
        id: common_vendor.o(($event) => formData.value.consultWorkers = "总是"),
        ie: formData.value.monitorSymptoms === "从不" ? 1 : "",
        ig: common_vendor.o(($event) => formData.value.monitorSymptoms = "从不"),
        ih: formData.value.monitorSymptoms === "偶尔" ? 1 : "",
        ii: common_vendor.o(($event) => formData.value.monitorSymptoms = "偶尔"),
        ij: formData.value.monitorSymptoms === "有时" ? 1 : "",
        ik: common_vendor.o(($event) => formData.value.monitorSymptoms = "有时"),
        il: formData.value.monitorSymptoms === "经常" ? 1 : "",
        im: common_vendor.o(($event) => formData.value.monitorSymptoms = "经常"),
        io: formData.value.monitorSymptoms === "总是" ? 1 : "",
        ip: common_vendor.o(($event) => formData.value.monitorSymptoms = "总是"),
        iq: formData.value.ankleEdema === "从不" ? 1 : "",
        ir: common_vendor.o(($event) => formData.value.ankleEdema = "从不"),
        is: formData.value.ankleEdema === "偶尔" ? 1 : "",
        it: common_vendor.o(($event) => formData.value.ankleEdema = "偶尔"),
        iv: formData.value.ankleEdema === "有时" ? 1 : "",
        iw: common_vendor.o(($event) => formData.value.ankleEdema = "有时"),
        ix: formData.value.ankleEdema === "经常" ? 1 : "",
        iy: common_vendor.o(($event) => formData.value.ankleEdema = "经常"),
        iz: formData.value.ankleEdema === "总是" ? 1 : "",
        iA: common_vendor.o(($event) => formData.value.ankleEdema = "总是"),
        iB: formData.value.shortnessOfBreath === "从不" ? 1 : "",
        iC: common_vendor.o(($event) => formData.value.shortnessOfBreath = "从不"),
        iD: formData.value.shortnessOfBreath === "偶尔" ? 1 : "",
        iE: common_vendor.o(($event) => formData.value.shortnessOfBreath = "偶尔"),
        iF: formData.value.shortnessOfBreath === "有时" ? 1 : "",
        iG: common_vendor.o(($event) => formData.value.shortnessOfBreath = "有时"),
        iH: formData.value.shortnessOfBreath === "经常" ? 1 : "",
        iI: common_vendor.o(($event) => formData.value.shortnessOfBreath = "经常"),
        iJ: formData.value.shortnessOfBreath === "总是" ? 1 : "",
        iK: common_vendor.o(($event) => formData.value.shortnessOfBreath = "总是"),
        iL: formData.value.checkOwnSymptoms === "从不" ? 1 : "",
        iM: common_vendor.o(($event) => formData.value.checkOwnSymptoms = "从不"),
        iN: formData.value.checkOwnSymptoms === "偶尔" ? 1 : "",
        iO: common_vendor.o(($event) => formData.value.checkOwnSymptoms = "偶尔"),
        iP: formData.value.checkOwnSymptoms === "有时" ? 1 : "",
        iQ: common_vendor.o(($event) => formData.value.checkOwnSymptoms = "有时"),
        iR: formData.value.checkOwnSymptoms === "经常" ? 1 : "",
        iS: common_vendor.o(($event) => formData.value.checkOwnSymptoms = "经常"),
        iT: formData.value.checkOwnSymptoms === "总是" ? 1 : "",
        iU: common_vendor.o(($event) => formData.value.checkOwnSymptoms = "总是"),
        iV: formData.value.timelyAwareness === "还没出现过" ? 1 : "",
        iW: common_vendor.o(($event) => formData.value.timelyAwareness = "还没出现过"),
        iX: formData.value.timelyAwareness === "偶尔" ? 1 : "",
        iY: common_vendor.o(($event) => formData.value.timelyAwareness = "偶尔"),
        iZ: formData.value.timelyAwareness === "不快" ? 1 : "",
        ja: common_vendor.o(($event) => formData.value.timelyAwareness = "不快"),
        jb: formData.value.timelyAwareness === "稍微快一点" ? 1 : "",
        jc: common_vendor.o(($event) => formData.value.timelyAwareness = "稍微快一点"),
        jd: formData.value.timelyAwareness === "经常" ? 1 : "",
        je: common_vendor.o(($event) => formData.value.timelyAwareness = "经常"),
        jf: formData.value.timelyAwareness === "非常迅速" ? 1 : "",
        jg: common_vendor.o(($event) => formData.value.timelyAwareness = "非常迅速"),
        jh: formData.value.timelyAwareness === "没有识别出症状" ? 1 : "",
        ji: common_vendor.o(($event) => formData.value.timelyAwareness = "没有识别出症状"),
        jj: formData.value.knowInTime === "还没出现过" ? 1 : "",
        jk: common_vendor.o(($event) => formData.value.knowInTime = "还没出现过"),
        jl: formData.value.knowInTime === "偶尔" ? 1 : "",
        jm: common_vendor.o(($event) => formData.value.knowInTime = "偶尔"),
        jn: formData.value.knowInTime === "不快" ? 1 : "",
        jo: common_vendor.o(($event) => formData.value.knowInTime = "不快"),
        jp: formData.value.knowInTime === "稍微快一点" ? 1 : "",
        jq: common_vendor.o(($event) => formData.value.knowInTime = "稍微快一点"),
        jr: formData.value.knowInTime === "经常" ? 1 : "",
        js: common_vendor.o(($event) => formData.value.knowInTime = "经常"),
        jt: formData.value.knowInTime === "非常迅速" ? 1 : "",
        jv: common_vendor.o(($event) => formData.value.knowInTime = "非常迅速"),
        jw: formData.value.knowInTime === "没有识别出症状" ? 1 : "",
        jx: common_vendor.o(($event) => formData.value.knowInTime = "没有识别出症状"),
        jy: formData.value.reduceSalt === "不会" ? 1 : "",
        jz: common_vendor.o(($event) => formData.value.reduceSalt = "不会"),
        jA: formData.value.reduceSalt === "偶尔" ? 1 : "",
        jB: common_vendor.o(($event) => formData.value.reduceSalt = "偶尔"),
        jC: formData.value.reduceSalt === "可能会" ? 1 : "",
        jD: common_vendor.o(($event) => formData.value.reduceSalt = "可能会"),
        jE: formData.value.reduceSalt === "经常" ? 1 : "",
        jF: common_vendor.o(($event) => formData.value.reduceSalt = "经常"),
        jG: formData.value.reduceSalt === "肯定会" ? 1 : "",
        jH: common_vendor.o(($event) => formData.value.reduceSalt = "肯定会"),
        jI: formData.value.reduceFluids === "不会" ? 1 : "",
        jJ: common_vendor.o(($event) => formData.value.reduceFluids = "不会"),
        jK: formData.value.reduceFluids === "偶尔" ? 1 : "",
        jL: common_vendor.o(($event) => formData.value.reduceFluids = "偶尔"),
        jM: formData.value.reduceFluids === "可能会" ? 1 : "",
        jN: common_vendor.o(($event) => formData.value.reduceFluids = "可能会"),
        jO: formData.value.reduceFluids === "经常" ? 1 : "",
        jP: common_vendor.o(($event) => formData.value.reduceFluids = "经常"),
        jQ: formData.value.reduceFluids === "肯定会" ? 1 : "",
        jR: common_vendor.o(($event) => formData.value.reduceFluids = "肯定会"),
        jS: formData.value.takeMedicine2 === "不会" ? 1 : "",
        jT: common_vendor.o(($event) => formData.value.takeMedicine2 = "不会"),
        jU: formData.value.takeMedicine2 === "偶尔" ? 1 : "",
        jV: common_vendor.o(($event) => formData.value.takeMedicine2 = "偶尔"),
        jW: formData.value.takeMedicine2 === "可能会" ? 1 : "",
        jX: common_vendor.o(($event) => formData.value.takeMedicine2 = "可能会"),
        jY: formData.value.takeMedicine2 === "经常" ? 1 : "",
        jZ: common_vendor.o(($event) => formData.value.takeMedicine2 = "经常"),
        ka: formData.value.takeMedicine2 === "肯定会" ? 1 : "",
        kb: common_vendor.o(($event) => formData.value.takeMedicine2 = "肯定会"),
        kc: formData.value.consultByPhone === "不会" ? 1 : "",
        kd: common_vendor.o(($event) => formData.value.consultByPhone = "不会"),
        ke: formData.value.consultByPhone === "偶尔" ? 1 : "",
        kf: common_vendor.o(($event) => formData.value.consultByPhone = "偶尔"),
        kg: formData.value.consultByPhone === "可能会" ? 1 : "",
        kh: common_vendor.o(($event) => formData.value.consultByPhone = "可能会"),
        ki: formData.value.consultByPhone === "经常" ? 1 : "",
        kj: common_vendor.o(($event) => formData.value.consultByPhone = "经常"),
        kk: formData.value.consultByPhone === "肯定会" ? 1 : "",
        kl: common_vendor.o(($event) => formData.value.consultByPhone = "肯定会"),
        km: formData.value.seekHelp === "不会" ? 1 : "",
        kn: common_vendor.o(($event) => formData.value.seekHelp = "不会"),
        ko: formData.value.seekHelp === "偶尔" ? 1 : "",
        kp: common_vendor.o(($event) => formData.value.seekHelp = "偶尔"),
        kq: formData.value.seekHelp === "可能会" ? 1 : "",
        kr: common_vendor.o(($event) => formData.value.seekHelp = "可能会"),
        ks: formData.value.seekHelp === "经常" ? 1 : "",
        kt: common_vendor.o(($event) => formData.value.seekHelp = "经常"),
        kv: formData.value.seekHelp === "肯定会" ? 1 : "",
        kw: common_vendor.o(($event) => formData.value.seekHelp = "肯定会"),
        kx: formData.value.tryToFind === "不会" ? 1 : "",
        ky: common_vendor.o(($event) => formData.value.tryToFind = "不会"),
        kz: formData.value.tryToFind === "偶尔" ? 1 : "",
        kA: common_vendor.o(($event) => formData.value.tryToFind = "偶尔"),
        kB: formData.value.tryToFind === "可能会" ? 1 : "",
        kC: common_vendor.o(($event) => formData.value.tryToFind = "可能会"),
        kD: formData.value.tryToFind === "经常" ? 1 : "",
        kE: common_vendor.o(($event) => formData.value.tryToFind = "经常"),
        kF: formData.value.tryToFind === "肯定会" ? 1 : "",
        kG: common_vendor.o(($event) => formData.value.tryToFind = "肯定会"),
        kH: formData.value.reduceActivity === "不会" ? 1 : "",
        kI: common_vendor.o(($event) => formData.value.reduceActivity = "不会"),
        kJ: formData.value.reduceActivity === "偶尔" ? 1 : "",
        kK: common_vendor.o(($event) => formData.value.reduceActivity = "偶尔"),
        kL: formData.value.reduceActivity === "可能会" ? 1 : "",
        kM: common_vendor.o(($event) => formData.value.reduceActivity = "可能会"),
        kN: formData.value.reduceActivity === "经常" ? 1 : "",
        kO: common_vendor.o(($event) => formData.value.reduceActivity = "经常"),
        kP: formData.value.reduceActivity === "肯定会" ? 1 : "",
        kQ: common_vendor.o(($event) => formData.value.reduceActivity = "肯定会"),
        kR: formData.value.feelBetter === "不会" ? 1 : "",
        kS: common_vendor.o(($event) => formData.value.feelBetter = "不会"),
        kT: formData.value.feelBetter === "偶尔" ? 1 : "",
        kU: common_vendor.o(($event) => formData.value.feelBetter = "偶尔"),
        kV: formData.value.feelBetter === "可能会" ? 1 : "",
        kW: common_vendor.o(($event) => formData.value.feelBetter = "可能会"),
        kX: formData.value.feelBetter === "经常" ? 1 : "",
        kY: common_vendor.o(($event) => formData.value.feelBetter = "经常"),
        kZ: formData.value.feelBetter === "肯定会" ? 1 : "",
        la: common_vendor.o(($event) => formData.value.feelBetter = "肯定会"),
        lb: formData.value.keepStable === "没有信心" ? 1 : "",
        lc: common_vendor.o(($event) => formData.value.keepStable = "没有信心"),
        ld: formData.value.keepStable === "偶尔" ? 1 : "",
        le: common_vendor.o(($event) => formData.value.keepStable = "偶尔"),
        lf: formData.value.keepStable === "有点信心" ? 1 : "",
        lg: common_vendor.o(($event) => formData.value.keepStable = "有点信心"),
        lh: formData.value.keepStable === "经常" ? 1 : "",
        li: common_vendor.o(($event) => formData.value.keepStable = "经常"),
        lj: formData.value.keepStable === "非常有信心" ? 1 : "",
        lk: common_vendor.o(($event) => formData.value.keepStable = "非常有信心"),
        ll: formData.value.followPlan === "没有信心" ? 1 : "",
        lm: common_vendor.o(($event) => formData.value.followPlan = "没有信心"),
        ln: formData.value.followPlan === "偶尔" ? 1 : "",
        lo: common_vendor.o(($event) => formData.value.followPlan = "偶尔"),
        lp: formData.value.followPlan === "有点信心" ? 1 : "",
        lq: common_vendor.o(($event) => formData.value.followPlan = "有点信心"),
        lr: formData.value.followPlan === "经常" ? 1 : "",
        ls: common_vendor.o(($event) => formData.value.followPlan = "经常"),
        lt: formData.value.followPlan === "非常有信心" ? 1 : "",
        lv: common_vendor.o(($event) => formData.value.followPlan = "非常有信心"),
        lw: formData.value.followPlanWithDifficluty === "没有信心" ? 1 : "",
        lx: common_vendor.o(($event) => formData.value.followPlanWithDifficluty = "没有信心"),
        ly: formData.value.followPlanWithDifficluty === "偶尔" ? 1 : "",
        lz: common_vendor.o(($event) => formData.value.followPlanWithDifficluty = "偶尔"),
        lA: formData.value.followPlanWithDifficluty === "有点信心" ? 1 : "",
        lB: common_vendor.o(($event) => formData.value.followPlanWithDifficluty = "有点信心"),
        lC: formData.value.followPlanWithDifficluty === "经常" ? 1 : "",
        lD: common_vendor.o(($event) => formData.value.followPlanWithDifficluty = "经常"),
        lE: formData.value.followPlanWithDifficluty === "非常有信心" ? 1 : "",
        lF: common_vendor.o(($event) => formData.value.followPlanWithDifficluty = "非常有信心"),
        lG: formData.value.regularMonitor === "没有信心" ? 1 : "",
        lH: common_vendor.o(($event) => formData.value.regularMonitor = "没有信心"),
        lI: formData.value.regularMonitor === "偶尔" ? 1 : "",
        lJ: common_vendor.o(($event) => formData.value.regularMonitor = "偶尔"),
        lK: formData.value.regularMonitor === "有点信心" ? 1 : "",
        lL: common_vendor.o(($event) => formData.value.regularMonitor = "有点信心"),
        lM: formData.value.regularMonitor === "经常" ? 1 : "",
        lN: common_vendor.o(($event) => formData.value.regularMonitor = "经常"),
        lO: formData.value.regularMonitor === "非常有信心" ? 1 : "",
        lP: common_vendor.o(($event) => formData.value.regularMonitor = "非常有信心"),
        lQ: formData.value.monitorWithDifficulty === "没有信心" ? 1 : "",
        lR: common_vendor.o(($event) => formData.value.monitorWithDifficulty = "没有信心"),
        lS: formData.value.monitorWithDifficulty === "偶尔" ? 1 : "",
        lT: common_vendor.o(($event) => formData.value.monitorWithDifficulty = "偶尔"),
        lU: formData.value.monitorWithDifficulty === "有点信心" ? 1 : "",
        lV: common_vendor.o(($event) => formData.value.monitorWithDifficulty = "有点信心"),
        lW: formData.value.monitorWithDifficulty === "经常" ? 1 : "",
        lX: common_vendor.o(($event) => formData.value.monitorWithDifficulty = "经常"),
        lY: formData.value.monitorWithDifficulty === "非常有信心" ? 1 : "",
        lZ: common_vendor.o(($event) => formData.value.monitorWithDifficulty = "非常有信心"),
        ma: formData.value.realizeChange === "没有信心" ? 1 : "",
        mb: common_vendor.o(($event) => formData.value.realizeChange = "没有信心"),
        mc: formData.value.realizeChange === "偶尔" ? 1 : "",
        md: common_vendor.o(($event) => formData.value.realizeChange = "偶尔"),
        me: formData.value.realizeChange === "有点信心" ? 1 : "",
        mf: common_vendor.o(($event) => formData.value.realizeChange = "有点信心"),
        mg: formData.value.realizeChange === "经常" ? 1 : "",
        mh: common_vendor.o(($event) => formData.value.realizeChange = "经常"),
        mi: formData.value.realizeChange === "非常有信心" ? 1 : "",
        mj: common_vendor.o(($event) => formData.value.realizeChange = "非常有信心"),
        mk: formData.value.assessSymptom === "没有信心" ? 1 : "",
        ml: common_vendor.o(($event) => formData.value.assessSymptom = "没有信心"),
        mm: formData.value.assessSymptom === "偶尔" ? 1 : "",
        mn: common_vendor.o(($event) => formData.value.assessSymptom = "偶尔"),
        mo: formData.value.assessSymptom === "有点信心" ? 1 : "",
        mp: common_vendor.o(($event) => formData.value.assessSymptom = "有点信心"),
        mq: formData.value.assessSymptom === "经常" ? 1 : "",
        mr: common_vendor.o(($event) => formData.value.assessSymptom = "经常"),
        ms: formData.value.assessSymptom === "非常有信心" ? 1 : "",
        mt: common_vendor.o(($event) => formData.value.assessSymptom = "非常有信心"),
        mv: formData.value.relieveSymptom === "没有信心" ? 1 : "",
        mw: common_vendor.o(($event) => formData.value.relieveSymptom = "没有信心"),
        mx: formData.value.relieveSymptom === "偶尔" ? 1 : "",
        my: common_vendor.o(($event) => formData.value.relieveSymptom = "偶尔"),
        mz: formData.value.relieveSymptom === "有点信心" ? 1 : "",
        mA: common_vendor.o(($event) => formData.value.relieveSymptom = "有点信心"),
        mB: formData.value.relieveSymptom === "经常" ? 1 : "",
        mC: common_vendor.o(($event) => formData.value.relieveSymptom = "经常"),
        mD: formData.value.relieveSymptom === "非常有信心" ? 1 : "",
        mE: common_vendor.o(($event) => formData.value.relieveSymptom = "非常有信心"),
        mF: formData.value.relieveWithDifficulty === "没有信心" ? 1 : "",
        mG: common_vendor.o(($event) => formData.value.relieveWithDifficulty = "没有信心"),
        mH: formData.value.relieveWithDifficulty === "偶尔" ? 1 : "",
        mI: common_vendor.o(($event) => formData.value.relieveWithDifficulty = "偶尔"),
        mJ: formData.value.relieveWithDifficulty === "有点信心" ? 1 : "",
        mK: common_vendor.o(($event) => formData.value.relieveWithDifficulty = "有点信心"),
        mL: formData.value.relieveWithDifficulty === "经常" ? 1 : "",
        mM: common_vendor.o(($event) => formData.value.relieveWithDifficulty = "经常"),
        mN: formData.value.relieveWithDifficulty === "非常有信心" ? 1 : "",
        mO: common_vendor.o(($event) => formData.value.relieveWithDifficulty = "非常有信心"),
        mP: formData.value.judgmentMethod === "没有信心" ? 1 : "",
        mQ: common_vendor.o(($event) => formData.value.judgmentMethod = "没有信心"),
        mR: formData.value.judgmentMethod === "偶尔" ? 1 : "",
        mS: common_vendor.o(($event) => formData.value.judgmentMethod = "偶尔"),
        mT: formData.value.judgmentMethod === "有点信心" ? 1 : "",
        mU: common_vendor.o(($event) => formData.value.judgmentMethod = "有点信心"),
        mV: formData.value.judgmentMethod === "经常" ? 1 : "",
        mW: common_vendor.o(($event) => formData.value.judgmentMethod = "经常"),
        mX: formData.value.judgmentMethod === "非常有信心" ? 1 : "",
        mY: common_vendor.o(($event) => formData.value.judgmentMethod = "非常有信心"),
        mZ: common_vendor.t(mode.value === "add" ? "提交" : "保存"),
        na: common_vendor.o(submitForm)
      });
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-19530caf"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=form.js.map
