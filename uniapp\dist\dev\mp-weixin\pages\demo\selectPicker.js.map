{"version": 3, "file": "selectPicker.js", "sources": ["../../../../../src/pages/demo/selectPicker.vue", "../../../../../uniPage:/cGFnZXMvZGVtby9zZWxlY3RQaWNrZXIudnVl"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n  <PageLayout navTitle=\"单复选择器\" backRouteName=\"demo\">\r\n    <wd-select-picker\r\n      label=\"多选\"\r\n      v-model=\"value\"\r\n      :columns=\"columns\"\r\n      @change=\"handleChange\"\r\n    ></wd-select-picker>\r\n    <wd-select-picker\r\n      label=\"单选\"\r\n      type=\"radio\"\r\n      v-model=\"radioValue\"\r\n      :columns=\"columns\"\r\n      @change=\"handleRadioChange\"\r\n    ></wd-select-picker>\r\n  </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { ref } from 'vue'\r\nimport { useToast } from 'wot-design-uni'\r\nconst { show: showToast } = useToast()\r\nconst columns = ref<any>([\r\n  {\r\n    value: '101',\r\n    label: '男装',\r\n  },\r\n  {\r\n    value: '102',\r\n    label: '奢侈品',\r\n  },\r\n  {\r\n    value: '103',\r\n    label: '女装',\r\n  },\r\n])\r\nconst value = ref<string[]>(['101'])\r\nconst radioValue = ref<string>('101')\r\nconst handleRadioChange = ({ value }) => {}\r\nfunction handleChange({ value }) {\r\n  showToast('选择了' + value)\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n//\r\n</style>\r\n", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages/demo/selectPicker.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useToast", "ref", "value"], "mappings": ";;;;;;;;;;;;;;;;AAyBA,UAAM,EAAE,MAAM,UAAU,IAAIA,uBAAS;AACrC,UAAM,UAAUC,cAAAA,IAAS;AAAA,MACvB;AAAA,QACE,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,OAAO;AAAA,MAAA;AAAA,IACT,CACD;AACD,UAAM,QAAQA,cAAAA,IAAc,CAAC,KAAK,CAAC;AAC7B,UAAA,aAAaA,kBAAY,KAAK;AACpC,UAAM,oBAAoB,CAAC,EAAE,OAAAC,aAAY;AAAA,IAAC;AAC1C,aAAS,aAAa,EAAE,OAAAA,UAAS;AAC/B,gBAAU,QAAQA,MAAK;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3CzB,GAAG,WAAW,eAAe;"}