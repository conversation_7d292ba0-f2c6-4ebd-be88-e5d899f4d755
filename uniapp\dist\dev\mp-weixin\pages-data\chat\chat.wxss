/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.patient-filter-fixed.data-v-d9776e99 {
  position: fixed;
  top: 88px;
  left: 0;
  right: 0;
  background-color: #fff;
  z-index: 999;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 10px;
  box-sizing: border-box;
}
.doctor-filter-fixed.data-v-d9776e99 {
  position: fixed;
  top: 88px;
  left: 0;
  right: 0;
  background-color: #fff;
  z-index: 999;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 10px;
  box-sizing: border-box;
}
.page-scroll-view.data-v-d9776e99 {
  height: calc(100vh - 44px);
  width: 100%;
  padding-bottom: 20rpx;
  box-sizing: border-box;
}
.page-scroll-view.patient-scroll.data-v-d9776e99 {
  padding-top: 220px;
}
.page-scroll-view.doctor-scroll.data-v-d9776e99 {
  padding-top: 200px;
  padding-left: 10rpx;
  padding-right: 10rpx;
}
.search-container.data-v-d9776e99 {
  background: #FFFFFF;
  padding: 12rpx;
  border-radius: 12rpx;
  margin-bottom: 12rpx;
}
.filter-section.data-v-d9776e99 {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 12rpx;
  margin-bottom: 8rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.date-filter.data-v-d9776e99 {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  gap: 12rpx;
}
.filter-label.data-v-d9776e99 {
  color: #333333;
  font-size: 28rpx;
  font-weight: 500;
  flex-shrink: 0;
}
.date-picker.data-v-d9776e99 {
  flex: 1;
  min-width: 180rpx;
  max-width: 200rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #F7F7F7;
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
}
.date-picker text.data-v-d9776e99 {
  color: #666666;
  font-size: 24rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.date-separator.data-v-d9776e99 {
  color: #666666;
  font-size: 24rpx;
  flex-shrink: 0;
}
.reset-btn.data-v-d9776e99 {
  width: 35rpx;
  height: 35rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border: none;
  transition: all 0.3s ease;
  cursor: pointer;
  flex-shrink: 0;
}
.reset-btn.data-v-d9776e99:active {
  transform: scale(0.85) rotate(360deg);
  opacity: 0.7;
}
.reset-btn.data-v-d9776e99:hover {
  transform: scale(1.1);
  opacity: 0.8;
}
.reset-btn.refreshing.data-v-d9776e99 {
  animation: refresh-spin-d9776e99 1s linear infinite;
}
@keyframes refresh-spin-d9776e99 {
from {
    transform: rotate(0deg);
}
to {
    transform: rotate(360deg);
}
}
.search-row.data-v-d9776e99 {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 15rpx;
  margin-bottom: 30rpx; /* 添加底部间距，分隔不同的输入框 */
}
.search-row.data-v-d9776e99:last-of-type {
  margin-bottom: 0; /* 最后一个输入框不需要底部间距 */
}
.search-label.data-v-d9776e99 {
  color: #333333;
  font-size: 28rpx;
  font-weight: 500;
  white-space: nowrap;
  flex-shrink: 0;
}
.search-input-container.data-v-d9776e99 {
  display: flex;
  align-items: center;
  position: relative;
  flex: 1;
}
.search-input.data-v-d9776e99 {
  height: 80rpx;
  background: #F5F5F5;
  border-radius: 8rpx;
  padding: 0 60rpx 0 20rpx;
  font-size: 28rpx;
  border: none;
  outline: none;
  box-sizing: border-box;
  flex: 1;
  overflow: hidden;
  text-overflow: clip;
  white-space: nowrap;
}

/* 姓名输入框和问题输入框 - 保持相同的宽度 */
.search-input.name-input.data-v-d9776e99,
.search-input.question-input.data-v-d9776e99 {
  flex: 1;
  width: 100%;
}
.clear-btn.data-v-d9776e99 {
  position: absolute;
  right: 15rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  color: #666666;
  font-size: 24rpx;
  font-weight: bold;
  z-index: 10;
  cursor: pointer;
  transition: all 0.2s ease;
}
.clear-btn.data-v-d9776e99:active {
  background: rgba(0, 0, 0, 0.2);
  transform: translateY(-50%) scale(0.9);
}
.button-row.data-v-d9776e99 {
  display: flex;
  gap: 20rpx;
  margin-top: 20rpx;
}
.search-button.data-v-d9776e99 {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #07C160;
  height: 80rpx;
  border-radius: 8rpx;
  cursor: pointer;
  padding: 0 30rpx;
  flex: 1;
  vertical-align: middle;
}
.search-button-text.data-v-d9776e99 {
  color: #FFFFFF;
  font-size: 28rpx;
  margin-left: 10rpx;
}
.clear-all-button.data-v-d9776e99 {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #F5F5F5;
  height: 80rpx;
  border-radius: 8rpx;
  cursor: pointer;
  padding: 0 30rpx;
  flex: 1;
  border: 1px solid #E0E0E0;
}
.clear-all-button-text.data-v-d9776e99 {
  color: #666666;
  font-size: 28rpx;
  margin-left: 10rpx;
}
.status-radio-container.data-v-d9776e99 {
  display: flex;
  flex: 1;
  gap: 30rpx;
  align-items: center;
}
.radio-option.data-v-d9776e99 {
  display: flex;
  align-items: center;
  cursor: pointer;
  gap: 12rpx;
}
.radio-circle.data-v-d9776e99 {
  width: 36rpx;
  height: 36rpx;
  border: 3rpx solid #D0D0D0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.radio-checked.data-v-d9776e99 {
  border-color: #07C160;
}
.radio-dot.data-v-d9776e99 {
  width: 20rpx;
  height: 20rpx;
  background-color: #07C160;
  border-radius: 50%;
}
.mention-filter-container.data-v-d9776e99 {
  display: flex;
  align-items: center;
}
.mention-filter-option.data-v-d9776e99 {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
}
.mention-filter-option.data-v-d9776e99:hover {
  opacity: 0.8;
}
.mention-checkbox.data-v-d9776e99 {
  width: 36rpx;
  height: 36rpx;
  border: 3rpx solid #D0D0D0;
  border-radius: 8rpx;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}
.mention-checkbox.mention-checked.data-v-d9776e99 {
  background-color: #07C160;
  border-color: #07C160;
}
.mention-label.data-v-d9776e99 {
  font-size: 28rpx;
  color: #333;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.mention-filter-option.mention-active .mention-label.data-v-d9776e99 {
  color: #07C160;
  font-weight: 500;
}
.radio-label.data-v-d9776e99 {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}
.add-btn.data-v-d9776e99 {
  background-color: #07C160;
  color: #FFFFFF;
  font-size: 28rpx;
  padding: 15rpx 25rpx;
  border-radius: 8rpx;
  margin: 20rpx;
}
.add-btn.data-v-d9776e99:disabled {
  background-color: #cccccc;
}
.scrollView.data-v-d9776e99 {
  padding: 0 10rpx;
  padding-top: 0;
}
.empty-tip.data-v-d9776e99 {
  color: #999;
  text-align: center;
  margin: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15rpx;
  padding: 15rpx;
}
.empty-text.data-v-d9776e99 {
  font-size: 34rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
  word-wrap: break-word;
}
.empty-hint.data-v-d9776e99 {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
  word-wrap: break-word;
}
.retry-button.data-v-d9776e99 {
  margin-top: 30rpx;
  padding: 16rpx 32rpx;
  background-color: #07C160;
  border-radius: 8rpx;
  cursor: pointer;
  transition: background-color 0.2s;
}
.retry-button.data-v-d9776e99:active {
  background-color: #06A050;
}
.retry-text.data-v-d9776e99 {
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
}
.loading-container.data-v-d9776e99 {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30rpx 20rpx;
  min-height: 100rpx;
}
.chat-list.data-v-d9776e99 {
  padding: 0 20rpx;
  padding-bottom: 40rpx;
  margin-top: 70px;
}
.chat-item.data-v-d9776e99:last-child {
  margin-bottom: 60rpx;
}
.chat-item.data-v-d9776e99 {
  min-height: 160rpx;
  height: auto;
  border-radius: 16rpx;
  margin: 8rpx 16rpx;
  display: flex;
  align-items: flex-start;
  padding: 20rpx 24rpx;
  background: #ffffff;
  border-bottom: 1px solid #f0f0f0;
  transition: background 0.2s;
  position: relative;
  box-sizing: border-box;
}
.chat-item.data-v-d9776e99:active {
  background: #e6e6e6;
}
.chat-avatar.data-v-d9776e99 {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  margin-right: 32rpx;
  margin-top: 0;
  -o-object-fit: cover;
     object-fit: cover;
  background: #eee;
  flex-shrink: 0;
  align-self: flex-start;
}
.chat-content.data-v-d9776e99 {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  min-width: 0;
  align-self: flex-start;
}
.chat-row.data-v-d9776e99 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  margin-bottom: 12rpx;
  margin-top: 0;
}
.chat-title-row.data-v-d9776e99 {
  width: 100%;
  margin: 12rpx 0;
}
.chat-title.data-v-d9776e99 {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
  line-height: 1.3;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.chat-bottom-row.data-v-d9776e99 {
  display: flex;
  width: 100%;
  padding-right: 140rpx;
  margin-top: 8rpx;
}
.chat-nickname-container.data-v-d9776e99 {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-left: 0;
  min-width: 0;
  align-self: flex-start;
}
.chat-nickname.data-v-d9776e99 {
  font-size: 32rpx;
  color: #222;
  font-weight: 500;
  line-height: 1.2;
  margin-top: 0;
  padding-top: 0;
}
.reply-text.data-v-d9776e99 {
  font-size: 24rpx;
  color: #666;
  margin-top: 4rpx;
  line-height: 1.3;
  display: flex;
  flex-wrap: wrap;
  align-items: baseline;
}
.reply-target.data-v-d9776e99 {
  color: #07C160;
  font-weight: 500;
}
.reply-content.data-v-d9776e99 {
  color: #999;
  font-style: italic;
  margin-left: 4rpx;
  word-break: break-all;
  flex: 1;
}
.chat-time.data-v-d9776e99 {
  font-size: 24rpx;
  color: #999;
  margin-left: 16rpx;
  flex-shrink: 0;
  line-height: 1.2;
}
.reply-status-badge.data-v-d9776e99 {
  position: absolute;
  bottom: 24rpx;
  right: 32rpx;
  padding: 8rpx 18rpx;
  border-radius: 18rpx;
  font-size: 22rpx;
  font-weight: 500;
  text-align: center;
  min-width: 80rpx;
  box-sizing: border-box;
  z-index: 1;
}
.status-pending.data-v-d9776e99 {
  background-color: #ffebee;
  border: 1rpx solid #ffcdd2;
}
.status-pending .status-text.data-v-d9776e99 {
  color: #d32f2f;
}
.status-replied.data-v-d9776e99 {
  background-color: #e8f5e8;
  border: 1rpx solid #c8e6c9;
}
.status-replied .status-text.data-v-d9776e99 {
  color: #2e7d32;
}
.status-text.data-v-d9776e99 {
  font-size: 22rpx;
  font-weight: 500;
  line-height: 1.2;
}
.chat-msg.data-v-d9776e99 {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
  flex: 1;
  word-wrap: break-word;
  word-break: break-all;
}
.mentioned-doctors-row.data-v-d9776e99 {
  display: flex;
  align-items: center;
  margin: 8rpx 0;
  padding: 8rpx 12rpx;
}
.mentioned-doctors.data-v-d9776e99 {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  flex: 1;
}
.mentioned-doctor.data-v-d9776e99 {
  font-size: 24rpx;
  color: #07C160;
  font-weight: 500;
  background-color: rgba(7, 193, 96, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  margin-right: 4rpx;
  margin-bottom: 2rpx;
}