"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../../common/vendor.js");
const utils_http = require("../../../utils/http.js");
if (!Array) {
  const _easycom_wd_select_picker2 = common_vendor.resolveComponent("wd-select-picker");
  _easycom_wd_select_picker2();
}
const _easycom_wd_select_picker = () => "../../../node-modules/wot-design-uni/components/wd-select-picker/wd-select-picker.js";
if (!Math) {
  _easycom_wd_select_picker();
}
const _sfc_main = {
  __name: "online-checkbox",
  props: {
    dict: {
      type: [Array, String],
      default: () => [],
      required: true
    },
    label: {
      type: String,
      default: "",
      required: false
    },
    labelWidth: {
      type: String,
      default: "80px",
      required: false
    },
    name: {
      type: String,
      default: "",
      required: false
    },
    dictStr: {
      type: String,
      default: "",
      required: false
    },
    type: {
      type: String,
      default: "",
      required: false
    },
    value: {
      type: [Array, String],
      required: false
    },
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  emits: ["input", "change", "update:value"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const selected = common_vendor.ref([]);
    const options = common_vendor.ref([]);
    const initSelections = () => __async(this, null, function* () {
      options.value = [];
      if (props.type === "sel_search" && props.dictStr) {
        let temp = props.dictStr;
        if (temp.indexOf(" ") > 0) {
          temp = encodeURI(props.dictStr);
        }
        try {
          const res = yield utils_http.http.get("/sys/dict/getDictItems/" + temp);
          if (res.success) {
            options.value = res.result;
          }
        } catch (error) {
          console.error("请求数据出错:", error);
        }
      } else {
        if (!props.dict || props.dict.length === 0) {
          return;
        }
        if (common_vendor.lodashExports.isString(props.dict)) {
          try {
            const res = yield utils_http.http.get("/sys/dict/getDictItems/" + props.dict);
            if (res.success) {
              options.value = res.result;
            }
          } catch (error) {
            console.error("请求数据出错:", error);
          }
        } else {
          props.dict.forEach((item) => {
            options.value.push(item);
          });
        }
      }
      console.log("options.value ", options.value);
    });
    const handleChange = (e) => {
      let value = "";
      if (selected.value && common_vendor.lodashExports.isArray(selected.value)) {
        value = selected.value.join(",");
      }
      emit("update:value", value);
      emit("change", value);
    };
    common_vendor.watch(() => props.dict, () => {
      initSelections();
    });
    common_vendor.watch(
      () => props.value,
      (val) => {
        selected.value = !val ? [] : val.split(",");
      },
      { immediate: true }
    );
    common_vendor.onMounted(() => {
      initSelections();
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(handleChange),
        b: common_vendor.o(($event) => selected.value = $event),
        c: common_vendor.p({
          ["label-width"]: __props.labelWidth,
          label: __props.label,
          filterable: true,
          clearable: true,
          columns: options.value,
          disabled: __props.disabled,
          placeholder: "请选择",
          modelValue: selected.value
        })
      };
    };
  }
};
wx.createComponent(_sfc_main);
//# sourceMappingURL=online-checkbox.js.map
