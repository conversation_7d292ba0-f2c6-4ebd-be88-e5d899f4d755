{"version": 3, "file": "wd-datetime-picker-view.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-datetime-picker-view/wd-datetime-picker-view.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1kYXRldGltZS1waWNrZXItdmlldy93ZC1kYXRldGltZS1waWNrZXItdmlldy52dWU"], "sourcesContent": ["<template>\n  <view>\n    <wd-picker-view\n      ref=\"datePickerview\"\n      :custom-class=\"customClass\"\n      :custom-style=\"customStyle\"\n      :immediate-change=\"immediateChange\"\n      v-model=\"pickerValue\"\n      :columns=\"columns\"\n      :columns-height=\"columnsHeight\"\n      :columnChange=\"columnChange\"\n      :loading=\"loading\"\n      :loading-color=\"loadingColor\"\n      @change=\"onChange\"\n      @pickstart=\"onPickStart\"\n      @pickend=\"onPickEnd\"\n    ></wd-picker-view>\n  </view>\n</template>\n<script lang=\"ts\">\nexport default {\n  name: 'wd-datetime-picker-view',\n  virtualHost: true,\n  addGlobalClass: true,\n  styleIsolation: 'shared'\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport wdPickerView from '../wd-picker-view/wd-picker-view.vue'\nimport { getCurrentInstance, onBeforeMount, ref, watch } from 'vue'\nimport { debounce, isFunction, isDef, padZero, range, isArray, isString } from '../common/util'\nimport {\n  getPickerValue,\n  datetimePickerViewProps,\n  type DatetimePickerViewColumnType,\n  type DatetimePickerViewOption,\n  type DatetimePickerViewExpose\n} from './types'\nimport type { PickerViewInstance } from '../wd-picker-view/types'\n\n// 本地时间戳\n/** @description 判断时间戳是否合法 */\nconst isValidDate = (date: string | number | Date) => isDef(date) && !Number.isNaN(date)\n/**\n * @description 生成n个元素，并使用iterator接口进行填充\n * @param n\n * @param iteratee\n * @return {any[]}\n */\nconst times = (n: number, iteratee: (index: number) => number) => {\n  let index: number = -1\n  const length = n < 0 ? 0 : n\n  const result: number[] = Array(length)\n  while (++index < n) {\n    result[index] = iteratee(index)\n  }\n  return result\n}\n/**\n * @description 获取某年某月有多少天\n * @param {Number} year\n * @param {Number} month\n * @return {Number} day\n */\nconst getMonthEndDay = (year: number, month: number) => {\n  return 32 - new Date(year, month - 1, 32).getDate()\n}\n\nconst props = defineProps(datetimePickerViewProps)\nconst emit = defineEmits(['change', 'pickstart', 'pickend', 'update:modelValue'])\n\n// pickerview\nconst datePickerview = ref<PickerViewInstance>()\n// 内部保持时间戳的\nconst innerValue = ref<null | string | number>(null)\n// 传递给pickerView的columns的数据\nconst columns = ref<DatetimePickerViewOption[][]>([])\n// 传递给pickerView的value的数据\nconst pickerValue = ref<string | number | boolean | string[] | number[] | boolean[]>([])\n// 自定义组件是否已经调用created hook\nconst created = ref<boolean>(false)\n\nconst { proxy } = getCurrentInstance() as any\n\ndefineExpose<DatetimePickerViewExpose>({\n  updateColumns,\n  setColumns,\n  getSelects,\n  correctValue,\n  getPickerValue,\n  getOriginColumns,\n  ...props\n})\n/**\n * @description updateValue 防抖函数的占位符\n */\nconst updateValue = debounce(() => {\n  // 只有等created hook初始化数据之后，observer才能执行此操作\n  if (!created.value) return\n  const val = correctValue(props.modelValue)\n  const isEqual = val === innerValue.value\n  if (!isEqual) {\n    updateColumnValue(val)\n  } else {\n    columns.value = updateColumns()\n  }\n}, 50)\n\nwatch(\n  () => props.modelValue,\n  (val, oldVal) => {\n    if (val === oldVal) return\n    // 外部传入值更改时 更新picker数据\n    const value = correctValue(val)\n    updateColumnValue(value)\n  },\n  { deep: true, immediate: true }\n)\n\nwatch(\n  () => props.type,\n  (target) => {\n    const type = ['date', 'year-month', 'time', 'datetime', 'year']\n    if (type.indexOf(target) === -1) {\n      console.error(`type must be one of ${type}`)\n    }\n    // 每次type更新时都需要刷新整个列表\n    updateValue()\n  },\n  { deep: true, immediate: true }\n)\n\nwatch(\n  () => props.filter,\n  (fn) => {\n    if (fn && !isFunction(fn)) {\n      console.error('The type of filter must be Function')\n    }\n    updateValue()\n  },\n  { deep: true, immediate: true }\n)\n\nwatch(\n  () => props.formatter,\n  (fn) => {\n    if (fn && !isFunction(fn)) {\n      console.error('The type of formatter must be Function')\n    }\n    updateValue()\n  },\n  { deep: true, immediate: true }\n)\n\nwatch(\n  () => props.columnFormatter,\n  (fn) => {\n    if (fn && !isFunction(fn)) {\n      console.error('The type of columnFormatter must be Function')\n    }\n    updateValue()\n  },\n  { deep: true, immediate: true }\n)\n\nwatch(\n  [\n    () => props.minDate,\n    () => props.maxDate,\n    () => props.minHour,\n    () => props.maxHour,\n    () => props.minMinute,\n    () => props.minMinute,\n    () => props.maxMinute\n  ],\n  () => {\n    updateValue()\n  },\n  {\n    deep: true,\n    immediate: true\n  }\n)\n\nonBeforeMount(() => {\n  // 初始化完毕，打开observer触发render的开关\n  created.value = true\n  const innerValue = correctValue(props.modelValue)\n  updateColumnValue(innerValue)\n})\n\n/** pickerView触发change事件，同步修改pickerValue */\nfunction onChange({ value }: { value: string | string[] }) {\n  // 更新pickerView的value\n  pickerValue.value = value\n  // pickerValue => innerValue\n  const result = updateInnerValue()\n  emit('update:modelValue', result)\n  // 这个地方的value返回的是picker数组，实际上在此处我们应该返回 change 的是 value date类型的值\n  emit('change', {\n    value: result,\n    picker: proxy.$.exposed\n  })\n}\n\n/**\n * @description 使用formatter格式化getOriginColumns的结果\n * @return {Array<Array<Number>>} 用于传入picker的columns\n */\nfunction updateColumns(): DatetimePickerViewOption[][] {\n  const { formatter, columnFormatter } = props\n  if (columnFormatter) {\n    return columnFormatter(proxy.$.exposed)\n  } else {\n    return getOriginColumns().map((column) => {\n      return column.values.map((value) => {\n        return {\n          label: formatter ? formatter(column.type, padZero(value)) : padZero(value),\n          value\n        }\n      })\n    })\n  }\n}\n\n/**\n * 设置数据列\n * @param columnList 数据列\n */\nfunction setColumns(columnList: DatetimePickerViewOption[][]) {\n  columns.value = columnList\n}\n\n/**\n * @description 根据getRanges得到的范围计算所有的列的数据\n * @return {{values: any[], type: String}[]} 年\n */\nfunction getOriginColumns() {\n  const { filter } = props\n  return getRanges().map(({ type, range }) => {\n    let values = times(range[1] - range[0] + 1, (index: number) => {\n      return range[0] + index\n    })\n\n    if (filter) {\n      values = filter(type, values)\n    }\n\n    return {\n      type,\n      values\n    }\n  })\n}\n\n/**\n * @description 根据时间戳生成年月日时分的边界范围\n * @return {Array<{type:String,range:Array<Number>}>}\n */\nfunction getRanges(): Array<{ type: DatetimePickerViewColumnType; range: number[] }> {\n  if (props.type === 'time') {\n    return [\n      {\n        type: 'hour',\n        range: [props.minHour, props.maxHour]\n      },\n      {\n        type: 'minute',\n        range: [props.minMinute, props.maxMinute]\n      }\n    ]\n  }\n\n  const { maxYear, maxDate, maxMonth, maxHour, maxMinute } = getBoundary('max', innerValue.value as number)\n  const { minYear, minDate, minMonth, minHour, minMinute } = getBoundary('min', innerValue.value as number)\n\n  const result: Array<{ type: DatetimePickerViewColumnType; range: number[] }> = [\n    {\n      type: 'year',\n      range: [minYear, maxYear]\n    },\n    {\n      type: 'month',\n      range: [minMonth, maxMonth]\n    },\n    {\n      type: 'date',\n      range: [minDate, maxDate]\n    },\n    {\n      type: 'hour',\n      range: [minHour, maxHour]\n    },\n    {\n      type: 'minute',\n      range: [minMinute, maxMinute]\n    }\n  ]\n\n  if (props.type === 'date') result.splice(3, 2)\n  if (props.type === 'year-month') result.splice(2, 3)\n  if (props.type === 'year') result.splice(1, 4)\n  return result\n}\n\n/**\n * @description 修正时间入参，判定是否为规范时间类型\n * @param {String | Number} value\n * @return {String | Number} innerValue\n */\nfunction correctValue(value: string | number | Date): string | number {\n  const isDateType = props.type !== 'time'\n  if (isDateType && !isValidDate(value)) {\n    // 是Date类型，但入参不可用，使用最小时间戳代替\n    value = props.minDate\n  } else if (!isDateType && !value) {\n    // 非Date类型，无入参，使用最小小时代替\n    value = `${padZero(props.minHour)}:00`\n  }\n\n  // 当type为time时\n  if (!isDateType) {\n    // 非Date类型，直接走此逻辑\n    let [hour, minute] = (isString(value) ? value : value.toString()).split(':')\n    hour = padZero(range(Number(hour), props.minHour, props.maxHour))\n    minute = padZero(range(Number(minute), props.minMinute, props.maxMinute))\n    return `${hour}:${minute}`\n  }\n\n  // date type\n  value = Math.min(Math.max(Number(value), props.minDate), props.maxDate)\n\n  return value\n}\n\n/**\n * @description 根据时间戳，计算所有选项的范围\n * @param {'min'|'max'} type 类型\n * @param {Number} innerValue 时间戳\n */\nfunction getBoundary(type: 'min' | 'max', innerValue: number) {\n  const value = new Date(innerValue)\n  const boundary = new Date(props[`${type}Date`])\n  const year = boundary.getFullYear()\n  let month: number = 1\n  let date: number = 1\n  let hour: number = 0\n  let minute: number = 0\n\n  if (type === 'max') {\n    month = 12\n    date = getMonthEndDay(value.getFullYear(), value.getMonth() + 1)\n    hour = 23\n    minute = 59\n  }\n\n  if (value.getFullYear() === year) {\n    month = boundary.getMonth() + 1\n    if (value.getMonth() + 1 === month) {\n      date = boundary.getDate()\n      if (value.getDate() === date) {\n        hour = boundary.getHours()\n        if (value.getHours() === hour) {\n          minute = boundary.getMinutes()\n        }\n      }\n    }\n  }\n  return {\n    [`${type}Year`]: year,\n    [`${type}Month`]: month,\n    [`${type}Date`]: date,\n    [`${type}Hour`]: hour,\n    [`${type}Minute`]: minute\n  }\n}\n\n/**\n * @description 根据传入的value以及type，初始化innerValue，期间会使用format格式化数据\n * @param value\n * @return {Array}\n */\nfunction updateColumnValue(value: string | number) {\n  const values = getPickerValue(value, props.type)\n  // 更新pickerView的value,columns\n  if (props.modelValue !== value) {\n    emit('update:modelValue', value)\n    emit('change', {\n      value,\n      picker: proxy.$.exposed\n    })\n  }\n  innerValue.value = value\n  columns.value = updateColumns()\n  pickerValue.value = values\n}\n\n/**\n * @description 根据当前的选中项 处理innerValue\n * @return {date} innerValue\n */\nfunction updateInnerValue() {\n  const { type } = props\n  let innerValue: string | number = ''\n  const pickerVal = datePickerview.value?.getValues() || []\n  const values = isArray(pickerVal) ? pickerVal : [pickerVal]\n\n  if (type === 'time') {\n    innerValue = `${padZero(values[0])}:${padZero(values[1])}`\n    return innerValue\n  }\n\n  // 处理年份 索引位0\n  const year = values[0] && parseInt(values[0])\n\n  // 处理月 索引位1\n  const month = type === 'year' ? 1 : values[1] && parseInt(values[1])\n\n  const maxDate = getMonthEndDay(Number(year), Number(month))\n\n  // 处理 date 日期 索引位2\n  let date: string | number = 1\n  if (type !== 'year-month' && type !== 'year') {\n    date = (Number(values[2]) && parseInt(String(values[2]))) > maxDate ? maxDate : values[2] && parseInt(String(values[2]))\n  }\n\n  // 处理 时分 索引位3，4\n  let hour = 0\n  let minute = 0\n\n  if (type === 'datetime') {\n    hour = Number(values[3]) && parseInt(values[3])\n    minute = Number(values[4]) && parseInt(values[4])\n  }\n  const value = new Date(Number(year), Number(month) - 1, Number(date), hour, minute).getTime()\n\n  innerValue = correctValue(value)\n  return innerValue\n}\n\n/**\n * @description 选中项改变，多级联动\n */\nfunction columnChange(picker: PickerViewInstance) {\n  // time year-mouth year 无需联动\n  if (props.type === 'time' || props.type === 'year-month' || props.type === 'year') {\n    return\n  }\n  /** 重新计算年月日时分秒，修正时间。 */\n  const values = picker.getValues() as string[]\n  const year = Number(values[0])\n  const month = Number(values[1])\n  const maxDate = getMonthEndDay(year, month)\n  let date = Number(values[2])\n  date = date > maxDate ? maxDate : date\n  let hour: number = 0\n  let minute: number = 0\n  if (props.type === 'datetime') {\n    hour = Number(values[3])\n    minute = Number(values[4])\n  }\n  const value = new Date(year, month - 1, date, hour, minute).getTime()\n  /** 根据计算选中项的时间戳，重新计算所有的选项列表 */\n  // 更新选中时间戳\n  innerValue.value = correctValue(value)\n  // 根据innerValue获取最新的时间表，重新生成对应的数据源\n\n  const newColumns = updateColumns()\n  // 深拷贝联动之前的选中项\n  const selectedIndex = picker.getSelectedIndex().slice(0)\n  /**\n   * 选中年会修改对应的年份的月数，和月份对应的日期。\n   * 选中月，会修改月份对应的日数\n   */\n  newColumns.forEach((_columns, index) => {\n    const nextColumnIndex = index + 1\n    const nextColumnData = newColumns[nextColumnIndex]\n    if (nextColumnIndex > newColumns.length - 1) return\n    picker.setColumnData(\n      nextColumnIndex,\n      nextColumnData,\n      selectedIndex[nextColumnIndex] <= nextColumnData.length - 1 ? selectedIndex[nextColumnIndex] : 0\n    )\n  })\n}\nfunction onPickStart() {\n  emit('pickstart')\n}\nfunction onPickEnd() {\n  emit('pickend')\n}\n\nfunction getSelects() {\n  const pickerVal = datePickerview.value?.getSelects()\n  if (pickerVal == null) return undefined\n  if (isArray(pickerVal)) return pickerVal\n  return [pickerVal]\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-datetime-picker-view/wd-datetime-picker-view.vue'\nwx.createComponent(Component)"], "names": ["isDef", "ref", "getCurrentInstance", "getPickerValue", "debounce", "watch", "isFunction", "onBeforeMount", "innerValue", "padZero", "range", "isString", "isArray"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AA6BA,MAAA,eAAyB,MAAA;AATzB,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,gBAAgB;AAClB;;;;;AAkBM,UAAA,cAAc,CAAC,SAAiCA,cAAA,MAAM,IAAI,KAAK,CAAC,OAAO,MAAM,IAAI;AAOjF,UAAA,QAAQ,CAAC,GAAW,aAAwC;AAChE,UAAI,QAAgB;AACd,YAAA,SAAS,IAAI,IAAI,IAAI;AACrB,YAAA,SAAmB,MAAM,MAAM;AAC9B,aAAA,EAAE,QAAQ,GAAG;AACX,eAAA,KAAK,IAAI,SAAS,KAAK;AAAA,MAAA;AAEzB,aAAA;AAAA,IACT;AAOM,UAAA,iBAAiB,CAAC,MAAc,UAAkB;AAC/C,aAAA,KAAK,IAAI,KAAK,MAAM,QAAQ,GAAG,EAAE,EAAE,QAAQ;AAAA,IACpD;AAEA,UAAM,QAAQ;AACd,UAAM,OAAO;AAGb,UAAM,iBAAiBC,cAAAA,IAAwB;AAEzC,UAAA,aAAaA,kBAA4B,IAAI;AAE7C,UAAA,UAAUA,cAAkC,IAAA,EAAE;AAE9C,UAAA,cAAcA,cAAiE,IAAA,EAAE;AAEjF,UAAA,UAAUA,kBAAa,KAAK;AAE5B,UAAA,EAAE,MAAM,IAAIC,iCAAmB;AAEE,aAAA;AAAA,MACrC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MAAA,gBACAC,cAAA;AAAA,MACA;AAAA,OACG,MACJ;AAIK,UAAA,cAAcC,cAAAA,SAAS,MAAM;AAEjC,UAAI,CAAC,QAAQ;AAAO;AACd,YAAA,MAAM,aAAa,MAAM,UAAU;AACnC,YAAA,UAAU,QAAQ,WAAW;AACnC,UAAI,CAAC,SAAS;AACZ,0BAAkB,GAAG;AAAA,MAAA,OAChB;AACL,gBAAQ,QAAQ,cAAc;AAAA,MAAA;AAAA,OAE/B,EAAE;AAELC,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,KAAK,WAAW;AACf,YAAI,QAAQ;AAAQ;AAEd,cAAA,QAAQ,aAAa,GAAG;AAC9B,0BAAkB,KAAK;AAAA,MACzB;AAAA,MACA,EAAE,MAAM,MAAM,WAAW,KAAK;AAAA,IAChC;AAEAA,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,WAAW;AACV,cAAM,OAAO,CAAC,QAAQ,cAAc,QAAQ,YAAY,MAAM;AAC9D,YAAI,KAAK,QAAQ,MAAM,MAAM,IAAI;AACvB,kBAAA,MAAM,uBAAuB,IAAI,EAAE;AAAA,QAAA;AAGjC,oBAAA;AAAA,MACd;AAAA,MACA,EAAE,MAAM,MAAM,WAAW,KAAK;AAAA,IAChC;AAEAA,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,OAAO;AACN,YAAI,MAAM,CAACC,yBAAW,EAAE,GAAG;AACzB,kBAAQ,MAAM,qCAAqC;AAAA,QAAA;AAEzC,oBAAA;AAAA,MACd;AAAA,MACA,EAAE,MAAM,MAAM,WAAW,KAAK;AAAA,IAChC;AAEAD,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,OAAO;AACN,YAAI,MAAM,CAACC,yBAAW,EAAE,GAAG;AACzB,kBAAQ,MAAM,wCAAwC;AAAA,QAAA;AAE5C,oBAAA;AAAA,MACd;AAAA,MACA,EAAE,MAAM,MAAM,WAAW,KAAK;AAAA,IAChC;AAEAD,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,OAAO;AACN,YAAI,MAAM,CAACC,yBAAW,EAAE,GAAG;AACzB,kBAAQ,MAAM,8CAA8C;AAAA,QAAA;AAElD,oBAAA;AAAA,MACd;AAAA,MACA,EAAE,MAAM,MAAM,WAAW,KAAK;AAAA,IAChC;AAEAD,kBAAA;AAAA,MACE;AAAA,QACE,MAAM,MAAM;AAAA,QACZ,MAAM,MAAM;AAAA,QACZ,MAAM,MAAM;AAAA,QACZ,MAAM,MAAM;AAAA,QACZ,MAAM,MAAM;AAAA,QACZ,MAAM,MAAM;AAAA,QACZ,MAAM,MAAM;AAAA,MACd;AAAA,MACA,MAAM;AACQ,oBAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,WAAW;AAAA,MAAA;AAAA,IAEf;AAEAE,kBAAAA,cAAc,MAAM;AAElB,cAAQ,QAAQ;AACVC,YAAAA,cAAa,aAAa,MAAM,UAAU;AAChD,wBAAkBA,WAAU;AAAA,IAAA,CAC7B;AAGQ,aAAA,SAAS,EAAE,SAAuC;AAEzD,kBAAY,QAAQ;AAEpB,YAAM,SAAS,iBAAiB;AAChC,WAAK,qBAAqB,MAAM;AAEhC,WAAK,UAAU;AAAA,QACb,OAAO;AAAA,QACP,QAAQ,MAAM,EAAE;AAAA,MAAA,CACjB;AAAA,IAAA;AAOH,aAAS,gBAA8C;AAC/C,YAAA,EAAE,WAAW,gBAAA,IAAoB;AACvC,UAAI,iBAAiB;AACZ,eAAA,gBAAgB,MAAM,EAAE,OAAO;AAAA,MAAA,OACjC;AACL,eAAO,iBAAiB,EAAE,IAAI,CAAC,WAAW;AACxC,iBAAO,OAAO,OAAO,IAAI,CAAC,UAAU;AAC3B,mBAAA;AAAA,cACL,OAAO,YAAY,UAAU,OAAO,MAAMC,sBAAQ,KAAK,CAAC,IAAIA,cAAA,QAAQ,KAAK;AAAA,cACzE;AAAA,YACF;AAAA,UAAA,CACD;AAAA,QAAA,CACF;AAAA,MAAA;AAAA,IACH;AAOF,aAAS,WAAW,YAA0C;AAC5D,cAAQ,QAAQ;AAAA,IAAA;AAOlB,aAAS,mBAAmB;AACpB,YAAA,EAAE,WAAW;AACZ,aAAA,YAAY,IAAI,CAAC,EAAE,MAAM,OAAAC,aAAY;AACtC,YAAA,SAAS,MAAMA,OAAM,CAAC,IAAIA,OAAM,CAAC,IAAI,GAAG,CAAC,UAAkB;AACtDA,iBAAAA,OAAM,CAAC,IAAI;AAAA,QAAA,CACnB;AAED,YAAI,QAAQ;AACD,mBAAA,OAAO,MAAM,MAAM;AAAA,QAAA;AAGvB,eAAA;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MAAA,CACD;AAAA,IAAA;AAOH,aAAS,YAA4E;AAC/E,UAAA,MAAM,SAAS,QAAQ;AAClB,eAAA;AAAA,UACL;AAAA,YACE,MAAM;AAAA,YACN,OAAO,CAAC,MAAM,SAAS,MAAM,OAAO;AAAA,UACtC;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,OAAO,CAAC,MAAM,WAAW,MAAM,SAAS;AAAA,UAAA;AAAA,QAE5C;AAAA,MAAA;AAGI,YAAA,EAAE,SAAS,SAAS,UAAU,SAAS,UAAc,IAAA,YAAY,OAAO,WAAW,KAAe;AAClG,YAAA,EAAE,SAAS,SAAS,UAAU,SAAS,UAAc,IAAA,YAAY,OAAO,WAAW,KAAe;AAExG,YAAM,SAAyE;AAAA,QAC7E;AAAA,UACE,MAAM;AAAA,UACN,OAAO,CAAC,SAAS,OAAO;AAAA,QAC1B;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,OAAO,CAAC,UAAU,QAAQ;AAAA,QAC5B;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,OAAO,CAAC,SAAS,OAAO;AAAA,QAC1B;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,OAAO,CAAC,SAAS,OAAO;AAAA,QAC1B;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,OAAO,CAAC,WAAW,SAAS;AAAA,QAAA;AAAA,MAEhC;AAEA,UAAI,MAAM,SAAS;AAAe,eAAA,OAAO,GAAG,CAAC;AAC7C,UAAI,MAAM,SAAS;AAAqB,eAAA,OAAO,GAAG,CAAC;AACnD,UAAI,MAAM,SAAS;AAAe,eAAA,OAAO,GAAG,CAAC;AACtC,aAAA;AAAA,IAAA;AAQT,aAAS,aAAa,OAAgD;AAC9D,YAAA,aAAa,MAAM,SAAS;AAClC,UAAI,cAAc,CAAC,YAAY,KAAK,GAAG;AAErC,gBAAQ,MAAM;AAAA,MAAA,WACL,CAAC,cAAc,CAAC,OAAO;AAEhC,gBAAQ,GAAGD,cAAA,QAAQ,MAAM,OAAO,CAAC;AAAA,MAAA;AAInC,UAAI,CAAC,YAAY;AAEf,YAAI,CAAC,MAAM,MAAM,KAAKE,cAAAA,SAAS,KAAK,IAAI,QAAQ,MAAM,YAAY,MAAM,GAAG;AACpE,eAAAF,cAAAA,QAAQC,oBAAM,OAAO,IAAI,GAAG,MAAM,SAAS,MAAM,OAAO,CAAC;AACvD,iBAAAD,cAAAA,QAAQC,oBAAM,OAAO,MAAM,GAAG,MAAM,WAAW,MAAM,SAAS,CAAC;AACjE,eAAA,GAAG,IAAI,IAAI,MAAM;AAAA,MAAA;AAIlB,cAAA,KAAK,IAAI,KAAK,IAAI,OAAO,KAAK,GAAG,MAAM,OAAO,GAAG,MAAM,OAAO;AAE/D,aAAA;AAAA,IAAA;AAQA,aAAA,YAAY,MAAqBF,aAAoB;AACtD,YAAA,QAAQ,IAAI,KAAKA,WAAU;AACjC,YAAM,WAAW,IAAI,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC;AACxC,YAAA,OAAO,SAAS,YAAY;AAClC,UAAI,QAAgB;AACpB,UAAI,OAAe;AACnB,UAAI,OAAe;AACnB,UAAI,SAAiB;AAErB,UAAI,SAAS,OAAO;AACV,gBAAA;AACR,eAAO,eAAe,MAAM,eAAe,MAAM,aAAa,CAAC;AACxD,eAAA;AACE,iBAAA;AAAA,MAAA;AAGP,UAAA,MAAM,YAAY,MAAM,MAAM;AACxB,gBAAA,SAAS,aAAa;AAC9B,YAAI,MAAM,aAAa,MAAM,OAAO;AAClC,iBAAO,SAAS,QAAQ;AACpB,cAAA,MAAM,QAAQ,MAAM,MAAM;AAC5B,mBAAO,SAAS,SAAS;AACrB,gBAAA,MAAM,SAAS,MAAM,MAAM;AAC7B,uBAAS,SAAS,WAAW;AAAA,YAAA;AAAA,UAC/B;AAAA,QACF;AAAA,MACF;AAEK,aAAA;AAAA,QACL,CAAC,GAAG,IAAI,MAAM,GAAG;AAAA,QACjB,CAAC,GAAG,IAAI,OAAO,GAAG;AAAA,QAClB,CAAC,GAAG,IAAI,MAAM,GAAG;AAAA,QACjB,CAAC,GAAG,IAAI,MAAM,GAAG;AAAA,QACjB,CAAC,GAAG,IAAI,QAAQ,GAAG;AAAA,MACrB;AAAA,IAAA;AAQF,aAAS,kBAAkB,OAAwB;AACjD,YAAM,SAASL,cAAA,eAAe,OAAO,MAAM,IAAI;AAE3C,UAAA,MAAM,eAAe,OAAO;AAC9B,aAAK,qBAAqB,KAAK;AAC/B,aAAK,UAAU;AAAA,UACb;AAAA,UACA,QAAQ,MAAM,EAAE;AAAA,QAAA,CACjB;AAAA,MAAA;AAEH,iBAAW,QAAQ;AACnB,cAAQ,QAAQ,cAAc;AAC9B,kBAAY,QAAQ;AAAA,IAAA;AAOtB,aAAS,mBAAmB;;AACpB,YAAA,EAAE,SAAS;AACjB,UAAIK,cAA8B;AAClC,YAAM,cAAY,oBAAe,UAAf,mBAAsB,gBAAe,CAAC;AACxD,YAAM,SAASI,cAAAA,QAAQ,SAAS,IAAI,YAAY,CAAC,SAAS;AAE1D,UAAI,SAAS,QAAQ;AACnBJ,sBAAa,GAAGC,sBAAQ,OAAO,CAAC,CAAC,CAAC,IAAIA,sBAAQ,OAAO,CAAC,CAAC,CAAC;AACjDD,eAAAA;AAAAA,MAAA;AAIT,YAAM,OAAO,OAAO,CAAC,KAAK,SAAS,OAAO,CAAC,CAAC;AAGtC,YAAA,QAAQ,SAAS,SAAS,IAAI,OAAO,CAAC,KAAK,SAAS,OAAO,CAAC,CAAC;AAEnE,YAAM,UAAU,eAAe,OAAO,IAAI,GAAG,OAAO,KAAK,CAAC;AAG1D,UAAI,OAAwB;AACxB,UAAA,SAAS,gBAAgB,SAAS,QAAQ;AACpC,gBAAA,OAAO,OAAO,CAAC,CAAC,KAAK,SAAS,OAAO,OAAO,CAAC,CAAC,CAAC,KAAK,UAAU,UAAU,OAAO,CAAC,KAAK,SAAS,OAAO,OAAO,CAAC,CAAC,CAAC;AAAA,MAAA;AAIzH,UAAI,OAAO;AACX,UAAI,SAAS;AAEb,UAAI,SAAS,YAAY;AAChB,eAAA,OAAO,OAAO,CAAC,CAAC,KAAK,SAAS,OAAO,CAAC,CAAC;AACrC,iBAAA,OAAO,OAAO,CAAC,CAAC,KAAK,SAAS,OAAO,CAAC,CAAC;AAAA,MAAA;AAElD,YAAM,QAAQ,IAAI,KAAK,OAAO,IAAI,GAAG,OAAO,KAAK,IAAI,GAAG,OAAO,IAAI,GAAG,MAAM,MAAM,EAAE,QAAQ;AAE5FA,oBAAa,aAAa,KAAK;AACxBA,aAAAA;AAAAA,IAAA;AAMT,aAAS,aAAa,QAA4B;AAE5C,UAAA,MAAM,SAAS,UAAU,MAAM,SAAS,gBAAgB,MAAM,SAAS,QAAQ;AACjF;AAAA,MAAA;AAGI,YAAA,SAAS,OAAO,UAAU;AAChC,YAAM,OAAO,OAAO,OAAO,CAAC,CAAC;AAC7B,YAAM,QAAQ,OAAO,OAAO,CAAC,CAAC;AACxB,YAAA,UAAU,eAAe,MAAM,KAAK;AAC1C,UAAI,OAAO,OAAO,OAAO,CAAC,CAAC;AACpB,aAAA,OAAO,UAAU,UAAU;AAClC,UAAI,OAAe;AACnB,UAAI,SAAiB;AACjB,UAAA,MAAM,SAAS,YAAY;AACtB,eAAA,OAAO,OAAO,CAAC,CAAC;AACd,iBAAA,OAAO,OAAO,CAAC,CAAC;AAAA,MAAA;AAErB,YAAA,QAAQ,IAAI,KAAK,MAAM,QAAQ,GAAG,MAAM,MAAM,MAAM,EAAE,QAAQ;AAGzD,iBAAA,QAAQ,aAAa,KAAK;AAGrC,YAAM,aAAa,cAAc;AAEjC,YAAM,gBAAgB,OAAO,iBAAiB,EAAE,MAAM,CAAC;AAK5C,iBAAA,QAAQ,CAAC,UAAU,UAAU;AACtC,cAAM,kBAAkB,QAAQ;AAC1B,cAAA,iBAAiB,WAAW,eAAe;AAC7C,YAAA,kBAAkB,WAAW,SAAS;AAAG;AACtC,eAAA;AAAA,UACL;AAAA,UACA;AAAA,UACA,cAAc,eAAe,KAAK,eAAe,SAAS,IAAI,cAAc,eAAe,IAAI;AAAA,QACjG;AAAA,MAAA,CACD;AAAA,IAAA;AAEH,aAAS,cAAc;AACrB,WAAK,WAAW;AAAA,IAAA;AAElB,aAAS,YAAY;AACnB,WAAK,SAAS;AAAA,IAAA;AAGhB,aAAS,aAAa;;AACd,YAAA,aAAY,oBAAe,UAAf,mBAAsB;AACxC,UAAI,aAAa;AAAa,eAAA;AAC9B,UAAII,cAAAA,QAAQ,SAAS;AAAU,eAAA;AAC/B,aAAO,CAAC,SAAS;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AChfnB,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}