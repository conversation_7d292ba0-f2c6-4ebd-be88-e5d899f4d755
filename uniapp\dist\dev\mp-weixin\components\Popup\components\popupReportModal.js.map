{"version": 3, "file": "popupReportModal.js", "sources": ["../../../../../../src/components/Popup/components/popupReportModal.vue", "../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvY29tcG9uZW50cy9Qb3B1cC9jb21wb25lbnRzL3BvcHVwUmVwb3J0TW9kYWwudnVl"], "sourcesContent": ["<template>\r\n  <wd-popup position=\"bottom\" v-model=\"show\">\r\n    <PageLayout\r\n      :navTitle=\"navTitle\"\r\n      type=\"popup\"\r\n      navRightText=\"确定\"\r\n      @navRight=\"handleConfirm\"\r\n      @navBack=\"handleCancel\"\r\n    >\r\n      <view class=\"wrap\">\r\n        <z-paging\r\n          ref=\"paging\"\r\n          :fixed=\"false\"\r\n          v-model=\"dataList\"\r\n          @query=\"queryList\"\r\n          :default-page-size=\"15\"\r\n        >\r\n          <template #top>\r\n            <wd-search\r\n              hide-cancel\r\n              :placeholder=\"search.placeholder\"\r\n              v-model=\"search.keyword\"\r\n              @search=\"handleSearch\"\r\n              @clear=\"handleClear\"\r\n            />\r\n          </template>\r\n          <template v-if=\"multi\">\r\n            <wd-checkbox-group shape=\"square\" v-model=\"checkedValue\">\r\n              <template v-for=\"(item, index) in dataList\" :key=\"index\">\r\n                <view class=\"list\" @click=\"hanldeCheck(index)\">\r\n                  <view class=\"left text-gray-5\">\r\n                    <template v-for=\"(cItem, cIndex) in columns\" :key=\"cIndex\">\r\n                      <view class=\"row\">\r\n                        <text class=\"label\">{{ cItem.title }}：</text>\r\n                        <text class=\"value\">{{ item[cItem.dataIndex] }}</text>\r\n                      </view>\r\n                    </template>\r\n                  </view>\r\n                  <view class=\"right\" @click.stop>\r\n                    <wd-checkbox ref=\"checkboxRef\" :modelValue=\"index\"></wd-checkbox>\r\n                  </view>\r\n                </view>\r\n              </template>\r\n            </wd-checkbox-group>\r\n          </template>\r\n          <template v-else>\r\n            <wd-radio-group shape=\"dot\" v-model=\"checkedValue\">\r\n              <template v-for=\"(item, index) in dataList\" :key=\"index\">\r\n                <wd-cell>\r\n                  <view class=\"list\" @click=\"hanldeCheck(index)\">\r\n                    <view class=\"left text-gray-5\">\r\n                      <template v-for=\"(cItem, cIndex) in columns\" :key=\"cIndex\">\r\n                        <view class=\"row\">\r\n                          <text class=\"label\">{{ cItem.title }}：</text>\r\n                          <text class=\"value\">{{ item[cItem.dataIndex] }}</text>\r\n                        </view>\r\n                      </template>\r\n                    </view>\r\n                    <view class=\"right\" @click.stop>\r\n                      <wd-radio :value=\"index\"></wd-radio>\r\n                    </view>\r\n                  </view>\r\n                </wd-cell>\r\n              </template>\r\n            </wd-radio-group>\r\n          </template>\r\n        </z-paging>\r\n      </view>\r\n    </PageLayout>\r\n  </wd-popup>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, reactive } from 'vue'\r\nimport { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'\r\nimport { http } from '@/utils/http'\r\nimport { isArray } from '@/utils/is'\r\ndefineOptions({\r\n  name: 'popupReportModal',\r\n  options: {\r\n    styleIsolation: 'shared',\r\n  },\r\n})\r\nconst props = defineProps({\r\n  code: {\r\n    type: String,\r\n    default: '',\r\n    required: true,\r\n  },\r\n  showFiled: {\r\n    type: String,\r\n    default: '',\r\n    required: true,\r\n  },\r\n  multi: {\r\n    type: Boolean,\r\n    default: true,\r\n  },\r\n})\r\nconst emit = defineEmits(['change', 'close'])\r\nconst toast = useToast()\r\nconst show = ref(true)\r\nconst api = {\r\n  getColumns: '/online/cgreport/api/getRpColumns',\r\n  getData: '/online/cgreport/api/getData',\r\n  getQueryInfo: '/online/cgreport/api/getQueryInfo',\r\n}\r\nconsole.log('props:::', props)\r\nconst navTitle = ref('')\r\nconst paging = ref(null)\r\nconst dataList = ref([])\r\n// 报表id\r\nlet rpConfigId = null\r\nlet loadedColumns = false\r\nconst dictOptions = ref([])\r\nconst columns = ref([])\r\nconst checkedValue: any = ref(props.multi ? [] : '')\r\nconst checkboxRef = ref(null)\r\nconst search = reactive({\r\n  keyword: '',\r\n  placeholder: '',\r\n  field: '',\r\n})\r\n\r\nconst handleClose = () => {\r\n  setTimeout(() => {\r\n    emit('close')\r\n  }, 400)\r\n}\r\nconst handleConfirm = () => {\r\n  if (checkedValue.value.length == 0) {\r\n    toast.warning('还没选择~')\r\n    return\r\n  }\r\n  const result = []\r\n  let value = checkedValue.value\r\n  if (!Array.isArray(checkedValue.value)) {\r\n    value = [checkedValue.value]\r\n  }\r\n  value.forEach((index) => {\r\n    result.push(dataList.value[index])\r\n  })\r\n  show.value = false\r\n  emit('change', result)\r\n  handleClose()\r\n}\r\nconst handleCancel = () => {\r\n  show.value = false\r\n  handleClose()\r\n  console.log('取消了~')\r\n}\r\n// 搜索\r\nfunction handleSearch() {\r\n  paging.value.reload()\r\n}\r\n// 清除搜索条件\r\nfunction handleClear() {\r\n  search.keyword = ''\r\n  handleSearch()\r\n}\r\nconst hanldeCheck = (index) => {\r\n  if (props.multi) {\r\n    if (Array.isArray(checkboxRef.value)) {\r\n      checkboxRef.value[index].toggle()\r\n    }\r\n  } else {\r\n    checkedValue.value = index\r\n  }\r\n}\r\nconst getQueryInfo = () => {\r\n  const analysis = (data = []) => {\r\n    if (data.length) {\r\n      search.placeholder = `请输入${data[0].label}`\r\n      search.field = data[0].field\r\n    } else {\r\n      const item = columns[0] ?? {}\r\n      search.placeholder = `请输入${item.title}`\r\n      search.field = item.dataIndex\r\n    }\r\n  }\r\n  http\r\n    .get(`${api.getQueryInfo}/${rpConfigId}`)\r\n    .then((res: any) => {\r\n      if (res.success) {\r\n        analysis(res.result)\r\n      } else {\r\n        analysis()\r\n      }\r\n    })\r\n    .catch((err) => {\r\n      analysis()\r\n    })\r\n}\r\nconst getRpColumns = () => {\r\n  return new Promise<void>((resolve, reject) => {\r\n    if (loadedColumns) {\r\n      resolve()\r\n    } else {\r\n      http\r\n        .get(`${api.getColumns}/${props.code}`)\r\n        .then((res: any) => {\r\n          if (res.success) {\r\n            loadedColumns = true\r\n            const { result } = res\r\n            navTitle.value = result.cgRpConfigName\r\n            dictOptions.value = result.dictOptions\r\n            rpConfigId = result.cgRpConfigId\r\n            const fileds = props.showFiled.split(',')\r\n            result.columns?.forEach((item) => {\r\n              if (fileds.includes(item.dataIndex)) {\r\n                columns.value.push(item)\r\n              }\r\n            })\r\n            getQueryInfo()\r\n            resolve()\r\n          } else {\r\n            reject()\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          reject()\r\n        })\r\n    }\r\n  })\r\n}\r\n\r\nconst queryList = (pageNo, pageSize) => {\r\n  const pararms = { pageNo, pageSize }\r\n  if (search.keyword) {\r\n    pararms[search.field] = `*${search.keyword}*`\r\n  }\r\n  getRpColumns()\r\n    .then(() => {\r\n      http\r\n        .get(`${api.getData}/${rpConfigId}`, pararms)\r\n        .then((res: any) => {\r\n          if (res.success && res.result.records) {\r\n            paging.value.complete(res.result.records ?? [])\r\n          } else {\r\n            paging.value.complete(false)\r\n          }\r\n        })\r\n        .catch((err) => {})\r\n    })\r\n    .catch((err) => {})\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n:deep(.wd-cell) {\r\n  --wot-color-white: tranparent;\r\n  --wot-cell-padding: 0;\r\n  .wd-cell__wrapper {\r\n    --wot-cell-wrapper-padding: 0;\r\n  }\r\n  .wd-cell__left {\r\n    display: none;\r\n  }\r\n}\r\n:deep(.wd-checkbox-group) {\r\n  --wot-checkbox-bg: tranparent;\r\n}\r\n:deep(.wd-radio-group) {\r\n  --wot-radio-bg: tranparent;\r\n}\r\n.list {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  background: #fff;\r\n  padding: 16px;\r\n  margin-top: 16px;\r\n  .left {\r\n    display: flex;\r\n    justify-content: center;\r\n    flex-direction: column;\r\n    .row {\r\n      display: flex;\r\n    }\r\n  }\r\n  .right {\r\n    :deep(.wd-checkbox) {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n.wrap {\r\n  height: 100%;\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/components/Popup/components/popupReportModal.vue'\nwx.createComponent(Component)"], "names": ["useToast", "ref", "reactive", "http"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmFA,UAAM,QAAQ;AAgBd,UAAM,OAAO;AACb,UAAM,QAAQA,cAAAA,SAAS;AACjB,UAAA,OAAOC,kBAAI,IAAI;AACrB,UAAM,MAAM;AAAA,MACV,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,cAAc;AAAA,IAChB;AACQ,YAAA,IAAI,YAAY,KAAK;AACvB,UAAA,WAAWA,kBAAI,EAAE;AACjB,UAAA,SAASA,kBAAI,IAAI;AACjB,UAAA,WAAWA,cAAI,IAAA,EAAE;AAEvB,QAAI,aAAa;AACjB,QAAI,gBAAgB;AACd,UAAA,cAAcA,cAAI,IAAA,EAAE;AACpB,UAAA,UAAUA,cAAI,IAAA,EAAE;AACtB,UAAM,eAAoBA,cAAAA,IAAI,MAAM,QAAQ,CAAA,IAAK,EAAE;AAC7C,UAAA,cAAcA,kBAAI,IAAI;AAC5B,UAAM,SAASC,cAAAA,SAAS;AAAA,MACtB,SAAS;AAAA,MACT,aAAa;AAAA,MACb,OAAO;AAAA,IAAA,CACR;AAED,UAAM,cAAc,MAAM;AACxB,iBAAW,MAAM;AACf,aAAK,OAAO;AAAA,SACX,GAAG;AAAA,IACR;AACA,UAAM,gBAAgB,MAAM;AACtB,UAAA,aAAa,MAAM,UAAU,GAAG;AAClC,cAAM,QAAQ,OAAO;AACrB;AAAA,MAAA;AAEF,YAAM,SAAS,CAAC;AAChB,UAAI,QAAQ,aAAa;AACzB,UAAI,CAAC,MAAM,QAAQ,aAAa,KAAK,GAAG;AAC9B,gBAAA,CAAC,aAAa,KAAK;AAAA,MAAA;AAEvB,YAAA,QAAQ,CAAC,UAAU;AACvB,eAAO,KAAK,SAAS,MAAM,KAAK,CAAC;AAAA,MAAA,CAClC;AACD,WAAK,QAAQ;AACb,WAAK,UAAU,MAAM;AACT,kBAAA;AAAA,IACd;AACA,UAAM,eAAe,MAAM;AACzB,WAAK,QAAQ;AACD,kBAAA;AACZ,cAAQ,IAAI,MAAM;AAAA,IACpB;AAEA,aAAS,eAAe;AACtB,aAAO,MAAM,OAAO;AAAA,IAAA;AAGtB,aAAS,cAAc;AACrB,aAAO,UAAU;AACJ,mBAAA;AAAA,IAAA;AAET,UAAA,cAAc,CAAC,UAAU;AAC7B,UAAI,MAAM,OAAO;AACf,YAAI,MAAM,QAAQ,YAAY,KAAK,GAAG;AACxB,sBAAA,MAAM,KAAK,EAAE,OAAO;AAAA,QAAA;AAAA,MAClC,OACK;AACL,qBAAa,QAAQ;AAAA,MAAA;AAAA,IAEzB;AACA,UAAM,eAAe,MAAM;AACzB,YAAM,WAAW,CAAC,OAAO,OAAO;;AAC9B,YAAI,KAAK,QAAQ;AACf,iBAAO,cAAc,MAAM,KAAK,CAAC,EAAE,KAAK;AACjC,iBAAA,QAAQ,KAAK,CAAC,EAAE;AAAA,QAAA,OAClB;AACL,gBAAM,QAAO,aAAQ,CAAC,MAAT,YAAc,CAAC;AACrB,iBAAA,cAAc,MAAM,KAAK,KAAK;AACrC,iBAAO,QAAQ,KAAK;AAAA,QAAA;AAAA,MAExB;AAEGC,iBAAAA,KAAA,IAAI,GAAG,IAAI,YAAY,IAAI,UAAU,EAAE,EACvC,KAAK,CAAC,QAAa;AAClB,YAAI,IAAI,SAAS;AACf,mBAAS,IAAI,MAAM;AAAA,QAAA,OACd;AACI,mBAAA;AAAA,QAAA;AAAA,MACX,CACD,EACA,MAAM,CAAC,QAAQ;AACL,iBAAA;AAAA,MAAA,CACV;AAAA,IACL;AACA,UAAM,eAAe,MAAM;AACzB,aAAO,IAAI,QAAc,CAAC,SAAS,WAAW;AAC5C,YAAI,eAAe;AACT,kBAAA;AAAA,QAAA,OACH;AAEFA,qBAAAA,KAAA,IAAI,GAAG,IAAI,UAAU,IAAI,MAAM,IAAI,EAAE,EACrC,KAAK,CAAC,QAAa;;AAClB,gBAAI,IAAI,SAAS;AACC,8BAAA;AACV,oBAAA,EAAE,WAAW;AACnB,uBAAS,QAAQ,OAAO;AACxB,0BAAY,QAAQ,OAAO;AAC3B,2BAAa,OAAO;AACpB,oBAAM,SAAS,MAAM,UAAU,MAAM,GAAG;AACjC,2BAAA,YAAA,mBAAS,QAAQ,CAAC,SAAS;AAChC,oBAAI,OAAO,SAAS,KAAK,SAAS,GAAG;AAC3B,0BAAA,MAAM,KAAK,IAAI;AAAA,gBAAA;AAAA,cACzB;AAEW,2BAAA;AACL,sBAAA;AAAA,YAAA,OACH;AACE,qBAAA;AAAA,YAAA;AAAA,UACT,CACD,EACA,MAAM,CAAC,QAAQ;AACP,mBAAA;AAAA,UAAA,CACR;AAAA,QAAA;AAAA,MACL,CACD;AAAA,IACH;AAEM,UAAA,YAAY,CAAC,QAAQ,aAAa;AAChC,YAAA,UAAU,EAAE,QAAQ,SAAS;AACnC,UAAI,OAAO,SAAS;AAClB,gBAAQ,OAAO,KAAK,IAAI,IAAI,OAAO,OAAO;AAAA,MAAA;AAE/B,mBAAA,EACV,KAAK,MAAM;AAEPA,mBAAAA,KAAA,IAAI,GAAG,IAAI,OAAO,IAAI,UAAU,IAAI,OAAO,EAC3C,KAAK,CAAC,QAAa;;AAClB,cAAI,IAAI,WAAW,IAAI,OAAO,SAAS;AACrC,mBAAO,MAAM,UAAS,SAAI,OAAO,YAAX,YAAsB,EAAE;AAAA,UAAA,OACzC;AACE,mBAAA,MAAM,SAAS,KAAK;AAAA,UAAA;AAAA,QAC7B,CACD,EACA,MAAM,CAAC,QAAQ;AAAA,QAAA,CAAE;AAAA,MAAA,CACrB,EACA,MAAM,CAAC,QAAQ;AAAA,MAAA,CAAE;AAAA,IACtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpPA,GAAG,gBAAgB,SAAS;"}