"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../../common/vendor.js");
if (!Math) {
  wdIcon();
}
const wdIcon = () => "../wd-icon/wd-icon.js";
const __default__ = {
  name: "wd-sort-button",
  options: {
    addGlobalClass: true,
    virtualHost: true,
    styleIsolation: "shared"
  }
};
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, __default__), {
  props: common_vendor.sortButtonProps,
  emits: ["change", "update:modelValue"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    function handleClick() {
      let { modelValue: value, allowReset, descFirst } = props;
      if (descFirst) {
        if (value === 0) {
          value = -1;
        } else if (value === -1) {
          value = 1;
        } else if (value === 1) {
          if (allowReset) {
            value = 0;
          } else {
            value = -1;
          }
        }
      } else {
        if (value === 0) {
          value = 1;
        } else if (value === 1) {
          value = -1;
        } else if (value === -1) {
          if (allowReset) {
            value = 0;
          } else {
            value = 1;
          }
        }
      }
      emit("update:modelValue", value);
      emit("change", {
        value
      });
    }
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.t(_ctx.title),
        b: common_vendor.n(`wd-sort-button__left ${_ctx.modelValue !== 0 ? "is-active" : ""}`),
        c: _ctx.modelValue !== 1
      }, _ctx.modelValue !== 1 ? {
        d: common_vendor.p({
          name: "arrow-up",
          ["custom-class"]: "wd-sort-button__icon-up"
        })
      } : {}, {
        e: _ctx.modelValue !== -1
      }, _ctx.modelValue !== -1 ? {
        f: common_vendor.p({
          name: "arrow-down",
          ["custom-class"]: "wd-sort-button__icon-down"
        })
      } : {}, {
        g: common_vendor.n(`wd-sort-button__right ${_ctx.modelValue !== 0 ? "is-active" : ""}`),
        h: common_vendor.n(`wd-sort-button ${_ctx.line ? "wd-sort-button--line" : ""} ${_ctx.customClass}`),
        i: common_vendor.s(_ctx.customStyle),
        j: common_vendor.o(handleClick)
      });
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-42f0b178"]]);
wx.createComponent(Component);
//# sourceMappingURL=wd-sort-button.js.map
