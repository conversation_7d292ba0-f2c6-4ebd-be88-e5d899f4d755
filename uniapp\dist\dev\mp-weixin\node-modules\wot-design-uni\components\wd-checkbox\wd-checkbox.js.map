{"version": 3, "file": "wd-checkbox.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-checkbox/wd-checkbox.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1jaGVja2JveC93ZC1jaGVja2JveC52dWU"], "sourcesContent": ["<template>\n  <view\n    :class=\"`wd-checkbox ${innerCell ? 'is-cell-box' : ''} ${innerShape === 'button' ? 'is-button-box' : ''} ${isChecked ? 'is-checked' : ''} ${\n      isFirst ? 'is-first-child' : ''\n    } ${isLast ? 'is-last-child' : ''} ${innerInline ? 'is-inline' : ''} ${innerShape === 'button' ? 'is-button' : ''} ${\n      innerDisabled ? 'is-disabled' : ''\n    } ${innerSize ? 'is-' + innerSize : ''} ${customClass}`\"\n    :style=\"customStyle\"\n    @click=\"toggle\"\n  >\n    <!--shape为button时，移除wd-checkbox__shape，只保留wd-checkbox__label-->\n    <view\n      v-if=\"innerShape !== 'button'\"\n      :class=\"`wd-checkbox__shape ${innerShape === 'square' ? 'is-square' : ''} ${customShapeClass}`\"\n      :style=\"isChecked && !innerDisabled && innerCheckedColor ? 'color :' + innerCheckedColor : ''\"\n    >\n      <wd-icon custom-class=\"wd-checkbox__check\" name=\"check-bold\" />\n    </view>\n    <!--shape为button时只保留wd-checkbox__label-->\n    <view\n      :class=\"`wd-checkbox__label ${customLabelClass}`\"\n      :style=\"isChecked && innerShape === 'button' && !innerDisabled && innerCheckedColor ? 'color:' + innerCheckedColor : ''\"\n    >\n      <!--button选中时展示的icon-->\n      <wd-icon v-if=\"innerShape === 'button' && isChecked\" custom-class=\"wd-checkbox__btn-check\" name=\"check-bold\" />\n      <!--文案-->\n      <view class=\"wd-checkbox__txt\" :style=\"maxWidth ? 'max-width:' + maxWidth : ''\">\n        <slot></slot>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script lang=\"ts\">\nexport default {\n  name: 'wd-checkbox',\n  options: {\n    addGlobalClass: true,\n    virtualHost: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport wdIcon from '../wd-icon/wd-icon.vue'\nimport { computed, getCurrentInstance, onBeforeMount, watch } from 'vue'\nimport { useParent } from '../composables/useParent'\nimport { CHECKBOX_GROUP_KEY } from '../wd-checkbox-group/types'\nimport { getPropByPath, isDef } from '../common/util'\nimport { checkboxProps, type CheckboxExpose } from './types'\n\nconst props = defineProps(checkboxProps)\nconst emit = defineEmits(['change', 'update:modelValue'])\n\ndefineExpose<CheckboxExpose>({\n  toggle\n})\n\nconst { parent: checkboxGroup, index } = useParent(CHECKBOX_GROUP_KEY)\n\nconst isChecked = computed(() => {\n  if (checkboxGroup) {\n    return checkboxGroup.props.modelValue.indexOf(props.modelValue) > -1\n  } else {\n    return props.modelValue === props.trueValue\n  }\n}) // 是否被选中\n\nconst isFirst = computed(() => {\n  return index.value === 0\n})\n\nconst isLast = computed(() => {\n  const children = isDef(checkboxGroup) ? checkboxGroup.children : []\n  return index.value === children.length - 1\n})\nconst { proxy } = getCurrentInstance() as any\n\nwatch(\n  () => props.modelValue,\n  () => {\n    // 组合使用走这个逻辑\n    if (checkboxGroup) {\n      checkName()\n    }\n  }\n)\n\nwatch(\n  () => props.shape,\n  (newValue) => {\n    const type = ['circle', 'square', 'button']\n    if (isDef(newValue) && type.indexOf(newValue) === -1) console.error(`shape must be one of ${type.toString()}`)\n  }\n)\n\nconst innerShape = computed(() => {\n  return props.shape || getPropByPath(checkboxGroup, 'props.shape') || 'circle'\n})\n\nconst innerCheckedColor = computed(() => {\n  return props.checkedColor || getPropByPath(checkboxGroup, 'props.checkedColor')\n})\n\nconst innerDisabled = computed(() => {\n  if (!checkboxGroup) {\n    return props.disabled\n  }\n  const { max, min, modelValue, disabled } = checkboxGroup.props\n  if (\n    (max && modelValue.length >= max && !isChecked.value) ||\n    (min && modelValue.length <= min && isChecked.value) ||\n    props.disabled === true ||\n    (disabled && props.disabled === null)\n  ) {\n    return true\n  }\n\n  return props.disabled\n})\n\nconst innerInline = computed(() => {\n  return getPropByPath(checkboxGroup, 'props.inline') || false\n})\n\nconst innerCell = computed(() => {\n  return getPropByPath(checkboxGroup, 'props.cell') || false\n})\n\nconst innerSize = computed(() => {\n  return props.size || getPropByPath(checkboxGroup, 'props.size')\n})\n\nonBeforeMount(() => {\n  // eslint-disable-next-line quotes\n  if (props.modelValue === null) console.error(\"checkbox's value must be set\")\n})\n\n/**\n * @description 检测checkbox绑定的value是否和其它checkbox的value冲突\n * @param {Object} self 自身\n * @param  myName 自己的标识符\n */\nfunction checkName() {\n  checkboxGroup &&\n    checkboxGroup.children &&\n    checkboxGroup.children.forEach((child: any) => {\n      if (child.$.uid !== proxy.$.uid && child.modelValue === props.modelValue) {\n        console.error(`The checkbox's bound value: ${props.modelValue} has been used`)\n      }\n    })\n}\n/**\n * @description 点击checkbox的Event handle\n */\nfunction toggle() {\n  if (innerDisabled.value) return\n  // 复选框单独使用时点击反选，并且在checkbox上触发change事件\n  if (checkboxGroup) {\n    emit('change', {\n      value: !isChecked.value\n    })\n    checkboxGroup.changeSelectState(props.modelValue)\n  } else {\n    const newVal = props.modelValue === props.trueValue ? props.falseValue : props.trueValue\n    emit('update:modelValue', newVal)\n    emit('change', {\n      value: newVal\n    })\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-checkbox/wd-checkbox.vue'\nwx.createComponent(Component)"], "names": ["useParent", "CHECKBOX_GROUP_KEY", "computed", "isDef", "getCurrentInstance", "watch", "getPropByPath", "onBeforeMount"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AA6CA,MAAA,SAAmB,MAAA;AAXnB,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,gBAAgB;AAAA,EAAA;AAEpB;;;;;AAWA,UAAM,QAAQ;AACd,UAAM,OAAO;AAEgB,aAAA;AAAA,MAC3B;AAAA,IAAA,CACD;AAED,UAAM,EAAE,QAAQ,eAAe,MAAM,IAAIA,cAAAA,UAAUC,cAAAA,kBAAkB;AAE/D,UAAA,YAAYC,cAAAA,SAAS,MAAM;AAC/B,UAAI,eAAe;AACjB,eAAO,cAAc,MAAM,WAAW,QAAQ,MAAM,UAAU,IAAI;AAAA,MAAA,OAC7D;AACE,eAAA,MAAM,eAAe,MAAM;AAAA,MAAA;AAAA,IACpC,CACD;AAEK,UAAA,UAAUA,cAAAA,SAAS,MAAM;AAC7B,aAAO,MAAM,UAAU;AAAA,IAAA,CACxB;AAEK,UAAA,SAASA,cAAAA,SAAS,MAAM;AAC5B,YAAM,WAAWC,cAAAA,MAAM,aAAa,IAAI,cAAc,WAAW,CAAC;AAC3D,aAAA,MAAM,UAAU,SAAS,SAAS;AAAA,IAAA,CAC1C;AACK,UAAA,EAAE,MAAM,IAAIC,iCAAmB;AAErCC,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,MAAM;AAEJ,YAAI,eAAe;AACP,oBAAA;AAAA,QAAA;AAAA,MACZ;AAAA,IAEJ;AAEAA,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,aAAa;AACZ,cAAM,OAAO,CAAC,UAAU,UAAU,QAAQ;AAC1C,YAAIF,cAAAA,MAAM,QAAQ,KAAK,KAAK,QAAQ,QAAQ,MAAM;AAAI,kBAAQ,MAAM,wBAAwB,KAAK,SAAU,CAAA,EAAE;AAAA,MAAA;AAAA,IAEjH;AAEM,UAAA,aAAaD,cAAAA,SAAS,MAAM;AAChC,aAAO,MAAM,SAASI,cAAAA,cAAc,eAAe,aAAa,KAAK;AAAA,IAAA,CACtE;AAEK,UAAA,oBAAoBJ,cAAAA,SAAS,MAAM;AACvC,aAAO,MAAM,gBAAgBI,4BAAc,eAAe,oBAAoB;AAAA,IAAA,CAC/E;AAEK,UAAA,gBAAgBJ,cAAAA,SAAS,MAAM;AACnC,UAAI,CAAC,eAAe;AAClB,eAAO,MAAM;AAAA,MAAA;AAEf,YAAM,EAAE,KAAK,KAAK,YAAY,SAAA,IAAa,cAAc;AACzD,UACG,OAAO,WAAW,UAAU,OAAO,CAAC,UAAU,SAC9C,OAAO,WAAW,UAAU,OAAO,UAAU,SAC9C,MAAM,aAAa,QAClB,YAAY,MAAM,aAAa,MAChC;AACO,eAAA;AAAA,MAAA;AAGT,aAAO,MAAM;AAAA,IAAA,CACd;AAEK,UAAA,cAAcA,cAAAA,SAAS,MAAM;AAC1B,aAAAI,4BAAc,eAAe,cAAc,KAAK;AAAA,IAAA,CACxD;AAEK,UAAA,YAAYJ,cAAAA,SAAS,MAAM;AACxB,aAAAI,4BAAc,eAAe,YAAY,KAAK;AAAA,IAAA,CACtD;AAEK,UAAA,YAAYJ,cAAAA,SAAS,MAAM;AAC/B,aAAO,MAAM,QAAQI,4BAAc,eAAe,YAAY;AAAA,IAAA,CAC/D;AAEDC,kBAAAA,cAAc,MAAM;AAElB,UAAI,MAAM,eAAe;AAAM,gBAAQ,MAAM,8BAA8B;AAAA,IAAA,CAC5E;AAOD,aAAS,YAAY;AACnB,uBACE,cAAc,YACd,cAAc,SAAS,QAAQ,CAAC,UAAe;AACzC,YAAA,MAAM,EAAE,QAAQ,MAAM,EAAE,OAAO,MAAM,eAAe,MAAM,YAAY;AACxE,kBAAQ,MAAM,+BAA+B,MAAM,UAAU,gBAAgB;AAAA,QAAA;AAAA,MAC/E,CACD;AAAA,IAAA;AAKL,aAAS,SAAS;AAChB,UAAI,cAAc;AAAO;AAEzB,UAAI,eAAe;AACjB,aAAK,UAAU;AAAA,UACb,OAAO,CAAC,UAAU;AAAA,QAAA,CACnB;AACa,sBAAA,kBAAkB,MAAM,UAAU;AAAA,MAAA,OAC3C;AACL,cAAM,SAAS,MAAM,eAAe,MAAM,YAAY,MAAM,aAAa,MAAM;AAC/E,aAAK,qBAAqB,MAAM;AAChC,aAAK,UAAU;AAAA,UACb,OAAO;AAAA,QAAA,CACR;AAAA,MAAA;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzKF,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}