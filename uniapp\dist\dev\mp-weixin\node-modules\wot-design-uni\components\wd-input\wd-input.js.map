{"version": 3, "file": "wd-input.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-input/wd-input.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1pbnB1dC93ZC1pbnB1dC52dWU"], "sourcesContent": ["<template>\n  <view :class=\"rootClass\" :style=\"customStyle\" @click=\"handleClick\">\n    <view v-if=\"label || $slots.label\" :class=\"labelClass\" :style=\"labelStyle\">\n      <view v-if=\"prefixIcon || $slots.prefix\" class=\"wd-input__prefix\">\n        <wd-icon v-if=\"prefixIcon && !$slots.prefix\" custom-class=\"wd-input__icon\" :name=\"prefixIcon\" @click=\"onClickPrefixIcon\" />\n        <slot v-else name=\"prefix\"></slot>\n      </view>\n      <view class=\"wd-input__label-inner\">\n        <template v-if=\"label && !$slots.label\">{{ label }}</template>\n        <slot v-else name=\"label\"></slot>\n      </view>\n    </view>\n    <view class=\"wd-input__body\">\n      <view class=\"wd-input__value\">\n        <view v-if=\"(prefixIcon || $slots.prefix) && !label\" class=\"wd-input__prefix\">\n          <wd-icon v-if=\"prefixIcon && !$slots.prefix\" custom-class=\"wd-input__icon\" :name=\"prefixIcon\" @click=\"onClickPrefixIcon\" />\n          <slot v-else name=\"prefix\"></slot>\n        </view>\n        <input\n          :class=\"[\n            'wd-input__inner',\n            prefixIcon ? 'wd-input__inner--prefix' : '',\n            showWordCount ? 'wd-input__inner--count' : '',\n            alignRight ? 'is-align-right' : '',\n            customInputClass\n          ]\"\n          :type=\"type\"\n          :password=\"showPassword && !isPwdVisible\"\n          v-model=\"inputValue\"\n          :placeholder=\"placeholderValue\"\n          :disabled=\"disabled || readonly\"\n          :maxlength=\"maxlength\"\n          :focus=\"focused\"\n          :confirm-type=\"confirmType\"\n          :confirm-hold=\"confirmHold\"\n          :cursor=\"cursor\"\n          :cursor-spacing=\"cursorSpacing\"\n          :placeholder-style=\"placeholderStyle\"\n          :selection-start=\"selectionStart\"\n          :selection-end=\"selectionEnd\"\n          :adjust-position=\"adjustPosition\"\n          :hold-keyboard=\"holdKeyboard\"\n          :always-embed=\"alwaysEmbed\"\n          :placeholder-class=\"inputPlaceholderClass\"\n          :ignoreCompositionEvent=\"ignoreCompositionEvent\"\n          :inputmode=\"inputmode\"\n          @input=\"handleInput\"\n          @focus=\"handleFocus\"\n          @blur=\"handleBlur\"\n          @confirm=\"handleConfirm\"\n          @keyboardheightchange=\"handleKeyboardheightchange\"\n        />\n        <view v-if=\"props.readonly\" class=\"wd-input__readonly-mask\" />\n        <view v-if=\"showClear || showPassword || suffixIcon || showWordCount || $slots.suffix\" class=\"wd-input__suffix\">\n          <wd-icon v-if=\"showClear\" custom-class=\"wd-input__clear\" name=\"error-fill\" @click=\"handleClear\" />\n          <wd-icon v-if=\"showPassword\" custom-class=\"wd-input__icon\" :name=\"isPwdVisible ? 'view' : 'eye-close'\" @click=\"togglePwdVisible\" />\n          <view v-if=\"showWordCount\" class=\"wd-input__count\">\n            <text\n              :class=\"[\n              inputValue && String(inputValue).length > 0 ? 'wd-input__count-current' : '',\n              String(inputValue).length > maxlength! ? 'is-error' : ''\n            ]\"\n            >\n              {{ String(inputValue).length }}\n            </text>\n            /{{ maxlength }}\n          </view>\n          <wd-icon v-if=\"suffixIcon && !$slots.suffix\" custom-class=\"wd-input__icon\" :name=\"suffixIcon\" @click=\"onClickSuffixIcon\" />\n          <slot v-else name=\"suffix\"></slot>\n        </view>\n      </view>\n      <view v-if=\"errorMessage\" class=\"wd-input__error-message\">{{ errorMessage }}</view>\n    </view>\n  </view>\n</template>\n\n<script lang=\"ts\">\nexport default {\n  name: 'wd-input',\n  options: {\n    virtualHost: true,\n    addGlobalClass: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport { computed, ref, watch, useSlots, type Slots } from 'vue'\nimport wdIcon from '../wd-icon/wd-icon.vue'\nimport { isDef, objToStyle, pause, isEqual } from '../common/util'\nimport { useCell } from '../composables/useCell'\nimport { FORM_KEY, type FormItemRule } from '../wd-form/types'\nimport { useParent } from '../composables/useParent'\nimport { useTranslate } from '../composables/useTranslate'\nimport { inputProps } from './types'\n\ninterface InputSlots extends Slots {\n  prefix?: () => any\n  suffix?: () => any\n  label?: () => any\n}\n\nconst props = defineProps(inputProps)\nconst emit = defineEmits([\n  'update:modelValue',\n  'clear',\n  'blur',\n  'focus',\n  'input',\n  'keyboardheightchange',\n  'confirm',\n  'clicksuffixicon',\n  'clickprefixicon',\n  'click'\n])\nconst slots = useSlots() as InputSlots\nconst { translate } = useTranslate('input')\n\nconst isPwdVisible = ref<boolean>(false)\nconst clearing = ref<boolean>(false) // 是否正在清空操作，避免重复触发失焦\nconst focused = ref<boolean>(false) // 控制聚焦\nconst focusing = ref<boolean>(false) // 当前是否激活状态\nconst inputValue = ref<string | number>(getInitValue()) // 输入框的值\nconst cell = useCell()\n\nwatch(\n  () => props.focus,\n  (newValue) => {\n    focused.value = newValue\n  },\n  { immediate: true, deep: true }\n)\n\nwatch(\n  () => props.modelValue,\n  (newValue) => {\n    inputValue.value = isDef(newValue) ? String(newValue) : ''\n  }\n)\n\nconst { parent: form } = useParent(FORM_KEY)\n\nconst placeholderValue = computed(() => {\n  return isDef(props.placeholder) ? props.placeholder : translate('placeholder')\n})\n\n/**\n * 展示清空按钮\n */\nconst showClear = computed(() => {\n  const { disabled, readonly, clearable, clearTrigger } = props\n  if (clearable && !readonly && !disabled && inputValue.value && (clearTrigger === 'always' || (props.clearTrigger === 'focus' && focusing.value))) {\n    return true\n  } else {\n    return false\n  }\n})\n\n/**\n * 展示字数统计\n */\nconst showWordCount = computed(() => {\n  const { disabled, readonly, maxlength, showWordLimit } = props\n  return Boolean(!disabled && !readonly && isDef(maxlength) && maxlength > -1 && showWordLimit)\n})\n\n/**\n * 表单错误提示信息\n */\nconst errorMessage = computed(() => {\n  if (form && props.prop && form.errorMessages && form.errorMessages[props.prop]) {\n    return form.errorMessages[props.prop]\n  } else {\n    return ''\n  }\n})\n\n// 是否展示必填\nconst isRequired = computed(() => {\n  let formRequired = false\n  if (form && form.props.rules) {\n    const rules = form.props.rules\n    for (const key in rules) {\n      if (Object.prototype.hasOwnProperty.call(rules, key) && key === props.prop && Array.isArray(rules[key])) {\n        formRequired = rules[key].some((rule: FormItemRule) => rule.required)\n      }\n    }\n  }\n  return props.required || props.rules.some((rule) => rule.required) || formRequired\n})\n\nconst rootClass = computed(() => {\n  return `wd-input  ${props.label || slots.label ? 'is-cell' : ''} ${props.center ? 'is-center' : ''} ${cell.border.value ? 'is-border' : ''} ${\n    props.size ? 'is-' + props.size : ''\n  } ${props.error ? 'is-error' : ''} ${props.disabled ? 'is-disabled' : ''}  ${\n    inputValue.value && String(inputValue.value).length > 0 ? 'is-not-empty' : ''\n  }  ${props.noBorder ? 'is-no-border' : ''} ${props.customClass}`\n})\n\nconst labelClass = computed(() => {\n  return `wd-input__label ${props.customLabelClass} ${isRequired.value ? 'is-required' : ''}`\n})\n\nconst inputPlaceholderClass = computed(() => {\n  return `wd-input__placeholder  ${props.placeholderClass}`\n})\n\nconst labelStyle = computed(() => {\n  return props.labelWidth\n    ? objToStyle({\n        'min-width': props.labelWidth,\n        'max-width': props.labelWidth\n      })\n    : ''\n})\n\n// 状态初始化\nfunction getInitValue() {\n  const formatted = formatValue(props.modelValue)\n  if (!isValueEqual(formatted, props.modelValue)) {\n    emit('update:modelValue', formatted)\n  }\n  return formatted\n}\n\nfunction formatValue(value: string | number) {\n  const { maxlength } = props\n  if (isDef(maxlength) && maxlength !== -1 && String(value).length > maxlength) {\n    return value.toString().slice(0, maxlength)\n  }\n  return value\n}\n\nfunction togglePwdVisible() {\n  isPwdVisible.value = !isPwdVisible.value\n}\nasync function handleClear() {\n  clearing.value = true\n  focusing.value = false\n  inputValue.value = ''\n  if (props.focusWhenClear) {\n    focused.value = false\n  }\n  await pause()\n  if (props.focusWhenClear) {\n    focused.value = true\n    focusing.value = true\n  }\n  emit('update:modelValue', inputValue.value)\n  emit('clear')\n}\nasync function handleBlur() {\n  // 等待150毫秒，clear执行完毕\n  await pause(150)\n  if (clearing.value) {\n    clearing.value = false\n    return\n  }\n  focusing.value = false\n  emit('blur', {\n    value: inputValue.value\n  })\n}\nfunction handleFocus({ detail }: any) {\n  focusing.value = true\n  emit('focus', detail)\n}\nfunction handleInput({ detail }: any) {\n  emit('update:modelValue', inputValue.value)\n  emit('input', detail)\n}\nfunction handleKeyboardheightchange({ detail }: any) {\n  emit('keyboardheightchange', detail)\n}\nfunction handleConfirm({ detail }: any) {\n  emit('confirm', detail)\n}\nfunction onClickSuffixIcon() {\n  emit('clicksuffixicon')\n}\nfunction onClickPrefixIcon() {\n  emit('clickprefixicon')\n}\nfunction handleClick(event: MouseEvent) {\n  emit('click', event)\n}\nfunction isValueEqual(value1: number | string, value2: number | string) {\n  return isEqual(String(value1), String(value2))\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n<style lang=\"scss\">\n@import './placeholder.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-input/wd-input.vue'\nwx.createComponent(Component)"], "names": ["useSlots", "useTranslate", "ref", "useCell", "watch", "isDef", "useParent", "FORM_KEY", "computed", "objToStyle", "pause", "isEqual"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyFA,MAAA,SAAmB,MAAA;AAZnB,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,EAAA;AAEpB;;;;;;;;;;;;;;;;AAmBA,UAAM,QAAQ;AACd,UAAM,OAAO;AAYb,UAAM,QAAQA,cAAAA,SAAS;AACvB,UAAM,EAAE,UAAA,IAAcC,cAAA,aAAa,OAAO;AAEpC,UAAA,eAAeC,kBAAa,KAAK;AACjC,UAAA,WAAWA,kBAAa,KAAK;AAC7B,UAAA,UAAUA,kBAAa,KAAK;AAC5B,UAAA,WAAWA,kBAAa,KAAK;AAC7B,UAAA,aAAaA,kBAAqB,cAAc;AACtD,UAAM,OAAOC,cAAAA,QAAQ;AAErBC,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,aAAa;AACZ,gBAAQ,QAAQ;AAAA,MAClB;AAAA,MACA,EAAE,WAAW,MAAM,MAAM,KAAK;AAAA,IAChC;AAEAA,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,aAAa;AACZ,mBAAW,QAAQC,oBAAM,QAAQ,IAAI,OAAO,QAAQ,IAAI;AAAA,MAAA;AAAA,IAE5D;AAEA,UAAM,EAAE,QAAQ,SAASC,cAAAA,UAAUC,cAAAA,QAAQ;AAErC,UAAA,mBAAmBC,cAAAA,SAAS,MAAM;AACtC,aAAOH,cAAAA,MAAM,MAAM,WAAW,IAAI,MAAM,cAAc,UAAU,aAAa;AAAA,IAAA,CAC9E;AAKK,UAAA,YAAYG,cAAAA,SAAS,MAAM;AAC/B,YAAM,EAAE,UAAU,UAAU,WAAW,aAAiB,IAAA;AACxD,UAAI,aAAa,CAAC,YAAY,CAAC,YAAY,WAAW,UAAU,iBAAiB,YAAa,MAAM,iBAAiB,WAAW,SAAS,QAAS;AACzI,eAAA;AAAA,MAAA,OACF;AACE,eAAA;AAAA,MAAA;AAAA,IACT,CACD;AAKK,UAAA,gBAAgBA,cAAAA,SAAS,MAAM;AACnC,YAAM,EAAE,UAAU,UAAU,WAAW,cAAkB,IAAA;AAClD,aAAA,QAAQ,CAAC,YAAY,CAAC,YAAYH,oBAAM,SAAS,KAAK,YAAY,MAAM,aAAa;AAAA,IAAA,CAC7F;AAKK,UAAA,eAAeG,cAAAA,SAAS,MAAM;AAC9B,UAAA,QAAQ,MAAM,QAAQ,KAAK,iBAAiB,KAAK,cAAc,MAAM,IAAI,GAAG;AACvE,eAAA,KAAK,cAAc,MAAM,IAAI;AAAA,MAAA,OAC/B;AACE,eAAA;AAAA,MAAA;AAAA,IACT,CACD;AAGK,UAAA,aAAaA,cAAAA,SAAS,MAAM;AAChC,UAAI,eAAe;AACf,UAAA,QAAQ,KAAK,MAAM,OAAO;AACtB,cAAA,QAAQ,KAAK,MAAM;AACzB,mBAAW,OAAO,OAAO;AACvB,cAAI,OAAO,UAAU,eAAe,KAAK,OAAO,GAAG,KAAK,QAAQ,MAAM,QAAQ,MAAM,QAAQ,MAAM,GAAG,CAAC,GAAG;AACvG,2BAAe,MAAM,GAAG,EAAE,KAAK,CAAC,SAAuB,KAAK,QAAQ;AAAA,UAAA;AAAA,QACtE;AAAA,MACF;AAEK,aAAA,MAAM,YAAY,MAAM,MAAM,KAAK,CAAC,SAAS,KAAK,QAAQ,KAAK;AAAA,IAAA,CACvE;AAEK,UAAA,YAAYA,cAAAA,SAAS,MAAM;AACxB,aAAA,aAAa,MAAM,SAAS,MAAM,QAAQ,YAAY,EAAE,IAAI,MAAM,SAAS,cAAc,EAAE,IAAI,KAAK,OAAO,QAAQ,cAAc,EAAE,IACxI,MAAM,OAAO,QAAQ,MAAM,OAAO,EACpC,IAAI,MAAM,QAAQ,aAAa,EAAE,IAAI,MAAM,WAAW,gBAAgB,EAAE,KACtE,WAAW,SAAS,OAAO,WAAW,KAAK,EAAE,SAAS,IAAI,iBAAiB,EAC7E,KAAK,MAAM,WAAW,iBAAiB,EAAE,IAAI,MAAM,WAAW;AAAA,IAAA,CAC/D;AAEK,UAAA,aAAaA,cAAAA,SAAS,MAAM;AAChC,aAAO,mBAAmB,MAAM,gBAAgB,IAAI,WAAW,QAAQ,gBAAgB,EAAE;AAAA,IAAA,CAC1F;AAEK,UAAA,wBAAwBA,cAAAA,SAAS,MAAM;AACpC,aAAA,0BAA0B,MAAM,gBAAgB;AAAA,IAAA,CACxD;AAEK,UAAA,aAAaA,cAAAA,SAAS,MAAM;AACzB,aAAA,MAAM,aACTC,yBAAW;AAAA,QACT,aAAa,MAAM;AAAA,QACnB,aAAa,MAAM;AAAA,MACpB,CAAA,IACD;AAAA,IAAA,CACL;AAGD,aAAS,eAAe;AAChB,YAAA,YAAY,YAAY,MAAM,UAAU;AAC9C,UAAI,CAAC,aAAa,WAAW,MAAM,UAAU,GAAG;AAC9C,aAAK,qBAAqB,SAAS;AAAA,MAAA;AAE9B,aAAA;AAAA,IAAA;AAGT,aAAS,YAAY,OAAwB;AACrC,YAAA,EAAE,cAAc;AAClB,UAAAJ,cAAA,MAAM,SAAS,KAAK,cAAc,MAAM,OAAO,KAAK,EAAE,SAAS,WAAW;AAC5E,eAAO,MAAM,SAAA,EAAW,MAAM,GAAG,SAAS;AAAA,MAAA;AAErC,aAAA;AAAA,IAAA;AAGT,aAAS,mBAAmB;AACb,mBAAA,QAAQ,CAAC,aAAa;AAAA,IAAA;AAErC,aAAe,cAAc;AAAA;AAC3B,iBAAS,QAAQ;AACjB,iBAAS,QAAQ;AACjB,mBAAW,QAAQ;AACnB,YAAI,MAAM,gBAAgB;AACxB,kBAAQ,QAAQ;AAAA,QAAA;AAElB,cAAMK,oBAAM;AACZ,YAAI,MAAM,gBAAgB;AACxB,kBAAQ,QAAQ;AAChB,mBAAS,QAAQ;AAAA,QAAA;AAEd,aAAA,qBAAqB,WAAW,KAAK;AAC1C,aAAK,OAAO;AAAA,MAAA;AAAA;AAEd,aAAe,aAAa;AAAA;AAE1B,cAAMA,cAAAA,MAAM,GAAG;AACf,YAAI,SAAS,OAAO;AAClB,mBAAS,QAAQ;AACjB;AAAA,QAAA;AAEF,iBAAS,QAAQ;AACjB,aAAK,QAAQ;AAAA,UACX,OAAO,WAAW;AAAA,QAAA,CACnB;AAAA,MAAA;AAAA;AAEM,aAAA,YAAY,EAAE,UAAe;AACpC,eAAS,QAAQ;AACjB,WAAK,SAAS,MAAM;AAAA,IAAA;AAEb,aAAA,YAAY,EAAE,UAAe;AAC/B,WAAA,qBAAqB,WAAW,KAAK;AAC1C,WAAK,SAAS,MAAM;AAAA,IAAA;AAEb,aAAA,2BAA2B,EAAE,UAAe;AACnD,WAAK,wBAAwB,MAAM;AAAA,IAAA;AAE5B,aAAA,cAAc,EAAE,UAAe;AACtC,WAAK,WAAW,MAAM;AAAA,IAAA;AAExB,aAAS,oBAAoB;AAC3B,WAAK,iBAAiB;AAAA,IAAA;AAExB,aAAS,oBAAoB;AAC3B,WAAK,iBAAiB;AAAA,IAAA;AAExB,aAAS,YAAY,OAAmB;AACtC,WAAK,SAAS,KAAK;AAAA,IAAA;AAEZ,aAAA,aAAa,QAAyB,QAAyB;AACtE,aAAOC,cAAAA,QAAQ,OAAO,MAAM,GAAG,OAAO,MAAM,CAAC;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/R/C,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}