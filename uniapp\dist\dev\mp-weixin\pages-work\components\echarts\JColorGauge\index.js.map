{"version": 3, "file": "index.js", "sources": ["../../../../../../../src/pages-work/components/echarts/JColorGauge/index.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvcGFnZXMtd29yay9jb21wb25lbnRzL2VjaGFydHMvSkNvbG9yR2F1Z2UvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"content\">\r\n    <statusTip v-if=\"pageTips.show\" :status=\"pageTips.status\"></statusTip>\r\n    <echartsUniapp v-else :option=\"option\"></echartsUniapp>\r\n  </view>\r\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { echartProps } from '@/pages-work/components/echarts/props'\r\nimport { deepMerge, handleTotalAndUnit, disposeGridLayout } from '../../common/echartUtil'\r\nimport { isNumber } from '@/utils/is'\r\nimport useChartHook from '@/pages-work/components/hooks/useEchart'\r\nimport { deepClone } from '@/uni_modules/da-tree/utils'\r\nimport echartsUniapp from '@/pages-work/components/echarts/index.vue'\r\nimport statusTip from '@/pages-work/components/statusTip.vue'\r\nimport {merge} from \"lodash-es\";\r\n//组件传参\r\nconst props = defineProps({\r\n  ...echartProps,\r\n})\r\n\r\n//最终图表配置项\r\nconst option = ref({})\r\nlet chartOption = {\r\n  title: {\r\n    show: true,\r\n  },\r\n  series: [\r\n    {\r\n      type: 'gauge',\r\n      anchor: {\r\n        show: true,\r\n        showAbove: true,\r\n        size: 18,\r\n        itemStyle: {\r\n          color: '#FAC858',\r\n        },\r\n      },\r\n      pointer: {\r\n        icon: 'path://M2.9,0.7L2.9,0.7c1.4,0,2.6,1.2,2.6,2.6v115c0,1.4-1.2,2.6-2.6,2.6l0,0c-1.4,0-2.6-1.2-2.6-2.6V3.3C0.3,1.9,1.4,0.7,2.9,0.7z',\r\n        width: 8,\r\n        length: '80%',\r\n        offsetCenter: [0, '8%'],\r\n        itemStyle: {\r\n          color: 'auto',\r\n        },\r\n      },\r\n      axisLine: {\r\n        roundCap: true,\r\n        lineStyle: {\r\n          width: 10,\r\n          color: [\r\n            [0.25, '#FF6E76'],\r\n            [0.5, '#FDDD60'],\r\n            [1, '#58D9F9'],\r\n          ],\r\n        },\r\n      },\r\n      data: [],\r\n      title: {\r\n        fontSize: 14,\r\n      },\r\n      detail: {\r\n        width: 40,\r\n        height: 14,\r\n        fontSize: 14,\r\n        color: '#fff',\r\n        backgroundColor: 'inherit',\r\n        borderRadius: 3,\r\n        formatter: '{value}%',\r\n      },\r\n    },\r\n  ],\r\n}\r\n//图表数据查询\r\nlet [{ dataSource, reload, pageTips, config }, { queryData }] = useChartHook(props, initOption)\r\n\r\n//初始化图表配置项\r\nfunction initOption(data) {\r\n  let chartData: any = dataSource.value\r\n  if (typeof chartData === 'string') {\r\n    chartData = JSON.parse(chartData)\r\n  }\r\n  if (chartData && chartData.length > 0) {\r\n    chartOption.series[0].data = chartData\r\n    // 合并配置\r\n    if (props.config && config.option) {\r\n      merge(chartOption, config.option)\r\n      chartOption = handleTotalAndUnit(props.compName, chartOption, config, chartData)\r\n      chartOption.tooltip.formatter = '{b} : {c}'\r\n      option.value = deepClone(chartOption)\r\n      pageTips.show = false\r\n    }\r\n  } else {\r\n    pageTips.status = 1\r\n    pageTips.show = true\r\n  }\r\n}\r\n\r\nonMounted(() => {\r\n  queryData()\r\n})\r\n</script>\r\n<style>\r\n.content {\r\n  padding: 10px;\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-work/components/echarts/JColorGauge/index.vue'\nwx.createComponent(Component)"], "names": ["ref", "useChartHook", "merge", "handleTotalAndUnit", "deepClone", "onMounted", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAaA,MAAA,gBAA0B,MAAA;AAC1B,MAAA,YAAsB,MAAA;;;;;AAGtB,UAAM,QAAQ;AAKR,UAAA,SAASA,cAAI,IAAA,EAAE;AACrB,QAAI,cAAc;AAAA,MAChB,OAAO;AAAA,QACL,MAAM;AAAA,MACR;AAAA,MACA,QAAQ;AAAA,QACN;AAAA,UACE,MAAM;AAAA,UACN,QAAQ;AAAA,YACN,MAAM;AAAA,YACN,WAAW;AAAA,YACX,MAAM;AAAA,YACN,WAAW;AAAA,cACT,OAAO;AAAA,YAAA;AAAA,UAEX;AAAA,UACA,SAAS;AAAA,YACP,MAAM;AAAA,YACN,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,cAAc,CAAC,GAAG,IAAI;AAAA,YACtB,WAAW;AAAA,cACT,OAAO;AAAA,YAAA;AAAA,UAEX;AAAA,UACA,UAAU;AAAA,YACR,UAAU;AAAA,YACV,WAAW;AAAA,cACT,OAAO;AAAA,cACP,OAAO;AAAA,gBACL,CAAC,MAAM,SAAS;AAAA,gBAChB,CAAC,KAAK,SAAS;AAAA,gBACf,CAAC,GAAG,SAAS;AAAA,cAAA;AAAA,YACf;AAAA,UAEJ;AAAA,UACA,MAAM,CAAC;AAAA,UACP,OAAO;AAAA,YACL,UAAU;AAAA,UACZ;AAAA,UACA,QAAQ;AAAA,YACN,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,UAAU;AAAA,YACV,OAAO;AAAA,YACP,iBAAiB;AAAA,YACjB,cAAc;AAAA,YACd,WAAW;AAAA,UAAA;AAAA,QACb;AAAA,MACF;AAAA,IAEJ;AAEA,QAAI,CAAC,EAAE,YAAY,QAAQ,UAAU,OAAA,GAAU,EAAE,WAAW,IAAIC,kDAAa,OAAO,UAAU;AAG9F,aAAS,WAAW,MAAM;AACxB,UAAI,YAAiB,WAAW;AAC5B,UAAA,OAAO,cAAc,UAAU;AACrB,oBAAA,KAAK,MAAM,SAAS;AAAA,MAAA;AAE9B,UAAA,aAAa,UAAU,SAAS,GAAG;AACzB,oBAAA,OAAO,CAAC,EAAE,OAAO;AAEzB,YAAA,MAAM,UAAU,OAAO,QAAQ;AAC3BC,8BAAA,aAAa,OAAO,MAAM;AAChC,wBAAcC,uCAAmB,mBAAA,MAAM,UAAU,aAAa,QAAQ,SAAS;AAC/E,sBAAY,QAAQ,YAAY;AACzB,iBAAA,QAAQC,mCAAU,WAAW;AACpC,mBAAS,OAAO;AAAA,QAAA;AAAA,MAClB,OACK;AACL,iBAAS,SAAS;AAClB,iBAAS,OAAO;AAAA,MAAA;AAAA,IAClB;AAGFC,kBAAAA,UAAU,MAAM;AACJ,gBAAA;AAAA,IAAA,CACX;;;;;;;;;;;;;;;;ACpGD,GAAG,gBAAgBC,SAAS;"}