/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 辅助函数
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/* 转换成字符串 */
/* 判断是否存在 Modifier */
/* 判断是否存在伪类 */
/**
 * 主题色切换
 * @params $theme-color 主题色
 * @params $type 变暗’dark‘ 变亮 'light'
 * @params $mix-color 自己设置的混色
 */
/**
 * 颜色结果切换， 如果开启线性渐变色 使用渐变色，如果没有开启，那么使用主题色
 * @params $open-linear 是否开启线性渐变色
 * @params $deg 渐变色角度
 * @params $theme-color 当前配色
 * @params [Array] $set 主题色明暗设置，与 $color-list 数量对应
 * @params [Array] $color-list 渐变色顺序， $color-list 和 $per-list 数量相同
 * @params [Array] $per-list 渐变色比例
 */
/**
 * UI规范基础变量
 */
/*----------------------------------------- Theme color. start ----------------------------------------*/
/* 主题颜色 */
/* 辅助色 */
/* 文字颜色（默认浅色背景下 */
/* 暗黑模式 */
/* 图形颜色 */
/*----------------------------------------- Theme color. end -------------------------------------------*/
/*-------------------------------- Theme color application size.  start --------------------------------*/
/* 文字字号 */
/* 文字字重 */
/* 尺寸 */
/*-------------------------------- Theme color application size.  end --------------------------------*/
/* component var */
/* action-sheet */
/* badge */
/* button */
/* cell */
/* calendar */
/* checkbox */
/* collapse */
/* divider */
/* drop-menu */
/* input-number */
/* input */
/* textarea */
/* loadmore */
/* message-box */
/* notice-bar */
/* pagination */
/* picker */
/* col-picker */
/* overlay */
/* popup */
/* progress */
/* radio */
/* search */
/* slider */
/* sort-button */
/* steps */
/* switch */
/* tabs */
/* tag */
/* toast */
/* loading */
/* tooltip */
/* popover */
/* grid-item */
/* statustip */
/* card */
/* upload */
/* curtain */
/* notify */
/* skeleton */
/* circle */
/* swiper */
/* swiper-nav */
/* segmented */
/* tabbar */
/* tabbar-item */
/* navbar */
/* navbar-capsule */
/* table */
/* sidebar */
/* sidebar-item */
/* fab */
/* count-down */
/* keyboard */
/* number-keyboard */
/* passwod-input */
/* form-item */
/* backtop */
/* index-bar */
/* text */
/* video-preview */
/* img-cropper */
/* floating-panel */
/* signature */
/**
 * 混合宏
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/**
 * 辅助函数
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/* 转换成字符串 */
/* 判断是否存在 Modifier */
/* 判断是否存在伪类 */
/**
 * 主题色切换
 * @params $theme-color 主题色
 * @params $type 变暗’dark‘ 变亮 'light'
 * @params $mix-color 自己设置的混色
 */
/**
 * 颜色结果切换， 如果开启线性渐变色 使用渐变色，如果没有开启，那么使用主题色
 * @params $open-linear 是否开启线性渐变色
 * @params $deg 渐变色角度
 * @params $theme-color 当前配色
 * @params [Array] $set 主题色明暗设置，与 $color-list 数量对应
 * @params [Array] $color-list 渐变色顺序， $color-list 和 $per-list 数量相同
 * @params [Array] $per-list 渐变色比例
 */
/**
  * BEM，定义块（b)
  */
/* 定义元素（e），对于伪类，会自动将 e 嵌套在 伪类 底下 */
/* 此方法用于生成穿透样式 */
/* 定义元素（e），对于伪类，会自动将 e 嵌套在 伪类 底下 */
/* 定义状态（m） */
/* 定义状态（m） */
/* 对于需要需要嵌套在 m 底下的 e，调用这个混合宏，一般在切换整个组件的状态，如切换颜色的时候 */
/* 状态，生成 is-$state 类名 */
/**
  * 常用混合宏
  */
/* 单行超出隐藏 */
/* 多行超出隐藏 */
/* 清除浮动 */
/* 0.5px 边框 指定方向*/
/* 0.5px 边框 环绕 */
/**
  * 三角形实现尖角样式，适用于背景透明情况
  * @param $size 三角形高，底边为 $size * 2
  * @param $bg 三角形背景颜色
  */
/**
  * 正方形实现尖角样式，适用于背景不透明情况
  * @param $size 正方形边长
  * @param $bg 正方形背景颜色
  * @param $z-index z-index属性值，不得大于外部包裹器
  * @param $box-shadow 阴影
*/
.wot-theme-dark .wd-month__title.data-v-25abc529 {
  color: var(--wot-dark-color, var(--wot-color-white, rgb(255, 255, 255)));
}
.wot-theme-dark .wd-month__days.data-v-25abc529 {
  color: var(--wot-dark-color, var(--wot-color-white, rgb(255, 255, 255)));
}
.wot-theme-dark .wd-month__day.is-disabled .wd-month__day-text.data-v-25abc529 {
  color: var(--wot-dark-color-gray, var(--wot-color-secondary, #595959));
}
.wd-month__title.data-v-25abc529 {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 45px;
  font-size: var(--wot-calendar-panel-title-fs, 14px);
  color: var(--wot-calendar-panel-title-color, rgba(0, 0, 0, 0.85));
}
.wd-month__days.data-v-25abc529 {
  display: flex;
  flex-wrap: wrap;
  font-size: var(--wot-calendar-day-fs, 16px);
  color: var(--wot-calendar-day-color, rgba(0, 0, 0, 0.85));
}
.wd-month__day.data-v-25abc529 {
  position: relative;
  width: 14.285%;
  height: var(--wot-calendar-day-height, 64px);
  line-height: var(--wot-calendar-day-height, 64px);
  text-align: center;
  margin-bottom: var(--wot-calendar-item-margin-bottom, 4px);
}
.wd-month__day.is-disabled .wd-month__day-text.data-v-25abc529 {
  color: var(--wot-calendar-disabled-color, rgba(0, 0, 0, 0.25));
}
.wd-month__day.is-current.data-v-25abc529 {
  color: var(--wot-calendar-active-color, var(--wot-color-theme, #4d80f0));
}
.wd-month__day.is-selected .wd-month__day-container.data-v-25abc529 {
  border-radius: var(--wot-calendar-active-border, 8px);
  background: var(--wot-calendar-active-color, var(--wot-color-theme, #4d80f0));
  color: var(--wot-calendar-selected-color, var(--wot-color-white, rgb(255, 255, 255)));
}
.wd-month__day.is-multiple-selected .wd-month__day-container.data-v-25abc529 {
  border-radius: var(--wot-calendar-active-border, 8px);
  background: var(--wot-calendar-active-color, var(--wot-color-theme, #4d80f0));
  color: var(--wot-calendar-selected-color, var(--wot-color-white, rgb(255, 255, 255)));
}
.wd-month__day.is-middle .wd-month__day-container.data-v-25abc529 {
  background: var(--wot-calendar-range-color, rgba(77, 128, 240, 0.09));
}
.wd-month__day.is-multiple-middle .wd-month__day-container.data-v-25abc529 {
  background: var(--wot-calendar-active-color, var(--wot-color-theme, #4d80f0));
  color: var(--wot-calendar-selected-color, var(--wot-color-white, rgb(255, 255, 255)));
}
.wd-month__day.is-start.data-v-25abc529::after {
  position: absolute;
  content: "";
  height: var(--wot-calendar-day-height, 64px);
  top: 0;
  right: 0;
  left: 50%;
  background: var(--wot-calendar-range-color, rgba(77, 128, 240, 0.09));
  z-index: 1;
}
.wd-month__day.is-start.is-without-end.data-v-25abc529::after {
  display: none;
}
.wd-month__day.is-start .wd-month__day-container.data-v-25abc529 {
  background: var(--wot-calendar-active-color, var(--wot-color-theme, #4d80f0));
  color: var(--wot-calendar-selected-color, var(--wot-color-white, rgb(255, 255, 255)));
  border-radius: var(--wot-calendar-active-border, 8px) 0 0 var(--wot-calendar-active-border, 8px);
}
.wd-month__day.is-end.data-v-25abc529::after {
  position: absolute;
  content: "";
  height: var(--wot-calendar-day-height, 64px);
  top: 0;
  left: 0;
  right: 50%;
  background: var(--wot-calendar-range-color, rgba(77, 128, 240, 0.09));
  z-index: 1;
}
.wd-month__day.is-end .wd-month__day-container.data-v-25abc529 {
  background: var(--wot-calendar-active-color, var(--wot-color-theme, #4d80f0));
  color: var(--wot-calendar-selected-color, var(--wot-color-white, rgb(255, 255, 255)));
  border-radius: 0 var(--wot-calendar-active-border, 8px) var(--wot-calendar-active-border, 8px) 0;
}
.wd-month__day.is-same .wd-month__day-container.data-v-25abc529 {
  background: var(--wot-calendar-active-color, var(--wot-color-theme, #4d80f0));
  color: var(--wot-calendar-selected-color, var(--wot-color-white, rgb(255, 255, 255)));
  border-radius: var(--wot-calendar-active-border, 8px);
}
.wd-month__day.is-last-row.data-v-25abc529 {
  margin-bottom: 0;
}
.wd-month__day-container.data-v-25abc529 {
  position: relative;
  z-index: 2;
}
.wd-month__day-text.data-v-25abc529 {
  font-weight: var(--wot-calendar-day-fw, 500);
}
.wd-month__day-top.data-v-25abc529 {
  position: absolute;
  top: 10px;
  left: 0;
  right: 0;
  line-height: 1.1;
  font-size: var(--wot-calendar-info-fs, 10px);
  text-align: center;
}
.wd-month__day-bottom.data-v-25abc529 {
  position: absolute;
  bottom: 10px;
  left: 0;
  right: 0;
  line-height: 1.1;
  font-size: var(--wot-calendar-info-fs, 10px);
  text-align: center;
}