{"version": 3, "file": "wd-swipe-action.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-swipe-action/wd-swipe-action.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1zd2lwZS1hY3Rpb24vd2Qtc3dpcGUtYWN0aW9uLnZ1ZQ"], "sourcesContent": ["<template>\n  <!--注意阻止横向滑动的穿透：横向移动时阻止冒泡-->\n  <view\n    :class=\"`wd-swipe-action ${customClass}`\"\n    :style=\"customStyle\"\n    @click.stop=\"onClick()\"\n    @touchstart=\"startDrag\"\n    @touchmove=\"onDrag\"\n    @touchend=\"endDrag\"\n    @touchcancel=\"endDrag\"\n  >\n    <!--容器-->\n    <view class=\"wd-swipe-action__wrapper\" :style=\"wrapperStyle\">\n      <!--左侧操作-->\n      <view class=\"wd-swipe-action__left\" @click=\"onClick('left')\">\n        <slot name=\"left\" />\n      </view>\n      <!--内容-->\n      <slot />\n      <!--右侧操作-->\n      <view class=\"wd-swipe-action__right\" @click=\"onClick('right')\">\n        <slot name=\"right\" />\n      </view>\n    </view>\n  </view>\n</template>\n<script lang=\"ts\">\nexport default {\n  name: 'wd-swipe-action',\n  options: {\n    addGlobalClass: true,\n    virtualHost: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n<script lang=\"ts\" setup>\nimport { getCurrentInstance, inject, onBeforeMount, onBeforeUnmount, onMounted, ref, watch } from 'vue'\nimport { closeOther, pushToQueue, removeFromQueue } from '../common/clickoutside'\nimport { type Queue, queueKey } from '../composables/useQueue'\nimport { useTouch } from '../composables/useTouch'\nimport { getRect } from '../common/util'\nimport { swipeActionProps, type SwipeActionPosition, type SwipeActionReason, type SwipeActionStatus } from './types'\n\nconst props = defineProps(swipeActionProps)\nconst emit = defineEmits(['click', 'update:modelValue'])\n\nconst queue = inject<Queue | null>(queueKey, null)\n\nconst wrapperStyle = ref<string>('')\n\n// 滑动开始时，wrapper的偏移量\nconst originOffset = ref<number>(0)\n// wrapper现在的偏移量\nconst wrapperOffset = ref<number>(0)\n// 是否处于滑动状态\nconst touching = ref<boolean>(false)\n\nconst touch = useTouch()\n\nconst { proxy } = getCurrentInstance() as any\n\nwatch(\n  () => props.modelValue,\n  (value, old) => {\n    changeState(value, old)\n  },\n  {\n    deep: true\n  }\n)\n\nonBeforeMount(() => {\n  if (queue && queue.pushToQueue) {\n    queue.pushToQueue(proxy)\n  } else {\n    pushToQueue(proxy)\n  }\n  // 滑动开始时，wrapper的偏移量\n  originOffset.value = 0\n  // wrapper现在的偏移量\n  wrapperOffset.value = 0\n  // 是否处于滑动状态\n  touching.value = false\n})\n\nonMounted(() => {\n  touching.value = true\n  changeState(props.modelValue)\n  touching.value = false\n})\n\nonBeforeUnmount(() => {\n  if (queue && queue.removeFromQueue) {\n    queue.removeFromQueue(proxy)\n  } else {\n    removeFromQueue(proxy)\n  }\n})\n\nfunction changeState(value: SwipeActionStatus, old?: SwipeActionStatus) {\n  if (props.disabled) {\n    return\n  }\n  getWidths().then(([leftWidth, rightWidth]) => {\n    switch (value) {\n      case 'close':\n        // 调用此函数时，偏移量本就是0\n        if (wrapperOffset.value === 0) return\n        close('value', old)\n        break\n      case 'left':\n        swipeMove(leftWidth)\n        break\n      case 'right':\n        swipeMove(-rightWidth)\n        break\n    }\n  })\n}\n\n/**\n * @description 获取左/右操作按钮的宽度\n * @return {Promise<[Number, Number]>} 左宽度、右宽度\n */\nfunction getWidths() {\n  return Promise.all([\n    getRect('.wd-swipe-action__left', false, proxy).then((rect) => {\n      return rect.width ? rect.width : 0\n    }),\n    getRect('.wd-swipe-action__right', false, proxy).then((rect) => {\n      return rect.width ? rect.width : 0\n    })\n  ])\n}\n/**\n * @description wrapper滑动函数\n * @param {Number} offset 滑动漂移量\n */\nfunction swipeMove(offset = 0) {\n  // this.offset = offset\n  const transform = `translate3d(${offset}px, 0, 0)`\n  // 跟随手指滑动，不需要动画\n  const transition = touching.value ? 'none' : '.6s cubic-bezier(0.18, 0.89, 0.32, 1)'\n  wrapperStyle.value = `\n        -webkit-transform: ${transform};\n        -webkit-transition: ${transition};\n        transform: ${transform};\n        transition: ${transition};\n      `\n  // 记录容器当前偏移的量\n  wrapperOffset.value = offset\n}\n/**\n * @description click的handler\n * @param event\n */\nfunction onClick(position?: SwipeActionPosition) {\n  if (props.disabled || wrapperOffset.value === 0) {\n    return\n  }\n\n  position = position || 'inside'\n  close('click', position)\n  emit('click', {\n    value: position\n  })\n}\n/**\n * @description 开始滑动\n */\nfunction startDrag(event: TouchEvent) {\n  if (props.disabled) return\n\n  originOffset.value = wrapperOffset.value\n  touch.touchStart(event)\n  if (queue && queue.closeOther) {\n    queue.closeOther(proxy)\n  } else {\n    closeOther(proxy)\n  }\n}\n/**\n * @description 滑动时，逐渐展示按钮\n * @param event\n */\nfunction onDrag(event: TouchEvent) {\n  if (props.disabled) return\n\n  touch.touchMove(event)\n  if (touch.direction.value === 'vertical') {\n    return\n  } else {\n    event.preventDefault()\n    event.stopPropagation()\n  }\n\n  touching.value = true\n\n  // 本次滑动，wrapper应该设置的偏移量\n  const offset = originOffset.value + touch.deltaX.value\n  getWidths().then(([leftWidth, rightWidth]) => {\n    // 如果需要想滑出来的按钮不存在，对应的按钮肯定滑不出来，容器处于初始状态。此时需要模拟一下位于此处的start事件。\n    if ((leftWidth === 0 && offset > 0) || (rightWidth === 0 && offset < 0)) {\n      swipeMove(0)\n      return startDrag(event)\n    }\n    // 按钮已经展示完了，再滑动没有任何意义，相当于滑动结束。此时需要模拟一下位于此处的start事件。\n    if (leftWidth !== 0 && offset >= leftWidth) {\n      swipeMove(leftWidth)\n      return startDrag(event)\n    } else if (rightWidth !== 0 && -offset >= rightWidth) {\n      swipeMove(-rightWidth)\n      return startDrag(event)\n    }\n    swipeMove(offset)\n  })\n}\n/**\n * @description 滑动结束，自动修正位置\n */\nfunction endDrag() {\n  if (props.disabled) return\n  // 滑出\"操作按钮\"的阈值\n  const THRESHOLD = 0.3\n  touching.value = false\n\n  getWidths().then(([leftWidth, rightWidth]) => {\n    if (\n      originOffset.value < 0 && // 之前展示的是右按钮\n      wrapperOffset.value < 0 && // 目前仍然是右按钮\n      wrapperOffset.value - originOffset.value < rightWidth * THRESHOLD // 并且滑动的范围不超过右边框阀值\n    ) {\n      swipeMove(-rightWidth) // 回归右按钮\n      emit('update:modelValue', 'right')\n    } else if (\n      originOffset.value > 0 && // 之前展示的是左按钮\n      wrapperOffset.value > 0 && // 现在仍然是左按钮\n      originOffset.value - wrapperOffset.value < leftWidth * THRESHOLD // 并且滑动的范围不超过左按钮阀值\n    ) {\n      swipeMove(leftWidth) // 回归左按钮\n      emit('update:modelValue', 'left')\n    } else if (\n      rightWidth > 0 &&\n      originOffset.value >= 0 && // 之前是初始状态或者展示左按钮显\n      wrapperOffset.value < 0 && // 现在展示右按钮\n      Math.abs(wrapperOffset.value) > rightWidth * THRESHOLD // 视图中已经展示的右按钮长度超过阀值\n    ) {\n      swipeMove(-rightWidth)\n      emit('update:modelValue', 'right')\n    } else if (\n      leftWidth > 0 &&\n      originOffset.value <= 0 && // 之前初始状态或者右按钮显示\n      wrapperOffset.value > 0 && // 现在左按钮\n      Math.abs(wrapperOffset.value) > leftWidth * THRESHOLD // 视图中已经展示的左按钮长度超过阀值\n    ) {\n      swipeMove(leftWidth)\n      emit('update:modelValue', 'left')\n    } else {\n      // 回归初始状态\n      close('swipe')\n    }\n  })\n}\n/**\n * @description 关闭操过按钮，并在合适的时候调用 beforeClose\n */\nfunction close(reason: SwipeActionReason, position?: SwipeActionPosition) {\n  if (reason === 'swipe' && originOffset.value === 0) {\n    // offset：0 ——> offset：0\n    return swipeMove(0)\n  } else if (reason === 'swipe' && originOffset.value > 0) {\n    // offset > 0 ——> offset：0\n    position = 'left'\n  } else if (reason === 'swipe' && originOffset.value < 0) {\n    // offset < 0 ——> offset：0\n    position = 'right'\n  }\n\n  if (reason && position) {\n    props.beforeClose && props.beforeClose(reason, position)\n  }\n\n  swipeMove(0)\n  if (props.modelValue !== 'close') {\n    emit('update:modelValue', 'close')\n  }\n}\n\ndefineExpose({ close })\n</script>\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-swipe-action/wd-swipe-action.vue'\nwx.createComponent(Component)"], "names": ["inject", "queueKey", "ref", "useTouch", "getCurrentInstance", "watch", "onBeforeMount", "pushToQueue", "onMounted", "onBeforeUnmount", "removeFromQueue", "getRect", "closeOther"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AA2BA,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,gBAAgB;AAAA,EAAA;AAEpB;;;;;AAUA,UAAM,QAAQ;AACd,UAAM,OAAO;AAEP,UAAA,QAAQA,cAAAA,OAAqBC,cAAA,UAAU,IAAI;AAE3C,UAAA,eAAeC,kBAAY,EAAE;AAG7B,UAAA,eAAeA,kBAAY,CAAC;AAE5B,UAAA,gBAAgBA,kBAAY,CAAC;AAE7B,UAAA,WAAWA,kBAAa,KAAK;AAEnC,UAAM,QAAQC,cAAAA,SAAS;AAEjB,UAAA,EAAE,MAAM,IAAIC,iCAAmB;AAErCC,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,OAAO,QAAQ;AACd,oBAAY,OAAO,GAAG;AAAA,MACxB;AAAA,MACA;AAAA,QACE,MAAM;AAAA,MAAA;AAAA,IAEV;AAEAC,kBAAAA,cAAc,MAAM;AACd,UAAA,SAAS,MAAM,aAAa;AAC9B,cAAM,YAAY,KAAK;AAAA,MAAA,OAClB;AACLC,sBAAAA,YAAY,KAAK;AAAA,MAAA;AAGnB,mBAAa,QAAQ;AAErB,oBAAc,QAAQ;AAEtB,eAAS,QAAQ;AAAA,IAAA,CAClB;AAEDC,kBAAAA,UAAU,MAAM;AACd,eAAS,QAAQ;AACjB,kBAAY,MAAM,UAAU;AAC5B,eAAS,QAAQ;AAAA,IAAA,CAClB;AAEDC,kBAAAA,gBAAgB,MAAM;AAChB,UAAA,SAAS,MAAM,iBAAiB;AAClC,cAAM,gBAAgB,KAAK;AAAA,MAAA,OACtB;AACLC,sBAAAA,gBAAgB,KAAK;AAAA,MAAA;AAAA,IACvB,CACD;AAEQ,aAAA,YAAY,OAA0B,KAAyB;AACtE,UAAI,MAAM,UAAU;AAClB;AAAA,MAAA;AAEF,kBAAY,KAAK,CAAC,CAAC,WAAW,UAAU,MAAM;AAC5C,gBAAQ,OAAO;AAAA,UACb,KAAK;AAEH,gBAAI,cAAc,UAAU;AAAG;AAC/B,kBAAM,SAAS,GAAG;AAClB;AAAA,UACF,KAAK;AACH,sBAAU,SAAS;AACnB;AAAA,UACF,KAAK;AACH,sBAAU,CAAC,UAAU;AACrB;AAAA,QAAA;AAAA,MACJ,CACD;AAAA,IAAA;AAOH,aAAS,YAAY;AACnB,aAAO,QAAQ,IAAI;AAAA,QACjBC,sBAAQ,0BAA0B,OAAO,KAAK,EAAE,KAAK,CAAC,SAAS;AACtD,iBAAA,KAAK,QAAQ,KAAK,QAAQ;AAAA,QAAA,CAClC;AAAA,QACDA,sBAAQ,2BAA2B,OAAO,KAAK,EAAE,KAAK,CAAC,SAAS;AACvD,iBAAA,KAAK,QAAQ,KAAK,QAAQ;AAAA,QAClC,CAAA;AAAA,MAAA,CACF;AAAA,IAAA;AAMM,aAAA,UAAU,SAAS,GAAG;AAEvB,YAAA,YAAY,eAAe,MAAM;AAEjC,YAAA,aAAa,SAAS,QAAQ,SAAS;AAC7C,mBAAa,QAAQ;AAAA,6BACM,SAAS;AAAA,8BACR,UAAU;AAAA,qBACnB,SAAS;AAAA,sBACR,UAAU;AAAA;AAG9B,oBAAc,QAAQ;AAAA,IAAA;AAMxB,aAAS,QAAQ,UAAgC;AAC/C,UAAI,MAAM,YAAY,cAAc,UAAU,GAAG;AAC/C;AAAA,MAAA;AAGF,iBAAW,YAAY;AACvB,YAAM,SAAS,QAAQ;AACvB,WAAK,SAAS;AAAA,QACZ,OAAO;AAAA,MAAA,CACR;AAAA,IAAA;AAKH,aAAS,UAAU,OAAmB;AACpC,UAAI,MAAM;AAAU;AAEpB,mBAAa,QAAQ,cAAc;AACnC,YAAM,WAAW,KAAK;AAClB,UAAA,SAAS,MAAM,YAAY;AAC7B,cAAM,WAAW,KAAK;AAAA,MAAA,OACjB;AACLC,sBAAAA,WAAW,KAAK;AAAA,MAAA;AAAA,IAClB;AAMF,aAAS,OAAO,OAAmB;AACjC,UAAI,MAAM;AAAU;AAEpB,YAAM,UAAU,KAAK;AACjB,UAAA,MAAM,UAAU,UAAU,YAAY;AACxC;AAAA,MAAA,OACK;AACL,cAAM,eAAe;AACrB,cAAM,gBAAgB;AAAA,MAAA;AAGxB,eAAS,QAAQ;AAGjB,YAAM,SAAS,aAAa,QAAQ,MAAM,OAAO;AACjD,kBAAY,KAAK,CAAC,CAAC,WAAW,UAAU,MAAM;AAE5C,YAAK,cAAc,KAAK,SAAS,KAAO,eAAe,KAAK,SAAS,GAAI;AACvE,oBAAU,CAAC;AACX,iBAAO,UAAU,KAAK;AAAA,QAAA;AAGpB,YAAA,cAAc,KAAK,UAAU,WAAW;AAC1C,oBAAU,SAAS;AACnB,iBAAO,UAAU,KAAK;AAAA,QACb,WAAA,eAAe,KAAK,CAAC,UAAU,YAAY;AACpD,oBAAU,CAAC,UAAU;AACrB,iBAAO,UAAU,KAAK;AAAA,QAAA;AAExB,kBAAU,MAAM;AAAA,MAAA,CACjB;AAAA,IAAA;AAKH,aAAS,UAAU;AACjB,UAAI,MAAM;AAAU;AAEpB,YAAM,YAAY;AAClB,eAAS,QAAQ;AAEjB,kBAAY,KAAK,CAAC,CAAC,WAAW,UAAU,MAAM;AAC5C,YACE,aAAa,QAAQ;AAAA,QACrB,cAAc,QAAQ;AAAA,QACtB,cAAc,QAAQ,aAAa,QAAQ,aAAa,WACxD;AACA,oBAAU,CAAC,UAAU;AACrB,eAAK,qBAAqB,OAAO;AAAA,QAAA,WAEjC,aAAa,QAAQ;AAAA,QACrB,cAAc,QAAQ;AAAA,QACtB,aAAa,QAAQ,cAAc,QAAQ,YAAY,WACvD;AACA,oBAAU,SAAS;AACnB,eAAK,qBAAqB,MAAM;AAAA,QAEhC,WAAA,aAAa,KACb,aAAa,SAAS;AAAA,QACtB,cAAc,QAAQ;AAAA,QACtB,KAAK,IAAI,cAAc,KAAK,IAAI,aAAa,WAC7C;AACA,oBAAU,CAAC,UAAU;AACrB,eAAK,qBAAqB,OAAO;AAAA,QAEjC,WAAA,YAAY,KACZ,aAAa,SAAS;AAAA,QACtB,cAAc,QAAQ;AAAA,QACtB,KAAK,IAAI,cAAc,KAAK,IAAI,YAAY,WAC5C;AACA,oBAAU,SAAS;AACnB,eAAK,qBAAqB,MAAM;AAAA,QAAA,OAC3B;AAEL,gBAAM,OAAO;AAAA,QAAA;AAAA,MACf,CACD;AAAA,IAAA;AAKM,aAAA,MAAM,QAA2B,UAAgC;AACxE,UAAI,WAAW,WAAW,aAAa,UAAU,GAAG;AAElD,eAAO,UAAU,CAAC;AAAA,MACT,WAAA,WAAW,WAAW,aAAa,QAAQ,GAAG;AAE5C,mBAAA;AAAA,MACF,WAAA,WAAW,WAAW,aAAa,QAAQ,GAAG;AAE5C,mBAAA;AAAA,MAAA;AAGb,UAAI,UAAU,UAAU;AACtB,cAAM,eAAe,MAAM,YAAY,QAAQ,QAAQ;AAAA,MAAA;AAGzD,gBAAU,CAAC;AACP,UAAA,MAAM,eAAe,SAAS;AAChC,aAAK,qBAAqB,OAAO;AAAA,MAAA;AAAA,IACnC;AAGW,aAAA,EAAE,OAAO;;;;;;;;;;;;;;;;;;AChStB,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}