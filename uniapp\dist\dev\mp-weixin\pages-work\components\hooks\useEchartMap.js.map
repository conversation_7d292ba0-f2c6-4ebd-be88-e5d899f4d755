{"version": 3, "file": "useEchartMap.js", "sources": ["../../../../../../src/pages-work/components/hooks/useEchartMap.ts"], "sourcesContent": ["import {\r\n  deepMerge,\r\n  packageParams,\r\n  handleCalcFields,\r\n  handleDateFields,\r\n  getGeoCoordMap,\r\n  calcUnit,\r\n  keepTwoDecimals,\r\n} from '../common/echartUtil'\r\n\r\nimport { china } from '../common/china'\r\nimport { isFunction } from '@/utils/is'\r\nimport { http } from '@/utils/http'\r\nexport default function useChartHook(props, initOption, echarts?) {\r\n  const config = props.config\r\n  const dataSource = ref([])\r\n  const reload = ref(true)\r\n  const pageTips = reactive({\r\n    show: true,\r\n    status: 0, // 0:loading,1:暂无数据,2:网络超时\r\n  })\r\n  //地图数据\r\n  const areaCode = ref('');\r\n  const mapName = ref('');\r\n  const mapDataJson = ref({})\r\n  const city_center = ref([])\r\n  //操作图表配置项\r\n  let chartOption = {\r\n    title: {\r\n      show: true,\r\n    },\r\n    card: {\r\n      title: '',\r\n    },\r\n    tooltip: {\r\n      formatter: '',\r\n    },\r\n    legend: {\r\n      bottom: '5%',\r\n      left: 'center',\r\n    },\r\n    xAxis: {\r\n      type: 'category',\r\n      data: [],\r\n    },\r\n    yAxis: {\r\n      type: 'value',\r\n    },\r\n    series: [{}] as any,\r\n  }\r\n  //监听配置修改\r\n  watch(\r\n    props.config,\r\n    (config) => {\r\n      if (!props?.isView) {\r\n        console.log('=======props.config============')\r\n        queryData()\r\n      }\r\n    },\r\n    { deep: true },\r\n  )\r\n  /**\r\n   * 获取区域编码\r\n   */\r\n  const getAreaCode = computed(() => {\r\n    if (areaCode.value) {\r\n      return areaCode?.value\r\n    }\r\n    return props.config.option?.area && props.config.option.area?.value\r\n      ? props.config.option?.area?.value[props.config.option?.area?.value.length - 1]\r\n      : 'china'\r\n  })\r\n  /**\r\n   * 获取区域名称\r\n   */\r\n  let getAreaName: any = computed(() => {\r\n    return (\r\n      (config?.option?.area &&\r\n        config?.option?.area?.name &&\r\n        config?.option?.area?.name[config?.option?.area?.name.length - 1]) ||\r\n      '中国'\r\n    )\r\n  })\r\n\r\n  /**\r\n   * 城市点位\r\n   */\r\n  const city_point = computed(() => {\r\n    return city_center.value\r\n  })\r\n  /**\r\n   * 查询数据\r\n   * @param compConfig\r\n   * @param queryParams\r\n   */\r\n  function queryData(compConfig?, queryParams?) {\r\n    let config = compConfig ? compConfig : { ...props.config }\r\n    if (config.dataType == 2) {\r\n    } else if (config.dataType == 4) {\r\n      //查询配置\r\n      let params = getParams(config, queryParams)\r\n      //查询数据\r\n      http.post('/drag/onlDragDatasetHead/getTotalData', params).then((res: any) => {\r\n        if (res.success) {\r\n          let result = res.result.chartData\r\n          if (result && result.length > 0) {\r\n            try {\r\n              let arr = JSON.parse(JSON.stringify(result))\r\n              dataSource.value = handleDateFields(arr, config)\r\n              dataSource.value = handleCalcFields(arr, config.valueFields, config.assistYFields)\r\n              initOption && isFunction(initOption) && initOption()\r\n            } catch (e) {\r\n              console.log('查询数据报错', e)\r\n            }\r\n          } else {\r\n            dataSource.value = []\r\n            initOption && isFunction(initOption) && initOption()\r\n          }\r\n        }\r\n      })\r\n    } else {\r\n      //静态数据\r\n      let chartData = props.config?.chartData\r\n      if (typeof chartData === 'string') {\r\n        try {\r\n          chartData = JSON.parse(chartData as string)\r\n        } catch (e) {}\r\n      }\r\n      dataSource.value = chartData\r\n      initOption && initOption(chartData)\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取参数\r\n   * @param config\r\n   * @param params\r\n   */\r\n  function getParams(config, params) {\r\n    let queryParams = packageParams(config, params)\r\n    return {\r\n      tableName: config.tableName,\r\n      compName: config.compName,\r\n      config: {\r\n        type: config.typeFields || [],\r\n        name: config.nameFields || [],\r\n        value: config.valueFields || [],\r\n        assistValue: config.assistYFields || [],\r\n        assistType: config.assistTypeFields || [],\r\n        formType: config.formType,\r\n      },\r\n      condition: {\r\n        ...queryParams,\r\n      },\r\n    }\r\n  }\r\n  /**\r\n   * 获取热力图数据\r\n   * @param data\r\n   */\r\n  function getHeatMapData(data) {\r\n    let res = []\r\n    for (let i = 0; i < data.length; i++) {\r\n      let geoCoord = unref(city_center)[data[i].name]\r\n      if (geoCoord) {\r\n        res.push(geoCoord.concat(data[i].value))\r\n      }\r\n    }\r\n    return res\r\n  }\r\n\r\n  /**\r\n   * 查询城市中心数据\r\n   */\r\n  async function queryCityCenter() {\r\n    if (city_center.value.length == 0) {\r\n      const res:any = await http.get('/drag/mock/json/city_center');\r\n      city_center.value = res;\r\n    }\r\n  }\r\n  /**\r\n   * 注册地图\r\n   */\r\n  function registerMap() {\r\n    let areaCode = getAreaCode.value;\r\n    if (getAreaCode.value != 'china' && getAreaCode.value != '') {\r\n      http\r\n        .get('/drag/onlDragDatasetHead/getMapDataByCode', {\r\n          code: getAreaCode.value,\r\n          name: getAreaName.value,\r\n        })\r\n        .then((res: any) => {\r\n          const { success, result } = res\r\n          if (success) {\r\n            mapDataJson.value = JSON.parse(result.mapData)\r\n          }\r\n        })\r\n    } else {\r\n      mapDataJson.value = china\r\n    }\r\n    //echarts.registerMap(getAreaCode.value, mapDataJson.value);\r\n    //需要置空,避免清空区域选择无法恢复到中国地图模式\r\n    //areaCode.value = null;\r\n\tmapName.value  = areaCode\r\n    return areaCode\r\n  }\r\n  /**\r\n   * 获取转换后的地图数据\r\n   * @param chartData\r\n   */\r\n  function getConvertData(chartData) {\r\n    let geoCoordMap = getGeoCoordMap(mapDataJson.value)\r\n    let result = []\r\n    if (geoCoordMap) {\r\n      for (let i = 0; i < chartData.length; i++) {\r\n        let geoCoord = geoCoordMap[chartData[i].name]\r\n        if (geoCoord) {\r\n          result.push({\r\n            name: chartData[i].name,\r\n            code: geoCoord.adcode,\r\n            value: geoCoord.center.concat(chartData[i].value),\r\n          })\r\n        }\r\n      }\r\n    }\r\n    return result\r\n  }\r\n  /**\r\n   * 设置地图配色\r\n   * @param options\r\n   */\r\n  function setGeoAreaColor(options, config) {\r\n    //当视觉映射关闭时删除属性\r\n    if (options.visualMap && options.visualMap.show == false) {\r\n      delete options.visualMap\r\n    }\r\n    if (options.visualMap && options.visualMap.show == true) {\r\n      options.visualMap.inRange = {\r\n        color: config.commonOption.inRange.color,\r\n      }\r\n    }\r\n    //不使用渐变色\r\n    if (config.commonOption && config.commonOption.gradientColor == false) {\r\n      options.geo.itemStyle.normal.areaColor = config.commonOption.areaColor.color1\r\n    }\r\n\r\n    //开启渐变色\r\n    if (config.commonOption && config.commonOption.gradientColor == true) {\r\n      options.geo.itemStyle.normal.areaColor = {\r\n        type: 'radial',\r\n        x: 0.5,\r\n        y: 0.5,\r\n        r: 0.8,\r\n        colorStops: [\r\n          {\r\n            offset: 0,\r\n            color: config.commonOption.areaColor.color1,\r\n          },\r\n          {\r\n            offset: 1,\r\n            color: config.commonOption.areaColor.color2,\r\n          },\r\n        ],\r\n        globalCoord: false,\r\n      }\r\n    }\r\n    return options\r\n  }\r\n\r\n  /**\r\n   * 处理总计和显示单位\r\n   * @param {Object} compName\r\n   * @param {Object} chartOption\r\n   * @param {Object} config\r\n   * @param {Object} chartData\r\n   */\r\n  function handleTotalAndUnitMap(compName, chartOption, config, chartData) {\r\n    //1.获取到label配置项\r\n    if (config.compStyleConfig) {\r\n      //显示单位配置\r\n      let showUnitConfig = config.compStyleConfig.showUnit\r\n      let unit = showUnitConfig.unit ? showUnitConfig.unit : '' //单位\r\n      let numberLevel = showUnitConfig.numberLevel ? showUnitConfig.numberLevel : '' //数值数量级\r\n      chartOption.series.forEach((item) => {\r\n        if (item.name == '数据') {\r\n          let labelConfig = {\r\n            label: {\r\n              normal: {\r\n                show: unit ? true : false,\r\n                formatter: (params) => {\r\n                  let showLabel = `${params.name}: `\r\n                  let value = 0\r\n                  if (params.seriesType == 'effectScatter') {\r\n                    if (Array.isArray(params.value)) {\r\n                      value = params.value[2]\r\n                    } else {\r\n                      value = params.value\r\n                    }\r\n                  }\r\n                  //计算显示数值和添加前后缀\r\n                  if (unit) {\r\n                    showLabel +=\r\n                      showUnitConfig.position == 'suffix'\r\n                        ? `${calcUnit(value, showUnitConfig)}${unit}`\r\n                        : `${unit}${calcUnit(value, showUnitConfig)}`\r\n                  }\r\n                  return showLabel\r\n                },\r\n              },\r\n            },\r\n          }\r\n          deepMerge(item, { ...labelConfig })\r\n        }\r\n      })\r\n      //显示总计配置\r\n      let summaryConfig = config.compStyleConfig.summary\r\n      if (summaryConfig.showTotal && chartData && chartData.length > 0) {\r\n        //左y轴\r\n        let leftData = chartData.filter((item) => !item.yAxisIndex || item.yAxisIndex == '0')\r\n        let totalTitle = summaryConfig.showY ? calcTotal(summaryConfig, leftData) : ''\r\n        Object.assign(chartOption.title, { text: totalTitle })\r\n      }\r\n    }\r\n    //设置内边距\r\n    chartOption.geo.top = 20\r\n    return chartOption\r\n  }\r\n  /**\r\n   * 计算总计\r\n   * @param series\r\n   * @param summaryConfig\r\n   */\r\n  function calcTotal(summaryConfig, chartData) {\r\n    let rawData = chartData\r\n    if (rawData && rawData.length > 0) {\r\n      let showField = summaryConfig.showField\r\n      let showName = summaryConfig.showName || '总计'\r\n      let totalType = summaryConfig.totalType || 'sum'\r\n      let valueField = showField == 'all' ? 'value' : showField\r\n      let valueArr = rawData.map((item) => (item[valueField] ? item[valueField] : 0))\r\n      let total = 0\r\n      if (valueArr.length > 0) {\r\n        if (totalType == 'sum') {\r\n          total = valueArr.reduce((prev, cur) => prev + cur, 0)\r\n        } else if (totalType == 'max') {\r\n          total = Math.max.apply(Math, valueArr)\r\n        } else if (totalType == 'min') {\r\n          total = Math.min.apply(Math, valueArr)\r\n        } else if (totalType == 'average') {\r\n          // @ts-ignore\r\n          total = (valueArr.reduce((prev, cur) => prev + cur, 0) / valueArr.length).toFixed(2)\r\n        }\r\n      }\r\n      //TODO 换算单位和数值级别\r\n      return `${showName}: ${keepTwoDecimals(total)}`\r\n    }\r\n    return ''\r\n  }\r\n  /**\r\n   * 设置常规配置\r\n   * @param {Object} chartOption\r\n   */\r\n  function handleCommonOpt(chartOption) {\r\n    if (chartOption.visualMap) {\r\n      chartOption.visualMap.show = false\r\n    }\r\n    return chartOption\r\n  }\r\n  /**\r\n   * 获取转换后的地图数据\r\n   * @param chartData\r\n   */\r\n  function getFromConvertData(chartData) {\r\n    let result = [];\r\n    let names = [];\r\n    for (let i = 0; i < chartData.length; i++) {\r\n      let fromName = chartData[i].fromName;\r\n      if (fromName && names.indexOf(fromName) == -1) {\r\n        result.push({\r\n          name: fromName,\r\n          value: [chartData[i].fromLng, chartData[i].fromLat, chartData[i].value],\r\n        });\r\n        names.push(fromName);\r\n      }\r\n    }\r\n    return result;\r\n  }\r\n\r\n  /**\r\n   * 获取转换后的地图数据\r\n   * @param chartData\r\n   */\r\n  function getToConvertData(chartData) {\r\n    let result = [];\r\n    let names = [];\r\n    for (let i = 0; i < chartData.length; i++) {\r\n      let toName = chartData[i].toName;\r\n      if (toName && names.indexOf(toName) == -1) {\r\n        result.push({\r\n          name: toName,\r\n          value: [chartData[i].toLng, chartData[i].toLat, chartData[i].value],\r\n        });\r\n        names.push(toName);\r\n      }\r\n    }\r\n    return result;\r\n  }\r\n  return [\r\n    { dataSource, reload, pageTips, config, chartOption, mapDataJson,mapName, getAreaCode,city_point,city_center },\r\n    {\r\n      queryData,\r\n      registerMap,\r\n      handleTotalAndUnitMap,\r\n      handleCommonOpt,\r\n      setGeoAreaColor,\r\n      getConvertData,\r\n      queryCityCenter,\r\n      getHeatMapData,\r\n      getFromConvertData,\r\n      getToConvertData,\r\n    },\r\n  ]\r\n}\r\n"], "names": ["ref", "reactive", "watch", "config", "computed", "http", "handleDateFields", "handleCalcFields", "isFunction", "packageParams", "unref", "areaCode", "china", "getGeoCoordMap", "chartOption", "calcUnit", "deepMerge", "keepTwoDecimals"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAawB,SAAA,aAAa,OAAO,YAAY,SAAU;AAChE,QAAM,SAAS,MAAM;AACf,QAAA,aAAaA,cAAI,IAAA,EAAE;AACnB,QAAA,SAASA,kBAAI,IAAI;AACvB,QAAM,WAAWC,cAAAA,SAAS;AAAA,IACxB,MAAM;AAAA,IACN,QAAQ;AAAA;AAAA,EAAA,CACT;AAEK,QAAA,WAAWD,kBAAI,EAAE;AACjB,QAAA,UAAUA,kBAAI,EAAE;AAChB,QAAA,cAAcA,cAAI,IAAA,EAAE;AACpB,QAAA,cAAcA,cAAI,IAAA,EAAE;AAE1B,MAAI,cAAc;AAAA,IAChB,OAAO;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,MAAM;AAAA,MACJ,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,IACb;AAAA,IACA,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAA;AAAA,IACR;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,QAAQ,CAAC,CAAE,CAAA;AAAA,EACb;AAEAE,gBAAA;AAAA,IACE,MAAM;AAAA,IACN,CAACC,YAAW;AACN,UAAA,EAAC,+BAAO,SAAQ;AAClB,gBAAQ,IAAI,iCAAiC;AACnC,kBAAA;AAAA,MAAA;AAAA,IAEd;AAAA,IACA,EAAE,MAAM,KAAK;AAAA,EACf;AAIM,QAAA,cAAcC,cAAAA,SAAS,MAAM;;AACjC,QAAI,SAAS,OAAO;AAClB,aAAO,qCAAU;AAAA,IAAA;AAEZ,aAAA,WAAM,OAAO,WAAb,mBAAqB,WAAQ,WAAM,OAAO,OAAO,SAApB,mBAA0B,UAC1D,iBAAM,OAAO,WAAb,mBAAqB,SAArB,mBAA2B,QAAM,iBAAM,OAAO,WAAb,mBAAqB,SAArB,mBAA2B,MAAM,UAAS,KAC3E;AAAA,EAAA,CACL;AAIG,MAAA,cAAmBA,cAAAA,SAAS,MAAM;;AACpC,aACG,sCAAQ,WAAR,mBAAgB,WACf,4CAAQ,WAAR,mBAAgB,SAAhB,mBAAsB,WACtB,4CAAQ,WAAR,mBAAgB,SAAhB,mBAAsB,OAAK,4CAAQ,WAAR,mBAAgB,SAAhB,mBAAsB,KAAK,UAAS,OACjE;AAAA,EAAA,CAEH;AAKK,QAAA,aAAaA,cAAAA,SAAS,MAAM;AAChC,WAAO,YAAY;AAAA,EAAA,CACpB;AAMQ,WAAA,UAAU,YAAa,aAAc;;AAC5C,QAAID,UAAS,aAAa,aAAa,mBAAK,MAAM;AAC9CA,QAAAA,QAAO,YAAY;AAAG;AAAA,aACfA,QAAO,YAAY,GAAG;AAE3B,UAAA,SAAS,UAAUA,SAAQ,WAAW;AAE1CE,iBAAA,KAAK,KAAK,yCAAyC,MAAM,EAAE,KAAK,CAAC,QAAa;AAC5E,YAAI,IAAI,SAAS;AACX,cAAA,SAAS,IAAI,OAAO;AACpB,cAAA,UAAU,OAAO,SAAS,GAAG;AAC3B,gBAAA;AACF,kBAAI,MAAM,KAAK,MAAM,KAAK,UAAU,MAAM,CAAC;AAChC,yBAAA,QAAQC,wDAAiB,KAAKH,OAAM;AAC/C,yBAAW,QAAQI,wDAAiB,KAAKJ,QAAO,aAAaA,QAAO,aAAa;AACnE,4BAAAK,SAAA,WAAW,UAAU,KAAK,WAAW;AAAA,qBAC5C,GAAG;AACF,sBAAA,IAAI,UAAU,CAAC;AAAA,YAAA;AAAA,UACzB,OACK;AACL,uBAAW,QAAQ,CAAC;AACN,0BAAAA,SAAA,WAAW,UAAU,KAAK,WAAW;AAAA,UAAA;AAAA,QACrD;AAAA,MACF,CACD;AAAA,IAAA,OACI;AAED,UAAA,aAAY,WAAM,WAAN,mBAAc;AAC1B,UAAA,OAAO,cAAc,UAAU;AAC7B,YAAA;AACU,sBAAA,KAAK,MAAM,SAAmB;AAAA,iBACnC,GAAG;AAAA,QAAA;AAAA,MAAC;AAEf,iBAAW,QAAQ;AACnB,oBAAc,WAAW,SAAS;AAAA,IAAA;AAAA,EACpC;AAQO,WAAA,UAAUL,SAAQ,QAAQ;AAC7B,QAAA,cAAcM,uCAAAA,cAAcN,SAAQ,MAAM;AACvC,WAAA;AAAA,MACL,WAAWA,QAAO;AAAA,MAClB,UAAUA,QAAO;AAAA,MACjB,QAAQ;AAAA,QACN,MAAMA,QAAO,cAAc,CAAC;AAAA,QAC5B,MAAMA,QAAO,cAAc,CAAC;AAAA,QAC5B,OAAOA,QAAO,eAAe,CAAC;AAAA,QAC9B,aAAaA,QAAO,iBAAiB,CAAC;AAAA,QACtC,YAAYA,QAAO,oBAAoB,CAAC;AAAA,QACxC,UAAUA,QAAO;AAAA,MACnB;AAAA,MACA,WAAW,mBACN;AAAA,IAEP;AAAA,EAAA;AAMF,WAAS,eAAe,MAAM;AAC5B,QAAI,MAAM,CAAC;AACX,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,UAAI,WAAWO,cAAAA,MAAM,WAAW,EAAE,KAAK,CAAC,EAAE,IAAI;AAC9C,UAAI,UAAU;AACZ,YAAI,KAAK,SAAS,OAAO,KAAK,CAAC,EAAE,KAAK,CAAC;AAAA,MAAA;AAAA,IACzC;AAEK,WAAA;AAAA,EAAA;AAMT,WAAe,kBAAkB;AAAA;AAC3B,UAAA,YAAY,MAAM,UAAU,GAAG;AACjC,cAAM,MAAU,MAAML,gBAAK,IAAI,6BAA6B;AAC5D,oBAAY,QAAQ;AAAA,MAAA;AAAA,IACtB;AAAA;AAKF,WAAS,cAAc;AACrB,QAAIM,YAAW,YAAY;AAC3B,QAAI,YAAY,SAAS,WAAW,YAAY,SAAS,IAAI;AAC3DN,iBAAA,KACG,IAAI,6CAA6C;AAAA,QAChD,MAAM,YAAY;AAAA,QAClB,MAAM,YAAY;AAAA,MAAA,CACnB,EACA,KAAK,CAAC,QAAa;AACZ,cAAA,EAAE,SAAS,OAAA,IAAW;AAC5B,YAAI,SAAS;AACX,sBAAY,QAAQ,KAAK,MAAM,OAAO,OAAO;AAAA,QAAA;AAAA,MAC/C,CACD;AAAA,IAAA,OACE;AACL,kBAAY,QAAQO,kCAAA;AAAA,IAAA;AAKzB,YAAQ,QAASD;AACPA,WAAAA;AAAAA,EAAA;AAMT,WAAS,eAAe,WAAW;AAC7B,QAAA,cAAcE,uCAAAA,eAAe,YAAY,KAAK;AAClD,QAAI,SAAS,CAAC;AACd,QAAI,aAAa;AACf,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,YAAI,WAAW,YAAY,UAAU,CAAC,EAAE,IAAI;AAC5C,YAAI,UAAU;AACZ,iBAAO,KAAK;AAAA,YACV,MAAM,UAAU,CAAC,EAAE;AAAA,YACnB,MAAM,SAAS;AAAA,YACf,OAAO,SAAS,OAAO,OAAO,UAAU,CAAC,EAAE,KAAK;AAAA,UAAA,CACjD;AAAA,QAAA;AAAA,MACH;AAAA,IACF;AAEK,WAAA;AAAA,EAAA;AAMA,WAAA,gBAAgB,SAASV,SAAQ;AAExC,QAAI,QAAQ,aAAa,QAAQ,UAAU,QAAQ,OAAO;AACxD,aAAO,QAAQ;AAAA,IAAA;AAEjB,QAAI,QAAQ,aAAa,QAAQ,UAAU,QAAQ,MAAM;AACvD,cAAQ,UAAU,UAAU;AAAA,QAC1B,OAAOA,QAAO,aAAa,QAAQ;AAAA,MACrC;AAAA,IAAA;AAGF,QAAIA,QAAO,gBAAgBA,QAAO,aAAa,iBAAiB,OAAO;AACrE,cAAQ,IAAI,UAAU,OAAO,YAAYA,QAAO,aAAa,UAAU;AAAA,IAAA;AAIzE,QAAIA,QAAO,gBAAgBA,QAAO,aAAa,iBAAiB,MAAM;AAC5D,cAAA,IAAI,UAAU,OAAO,YAAY;AAAA,QACvC,MAAM;AAAA,QACN,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,YAAY;AAAA,UACV;AAAA,YACE,QAAQ;AAAA,YACR,OAAOA,QAAO,aAAa,UAAU;AAAA,UACvC;AAAA,UACA;AAAA,YACE,QAAQ;AAAA,YACR,OAAOA,QAAO,aAAa,UAAU;AAAA,UAAA;AAAA,QAEzC;AAAA,QACA,aAAa;AAAA,MACf;AAAA,IAAA;AAEK,WAAA;AAAA,EAAA;AAUT,WAAS,sBAAsB,UAAUW,cAAaX,SAAQ,WAAW;AAEvE,QAAIA,QAAO,iBAAiB;AAEtB,UAAA,iBAAiBA,QAAO,gBAAgB;AAC5C,UAAI,OAAO,eAAe,OAAO,eAAe,OAAO;AACrC,qBAAe,cAAc,eAAe,cAAc;AAC5EW,mBAAY,OAAO,QAAQ,CAAC,SAAS;AAC/B,YAAA,KAAK,QAAQ,MAAM;AACrB,cAAI,cAAc;AAAA,YAChB,OAAO;AAAA,cACL,QAAQ;AAAA,gBACN,MAAM,OAAO,OAAO;AAAA,gBACpB,WAAW,CAAC,WAAW;AACjB,sBAAA,YAAY,GAAG,OAAO,IAAI;AAC9B,sBAAI,QAAQ;AACR,sBAAA,OAAO,cAAc,iBAAiB;AACxC,wBAAI,MAAM,QAAQ,OAAO,KAAK,GAAG;AACvB,8BAAA,OAAO,MAAM,CAAC;AAAA,oBAAA,OACjB;AACL,8BAAQ,OAAO;AAAA,oBAAA;AAAA,kBACjB;AAGF,sBAAI,MAAM;AACR,iCACE,eAAe,YAAY,WACvB,GAAGC,uCAAA,SAAS,OAAO,cAAc,CAAC,GAAG,IAAI,KACzC,GAAG,IAAI,GAAGA,uCAAAA,SAAS,OAAO,cAAc,CAAC;AAAA,kBAAA;AAE1C,yBAAA;AAAA,gBAAA;AAAA,cACT;AAAA,YACF;AAAA,UAEJ;AACAC,iDAAAA,UAAU,MAAM,mBAAK,YAAa;AAAA,QAAA;AAAA,MACpC,CACD;AAEG,UAAA,gBAAgBb,QAAO,gBAAgB;AAC3C,UAAI,cAAc,aAAa,aAAa,UAAU,SAAS,GAAG;AAE5D,YAAA,WAAW,UAAU,OAAO,CAAC,SAAS,CAAC,KAAK,cAAc,KAAK,cAAc,GAAG;AACpF,YAAI,aAAa,cAAc,QAAQ,UAAU,eAAe,QAAQ,IAAI;AAC5E,eAAO,OAAOW,aAAY,OAAO,EAAE,MAAM,YAAY;AAAA,MAAA;AAAA,IACvD;AAGFA,iBAAY,IAAI,MAAM;AACfA,WAAAA;AAAAA,EAAA;AAOA,WAAA,UAAU,eAAe,WAAW;AAC3C,QAAI,UAAU;AACV,QAAA,WAAW,QAAQ,SAAS,GAAG;AACjC,UAAI,YAAY,cAAc;AAC1B,UAAA,WAAW,cAAc,YAAY;AACrC,UAAA,YAAY,cAAc,aAAa;AACvC,UAAA,aAAa,aAAa,QAAQ,UAAU;AAC5C,UAAA,WAAW,QAAQ,IAAI,CAAC,SAAU,KAAK,UAAU,IAAI,KAAK,UAAU,IAAI,CAAE;AAC9E,UAAI,QAAQ;AACR,UAAA,SAAS,SAAS,GAAG;AACvB,YAAI,aAAa,OAAO;AACtB,kBAAQ,SAAS,OAAO,CAAC,MAAM,QAAQ,OAAO,KAAK,CAAC;AAAA,QAAA,WAC3C,aAAa,OAAO;AAC7B,kBAAQ,KAAK,IAAI,MAAM,MAAM,QAAQ;AAAA,QAAA,WAC5B,aAAa,OAAO;AAC7B,kBAAQ,KAAK,IAAI,MAAM,MAAM,QAAQ;AAAA,QAAA,WAC5B,aAAa,WAAW;AAEjC,mBAAS,SAAS,OAAO,CAAC,MAAM,QAAQ,OAAO,KAAK,CAAC,IAAI,SAAS,QAAQ,QAAQ,CAAC;AAAA,QAAA;AAAA,MACrF;AAGF,aAAO,GAAG,QAAQ,KAAKG,uCAAAA,gBAAgB,KAAK,CAAC;AAAA,IAAA;AAExC,WAAA;AAAA,EAAA;AAMT,WAAS,gBAAgBH,cAAa;AACpC,QAAIA,aAAY,WAAW;AACzBA,mBAAY,UAAU,OAAO;AAAA,IAAA;AAExBA,WAAAA;AAAAA,EAAA;AAMT,WAAS,mBAAmB,WAAW;AACrC,QAAI,SAAS,CAAC;AACd,QAAI,QAAQ,CAAC;AACb,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACrC,UAAA,WAAW,UAAU,CAAC,EAAE;AAC5B,UAAI,YAAY,MAAM,QAAQ,QAAQ,KAAK,IAAI;AAC7C,eAAO,KAAK;AAAA,UACV,MAAM;AAAA,UACN,OAAO,CAAC,UAAU,CAAC,EAAE,SAAS,UAAU,CAAC,EAAE,SAAS,UAAU,CAAC,EAAE,KAAK;AAAA,QAAA,CACvE;AACD,cAAM,KAAK,QAAQ;AAAA,MAAA;AAAA,IACrB;AAEK,WAAA;AAAA,EAAA;AAOT,WAAS,iBAAiB,WAAW;AACnC,QAAI,SAAS,CAAC;AACd,QAAI,QAAQ,CAAC;AACb,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACrC,UAAA,SAAS,UAAU,CAAC,EAAE;AAC1B,UAAI,UAAU,MAAM,QAAQ,MAAM,KAAK,IAAI;AACzC,eAAO,KAAK;AAAA,UACV,MAAM;AAAA,UACN,OAAO,CAAC,UAAU,CAAC,EAAE,OAAO,UAAU,CAAC,EAAE,OAAO,UAAU,CAAC,EAAE,KAAK;AAAA,QAAA,CACnE;AACD,cAAM,KAAK,MAAM;AAAA,MAAA;AAAA,IACnB;AAEK,WAAA;AAAA,EAAA;AAEF,SAAA;AAAA,IACL,EAAE,YAAY,QAAQ,UAAU,QAAQ,aAAa,aAAY,SAAS,aAAY,YAAW,YAAY;AAAA,IAC7G;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IAAA;AAAA,EAEJ;AACF;;"}