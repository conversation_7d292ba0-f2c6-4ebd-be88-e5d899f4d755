"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
const store_user = require("../../store/user.js");
if (!Array) {
  const _component_NavBar = common_vendor.resolveComponent("NavBar");
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _easycom_uni_calendar2 = common_vendor.resolveComponent("uni-calendar");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_component_NavBar + _easycom_uni_icons2 + _easycom_PageLayout2 + _easycom_uni_calendar2 + _component_layout_default_uni)();
}
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
const _easycom_uni_calendar = () => "../../uni_modules/uni-calendar/components/uni-calendar/uni-calendar.js";
if (!Math) {
  (_easycom_uni_icons + _easycom_PageLayout + _easycom_uni_calendar)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "MedicationList"
}), {
  __name: "list",
  setup(__props) {
    const userStore = store_user.useUserStore();
    const recordList = common_vendor.ref([]);
    const loading = common_vendor.ref(false);
    const startDate = common_vendor.ref("");
    const endDate = common_vendor.ref("");
    const datePickerType = common_vendor.ref("");
    const calendar = common_vendor.ref(null);
    function onStartDateChange(e) {
      startDate.value = e.detail.value;
    }
    function onEndDateChange(e) {
      endDate.value = e.detail.value;
    }
    const dateConfirm = (e) => {
      const selectedDate = e.fulldate;
      if (datePickerType.value === "start") {
        startDate.value = selectedDate;
      } else if (datePickerType.value === "end") {
        endDate.value = selectedDate;
      }
    };
    const fetchData = () => {
      loading.value = true;
      const url = `${"https://www.mograine.cn/api"}/patient/getmedications`;
      const params = {};
      if (startDate.value) {
        params.beginDate = startDate.value;
      }
      if (endDate.value) {
        params.endDate = endDate.value;
      }
      try {
        params.userId = userStore.userInfo.userid;
        console.log("用户ID:", params.userId);
      } catch (error) {
        console.error("获取用户ID失败:", error);
        common_vendor.index.showToast({
          title: "获取用户信息失败",
          icon: "none"
        });
        loading.value = false;
        return;
      }
      common_vendor.index.request({
        url,
        method: "GET",
        data: params,
        success: (res) => {
          const response = res.data;
          console.log("接口返回数据:", response);
          if (response && response.code === 200) {
            if (response.result && response.result.records) {
              const rawRecords = response.result.records || [];
              recordList.value = rawRecords.map((item) => ({
                date: item.createTime,
                medications: item.specificNameList || [],
                // 用药列表
                idList: item.idList || []
              }));
              console.log("解析后的列表数据:", recordList.value);
            } else {
              recordList.value = [];
              console.log("没有找到records数据");
            }
          } else {
            common_vendor.index.showToast({
              title: response.msg || "获取数据失败",
              icon: "none"
            });
          }
        },
        fail: (err) => {
          console.error("获取数据失败:", err);
          common_vendor.index.showToast({
            title: "网络异常，请稍后重试",
            icon: "none"
          });
        },
        complete: () => {
          loading.value = false;
        }
      });
    };
    const searchByDate = () => {
      fetchData();
    };
    const resetDateFilter = () => {
      startDate.value = "";
      endDate.value = "";
      fetchData();
    };
    const navigateToAdd = () => {
      common_vendor.index.navigateTo({
        url: "/pages-data/medication/form"
      });
    };
    const navigateToDetail = (item) => {
      const idListStr = item.idList ? item.idList.join(",") : "";
      common_vendor.index.navigateTo({
        url: `/pages-data/medication/form?idList=${idListStr}&mode=view`
      });
    };
    common_vendor.onMounted(() => {
      fetchData();
    });
    common_vendor.onShow(() => {
      resetDateFilter();
    });
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.p({
          title: "用药情况"
        }),
        b: common_vendor.t(startDate.value || "开始日期"),
        c: common_vendor.p({
          type: "calendar",
          size: "15",
          color: "#666666"
        }),
        d: startDate.value,
        e: common_vendor.o(onStartDateChange),
        f: common_vendor.t(endDate.value || "结束日期"),
        g: common_vendor.p({
          type: "calendar",
          size: "15",
          color: "#666666"
        }),
        h: endDate.value,
        i: common_vendor.o(onEndDateChange),
        j: common_vendor.p({
          type: "reload",
          size: "18",
          color: "#07C160"
        }),
        k: common_vendor.o(resetDateFilter),
        l: common_vendor.p({
          type: "search",
          size: "16",
          color: "#FFFFFF"
        }),
        m: common_vendor.o(searchByDate),
        n: common_vendor.p({
          type: "plusempty",
          size: "16",
          color: "#FFFFFF"
        }),
        o: common_vendor.o(navigateToAdd),
        p: recordList.value.length === 0
      }, recordList.value.length === 0 ? {} : {}, {
        q: common_vendor.f(recordList.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item.date),
            b: common_vendor.t(item.medications.join("、")),
            c: "fb115aa5-8-" + i0 + ",fb115aa5-1",
            d: index,
            e: common_vendor.o(($event) => navigateToDetail(item), index)
          };
        }),
        r: common_vendor.p({
          type: "right",
          size: "16",
          color: "#07C160"
        }),
        s: common_vendor.sr(calendar, "fb115aa5-9,fb115aa5-0", {
          "k": "calendar"
        }),
        t: common_vendor.o(dateConfirm),
        v: common_vendor.p({
          insert: false,
          range: false
        })
      });
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-fb115aa5"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=list.js.map
