"use strict";
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _easycom_wd_select_picker2 = common_vendor.resolveComponent("wd-select-picker");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_select_picker2 + _easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_wd_select_picker = () => "../../node-modules/wot-design-uni/components/wd-select-picker/wd-select-picker.js";
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  (_easycom_wd_select_picker + _easycom_PageLayout)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "selectPicker",
  setup(__props) {
    const { show: showToast } = common_vendor.useToast();
    const columns = common_vendor.ref([
      {
        value: "101",
        label: "男装"
      },
      {
        value: "102",
        label: "奢侈品"
      },
      {
        value: "103",
        label: "女装"
      }
    ]);
    const value = common_vendor.ref(["101"]);
    const radioValue = common_vendor.ref("101");
    const handleRadioChange = ({ value: value2 }) => {
    };
    function handleChange({ value: value2 }) {
      showToast("选择了" + value2);
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(handleChange),
        b: common_vendor.o(($event) => value.value = $event),
        c: common_vendor.p({
          label: "多选",
          columns: columns.value,
          modelValue: value.value
        }),
        d: common_vendor.o(handleRadioChange),
        e: common_vendor.o(($event) => radioValue.value = $event),
        f: common_vendor.p({
          label: "单选",
          type: "radio",
          columns: columns.value,
          modelValue: radioValue.value
        }),
        g: common_vendor.p({
          navTitle: "单复选择器",
          backRouteName: "demo"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-ec48e647"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=selectPicker.js.map
