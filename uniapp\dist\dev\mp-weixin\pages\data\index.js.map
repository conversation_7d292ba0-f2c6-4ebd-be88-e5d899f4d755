{"version": 3, "file": "index.js", "sources": ["../../../../../src/pages/data/index.vue", "../../../../../uniPage:/cGFnZXMvZGF0YS9pbmRleC52dWU"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n    <PageLayout :navLeftArrow=\"false\" navLeftText=\"\">\r\n        <view class=\"page-container\">\r\n            <view class=\"scrollView\" v-if=\"Number(userStore.userInfo.userCategory) === 1\">\r\n                <view class=\"category-title\">\r\n                    <view class=\"title-indicator\"></view>\r\n                    <text class=\"title-text\">请选择录入数据</text>\r\n                </view>\r\n\r\n                <view class=\"data-grid\">\r\n                    <view class=\"data-card\" v-for=\"(item, index) in dataItems\" :key=\"index\"\r\n                        @click=\"navigateToDetail(item)\">\r\n                        <view class=\"card-icon\">\r\n                            <image :src=\"item.iconPath\" class=\"card-image\" mode=\"contain\"></image>\r\n                        </view>\r\n                        <view class=\"card-content\">\r\n                            <text class=\"card-title\">{{ item.title }}</text>\r\n                            <view class=\"card-action\">\r\n                                <text class=\"action-text\">查看详情</text>\r\n                                <uni-icons type=\"right\" size=\"14\" color=\"#07C160\"></uni-icons>\r\n                            </view>\r\n                        </view>\r\n                    </view>\r\n                </view>\r\n            </view>\r\n            <!-- 获得患者数据 -->\r\n            <scroll-view class=\"page-scroll-view\" scroll-y=\"true\">\r\n            <view class=\"scrollView\" v-if=\"Number(userStore.userInfo.userCategory) === 0 || Number(userStore.userInfo.userCategory) === 2\">\r\n                <view class=\"search-container\">\r\n                    <!-- 患者姓名搜索框-->\r\n                    <text class=\"search-label\">患者姓名：</text>\r\n                    <view class=\"search-input-container\">\r\n                        <input type=\"text\" v-model=\"searchParams.userName\" placeholder=\"请输入患者姓名\" class=\"search-input\" />\r\n                        <uni-icons v-if=\"searchParams.userName\" type=\"clear\" size=\"14\" color=\"#666666\" @click=\"searchParams.userName = ''\" class=\"clear-icon\" />\r\n                    </view>\r\n                    <view class=\"search-button\" @click=\"handleSearch\">\r\n                        <uni-icons type=\"search\" size=\"16\" color=\"#FFFFFF\"></uni-icons> \r\n                        <text class=\"search-button-text\">查询</text>\r\n                    </view>\r\n                </view>\r\n\r\n                <view class=\"recent-data\">\r\n                    <view class=\"loading-container\" v-if=\"loadingPatient\">\r\n                        <wd-loading />\r\n                    </view>\r\n                    <view v-else-if=\"patientList.length === 0\" class=\"empty-data\">\r\n                        <wd-text text=\"暂无记录\"></wd-text>\r\n                    </view>\r\n                    <view v-else class=\"data-items\">\r\n                        <view class=\"data-card\" v-for=\"(item, index) in patientList\" :key=\"index\" @click=\"viewPatientDetail(item)\">\r\n                            <view class=\"data-info\">\r\n                                <view class=\"data-icon\">\r\n                                    <image  :src=\"item.avatarUrl || '/static/default-avatar.png'\" class=\"card-image\" mode=\"contain\"></image>\r\n                                </view>\r\n                                <view class=\"data-details\">\r\n                                    <view class=\"data-title-row\">\r\n                                        <text class=\"data-type\">患者姓名：{{ item.realName || '-' }}</text>\r\n                                    </view>\r\n                                    <view class=\"data-content\">\r\n                                        <view class=\"data-fields\">\r\n                                            <text class=\"field-value\">性别：{{ item.sex || '-' }}</text>\r\n                                            <text class=\"field-value\">年龄：{{ calculateAge(item.birthDate) || '-' }}</text>\r\n                                            <text class=\"field-value\">手机号：{{ item.phoneNumber || '-' }}</text>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                            </view>\r\n                            <view class=\"data-action\">\r\n                                <text class=\"action-text\">查看详情</text>\r\n                                <uni-icons type=\"right\" size=\"16\" color=\"#07C160\"></uni-icons>\r\n                            </view>\r\n                        </view>\r\n                    </view>\r\n                </view>\r\n            </view>\r\n            </scroll-view>\r\n        </view>\r\n    </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport dragList from './components/dragList.vue'\r\nimport { platform, isMp } from '@/utils/platform'\r\nimport { useUserStore } from '@/store/user'\r\nimport dayjs from 'dayjs'\r\n\r\ndefineOptions({\r\n    name: 'data',\r\n    options: {\r\n        styleIsolation: 'shared',\r\n    },\r\n})\r\n\r\nimport { ref, computed } from 'vue'\r\nimport { http } from '@/utils/http'\r\nconst globalData = getApp().globalData\r\nconst { systemInfo, navHeight } = globalData\r\nconst { statusBarHeight } = systemInfo\r\n\r\nconst userStore = useUserStore()\r\nconst loadingPatient = ref(false);\r\nconst patientList = ref([]);\r\nconst searchParams = ref({\r\n    userName: ''\r\n})\r\n\r\n// 计算属性：判断当前用户是否有患者角色\r\nconst isPatientRole = computed(() => {\r\n    const userRoles = userStore.userInfo.roleList || []\r\n    return Array.isArray(userRoles) && userRoles.includes('1')\r\n})\r\n\r\n// 计算属性：判断当前用户是否有患者角色\r\nconst isDoctorRole = computed(() => {\r\n    const userRoles = userStore.userInfo.roleList || []\r\n    return Array.isArray(userRoles) && userRoles.includes('0') \r\n})\r\n// 计算属性：判断当前用户是否有社工角色\r\nconst isSocialRole = computed(() => {\r\n    const userRoles = userStore.userInfo.roleList || []\r\n    return Array.isArray(userRoles) && userRoles.includes('2') \r\n})\r\n\r\n// 数据项列表\r\nconst dataItems = ref([\r\n    {\r\n        title: '日常体征监测',\r\n        type: 'vital-signs',\r\n        path: '/pages-data/vital-signs/list',\r\n        iconPath: 'https://www.mograine.cn/images/vital-signs.png'\r\n    },\r\n    {\r\n        title: '用药情况',\r\n        type: 'medication',\r\n        path: '/pages-data/medication/list',\r\n        iconPath: 'https://www.mograine.cn/images/medication.png'\r\n    },\r\n    {\r\n        title: '院外检查报告',\r\n        type: 'examinationReport',\r\n        path: '/pages-data/examinationReport/list',\r\n        iconPath: 'https://www.mograine.cn/images/report.png'\r\n    },\r\n    {\r\n        title: '慢性心衰患者监测表\\n（3次/周）',\r\n        type: 'monitor',\r\n        path: '/pages-data/monitor/list',\r\n        iconPath: 'https://www.mograine.cn/images/monitor.png'\r\n    },\r\n    {\r\n        title: '慢性心衰患者心理量表\\n（1次/周）',\r\n        type: 'psychology',\r\n        path: '/pages-data/psychology/list',\r\n        iconPath: 'https://www.mograine.cn/images/psychology.png'\r\n    },\r\n    {\r\n        title: '慢性心衰患者日常生活指数评估\\n（1次/周）',\r\n        type: 'dailyLife',\r\n        path: '/pages-data/dailyLife/list',\r\n        iconPath: 'https://www.mograine.cn/images/dailyLife.png'\r\n    },\r\n])\r\n\r\n// 跳转到详情页\r\nconst navigateToDetail = (item) => {\r\n    if (!isPatientRole.value) {\r\n        uni.showToast({\r\n            title: '请先填写知情同意与登记！',\r\n            icon: 'none',\r\n            duration: 2000\r\n        })\r\n        return // 不执行后续跳转\r\n    }\r\n\r\n    // 如果用户有患者角色，正常跳转\r\n    uni.navigateTo({\r\n        url: item.path\r\n    })\r\n}\r\n// 查看患者详情\r\nconst viewPatientDetail = (item) => {\r\n    // if (!isDoctorRole.value) {\r\n    //     uni.showToast({\r\n    //         title: '请先注册医生账号！',\r\n    //         icon: 'none',\r\n    //         duration: 2000\r\n    //     })\r\n    //     return // 不执行后续跳转\r\n    // }\r\n\r\n    // 如果用户有医生角色，正常跳转\r\n    uni.navigateTo({\r\n        url: `/pages/data/detail?id=${item.userId}`\r\n    })\r\n}\r\n// 计算年龄\r\nconst calculateAge = (birthDate) => {\r\n    if (!birthDate) return '-'\r\n    const birth = new Date(birthDate)\r\n    const today = new Date()\r\n    let age = today.getFullYear() - birth.getFullYear()\r\n    const monthDiff = today.getMonth() - birth.getMonth()\r\n    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {\r\n        age--;\r\n    }\r\n    return age;\r\n}\r\n\r\n// 搜索处理函数\r\nconst handleSearch = () => {\r\n    if (!isSocialRole.value || !isDoctorRole.value) {\r\n        uni.showToast({\r\n            title: '请先注册账号！',\r\n            icon: 'none',\r\n            duration: 2000\r\n        })\r\n        return // 不执行后续跳转\r\n    }else {\r\n        fetchPatientList()\r\n    }\r\n}\r\n// 计算属性：判断用户是否有查看数据的权限\r\nconst hasCommAndDataPermission = computed(() => {\r\n  // 获取当前用户角色列表\r\n  const userRoles = userStore.userInfo.roleList || []\r\n  // 获取用户类别：0-医生，1-患者，2-社工\r\n  const userCategory = Number(userStore.userInfo.userCategory)\r\n\r\n  if (userCategory === 0 ) {\r\n    // 医生必须完成注册（具有医生角色）才能查看数据\r\n    return userRoles.includes('0')\r\n  }else if (userCategory === 2) {\r\n    // 社工必须完成注册（具有社工角色）才能查看数据\r\n    return userRoles.includes('2')\r\n  }\r\n  return false\r\n})\r\n// 获取患者数据\r\nconst fetchPatientList = async () => {\r\n     if (!hasCommAndDataPermission.value) {\r\n        patientList.value = [];\r\n        return;\r\n    }\r\n    try {\r\n        loadingPatient.value = true\r\n        \r\n        const params = {\r\n            realName: searchParams.value.userName,\r\n        }\r\n        http.get('/sys/user/1/getUserList', params).then((res:any)=>{\r\n            if (res.success && res.result) {\r\n                console.log('API返回的患者数据:', res.result.records)\r\n                patientList.value = res.result.records || []\r\n            } else {\r\n                uni.showToast({\r\n                    title: '获取患者列表失败',\r\n                    icon: 'none'\r\n                })\r\n            }\r\n        })\r\n    } finally {\r\n        loadingPatient.value = false\r\n    }\r\n}\r\n\r\nonMounted(() => {\r\n    fetchPatientList()\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-container {\r\n    display: flex;\r\n    flex-direction: column;\r\n    height: 100vh;\r\n    min-height: 0;\r\n    background-color: #f5f7fa;\r\n    padding-bottom: 40rpx;\r\n}\r\n\r\n.page-header {\r\n    background-color: #07C160;\r\n    padding: 40rpx 30rpx 60rpx;\r\n    color: white;\r\n    border-radius: 0 0 30rpx 30rpx;\r\n    margin-bottom: 20rpx;\r\n    box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.2);\r\n\r\n    .page-title {\r\n        font-size: 40rpx;\r\n        font-weight: bold;\r\n        margin-bottom: 10rpx;\r\n        display: block;\r\n    }\r\n\r\n    .page-subtitle {\r\n        font-size: 28rpx;\r\n        opacity: 0.9;\r\n    }\r\n}\r\n.page-scroll-view {\r\n  flex: 1;\r\n  min-height: 0;\r\n  width: 100%;\r\n  background-color: #f5f7fa;\r\n  overflow: auto;\r\n}\r\n\r\n    .category-title {\r\n        display: flex;\r\n        align-items: center;\r\n        margin: 30rpx 10rpx 20rpx;\r\n\r\n        .title-indicator {\r\n            width: 8rpx;\r\n            height: 30rpx;\r\n            background-color: #07C160;\r\n            border-radius: 4rpx;\r\n            margin-right: 16rpx;\r\n        }\r\n\r\n        .title-text {\r\n            font-size: 32rpx;\r\n            font-weight: 500;\r\n            color: #333;\r\n        }\r\n    }\r\n\r\n    .data-grid {\r\n        display: flex;\r\n        flex-direction: column;\r\n        gap: 20rpx;\r\n        margin-bottom: 30rpx;\r\n\r\n        .data-card {\r\n            display: flex;\r\n            align-items: center;\r\n            background-color: white;\r\n            border-radius: 16rpx;\r\n            padding: 24rpx;\r\n            box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n            transition: all 0.3s;\r\n\r\n            &:active {\r\n                transform: scale(0.98);\r\n                background-color: #fafafa;\r\n            }\r\n\r\n            .card-icon {\r\n                background-color: rgba(7, 193, 96, 0.1);\r\n                width: 90rpx;\r\n                height: 90rpx;\r\n                border-radius: 50%;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                margin-right: 24rpx;\r\n                flex-shrink: 0;\r\n            }\r\n\r\n            .card-image {\r\n                width: 50rpx;\r\n                height: 50rpx;\r\n                display: block;\r\n            }\r\n\r\n            .card-content {\r\n                flex: 1;\r\n                display: flex;\r\n                flex-direction: column;\r\n                justify-content: center;\r\n\r\n                .card-title {\r\n                    font-size: 32rpx;\r\n                    color: #333;\r\n                    font-weight: 500;\r\n                    margin-bottom: 8rpx;\r\n                    white-space: pre-line;\r\n                }\r\n\r\n                .card-action {\r\n                    display: flex;\r\n                    align-items: center;\r\n\r\n                    .action-text {\r\n                        font-size: 24rpx;\r\n                        color: #07C160;\r\n                        margin-right: 8rpx;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .help-section {\r\n        padding: 0 10rpx;\r\n        margin-top: 20rpx;\r\n\r\n        .help-card {\r\n            display: flex;\r\n            align-items: center;\r\n            background-color: #f0f9f4;\r\n            border-radius: 16rpx;\r\n            padding: 20rpx 24rpx;\r\n            border-left: 8rpx solid #07C160;\r\n\r\n            .help-icon {\r\n                background-color: #07C160;\r\n                width: 60rpx;\r\n                height: 60rpx;\r\n                border-radius: 50%;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                margin-right: 20rpx;\r\n            }\r\n\r\n            .help-content {\r\n                flex: 1;\r\n\r\n                .help-title {\r\n                    font-size: 28rpx;\r\n                    color: #333;\r\n                    font-weight: 500;\r\n                    margin-bottom: 4rpx;\r\n                }\r\n\r\n                .help-text {\r\n                    font-size: 24rpx;\r\n                    color: #666;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .search-container {\r\n        background: #FFFFFF;\r\n        padding: 20rpx;\r\n        border-radius: 12rpx;\r\n        margin-bottom: 20rpx;\r\n\r\n        .search-label {\r\n            margin-bottom: 40rpx;\r\n            \r\n            text {\r\n                font-size: 28rpx;\r\n                color: #333;\r\n            }\r\n        }\r\n\r\n        .search-input-container {\r\n            display: inline-block;\r\n            vertical-align: middle;\r\n            position: relative;\r\n\r\n            .search-input {\r\n                width: 130%;\r\n                height: 80rpx;\r\n                background: #F5F5F5;\r\n                border-radius: 8rpx;\r\n                padding: 0 60rpx 0 20rpx; // 调整右侧padding，给清除图标留出空间\r\n                font-size: 28rpx;\r\n            }\r\n\r\n            .clear-icon {\r\n                position: absolute;\r\n                right: -30%; // 调整清除图标的位置，确保它显示在输入框最右侧\r\n                top: 50%;\r\n                transform: translateY(-50%);\r\n                z-index: 1; // 确保图标在最上层\r\n                padding: 10rpx; // 增加点击区域\r\n            }\r\n        }\r\n\r\n        .search-button {\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            background: #07C160;\r\n            height: 80rpx;\r\n            border-radius: 8rpx;\r\n            cursor: pointer;\r\n            padding: 0 30rpx;\r\n            margin-top: 20rpx;\r\n            vertical-align: middle;\r\n\r\n            .search-button-text {\r\n                color: #FFFFFF;\r\n                font-size: 28rpx;\r\n                margin-left: 10rpx;\r\n            }\r\n        }\r\n    }\r\n    .recent-data {\r\n      padding: 0 20upx 20upx;\r\n      position: relative;\r\n      min-height: 200rpx;\r\n\r\n      .loading-container {\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        position: absolute;\r\n        top: 0;\r\n        left: 0;\r\n        width: 100%;\r\n        height: 100%;\r\n        z-index: 10;\r\n      }\r\n\r\n      .empty-data {\r\n        padding: 30upx 0;\r\n        text-align: center;\r\n        color: #999;\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n\r\n        .empty-image {\r\n          width: 200rpx;\r\n          height: 200rpx;\r\n          margin-bottom: 20rpx;\r\n        }\r\n      }\r\n      \r\n\r\n      .data-items {\r\n        .data-card {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          padding: 24rpx;\r\n          background-color: white;\r\n          border-radius: 16rpx;\r\n          margin-bottom: 16rpx;\r\n          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\r\n          transition: all 0.3s;\r\n          margin-left:  -20rpx;\r\n          margin-right: -20rpx;\r\n          &:active {\r\n            transform: scale(0.99);\r\n            background-color: #fafafa;\r\n          }\r\n\r\n          .data-info {\r\n            display: flex;\r\n            align-items: flex-start;\r\n            flex: 1;\r\n\r\n            .data-icon {\r\n              margin-right: 30rpx;\r\n            //   margin-top: 6rpx;\r\n              width: 90rpx;\r\n              height: 90rpx;\r\n              flex-shrink: 0;\r\n              overflow: hidden;\r\n              border-radius: 50%;\r\n\r\n              .card-image {\r\n                width: 100%;\r\n                height: 100%;\r\n                object-fit: cover;\r\n                border-radius: 50%;\r\n              }\r\n            }\r\n\r\n            .data-details {\r\n              flex: 1;\r\n              overflow: hidden;\r\n\r\n              .data-title-row {\r\n                display: flex;\r\n                justify-content: space-between;\r\n                margin-bottom: 8rpx;\r\n\r\n                .data-type {\r\n                  font-size: 30rpx;\r\n                  color: #333333;\r\n                  font-weight: 500;\r\n                }\r\n              }\r\n\r\n              .data-content {\r\n                .data-fields {\r\n                  display: flex;\r\n                  flex-direction: column;\r\n\r\n                    .field-value {\r\n                        color: #666666;\r\n                        font-size: 24rpx;\r\n                        display: block;\r\n                    }\r\n                }\r\n              }\r\n            }\r\n          }\r\n\r\n          .data-action {\r\n            display: flex;\r\n            align-items: center;\r\n            margin-left: 20rpx;\r\n\r\n            .action-text {\r\n              color: #07C160;\r\n              font-size: 26rpx;\r\n              margin-right: 10rpx;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n</style>", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages/data/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "ref", "computed", "uni", "http", "onMounted"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmGM,UAAA,aAAa,SAAS;AACtB,UAAA,EAAE,YAAY,UAAA,IAAc;AAC5B,UAAA,EAAE,oBAAoB;AAE5B,UAAM,YAAYA,WAAAA,aAAa;AACzB,UAAA,iBAAiBC,kBAAI,KAAK;AAC1B,UAAA,cAAcA,cAAI,IAAA,EAAE;AAC1B,UAAM,eAAeA,cAAAA,IAAI;AAAA,MACrB,UAAU;AAAA,IAAA,CACb;AAGK,UAAA,gBAAgBC,cAAAA,SAAS,MAAM;AACjC,YAAM,YAAY,UAAU,SAAS,YAAY,CAAC;AAClD,aAAO,MAAM,QAAQ,SAAS,KAAK,UAAU,SAAS,GAAG;AAAA,IAAA,CAC5D;AAGK,UAAA,eAAeA,cAAAA,SAAS,MAAM;AAChC,YAAM,YAAY,UAAU,SAAS,YAAY,CAAC;AAClD,aAAO,MAAM,QAAQ,SAAS,KAAK,UAAU,SAAS,GAAG;AAAA,IAAA,CAC5D;AAEK,UAAA,eAAeA,cAAAA,SAAS,MAAM;AAChC,YAAM,YAAY,UAAU,SAAS,YAAY,CAAC;AAClD,aAAO,MAAM,QAAQ,SAAS,KAAK,UAAU,SAAS,GAAG;AAAA,IAAA,CAC5D;AAGD,UAAM,YAAYD,cAAAA,IAAI;AAAA,MAClB;AAAA,QACI,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,MACd;AAAA,MACA;AAAA,QACI,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,MACd;AAAA,MACA;AAAA,QACI,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,MACd;AAAA,MACA;AAAA,QACI,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,MACd;AAAA,MACA;AAAA,QACI,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,MACd;AAAA,MACA;AAAA,QACI,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,MAAA;AAAA,IACd,CACH;AAGK,UAAA,mBAAmB,CAAC,SAAS;AAC3B,UAAA,CAAC,cAAc,OAAO;AACtBE,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAAA,CACb;AACD;AAAA,MAAA;AAIJA,oBAAAA,MAAI,WAAW;AAAA,QACX,KAAK,KAAK;AAAA,MAAA,CACb;AAAA,IACL;AAEM,UAAA,oBAAoB,CAAC,SAAS;AAWhCA,oBAAAA,MAAI,WAAW;AAAA,QACX,KAAK,yBAAyB,KAAK,MAAM;AAAA,MAAA,CAC5C;AAAA,IACL;AAEM,UAAA,eAAe,CAAC,cAAc;AAChC,UAAI,CAAC;AAAkB,eAAA;AACjB,YAAA,QAAQ,IAAI,KAAK,SAAS;AAC1B,YAAA,4BAAY,KAAK;AACvB,UAAI,MAAM,MAAM,YAAY,IAAI,MAAM,YAAY;AAClD,YAAM,YAAY,MAAM,SAAS,IAAI,MAAM,SAAS;AAChD,UAAA,YAAY,KAAM,cAAc,KAAK,MAAM,QAAQ,IAAI,MAAM,WAAY;AACzE;AAAA,MAAA;AAEG,aAAA;AAAA,IACX;AAGA,UAAM,eAAe,MAAM;AACvB,UAAI,CAAC,aAAa,SAAS,CAAC,aAAa,OAAO;AAC5CA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAAA,CACb;AACD;AAAA,MAAA,OACE;AACe,yBAAA;AAAA,MAAA;AAAA,IAEzB;AAEM,UAAA,2BAA2BD,cAAAA,SAAS,MAAM;AAE9C,YAAM,YAAY,UAAU,SAAS,YAAY,CAAC;AAElD,YAAM,eAAe,OAAO,UAAU,SAAS,YAAY;AAE3D,UAAI,iBAAiB,GAAI;AAEhB,eAAA,UAAU,SAAS,GAAG;AAAA,MAAA,WACrB,iBAAiB,GAAG;AAErB,eAAA,UAAU,SAAS,GAAG;AAAA,MAAA;AAExB,aAAA;AAAA,IAAA,CACR;AAED,UAAM,mBAAmB,MAAY;AAC5B,UAAA,CAAC,yBAAyB,OAAO;AAClC,oBAAY,QAAQ,CAAC;AACrB;AAAA,MAAA;AAEA,UAAA;AACA,uBAAe,QAAQ;AAEvB,cAAM,SAAS;AAAA,UACX,UAAU,aAAa,MAAM;AAAA,QACjC;AACAE,mBAAA,KAAK,IAAI,2BAA2B,MAAM,EAAE,KAAK,CAAC,QAAU;AACpD,cAAA,IAAI,WAAW,IAAI,QAAQ;AAC3B,oBAAQ,IAAI,eAAe,IAAI,OAAO,OAAO;AAC7C,wBAAY,QAAQ,IAAI,OAAO,WAAW,CAAC;AAAA,UAAA,OACxC;AACHD,0BAAAA,MAAI,UAAU;AAAA,cACV,OAAO;AAAA,cACP,MAAM;AAAA,YAAA,CACT;AAAA,UAAA;AAAA,QACL,CACH;AAAA,MAAA,UACH;AACE,uBAAe,QAAQ;AAAA,MAAA;AAAA,IAE/B;AAEAE,kBAAAA,UAAU,MAAM;AACK,uBAAA;AAAA,IAAA,CACpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9QD,GAAG,WAAW,eAAe;"}