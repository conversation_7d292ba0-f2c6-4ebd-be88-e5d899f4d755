"use strict";
const common_vendor = require("../../common/vendor.js");
const common_uitls = require("../../common/uitls.js");
const store_user = require("../../store/user.js");
const baseUrl = "https://www.mograine.cn/api";
class socket {
  constructor() {
    this.socketUrl = baseUrl;
    this.socketStart = false;
    this.socketType = "";
    this.monitorSocketError();
    this.monitorSocketClose();
    this.socketReceive();
  }
  init(socket_type, callback) {
    const userStore = store_user.useUserStore();
    const _this = this;
    {
      if (this.socketStart) {
        console.log("webSocket已经启动了");
      } else {
        _this.socketType = socket_type;
        let url = this.socketUrl.replace("https://", "wss://").replace("http://", "ws://") + "/" + socket_type + "/" + userStore.userInfo.userid + "_app";
        if (socket_type == "eoaNewChatSocket") {
          let randomMessageId = common_uitls.randomString(6);
          url = this.socketUrl.replace("https://", "wss://").replace("http://", "ws://") + "/eoaNewChatSocket/" + userStore.userInfo.userid + "/" + randomMessageId;
        }
        console.log("启动this.socketUrl连接地址：", url);
        let token = userStore.userInfo.token;
        common_vendor.index.connectSocket({
          url,
          method: "GET",
          protocols: [token]
        });
        common_vendor.index.onSocketOpen((res) => {
          this.socketStart = true;
          callback && callback();
          console.log("WebSocket连接已打开！");
        });
      }
    }
  }
  // Socket给服务器发送消息
  send(data, callback) {
    const userStore = store_user.useUserStore();
    if (userStore.userInfo.userid) {
      data.userUid = userStore.userInfo.userid;
    }
    console.log(data);
    common_vendor.index.sendSocketMessage({
      data: JSON.stringify(data),
      success: () => {
        callback && callback(true);
      },
      fail: () => {
        callback && callback(false);
      }
    });
  }
  // Socket接收服务器发送过来的消息
  socketReceive() {
    const _this = this;
    common_vendor.index.onSocketMessage(function(res) {
      console.log("APP:--》收到服务器内容：");
      let data = JSON.parse(res.data);
      _this.acceptMessage && _this.acceptMessage(data);
    });
  }
  // 关闭Socket
  closeSocket() {
    const _this = this;
    common_vendor.index.closeSocket();
    _this.socketStart = false;
  }
  // 监听Socket关闭
  monitorSocketClose() {
    const _this = this;
    common_vendor.index.onSocketClose(function(res) {
      console.log("WebSocket 已关闭！");
      _this.socketStart = false;
      setTimeout(function() {
        _this.init(_this.socketType);
      }, 3e3);
    });
  }
  // 监听Socket错误
  monitorSocketError() {
    const _this = this;
    common_vendor.index.onSocketError(function(res) {
      _this.socketStart = false;
      console.log("WebSocket连接打开失败，请检查！");
    });
  }
  // 心跳
  getHeartbeat() {
    const userStore = store_user.useUserStore();
    const _this = this;
    this.send(
      {
        type: "心跳",
        userUid: userStore.userInfo.userid
      },
      (val) => {
        setTimeout(() => {
          if (val)
            ;
          else {
            if (!_this.socketStart)
              ;
          }
        }, 1e4);
      }
    );
  }
}
const mySocket = new socket();
exports.mySocket = mySocket;
//# sourceMappingURL=socket.js.map
