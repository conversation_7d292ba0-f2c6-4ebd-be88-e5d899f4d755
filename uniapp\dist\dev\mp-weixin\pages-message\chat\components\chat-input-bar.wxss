/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.chat-input-bar.data-v-5a847842 {
  display: flex;
  flex-direction: row;
  align-items: center;
  border-top: solid 1px #f5f5f5;
  background-color: #f8f8f8;
  padding: 10rpx 20rpx;
}
.add-container.data-v-5a847842 {
  margin-right: 8px;
}
.add-container .icon.data-v-5a847842 {
  font-size: 26px;
}
.chat-input-container.data-v-5a847842 {
  flex: 1;
  display: flex;
  padding: 15rpx;
  background-color: white;
  border-radius: 10rpx;
}
.chat-input.data-v-5a847842 {
  flex: 1;
  font-size: 28rpx;
}
.emoji-container.data-v-5a847842 {
  width: 54rpx;
  height: 54rpx;
  margin: 10rpx 0rpx 10rpx 20rpx;
}
.emoji-img.data-v-5a847842 {
  width: 54rpx;
  height: 54rpx;
}
.chat-input-send.data-v-5a847842 {
  background-color: #007aff;
  margin: 10rpx 10rpx 10rpx 20rpx;
  border-radius: 10rpx;
  width: 110rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.chat-input-send-text.data-v-5a847842 {
  color: white;
  font-size: 26rpx;
}
.emoji-panel-container.data-v-5a847842 {
  border-top: 1px solid #e8e8e8;
  background-color: #f8f8f8;
  overflow: hidden;
  transition-property: height;
  transition-duration: 0.15s;
}
.emoji-panel.data-v-5a847842 {
  height: 100%;
  padding: 0 8vw;
}
.emoji-panel .swiperItem.data-v-5a847842 {
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
}
.emoji-panel .swiperItem .item.data-v-5a847842 {
  width: 12vw;
  height: 12vw;
  display: flex;
  justify-content: center;
  align-items: center;
}
.more-panel.data-v-5a847842 {
  display: flex;
  padding-top: 3vw;
}
.more-panel .box.data-v-5a847842 {
  width: 18vw;
  height: 18vw;
  border-radius: 10px;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 3vw 2vw 3vw;
}
.more-panel .box .icon.data-v-5a847842 {
  font-size: 30px;
}