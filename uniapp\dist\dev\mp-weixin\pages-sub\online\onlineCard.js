"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
const utils_http = require("../../utils/http.js");
require("../../plugin/uni-mini-router/router/index.js");
const plugin_uniMiniRouter_core_index = require("../../plugin/uni-mini-router/core/index.js");
const store_user = require("../../store/user.js");
const store_pageParams = require("../../store/page-params.js");
if (!Array) {
  const _easycom_wd_swipe_action2 = common_vendor.resolveComponent("wd-swipe-action");
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_swipe_action2 + _easycom_z_paging2 + _easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_wd_swipe_action = () => "../../node-modules/wot-design-uni/components/wd-swipe-action/wd-swipe-action.js";
const _easycom_z_paging = () => "../../uni_modules/z-paging/components/z-paging/z-paging.js";
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  (onlineTableCell + _easycom_wd_swipe_action + _easycom_z_paging + _easycom_PageLayout)();
}
const onlineTableCell = () => "./components/onlineTableCell.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "onlineCard",
  options: {
    styleIsolation: "shared"
  }
}), {
  __name: "onlineCard",
  setup(__props) {
    var _a, _b;
    const toast = common_vendor.useToast();
    const router = plugin_uniMiniRouter_core_index.useRouter();
    store_user.useUserStore();
    const paramsStore = store_pageParams.useParamsStore();
    const globalData = getApp().globalData;
    const { systemInfo } = globalData;
    const { safeArea } = systemInfo;
    const paging = common_vendor.ref(null);
    const columns = common_vendor.ref([]);
    const columnsInfo = common_vendor.ref({});
    const pageNo = common_vendor.ref(1);
    const pageSize = common_vendor.ref(10);
    common_vendor.ref(1);
    let pageParams = (_b = (_a = paramsStore.getPageParams("onlineCard")) == null ? void 0 : _a.data) != null ? _b : {};
    const dataList = common_vendor.ref([]);
    const getBoxStyle = common_vendor.computed(() => {
      let len = columns.value.length;
      if (len > 3)
        len = 3;
      return { width: `calc(${100 / len}% - 5px)` };
    });
    const queryParams = () => {
      return {
        pageNo: pageNo.value,
        pageSize: pageSize.value,
        order: "asc",
        column: "id",
        hasQuery: true
      };
    };
    const getColumns = () => {
      return new Promise((resove, reject) => {
        if (columns.value.length) {
          resove();
          return;
        }
        const analysis = (data) => {
          const len = data.length;
          const maxShowColumn = 3;
          let space = 1;
          if (len == 1) {
            space = 2;
          }
          const width = safeArea.width / (len > maxShowColumn ? maxShowColumn : len) - space;
          columns.value = data.map((item) => {
            return __spreadProps(__spreadValues({}, item), {
              prop: item.dataIndex,
              align: item.align,
              label: item.title,
              width
            });
          });
        };
        utils_http.http.get(`/online/cgform/api/getColumns/${pageParams.id}`).then((res) => {
          var _a2, _b2;
          if (res.success) {
            if ((_b2 = (_a2 = res.result) == null ? void 0 : _a2.columns) == null ? void 0 : _b2.length) {
              columnsInfo.value = res.result;
              analysis(res.result.columns);
              resove();
            }
          } else {
            toast.warning(res.message);
            reject();
          }
        }).catch((res) => {
          toast.error("加载列头失败~");
          reject();
        });
      });
    };
    const getData = () => {
      utils_http.http.get(`/online/cgform/api/getData/${pageParams.id}`, __spreadValues({}, queryParams())).then((res) => {
        var _a2, _b2;
        if (res.success) {
          paging.value.complete((_b2 = (_a2 = res.result) == null ? void 0 : _a2.records) != null ? _b2 : []);
        } else {
          toast.warning(res.message);
        }
      }).catch((res) => {
        toast.error("加载表格数据失败~");
      });
    };
    const handleAction = (val, item) => {
      {
        utils_http.http.delete(`/online/cgform/api/form/${pageParams.id}/${item.id}`).then((res) => {
          toast.success("删除成功~");
          paging.value.reload();
        });
      }
    };
    const queryList = (_pageNo, _pageSize) => {
      pageNo.value = _pageNo;
      pageSize.value = _pageSize;
      getColumns().then(() => {
        getData();
      });
    };
    const handleAdd = () => {
      router.push({
        name: "onlineAdd",
        params: {
          desformCode: pageParams.tableName,
          desformName: pageParams.tableTxt,
          backRouteName: "onlineCard"
        }
      });
    };
    const handleGoTable = (params) => {
      paramsStore.setPageParams("onlineTable", { data: pageParams });
      router.push({ name: "onlineTable" });
    };
    const handleEdit = (record) => {
      router.push({
        name: "onlineEdit",
        params: {
          desformCode: pageParams.tableName,
          desformName: pageParams.tableTxt,
          id: record.id,
          backRouteName: "onlineCard"
        }
      });
    };
    common_vendor.onMounted(() => {
      common_vendor.index.$on("refreshList", () => {
        getData();
      });
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.f(dataList.value, (item, index, i0) => {
          return {
            a: common_vendor.f(columns.value, (cItem, cIndex, i1) => {
              return common_vendor.e$1({
                a: cIndex < 3
              }, cIndex < 3 ? {
                b: common_vendor.t(cItem["title"]),
                c: item.id,
                d: "75097847-4-" + i0 + "-" + i1 + "," + ("75097847-3-" + i0),
                e: common_vendor.p({
                  columnsInfo: columnsInfo.value,
                  record: item,
                  column: cItem
                }),
                f: common_vendor.s(getBoxStyle.value)
              } : {});
            }),
            b: index,
            c: common_vendor.o(($event) => handleEdit(item), item.id),
            d: common_vendor.o(($event) => handleAction("del", item), item.id),
            e: "75097847-3-" + i0 + ",75097847-2",
            f: item.id
          };
        }),
        b: common_vendor.sr(paging, "75097847-2,75097847-1", {
          "k": "paging"
        }),
        c: common_vendor.o(queryList),
        d: common_vendor.o(($event) => dataList.value = $event),
        e: common_vendor.p({
          fixed: false,
          ["default-page-size"]: 15,
          modelValue: dataList.value
        }),
        f: common_vendor.o(handleAdd),
        g: common_vendor.o(handleGoTable),
        h: common_vendor.p({
          backRouteName: "online",
          navTitle: "online在线表单"
        })
      };
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-75097847"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=onlineCard.js.map
