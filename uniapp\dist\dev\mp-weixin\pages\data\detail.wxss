/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-scroll-view.data-v-d014d19c {
  height: calc(100vh - 44px);
  width: 100%;
  background-color: #f5f7fa;
}
.patient-data.data-v-d014d19c {
  padding: 0 20rpx 20rpx;
  position: relative;
  min-height: 200rpx;
}
.patient-data .loading-container.data-v-d014d19c {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
}
.patient-data .empty-data.data-v-d014d19c {
  padding: 30rpx 0;
  text-align: center;
  color: #999;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.patient-data .empty-data .empty-image.data-v-d014d19c {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}
.patient-data .data-items .data-card.data-v-d014d19c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background-color: white;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}
.patient-data .data-items .data-card.data-v-d014d19c:active {
  transform: scale(0.99);
  background-color: #fafafa;
}
.patient-data .data-items .data-card .data-info.data-v-d014d19c {
  display: flex;
  align-items: flex-start;
  flex: 1;
}
.patient-data .data-items .data-card .data-info .data-icon.data-v-d014d19c {
  margin-right: 16rpx;
  background-color: rgba(7, 193, 96, 0.1);
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.patient-data .data-items .data-card .data-info .card-image.data-v-d014d19c {
  width: 50rpx;
  height: 50rpx;
  display: block;
}
.patient-data .data-items .data-card .data-info .data-details.data-v-d014d19c {
  flex: 1;
  overflow: hidden;
}
.patient-data .data-items .data-card .data-info .data-details .data-title-row.data-v-d014d19c {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8rpx;
}
.patient-data .data-items .data-card .data-info .data-details .data-title-row .data-type.data-v-d014d19c {
  font-size: 30rpx;
  color: #333333;
  font-weight: 500;
}
.patient-data .data-items .data-card .data-info .data-details .data-content .data-fields.data-v-d014d19c {
  display: flex;
  flex-direction: column;
}
.patient-data .data-items .data-card .data-info .data-details .data-content .data-fields .field-value.data-v-d014d19c {
  color: #666666;
  font-size: 24rpx;
  display: block;
}
.patient-data .data-items .data-card .data-action.data-v-d014d19c {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
}
.patient-data .data-items .data-card .data-action .action-text.data-v-d014d19c {
  color: #07C160;
  font-size: 26rpx;
  margin-right: 10rpx;
}
.filter-section.data-v-d014d19c {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.filter-section .date-filter.data-v-d014d19c {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  flex-wrap: wrap;
  /* 允许在窄屏设备上换行 */
  gap: 10rpx 0;
  /* 设置行间距，以防换行时元素太挤 */
}
.filter-section .date-filter .filter-label.data-v-d014d19c {
  color: #333333;
  font-size: 28rpx;
  margin-right: 10rpx;
  font-weight: 500;
}
.filter-section .date-filter .date-picker.data-v-d014d19c {
  flex: 2;
  min-width: 210rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #F7F7F7;
  padding: 12rpx 20rpx;
  border-radius: 8rpx;
}
.filter-section .date-filter .date-picker text.data-v-d014d19c {
  color: #666666;
  font-size: 26rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.filter-section .date-filter .date-separator.data-v-d014d19c {
  margin: 0 15rpx;
  color: #666666;
  font-size: 28rpx;
}
.filter-section .date-filter .reset-btn.data-v-d014d19c {
  margin-left: 3rpx;
  width: 35rpx;
  height: 35rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(7, 193, 96, 0.1);
  border-radius: 50%;
  transition: all 0.2s;
}
.filter-section .date-filter .reset-btn.data-v-d014d19c:active {
  transform: scale(0.9) rotate(180deg);
}
.filter-section .type-filter.data-v-d014d19c {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.filter-section .type-filter .filter-label.data-v-d014d19c {
  color: #333333;
  font-size: 28rpx;
  margin-right: 10rpx;
  font-weight: 500;
}
.filter-section .type-filter .type-picker.data-v-d014d19c {
  flex: 1;
  min-width: 475rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #F7F7F7;
  padding: 12rpx 20rpx;
  border-radius: 8rpx;
}
.filter-section .type-filter .type-picker text.data-v-d014d19c {
  color: #666666;
  font-size: 26rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.filter-section .buttons-row.data-v-d014d19c {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}
.filter-section .buttons-row .filter-btn.data-v-d014d19c {
  flex: 1;
  background-color: #07C160;
  color: #FFFFFF;
  text-align: center;
  padding: 15rpx 0;
  border-radius: 8rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 8rpx rgba(7, 193, 96, 0.2);
  transition: all 0.3s;
}
.filter-section .buttons-row .filter-btn.data-v-d014d19c:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 4rpx rgba(7, 193, 96, 0.2);
}
.filter-section .buttons-row .filter-btn text.data-v-d014d19c {
  margin-left: 10rpx;
}