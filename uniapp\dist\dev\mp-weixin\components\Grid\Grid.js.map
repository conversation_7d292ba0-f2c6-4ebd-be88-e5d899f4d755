{"version": 3, "file": "Grid.js", "sources": ["../../../../../src/components/Grid/Grid.vue", "../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvY29tcG9uZW50cy9HcmlkL0dyaWQudnVl"], "sourcesContent": ["<template>\r\n  <view\r\n    class=\"wrap\"\r\n    :style=\"{\r\n      '--borderColor': borderColor,\r\n      '--imgWidth': imgWidth,\r\n      '--imgHeight': imgHeight,\r\n    }\"\r\n  >\r\n    <wd-grid :clickable=\"clickable\" :column=\"column\">\r\n      <template v-for=\"(item, index) in modelValue\" :key=\"item[itemKey]\">\r\n        <wd-grid-item\r\n          :custom-class=\"getClass(index)\"\r\n          use-icon-slot\r\n          :text=\"item.text\"\r\n          @itemclick=\"handleClik(item, index)\"\r\n        >\r\n          <template #icon>\r\n            <wd-img :width=\"imgWidth\" :height=\"imgHeight\" :src=\"item.img\"></wd-img>\r\n          </template>\r\n        </wd-grid-item>\r\n      </template>\r\n    </wd-grid>\r\n  </view>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref } from 'vue'\r\ndefineOptions({\r\n  name: 'Grid',\r\n  options: {\r\n    styleIsolation: 'shared',\r\n  },\r\n})\r\nconst props = defineProps({\r\n  modelValue: {\r\n    type: Array,\r\n    default: () => [],\r\n  },\r\n  column: {\r\n    type: Number,\r\n    default: 4,\r\n  },\r\n  itemKey: {\r\n    type: String,\r\n    default: 'id',\r\n  },\r\n  imgWidth: {\r\n    type: String,\r\n    default: '28px',\r\n  },\r\n  imgHeight: {\r\n    type: String,\r\n    default: '28px',\r\n  },\r\n  clickable: {\r\n    type: Boolean,\r\n    default: true,\r\n  },\r\n  borderColor: {\r\n    type: String,\r\n    default: 'rgba(165, 165, 165, 0.1)',\r\n  },\r\n})\r\nconst emit = defineEmits(['itemClik'])\r\nconst getClass = (index) => {\r\n  let className = ''\r\n  if (index < props.column) {\r\n    className = 'first-row'\r\n  }\r\n  if ((index + 1) % props.column == 0) {\r\n    className += ` lastCol`\r\n  }\r\n  return className\r\n}\r\nconst handleClik = (item, index) => {\r\n  emit('itemClik', item, index)\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n:deep(.wd-grid-item) {\r\n  box-sizing: border-box;\r\n  border-right: 1px solid var(--borderColor, rgba(165, 165, 165, 0.1));\r\n  border-bottom: 1px solid var(--borderColor, rgba(165, 165, 165, 0.1));\r\n  &.first-row {\r\n    border-top: 1px solid var(--borderColor, rgba(165, 165, 165, 0.1));\r\n  }\r\n  &.lastCol {\r\n    border-right: none;\r\n  }\r\n  .wd-grid-item__text {\r\n    margin-top: 10px;\r\n  }\r\n  .wd-grid-item__wrapper {\r\n    width: var(--imgWidth, 28px) !important;\r\n    height: var(--imgHeight, 28px) !important;\r\n  }\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/components/Grid/Grid.vue'\nwx.createComponent(Component)"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,UAAM,QAAQ;AA8Bd,UAAM,OAAO;AACP,UAAA,WAAW,CAAC,UAAU;AAC1B,UAAI,YAAY;AACZ,UAAA,QAAQ,MAAM,QAAQ;AACZ,oBAAA;AAAA,MAAA;AAEd,WAAK,QAAQ,KAAK,MAAM,UAAU,GAAG;AACtB,qBAAA;AAAA,MAAA;AAER,aAAA;AAAA,IACT;AACM,UAAA,aAAa,CAAC,MAAM,UAAU;AAC7B,WAAA,YAAY,MAAM,KAAK;AAAA,IAC9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5EA,GAAG,gBAAgB,SAAS;"}