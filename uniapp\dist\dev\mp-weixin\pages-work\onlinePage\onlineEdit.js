"use strict";
const common_vendor = require("../../common/vendor.js");
const router_index = require("../../router/index.js");
if (!Array) {
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  (OnlineLoader + _easycom_PageLayout)();
}
const OnlineLoader = () => "../../components/online/online-loader.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "onlineEdit",
  setup(__props) {
    const tableName = common_vendor.ref("");
    const navTitle = common_vendor.ref("");
    const dataId = common_vendor.ref("");
    const online = common_vendor.ref(null);
    const backRouteName = common_vendor.ref("onlineTable");
    const initForm = (item) => {
      console.log("initForm item", item);
      tableName.value = item.desformCode;
      navTitle.value = `表单【${item.desformName}】`;
      dataId.value = item.id;
      item.backRouteName && (backRouteName.value = item.backRouteName);
      common_vendor.nextTick$1(() => {
        online.value.loadByTableName(tableName.value);
      });
    };
    const backRoute = () => {
      router_index.router.back();
    };
    const handleSuccess = (id) => {
      common_vendor.index.$emit("refreshList");
      backRoute();
    };
    common_vendor.onLoad((option) => {
      initForm(option);
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.sr(online, "1397ba2a-2,1397ba2a-1", {
          "k": "online"
        }),
        b: common_vendor.o(handleSuccess),
        c: common_vendor.o(backRoute),
        d: common_vendor.p({
          table: common_vendor.unref(tableName),
          title: common_vendor.unref(navTitle),
          dataId: common_vendor.unref(dataId),
          edit: true,
          ["show-footer"]: true
        }),
        e: common_vendor.p({
          navTitle: common_vendor.unref(navTitle),
          backRouteName: common_vendor.unref(backRouteName)
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-1397ba2a"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=onlineEdit.js.map
