"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
const plugin_uniMiniRouter_symbols_index = require("./symbols/index.js");
const plugin_uniMiniRouter_router_index = require("./router/index.js");
const plugin_uniMiniRouter_utils_index = require("./utils/index.js");
function createRouter(options) {
  const router = {
    routes: options.routes,
    guardHooks: {
      beforeHooks: null,
      afterHooks: null
    },
    push(to) {
      return plugin_uniMiniRouter_router_index.navjump(to, this, "push");
    },
    replace(to) {
      return plugin_uniMiniRouter_router_index.navjump(to, this, "replace");
    },
    replaceAll(to) {
      return plugin_uniMiniRouter_router_index.navjump(to, this, "replaceAll");
    },
    pushTab(to) {
      return plugin_uniMiniRouter_router_index.navjump(to, this, "pushTab");
    },
    back(to) {
      return common_vendor.index.navigateBack(to);
    },
    beforeEach(userGuard) {
      plugin_uniMiniRouter_router_index.registerEachHooks(router, "beforeHooks", userGuard);
    },
    afterEach(userGuard) {
      plugin_uniMiniRouter_router_index.registerEachHooks(router, "afterHooks", userGuard);
    },
    install: function(app) {
      const router2 = this;
      app.provide(plugin_uniMiniRouter_symbols_index.routerKey, this);
      app.provide(plugin_uniMiniRouter_symbols_index.routeKey, this.route);
      plugin_uniMiniRouter_router_index.rewriteNavMethod(router2);
      app.mixin({
        beforeCreate() {
          if (this.$mpType === "page") {
            if (router2.guardHooks.afterHooks && router2.guardHooks.afterHooks[0]) {
              const from = router2.route.value;
              const to = plugin_uniMiniRouter_router_index.getCurrentPageRoute(router2);
              router2.guardHooks.afterHooks[0].call(null, to, from);
            }
          }
        },
        onLoad(option) {
          if (!plugin_uniMiniRouter_utils_index.isEmptyObject(option) && plugin_uniMiniRouter_utils_index.isEmptyObject(router2.route.value.query) && plugin_uniMiniRouter_utils_index.isEmptyObject(router2.route.value.params)) {
            router2.route.value = __spreadProps(__spreadValues({}, router2.route.value), { query: option });
          }
        },
        onShow() {
          if (this.$mpType === "page") {
            plugin_uniMiniRouter_router_index.saveCurrRouteByCurrPage(router2);
          }
        }
      });
      Object.defineProperty(app.config.globalProperties, "$Router", {
        get() {
          return router2;
        }
      });
      Object.defineProperty(app.config.globalProperties, "$Route", {
        enumerable: true,
        get: () => common_vendor.unref(this.route)
      });
    },
    route: common_vendor.shallowRef({ path: "/" })
  };
  return router;
}
exports.createRouter = createRouter;
//# sourceMappingURL=index.js.map
