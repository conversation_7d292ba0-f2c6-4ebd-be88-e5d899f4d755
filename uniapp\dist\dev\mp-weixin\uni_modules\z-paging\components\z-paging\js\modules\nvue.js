"use strict";
const uni_modules_zPaging_components_zPaging_js_zPagingUtils = require("../z-paging-utils.js");
const nvueModule = {
  props: {},
  data() {
    return {
      nRefresherLoading: false,
      nListIsDragging: false,
      nShowBottom: true,
      nFixFreezing: false,
      nShowRefresherReveal: false,
      nLoadingMoreFixedHeight: false,
      nShowRefresherRevealHeight: 0,
      nOldShowRefresherRevealHeight: -1,
      nRefresherWidth: uni_modules_zPaging_components_zPaging_js_zPagingUtils.u.rpx2px(750),
      nF2Opacity: 0
    };
  },
  computed: {},
  mounted() {
  },
  methods: {}
};
exports.nvueModule = nvueModule;
//# sourceMappingURL=nvue.js.map
