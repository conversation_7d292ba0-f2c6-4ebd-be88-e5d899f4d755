"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
const pagesWork_components_common_concants = require("./concants.js");
const common_vendor = require("../../../common/vendor.js");
const utils_is = require("../../../utils/is.js");
const utils_index = require("../../../utils/index.js");
common_vendor.dayjs.extend(common_vendor.weekday);
common_vendor.dayjs.extend(common_vendor.localeData);
function parseTime(time, cFormat) {
  if (arguments.length === 0) {
    return null;
  }
  let date;
  const format = cFormat || "{y}-{m}-{d} {h}:{i}:{s}";
  if (typeof time === "object") {
    date = time;
  } else {
    if (typeof time === "string" && /^[0-9]+$/.test(time)) {
      time = parseInt(time);
    } else {
      time = new Date(time);
    }
    date = new Date(time.toString().replace(/-/g, "/"));
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  };
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key];
    if (key === "a") {
      return ["日", "一", "二", "三", "四", "五", "六"][value];
    }
    return value.toString().padStart(2, "0");
  });
  return time_str;
}
function getRange(type) {
  let date = /* @__PURE__ */ new Date();
  switch (type) {
    case "today":
      return nowDay(date);
    case "yesterday":
      return preDay(date);
    case "befYesterday":
      return preDay(date, 2);
    case "tomorrow":
      return nextDay(date);
    case "week":
      return nowWeek(date);
    case "preWeek":
      return preWeek(date);
    case "befPreWeek":
      return preWeek(date, 2);
    case "nextWeek":
      return nextWeek(date);
    case "month":
      return nowMonth(date);
    case "preMonth":
      return preMonth(date);
    case "befPreMonth":
      return preMonth(date, 2);
    case "nextMonth":
      return nextMonth(date);
    case "year":
      return nowYear(date);
    case "preYear":
      return preYear(date);
    case "befPreYear":
      return preYear(date, 2);
    case "nextYear":
      return nextYear(date);
    default:
      return null;
  }
}
function nowDay(date) {
  let startDate = common_vendor.dayjs(date).startOf("days").format("YYYY-MM-DD HH:mm:ss");
  let endDate = common_vendor.dayjs(date).endOf("days").format("YYYY-MM-DD HH:mm:ss");
  return [startDate, endDate];
}
function nowWeek(date) {
  let startDate = common_vendor.dayjs(date).startOf("week").format("YYYY-MM-DD HH:mm:ss");
  let endDate = common_vendor.dayjs(date).endOf("week").format("YYYY-MM-DD HH:mm:ss");
  return [startDate, endDate];
}
function nowMonth(date) {
  let startDate = common_vendor.dayjs(date).startOf("month").format("YYYY-MM-DD HH:mm:ss");
  let endDate = common_vendor.dayjs(date).endOf("month").format("YYYY-MM-DD HH:mm:ss");
  return [startDate, endDate];
}
function nowYear(date) {
  let startDate = common_vendor.dayjs(date).startOf("year").format("YYYY-MM-DD HH:mm:ss");
  let endDate = common_vendor.dayjs(date).endOf("year").format("YYYY-MM-DD HH:mm:ss");
  return [startDate, endDate];
}
function preDay(date, interval = 1) {
  let startDate = common_vendor.dayjs(date).subtract(interval, "days").startOf("days").format("YYYY-MM-DD HH:mm:ss");
  let endDate = common_vendor.dayjs(date).subtract(interval, "days").endOf("days").format("YYYY-MM-DD HH:mm:ss");
  return [startDate, endDate];
}
function nextDay(date) {
  let startDate = common_vendor.dayjs(date).add(1, "days").startOf("days").format("YYYY-MM-DD HH:mm:ss");
  let endDate = common_vendor.dayjs(date).add(1, "days").endOf("days").format("YYYY-MM-DD HH:mm:ss");
  return [startDate, endDate];
}
function preWeek(date, interval = 1) {
  let startDate = common_vendor.dayjs(date).subtract(interval, "week").startOf("week").format("YYYY-MM-DD HH:mm:ss");
  let endDate = common_vendor.dayjs(date).subtract(interval, "week").endOf("week").format("YYYY-MM-DD HH:mm:ss");
  return [startDate, endDate];
}
function nextWeek(date) {
  let startDate = common_vendor.dayjs(date).add(1, "week").startOf("week").format("YYYY-MM-DD HH:mm:ss");
  let endDate = common_vendor.dayjs(date).add(1, "week").endOf("week").format("YYYY-MM-DD HH:mm:ss");
  return [startDate, endDate];
}
function preMonth(date, interval = 1) {
  let startDate = common_vendor.dayjs(date).subtract(interval, "month").startOf("month").format("YYYY-MM-DD HH:mm:ss");
  let endDate = common_vendor.dayjs(date).subtract(interval, "month").endOf("month").format("YYYY-MM-DD HH:mm:ss");
  return [startDate, endDate];
}
function nextMonth(date) {
  let startDate = common_vendor.dayjs(date).add(1, "month").startOf("month").format("YYYY-MM-DD HH:mm:ss");
  let endDate = common_vendor.dayjs(date).add(1, "month").endOf("month").format("YYYY-MM-DD HH:mm:ss");
  return [startDate, endDate];
}
function preYear(date, interval = 1) {
  let startDate = common_vendor.dayjs(date).subtract(interval, "year").startOf("year").format("YYYY-MM-DD HH:mm:ss");
  let endDate = common_vendor.dayjs(date).subtract(interval, "year").endOf("year").format("YYYY-MM-DD HH:mm:ss");
  return [startDate, endDate];
}
function nextYear(date) {
  let startDate = common_vendor.dayjs(date).add(1, "year").startOf("year").format("YYYY-MM-DD HH:mm:ss");
  let endDate = common_vendor.dayjs(date).add(1, "year").endOf("year").format("YYYY-MM-DD HH:mm:ss");
  return [startDate, endDate];
}
function calcStr(fn) {
  try {
    const Fn = Function;
    return new Fn("return " + fn)();
  } catch (e) {
    console.log("calcStr", e);
    return null;
  }
}
function getTimeRange(queryCondition) {
  let params = [];
  if (queryCondition.customTime && queryCondition.customTime.length == 2) {
    let startTime = `${queryCondition.customTime[0]} 00:00:00`;
    let endTime = `${queryCondition.customTime[1]} 23:59:59`;
    return [startTime, endTime];
  }
  if (queryCondition.queryRange != "all") {
    let timeRange = getRange(queryCondition.queryRange);
    if (timeRange && timeRange.length > 0) {
      params[0] = timeRange[0];
      params[1] = timeRange[1];
    }
    return params;
  }
  return params;
}
function getConditionOptions(ele) {
  if (ele.widgetType == "pca") {
    return pagesWork_components_common_concants.conditionOptions["pca"];
  }
  if (ele.widgetType == "sub-table-design") {
    return pagesWork_components_common_concants.conditionOptions["sub-table-design"];
  }
  if (ele.widgetType == "link-record") {
    return pagesWork_components_common_concants.conditionOptions["link-record"];
  }
  if (ele.widgetType == "table-dict" && ele.options.queryScope !== "database") {
    return pagesWork_components_common_concants.conditionOptions["link-record"];
  }
  if (ele.widgetType == "time") {
    return pagesWork_components_common_concants.conditionOptions["time"];
  }
  if (ele.fieldType == "Date" || ele.fieldType == "date") {
    return pagesWork_components_common_concants.conditionOptions["date"];
  }
  if (pagesWork_components_common_concants.selectType.includes(ele.widgetType)) {
    return pagesWork_components_common_concants.conditionOptions["select"];
  }
  if (["int", "double", "BigDecimal", "number"].includes(ele.fieldType) || ["money", "integer", "rate", "slider"].includes(ele.widgetType)) {
    return pagesWork_components_common_concants.conditionOptions["number"];
  }
  return pagesWork_components_common_concants.conditionOptions["text"];
}
function packageConditionFields(conditionFields, formType) {
  let arr = [];
  conditionFields.forEach((fieldItem) => {
    let obj = {};
    let condition = fieldItem.condition;
    let fieldName = fieldItem.fieldName;
    let fieldType = fieldItem.fieldType;
    let widgetType = fieldItem.widgetType;
    let fieldValue = fieldItem.fieldValue;
    obj["cType"] = condition;
    obj["field"] = fieldName;
    obj["type"] = fieldType;
    obj["wType"] = widgetType;
    obj["value"] = fieldValue;
    let conditionOption = getConditionOptions(fieldItem).filter((item) => item.value == condition);
    obj["expression"] = `common_${condition}`;
    if (["link-record", "sub-table-design"].includes(widgetType)) {
      obj["code"] = fieldItem.localField || fieldItem.sourceCode;
    }
    if ("table-dict" == widgetType && fieldItem.options.queryScope !== "database") {
      obj["code"] = fieldName;
    }
    if ("switch" == widgetType && !fieldValue) {
      fieldValue = fieldItem.options.defaultValue || fieldItem.options.inactiveValue;
      obj["value"] = fieldValue;
    }
    if (condition == "7" || condition == "8") {
      arr.push(obj);
    } else {
      let hasValueFlag = fieldValue ? true : false;
      if (["int", "integer", "double", "BigDecimal", "number"].includes(fieldType)) {
        obj["type"] = "number";
        if (condition == "9" || condition == "10") {
          obj["begin"] = fieldItem.beginValue ? Number(fieldItem.beginValue) : fieldItem.beginValue;
          obj["end"] = fieldItem.endValue ? Number(fieldItem.endValue) : fieldItem.endValue;
          hasValueFlag = (obj["begin"] || obj["begin"] == 0) && (obj["end"] || obj["end"] == 0) ? true : false;
        } else {
          obj["value"] = Number(fieldValue);
          hasValueFlag = obj["value"] || obj["value"] == 0 ? true : false;
        }
      } else if (fieldItem.widgetType == "time") {
        obj["type"] = "time";
        if (condition == "9" || condition == "10") {
          obj["begin"] = fieldItem.beginValue ? parseTime(fieldItem.beginValue, "HH:mm:ss") : null;
          obj["end"] = fieldItem.endValue ? parseTime(fieldItem.endValue, "HH:mm:ss") : null;
          hasValueFlag = !obj["begin"] || !obj["end"] ? false : true;
        } else {
          obj["value"] = fieldValue ? fieldValue.format("HH:mm:ss") : null;
          hasValueFlag = obj["value"] ? true : false;
        }
      } else if (fieldItem.widgetType == "pca" || fieldItem.widgetType == "area-linkage") {
        obj["type"] = "pca";
        obj["value"] = fieldValue && utils_is.isArray(fieldValue) ? fieldValue[fieldValue.length - 1] : null;
        hasValueFlag = obj["value"] ? true : false;
      } else if (fieldType == "Date" || fieldType == "date") {
        obj["type"] = "date";
        if (condition == "9" || condition == "10") {
          if (fieldItem.options && fieldItem.options.type == "datetime") {
            obj["begin"] = fieldItem.fieldValue[0] ? fieldItem.fieldValue[0] : null;
            obj["end"] = fieldItem.fieldValue[1] ? fieldItem.fieldValue[1] : null;
          } else {
            if (fieldItem.fieldValue[0]) {
              const timeBegin = new Date(parseTime(fieldItem.fieldValue[0], `YYYY-MM-DD HH:mm:ss`));
              timeBegin.setHours(0, 0, 0, 0);
              obj["begin"] = parseTime(timeBegin.getTime(), `YYYY-MM-DD HH:mm:ss`);
            }
            if (fieldItem.fieldValue[1]) {
              const timeEnd = new Date(parseTime(fieldItem.fieldValue[1], `YYYY-MM-DD HH:mm:ss`));
              timeEnd.setHours(23, 59, 59, 999);
              obj["end"] = parseTime(timeEnd.getTime(), `YYYY-MM-DD HH:mm:ss`);
            }
          }
          hasValueFlag = !obj["begin"] || !obj["end"] ? false : true;
        } else {
          if (fieldItem.timeCondition == "customTime") {
            if (fieldValue) {
              const timeBegin = new Date(fieldValue);
              timeBegin.setHours(0, 0, 0, 0);
              const timeEnd = new Date(fieldValue);
              timeEnd.setHours(23, 59, 59, 999);
              obj["begin"] = parseTime(timeBegin.getTime(), `YYYY-MM-DD HH:mm:ss`);
              obj["end"] = parseTime(timeEnd.getTime(), `YYYY-MM-DD HH:mm:ss`);
              hasValueFlag = !obj["begin"] || !obj["end"] ? false : true;
            }
          } else {
            let range = getRange(fieldItem.timeCondition);
            obj["value"] = range && range.length > 0 ? range[0] : null;
            obj["begin"] = range && range.length > 0 ? range[0] : null;
            obj["end"] = range && range.length > 1 ? range[1] : null;
            if (condition == "1" || condition == "2") {
              hasValueFlag = !obj["begin"] || !obj["end"] ? false : true;
            }
            if (condition == "4" || condition == "6") {
              obj["value"] = range && range.length > 0 ? range[0] : null;
              hasValueFlag = obj["value"] ? true : false;
            }
            if (condition == "3" || condition == "5") {
              obj["value"] = range && range.length > 0 ? range[1] : null;
              hasValueFlag = obj["value"] ? true : false;
            }
          }
        }
      }
      if (pagesWork_components_common_concants.selectType.includes(fieldItem.widgetType)) {
        obj["type"] = "select";
        obj["value"] = null;
        if (fieldValue) {
          if (utils_is.isArray(fieldValue)) {
            obj["value"] = fieldValue.length > 0 ? JSON.stringify(fieldValue) : null;
          } else {
            obj["value"] = JSON.stringify(fieldValue.split(","));
          }
        }
        hasValueFlag = obj["value"] ? true : false;
      }
      if (conditionOption && conditionOption.length > 0) {
        if (["string", "text", "Text"].includes(obj["type"]) && ["4", "5", "6"].includes(condition)) {
          obj["expression"] = `text_${condition}`;
          if (formType && formType == "design") {
            obj["value"] = `${fieldValue}`;
          } else {
            obj["value"] = conditionOption[0]["value"] == "5" ? `${fieldValue}%` : `%${fieldValue}`;
          }
        }
        if (obj["type"] == "select" && ["3", "4"].includes(condition)) {
          obj["expression"] = `select_${condition}`;
        }
        hasValueFlag && arr.push(obj);
      }
    }
  });
  return arr;
}
function packageConditionQuery(conditionFields) {
  let arr = [];
  conditionFields.forEach((fieldItem) => {
    let obj = {};
    let condition = fieldItem.condition;
    let fieldName = fieldItem.fieldName;
    let fieldType = fieldItem.fieldType;
    let widgetType = fieldItem.widgetType;
    let fieldValue = fieldItem.fieldValue;
    obj["cType"] = condition;
    obj["field"] = fieldName;
    obj["type"] = fieldType;
    obj["wType"] = widgetType;
    obj["value"] = fieldValue;
    let conditionOption = getConditionOptions(fieldItem).filter((item) => item.value == condition);
    obj["expression"] = `common_${condition}`;
    if (["link-record", "sub-table-design"].includes(widgetType)) {
      obj["code"] = fieldItem.localField || fieldItem.sourceCode;
    }
    if ("table-dict" == widgetType && fieldItem.options.queryScope !== "database") {
      obj["code"] = fieldName;
    }
    if ("switch" == widgetType && !fieldValue) {
      obj["value"] = fieldValue;
    }
    let hasValueFlag = fieldValue ? true : false;
    if (["int", "integer", "double", "BigDecimal", "number"].includes(fieldType)) {
      obj["type"] = "number";
      if (condition == "9") {
        obj["begin"] = fieldItem.beginValue ? Number(fieldItem.beginValue) : fieldItem.beginValue;
        obj["end"] = fieldItem.endValue ? Number(fieldItem.endValue) : fieldItem.endValue;
        hasValueFlag = (obj["begin"] || obj["begin"] == 0) && (obj["end"] || obj["end"] == 0) ? true : false;
      } else {
        obj["value"] = fieldValue ? Number(fieldValue) : null;
        hasValueFlag = obj["value"] || obj["value"] == 0 ? true : false;
      }
    } else if (fieldItem.widgetType == "time") {
      obj["type"] = "time";
      if (condition == "9") {
        obj["begin"] = fieldItem.beginValue ? parseTime(fieldItem.beginValue, "HH:mm:ss") : null;
        obj["end"] = fieldItem.endValue ? parseTime(fieldItem.endValue, "HH:mm:ss") : null;
        hasValueFlag = !obj["begin"] || !obj["end"] ? false : true;
      } else {
        obj["value"] = fieldValue;
        hasValueFlag = obj["value"] ? true : false;
      }
    } else if (fieldItem.widgetType == "pca" || fieldItem.widgetType == "area-linkage") {
      obj["type"] = "pca";
      obj["value"] = fieldValue && utils_is.isArray(fieldValue) ? fieldValue[fieldValue.length - 1] : null;
      hasValueFlag = obj["value"] ? true : false;
    } else if (fieldType == "Date" || fieldType == "date") {
      obj["type"] = "date";
      if (condition == "9") {
        if (fieldItem.options && fieldItem.options.type == "datetime") {
          obj["begin"] = fieldItem.fieldValue[0] ? fieldItem.fieldValue[0] : null;
          obj["end"] = fieldItem.fieldValue[1] ? fieldItem.fieldValue[1] : null;
        } else {
          if (fieldItem.beginValue) {
            const timeBegin = new Date(fieldItem.beginValue);
            timeBegin.setHours(0, 0, 0, 0);
            obj["begin"] = common_vendor.dayjs(timeBegin.getTime()).format(`YYYY-MM-DD HH:mm:ss`);
          }
          if (fieldItem.endValue) {
            const timeEnd = new Date(fieldItem.endValue);
            timeEnd.setHours(23, 59, 59, 999);
            obj["end"] = common_vendor.dayjs(timeEnd.getTime()).format(`YYYY-MM-DD HH:mm:ss`);
          }
        }
        hasValueFlag = !obj["begin"] || !obj["end"] ? false : true;
      } else {
        if (fieldValue) {
          const timeBegin = new Date(fieldValue);
          timeBegin.setHours(0, 0, 0, 0);
          const timeEnd = new Date(fieldValue);
          timeEnd.setHours(23, 59, 59, 999);
          obj["begin"] = common_vendor.dayjs(timeBegin.getTime()).format(`YYYY-MM-DD HH:mm:ss`);
          obj["end"] = common_vendor.dayjs(timeEnd.getTime()).format(`YYYY-MM-DD HH:mm:ss`);
          hasValueFlag = !obj["begin"] || !obj["end"] ? false : true;
        }
      }
    }
    if (pagesWork_components_common_concants.selectType.includes(fieldItem.widgetType)) {
      obj["type"] = "select";
      obj["value"] = null;
      if (fieldValue) {
        if (utils_is.isArray(fieldValue)) {
          obj["value"] = fieldValue.length > 0 ? JSON.stringify(fieldValue) : null;
        } else {
          obj["value"] = JSON.stringify(fieldValue.split(","));
        }
      }
      hasValueFlag = obj["value"] ? true : false;
    }
    if (conditionOption && conditionOption.length > 0) {
      if (obj["type"] == "select" && ["3", "4"].includes(condition)) {
        obj["expression"] = `select_${condition}`;
      }
      hasValueFlag && arr.push(obj);
    }
  });
  console.log("arr****************>>>>>>", arr);
  return arr;
}
function packageParams(config, params) {
  console.log("config", config);
  console.log("params", params);
  let sorts = {};
  if (config.sorts && config.sorts.name && config.sorts.name.indexOf("null") == -1) {
    let lastIndexOf = config.sorts.name.lastIndexOf("_");
    sorts["name"] = config.sorts.name.substring(0, lastIndexOf);
    sorts["order"] = config.sorts.name.substring(lastIndexOf + 1);
    sorts["type"] = config.sorts.type;
  }
  let conditionFields = [];
  if (config && config.filter && config.filter.conditionFields && config.filter.conditionFields.length > 0) {
    conditionFields = packageConditionFields(config.filter.conditionFields, config.formType);
  }
  let filter = {
    field: config.filter.queryField,
    range: getTimeRange(config.filter),
    mode: config.filter.conditionMode,
    fields: conditionFields
  };
  if (params) {
    if (params.type && params.type == "fieldQuery") {
      if (params.conditionFields && params.conditionFields.length > 0) {
        filter["mode"] = "and";
        filter["fields"] = packageConditionQuery(params.conditionFields);
      }
      console.log("筛选器筛选条件filter:::::>>>>>", filter);
    }
  }
  return {
    sorts,
    filter
  };
}
function formatDate(dateStr, fmt = "YYYY-MM-D hh:mm:ss") {
  fmt = fmt.toUpperCase();
  if (fmt.indexOf("SS") != -1) {
    fmt = fmt.replace("SS", "ss");
  }
  return common_vendor.dayjs(dateStr).format(fmt);
}
function handleCalcFields(arr, valueFields, assistYFields) {
  let valField = valueFields.filter((item) => item.widgetType == "calcVal");
  let assistYField = assistYFields.filter((item) => item.widgetType == "calcVal");
  let calcField = [.../* @__PURE__ */ new Set([...valField, ...assistYField])];
  if (calcField && calcField.length > 0) {
    calcField.forEach((field) => {
      let fieldName = field.fieldName;
      arr.forEach((item) => {
        if (Object.keys(item).includes(fieldName)) {
          let formulaStr = fieldName.replace(/\$(.*?)\$/g, function(str) {
            if (str) {
              let field2 = str.replace(/\$/g, "");
              if (field2 && utils_is.isString(field2) && !utils_is.isNullOrUnDef(item[field2])) {
                return item[field2];
              }
            }
            return str;
          });
          item["value"] = keepTwoDecimals(calcStr(formulaStr));
        }
      });
    });
  }
  return arr;
}
function handleDateFields(chartData, config) {
  let nameField = config.nameFields;
  let typeField = config.typeFields;
  let assistType = config.assistTypeFields;
  let nameHasDate = nameField.some((name) => name.widgetType == "date");
  let typeHasDate = typeField.some((type) => type.widgetType == "date");
  let assistTypeHasDate = assistType.some((type) => type.widgetType == "date");
  if (nameHasDate || typeHasDate || assistTypeHasDate) {
    chartData.forEach((item) => {
      if (nameHasDate && !item[nameField[0].fieldName + "_dictVal"]) {
        let defVal = item.name.indexOf("-") >= 0 ? item.name : parseInt(item.name);
        let nameFormat = nameField[0].options && nameField[0].options.format ? nameField[0].options.format : "YYYY-MM-DD";
        item.name = item.name ? formatDate(defVal, nameFormat) : "";
      }
      if (typeHasDate && !item[typeField[0].fieldName + "_dictVal"]) {
        let defVal = item.type.indexOf("-") >= 0 ? item.type : parseInt(item.type);
        let typeFormat = typeField[0].options && typeField[0].options.format ? typeField[0].options.format : "YYYY-MM-DD";
        item.type = item.type ? formatDate(defVal, typeFormat) : "";
      }
      if (assistTypeHasDate && !item[assistType[0].fieldName + "_dictVal"] && item.yAxisIndex == "1" && item.type) {
        let defVal = item.type.indexOf("-") >= 0 ? item.type : parseInt(item.type);
        let typeFormat = assistType[0].options && assistType[0].options.format ? assistType[0].options.format : "YYYY-MM-DD";
        item.type = item.type ? formatDate(defVal, typeFormat) : "";
      }
    });
  }
  return chartData;
}
function keepTwoDecimals(total) {
  if (total) {
    let dot = String(total).indexOf(".");
    if (dot != -1) {
      let dotCnt = String(total).substring(dot + 1, total.length);
      if (dotCnt.length > 2) {
        total = total.toFixed(2);
      }
    }
  }
  return total;
}
function deepMerge(target, other) {
  const targetToString = Object.prototype.toString.call(target);
  const otherToString = Object.prototype.toString.call(target);
  if (targetToString === "[object Object]" && otherToString === "[object Object]") {
    for (let [key, val] of Object.entries(other)) {
      if (!target[key]) {
        target[key] = val;
      } else {
        target[key] = deepMerge(target[key], val);
      }
    }
  } else if (targetToString === "[object Array]" && otherToString === "[object Array]") {
    for (let [key, val] of Object.entries(other)) {
      if (target[key]) {
        target[key] = deepMerge(target[key], val);
      } else {
        target.push(val);
      }
    }
  }
  return target;
}
function getCustomColor(customColor) {
  let colors = pagesWork_components_common_concants.colorPanel.classic.map((color) => ({ color }));
  return customColor ? customColor : colors;
}
function getDataSet(chartData, config) {
  let dataObj = { dimensions: [], source: [] };
  let dataList = [];
  let dimensions = ["stack", ...new Set(chartData.map((item) => item["type"]))];
  let nameArr = [...new Set(chartData.map((item) => item["name"]))];
  if (config.dataFilterNum && utils_is.isNumber(config.dataFilterNum)) {
    nameArr = nameArr.slice(0, config.dataFilterNum);
  }
  nameArr.forEach((name) => {
    let arr = chartData.filter((item) => item["name"] == name);
    let valueList = arr.map((item) => item["value"]);
    valueList.unshift(name);
    dataList.push(valueList);
  });
  dataObj.dimensions = dimensions;
  dataObj.source = dataList;
  return dataObj;
}
function calcUnit(value, calcConfig, defDecimal = 0) {
  let numberLevel = calcConfig.numberLevel;
  let decimal = calcConfig.decimal || defDecimal;
  let mapping = { "0": 1, "1": 100, "2": 1e3, "3": 1 / 1e3, "4": 1 / 1e4, "5": 1 / 1e6 };
  let multiple = numberLevel ? mapping[numberLevel] : 1;
  value = (value * multiple).toFixed(decimal);
  return value;
}
function calcTotal(summaryConfig, rawData, config) {
  var _a, _b;
  if (rawData && rawData.length > 0) {
    let showField = summaryConfig.showField;
    const findItem = (_a = config.valueFields) == null ? void 0 : _a.find((item) => item.fieldName === showField);
    const unitText = ((_b = findItem == null ? void 0 : findItem.options) == null ? void 0 : _b.unitText) || "";
    let showName = summaryConfig.showName || "总计";
    let totalType = summaryConfig.totalType || "sum";
    let valueField = showField == "all" ? "value" : showField;
    let valueArr = rawData.map((item) => {
      if (utils_is.isNumber(item[valueField])) {
        return item[valueField];
      } else {
        const value = Number(item[valueField].replace(unitText, ""));
        if (Number.isNaN(value)) {
          return 0;
        } else {
          return value;
        }
      }
    });
    let total = 0;
    if (valueArr.length > 0) {
      if (totalType == "sum") {
        total = valueArr.reduce((prev, cur) => prev + cur, 0);
      } else if (totalType == "max") {
        total = Math.max.apply(Math, valueArr);
      } else if (totalType == "min") {
        total = Math.min.apply(Math, valueArr);
      } else if (totalType == "average") {
        total = (valueArr.reduce((prev, cur) => prev + cur, 0) / valueArr.length).toFixed(2);
      }
    }
    return `${showName}: ${keepTwoDecimals(total)}`;
  }
  return "";
}
function handleTotalAndUnit(compName, chartOption, config, chartData) {
  if (config.compStyleConfig) {
    let showUnitConfig = config.compStyleConfig.showUnit;
    let unit = showUnitConfig.unit ? showUnitConfig.unit : "";
    showUnitConfig.numberLevel ? showUnitConfig.numberLevel : "";
    chartOption.series.forEach((item) => {
      if (item.yAxisIndex == "1") {
        showUnitConfig = config.compStyleConfig.assist.showUnit;
      }
      let labelPosition = compName.indexOf("Pie") != -1 ? chartOption.pieLabelPosition : "top";
      let labelConfig;
      switch (compName) {
        case "JColorGauge":
        case "JGauge":
          delete item.detail.formatter;
          labelConfig = {
            detail: {
              formatter: (value) => {
                let showLabel = showUnitConfig.position == "suffix" ? `${calcUnit(value, showUnitConfig)}${unit}` : `${unit}${calcUnit(value, showUnitConfig)}`;
                return showLabel;
              }
            }
          };
          break;
        default:
          labelConfig = {
            label: {
              position: compName.indexOf("Funnel") >= 0 ? "inside" : labelPosition,
              formatter: (params) => {
                let type = params.seriesType;
                let showLabel = "";
                if (type == "pie") {
                  showLabel = `${params.name || "空"}:`;
                }
                if (type == "funnel") {
                  showLabel = `${params.name || "空"}: `;
                }
                let value = 0;
                if (Array.isArray(params.value)) {
                  value = type == "scatter" ? params.value[1] : params.value[params.seriesIndex + 1];
                } else {
                  value = params.value;
                }
                showLabel += showUnitConfig.position == "suffix" ? `${calcUnit(value, showUnitConfig)}${unit}` : `${unit}${calcUnit(value, showUnitConfig)}`;
                return showLabel;
              }
            }
          };
      }
      deepMerge(item, __spreadValues({}, labelConfig));
    });
    let summaryConfig = config.compStyleConfig.summary;
    if (summaryConfig.showTotal && chartData && chartData.length > 0) {
      let leftData = chartData.filter((item) => !item.yAxisIndex || item.yAxisIndex == "0");
      let totalTitle = summaryConfig.showY ? calcTotal(summaryConfig, leftData, config) : "";
      Object.assign(chartOption.title, { text: totalTitle });
    }
  }
  chartOption = otherConfig(chartOption);
  return chartOption;
}
function disposeGridLayout(compName, chartOption, config, chartData) {
  var _a;
  chartOption.grid = { containLabel: true, top: 30, bottom: 60, left: 5, right: 5 };
  const { xAxis, yAxis, series } = chartOption;
  if (xAxis) {
    const { name, nameTextStyle = {} } = xAxis;
    if (name) {
      chartOption.grid.top += 30;
    }
  }
  if (yAxis) {
    const { name, nameTextStyle = {} } = yAxis;
    if (name) {
      const { fontSize = 12 } = nameTextStyle;
      chartOption.grid.right += name.length * (fontSize + 1) + 15;
    }
  }
  if ((_a = chartOption.title) == null ? void 0 : _a.show) {
    const { textStyle = {} } = chartOption.title;
    const { fontSize = 18 } = textStyle;
    chartOption.grid.top += fontSize;
    if (chartOption.grid.top > 200) {
      chartOption.grid.top = 30;
    }
  }
  if (series.length <= 1) {
    chartOption.grid.bottom = 10;
  }
  console.log("---chartOption--", chartOption);
  return chartOption;
}
function otherConfig(chartOption) {
  chartOption.grid = {
    left: 30,
    right: 30,
    top: 30,
    bottom: 60
  };
  chartOption.legend = {
    bottom: 15
  };
  chartOption.tooltip && (chartOption.tooltip.extraCssText = "z-index:9");
  return chartOption;
}
function getRandomColor(index) {
  let naturalColors = ["rgb(133, 202, 205)", "rgb(167, 214, 118)", "rgb(254, 225, 89)", "rgb(251, 199, 142)", "rgb(239, 145, 139)", "rgb(169, 181, 255)", "rgb(231, 218, 202)", "rgb(252, 128, 58)", "rgb(254, 161, 172)", "rgb(194, 163, 205)"];
  let colors = ["rgb(100, 181, 246)", "rgb(77, 182, 172)", "rgb(255, 183, 77)", "rgb(229, 115, 115)", "rgb(149, 117, 205)", "rgb(161, 136, 127)", "rgb(144, 164, 174)", "rgb(77, 208, 225)", "rgb(129, 199, 132)", "rgb(255, 138, 101)", ...naturalColors];
  return index && index < 19 ? colors[index] : colors[Math.floor(Math.random() * (colors.length - 1))];
}
function handleParam(config) {
  let paramList = config.paramOption;
  let url = common_vendor.cloneDeep(config.dataSetApi);
  let dataMap = {};
  if (paramList && paramList.length > 0) {
    paramList.forEach((item) => {
      dataMap[item.label] = item.defaultVal || "";
    });
  }
  let reg = /\$\{[^}]*\}/g;
  if (reg.test(config.dataSetApi)) {
    url = url.split("?")[0];
  }
  return { dataMap, url };
}
function getGeoCoordMap(mapDataJson) {
  if (mapDataJson.features) {
    let mapFeatures = mapDataJson.features;
    let geoCoordMap = {};
    mapFeatures.forEach((v) => {
      let name = v.properties.name;
      geoCoordMap[name] = {
        center: v.properties.cp,
        adcode: v.properties.adcode
      };
    });
    return geoCoordMap;
  }
  return null;
}
function addImgPrefix(imgUrl) {
  if (imgUrl) {
    if (imgUrl.startsWith("http://") || imgUrl.startsWith("https://")) {
      return imgUrl;
    }
    if (imgUrl.startsWith("data:image/png;base64")) {
      return imgUrl;
    } else {
      imgUrl = imgUrl.indexOf("/img/bg/source/") >= 0 ? imgUrl.replaceAll("/img/bg/source/", "/img/") : imgUrl;
      imgUrl = imgUrl.indexOf("/img/bg/border/") >= 0 ? imgUrl.replaceAll("/img/bg/border/", "/img/") : imgUrl;
      let url = imgUrl && imgUrl.indexOf("/img/bg/") >= 0 ? imgUrl.replaceAll("/img/bg/", "/img/") : imgUrl;
      return `${utils_index.getEnvBaseUrl()}/drag/lib${url}`;
    }
  }
}
function checkUrlPrefix(url) {
  const currentProtocol = window.location.protocol;
  const urlObj = new URL(url);
  const urlProtocol = urlObj.protocol;
  const isDiffProtocol = currentProtocol.startsWith("https") && currentProtocol != urlProtocol;
  return {
    isDiffProtocol,
    currentProtocol
  };
}
function dictTransform(chartData, dictOptions) {
  if (dictOptions && Object.keys(dictOptions).length > 0) {
    Object.keys(dictOptions).forEach((code) => {
      if (dictOptions[code] && utils_is.isArray(dictOptions[code])) {
        chartData.forEach((item) => {
          let obj = dictOptions[code].filter((dict) => dict.value === item[code] + "");
          item[code] = obj && obj.length > 0 ? obj[0]["text"] : item[code];
        });
      }
    });
  }
  return chartData;
}
const getUrlParams = (url) => {
  let result = {
    url: "",
    params: {}
  };
  let list = url.split("?");
  result.url = list[0];
  let params = list[1];
  if (params) {
    let list2 = params.split("&");
    list2.forEach((ele) => {
      let dic = ele.split("=");
      let label = dic[0];
      let value = dic[1];
      result.params[label] = decodeURIComponent(value);
    });
  }
  return result;
};
exports.addImgPrefix = addImgPrefix;
exports.calcUnit = calcUnit;
exports.checkUrlPrefix = checkUrlPrefix;
exports.deepMerge = deepMerge;
exports.dictTransform = dictTransform;
exports.disposeGridLayout = disposeGridLayout;
exports.getCustomColor = getCustomColor;
exports.getDataSet = getDataSet;
exports.getGeoCoordMap = getGeoCoordMap;
exports.getRandomColor = getRandomColor;
exports.getUrlParams = getUrlParams;
exports.handleCalcFields = handleCalcFields;
exports.handleDateFields = handleDateFields;
exports.handleParam = handleParam;
exports.handleTotalAndUnit = handleTotalAndUnit;
exports.keepTwoDecimals = keepTwoDecimals;
exports.packageParams = packageParams;
//# sourceMappingURL=echartUtil.js.map
