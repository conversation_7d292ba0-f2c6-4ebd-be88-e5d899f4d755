"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
const common_vendor = require("../../../../common/vendor.js");
const pagesWork_components_echarts_props = require("../props.js");
const pagesWork_components_common_echartUtil = require("../../common/echartUtil.js");
const pagesWork_components_hooks_useEchart = require("../../hooks/useEchart.js");
const uni_modules_daTree_utils = require("../../../../uni_modules/da-tree/utils.js");
if (!Math) {
  (statusTip + echartsUniapp)();
}
const echartsUniapp = () => "../index.js";
const statusTip = () => "../../statusTip.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  props: __spreadValues({}, pagesWork_components_echarts_props.echartProps),
  setup(__props) {
    const props = __props;
    const option = common_vendor.ref({});
    let chartOption = {
      title: {
        show: true
      },
      legend: {
        show: true,
        data: []
      },
      xAxis: {
        type: "category",
        nameGap: 25,
        data: []
      },
      yAxis: [
        { type: "value", alignTicks: true },
        { type: "value", alignTicks: true }
      ],
      graphic: {
        type: "text",
        right: 0,
        top: 0,
        style: {
          text: "",
          fill: "#464646",
          font: 'bolder 18px "Microsoft YaHei", sans-serif'
        }
      },
      series: []
    };
    let [{ dataSource, reload, pageTips, config }, { queryData }] = pagesWork_components_hooks_useEchart.useChartHook(
      props,
      initOption
    );
    function initOption(data) {
      let chartData = dataSource.value;
      if (typeof chartData === "string") {
        chartData = JSON.parse(chartData);
      }
      if (chartData && chartData.length > 0) {
        const colors = pagesWork_components_common_echartUtil.getCustomColor(config.option.customColor);
        let configOption = config.option;
        let leftChartType = configOption.yAxis && configOption.yAxis.length > 0 ? configOption.yAxis[0].chartType : "bar";
        let legendData = [...new Set(chartData.map((item) => item.type))];
        chartOption.series = [];
        legendData.forEach((legend, index) => {
          let legendColor = configOption.series.length > 0 && configOption.series[0].color ? configOption.series[0].color[index] : null;
          let allData = chartData.filter((item) => item.type == legend);
          let leftData = allData.filter((item) => !item.yAxisIndex || item.yAxisIndex && item.yAxisIndex == "0");
          let rightData = allData.filter((item) => item.yAxisIndex && item.yAxisIndex == "1");
          let seriesType = config.seriesType.filter((item) => item.series == legend);
          if (leftData && leftData.length > 0) {
            let leftSeriesType = seriesType && seriesType.length > 0 ? seriesType[0]["type"] : "bar";
            let color = colors && colors[index] ? colors[index].color : "#64b5f6";
            chartOption.series.push({
              name: legend,
              type: leftChartType == "line" ? leftChartType : leftSeriesType,
              data: leftData.map((item) => item["value"]),
              color: legendColor || color || "",
              yAxisIndex: 0
            });
          }
          if (rightData && rightData.length > 0) {
            let color = colors && colors[index] ? colors[index].color : "";
            chartOption.series.push({
              name: legend,
              type: "line",
              data: rightData.map((item) => item["value"]),
              color: legendColor || color || "",
              yAxisIndex: 1
            });
          }
        });
        chartOption.xAxis.data = [...new Set(chartData.map((item) => item.name))];
        chartOption.legend.data = legendData;
        if (props.config && config.option) {
          common_vendor.merge(chartOption, config.option);
          chartOption = pagesWork_components_common_echartUtil.handleTotalAndUnit(props.compName, chartOption, config, chartData);
          chartOption = pagesWork_components_common_echartUtil.disposeGridLayout(props.compName, chartOption);
          let title = config.option.title;
          let color = title.textStyle.color || "#000";
          let weight = title.textStyle.fontWeight || "normal";
          let fontSize = title.textStyle.fontSize || "14";
          chartOption.graphic.style = {
            text: "",
            fill: color,
            font: `${weight} ${fontSize}px "Microsoft YaHei", sans-serif`
          };
        }
        console.log("双轴图this.chartOption====>", chartOption);
        option.value = uni_modules_daTree_utils.deepClone(chartOption);
        pageTips.show = false;
      } else {
        pageTips.status = 1;
        pageTips.show = true;
      }
    }
    common_vendor.onMounted(() => {
      queryData();
    });
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.unref(pageTips).show
      }, common_vendor.unref(pageTips).show ? {
        b: common_vendor.p({
          status: common_vendor.unref(pageTips).status
        })
      } : {
        c: common_vendor.p({
          option: common_vendor.unref(option)
        })
      });
    };
  }
});
wx.createComponent(_sfc_main);
//# sourceMappingURL=index.js.map
