"use strict";
const common_vendor = require("../common/vendor.js");
const utils_index = require("../utils/index.js");
const store_user = require("../store/user.js");
const VITE_UPLOAD_BASEURL = `${utils_index.getEnvBaseUploadUrl()}`;
function useUpload(formData = {}, { url, sizeType = ["original", "compressed"], sourceType = ["album", "camera"] }) {
  const loading = common_vendor.ref(false);
  const error = common_vendor.ref(false);
  const data = common_vendor.ref();
  const run = () => {
    common_vendor.index.chooseMedia({
      count: 1,
      mediaType: ["image"],
      sourceType,
      sizeType,
      //可以指定是原图还是压缩图，默认二者都有
      success: (res) => {
        loading.value = true;
        const tempFilePath = res.tempFiles[0].tempFilePath;
        const fileName = res.type;
        formData.fileName = fileName;
        uploadFile({ url, tempFilePath, formData, data, error, loading });
      },
      fail: (err) => {
        console.error("uni.chooseMedia err->", err);
        error.value = true;
      }
    });
  };
  return { loading, error, data, run };
}
function uploadFile({ url, tempFilePath, formData, data, error, loading }) {
  const userStore = store_user.useUserStore();
  common_vendor.index.uploadFile({
    url: url != null ? url : VITE_UPLOAD_BASEURL,
    filePath: tempFilePath,
    name: "file",
    formData,
    header: {
      "X-Access-Token": userStore.userInfo.token,
      "X-Tenant-Id": userStore.userInfo.tenantId
    },
    success: (uploadFileRes) => {
      data.value = JSON.parse(uploadFileRes.data);
    },
    fail: (err) => {
      console.error("uni.uploadFile err->", err);
      error.value = true;
    },
    complete: () => {
      loading.value = false;
    }
  });
}
exports.useUpload = useUpload;
//# sourceMappingURL=useUpload.js.map
