"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
require("../../plugin/uni-mini-router/router/index.js");
const plugin_uniMiniRouter_core_index = require("../../plugin/uni-mini-router/core/index.js");
const store_pageParams = require("../../store/page-params.js");
if (!Array) {
  const _easycom_wd_navbar2 = common_vendor.resolveComponent("wd-navbar");
  const _easycom_wd_toast2 = common_vendor.resolveComponent("wd-toast");
  const _easycom_wd_message_box2 = common_vendor.resolveComponent("wd-message-box");
  const _easycom_wd_notify2 = common_vendor.resolveComponent("wd-notify");
  (_easycom_wd_navbar2 + _easycom_wd_toast2 + _easycom_wd_message_box2 + _easycom_wd_notify2)();
}
const _easycom_wd_navbar = () => "../../node-modules/wot-design-uni/components/wd-navbar/wd-navbar.js";
const _easycom_wd_toast = () => "../../node-modules/wot-design-uni/components/wd-toast/wd-toast.js";
const _easycom_wd_message_box = () => "../../node-modules/wot-design-uni/components/wd-message-box/wd-message-box.js";
const _easycom_wd_notify = () => "../../node-modules/wot-design-uni/components/wd-notify/wd-notify.js";
if (!Math) {
  (_easycom_wd_navbar + _easycom_wd_toast + _easycom_wd_message_box + _easycom_wd_notify)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "pageLayout",
  options: {
    // apply-shared‌：当前页面样式会影响到子组件样式.(小程序)
    // shared‌：当前页面样式影响到子组件，子组件样式也会影响到当前页面.(小程序)
    styleIsolation: "shared"
  }
}), {
  __name: "PageLayout",
  props: {
    backRouteName: {
      type: String,
      default: ""
    },
    backRoutePath: {
      type: String,
      default: ""
    },
    routeParams: {
      type: Object,
      default: () => {
      }
    },
    routeQuery: {
      type: Object,
      default: () => {
      }
    },
    routeMethod: {
      type: String,
      default: "replace"
    },
    navbarShow: {
      type: Boolean,
      default: true
    },
    navBgTransparent: {
      type: Boolean,
      default: false
    },
    navFixed: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: "page"
      //'page','popup'
    },
    navTitle: {
      type: String,
      default: ""
    },
    navLeftText: {
      type: String,
      default: "返回"
    },
    navLeftArrow: {
      typeof: Boolean,
      default: true
    },
    navRightText: {
      typeof: String,
      default: ""
    }
  },
  emits: ["navBack", "navRight"],
  setup(__props, { emit: __emit }) {
    const paramsStore = store_pageParams.useParamsStore();
    const router = plugin_uniMiniRouter_core_index.useRouter();
    const props = __props;
    common_vendor.useSlots();
    const globalData = getApp().globalData;
    const { systemInfo, navHeight } = globalData;
    const { statusBarHeight } = systemInfo;
    const emit = __emit;
    const handleClickLeft = () => {
      emit("navBack");
      if (props.type === "page") {
        const pages = getCurrentPages();
        if (props.backRouteName || props.backRoutePath) {
          const prevPage = pages[pages.length - 2];
          if (prevPage) {
            const route = prevPage.route;
            const name = route.split("/").pop();
            if (route === props.backRoutePath || props.backRouteName === name) {
              router.back();
              clearPageParamsCache();
              return;
            }
          }
          if (props.backRouteName) {
            router[props.routeMethod]({ name: props.backRouteName, params: props.routeParams });
            clearPageParamsCache();
          } else {
            router[props.routeMethod]({ name: props.backRoutePath, query: props.routeQuery });
            clearPageParamsCache();
          }
        } else {
          router.back();
          clearPageParamsCache();
        }
      }
    };
    const clearPageParamsCache = () => {
      const pages = getCurrentPages();
      const curPage = pages[pages.length - 1];
      const curRoute = curPage.route;
      const name = curRoute.split("/").pop();
      paramsStore.clearPageParams(name);
    };
    const handleClickRight = () => {
      emit("navRight");
    };
    console.log("props:", props);
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: __props.navbarShow
      }, __props.navbarShow ? common_vendor.e$1({
        b: `${common_vendor.unref(statusBarHeight)}px`,
        c: _ctx.$slots.navRight
      }, _ctx.$slots.navRight ? {} : {}, {
        d: common_vendor.o(handleClickLeft),
        e: common_vendor.o(handleClickRight),
        f: common_vendor.p({
          bordered: !__props.navBgTransparent,
          title: __props.navTitle || "心衰院外管理",
          leftText: __props.navLeftText,
          leftArrow: __props.navLeftArrow,
          rightText: __props.navRightText,
          ["custom-class"]: "nav"
        }),
        g: __props.navBgTransparent ? 1 : "",
        h: __props.navFixed ? 1 : "",
        i: `${common_vendor.unref(statusBarHeight) + common_vendor.unref(navHeight)}px`
      }) : {});
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-3318346e"]]);
wx.createComponent(Component);
//# sourceMappingURL=PageLayout.js.map
