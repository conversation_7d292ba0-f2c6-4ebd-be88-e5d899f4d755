{"version": 3, "file": "index.js", "sources": ["../../../../../../../src/pages-work/components/echarts/JStackBar/index.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvcGFnZXMtd29yay9jb21wb25lbnRzL2VjaGFydHMvSlN0YWNrQmFyL2luZGV4LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"content\">\r\n    <statusTip v-if=\"pageTips.show\" :status=\"pageTips.status\"></statusTip>\r\n    <echartsUniapp v-else :option=\"option\"></echartsUniapp>\r\n  </view>\r\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { echartProps } from '@/pages-work/components/echarts/props'\r\nimport {\r\n  deepMerge,\r\n  handleTotalAndUnit,\r\n  disposeGridLayout,\r\n  getCustomColor,\r\n  getDataSet,\r\n} from '../../common/echartUtil'\r\nimport { isNumber } from '@/utils/is'\r\nimport useChartHook from '@/pages-work/components/hooks/useEchart'\r\nimport { deepClone } from '@/uni_modules/da-tree/utils'\r\nimport echartsUniapp from '@/pages-work/components/echarts/index.vue'\r\nimport statusTip from '@/pages-work/components/statusTip.vue'\r\nimport {merge} from \"lodash-es\";\r\n//组件传参\r\nconst props = defineProps({\r\n  ...echartProps,\r\n})\r\n//最终图表配置项\r\nconst option = ref({})\r\nlet chartOption = {\r\n  title: {\r\n    show: true,\r\n  },\r\n  legend: {\r\n    show: true,\r\n    data: [],\r\n  },\r\n  xAxis: {\r\n    type: 'category',\r\n  },\r\n  yAxis: {\r\n    type: 'value',\r\n  },\r\n  series: [],\r\n  dataset: {\r\n    dimensions: [],\r\n    source: [],\r\n  },\r\n}\r\n//图表数据查询\r\nlet [{ dataSource, reload, pageTips, config }, { queryData }] = useChartHook(props, initOption)\r\n\r\n//初始化图表配置项\r\nfunction initOption(data) {\r\n  let chartData: any = dataSource.value\r\n  if (typeof chartData === 'string') {\r\n    chartData = JSON.parse(chartData)\r\n  }\r\n  if (chartData && chartData.length > 0) {\r\n    const colors = getCustomColor(config.option.customColor)\r\n    let configOption = config.option\r\n    let dataset = getDataSet(chartData, config)\r\n    let label =\r\n      configOption.series.length > 0 && configOption.series[0].label\r\n        ? configOption.series[0].label\r\n        : {}\r\n    chartOption.dataset = dataset\r\n    chartOption.series = []\r\n    dataset.dimensions.forEach((series, index) => {\r\n      if (index > 0) {\r\n        let legengColor =\r\n          configOption.series.length > 0 && configOption.series[0].color\r\n            ? configOption.series[0].color[index - 1]\r\n            : null\r\n        let color = colors && colors[index - 1] ? colors[index - 1].color : ''\r\n        chartOption.series.push({\r\n          type: 'bar', //TODO 自定义图表类型\r\n          stack: 'stack', //TODO 自定义堆叠分组\r\n          color: legengColor || color, //TODO 自定义颜色\r\n          series: series, //TODO 系列，冗余数据，只是table展示使用\r\n          label: label,\r\n        })\r\n      }\r\n    })\r\n    chartOption.legend.data = chartOption.series.map((item) => item.series)\r\n    //2.类目轴和数值轴赋值\r\n    if (config.option.xAxis && config.option.xAxis.type) {\r\n      let type = config.option.xAxis['type'] == 'value' ? 'category' : 'value'\r\n      chartOption.yAxis['type'] = type\r\n    }\r\n    // 合并配置\r\n    if (props.config && config.option) {\r\n      merge(chartOption, config.option)\r\n      chartOption = handleTotalAndUnit(props.compName, chartOption, config, chartData)\r\n      chartOption = disposeGridLayout(props.compName, chartOption, config, chartData)\r\n      option.value = deepClone(chartOption)\r\n      pageTips.show = false\r\n    }\r\n  } else {\r\n    pageTips.status = 1\r\n    pageTips.show = true\r\n  }\r\n}\r\n\r\nonMounted(() => {\r\n  queryData()\r\n})\r\n</script>\r\n<style>\r\n.content {\r\n  padding: 10px;\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-work/components/echarts/JStackBar/index.vue'\nwx.createComponent(Component)"], "names": ["ref", "useChartHook", "getCustomColor", "getDataSet", "merge", "handleTotalAndUnit", "disposeGridLayout", "deepClone", "onMounted", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAmBA,MAAA,gBAA0B,MAAA;AAC1B,MAAA,YAAsB,MAAA;;;;;AAGtB,UAAM,QAAQ;AAIR,UAAA,SAASA,cAAI,IAAA,EAAE;AACrB,QAAI,cAAc;AAAA,MAChB,OAAO;AAAA,QACL,MAAM;AAAA,MACR;AAAA,MACA,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,MAAM,CAAA;AAAA,MACR;AAAA,MACA,OAAO;AAAA,QACL,MAAM;AAAA,MACR;AAAA,MACA,OAAO;AAAA,QACL,MAAM;AAAA,MACR;AAAA,MACA,QAAQ,CAAC;AAAA,MACT,SAAS;AAAA,QACP,YAAY,CAAC;AAAA,QACb,QAAQ,CAAA;AAAA,MAAC;AAAA,IAEb;AAEA,QAAI,CAAC,EAAE,YAAY,QAAQ,UAAU,OAAA,GAAU,EAAE,WAAW,IAAIC,kDAAa,OAAO,UAAU;AAG9F,aAAS,WAAW,MAAM;AACxB,UAAI,YAAiB,WAAW;AAC5B,UAAA,OAAO,cAAc,UAAU;AACrB,oBAAA,KAAK,MAAM,SAAS;AAAA,MAAA;AAE9B,UAAA,aAAa,UAAU,SAAS,GAAG;AACrC,cAAM,SAASC,uCAAA,eAAe,OAAO,OAAO,WAAW;AACvD,YAAI,eAAe,OAAO;AACtB,YAAA,UAAUC,uCAAAA,WAAW,WAAW,MAAM;AAC1C,YAAI,QACF,aAAa,OAAO,SAAS,KAAK,aAAa,OAAO,CAAC,EAAE,QACrD,aAAa,OAAO,CAAC,EAAE,QACvB,CAAC;AACP,oBAAY,UAAU;AACtB,oBAAY,SAAS,CAAC;AACtB,gBAAQ,WAAW,QAAQ,CAAC,QAAQ,UAAU;AAC5C,cAAI,QAAQ,GAAG;AACb,gBAAI,cACF,aAAa,OAAO,SAAS,KAAK,aAAa,OAAO,CAAC,EAAE,QACrD,aAAa,OAAO,CAAC,EAAE,MAAM,QAAQ,CAAC,IACtC;AACF,gBAAA,QAAQ,UAAU,OAAO,QAAQ,CAAC,IAAI,OAAO,QAAQ,CAAC,EAAE,QAAQ;AACpE,wBAAY,OAAO,KAAK;AAAA,cACtB,MAAM;AAAA;AAAA,cACN,OAAO;AAAA;AAAA,cACP,OAAO,eAAe;AAAA;AAAA,cACtB;AAAA;AAAA,cACA;AAAA,YAAA,CACD;AAAA,UAAA;AAAA,QACH,CACD;AACW,oBAAA,OAAO,OAAO,YAAY,OAAO,IAAI,CAAC,SAAS,KAAK,MAAM;AAEtE,YAAI,OAAO,OAAO,SAAS,OAAO,OAAO,MAAM,MAAM;AACnD,cAAI,OAAO,OAAO,OAAO,MAAM,MAAM,KAAK,UAAU,aAAa;AACrD,sBAAA,MAAM,MAAM,IAAI;AAAA,QAAA;AAG1B,YAAA,MAAM,UAAU,OAAO,QAAQ;AAC3BC,8BAAA,aAAa,OAAO,MAAM;AAChC,wBAAcC,uCAAmB,mBAAA,MAAM,UAAU,aAAa,QAAQ,SAAS;AAC/E,wBAAcC,uCAAkB,kBAAA,MAAM,UAAU,WAA8B;AACvE,iBAAA,QAAQC,mCAAU,WAAW;AACpC,mBAAS,OAAO;AAAA,QAAA;AAAA,MAClB,OACK;AACL,iBAAS,SAAS;AAClB,iBAAS,OAAO;AAAA,MAAA;AAAA,IAClB;AAGFC,kBAAAA,UAAU,MAAM;AACJ,gBAAA;AAAA,IAAA,CACX;;;;;;;;;;;;;;;;ACxGD,GAAG,gBAAgBC,SAAS;"}