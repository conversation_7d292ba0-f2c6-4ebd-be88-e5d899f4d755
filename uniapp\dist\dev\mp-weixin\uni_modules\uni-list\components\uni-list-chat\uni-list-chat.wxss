/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-list-chat {
  font-size: 16px;
  position: relative;
  flex-direction: column;
  justify-content: space-between;
  background-color: #fff;
}
.uni-list-chat--hover {
  background-color: #f5f5f5;
}
.uni-list--border {
  position: relative;
  margin-left: 15px;
}
.uni-list--border:after {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 1px;
  content: "";
  transform: scaleY(0.5);
  background-color: #e5e5e5;
}
.uni-list-item--first:after {
  height: 0px;
}
.uni-list-chat--first {
  border-top-width: 0px;
}
.uni-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.uni-ellipsis-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.uni-list-chat__container {
  position: relative;
  display: flex;
  flex-direction: row;
  flex: 1;
  padding: 10px 15px;
  position: relative;
  overflow: hidden;
}
.uni-list-chat__header-warp {
  position: relative;
}
.uni-list-chat__header {
  display: flex;
  align-content: center;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap-reverse;
  width: 45px;
  height: 45px;
  border-radius: 5px;
  border-color: #eee;
  border-width: 1px;
  border-style: solid;
  overflow: hidden;
}
.uni-list-chat__header-box {
  box-sizing: border-box;
  display: flex;
  width: 45px;
  height: 45px;
  overflow: hidden;
  border-radius: 2px;
}
.uni-list-chat__header-image {
  margin: 1px;
  width: 45px;
  height: 45px;
}
.uni-list-chat__header-image {
  display: block;
  width: 100%;
  height: 100%;
}
.avatarItem--1 {
  width: 100%;
  height: 100%;
}
.avatarItem--2 {
  width: 47%;
  height: 47%;
}
.avatarItem--3 {
  width: 32%;
  height: 32%;
}
.header--circle {
  border-radius: 50%;
}
.uni-list-chat__content {
  display: flex;
  flex-direction: row;
  flex: 1;
  overflow: hidden;
  padding: 2px 0;
}
.uni-list-chat__content-main {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-left: 10px;
  flex: 1;
  overflow: hidden;
}
.uni-list-chat__content-title {
  font-size: 16px;
  color: #3b4144;
  font-weight: normal;
  overflow: hidden;
}
.draft, .uni-list-chat__content-note {
  margin-top: 3px;
  color: #999;
  font-size: 12px;
  font-weight: normal;
  overflow: hidden;
}
.draft {
  color: #eb3a41;
  flex-shrink: 0;
  padding-right: 3px;
}
.uni-list-chat__content-extra {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-end;
  margin-left: 5px;
}
.uni-list-chat__content-extra-text {
  color: #999;
  font-size: 12px;
  font-weight: normal;
  overflow: hidden;
}
.uni-list-chat__badge-pos {
  position: absolute;
  left: calc(45px + 10px - 6px + 0px);
  top: calc(10px / 2 + 1px + 0px);
}
.uni-list-chat__badge {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 100px;
  background-color: #ff5a5f;
}
.uni-list-chat__badge-text {
  color: #fff;
  font-size: 12px;
}
.uni-badge--single {
  width: 18px;
  height: 18px;
}
.uni-badge--complex {
  width: auto;
  height: 18px;
  padding: 0 6px;
}
.uni-badge--dot {
  left: calc(45px + 15px - 10px / 2 + 1px + 0px);
  width: 10px;
  height: 10px;
  padding: 0;
}