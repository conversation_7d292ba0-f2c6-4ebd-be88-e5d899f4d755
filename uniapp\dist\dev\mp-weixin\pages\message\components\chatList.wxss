/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.z-paging-content.data-v-c0fbcb96 {
  background-color: #f1f1f1;
}
.list.data-v-c0fbcb96 {
  position: relative;
}
.list.isTop.data-v-c0fbcb96 .uni-list-chat {
  background-color: #eee;
}
.list .avatar.data-v-c0fbcb96 {
  position: absolute;
  top: 10px;
  left: 15px;
  background-color: #0081ff;
  font-size: 24px;
  width: 45px;
  height: 45px;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}
.list .avatar.group.data-v-c0fbcb96 {
  background-color: #f37b1d;
  font-size: 18px;
}
.data-v-c0fbcb96 .uni-list-chat .uni-list-chat__badge {
  z-index: 2;
}
.data-v-c0fbcb96 .uni-list-chat .uni-list-chat__content-title {
  color: #9ca3af;
}
.data-v-c0fbcb96 .uni-list-chat .uni-list-chat__content-title {
  font-size: 15px;
}
.data-v-c0fbcb96 .uni-list-chat .uni-list-chat__header {
  background-color: #eee;
}
.data-v-c0fbcb96 .wd-popup.wd-popup--bottom {
  bottom: 50px;
}