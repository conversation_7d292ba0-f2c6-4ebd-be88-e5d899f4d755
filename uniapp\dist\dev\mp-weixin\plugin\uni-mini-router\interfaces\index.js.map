{"version": 3, "file": "index.js", "sources": ["../../../../../../src/plugin/uni-mini-router/interfaces/index.ts"], "sourcesContent": ["/*\r\n * @Author: 徐庆凯\r\n * @Date: 2023-03-13 15:48:09\r\n * @LastEditTime: 2023-07-06 16:07:30\r\n * @LastEditors: weisheng\r\n * @Description:\r\n * @FilePath: \\uni-mini-router\\src\\interfaces\\index.ts\r\n * 记得注释\r\n */\r\n/* eslint-disable @typescript-eslint/ban-types */\r\n\r\nimport { Ref } from 'vue'\r\n\r\n/**\r\n * Router instance.\r\n */\r\nexport interface Router {\r\n  route: Ref<Route> // 当前路由信息\r\n  routes: any // 路由表\r\n  readonly guardHooks: GuardHooksConfig // 守卫钩子\r\n  back(to?: RouteBackLocation): void\r\n  push(to: RouteLocationRaw): void\r\n  replace(to: RouteLocationRaw): void\r\n  replaceAll(to: RouteLocationRaw): void\r\n  pushTab(to: RouteLocationRaw): void\r\n  beforeEach(userGuard: BeforeEachGuard): void // 全局前置路由守卫\r\n  afterEach(userGuard: AfterEachGuard): void // 全局后置路由守卫\r\n  install(App: any): void\r\n}\r\n\r\nexport type BeforeEachGuard = (to: Route, from: Route, next: (rule?: NextRouteLocationRaw | boolean) => void) => void | Promise<void> // 全局前置守卫函数\r\nexport type AfterEachGuard = (to: Route, from: Route) => void // 全局后置守卫函数\r\n\r\nexport interface GuardHooksConfig {\r\n  beforeHooks: BeforeEachGuard[] // 前置钩子\r\n  afterHooks: AfterEachGuard[] // 后置钩子\r\n}\r\n\r\nexport interface RouteLocationBase {\r\n  animationType?: StartAnimationType | EndAnimationType // 动画类型\r\n  animationDuration?: number // 动画时间\r\n}\r\n\r\nexport type StartAnimationType =\r\n  | 'slide-in-right'\r\n  | 'slide-in-left'\r\n  | 'slide-in-top'\r\n  | 'slide-in-bottom'\r\n  | 'pop-in'\r\n  | 'fade-in'\r\n  | 'zoom-out'\r\n  | 'zoom-fade-out'\r\n  | 'none'\r\nexport type EndAnimationType =\r\n  | 'slide-out-right'\r\n  | 'slide-out-left'\r\n  | 'slide-out-top'\r\n  | 'slide-out-bottom'\r\n  | 'pop-out'\r\n  | 'fade-out'\r\n  | 'zoom-in'\r\n  | 'zoom-fade-in'\r\n  | 'none'\r\n\r\n// name与params组合\r\nexport interface RouteNameLocation extends RouteLocationBase {\r\n  name: string // 路由名称\r\n  params?: Record<string, string> // 参数\r\n}\r\n\r\n// path与query组合\r\nexport interface RoutePathLocation extends RouteLocationBase {\r\n  path: string // 路由路径\r\n  query?: Record<string, string> // 参数\r\n}\r\n\r\n// back方法参数\r\nexport interface RouteBackLocation extends RouteLocationBase {\r\n  animationType: EndAnimationType\r\n  delta?: number // 返回的页面数，如果 delta 大于现有页面数，则返回到首页。\r\n}\r\n\r\nexport type RouteUrlLocation = string\r\nexport type RouteLocationRaw = RouteUrlLocation | RouteNameLocation | RoutePathLocation // 路由位置\r\n\r\n// 创建路由实例的选项\r\nexport interface RouterOptions {\r\n  routes: any\r\n}\r\n\r\n// 路由信息\r\nexport interface Route {\r\n  fullPath?: string\r\n  aliasPath?: string\r\n  name?: string\r\n  path?: string\r\n  query?: Record<string, any>\r\n  params?: Record<string, any>\r\n}\r\n// 导航类型\r\nexport type NAVTYPE = 'push' | 'replace' | 'replaceAll' | 'pushTab' | 'back'\r\nexport type NavMethodType = 'navigateTo' | 'redirectTo' | 'reLaunch' | 'switchTab' | 'navigateBack'\r\n\r\n// 导航类型枚举\r\nexport enum NavTypeEnum {\r\n  push = 'navigateTo',\r\n  replace = 'redirectTo',\r\n  replaceAll = 'reLaunch',\r\n  pushTab = 'switchTab',\r\n  back = 'navigateBack'\r\n}\r\n\r\n// 导航类型枚举反向映射\r\n// export enum NavTypeReverseEnum {\r\n//   navigateTo = 'push',\r\n//   redirectTo = 'replace',\r\n//   reLaunch = 'replaceAll',\r\n//   switchTab = 'pushTab',\r\n//   navigateBack = 'back'\r\n// }\r\nexport type HookType = 'beforeHooks' | 'afterHooks'\r\nexport const NavMethod: NavMethodType[] = ['navigateTo', 'redirectTo', 'reLaunch', 'switchTab', 'navigateBack']\r\n\r\n// next方法\r\n// name与params组合\r\nexport interface NextRouteNameLocation extends RouteNameLocation {\r\n  navType?: NAVTYPE // 导航类型\r\n}\r\n\r\n// path与query组合\r\nexport interface NextRoutePathLocation extends RoutePathLocation {\r\n  navType?: NAVTYPE // 导航类型\r\n}\r\n\r\n// back方法参数\r\nexport interface NextRouteBackLocation extends RouteBackLocation {\r\n  navType?: NAVTYPE // 导航类型\r\n}\r\n\r\n// Next方法入参\r\nexport type NextRouteLocationRaw = RouteUrlLocation | NextRouteNameLocation | NextRoutePathLocation | NextRouteBackLocation\r\n"], "names": ["NavTypeEnum"], "mappings": ";AAwGY,IAAA,gCAAAA,iBAAL;AACLA,eAAA,MAAO,IAAA;AACPA,eAAA,SAAU,IAAA;AACVA,eAAA,YAAa,IAAA;AACbA,eAAA,SAAU,IAAA;AACVA,eAAA,MAAO,IAAA;AALGA,SAAAA;AAAA,GAAA,eAAA,CAAA,CAAA;AAiBL,MAAM,YAA6B,CAAC,cAAc,cAAc,YAAY,aAAa,cAAc;;;"}