{"version": 3, "file": "wd-text.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-text/wd-text.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC10ZXh0L3dkLXRleHQudnVl"], "sourcesContent": ["<template>\n  <text @click=\"handleClick\" :class=\"rootClass\" :style=\"rootStyle\">\n    <slot v-if=\"$slots.prefix || prefix\" name=\"prefix\">{{ prefix }}</slot>\n    <text>{{ formattedText }}</text>\n    <slot v-if=\"$slots.suffix || suffix\" name=\"suffix\">{{ suffix }}</slot>\n  </text>\n</template>\n\n<script lang=\"ts\">\nexport default {\n  name: 'wd-text',\n  options: {\n    virtualHost: true,\n    addGlobalClass: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport { computed, ref, watch } from 'vue'\nimport { isDef, objToStyle } from '../common/util'\nimport { textProps } from './types'\nimport { dayjs } from '../common/dayjs'\n\n// 获取组件的 props 和 emit 函数\nconst props = defineProps(textProps)\nconst emit = defineEmits(['click'])\n\n// 存储文本类名的响应式变量\nconst textClass = ref<string>('')\n\n// 监听 props 变化，合并 watch 逻辑\nwatch(\n  () => ({\n    type: props.type,\n    text: props.text,\n    mode: props.mode,\n    color: props.color,\n    bold: props.bold,\n    lines: props.lines,\n    format: props.format\n  }),\n  ({ type }) => {\n    // 验证 type 属性\n    const types = ['primary', 'error', 'warning', 'success', 'default']\n    if (type && !types.includes(type)) {\n      console.error(`type must be one of ${types.toString()}`)\n    }\n    computeTextClass()\n  },\n  { deep: true, immediate: true }\n)\n\n// 计算根元素的类名\nconst rootClass = computed(() => {\n  return `wd-text ${props.customClass} ${textClass.value}`\n})\n\n// 计算根元素的样式\nconst rootStyle = computed(() => {\n  const rootStyle: Record<string, any> = {}\n  if (props.color) {\n    rootStyle['color'] = props.color\n  }\n  if (props.size) {\n    rootStyle['font-size'] = `${props.size}`\n  }\n  if (props.lineHeight) {\n    rootStyle['line-height'] = `${props.lineHeight}`\n  }\n  if (props.decoration) {\n    rootStyle['text-decoration'] = `${props.decoration}`\n  }\n  return `${objToStyle(rootStyle)}${props.customStyle}`\n})\n\n// 计算文本类名的函数\nfunction computeTextClass() {\n  const { type, color, bold, lines } = props\n  const textClassList: string[] = []\n  if (!color) {\n    textClassList.push(`is-${type}`)\n  }\n  if (isDef(lines)) {\n    textClassList.push(`is-lines-${lines}`)\n  }\n  bold && textClassList.push('is-bold')\n  textClass.value = textClassList.join(' ')\n}\n\n// 格式化文本的函数\nfunction formatText(text: string, format: boolean, mode: string): string {\n  if (format) {\n    if (mode === 'phone') {\n      return text.replace(/^(\\d{3})\\d{4}(\\d{4})$/, '$1****$2')\n    } else if (mode === 'name') {\n      return text.replace(/^(.).*(.)$/, '$1**$2')\n    } else {\n      throw new Error('mode must be one of phone or name for encryption')\n    }\n  }\n  return text\n}\n\n// 格式化数字的函数\nfunction formatNumber(num: number | string): string {\n  num = Number(num).toFixed(2)\n  const x = num.split('.')\n  let x1 = x[0]\n  const x2 = x.length > 1 ? '.' + x[1] : ''\n  const rgx = /(\\d+)(\\d{3})/\n  while (rgx.test(x1)) {\n    x1 = x1.replace(rgx, '$1,$2')\n  }\n  return x1 + x2\n}\n\n// 计算格式化后的文本\nconst formattedText = computed(() => {\n  const { text, mode, format } = props\n  if (mode === 'date') {\n    return dayjs(Number(text)).format('YYYY-MM-DD')\n  }\n  if (mode === 'price') {\n    return formatNumber(text)\n  }\n  return formatText(`${text}`, format, mode)\n})\n\n// 处理点击事件\nfunction handleClick(event: Event) {\n  emit('click', event)\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-text/wd-text.vue'\nwx.createComponent(Component)"], "names": ["ref", "watch", "computed", "rootStyle", "objToStyle", "isDef", "dayjs"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AASA,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,EAAA;AAEpB;;;;;AAUA,UAAM,QAAQ;AACd,UAAM,OAAO;AAGP,UAAA,YAAYA,kBAAY,EAAE;AAGhCC,kBAAA;AAAA,MACE,OAAO;AAAA,QACL,MAAM,MAAM;AAAA,QACZ,MAAM,MAAM;AAAA,QACZ,MAAM,MAAM;AAAA,QACZ,OAAO,MAAM;AAAA,QACb,MAAM,MAAM;AAAA,QACZ,OAAO,MAAM;AAAA,QACb,QAAQ,MAAM;AAAA,MAAA;AAAA,MAEhB,CAAC,EAAE,KAAA,MAAW;AAEZ,cAAM,QAAQ,CAAC,WAAW,SAAS,WAAW,WAAW,SAAS;AAClE,YAAI,QAAQ,CAAC,MAAM,SAAS,IAAI,GAAG;AACjC,kBAAQ,MAAM,uBAAuB,MAAM,SAAU,CAAA,EAAE;AAAA,QAAA;AAExC,yBAAA;AAAA,MACnB;AAAA,MACA,EAAE,MAAM,MAAM,WAAW,KAAK;AAAA,IAChC;AAGM,UAAA,YAAYC,cAAAA,SAAS,MAAM;AAC/B,aAAO,WAAW,MAAM,WAAW,IAAI,UAAU,KAAK;AAAA,IAAA,CACvD;AAGK,UAAA,YAAYA,cAAAA,SAAS,MAAM;AAC/B,YAAMC,aAAiC,CAAC;AACxC,UAAI,MAAM,OAAO;AACfA,mBAAU,OAAO,IAAI,MAAM;AAAA,MAAA;AAE7B,UAAI,MAAM,MAAM;AACdA,mBAAU,WAAW,IAAI,GAAG,MAAM,IAAI;AAAA,MAAA;AAExC,UAAI,MAAM,YAAY;AACpBA,mBAAU,aAAa,IAAI,GAAG,MAAM,UAAU;AAAA,MAAA;AAEhD,UAAI,MAAM,YAAY;AACpBA,mBAAU,iBAAiB,IAAI,GAAG,MAAM,UAAU;AAAA,MAAA;AAEpD,aAAO,GAAGC,cAAAA,WAAWD,UAAS,CAAC,GAAG,MAAM,WAAW;AAAA,IAAA,CACpD;AAGD,aAAS,mBAAmB;AAC1B,YAAM,EAAE,MAAM,OAAO,MAAM,MAAU,IAAA;AACrC,YAAM,gBAA0B,CAAC;AACjC,UAAI,CAAC,OAAO;AACI,sBAAA,KAAK,MAAM,IAAI,EAAE;AAAA,MAAA;AAE7B,UAAAE,cAAAA,MAAM,KAAK,GAAG;AACF,sBAAA,KAAK,YAAY,KAAK,EAAE;AAAA,MAAA;AAEhC,cAAA,cAAc,KAAK,SAAS;AAC1B,gBAAA,QAAQ,cAAc,KAAK,GAAG;AAAA,IAAA;AAIjC,aAAA,WAAW,MAAc,QAAiB,MAAsB;AACvE,UAAI,QAAQ;AACV,YAAI,SAAS,SAAS;AACb,iBAAA,KAAK,QAAQ,yBAAyB,UAAU;AAAA,QAAA,WAC9C,SAAS,QAAQ;AACnB,iBAAA,KAAK,QAAQ,cAAc,QAAQ;AAAA,QAAA,OACrC;AACC,gBAAA,IAAI,MAAM,kDAAkD;AAAA,QAAA;AAAA,MACpE;AAEK,aAAA;AAAA,IAAA;AAIT,aAAS,aAAa,KAA8B;AAClD,YAAM,OAAO,GAAG,EAAE,QAAQ,CAAC;AACrB,YAAA,IAAI,IAAI,MAAM,GAAG;AACnB,UAAA,KAAK,EAAE,CAAC;AACZ,YAAM,KAAK,EAAE,SAAS,IAAI,MAAM,EAAE,CAAC,IAAI;AACvC,YAAM,MAAM;AACL,aAAA,IAAI,KAAK,EAAE,GAAG;AACd,aAAA,GAAG,QAAQ,KAAK,OAAO;AAAA,MAAA;AAE9B,aAAO,KAAK;AAAA,IAAA;AAIR,UAAA,gBAAgBH,cAAAA,SAAS,MAAM;AACnC,YAAM,EAAE,MAAM,MAAM,OAAW,IAAA;AAC/B,UAAI,SAAS,QAAQ;AACnB,eAAOI,cAAAA,QAAM,OAAO,IAAI,CAAC,EAAE,OAAO,YAAY;AAAA,MAAA;AAEhD,UAAI,SAAS,SAAS;AACpB,eAAO,aAAa,IAAI;AAAA,MAAA;AAE1B,aAAO,WAAW,GAAG,IAAI,IAAI,QAAQ,IAAI;AAAA,IAAA,CAC1C;AAGD,aAAS,YAAY,OAAc;AACjC,WAAK,SAAS,KAAK;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;ACnIrB,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}