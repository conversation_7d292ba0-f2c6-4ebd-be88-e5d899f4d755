{"version": 3, "file": "message.js", "sources": ["../../../../../src/pages/message/message.vue", "../../../../../uniPage:/cGFnZXMvbWVzc2FnZS9tZXNzYWdlLnZ1ZQ"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n  <PageLayout :navbarShow=\"false\">\r\n    <view\r\n      class=\"wrap\"\r\n      :style=\"{\r\n        '--nav-height': `${statusBarHeight + navHeight}px`,\r\n        '--status-bar-height': `${statusBarHeight}px`,\r\n      }\"\r\n    >\r\n      <wd-tabs :customClass=\"getClass()\" v-model=\"tabActive\">\r\n        <template v-for=\"(item, index) in tabList\" :key=\"index\">\r\n          <wd-tab :title=\"item.title\" :name=\"item.key\">\r\n            <chatList v-if=\"item.key === '1'\"></chatList>\r\n            <addressBookList v-if=\"item.key === '2'\"></addressBookList>\r\n          </wd-tab>\r\n        </template>\r\n      </wd-tabs>\r\n    </view>\r\n  </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport chatList from './components/chatList.vue'\r\nimport addressBookList from './components/addressBookList.vue'\r\nimport { platform, isMp } from '@/utils/platform'\r\ndefineOptions({\r\n  name: 'message',\r\n  options: {\r\n    // apply-shared‌：当前页面样式会影响到子组件样式.(小程序)\r\n    // shared‌：当前页面样式影响到子组件，子组件样式也会影响到当前页面.(小程序)\r\n    styleIsolation: '‌shared‌',\r\n  },\r\n})\r\nimport { ref } from 'vue'\r\nconst globalData = getApp().globalData\r\nconst { systemInfo, navHeight } = globalData\r\nconst { statusBarHeight } = systemInfo\r\nconsole.log('systemInfo:::', systemInfo)\r\nconst tabList = ref([\r\n  { key: '1', title: '消息' },\r\n  { key: '2', title: '通讯录' },\r\n])\r\nconst tabActive = ref<string>('1')\r\nconst getClass = () => {\r\n  return `${platform} ${isMp ? 'mp' : ''}`\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.wrap {\r\n  height: 100%;\r\n}\r\n:deep(.wd-tabs) {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  &.mp {\r\n    .wd-tabs__nav-container {\r\n      padding-right: 7%;\r\n    }\r\n  }\r\n  .wd-tabs__nav {\r\n    background: linear-gradient(45deg, #0081ff, #1cbbb4);\r\n    height: var(--nav-height);\r\n    padding-top: var(--status-bar-height);\r\n    .wd-tabs__nav-item {\r\n      color: #fff;\r\n    }\r\n  }\r\n  .wd-tabs__container {\r\n    flex: 1;\r\n    width: 100%;\r\n  }\r\n  .wd-tabs__body {\r\n    position: relative;\r\n  }\r\n  .wd-tabs__line {\r\n    background-color: #fff;\r\n  }\r\n}\r\n:deep(.wd-tab) {\r\n  .wd-tab__body {\r\n    position: absolute;\r\n    height: 100%;\r\n    width: 100%;\r\n    top: 0;\r\n    left: 0;\r\n  }\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages/message/message.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "platform", "isMp"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,MAAA,WAAqB,MAAA;AACrB,MAAA,kBAA4B,MAAA;;;;;;;;;;;AAWtB,UAAA,aAAa,SAAS;AACtB,UAAA,EAAE,YAAY,UAAA,IAAc;AAC5B,UAAA,EAAE,oBAAoB;AACpB,YAAA,IAAI,iBAAiB,UAAU;AACvC,UAAM,UAAUA,cAAAA,IAAI;AAAA,MAClB,EAAE,KAAK,KAAK,OAAO,KAAK;AAAA,MACxB,EAAE,KAAK,KAAK,OAAO,MAAM;AAAA,IAAA,CAC1B;AACK,UAAA,YAAYA,kBAAY,GAAG;AACjC,UAAM,WAAW,MAAM;AACrB,aAAO,GAAGC,eAAAA,QAAQ,IAAIC,eAAAA,OAAO,OAAO,EAAE;AAAA,IACxC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChDA,GAAG,WAAW,eAAe;"}