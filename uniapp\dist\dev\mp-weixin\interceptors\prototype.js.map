{"version": 3, "file": "prototype.js", "sources": ["../../../../src/interceptors/prototype.ts"], "sourcesContent": ["export const prototypeInterceptor = {\r\n  install() {\r\n    // 解决低版本手机不识别 array.at() 导致运行报错的问题\r\n    if (typeof Array.prototype.at !== 'function') {\r\n      // eslint-disable-next-line no-extend-native\r\n      Array.prototype.at = function (index: number) {\r\n        if (index < 0) return this[this.length + index]\r\n        if (index >= this.length) return undefined\r\n        return this[index]\r\n      }\r\n    }\r\n  },\r\n}\r\n"], "names": [], "mappings": ";AAAO,MAAM,uBAAuB;AAAA,EAClC,UAAU;AAER,QAAI,OAAO,MAAM,UAAU,OAAO,YAAY;AAEtC,YAAA,UAAU,KAAK,SAAU,OAAe;AAC5C,YAAI,QAAQ;AAAU,iBAAA,KAAK,KAAK,SAAS,KAAK;AAC9C,YAAI,SAAS,KAAK;AAAe,iBAAA;AACjC,eAAO,KAAK,KAAK;AAAA,MACnB;AAAA,IAAA;AAAA,EACF;AAEJ;;"}