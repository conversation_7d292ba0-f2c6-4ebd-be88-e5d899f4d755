{"version": 3, "file": "wd-status-tip.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-status-tip/wd-status-tip.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1zdGF0dXMtdGlwL3dkLXN0YXR1cy10aXAudnVl"], "sourcesContent": ["<!--\n * @Author: weish<PERSON>\n * @Date: 2023-06-12 10:04:19\n * @LastEditTime: 2024-09-20 10:23:38\n * @LastEditors: jiao<PERSON><PERSON>yan\n * @Description: \n * @FilePath: \\wot-design-uni\\src\\uni_modules\\wot-design-uni\\components\\wd-status-tip\\wd-status-tip.vue\n * 记得注释\n-->\n<template>\n  <view :class=\"`wd-status-tip  ${customClass}`\" :style=\"customStyle\">\n    <slot name=\"image\" v-if=\"$slots.image\"></slot>\n    <wd-img v-else-if=\"imgUrl\" :mode=\"imageMode\" :src=\"imgUrl\" custom-class=\"wd-status-tip__image\" :custom-style=\"imgStyle\"></wd-img>\n    <view v-if=\"tip\" class=\"wd-status-tip__text\">{{ tip }}</view>\n  </view>\n</template>\n\n<script lang=\"ts\">\nexport default {\n  name: 'wd-status-tip',\n  options: {\n    addGlobalClass: true,\n    virtualHost: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport wdImg from '../wd-img/wd-img.vue'\nimport { computed, type CSSProperties } from 'vue'\nimport { addUnit, isDef, isObj, objToStyle } from '../common/util'\nimport { statusTipProps } from './types'\n\nconst props = defineProps(statusTipProps)\n\n// 图片地址\nconst imgUrl = computed(() => {\n  // 改用网络地址，避免小程序打包的时候统一打包进去导致包过大问题\n  let img: string = ''\n  if (['search', 'network', 'content', 'collect', 'comment', 'halo', 'message'].includes(props.image)) {\n    img = `${props.urlPrefix}${props.image}.png`\n  } else {\n    img = props.image\n  }\n  return img\n})\n\n/**\n * 图片样式\n */\nconst imgStyle = computed(() => {\n  let style: CSSProperties = {}\n  if (props.imageSize) {\n    if (isObj(props.imageSize)) {\n      isDef(props.imageSize.height) && (style.height = addUnit(props.imageSize.height))\n      isDef(props.imageSize.width) && (style.width = addUnit(props.imageSize.width))\n    } else {\n      style = {\n        height: addUnit(props.imageSize),\n        width: addUnit(props.imageSize)\n      }\n    }\n  }\n  return `${objToStyle(style)}`\n})\n</script>\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-status-tip/wd-status-tip.vue'\nwx.createComponent(Component)"], "names": ["computed", "isObj", "isDef", "addUnit", "objToStyle"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AA6BA,MAAA,QAAkB,MAAA;AAXlB,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,gBAAgB;AAAA,EAAA;AAEpB;;;;AASA,UAAM,QAAQ;AAGR,UAAA,SAASA,cAAAA,SAAS,MAAM;AAE5B,UAAI,MAAc;AAClB,UAAI,CAAC,UAAU,WAAW,WAAW,WAAW,WAAW,QAAQ,SAAS,EAAE,SAAS,MAAM,KAAK,GAAG;AACnG,cAAM,GAAG,MAAM,SAAS,GAAG,MAAM,KAAK;AAAA,MAAA,OACjC;AACL,cAAM,MAAM;AAAA,MAAA;AAEP,aAAA;AAAA,IAAA,CACR;AAKK,UAAA,WAAWA,cAAAA,SAAS,MAAM;AAC9B,UAAI,QAAuB,CAAC;AAC5B,UAAI,MAAM,WAAW;AACf,YAAAC,cAAA,MAAM,MAAM,SAAS,GAAG;AACpBC,wBAAAA,MAAA,MAAM,UAAU,MAAM,MAAM,MAAM,SAASC,sBAAQ,MAAM,UAAU,MAAM;AACzED,wBAAAA,MAAA,MAAM,UAAU,KAAK,MAAM,MAAM,QAAQC,sBAAQ,MAAM,UAAU,KAAK;AAAA,QAAA,OACvE;AACG,kBAAA;AAAA,YACN,QAAQA,cAAAA,QAAQ,MAAM,SAAS;AAAA,YAC/B,OAAOA,cAAAA,QAAQ,MAAM,SAAS;AAAA,UAChC;AAAA,QAAA;AAAA,MACF;AAEK,aAAA,GAAGC,cAAAA,WAAW,KAAK,CAAC;AAAA,IAAA,CAC5B;;;;;;;;;;;;;;;;;;;;;;;;AChED,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}