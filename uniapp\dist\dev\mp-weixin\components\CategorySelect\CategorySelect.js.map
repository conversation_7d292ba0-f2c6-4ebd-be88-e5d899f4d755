{"version": 3, "file": "CategorySelect.js", "sources": ["../../../../../src/components/CategorySelect/CategorySelect.vue", "../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvY29tcG9uZW50cy9DYXRlZ29yeVNlbGVjdC9DYXRlZ29yeVNlbGVjdC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"CategorySelect\">\r\n    <view @click=\"handleClick\">\r\n      <wd-input\r\n        :placeholder=\"`请选择${$attrs.label}`\"\r\n        v-bind=\"$attrs\"\r\n        readonly\r\n        v-model=\"showText\"\r\n      ></wd-input>\r\n    </view>\r\n    <wd-popup position=\"bottom\" v-model=\"popupShow\">\r\n      <view class=\"content\">\r\n        <view class=\"operation\">\r\n          <view class=\"cancel text-gray-5\" @click.stop=\"cancel\">取消</view>\r\n          <view class=\"confrim\" @click.stop=\"confirm\">确定</view>\r\n        </view>\r\n        <scroll-view class=\"flex-1\" scroll-y>\r\n          <DaTree\r\n            :data=\"treeData\"\r\n            labelField=\"title\"\r\n            valueField=\"key\"\r\n            loadMode\r\n            :showCheckbox=\"multiple\"\r\n            :showRadioIcon=\"false\"\r\n            :checkStrictly=\"true\"\r\n            :loadApi=\"asyncLoadTreeData\"\r\n            @change=\"handleTreeChange\"\r\n          ></DaTree>\r\n        </scroll-view>\r\n      </view>\r\n    </wd-popup>\r\n  </view>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, watch, useAttrs } from 'vue'\r\nimport { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'\r\nimport { http } from '@/utils/http'\r\nimport DaTree from '@/uni_modules/da-tree/index.vue'\r\nimport { isArray } from '@/utils/is'\r\ndefineOptions({\r\n  name: 'CategorySelect',\r\n})\r\nconst props = defineProps({\r\n  modelValue: {\r\n    type: [Array, String],\r\n  },\r\n  placeholder: {\r\n    type: String,\r\n    default: '请选择',\r\n    required: false,\r\n  },\r\n  condition: {\r\n    type: String,\r\n    default: '',\r\n    required: false,\r\n  },\r\n  // 是否支持多选\r\n  multiple: {\r\n    type: Boolean,\r\n    default: false,\r\n  },\r\n  pid: {\r\n    type: String,\r\n    default: '',\r\n    required: false,\r\n  },\r\n  pcode: {\r\n    type: String,\r\n    default: '',\r\n    required: false,\r\n  },\r\n})\r\nconst emit = defineEmits(['change', 'update:modelValue'])\r\nconst toast = useToast()\r\nconst api = {\r\n  loadDictItem: '/sys/category/loadDictItem/',\r\n  loadTreeData: '/sys/category/loadTreeData',\r\n}\r\nconst showText = ref('')\r\nconst popupShow = ref(false)\r\nconst treeData = ref<any[]>([])\r\nconst treeValue = ref([])\r\nconst handleClick = () => {\r\n  popupShow.value = true\r\n}\r\nconst cancel = () => {\r\n  popupShow.value = false\r\n}\r\nconst confirm = () => {\r\n  const titles = treeValue.value.map((item) => item.title)\r\n  const keys = treeValue.value.map((item) => item.key).join(',')\r\n  showText.value = titles.join(',')\r\n  popupShow.value = false\r\n  emit('update:modelValue', keys)\r\n  emit('change', keys)\r\n}\r\nconst handleTreeChange = (value, record) => {\r\n  const { originItem, checkedStatus } = record\r\n  const { key, title } = originItem\r\n  if (checkedStatus) {\r\n    // 选中\r\n    if (props.multiple) {\r\n      treeValue.value.push({ key, title })\r\n    } else {\r\n      treeValue.value = [{ key, title }]\r\n    }\r\n  } else {\r\n    // 取消\r\n    if (props.multiple) {\r\n      const findIndex = treeValue.value.findIndex((item) => item.key == key)\r\n      if (findIndex != -1) {\r\n        treeValue.value.splice(findIndex, 1)\r\n      }\r\n    } else {\r\n      treeValue.value = []\r\n    }\r\n  }\r\n}\r\nconst transformField = (result) => {\r\n  for (let i of result) {\r\n    i.value = i.key\r\n    if (i.leaf == false) {\r\n      i.isLeaf = false\r\n    } else if (i.leaf == true) {\r\n      i.isLeaf = true\r\n    }\r\n  }\r\n}\r\n// 异步加载\r\nconst asyncLoadTreeData = ({ originItem }) => {\r\n  return new Promise<void>((resolve) => {\r\n    let param = {\r\n      pid: originItem.key,\r\n      condition: props.condition,\r\n    }\r\n    http\r\n      .get(api.loadTreeData, param)\r\n      .then((res: any) => {\r\n        if (res.success) {\r\n          const { result } = res\r\n          transformField(result)\r\n          resolve(result)\r\n        } else {\r\n          resolve(null)\r\n        }\r\n      })\r\n      .catch((err) => resolve(null))\r\n  })\r\n}\r\n// 加载根节点\r\nfunction loadRoot() {\r\n  let param = {\r\n    pid: props.pid,\r\n    pcode: !props.pcode ? '0' : props.pcode,\r\n    condition: props.condition,\r\n  }\r\n  http\r\n    .get(api.loadTreeData, param)\r\n    .then((res: any) => {\r\n      if (res.success) {\r\n        const { result } = res\r\n        if (result && result.length > 0) {\r\n          transformField(result)\r\n          treeData.value = result\r\n        }\r\n      } else {\r\n        toast.warning('分类字典书组件根节点数据加载失败~')\r\n      }\r\n    })\r\n    .catch((err) => {\r\n      toast.warning('分类字典书组件根节点数据加载失败~')\r\n    })\r\n}\r\n// 翻译input内的值\r\nfunction loadItemByCode() {\r\n  let value = props.modelValue\r\n  if (isArray(props.modelValue)) {\r\n    // @ts-ignore\r\n    value = value.join()\r\n  }\r\n  if (value === treeData.value.map((item) => item.key).join(',')) {\r\n    // 说明是刚选完，内部已有翻译。不需要再请求\r\n    return\r\n  }\r\n  http\r\n    .get(api.loadDictItem, { ids: value })\r\n    .then((res: any) => {\r\n      if (res.success) {\r\n        const { result = [] } = res\r\n        showText.value = result.join(',')\r\n      } else {\r\n      }\r\n    })\r\n    .catch((err) => {})\r\n}\r\n\r\nwatch(\r\n  () => props.modelValue,\r\n  () => {\r\n    loadItemByCode()\r\n  },\r\n  { deep: true, immediate: true },\r\n)\r\nwatch(\r\n  () => props.pcode,\r\n  () => {\r\n    loadRoot()\r\n  },\r\n  { deep: true, immediate: true },\r\n)\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n:deep(.wd-popup-wrapper) {\r\n  .wd-popup {\r\n    border-top-left-radius: 10px;\r\n    border-top-right-radius: 10px;\r\n  }\r\n}\r\n.content {\r\n  height: 50vh;\r\n  width: 100vw;\r\n  display: flex;\r\n  flex-direction: column;\r\n  .operation {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    line-height: 40px;\r\n    padding: 0 5px;\r\n    position: relative;\r\n    &::before {\r\n      content: ' ';\r\n      position: absolute;\r\n      bottom: 0;\r\n      left: 8px;\r\n      right: 8px;\r\n      height: 1px;\r\n      background-color: #e5e5e5;\r\n    }\r\n    .cancel,\r\n    .confrim {\r\n      font-size: 15px;\r\n      height: 40px;\r\n      min-width: 40px;\r\n      text-align: center;\r\n    }\r\n    .confrim {\r\n      color: var(--wot-color-theme);\r\n    }\r\n  }\r\n  :deep(.da-tree) {\r\n    .da-tree-item__checkbox {\r\n      // display: none;\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/components/CategorySelect/CategorySelect.vue'\nwx.createComponent(Component)"], "names": ["useToast", "ref", "http", "isArray", "watch"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCA,MAAA,SAAmB,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKnB,UAAM,QAAQ;AA8Bd,UAAM,OAAO;AACb,UAAM,QAAQA,cAAAA,SAAS;AACvB,UAAM,MAAM;AAAA,MACV,cAAc;AAAA,MACd,cAAc;AAAA,IAChB;AACM,UAAA,WAAWC,kBAAI,EAAE;AACjB,UAAA,YAAYA,kBAAI,KAAK;AACrB,UAAA,WAAWA,cAAW,IAAA,EAAE;AACxB,UAAA,YAAYA,cAAI,IAAA,EAAE;AACxB,UAAM,cAAc,MAAM;AACxB,gBAAU,QAAQ;AAAA,IACpB;AACA,UAAM,SAAS,MAAM;AACnB,gBAAU,QAAQ;AAAA,IACpB;AACA,UAAM,UAAU,MAAM;AACpB,YAAM,SAAS,UAAU,MAAM,IAAI,CAAC,SAAS,KAAK,KAAK;AACjD,YAAA,OAAO,UAAU,MAAM,IAAI,CAAC,SAAS,KAAK,GAAG,EAAE,KAAK,GAAG;AACpD,eAAA,QAAQ,OAAO,KAAK,GAAG;AAChC,gBAAU,QAAQ;AAClB,WAAK,qBAAqB,IAAI;AAC9B,WAAK,UAAU,IAAI;AAAA,IACrB;AACM,UAAA,mBAAmB,CAAC,OAAO,WAAW;AACpC,YAAA,EAAE,YAAY,cAAA,IAAkB;AAChC,YAAA,EAAE,KAAK,MAAA,IAAU;AACvB,UAAI,eAAe;AAEjB,YAAI,MAAM,UAAU;AAClB,oBAAU,MAAM,KAAK,EAAE,KAAK,OAAO;AAAA,QAAA,OAC9B;AACL,oBAAU,QAAQ,CAAC,EAAE,KAAK,OAAO;AAAA,QAAA;AAAA,MACnC,OACK;AAEL,YAAI,MAAM,UAAU;AACZ,gBAAA,YAAY,UAAU,MAAM,UAAU,CAAC,SAAS,KAAK,OAAO,GAAG;AACrE,cAAI,aAAa,IAAI;AACT,sBAAA,MAAM,OAAO,WAAW,CAAC;AAAA,UAAA;AAAA,QACrC,OACK;AACL,oBAAU,QAAQ,CAAC;AAAA,QAAA;AAAA,MACrB;AAAA,IAEJ;AACM,UAAA,iBAAiB,CAAC,WAAW;AACjC,eAAS,KAAK,QAAQ;AACpB,UAAE,QAAQ,EAAE;AACR,YAAA,EAAE,QAAQ,OAAO;AACnB,YAAE,SAAS;AAAA,QAAA,WACF,EAAE,QAAQ,MAAM;AACzB,YAAE,SAAS;AAAA,QAAA;AAAA,MACb;AAAA,IAEJ;AAEA,UAAM,oBAAoB,CAAC,EAAE,iBAAiB;AACrC,aAAA,IAAI,QAAc,CAAC,YAAY;AACpC,YAAI,QAAQ;AAAA,UACV,KAAK,WAAW;AAAA,UAChB,WAAW,MAAM;AAAA,QACnB;AACAC,mBAAA,KACG,IAAI,IAAI,cAAc,KAAK,EAC3B,KAAK,CAAC,QAAa;AAClB,cAAI,IAAI,SAAS;AACT,kBAAA,EAAE,WAAW;AACnB,2BAAe,MAAM;AACrB,oBAAQ,MAAM;AAAA,UAAA,OACT;AACL,oBAAQ,IAAI;AAAA,UAAA;AAAA,QAEf,CAAA,EACA,MAAM,CAAC,QAAQ,QAAQ,IAAI,CAAC;AAAA,MAAA,CAChC;AAAA,IACH;AAEA,aAAS,WAAW;AAClB,UAAI,QAAQ;AAAA,QACV,KAAK,MAAM;AAAA,QACX,OAAO,CAAC,MAAM,QAAQ,MAAM,MAAM;AAAA,QAClC,WAAW,MAAM;AAAA,MACnB;AACAA,iBAAA,KACG,IAAI,IAAI,cAAc,KAAK,EAC3B,KAAK,CAAC,QAAa;AAClB,YAAI,IAAI,SAAS;AACT,gBAAA,EAAE,WAAW;AACf,cAAA,UAAU,OAAO,SAAS,GAAG;AAC/B,2BAAe,MAAM;AACrB,qBAAS,QAAQ;AAAA,UAAA;AAAA,QACnB,OACK;AACL,gBAAM,QAAQ,mBAAmB;AAAA,QAAA;AAAA,MACnC,CACD,EACA,MAAM,CAAC,QAAQ;AACd,cAAM,QAAQ,mBAAmB;AAAA,MAAA,CAClC;AAAA,IAAA;AAGL,aAAS,iBAAiB;AACxB,UAAI,QAAQ,MAAM;AACd,UAAAC,SAAA,QAAQ,MAAM,UAAU,GAAG;AAE7B,gBAAQ,MAAM,KAAK;AAAA,MAAA;AAEjB,UAAA,UAAU,SAAS,MAAM,IAAI,CAAC,SAAS,KAAK,GAAG,EAAE,KAAK,GAAG,GAAG;AAE9D;AAAA,MAAA;AAGCD,sBAAA,IAAI,IAAI,cAAc,EAAE,KAAK,OAAO,EACpC,KAAK,CAAC,QAAa;AAClB,YAAI,IAAI,SAAS;AACf,gBAAM,EAAE,SAAS,CAAC,EAAA,IAAM;AACf,mBAAA,QAAQ,OAAO,KAAK,GAAG;AAAA,QAAA;AAAA,MAElC,CACD,EACA,MAAM,CAAC,QAAQ;AAAA,MAAA,CAAE;AAAA,IAAA;AAGtBE,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,MAAM;AACW,uBAAA;AAAA,MACjB;AAAA,MACA,EAAE,MAAM,MAAM,WAAW,KAAK;AAAA,IAChC;AACAA,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,MAAM;AACK,iBAAA;AAAA,MACX;AAAA,MACA,EAAE,MAAM,MAAM,WAAW,KAAK;AAAA,IAChC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjNA,GAAG,gBAAgB,SAAS;"}