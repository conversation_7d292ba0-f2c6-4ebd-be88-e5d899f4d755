{"version": 3, "file": "work.js", "sources": ["../../../../src/common/work.ts"], "sourcesContent": ["/**\r\n * 常用服务\r\n * useful server\r\n */\r\n\r\nconst icon_prefix = '/static/index/128/'\r\n\r\n/*\r\n */\r\nexport const us = {\r\n  data: [\r\n    {\r\n      title: 'online',\r\n      icon: icon_prefix + 'qingjia1.png',\r\n      description: '请假申请',\r\n      useCount: 10000,\r\n      routeIndex: 'online',\r\n      enabled: true,\r\n    },\r\n    {\r\n      title: '组件示例',\r\n      icon: icon_prefix + 'chuchai.png',\r\n      description: '出差申请',\r\n      useCount: 10000,\r\n      routeIndex: 'demo',\r\n      enabled: true,\r\n    },\r\n    {\r\n      title: '公文发文',\r\n      icon: icon_prefix + 'gongwen.png',\r\n      description: '公文发文',\r\n      useCount: 10000,\r\n      routeIndex: 'docSend',\r\n    },\r\n    {\r\n      title: '通知公告',\r\n      icon: icon_prefix + 'tongzhi.png',\r\n      description: '查看企业对员工下发的通知公告',\r\n      useCount: 10000,\r\n      routeIndex: 'annotationList',\r\n      enabled: true,\r\n    },\r\n    {\r\n      title: '日程',\r\n      icon: icon_prefix + 'richeng.png',\r\n      description: '建立和查看个人工作安排',\r\n      useCount: 10000,\r\n      routeIndex: 'plan',\r\n    },\r\n    {\r\n      title: '考勤',\r\n      icon: icon_prefix + 'kaoqin.png',\r\n      description: '工作考勤',\r\n      routeIndex: 'attendance',\r\n      useCount: 10000,\r\n    },\r\n    {\r\n      title: '内部邮件',\r\n      icon: icon_prefix + 'youjian.png',\r\n      description: '查看内部消息',\r\n      useCount: 10000,\r\n      dot: false,\r\n      routeIndex: 'mailHome',\r\n    },\r\n    {\r\n      title: '通讯录',\r\n      icon: icon_prefix + 'tongxun.png',\r\n      description: '查看组员',\r\n      useCount: 10000,\r\n      //routeIndex:'addressBook',\r\n      routeIndex: 'levelAddressBook',\r\n    },\r\n    {\r\n      title: '日报',\r\n      icon: icon_prefix + 'richang.png',\r\n      description: '记录每天的工作经验和心得',\r\n      useCount: 1000,\r\n    },\r\n    {\r\n      title: '周报',\r\n      icon: icon_prefix + 'zhoubao.png',\r\n      description: '总结每周的工作情况和下周计划',\r\n      useCount: 10000,\r\n    },\r\n  ],\r\n}\r\n\r\n/**\r\n * other server 其他服务\r\n */\r\nexport const os = {\r\n  data: [\r\n    {\r\n      title: '新闻中心',\r\n      icon: icon_prefix + 'xinwen.png',\r\n      description: '新闻中心',\r\n      routeIndex: 'columnList',\r\n      useCount: 10000,\r\n    },\r\n    {\r\n      title: '文档中心',\r\n      icon: icon_prefix + 'wendang.png',\r\n      description: '文档中心',\r\n      routeIndex: 'fileHome',\r\n      useCount: 10000,\r\n    },\r\n    {\r\n      title: '会议',\r\n      icon: icon_prefix + 'huiyi.png',\r\n      description: '会议',\r\n      useCount: 10000,\r\n      routeIndex: 'meeting',\r\n    },\r\n    {\r\n      title: '任务中心',\r\n      icon: icon_prefix + 'renwu.png',\r\n      description: '任务中心',\r\n      useCount: 10000,\r\n    },\r\n    {\r\n      title: '合同',\r\n      icon: icon_prefix + 'hetong.png',\r\n      description: '合同',\r\n      useCount: 10000,\r\n    },\r\n    // #ifndef MP-WEIXIN\r\n    {\r\n      title: '聊天',\r\n      icon: icon_prefix + 'kehu.png',\r\n      description: '聊天',\r\n      routeIndex: 'chathome',\r\n    },\r\n    // #endif\r\n  ],\r\n}\r\n"], "names": [], "mappings": ";AAKA,MAAM,cAAc;AAIb,MAAM,KAAK;AAAA,EAChB,MAAM;AAAA,IACJ;AAAA,MACE,OAAO;AAAA,MACP,MAAM,cAAc;AAAA,MACpB,aAAa;AAAA,MACb,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS;AAAA,IACX;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,MAAM,cAAc;AAAA,MACpB,aAAa;AAAA,MACb,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS;AAAA,IACX;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,MAAM,cAAc;AAAA,MACpB,aAAa;AAAA,MACb,UAAU;AAAA,MACV,YAAY;AAAA,IACd;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,MAAM,cAAc;AAAA,MACpB,aAAa;AAAA,MACb,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS;AAAA,IACX;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,MAAM,cAAc;AAAA,MACpB,aAAa;AAAA,MACb,UAAU;AAAA,MACV,YAAY;AAAA,IACd;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,MAAM,cAAc;AAAA,MACpB,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,UAAU;AAAA,IACZ;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,MAAM,cAAc;AAAA,MACpB,aAAa;AAAA,MACb,UAAU;AAAA,MACV,KAAK;AAAA,MACL,YAAY;AAAA,IACd;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,MAAM,cAAc;AAAA,MACpB,aAAa;AAAA,MACb,UAAU;AAAA;AAAA,MAEV,YAAY;AAAA,IACd;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,MAAM,cAAc;AAAA,MACpB,aAAa;AAAA,MACb,UAAU;AAAA,IACZ;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,MAAM,cAAc;AAAA,MACpB,aAAa;AAAA,MACb,UAAU;AAAA,IAAA;AAAA,EACZ;AAEJ;AAKO,MAAM,KAAK;AAAA,EAChB,MAAM;AAAA,IACJ;AAAA,MACE,OAAO;AAAA,MACP,MAAM,cAAc;AAAA,MACpB,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,UAAU;AAAA,IACZ;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,MAAM,cAAc;AAAA,MACpB,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,UAAU;AAAA,IACZ;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,MAAM,cAAc;AAAA,MACpB,aAAa;AAAA,MACb,UAAU;AAAA,MACV,YAAY;AAAA,IACd;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,MAAM,cAAc;AAAA,MACpB,aAAa;AAAA,MACb,UAAU;AAAA,IACZ;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,MAAM,cAAc;AAAA,MACpB,aAAa;AAAA,MACb,UAAU;AAAA,IAAA;AAAA,EACZ;AAUJ;;;"}