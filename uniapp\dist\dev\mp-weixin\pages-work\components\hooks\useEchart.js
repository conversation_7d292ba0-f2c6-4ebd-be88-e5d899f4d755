"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
const common_vendor = require("../../../common/vendor.js");
const pagesWork_components_common_echartUtil = require("../common/echartUtil.js");
const pagesWork_components_common_concants = require("../common/concants.js");
const utils_is = require("../../../utils/is.js");
const utils_http = require("../../../utils/http.js");
const pagesWork_common_is = require("../../common/is.js");
function useChartHook(props, initOption, echarts) {
  const config = props.config;
  const dataSource = common_vendor.ref([]);
  const reload = common_vendor.ref(true);
  const pageTips = common_vendor.reactive({
    show: true,
    status: 0
    // 0:loading,1:暂无数据,2:网络超时
  });
  const toast = common_vendor.useToast();
  common_vendor.ref("");
  const mapDataJson = common_vendor.ref({});
  let chartOption = {
    title: {
      show: true
    },
    card: {
      title: ""
    },
    tooltip: {
      formatter: ""
    },
    legend: {
      bottom: "5%",
      left: "center"
    },
    xAxis: {
      type: "category",
      data: []
    },
    yAxis: {
      type: "value"
    },
    series: [{}]
  };
  common_vendor.watch(
    props.config,
    (config2) => {
      if (!(props == null ? void 0 : props.isView)) {
        console.log("=======props.config============");
        queryData();
      }
    },
    { deep: true }
  );
  function queryData(compConfig, queryParams) {
    let config2 = compConfig ? compConfig : __spreadValues({}, props.config);
    if (config2.dataType == 2) {
      if (config2.dataSetId && config2.dataSetType == "api" && config2.dataSetIzAgent !== "1") {
        let { url, dataMap } = pagesWork_components_common_echartUtil.handleParam(config2);
        let linkParams = {};
        queryParams = Object.assign({}, dataMap, queryParams, linkParams);
        if (url.startsWith("#{api_base_path}") || url.startsWith("{{ domainURL }}")) {
          getAgentData(queryParams, config2);
        } else {
          let checkUrl = pagesWork_components_common_echartUtil.checkUrlPrefix(url);
          if (checkUrl.isDiffProtocol) {
            toast.warning("请求API地址需要https协议接口！");
            return;
          }
          getCompData({ url, queryParams }).then((res) => {
            var _a;
            dataSource.value = res.data || res;
            if ((res == null ? void 0 : res.result) && pagesWork_common_is.isArray(res == null ? void 0 : res.result)) {
              dataSource.value = res.result;
            } else if (((_a = res == null ? void 0 : res.result) == null ? void 0 : _a.records) && pagesWork_common_is.isArray(res == null ? void 0 : res.result.records)) {
              dataSource.value = res.result.records;
            }
            getDataCallBack();
          });
        }
      } else if (config2.dataSetType == "websocket")
        ;
      else {
        let { dataMap } = pagesWork_components_common_echartUtil.handleParam(config2);
        let linkParams = {};
        queryParams = Object.assign({}, dataMap, queryParams, linkParams);
        getAgentData(queryParams, config2);
      }
    } else if (config2.dataType == 4) {
      let params = getParams(config2, queryParams);
      utils_http.http.post("/drag/onlDragDatasetHead/getTotalData", params).then((res) => {
        if (res.success) {
          let result = res.result.chartData;
          if (result && result.length > 0) {
            try {
              let arr = JSON.parse(JSON.stringify(result));
              dataSource.value = pagesWork_components_common_echartUtil.handleDateFields(arr, config2);
              dataSource.value = pagesWork_components_common_echartUtil.handleCalcFields(arr, config2.valueFields, config2.assistYFields);
              initOption && utils_is.isFunction(initOption) && initOption();
            } catch (e) {
              console.log("查询数据报错", e);
            }
          } else {
            dataSource.value = [];
            initOption && utils_is.isFunction(initOption) && initOption();
          }
        }
      });
    } else {
      let chartData = props.config.chartData;
      if (typeof chartData === "string") {
        try {
          chartData = JSON.parse(chartData);
        } catch (e) {
        }
      }
      dataSource.value = chartData;
      initOption && initOption(chartData);
    }
  }
  const getCompData = (option) => {
    let { url, params: queryParams } = pagesWork_components_common_echartUtil.getUrlParams(option.url);
    if (!utils_is.isUrl(url)) {
      console.info("url", option.url);
      console.info("请求地址有问题", option.url);
      return;
    }
    let method = option.method ? option.method : "get";
    let params = option.params ? option.params : {};
    Object.assign(params, queryParams);
    let serverAgent = option.serverAgent ? option.serverAgent : false;
    if (serverAgent) {
      params = option;
    }
    return new Promise((resolve, reject) => {
      common_vendor.index.request({
        method,
        params,
        transformRequest: [
          function(data) {
            return JSON.stringify(__spreadValues({}, data));
          }
        ],
        url
      }).then((res) => {
        resolve(res.data);
      }).catch((err) => {
        reject(err.data);
      });
    });
  };
  function getAgentData(params, config2) {
    utils_http.http.post("/drag/onlDragDatasetHead/getAllChartData", {
      id: config2.dataSetId,
      params,
      dataMapping: config2.dataMapping
    }).then((res) => {
      if (res.success) {
        let result = res.result;
        let data = result.data || [];
        dataSource.value = JSON.parse(JSON.stringify(data));
        dataSource.value = pagesWork_components_common_echartUtil.dictTransform(dataSource.value, result.dictOptions);
        config2.dictOptions = result.dictOptions;
        getDataCallBack();
      } else {
        dataSource.value = [];
        toast.warning("查询失败");
      }
    });
  }
  function getDataCallBack() {
    dataSource.value = dataTransform(dataSource.value, config.dataMapping);
    handleData();
    console.log("getDataCallBack", dataSource.value);
    initOption && initOption(dataSource.value);
  }
  function handleData() {
    dataFilter();
    dataNumFilter();
  }
  function dataFilter() {
    if (config.dataFilter) {
      try {
        const func = new Function("data", config.dataFilter);
        dataSource.value = func(dataSource.value);
        console.info("过滤后的数据:", dataSource.value);
      } catch (e) {
        console.info("过滤器异常:", e);
      }
    }
  }
  function dataNumFilter() {
    let value = dataSource.value;
    if (value && pagesWork_common_is.isArray(value) && value.length > 0) {
      let totalNum = value.length;
      let dataNum = config.dataNum || 0;
      if (dataNum > 0 && dataNum < totalNum) {
        dataSource.value = dataSource.value.slice(0, dataNum);
      }
    }
  }
  function dataTransform(chartData, dataMapping) {
    if (dataMapping && Array.isArray(chartData)) {
      let newChartData = [];
      chartData.forEach((data) => {
        let obj = __spreadValues({}, data);
        try {
          dataMapping.forEach((item) => {
            var _a, _b;
            let value = item["mapping"] != null ? (_b = (_a = data[item["mapping"]]) != null ? _a : data[typeof item["mapping"] === "string" ? item["mapping"].toUpperCase() : null]) != null ? _b : null : null;
            pagesWork_components_common_concants.fieldMappings.forEach((field) => {
              if (item["filed"] == field["label"]) {
                if (["name", "type", "value"].includes(field["key"])) {
                  obj[field["key"]] = value || value == 0 ? value : "";
                } else {
                  obj[field["key"]] = value || value == 0 ? value : data[field["key"]] || data[field["key"].toUpperCase()];
                }
              }
            });
          });
        } catch (e) {
          console.info("转换异常:", e);
        }
        newChartData.push(obj);
      });
      console.info("转换后的数据:", newChartData);
      return newChartData;
    }
    return chartData;
  }
  function getParams(config2, params) {
    let queryParams = pagesWork_components_common_echartUtil.packageParams(config2, params);
    return {
      tableName: config2.tableName,
      compName: config2.compName,
      config: {
        type: config2.typeFields || [],
        name: config2.nameFields || [],
        value: config2.valueFields || [],
        assistValue: config2.assistYFields || [],
        assistType: config2.assistTypeFields || [],
        formType: config2.formType
      },
      condition: __spreadValues({}, queryParams)
    };
  }
  return [
    { dataSource, reload, pageTips, config, chartOption, mapDataJson },
    { queryData }
  ];
}
exports.useChartHook = useChartHook;
//# sourceMappingURL=useEchart.js.map
