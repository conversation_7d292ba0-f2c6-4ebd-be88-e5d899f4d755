"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _easycom_wd_img2 = common_vendor.resolveComponent("wd-img");
  const _easycom_wd_grid_item2 = common_vendor.resolveComponent("wd-grid-item");
  const _easycom_wd_grid2 = common_vendor.resolveComponent("wd-grid");
  (_easycom_wd_img2 + _easycom_wd_grid_item2 + _easycom_wd_grid2)();
}
const _easycom_wd_img = () => "../../node-modules/wot-design-uni/components/wd-img/wd-img.js";
const _easycom_wd_grid_item = () => "../../node-modules/wot-design-uni/components/wd-grid-item/wd-grid-item.js";
const _easycom_wd_grid = () => "../../node-modules/wot-design-uni/components/wd-grid/wd-grid.js";
if (!Math) {
  (_easycom_wd_img + _easycom_wd_grid_item + _easycom_wd_grid)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "Grid",
  options: {
    styleIsolation: "shared"
  }
}), {
  __name: "Grid",
  props: {
    modelValue: {
      type: Array,
      default: () => []
    },
    column: {
      type: Number,
      default: 4
    },
    itemKey: {
      type: String,
      default: "id"
    },
    imgWidth: {
      type: String,
      default: "28px"
    },
    imgHeight: {
      type: String,
      default: "28px"
    },
    clickable: {
      type: Boolean,
      default: true
    },
    borderColor: {
      type: String,
      default: "rgba(165, 165, 165, 0.1)"
    }
  },
  emits: ["itemClik"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const getClass = (index) => {
      let className = "";
      if (index < props.column) {
        className = "first-row";
      }
      if ((index + 1) % props.column == 0) {
        className += ` lastCol`;
      }
      return className;
    };
    const handleClik = (item, index) => {
      emit("itemClik", item, index);
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.f(__props.modelValue, (item, index, i0) => {
          return {
            a: "3b3d05c9-2-" + i0 + "," + ("3b3d05c9-1-" + i0),
            b: common_vendor.p({
              width: __props.imgWidth,
              height: __props.imgHeight,
              src: item.img
            }),
            c: common_vendor.o(($event) => handleClik(item, index), item[__props.itemKey]),
            d: "3b3d05c9-1-" + i0 + ",3b3d05c9-0",
            e: common_vendor.p({
              ["custom-class"]: getClass(index),
              ["use-icon-slot"]: true,
              text: item.text
            }),
            f: item[__props.itemKey]
          };
        }),
        b: common_vendor.p({
          clickable: __props.clickable,
          column: __props.column
        }),
        c: __props.borderColor,
        d: __props.imgWidth,
        e: __props.imgHeight
      };
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-3b3d05c9"]]);
wx.createComponent(Component);
//# sourceMappingURL=Grid.js.map
