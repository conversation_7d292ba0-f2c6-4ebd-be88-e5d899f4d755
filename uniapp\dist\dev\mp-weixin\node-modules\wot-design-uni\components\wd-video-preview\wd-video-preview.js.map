{"version": 3, "file": "wd-video-preview.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-video-preview/wd-video-preview.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC12aWRlby1wcmV2aWV3L3dkLXZpZGVvLXByZXZpZXcudnVl"], "sourcesContent": ["<template>\n  <view :class=\"`wd-video-preview ${customClass}`\" :style=\"customStyle\" v-if=\"showPopup\" @click=\"close\">\n    <view class=\"wd-video-preview__video\" @click.stop=\"\">\n      <video\n        class=\"wd-video-preview__video\"\n        v-if=\"previdewVideo.url\"\n        :controls=\"true\"\n        :poster=\"previdewVideo.poster\"\n        :title=\"previdewVideo.title\"\n        play-btn-position=\"center\"\n        :enableNative=\"true\"\n        :src=\"previdewVideo.url\"\n        :enable-progress-gesture=\"false\"\n      ></video>\n    </view>\n    <wd-icon name=\"close\" :custom-class=\"`wd-video-preview__close`\" @click=\"close\" />\n  </view>\n</template>\n\n<script lang=\"ts\">\nexport default {\n  name: 'wd-video-preview',\n  options: {\n    addGlobalClass: true,\n    virtualHost: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport wdIcon from '../wd-icon/wd-icon.vue'\nimport { nextTick, reactive, ref } from 'vue'\nimport { videoPreviewProps, type PreviewVideo, type VideoPreviewExpose } from './types'\nimport useLockScroll from '../composables/useLockScroll'\ndefineProps(videoPreviewProps)\n\nconst showPopup = ref<boolean>(false)\nconst previdewVideo = reactive<PreviewVideo>({ url: '', poster: '', title: '' })\n\nfunction open(video: PreviewVideo) {\n  showPopup.value = true\n  previdewVideo.url = video.url\n  previdewVideo.poster = video.poster\n  previdewVideo.title = video.title\n}\n\nfunction close() {\n  showPopup.value = false\n  nextTick(() => {\n    handleClosed()\n  })\n}\n\nfunction handleClosed() {\n  previdewVideo.url = ''\n  previdewVideo.poster = ''\n  previdewVideo.title = ''\n}\n\n\n\n\n\ndefineExpose<VideoPreviewExpose>({\n  open,\n  close\n})\n</script>\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-video-preview/wd-video-preview.vue'\nwx.createComponent(Component)"], "names": ["ref", "reactive", "nextTick"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AA+BA,MAAA,SAAmB,MAAA;AAXnB,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,gBAAgB;AAAA,EAAA;AAEpB;;;;AAUM,UAAA,YAAYA,kBAAa,KAAK;AAC9B,UAAA,gBAAgBC,uBAAuB,EAAE,KAAK,IAAI,QAAQ,IAAI,OAAO,IAAI;AAE/E,aAAS,KAAK,OAAqB;AACjC,gBAAU,QAAQ;AAClB,oBAAc,MAAM,MAAM;AAC1B,oBAAc,SAAS,MAAM;AAC7B,oBAAc,QAAQ,MAAM;AAAA,IAAA;AAG9B,aAAS,QAAQ;AACf,gBAAU,QAAQ;AAClBC,oBAAAA,WAAS,MAAM;AACA,qBAAA;AAAA,MAAA,CACd;AAAA,IAAA;AAGH,aAAS,eAAe;AACtB,oBAAc,MAAM;AACpB,oBAAc,SAAS;AACvB,oBAAc,QAAQ;AAAA,IAAA;AAOS,aAAA;AAAA,MAC/B;AAAA,MACA;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;AClED,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}