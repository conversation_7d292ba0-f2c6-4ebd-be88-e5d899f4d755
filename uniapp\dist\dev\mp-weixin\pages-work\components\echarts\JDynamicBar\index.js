"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
const common_vendor = require("../../../../common/vendor.js");
const pagesWork_components_echarts_props = require("../props.js");
const pagesWork_components_hooks_useEchart = require("../../hooks/useEchart.js");
const uni_modules_daTree_utils = require("../../../../uni_modules/da-tree/utils.js");
if (!Math) {
  (statusTip + echartsUniapp)();
}
const echartsUniapp = () => "../index.js";
const statusTip = () => "../../statusTip.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  props: __spreadValues({}, pagesWork_components_echarts_props.echartProps),
  setup(__props) {
    const props = __props;
    const option = common_vendor.ref({});
    let chartOption = {
      title: {
        show: true
      },
      legend: {
        show: true,
        data: []
      },
      xAxis: {
        max: "dataMax",
        type: "value"
      },
      yAxis: {
        type: "category",
        inverse: false,
        animationDuration: 300,
        animationDurationUpdate: 300,
        nameTextStyle: {
          align: "right"
        },
        axisLine: {
          show: true
        }
      },
      series: [],
      dataset: {
        dimensions: [],
        source: []
      },
      animationDuration: 0,
      animationDurationUpdate: 2e3,
      animationEasing: "linear",
      animationEasingUpdate: "linear"
    };
    let [{ dataSource, reload, pageTips, config }, { queryData }] = pagesWork_components_hooks_useEchart.useChartHook(
      props,
      initOption
    );
    function initOption(data) {
      var _a, _b, _c;
      let chartData = dataSource.value;
      if (typeof chartData === "string") {
        chartData = JSON.parse(chartData);
      }
      if (chartData && chartData.length > 0) {
        let configOption = props.config.option;
        let dataset = getDataSet(chartData);
        chartOption.dataset = dataset;
        chartOption.series = [];
        dataset.dimensions.forEach((series, index) => {
          if (index > 0) {
            chartOption.series.push({
              realtimeSort: true,
              type: "bar",
              //TODO 自定义图表类型
              color: "",
              //TODO 自定义颜色
              series,
              //TODO 系列，冗余数据，只是table展示使用
              label: {
                show: true,
                position: "right",
                valueAnimation: true
              }
            });
          }
        });
        chartOption.legend.data = chartOption.series.map((item) => item.series).filter((type) => type);
        chartOption.yAxis.type = common_vendor.pull(["category", "value"], (_a = configOption == null ? void 0 : configOption.xAxis) == null ? void 0 : _a.type)[0];
        if (props.config && config.option) {
          common_vendor.merge(chartOption, config.option);
          synchSeries(chartOption, (_c = (_b = props == null ? void 0 : props.config) == null ? void 0 : _b.option) == null ? void 0 : _c.series);
          option.value = uni_modules_daTree_utils.deepClone(chartOption);
          pageTips.show = false;
        }
      } else {
        pageTips.status = 1;
        pageTips.show = true;
      }
    }
    function synchSeries(chartOption2, series) {
      var _a, _b, _c, _d, _e;
      if (((_a = chartOption2 == null ? void 0 : chartOption2.series) == null ? void 0 : _a.length) && (series == null ? void 0 : series.length)) {
        const _series = common_vendor.cloneDeep(series);
        if ((_c = (_b = _series[0]) == null ? void 0 : _b.itemStyle) == null ? void 0 : _c.color) {
          (_e = (_d = _series[0]) == null ? void 0 : _d.itemStyle) == null ? true : delete _e.color;
        }
        chartOption2.series.forEach(() => {
          _series.push(_series[0]);
        });
        common_vendor.merge(chartOption2, { series: _series });
      }
    }
    function getDataSet(chartData) {
      let dataObj = { dimensions: [], source: [] };
      let dataList = [];
      let seriesArr = chartData.map((item) => item["type"]);
      let dimensions = seriesArr && seriesArr.length > 0 ? ["dynamic", ...new Set(seriesArr)] : [];
      let nameArr = [...new Set(chartData.map((item) => item["name"]))];
      nameArr.forEach((name, index) => {
        let arr = chartData.filter((item) => item["name"] == name);
        let valueList = arr.map((item) => item["value"]);
        valueList.unshift(name);
        dataList.push(valueList);
      });
      dataObj.dimensions = dimensions;
      dataObj.source = dataList;
      return dataObj;
    }
    common_vendor.onMounted(() => {
      queryData();
    });
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.unref(pageTips).show
      }, common_vendor.unref(pageTips).show ? {
        b: common_vendor.p({
          status: common_vendor.unref(pageTips).status
        })
      } : {
        c: common_vendor.p({
          option: common_vendor.unref(option)
        })
      });
    };
  }
});
wx.createComponent(_sfc_main);
//# sourceMappingURL=index.js.map
