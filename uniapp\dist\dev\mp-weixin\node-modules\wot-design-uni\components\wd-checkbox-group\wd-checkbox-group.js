"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../../common/vendor.js");
const __default__ = {
  name: "wd-checkbox-group",
  options: {
    addGlobalClass: true,
    virtualHost: true,
    styleIsolation: "shared"
  }
};
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, __default__), {
  props: common_vendor.checkboxGroupProps,
  emits: ["change", "update:modelValue"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const { linkChildren } = common_vendor.useChildren(common_vendor.CHECKBOX_GROUP_KEY);
    linkChildren({ props, changeSelectState });
    common_vendor.watch(
      () => props.modelValue,
      (newValue) => {
        if (new Set(newValue).size !== newValue.length) {
          console.error("checkboxGroup's bound value includes same value");
        }
        if (newValue.length < props.min) {
          console.error("checkboxGroup's bound value's length can't be less than min");
        }
        if (props.max !== 0 && newValue.length > props.max) {
          console.error("checkboxGroup's bound value's length can't be large than max");
        }
      },
      { deep: true, immediate: true }
    );
    common_vendor.watch(
      () => props.shape,
      (newValue) => {
        const type = ["circle", "square", "button"];
        if (type.indexOf(newValue) === -1)
          console.error(`shape must be one of ${type.toString()}`);
      },
      { deep: true, immediate: true }
    );
    common_vendor.watch(
      () => props.min,
      (newValue) => {
        common_vendor.checkNumRange(newValue, "min");
      },
      { deep: true, immediate: true }
    );
    common_vendor.watch(
      () => props.max,
      (newValue) => {
        common_vendor.checkNumRange(newValue, "max");
      },
      { deep: true, immediate: true }
    );
    function changeSelectState(value) {
      const temp = common_vendor.deepClone(props.modelValue);
      const index = temp.indexOf(value);
      if (index > -1) {
        temp.splice(index, 1);
      } else {
        temp.push(value);
      }
      emit("update:modelValue", temp);
      emit("change", {
        value: temp
      });
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.n(`wd-checkbox-group ${_ctx.shape === "button" && _ctx.cell ? "is-button" : ""} ${_ctx.customClass}`),
        b: common_vendor.s(_ctx.customStyle)
      };
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-0fec63df"]]);
wx.createComponent(Component);
//# sourceMappingURL=wd-checkbox-group.js.map
