"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  props: {
    urls: {
      type: Array,
      required: true,
      default: () => {
        return [];
      }
    }
  },
  data() {
    return {
      show: false,
      current: 0,
      //当前页
      scale: 1,
      isZooming: false
      // 是否处于缩放状态
    };
  },
  methods: {
    open(current) {
      this.current = this.urls.findIndex((item) => item === current);
    },
    //关闭
    close() {
      if (!this.isZooming) {
        this.show = false;
        this.current = 0;
        this.$emit("close");
      }
    },
    //图片改变
    swiperChange(e) {
      this.current = e.detail.current;
    },
    //监听长按
    onLongpress(e) {
      this.$emit("onLongpress", e);
    },
    handleTouchStart() {
      this.isZooming = true;
    },
    handleTouchEnd() {
      this.isZooming = false;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e$1({
    a: $props.urls.length > 0
  }, $props.urls.length > 0 ? {
    b: common_vendor.t($data.current + 1),
    c: common_vendor.t($props.urls.length)
  } : {}, {
    d: common_vendor.f($props.urls, (item, index, i0) => {
      return {
        a: index,
        b: item,
        c: common_vendor.o(($event) => $options.onLongpress(item), index),
        d: index
      };
    }),
    e: $data.scale,
    f: $data.current,
    g: common_vendor.o((...args) => $options.swiperChange && $options.swiperChange(...args)),
    h: common_vendor.o((...args) => $options.handleTouchStart && $options.handleTouchStart(...args)),
    i: common_vendor.o((...args) => $options.handleTouchEnd && $options.handleTouchEnd(...args)),
    j: common_vendor.o((...args) => $options.close && $options.close(...args))
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-788ba943"]]);
wx.createComponent(Component);
//# sourceMappingURL=ImgPreview.js.map
