"use strict";
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _easycom_wd_text2 = common_vendor.resolveComponent("wd-text");
  const _easycom_wd_radio2 = common_vendor.resolveComponent("wd-radio");
  const _easycom_wd_cell2 = common_vendor.resolveComponent("wd-cell");
  const _easycom_wd_radio_group2 = common_vendor.resolveComponent("wd-radio-group");
  const _easycom_wd_cell_group2 = common_vendor.resolveComponent("wd-cell-group");
  const _easycom_wd_popup2 = common_vendor.resolveComponent("wd-popup");
  (_easycom_wd_text2 + _easycom_wd_radio2 + _easycom_wd_cell2 + _easycom_wd_radio_group2 + _easycom_wd_cell_group2 + _easycom_wd_popup2)();
}
const _easycom_wd_text = () => "../../node-modules/wot-design-uni/components/wd-text/wd-text.js";
const _easycom_wd_radio = () => "../../node-modules/wot-design-uni/components/wd-radio/wd-radio.js";
const _easycom_wd_cell = () => "../../node-modules/wot-design-uni/components/wd-cell/wd-cell.js";
const _easycom_wd_radio_group = () => "../../node-modules/wot-design-uni/components/wd-radio-group/wd-radio-group.js";
const _easycom_wd_cell_group = () => "../../node-modules/wot-design-uni/components/wd-cell-group/wd-cell-group.js";
const _easycom_wd_popup = () => "../../node-modules/wot-design-uni/components/wd-popup/wd-popup.js";
if (!Math) {
  (_easycom_wd_text + _easycom_wd_radio + _easycom_wd_cell + _easycom_wd_radio_group + _easycom_wd_cell_group + _easycom_wd_popup)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "RightConditionFilter",
  props: ["title", "data", "options", "checked"],
  emits: ["change", "close"],
  setup(__props, { emit: __emit }) {
    const eimt = __emit;
    const show = common_vendor.ref(true);
    const props = __props;
    const checked = common_vendor.ref(props.checked);
    const handleClose = () => {
      show.value = false;
      setTimeout(() => {
        eimt("close");
      }, 300);
    };
    const handleSelected = (item) => {
      checked.value = item.key;
      eimt("change", { option: item, data: props.data });
      handleClose();
    };
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: __props.title
      }, __props.title ? {
        b: common_vendor.p({
          text: __props.title
        })
      } : {}, {
        c: common_vendor.f(__props.options, (item, index, i0) => {
          return {
            a: "d984277f-5-" + i0 + "," + ("d984277f-4-" + i0),
            b: common_vendor.p({
              value: item.key
            }),
            c: common_vendor.o(($event) => handleSelected(item)),
            d: "d984277f-4-" + i0 + ",d984277f-3",
            e: common_vendor.p({
              title: item.title,
              clickable: true
            })
          };
        }),
        d: common_vendor.o(($event) => checked.value = $event),
        e: common_vendor.p({
          modelValue: checked.value
        }),
        f: common_vendor.p({
          border: true
        }),
        g: common_vendor.o(handleClose),
        h: common_vendor.o(($event) => show.value = $event),
        i: common_vendor.p({
          position: "right",
          modelValue: show.value
        })
      });
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d984277f"]]);
wx.createComponent(Component);
//# sourceMappingURL=RightConditionFilter.js.map
