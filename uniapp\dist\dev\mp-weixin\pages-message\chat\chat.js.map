{"version": 3, "file": "chat.js", "sources": ["../../../../../src/pages-message/chat/chat.vue", "../../../../../uniPage:/cGFnZXMtbWVzc2FnZVxjaGF0XGNoYXQudnVl"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n  <PageLayout :navTitle=\"navTitle\" backRouteName=\"message\" routeMethod=\"pushTab\">\r\n    <view class=\"wrap\">\r\n      <!-- prettier-ignore -->\r\n      <z-paging ref=\"paging\" v-model=\"dataList\" :fixed=\"false\" use-chat-record-mode use-virtual-list cell-height-mode=\"dynamic\" safe-area-inset-bottom bottom-bg-color=\"#e5e5e5\" @query=\"queryList\" @keyboardHeightChange=\"keyboardHeightChange\" @hidedKeyboard=\"hidedKeyboard\">\r\n        <template #cell=\"{item,index}\">\r\n          <view style=\"transform: scaleY(-1)\">\r\n            <chat-item :item=\"item\" :playMsgid=\"playMsgid\" @playVoice=\"handlePlayVoice\"></chat-item>\r\n          </view>\r\n        </template>\r\n        <template #bottom>\r\n          <chat-input-bar ref=\"inputBar\" @send=\"doSend\" @image=\"handleImage\" />\r\n        </template>\r\n      </z-paging>\r\n    </view>\r\n  </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\n//\r\nimport { onLaunch, onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'\r\nimport { nextTick, onMounted, ref } from 'vue'\r\nimport { useUserStore } from '@/store/user'\r\nimport { http } from '@/utils/http'\r\nimport { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'\r\nimport { useRouter } from '@/plugin/uni-mini-router'\r\nimport { cache, getFileAccessHttpUrl, hasRoute, formatDate } from '@/common/uitls'\r\nimport { TENANT_LIST } from '@/common/constants'\r\nimport socket from '../common/socket'\r\nimport { textReplaceEmoji, getEmojiImageUrl } from './emojis'\r\nimport chatInputBar from './components/chat-input-bar.vue'\r\nimport chatItem from './components/chat-item.vue'\r\nimport { getEnvBaseUrl } from '@/utils/index'\r\nimport { useParamsStore } from '@/store/page-params'\r\n\r\ndefineOptions({\r\n  name: 'chat',\r\n  options: {\r\n    // apply-shared‌：当前页面样式会影响到子组件样式.(小程序)\r\n    // shared‌：当前页面样式影响到子组件，子组件样式也会影响到当前页面.(小程序)\r\n    styleIsolation: '‌apply-shared‌',\r\n  },\r\n})\r\n\r\nconst api = {\r\n  chatlog_old: '/eoa/im/api/queryChatLogList',\r\n  chatlog: '/eoa/im/newApi/records',\r\n  sendMsg: '/eoa/im/newApi/sendMessage',\r\n  sendFile: '/eoa/im/newApi/sendFile',\r\n  creatFriendSession: '/eoa/im/newApi/creatFriendSession',\r\n  uploadUrl: `${getEnvBaseUrl()}/eoa/im/newApi/sendImage`,\r\n}\r\n\r\nconst toast = useToast()\r\nconst userStore = useUserStore()\r\nconst paging = ref(null)\r\n\r\n// 聊天对方用户信息\r\nconst chatObj = ref(null)\r\nconst navTitle = ref('')\r\n// 对方userid\r\nconst chatto = ref()\r\nconst myuid = ref(userStore.userInfo.userid)\r\nconst msgList = ref([])\r\n// const pageNo = ref(1)\r\n// const pageSize = ref(10)\r\nconst loadingShow = ref(false)\r\nconst hasRecord = ref(false)\r\nconst dataList = ref([])\r\nconst inputBar = ref(null)\r\nconst AUDIO = uni.createInnerAudioContext()\r\nconst playMsgid = ref('')\r\nlet stopWatch: any = null\r\nconst paramsStore = useParamsStore()\r\n\r\n// 页面初始化\r\nconst init = () => {\r\n  const localData = paramsStore.getPageParams('chat')\r\n  const params = localData.data\r\n  if (!params) {\r\n    return\r\n  }\r\n  chatObj.value = { ...params }\r\n  navTitle.value = params.fromUserName\r\n  chatto.value = chatObj.value.msgTo\r\n  creatFriendSession(chatObj.value.msgTo)\r\n  onSocketOpen()\r\n  onSocketReceive()\r\n  getMsgList()\r\n}\r\n// 创建会话数据\r\nconst creatFriendSession = (userId) => {\r\n  http.post(api.creatFriendSession, {\r\n    type: 'friend',\r\n    userId,\r\n  })\r\n}\r\nconst onSocketOpen = () => {\r\n  console.log('启动webSocket')\r\n  socket.init('eoaNewChatSocket')\r\n}\r\nconst onSocketReceive = () => {\r\n  var _this = this\r\n  socket.acceptMessage = function (res) {\r\n    console.log('页面收到的消息=====》', res)\r\n    if (res.event == 'event_talk_revoke') {\r\n      // 撤回了消息\r\n      removeMsg(res)\r\n    } else {\r\n      if (res.type == 'friend') {\r\n        //聊天消息\r\n        screenMsg(res)\r\n        unreadClear()\r\n      }\r\n    }\r\n  }\r\n}\r\nconst removeMsg = (data) => {\r\n  let arr = msgList.value.filter((item) => item.id != data.id)\r\n  msgList.value = arr\r\n}\r\nconst screenMsg = (msg) => {\r\n  //消息处理\r\n  if (msg.msgFrom == chatto.value && msg.msgTo == myuid.value) {\r\n    console.log('用户消息')\r\n    let time = formatDate(msg.sendTime, 'yyyy-MM-dd hh:mm:ss')\r\n    let id = time.replace(/\\:/g, '').replace(/\\-/g, '').replace(' ', '')\r\n    let content = msg.msgData\r\n    if (msg.msgType == 'text') {\r\n      content = replaceEmoji(content)\r\n    }\r\n    if (msg.msgType == 'voice') {\r\n      content = JSON.parse(content)\r\n    }\r\n    msgList.value.push({\r\n      fromUserName: msg.fromUserName,\r\n      msgTo: msg.msgTo,\r\n      msgFrom: msg.msgFrom,\r\n      msgData: content,\r\n      fromAvatar: msg.fromAvatar,\r\n      sendTime: time,\r\n      msgType: msg.msgType,\r\n      sendTimeId: id,\r\n      fileName: msg.fileName,\r\n      id: msg.id,\r\n    })\r\n    //非自己的消息震动\r\n    if (msg.msgFrom != myuid.value) {\r\n      console.log('振动')\r\n      uni.vibrateLong()\r\n    }\r\n    // this.$nextTick(function () {\r\n    //   // 滚动到底\r\n    //   this.scrollToView = 'msg' + id\r\n    // })\r\n  }\r\n}\r\n//替换表情符号为图片\r\nconst replaceEmoji = (str) => {\r\n  let temp = textReplaceEmoji(str)\r\n  return '<div style=\"display:inline-block\">' + temp + '</div>'\r\n}\r\n\r\nconst queryList = (pageNo, pageSize) => {\r\n  //数据库查询消息列表\r\n  let params = {\r\n    type: 'friend',\r\n    pageNo: pageNo,\r\n    pageSize: pageSize,\r\n    msgTo: chatto.value,\r\n    id: myuid.value,\r\n    sort: 'DESC',\r\n  }\r\n  console.log('params', params)\r\n  http\r\n    .get(api.chatlog, params)\r\n    .then((res: any) => {\r\n      if (res.success && res.result?.records) {\r\n        const records = analysis(res.result.records)\r\n        paging.value.complete(records)\r\n      } else {\r\n        paging.value.complete(false)\r\n      }\r\n    })\r\n    .catch((res) => {\r\n      paging.value.complete(false)\r\n    })\r\n}\r\nconst analysis = (data) => {\r\n  let arr = data\r\n  if (arr.length > 0) {\r\n    let list = arr.map((item) => {\r\n      let id = item.sendTime.replace(/\\:/g, '').replace(/\\-/g, '').replace(' ', '')\r\n      item.sendTimeId = id\r\n      let content = item.msgData\r\n      if (item.msgType == 'text') {\r\n        content = replaceEmoji(content)\r\n      }\r\n      if (item.msgType == 'voice') {\r\n        content = JSON.parse(content)\r\n      }\r\n      item.msgData = content\r\n      return item\r\n    })\r\n    for (let i = 0; i < list.length; i++) {\r\n      if (list[i].msgType == 'revoke') {\r\n        continue\r\n      }\r\n      if (list[i].referenceMsgId) {\r\n        list[i] = handleReplyMsg(list[i], list)\r\n      }\r\n    }\r\n  }\r\n  return data\r\n}\r\n\r\n// 加载初始页面消息\r\nconst getMsgList = () => {}\r\nconst handleReplyMsg = (item, list) => {\r\n  let tempId = item.referenceMsgId\r\n  item.reply = true\r\n  let replyContent = ''\r\n  for (let i = 0; i < list.length; i++) {\r\n    if (list[i].id == tempId) {\r\n      replyContent = '\"' + list[i].fromUserName + ':' + list[i].msgData + '\"'\r\n      break\r\n    }\r\n  }\r\n  item.replyContent = replyContent\r\n  return item\r\n}\r\nconst unreadClear = () => {\r\n  http\r\n    .post('/eoa/im/newApi/unreadClear', {\r\n      type: chatObj.value.type,\r\n      msgTo: chatObj.value.msgTo,\r\n      msgFrom: chatObj.value.msgFrom,\r\n    })\r\n    .then((res: any) => {\r\n      if (res.success) {\r\n        // _this.eventChannel.emit('toPrePageData', { data: 'data from chat page' })\r\n      }\r\n    })\r\n}\r\n// 播放语音\r\nconst handlePlayVoice = (item) => {\r\n  if (item.id == playMsgid.value) {\r\n    AUDIO.stop()\r\n    playMsgid.value = ''\r\n  } else {\r\n    playMsgid.value = item.id\r\n    AUDIO.src = item.msgData.url\r\n    nextTick(function () {\r\n      AUDIO.play()\r\n    })\r\n  }\r\n}\r\n// 播放语音结束\r\nAUDIO.onEnded((res) => {\r\n  playMsgid.value = ''\r\n})\r\n\r\n// 监听键盘高度改变，请不要直接通过uni.onKeyboardHeightChange监听，否则可能导致z-paging内置的键盘高度改变监听失效（如果不需要切换表情面板则不用写）\r\nconst keyboardHeightChange = (res) => {\r\n  inputBar.value.updateKeyboardHeightChange(res)\r\n}\r\n// 用户尝试隐藏键盘，此时如果表情面板在展示中，应当通知chatInputBar隐藏表情面板（如果不需要切换表情面板则不用写）\r\nconst hidedKeyboard = () => {\r\n  inputBar.value.hidedKeyboard()\r\n}\r\nconst doSend = (textMsg) => {\r\n  // paging.value.addChatRecordData([\r\n  //   {\r\n  //     time: '',\r\n  //     icon: '/static/daxiong.jpg',\r\n  //     name: '大雄',\r\n  //     content: msg,\r\n  //     isMe: true,\r\n  //   },\r\n  // ])\r\n\r\n  let content = replaceEmoji(textMsg)\r\n  //let msg = {'text':content}\r\n  //content = (content||'').replace(/&(?!#?[a-zA-Z0-9]+;)/g, '&amp;')\r\n  console.log('content', content)\r\n  let msg = textMsg\r\n  let time = formatDate(new Date().getTime(), 'yyyy-MM-dd hh:mm:ss')\r\n  let id = time.replace(/\\:/g, '').replace(/\\-/g, '').replace(' ', '')\r\n  //发送\r\n  sendMsg(msg, 'text')\r\n  paging.value.addChatRecordData(\r\n    analysis([\r\n      {\r\n        fromUserName: userStore.userInfo.realname,\r\n        msgTo: chatto.value,\r\n        msgFrom: myuid.value,\r\n        msgData: content,\r\n        fromAvatar: userStore.userInfo.avatar,\r\n        sendTime: time,\r\n        sendTimeId: id,\r\n        msgType: 'text',\r\n      },\r\n    ]),\r\n  )\r\n}\r\n\r\nconst sendMsg = (content, type) => {\r\n  //实际应用中，此处应该提交长连接，模板仅做本地处理。\r\n  var nowDate = new Date()\r\n  // 发送消息\r\n  var obj = {\r\n    mine: {\r\n      avatar: userStore.userInfo.avatar,\r\n      content: content,\r\n      id: myuid.value,\r\n      mine: true,\r\n      username: userStore.userInfo.username,\r\n    },\r\n    to: {\r\n      avatar: chatObj.value.avatar,\r\n      id: chatObj.value.msgTo,\r\n      type: 'friend',\r\n      username: chatObj.value.username,\r\n    },\r\n  }\r\n\r\n  let sendData = {\r\n    type: 'chatMessage',\r\n    data: obj,\r\n  }\r\n  console.log('sendData======>', sendData)\r\n  let params = {\r\n    type: 'friend',\r\n    msgTo: chatObj.value.msgTo,\r\n    text: content,\r\n    msgType: 'text',\r\n  }\r\n  http.post(api.sendMsg, params).then((res: any) => {\r\n    console.log('消息发送结果：', res)\r\n    if (!res.success) {\r\n      toast.error(res.message)\r\n    }\r\n  })\r\n}\r\n\r\nconst handleImage = (type) => {\r\n  let time = formatDate(new Date().getTime(), 'yyyy-MM-dd hh:mm:ss')\r\n  let id = time.replace(/\\:/g, '').replace(/\\-/g, '').replace(' ', '')\r\n  let formData = {\r\n    type: 'friend',\r\n    msgTo: chatto.value,\r\n    fileId: id,\r\n    msgType: 'images',\r\n    fileName: '',\r\n  }\r\n  const { loading, data, error, run } = useUpload(\r\n    { ...formData, name: 'image' },\r\n    { url: api.uploadUrl, sourceType: [type] },\r\n  )\r\n  if (stopWatch) stopWatch()\r\n  run()\r\n  stopWatch = watch(\r\n    () => [loading.value, error.value, data.value],\r\n    ([loading, err, data], oldValue) => {\r\n      if (loading == false) {\r\n        if (err) {\r\n          toast.warning('修改失败')\r\n          uni.hideLoading()\r\n        } else {\r\n          if (data) {\r\n            console.log('data::', data)\r\n          }\r\n        }\r\n      }\r\n    },\r\n  )\r\n}\r\ninit()\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n//\r\n.wrap {\r\n  height: 100%;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-message/chat/chat.vue'\nwx.createPage(MiniProgramPage)"], "names": ["getEnvBaseUrl", "useToast", "useUserStore", "ref", "uni", "useParamsStore", "http", "socket", "formatDate", "textReplaceEmoji", "nextTick", "useUpload", "watch", "loading", "data"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,MAAA,eAAyB,MAAA;AACzB,MAAA,WAAqB,MAAA;;;;;;;;;;;AAarB,UAAM,MAAM;AAAA,MAEV,SAAS;AAAA,MACT,SAAS;AAAA,MAET,oBAAoB;AAAA,MACpB,WAAW,GAAGA,YAAA,cAAA,CAAe;AAAA,IAC/B;AAEA,UAAM,QAAQC,cAAAA,SAAS;AACvB,UAAM,YAAYC,WAAAA,aAAa;AACzB,UAAA,SAASC,kBAAI,IAAI;AAGjB,UAAA,UAAUA,kBAAI,IAAI;AAClB,UAAA,WAAWA,kBAAI,EAAE;AAEvB,UAAM,SAASA,cAAAA,IAAI;AACnB,UAAM,QAAQA,cAAA,IAAI,UAAU,SAAS,MAAM;AACrC,UAAA,UAAUA,cAAI,IAAA,EAAE;AAGFA,kBAAAA,IAAI,KAAK;AACXA,kBAAAA,IAAI,KAAK;AACrB,UAAA,WAAWA,cAAI,IAAA,EAAE;AACjB,UAAA,WAAWA,kBAAI,IAAI;AACnB,UAAA,QAAQC,oBAAI,wBAAwB;AACpC,UAAA,YAAYD,kBAAI,EAAE;AACxB,QAAI,YAAiB;AACrB,UAAM,cAAcE,iBAAAA,eAAe;AAGnC,UAAM,OAAO,MAAM;AACX,YAAA,YAAY,YAAY,cAAc,MAAM;AAClD,YAAM,SAAS,UAAU;AACzB,UAAI,CAAC,QAAQ;AACX;AAAA,MAAA;AAEM,cAAA,QAAQ,mBAAK;AACrB,eAAS,QAAQ,OAAO;AACjB,aAAA,QAAQ,QAAQ,MAAM;AACV,yBAAA,QAAQ,MAAM,KAAK;AACzB,mBAAA;AACG,sBAAA;AAAA,IAElB;AAEM,UAAA,qBAAqB,CAAC,WAAW;AAChCC,sBAAA,KAAK,IAAI,oBAAoB;AAAA,QAChC,MAAM;AAAA,QACN;AAAA,MAAA,CACD;AAAA,IACH;AACA,UAAM,eAAe,MAAM;AACzB,cAAQ,IAAI,aAAa;AACzBC,iCAAA,SAAO,KAAK,kBAAkB;AAAA,IAChC;AACA,UAAM,kBAAkB,MAAM;AAErBA,0CAAA,gBAAgB,SAAU,KAAK;AAC5B,gBAAA,IAAI,iBAAiB,GAAG;AAC5B,YAAA,IAAI,SAAS,qBAAqB;AAEpC,oBAAU,GAAG;AAAA,QAAA,OACR;AACD,cAAA,IAAI,QAAQ,UAAU;AAExB,sBAAU,GAAG;AACD,wBAAA;AAAA,UAAA;AAAA,QACd;AAAA,MAEJ;AAAA,IACF;AACM,UAAA,YAAY,CAAC,SAAS;AACtB,UAAA,MAAM,QAAQ,MAAM,OAAO,CAAC,SAAS,KAAK,MAAM,KAAK,EAAE;AAC3D,cAAQ,QAAQ;AAAA,IAClB;AACM,UAAA,YAAY,CAAC,QAAQ;AAEzB,UAAI,IAAI,WAAW,OAAO,SAAS,IAAI,SAAS,MAAM,OAAO;AAC3D,gBAAQ,IAAI,MAAM;AAClB,YAAI,OAAOC,aAAA,WAAW,IAAI,UAAU,qBAAqB;AACzD,YAAI,KAAK,KAAK,QAAQ,OAAO,EAAE,EAAE,QAAQ,OAAO,EAAE,EAAE,QAAQ,KAAK,EAAE;AACnE,YAAI,UAAU,IAAI;AACd,YAAA,IAAI,WAAW,QAAQ;AACzB,oBAAU,aAAa,OAAO;AAAA,QAAA;AAE5B,YAAA,IAAI,WAAW,SAAS;AAChB,oBAAA,KAAK,MAAM,OAAO;AAAA,QAAA;AAE9B,gBAAQ,MAAM,KAAK;AAAA,UACjB,cAAc,IAAI;AAAA,UAClB,OAAO,IAAI;AAAA,UACX,SAAS,IAAI;AAAA,UACb,SAAS;AAAA,UACT,YAAY,IAAI;AAAA,UAChB,UAAU;AAAA,UACV,SAAS,IAAI;AAAA,UACb,YAAY;AAAA,UACZ,UAAU,IAAI;AAAA,UACd,IAAI,IAAI;AAAA,QAAA,CACT;AAEG,YAAA,IAAI,WAAW,MAAM,OAAO;AAC9B,kBAAQ,IAAI,IAAI;AAChBJ,wBAAAA,MAAI,YAAY;AAAA,QAAA;AAAA,MAClB;AAAA,IAMJ;AAEM,UAAA,eAAe,CAAC,QAAQ;AACxB,UAAA,OAAOK,0CAAiB,GAAG;AAC/B,aAAO,uCAAuC,OAAO;AAAA,IACvD;AAEM,UAAA,YAAY,CAAC,QAAQ,aAAa;AAEtC,UAAI,SAAS;AAAA,QACX,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,OAAO,OAAO;AAAA,QACd,IAAI,MAAM;AAAA,QACV,MAAM;AAAA,MACR;AACQ,cAAA,IAAI,UAAU,MAAM;AAC5BH,iBAAA,KACG,IAAI,IAAI,SAAS,MAAM,EACvB,KAAK,CAAC,QAAa;;AAClB,YAAI,IAAI,aAAW,SAAI,WAAJ,mBAAY,UAAS;AACtC,gBAAM,UAAU,SAAS,IAAI,OAAO,OAAO;AACpC,iBAAA,MAAM,SAAS,OAAO;AAAA,QAAA,OACxB;AACE,iBAAA,MAAM,SAAS,KAAK;AAAA,QAAA;AAAA,MAC7B,CACD,EACA,MAAM,CAAC,QAAQ;AACP,eAAA,MAAM,SAAS,KAAK;AAAA,MAAA,CAC5B;AAAA,IACL;AACM,UAAA,WAAW,CAAC,SAAS;AACzB,UAAI,MAAM;AACN,UAAA,IAAI,SAAS,GAAG;AAClB,YAAI,OAAO,IAAI,IAAI,CAAC,SAAS;AAC3B,cAAI,KAAK,KAAK,SAAS,QAAQ,OAAO,EAAE,EAAE,QAAQ,OAAO,EAAE,EAAE,QAAQ,KAAK,EAAE;AAC5E,eAAK,aAAa;AAClB,cAAI,UAAU,KAAK;AACf,cAAA,KAAK,WAAW,QAAQ;AAC1B,sBAAU,aAAa,OAAO;AAAA,UAAA;AAE5B,cAAA,KAAK,WAAW,SAAS;AACjB,sBAAA,KAAK,MAAM,OAAO;AAAA,UAAA;AAE9B,eAAK,UAAU;AACR,iBAAA;AAAA,QAAA,CACR;AACD,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAI,KAAK,CAAC,EAAE,WAAW,UAAU;AAC/B;AAAA,UAAA;AAEE,cAAA,KAAK,CAAC,EAAE,gBAAgB;AAC1B,iBAAK,CAAC,IAAI,eAAe,KAAK,CAAC,GAAG,IAAI;AAAA,UAAA;AAAA,QACxC;AAAA,MACF;AAEK,aAAA;AAAA,IACT;AAIM,UAAA,iBAAiB,CAAC,MAAM,SAAS;AACrC,UAAI,SAAS,KAAK;AAClB,WAAK,QAAQ;AACb,UAAI,eAAe;AACnB,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,YAAI,KAAK,CAAC,EAAE,MAAM,QAAQ;AACT,yBAAA,MAAM,KAAK,CAAC,EAAE,eAAe,MAAM,KAAK,CAAC,EAAE,UAAU;AACpE;AAAA,QAAA;AAAA,MACF;AAEF,WAAK,eAAe;AACb,aAAA;AAAA,IACT;AACA,UAAM,cAAc,MAAM;AACxBA,iBAAA,KACG,KAAK,8BAA8B;AAAA,QAClC,MAAM,QAAQ,MAAM;AAAA,QACpB,OAAO,QAAQ,MAAM;AAAA,QACrB,SAAS,QAAQ,MAAM;AAAA,MAAA,CACxB,EACA,KAAK,CAAC,QAAa;AAClB,YAAI,IAAI;AAAS;AAAA,MAEjB,CACD;AAAA,IACL;AAEM,UAAA,kBAAkB,CAAC,SAAS;AAC5B,UAAA,KAAK,MAAM,UAAU,OAAO;AAC9B,cAAM,KAAK;AACX,kBAAU,QAAQ;AAAA,MAAA,OACb;AACL,kBAAU,QAAQ,KAAK;AACjB,cAAA,MAAM,KAAK,QAAQ;AACzBI,sBAAAA,WAAS,WAAY;AACnB,gBAAM,KAAK;AAAA,QAAA,CACZ;AAAA,MAAA;AAAA,IAEL;AAEM,UAAA,QAAQ,CAAC,QAAQ;AACrB,gBAAU,QAAQ;AAAA,IAAA,CACnB;AAGK,UAAA,uBAAuB,CAAC,QAAQ;AAC3B,eAAA,MAAM,2BAA2B,GAAG;AAAA,IAC/C;AAEA,UAAM,gBAAgB,MAAM;AAC1B,eAAS,MAAM,cAAc;AAAA,IAC/B;AACM,UAAA,SAAS,CAAC,YAAY;AAWtB,UAAA,UAAU,aAAa,OAAO;AAG1B,cAAA,IAAI,WAAW,OAAO;AAC9B,UAAI,MAAM;AACV,UAAI,OAAOF,aAAAA,YAAW,oBAAI,QAAO,WAAW,qBAAqB;AACjE,UAAI,KAAK,KAAK,QAAQ,OAAO,EAAE,EAAE,QAAQ,OAAO,EAAE,EAAE,QAAQ,KAAK,EAAE;AAEnE,cAAQ,GAAW;AACnB,aAAO,MAAM;AAAA,QACX,SAAS;AAAA,UACP;AAAA,YACE,cAAc,UAAU,SAAS;AAAA,YACjC,OAAO,OAAO;AAAA,YACd,SAAS,MAAM;AAAA,YACf,SAAS;AAAA,YACT,YAAY,UAAU,SAAS;AAAA,YAC/B,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,SAAS;AAAA,UAAA;AAAA,QAEZ,CAAA;AAAA,MACH;AAAA,IACF;AAEM,UAAA,UAAU,CAAC,SAAS,SAAS;AAIjC,UAAI,MAAM;AAAA,QACR,MAAM;AAAA,UACJ,QAAQ,UAAU,SAAS;AAAA,UAC3B;AAAA,UACA,IAAI,MAAM;AAAA,UACV,MAAM;AAAA,UACN,UAAU,UAAU,SAAS;AAAA,QAC/B;AAAA,QACA,IAAI;AAAA,UACF,QAAQ,QAAQ,MAAM;AAAA,UACtB,IAAI,QAAQ,MAAM;AAAA,UAClB,MAAM;AAAA,UACN,UAAU,QAAQ,MAAM;AAAA,QAAA;AAAA,MAE5B;AAEA,UAAI,WAAW;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AACQ,cAAA,IAAI,mBAAmB,QAAQ;AACvC,UAAI,SAAS;AAAA,QACX,MAAM;AAAA,QACN,OAAO,QAAQ,MAAM;AAAA,QACrB,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AACAF,iBAAA,KAAK,KAAK,IAAI,SAAS,MAAM,EAAE,KAAK,CAAC,QAAa;AACxC,gBAAA,IAAI,WAAW,GAAG;AACtB,YAAA,CAAC,IAAI,SAAS;AACV,gBAAA,MAAM,IAAI,OAAO;AAAA,QAAA;AAAA,MACzB,CACD;AAAA,IACH;AAEM,UAAA,cAAc,CAAC,SAAS;AAC5B,UAAI,OAAOE,aAAAA,YAAW,oBAAI,QAAO,WAAW,qBAAqB;AACjE,UAAI,KAAK,KAAK,QAAQ,OAAO,EAAE,EAAE,QAAQ,OAAO,EAAE,EAAE,QAAQ,KAAK,EAAE;AACnE,UAAI,WAAW;AAAA,QACb,MAAM;AAAA,QACN,OAAO,OAAO;AAAA,QACd,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AACA,YAAM,EAAE,SAAS,MAAM,OAAO,IAAQ,IAAAG,gBAAA;AAAA,QACpC,iCAAK,WAAL,EAAe,MAAM,QAAQ;AAAA,QAC7B,EAAE,KAAK,IAAI,WAAW,YAAY,CAAC,IAAI,EAAE;AAAA,MAC3C;AACI,UAAA;AAAqB,kBAAA;AACrB,UAAA;AACQ,kBAAAC,cAAA;AAAA,QACV,MAAM,CAAC,QAAQ,OAAO,MAAM,OAAO,KAAK,KAAK;AAAA,QAC7C,CAAC,CAACC,UAAS,KAAKC,KAAI,GAAG,aAAa;AAClC,cAAID,YAAW,OAAO;AACpB,gBAAI,KAAK;AACP,oBAAM,QAAQ,MAAM;AACpBT,4BAAAA,MAAI,YAAY;AAAA,YAAA,OACX;AACL,kBAAIU,OAAM;AACA,wBAAA,IAAI,UAAUA,KAAI;AAAA,cAAA;AAAA,YAC5B;AAAA,UACF;AAAA,QACF;AAAA,MAEJ;AAAA,IACF;AACK,SAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5XL,GAAG,WAAW,eAAe;"}