{"version": 3, "file": "index.js", "sources": ["../../../../../../../src/pages-work/components/echarts/DoubleLineBar/index.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvcGFnZXMtd29yay9jb21wb25lbnRzL2VjaGFydHMvRG91YmxlTGluZUJhci9pbmRleC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"content\">\r\n    <statusTip v-if=\"pageTips.show\" :status=\"pageTips.status\"></statusTip>\r\n\t<echartsUniapp v-else :option=\"option\"></echartsUniapp>\r\n  </view>\r\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { echartProps } from '@/pages-work/components/echarts/props';\r\nimport { deepMerge, handleTotalAndUnit, disposeGridLayout,getCustomColor } from '../../common/echartUtil';\r\nimport { isNumber } from '@/utils/is';\r\nimport useChartHook from '@/pages-work/components/hooks/useEchart';\r\nimport { deepClone } from '@/uni_modules/da-tree/utils';\r\nimport echartsUniapp from '@/pages-work/components/echarts/index.vue';\r\nimport statusTip from '@/pages-work/components/statusTip.vue';\r\nimport {merge} from \"lodash-es\";\r\n//组件传参\r\nconst props = defineProps({\r\n\t...echartProps\r\n})\r\n//最终图表配置项\r\nconst option = ref({});\r\nlet chartOption = {\r\n    title: {\r\n        show: true,\r\n    },\r\n    legend: {\r\n        show: true,\r\n        data: [],\r\n    },\r\n    xAxis: {\r\n        type: 'category',\r\n        nameGap:25,\r\n        data: [],\r\n    },\r\n    yAxis: [\r\n        { type: 'value', alignTicks: true },\r\n        { type: 'value', alignTicks: true },\r\n    ],\r\n    graphic:{\r\n        type: \"text\",\r\n        right: 0,\r\n        top: 0,\r\n        style: {\r\n            text: \"\",\r\n            fill: \"#464646\",\r\n            font: \"bolder 18px \\\"Microsoft YaHei\\\", sans-serif\"\r\n        }\r\n    },\r\n    series: []\r\n};\r\n//图表数据查询\r\nlet [{ dataSource, reload, pageTips, config }, { queryData }] = useChartHook(\r\n  props,\r\n  initOption\r\n)\r\n\r\n\r\n//初始化图表配置项\r\nfunction initOption(data) {\r\n  let chartData: any = dataSource.value\r\n  if (typeof chartData === 'string') {\r\n    chartData = JSON.parse(chartData)\r\n  }\r\n  if (chartData && chartData.length > 0) {\r\n      const colors = getCustomColor(config.option.customColor);\r\n      let configOption = config.option;\r\n      let leftChartType = configOption.yAxis && configOption.yAxis.length>0?configOption.yAxis[0].chartType:'bar';\r\n      //@ts-ignore\r\n      let legendData = [...new Set(chartData.map((item) => item.type))];\r\n      chartOption.series = [];\r\n      legendData.forEach((legend,index) => {\r\n          //图例颜色\r\n          let legendColor = configOption.series.length>0&&configOption.series[0].color?configOption.series[0].color[index]:null;\r\n          //1.获取类型\r\n          let allData = chartData.filter((item) => item.type == legend);\r\n          //2.获取数据\r\n          let leftData = allData.filter((item) => !item.yAxisIndex || (item.yAxisIndex && item.yAxisIndex == '0'));\r\n          let rightData = allData.filter((item) => item.yAxisIndex && item.yAxisIndex == '1');\r\n          //3.设置数据\r\n          //左y轴\r\n          let seriesType = config.seriesType.filter((item) => item.series == legend);\r\n          if (leftData && leftData.length > 0) {\r\n              let leftSeriesType = seriesType && seriesType.length > 0 ? seriesType[0]['type']:'bar';\r\n              let color = colors&&colors[index]?colors[index].color:\"#64b5f6\";\r\n              chartOption.series.push({\r\n                  name: legend,\r\n                  type: leftChartType=='line'?leftChartType:leftSeriesType,\r\n                  data: leftData.map((item) => item['value']),\r\n                  color: legendColor || color || '',\r\n                  yAxisIndex: 0,\r\n              });\r\n          }\r\n          //右y轴\r\n          if (rightData && rightData.length > 0) {\r\n              let color = colors && colors[index]?colors[index].color:\"\";\r\n              chartOption.series.push({\r\n                  name: legend,\r\n                  type: 'line',\r\n                  data: rightData.map((item) => item['value']),\r\n                  color: legendColor || color || '',\r\n                  yAxisIndex: 1,\r\n              });\r\n          }\r\n      });\r\n    //@ts-ignore\r\n      chartOption.xAxis.data = [...new Set(chartData.map((item) => item.name))];\r\n      chartOption.legend.data = legendData;\r\n      // 合并配置\r\n      if (props.config && config.option) {\r\n          merge(chartOption, config.option)\r\n          chartOption = handleTotalAndUnit(props.compName, chartOption, config, chartData)\r\n          chartOption = disposeGridLayout(props.compName, chartOption, config, chartData)\r\n          let title = config.option.title;\r\n          let color = title.textStyle.color||'#000';\r\n          let weight = title.textStyle.fontWeight || 'normal';\r\n          let fontSize = title.textStyle.fontSize || '14';\r\n          chartOption.graphic.style = {\r\n              text: \"\",\r\n              fill: color,\r\n              font: `${weight} ${fontSize}px \"Microsoft YaHei\", sans-serif`\r\n          }\r\n      }\r\n      console.log(\"双轴图this.chartOption====>\",chartOption);\r\n      option.value = deepClone(chartOption)\r\n      pageTips.show = false\r\n  } else {\r\n    pageTips.status = 1\r\n    pageTips.show = true\r\n  }\r\n}\r\n\r\nonMounted(()=>{\r\n\tqueryData();\r\n})\r\n\r\n</script>\r\n<style>\r\n.content {\r\n  padding: 10px;\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-work/components/echarts/DoubleLineBar/index.vue'\nwx.createComponent(Component)"], "names": ["ref", "useChartHook", "getCustomColor", "merge", "handleTotalAndUnit", "disposeGridLayout", "deepClone", "onMounted", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAaA,MAAA,gBAA0B,MAAA;AAC1B,MAAA,YAAsB,MAAA;;;;;AAGtB,UAAM,QAAQ;AAIR,UAAA,SAASA,cAAI,IAAA,EAAE;AACrB,QAAI,cAAc;AAAA,MACd,OAAO;AAAA,QACH,MAAM;AAAA,MACV;AAAA,MACA,QAAQ;AAAA,QACJ,MAAM;AAAA,QACN,MAAM,CAAA;AAAA,MACV;AAAA,MACA,OAAO;AAAA,QACH,MAAM;AAAA,QACN,SAAQ;AAAA,QACR,MAAM,CAAA;AAAA,MACV;AAAA,MACA,OAAO;AAAA,QACH,EAAE,MAAM,SAAS,YAAY,KAAK;AAAA,QAClC,EAAE,MAAM,SAAS,YAAY,KAAK;AAAA,MACtC;AAAA,MACA,SAAQ;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,KAAK;AAAA,QACL,OAAO;AAAA,UACH,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,QAAA;AAAA,MAEd;AAAA,MACA,QAAQ,CAAA;AAAA,IACZ;AAEI,QAAA,CAAC,EAAE,YAAY,QAAQ,UAAU,UAAU,EAAE,UAAW,CAAA,IAAIC,qCAAA;AAAA,MAC9D;AAAA,MACA;AAAA,IACF;AAIA,aAAS,WAAW,MAAM;AACxB,UAAI,YAAiB,WAAW;AAC5B,UAAA,OAAO,cAAc,UAAU;AACrB,oBAAA,KAAK,MAAM,SAAS;AAAA,MAAA;AAE9B,UAAA,aAAa,UAAU,SAAS,GAAG;AACnC,cAAM,SAASC,uCAAA,eAAe,OAAO,OAAO,WAAW;AACvD,YAAI,eAAe,OAAO;AACtB,YAAA,gBAAgB,aAAa,SAAS,aAAa,MAAM,SAAO,IAAE,aAAa,MAAM,CAAC,EAAE,YAAU;AAEtG,YAAI,aAAa,CAAC,GAAG,IAAI,IAAI,UAAU,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,CAAC;AAChE,oBAAY,SAAS,CAAC;AACX,mBAAA,QAAQ,CAAC,QAAO,UAAU;AAEjC,cAAI,cAAc,aAAa,OAAO,SAAO,KAAG,aAAa,OAAO,CAAC,EAAE,QAAM,aAAa,OAAO,CAAC,EAAE,MAAM,KAAK,IAAE;AAEjH,cAAI,UAAU,UAAU,OAAO,CAAC,SAAS,KAAK,QAAQ,MAAM;AAE5D,cAAI,WAAW,QAAQ,OAAO,CAAC,SAAS,CAAC,KAAK,cAAe,KAAK,cAAc,KAAK,cAAc,GAAI;AACnG,cAAA,YAAY,QAAQ,OAAO,CAAC,SAAS,KAAK,cAAc,KAAK,cAAc,GAAG;AAG9E,cAAA,aAAa,OAAO,WAAW,OAAO,CAAC,SAAS,KAAK,UAAU,MAAM;AACrE,cAAA,YAAY,SAAS,SAAS,GAAG;AAC7B,gBAAA,iBAAiB,cAAc,WAAW,SAAS,IAAI,WAAW,CAAC,EAAE,MAAM,IAAE;AAC7E,gBAAA,QAAQ,UAAQ,OAAO,KAAK,IAAE,OAAO,KAAK,EAAE,QAAM;AACtD,wBAAY,OAAO,KAAK;AAAA,cACpB,MAAM;AAAA,cACN,MAAM,iBAAe,SAAO,gBAAc;AAAA,cAC1C,MAAM,SAAS,IAAI,CAAC,SAAS,KAAK,OAAO,CAAC;AAAA,cAC1C,OAAO,eAAe,SAAS;AAAA,cAC/B,YAAY;AAAA,YAAA,CACf;AAAA,UAAA;AAGD,cAAA,aAAa,UAAU,SAAS,GAAG;AAC/B,gBAAA,QAAQ,UAAU,OAAO,KAAK,IAAE,OAAO,KAAK,EAAE,QAAM;AACxD,wBAAY,OAAO,KAAK;AAAA,cACpB,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM,UAAU,IAAI,CAAC,SAAS,KAAK,OAAO,CAAC;AAAA,cAC3C,OAAO,eAAe,SAAS;AAAA,cAC/B,YAAY;AAAA,YAAA,CACf;AAAA,UAAA;AAAA,QACL,CACH;AAED,oBAAY,MAAM,OAAO,CAAC,GAAG,IAAI,IAAI,UAAU,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,CAAC;AACxE,oBAAY,OAAO,OAAO;AAEtB,YAAA,MAAM,UAAU,OAAO,QAAQ;AACzBC,8BAAA,aAAa,OAAO,MAAM;AAChC,wBAAcC,uCAAmB,mBAAA,MAAM,UAAU,aAAa,QAAQ,SAAS;AAC/E,wBAAcC,uCAAkB,kBAAA,MAAM,UAAU,WAA8B;AAC1E,cAAA,QAAQ,OAAO,OAAO;AACtB,cAAA,QAAQ,MAAM,UAAU,SAAO;AAC/B,cAAA,SAAS,MAAM,UAAU,cAAc;AACvC,cAAA,WAAW,MAAM,UAAU,YAAY;AAC3C,sBAAY,QAAQ,QAAQ;AAAA,YACxB,MAAM;AAAA,YACN,MAAM;AAAA,YACN,MAAM,GAAG,MAAM,IAAI,QAAQ;AAAA,UAC/B;AAAA,QAAA;AAEI,gBAAA,IAAI,4BAA2B,WAAW;AAC3C,eAAA,QAAQC,mCAAU,WAAW;AACpC,iBAAS,OAAO;AAAA,MAAA,OACb;AACL,iBAAS,SAAS;AAClB,iBAAS,OAAO;AAAA,MAAA;AAAA,IAClB;AAGFC,kBAAAA,UAAU,MAAI;AACH,gBAAA;AAAA,IAAA,CACV;;;;;;;;;;;;;;;;ACrID,GAAG,gBAAgBC,SAAS;"}