{"version": 3, "file": "about.js", "sources": ["../../../../../src/pages/about/about.vue", "../../../../../uniPage:/cGFnZXMvYWJvdXQvYWJvdXQudnVl"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n  <view\r\n    class=\"bg-white overflow-hidden pt-2 px-4\"\r\n    :style=\"{ marginTop: safeAreaInsets?.top + 'px' }\"\r\n  >\r\n    <RequestComp />\r\n    <UploadComp />\r\n  </view>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport RequestComp from './components/request.vue'\r\nimport UploadComp from './components/upload.vue'\r\n\r\n// 获取屏幕边界到安全区域距离\r\nconst { safeAreaInsets } = uni.getSystemInfoSync()\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.test-css {\r\n  // mt-4=>1rem=>16px;\r\n  margin-top: 16px;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages/about/about.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;;;;;;;AAeA,MAAA,cAAwB,MAAA;AACxB,MAAA,aAAuB,MAAA;;;;AAGvB,UAAM,EAAE,eAAA,IAAmBA,cAAA,MAAI,kBAAkB;;;;;;;;;;AClBjD,GAAG,WAAW,eAAe;"}