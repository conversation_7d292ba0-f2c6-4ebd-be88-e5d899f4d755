"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const pagesWork_components_common_echartUtil = require("../common/echartUtil.js");
const pagesWork_components_common_china = require("../common/china.js");
const utils_is = require("../../../utils/is.js");
const utils_http = require("../../../utils/http.js");
const common_vendor = require("../../../common/vendor.js");
function useChartHook(props, initOption, echarts) {
  const config = props.config;
  const dataSource = common_vendor.ref([]);
  const reload = common_vendor.ref(true);
  const pageTips = common_vendor.reactive({
    show: true,
    status: 0
    // 0:loading,1:暂无数据,2:网络超时
  });
  const areaCode = common_vendor.ref("");
  const mapName = common_vendor.ref("");
  const mapDataJson = common_vendor.ref({});
  const city_center = common_vendor.ref([]);
  let chartOption = {
    title: {
      show: true
    },
    card: {
      title: ""
    },
    tooltip: {
      formatter: ""
    },
    legend: {
      bottom: "5%",
      left: "center"
    },
    xAxis: {
      type: "category",
      data: []
    },
    yAxis: {
      type: "value"
    },
    series: [{}]
  };
  common_vendor.watch(
    props.config,
    (config2) => {
      if (!(props == null ? void 0 : props.isView)) {
        console.log("=======props.config============");
        queryData();
      }
    },
    { deep: true }
  );
  const getAreaCode = common_vendor.computed(() => {
    var _a, _b, _c, _d, _e, _f;
    if (areaCode.value) {
      return areaCode == null ? void 0 : areaCode.value;
    }
    return ((_a = props.config.option) == null ? void 0 : _a.area) && ((_b = props.config.option.area) == null ? void 0 : _b.value) ? (_f = (_c = props.config.option) == null ? void 0 : _c.area) == null ? void 0 : _f.value[((_e = (_d = props.config.option) == null ? void 0 : _d.area) == null ? void 0 : _e.value.length) - 1] : "china";
  });
  let getAreaName = common_vendor.computed(() => {
    var _a, _b, _c, _d, _e, _f, _g;
    return ((_a = config == null ? void 0 : config.option) == null ? void 0 : _a.area) && ((_c = (_b = config == null ? void 0 : config.option) == null ? void 0 : _b.area) == null ? void 0 : _c.name) && ((_g = (_d = config == null ? void 0 : config.option) == null ? void 0 : _d.area) == null ? void 0 : _g.name[((_f = (_e = config == null ? void 0 : config.option) == null ? void 0 : _e.area) == null ? void 0 : _f.name.length) - 1]) || "中国";
  });
  const city_point = common_vendor.computed(() => {
    return city_center.value;
  });
  function queryData(compConfig, queryParams) {
    var _a;
    let config2 = compConfig ? compConfig : __spreadValues({}, props.config);
    if (config2.dataType == 2)
      ;
    else if (config2.dataType == 4) {
      let params = getParams(config2, queryParams);
      utils_http.http.post("/drag/onlDragDatasetHead/getTotalData", params).then((res) => {
        if (res.success) {
          let result = res.result.chartData;
          if (result && result.length > 0) {
            try {
              let arr = JSON.parse(JSON.stringify(result));
              dataSource.value = pagesWork_components_common_echartUtil.handleDateFields(arr, config2);
              dataSource.value = pagesWork_components_common_echartUtil.handleCalcFields(arr, config2.valueFields, config2.assistYFields);
              initOption && utils_is.isFunction(initOption) && initOption();
            } catch (e) {
              console.log("查询数据报错", e);
            }
          } else {
            dataSource.value = [];
            initOption && utils_is.isFunction(initOption) && initOption();
          }
        }
      });
    } else {
      let chartData = (_a = props.config) == null ? void 0 : _a.chartData;
      if (typeof chartData === "string") {
        try {
          chartData = JSON.parse(chartData);
        } catch (e) {
        }
      }
      dataSource.value = chartData;
      initOption && initOption(chartData);
    }
  }
  function getParams(config2, params) {
    let queryParams = pagesWork_components_common_echartUtil.packageParams(config2, params);
    return {
      tableName: config2.tableName,
      compName: config2.compName,
      config: {
        type: config2.typeFields || [],
        name: config2.nameFields || [],
        value: config2.valueFields || [],
        assistValue: config2.assistYFields || [],
        assistType: config2.assistTypeFields || [],
        formType: config2.formType
      },
      condition: __spreadValues({}, queryParams)
    };
  }
  function getHeatMapData(data) {
    let res = [];
    for (let i = 0; i < data.length; i++) {
      let geoCoord = common_vendor.unref(city_center)[data[i].name];
      if (geoCoord) {
        res.push(geoCoord.concat(data[i].value));
      }
    }
    return res;
  }
  function queryCityCenter() {
    return __async(this, null, function* () {
      if (city_center.value.length == 0) {
        const res = yield utils_http.http.get("/drag/mock/json/city_center");
        city_center.value = res;
      }
    });
  }
  function registerMap() {
    let areaCode2 = getAreaCode.value;
    if (getAreaCode.value != "china" && getAreaCode.value != "") {
      utils_http.http.get("/drag/onlDragDatasetHead/getMapDataByCode", {
        code: getAreaCode.value,
        name: getAreaName.value
      }).then((res) => {
        const { success, result } = res;
        if (success) {
          mapDataJson.value = JSON.parse(result.mapData);
        }
      });
    } else {
      mapDataJson.value = pagesWork_components_common_china.china;
    }
    mapName.value = areaCode2;
    return areaCode2;
  }
  function getConvertData(chartData) {
    let geoCoordMap = pagesWork_components_common_echartUtil.getGeoCoordMap(mapDataJson.value);
    let result = [];
    if (geoCoordMap) {
      for (let i = 0; i < chartData.length; i++) {
        let geoCoord = geoCoordMap[chartData[i].name];
        if (geoCoord) {
          result.push({
            name: chartData[i].name,
            code: geoCoord.adcode,
            value: geoCoord.center.concat(chartData[i].value)
          });
        }
      }
    }
    return result;
  }
  function setGeoAreaColor(options, config2) {
    if (options.visualMap && options.visualMap.show == false) {
      delete options.visualMap;
    }
    if (options.visualMap && options.visualMap.show == true) {
      options.visualMap.inRange = {
        color: config2.commonOption.inRange.color
      };
    }
    if (config2.commonOption && config2.commonOption.gradientColor == false) {
      options.geo.itemStyle.normal.areaColor = config2.commonOption.areaColor.color1;
    }
    if (config2.commonOption && config2.commonOption.gradientColor == true) {
      options.geo.itemStyle.normal.areaColor = {
        type: "radial",
        x: 0.5,
        y: 0.5,
        r: 0.8,
        colorStops: [
          {
            offset: 0,
            color: config2.commonOption.areaColor.color1
          },
          {
            offset: 1,
            color: config2.commonOption.areaColor.color2
          }
        ],
        globalCoord: false
      };
    }
    return options;
  }
  function handleTotalAndUnitMap(compName, chartOption2, config2, chartData) {
    if (config2.compStyleConfig) {
      let showUnitConfig = config2.compStyleConfig.showUnit;
      let unit = showUnitConfig.unit ? showUnitConfig.unit : "";
      showUnitConfig.numberLevel ? showUnitConfig.numberLevel : "";
      chartOption2.series.forEach((item) => {
        if (item.name == "数据") {
          let labelConfig = {
            label: {
              normal: {
                show: unit ? true : false,
                formatter: (params) => {
                  let showLabel = `${params.name}: `;
                  let value = 0;
                  if (params.seriesType == "effectScatter") {
                    if (Array.isArray(params.value)) {
                      value = params.value[2];
                    } else {
                      value = params.value;
                    }
                  }
                  if (unit) {
                    showLabel += showUnitConfig.position == "suffix" ? `${pagesWork_components_common_echartUtil.calcUnit(value, showUnitConfig)}${unit}` : `${unit}${pagesWork_components_common_echartUtil.calcUnit(value, showUnitConfig)}`;
                  }
                  return showLabel;
                }
              }
            }
          };
          pagesWork_components_common_echartUtil.deepMerge(item, __spreadValues({}, labelConfig));
        }
      });
      let summaryConfig = config2.compStyleConfig.summary;
      if (summaryConfig.showTotal && chartData && chartData.length > 0) {
        let leftData = chartData.filter((item) => !item.yAxisIndex || item.yAxisIndex == "0");
        let totalTitle = summaryConfig.showY ? calcTotal(summaryConfig, leftData) : "";
        Object.assign(chartOption2.title, { text: totalTitle });
      }
    }
    chartOption2.geo.top = 20;
    return chartOption2;
  }
  function calcTotal(summaryConfig, chartData) {
    let rawData = chartData;
    if (rawData && rawData.length > 0) {
      let showField = summaryConfig.showField;
      let showName = summaryConfig.showName || "总计";
      let totalType = summaryConfig.totalType || "sum";
      let valueField = showField == "all" ? "value" : showField;
      let valueArr = rawData.map((item) => item[valueField] ? item[valueField] : 0);
      let total = 0;
      if (valueArr.length > 0) {
        if (totalType == "sum") {
          total = valueArr.reduce((prev, cur) => prev + cur, 0);
        } else if (totalType == "max") {
          total = Math.max.apply(Math, valueArr);
        } else if (totalType == "min") {
          total = Math.min.apply(Math, valueArr);
        } else if (totalType == "average") {
          total = (valueArr.reduce((prev, cur) => prev + cur, 0) / valueArr.length).toFixed(2);
        }
      }
      return `${showName}: ${pagesWork_components_common_echartUtil.keepTwoDecimals(total)}`;
    }
    return "";
  }
  function handleCommonOpt(chartOption2) {
    if (chartOption2.visualMap) {
      chartOption2.visualMap.show = false;
    }
    return chartOption2;
  }
  function getFromConvertData(chartData) {
    let result = [];
    let names = [];
    for (let i = 0; i < chartData.length; i++) {
      let fromName = chartData[i].fromName;
      if (fromName && names.indexOf(fromName) == -1) {
        result.push({
          name: fromName,
          value: [chartData[i].fromLng, chartData[i].fromLat, chartData[i].value]
        });
        names.push(fromName);
      }
    }
    return result;
  }
  function getToConvertData(chartData) {
    let result = [];
    let names = [];
    for (let i = 0; i < chartData.length; i++) {
      let toName = chartData[i].toName;
      if (toName && names.indexOf(toName) == -1) {
        result.push({
          name: toName,
          value: [chartData[i].toLng, chartData[i].toLat, chartData[i].value]
        });
        names.push(toName);
      }
    }
    return result;
  }
  return [
    { dataSource, reload, pageTips, config, chartOption, mapDataJson, mapName, getAreaCode, city_point, city_center },
    {
      queryData,
      registerMap,
      handleTotalAndUnitMap,
      handleCommonOpt,
      setGeoAreaColor,
      getConvertData,
      queryCityCenter,
      getHeatMapData,
      getFromConvertData,
      getToConvertData
    }
  ];
}
exports.useChartHook = useChartHook;
//# sourceMappingURL=useEchartMap.js.map
