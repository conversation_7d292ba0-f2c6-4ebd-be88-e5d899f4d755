{"version": 3, "file": "request.js", "sources": ["../../../../../../src/pages/about/components/request.vue", "../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvcGFnZXMvYWJvdXQvY29tcG9uZW50cy9yZXF1ZXN0LnZ1ZQ"], "sourcesContent": ["\r\n\r\n<template>\r\n  <view class=\"p-6 text-center\">\r\n    <view class=\"my-2\">使用的是 laf 云后台</view>\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n    <view class=\"my-2 text-left text-sm\">{{ recommendUrl }}</view>\r\n\r\n\r\n    <!-- http://localhost:9000/#/pages/index/request -->\r\n    <wd-button @click=\"run\" class=\"my-6\">发送请求</wd-button>\r\n    <view class=\"h-16\">\r\n      <view v-if=\"loading\">loading...</view>\r\n      <template v-else>\r\n        <view class=\"text-xl\">请求数据如下</view>\r\n        <view class=\"text-green leading-8\">{{ JSON.stringify(data) }}</view>\r\n      </template>\r\n    </view>\r\n    <wd-button type=\"error\" @click=\"reset\" class=\"my-6\" :disabled=\"!data\">重置数据</wd-button>\r\n  </view>\r\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { getFooAPI, postFooAPI, IFooItem } from '@/service/index/foo'\r\nimport { findPetsByStatusQueryOptions } from '@/service/app'\r\nimport { useQuery } from '@tanstack/vue-query'\r\n\r\nconst recommendUrl = ref('http://laf.run/signup?code=ohaOgIX')\r\n\r\n// const initialData = {\r\n//   name: 'initialData',\r\n//   id: '1234',\r\n// }\r\nconst initialData = undefined\r\n// 适合少部分全局性的接口————多个页面都需要的请求接口，额外编写一个 Service 层\r\nconst { loading, error, data, run } = useRequest<IFooItem>(() => getFooAPI('张三'), {\r\n  immediate: true,\r\n  initialData,\r\n})\r\n\r\n// 使用 vue-query 的 useQuery 来请求数据，只做参考，是否使用请根据实际情况而定\r\nconst {\r\n  data: data2,\r\n  error: error2,\r\n  isLoading: isLoading2,\r\n  refetch,\r\n} = useQuery(findPetsByStatusQueryOptions({ params: { status: ['available'] } }))\r\n\r\nconst reset = () => {\r\n  data.value = initialData\r\n}\r\n</script>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages/about/components/request.vue'\nwx.createComponent(Component)"], "names": ["ref", "useRequest", "getFooAPI", "useQuery", "findPetsByStatusQueryOptions", "Component"], "mappings": ";;;;;;;;;;;;;;;;AAiCM,UAAA,eAAeA,kBAAI,oCAAoC;AAM7D,UAAM,cAAc;AAEd,UAAA,EAAE,SAAgB,MAAM,IAAA,IAAQC,iBAAAA,WAAqB,MAAMC,kBAAAA,UAAU,IAAI,GAAG;AAAA,MAChF,WAAW;AAAA,MACX;AAAA,IAAA,CACD;AAGK,UAAA;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,WAAW;AAAA,MACX;AAAA,IAAA,IACEC,cAAA,SAASC,yBAA6B,6BAAA,EAAE,QAAQ,EAAE,QAAQ,CAAC,WAAW,EAAI,EAAA,CAAC,CAAC;AAEhF,UAAM,QAAQ,MAAM;AAClB,WAAK,QAAQ;AAAA,IACf;;;;;;;;;;;;;;;;;;ACvDA,GAAG,gBAAgBC,SAAS;"}