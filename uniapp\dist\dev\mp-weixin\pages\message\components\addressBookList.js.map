{"version": 3, "file": "addressBookList.js", "sources": ["../../../../../../src/pages/message/components/addressBookList.vue", "../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvcGFnZXMvbWVzc2FnZS9jb21wb25lbnRzL2FkZHJlc3NCb29rTGlzdC52dWU"], "sourcesContent": ["<template>\r\n  <z-paging ref=\"paging\" :fixed=\"false\" v-model=\"dataList\" @query=\"queryList\">\r\n    <view class=\"list\" v-for=\"(item, index) in dataSource\" :key=\"index\" @click=\"handleGo(item)\">\r\n      <view :class=\"['avatar', item.color]\">\r\n        <text :class=\"[`cuIcon-${item.icon}`]\"></text>\r\n      </view>\r\n      <text class=\"content color-gray-4 text-ellipsis\">{{ item.name }}</text>\r\n    </view>\r\n  </z-paging>\r\n  <wd-toast />\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { onLaunch, onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'\r\nimport { hasRoute, cache } from '@/common/uitls'\r\nimport { TENANT_LIST } from '@/common/constants'\r\nimport { http } from '@/utils/http'\r\nimport { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'\r\nimport { useRouter } from '@/plugin/uni-mini-router'\r\n\r\nconst toast = useToast()\r\nconst router = useRouter()\r\nconst paging = ref(null)\r\nconst dataList = ref([])\r\nconst dataSource = computed(() => {\r\n  return [\r\n    {\r\n      name: '联系人',\r\n      icon: 'addressbook',\r\n      type: 'friend',\r\n      color: 'blue',\r\n      path: 'contacts',\r\n      value: '1',\r\n    },\r\n    { name: '我的群组', icon: 'group', color: 'azure-green', path: 'myGroup', value: '2' },\r\n    { name: '更多功能', icon: 'moreandroid', color: 'orange', path: 'msgMore', value: '3' },\r\n    ...dataList.value.map((item) => {\r\n      return {\r\n        label: item.name,\r\n        name: item.name,\r\n        value: item.id,\r\n        key: item.id,\r\n        color: '',\r\n        icon: 'list',\r\n        path: 'tenant',\r\n      }\r\n    }),\r\n  ]\r\n})\r\nconst queryList = () => {\r\n  http\r\n    .get('/sys/tenant/getCurrentUserTenant')\r\n    .then((res: any) => {\r\n      if (res.success && res.result?.list?.length) {\r\n        paging.value.complete(res.result.list)\r\n        cache(TENANT_LIST, res.result.list)\r\n      } else {\r\n        paging.value.complete(false)\r\n      }\r\n    })\r\n    .catch((res) => {\r\n      paging.value.complete(false)\r\n    })\r\n}\r\nconst handleGo = (item) => {\r\n  if (!hasRoute({ name: item.path })) {\r\n    toast.warning('还未开发~')\r\n    return\r\n  }\r\n  router.push({\r\n    name: item.path,\r\n    params: {\r\n      id: item.value,\r\n      title: item.name,\r\n    },\r\n  })\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.list {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10px 15px;\r\n  border-bottom: 1px solid rgba(229, 229, 229, 0.5);\r\n  .avatar {\r\n    width: 45px;\r\n    height: 45px;\r\n    border-radius: 50%;\r\n    color: #fff;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    font-size: 22px;\r\n    margin-right: 10px;\r\n    background-color: #617f89;\r\n    flex: none;\r\n    &.blue {\r\n      background-color: #0081ff;\r\n    }\r\n    &.azure-green {\r\n      background-color: #02bbd5;\r\n    }\r\n    &.orange {\r\n      background-color: #f37b1d;\r\n    }\r\n  }\r\n  .content {\r\n    font-size: 15px;\r\n  }\r\n}\r\n:deep(.zp-empty-view) {\r\n  display: none;\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages/message/components/addressBookList.vue'\nwx.createComponent(Component)"], "names": ["useToast", "useRouter", "ref", "computed", "http", "cache", "TENANT_LIST", "hasRoute"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAoBA,UAAM,QAAQA,cAAAA,SAAS;AACvB,UAAM,SAASC,gCAAAA,UAAU;AACnB,UAAA,SAASC,kBAAI,IAAI;AACjB,UAAA,WAAWA,cAAI,IAAA,EAAE;AACjB,UAAA,aAAaC,cAAAA,SAAS,MAAM;AACzB,aAAA;AAAA,QACL;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,QACA,EAAE,MAAM,QAAQ,MAAM,SAAS,OAAO,eAAe,MAAM,WAAW,OAAO,IAAI;AAAA,QACjF,EAAE,MAAM,QAAQ,MAAM,eAAe,OAAO,UAAU,MAAM,WAAW,OAAO,IAAI;AAAA,QAClF,GAAG,SAAS,MAAM,IAAI,CAAC,SAAS;AACvB,iBAAA;AAAA,YACL,OAAO,KAAK;AAAA,YACZ,MAAM,KAAK;AAAA,YACX,OAAO,KAAK;AAAA,YACZ,KAAK,KAAK;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,YACN,MAAM;AAAA,UACR;AAAA,QACD,CAAA;AAAA,MACH;AAAA,IAAA,CACD;AACD,UAAM,YAAY,MAAM;AACtBC,iBAAAA,KACG,IAAI,kCAAkC,EACtC,KAAK,CAAC,QAAa;;AAClB,YAAI,IAAI,aAAW,eAAI,WAAJ,mBAAY,SAAZ,mBAAkB,SAAQ;AAC3C,iBAAO,MAAM,SAAS,IAAI,OAAO,IAAI;AAC/BC,uBAAAA,MAAAC,iBAAA,aAAa,IAAI,OAAO,IAAI;AAAA,QAAA,OAC7B;AACE,iBAAA,MAAM,SAAS,KAAK;AAAA,QAAA;AAAA,MAC7B,CACD,EACA,MAAM,CAAC,QAAQ;AACP,eAAA,MAAM,SAAS,KAAK;AAAA,MAAA,CAC5B;AAAA,IACL;AACM,UAAA,WAAW,CAAC,SAAS;AACzB,UAAI,CAACC,aAAS,SAAA,EAAE,MAAM,KAAK,KAAM,CAAA,GAAG;AAClC,cAAM,QAAQ,OAAO;AACrB;AAAA,MAAA;AAEF,aAAO,KAAK;AAAA,QACV,MAAM,KAAK;AAAA,QACX,QAAQ;AAAA,UACN,IAAI,KAAK;AAAA,UACT,OAAO,KAAK;AAAA,QAAA;AAAA,MACd,CACD;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;AC3EA,GAAG,gBAAgB,SAAS;"}