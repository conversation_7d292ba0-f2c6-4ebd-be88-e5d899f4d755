{"version": 3, "file": "page-params.js", "sources": ["../../../../src/store/page-params.ts"], "sourcesContent": ["import { defineStore } from 'pinia'\r\nimport { ref } from 'vue'\r\n\r\nconst initState = {}\r\n// 持久化页面参数，h5刷新参数不见了\r\nexport const useParamsStore = defineStore(\r\n  'page-params',\r\n  () => {\r\n    const params = ref({ ...initState })\r\n    const setPageParams = (key, options) => {\r\n      params.value = { ...params.value, ...{ [key]: options } }\r\n    }\r\n    const getPageParams = (key) => {\r\n      return params.value[key]\r\n    }\r\n    const clearPageParams = (key) => {\r\n      delete params.value[key]\r\n    }\r\n    const reset = () => {\r\n      params.value = {}\r\n    }\r\n    return {\r\n      params,\r\n      setPageParams,\r\n      clearPageParams,\r\n      getPageParams,\r\n      reset,\r\n    }\r\n  },\r\n  {\r\n    persist: true,\r\n  },\r\n)\r\n"], "names": ["defineStore", "ref"], "mappings": ";;;;;;;;;;;;;;;;;;AAGA,MAAM,YAAY,CAAC;AAEZ,MAAM,iBAAiBA,cAAA;AAAA,EAC5B;AAAA,EACA,MAAM;AACJ,UAAM,SAASC,cAAAA,IAAI,mBAAK,UAAW;AAC7B,UAAA,gBAAgB,CAAC,KAAK,YAAY;AAC/B,aAAA,QAAQ,kCAAK,OAAO,QAAU,EAAE,CAAC,GAAG,GAAG;IAChD;AACM,UAAA,gBAAgB,CAAC,QAAQ;AACtB,aAAA,OAAO,MAAM,GAAG;AAAA,IACzB;AACM,UAAA,kBAAkB,CAAC,QAAQ;AACxB,aAAA,OAAO,MAAM,GAAG;AAAA,IACzB;AACA,UAAM,QAAQ,MAAM;AAClB,aAAO,QAAQ,CAAC;AAAA,IAClB;AACO,WAAA;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,SAAS;AAAA,EAAA;AAEb;;"}