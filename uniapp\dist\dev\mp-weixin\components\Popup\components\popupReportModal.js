"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../common/vendor.js");
const utils_http = require("../../../utils/http.js");
if (!Array) {
  const _easycom_wd_search2 = common_vendor.resolveComponent("wd-search");
  const _easycom_wd_checkbox2 = common_vendor.resolveComponent("wd-checkbox");
  const _easycom_wd_checkbox_group2 = common_vendor.resolveComponent("wd-checkbox-group");
  const _easycom_wd_radio2 = common_vendor.resolveComponent("wd-radio");
  const _easycom_wd_cell2 = common_vendor.resolveComponent("wd-cell");
  const _easycom_wd_radio_group2 = common_vendor.resolveComponent("wd-radio-group");
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _easycom_wd_popup2 = common_vendor.resolveComponent("wd-popup");
  (_easycom_wd_search2 + _easycom_wd_checkbox2 + _easycom_wd_checkbox_group2 + _easycom_wd_radio2 + _easycom_wd_cell2 + _easycom_wd_radio_group2 + _easycom_z_paging2 + _easycom_PageLayout2 + _easycom_wd_popup2)();
}
const _easycom_wd_search = () => "../../../node-modules/wot-design-uni/components/wd-search/wd-search.js";
const _easycom_wd_checkbox = () => "../../../node-modules/wot-design-uni/components/wd-checkbox/wd-checkbox.js";
const _easycom_wd_checkbox_group = () => "../../../node-modules/wot-design-uni/components/wd-checkbox-group/wd-checkbox-group.js";
const _easycom_wd_radio = () => "../../../node-modules/wot-design-uni/components/wd-radio/wd-radio.js";
const _easycom_wd_cell = () => "../../../node-modules/wot-design-uni/components/wd-cell/wd-cell.js";
const _easycom_wd_radio_group = () => "../../../node-modules/wot-design-uni/components/wd-radio-group/wd-radio-group.js";
const _easycom_z_paging = () => "../../../uni_modules/z-paging/components/z-paging/z-paging.js";
const _easycom_PageLayout = () => "../../PageLayout/PageLayout.js";
const _easycom_wd_popup = () => "../../../node-modules/wot-design-uni/components/wd-popup/wd-popup.js";
if (!Math) {
  (_easycom_wd_search + _easycom_wd_checkbox + _easycom_wd_checkbox_group + _easycom_wd_radio + _easycom_wd_cell + _easycom_wd_radio_group + _easycom_z_paging + _easycom_PageLayout + _easycom_wd_popup)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "popupReportModal",
  options: {
    styleIsolation: "shared"
  }
}), {
  __name: "popupReportModal",
  props: {
    code: {
      type: String,
      default: "",
      required: true
    },
    showFiled: {
      type: String,
      default: "",
      required: true
    },
    multi: {
      type: Boolean,
      default: true
    }
  },
  emits: ["change", "close"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const toast = common_vendor.useToast();
    const show = common_vendor.ref(true);
    const api = {
      getColumns: "/online/cgreport/api/getRpColumns",
      getData: "/online/cgreport/api/getData",
      getQueryInfo: "/online/cgreport/api/getQueryInfo"
    };
    console.log("props:::", props);
    const navTitle = common_vendor.ref("");
    const paging = common_vendor.ref(null);
    const dataList = common_vendor.ref([]);
    let rpConfigId = null;
    let loadedColumns = false;
    const dictOptions = common_vendor.ref([]);
    const columns = common_vendor.ref([]);
    const checkedValue = common_vendor.ref(props.multi ? [] : "");
    const checkboxRef = common_vendor.ref(null);
    const search = common_vendor.reactive({
      keyword: "",
      placeholder: "",
      field: ""
    });
    const handleClose = () => {
      setTimeout(() => {
        emit("close");
      }, 400);
    };
    const handleConfirm = () => {
      if (checkedValue.value.length == 0) {
        toast.warning("还没选择~");
        return;
      }
      const result = [];
      let value = checkedValue.value;
      if (!Array.isArray(checkedValue.value)) {
        value = [checkedValue.value];
      }
      value.forEach((index) => {
        result.push(dataList.value[index]);
      });
      show.value = false;
      emit("change", result);
      handleClose();
    };
    const handleCancel = () => {
      show.value = false;
      handleClose();
      console.log("取消了~");
    };
    function handleSearch() {
      paging.value.reload();
    }
    function handleClear() {
      search.keyword = "";
      handleSearch();
    }
    const hanldeCheck = (index) => {
      if (props.multi) {
        if (Array.isArray(checkboxRef.value)) {
          checkboxRef.value[index].toggle();
        }
      } else {
        checkedValue.value = index;
      }
    };
    const getQueryInfo = () => {
      const analysis = (data = []) => {
        var _a;
        if (data.length) {
          search.placeholder = `请输入${data[0].label}`;
          search.field = data[0].field;
        } else {
          const item = (_a = columns[0]) != null ? _a : {};
          search.placeholder = `请输入${item.title}`;
          search.field = item.dataIndex;
        }
      };
      utils_http.http.get(`${api.getQueryInfo}/${rpConfigId}`).then((res) => {
        if (res.success) {
          analysis(res.result);
        } else {
          analysis();
        }
      }).catch((err) => {
        analysis();
      });
    };
    const getRpColumns = () => {
      return new Promise((resolve, reject) => {
        if (loadedColumns) {
          resolve();
        } else {
          utils_http.http.get(`${api.getColumns}/${props.code}`).then((res) => {
            var _a;
            if (res.success) {
              loadedColumns = true;
              const { result } = res;
              navTitle.value = result.cgRpConfigName;
              dictOptions.value = result.dictOptions;
              rpConfigId = result.cgRpConfigId;
              const fileds = props.showFiled.split(",");
              (_a = result.columns) == null ? void 0 : _a.forEach((item) => {
                if (fileds.includes(item.dataIndex)) {
                  columns.value.push(item);
                }
              });
              getQueryInfo();
              resolve();
            } else {
              reject();
            }
          }).catch((err) => {
            reject();
          });
        }
      });
    };
    const queryList = (pageNo, pageSize) => {
      const pararms = { pageNo, pageSize };
      if (search.keyword) {
        pararms[search.field] = `*${search.keyword}*`;
      }
      getRpColumns().then(() => {
        utils_http.http.get(`${api.getData}/${rpConfigId}`, pararms).then((res) => {
          var _a;
          if (res.success && res.result.records) {
            paging.value.complete((_a = res.result.records) != null ? _a : []);
          } else {
            paging.value.complete(false);
          }
        }).catch((err) => {
        });
      }).catch((err) => {
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.o(handleSearch),
        b: common_vendor.o(handleClear),
        c: common_vendor.o(($event) => search.keyword = $event),
        d: common_vendor.p({
          ["hide-cancel"]: true,
          placeholder: search.placeholder,
          modelValue: search.keyword
        }),
        e: __props.multi
      }, __props.multi ? {
        f: common_vendor.f(dataList.value, (item, index, i0) => {
          return {
            a: common_vendor.f(columns.value, (cItem, cIndex, i1) => {
              return {
                a: common_vendor.t(cItem.title),
                b: common_vendor.t(item[cItem.dataIndex]),
                c: cIndex
              };
            }),
            b: common_vendor.sr(checkboxRef, "c6ca6e22-5-" + i0 + ",c6ca6e22-4", {
              "k": "checkboxRef",
              "f": 1
            }),
            c: "c6ca6e22-5-" + i0 + ",c6ca6e22-4",
            d: common_vendor.p({
              modelValue: index
            }),
            e: common_vendor.o(() => {
            }, index),
            f: common_vendor.o(($event) => hanldeCheck(index), index),
            g: index
          };
        }),
        g: common_vendor.o(($event) => checkedValue.value = $event),
        h: common_vendor.p({
          shape: "square",
          modelValue: checkedValue.value
        })
      } : {
        i: common_vendor.f(dataList.value, (item, index, i0) => {
          return {
            a: common_vendor.f(columns.value, (cItem, cIndex, i1) => {
              return {
                a: common_vendor.t(cItem.title),
                b: common_vendor.t(item[cItem.dataIndex]),
                c: cIndex
              };
            }),
            b: "c6ca6e22-8-" + i0 + "," + ("c6ca6e22-7-" + i0),
            c: common_vendor.p({
              value: index
            }),
            d: common_vendor.o(() => {
            }, index),
            e: common_vendor.o(($event) => hanldeCheck(index), index),
            f: "c6ca6e22-7-" + i0 + ",c6ca6e22-6",
            g: index
          };
        }),
        j: common_vendor.o(($event) => checkedValue.value = $event),
        k: common_vendor.p({
          shape: "dot",
          modelValue: checkedValue.value
        })
      }, {
        l: common_vendor.sr(paging, "c6ca6e22-2,c6ca6e22-1", {
          "k": "paging"
        }),
        m: common_vendor.o(queryList),
        n: common_vendor.o(($event) => dataList.value = $event),
        o: common_vendor.p({
          fixed: false,
          ["default-page-size"]: 15,
          modelValue: dataList.value
        }),
        p: common_vendor.o(handleConfirm),
        q: common_vendor.o(handleCancel),
        r: common_vendor.p({
          navTitle: navTitle.value,
          type: "popup",
          navRightText: "确定"
        }),
        s: common_vendor.o(($event) => show.value = $event),
        t: common_vendor.p({
          position: "bottom",
          modelValue: show.value
        })
      });
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-c6ca6e22"]]);
wx.createComponent(Component);
//# sourceMappingURL=popupReportModal.js.map
