"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_user = require("../../../store/user.js");
const utils_http = require("../../../utils/http.js");
require("../../../plugin/uni-mini-router/router/index.js");
const plugin_uniMiniRouter_core_index = require("../../../plugin/uni-mini-router/core/index.js");
const common_uitls = require("../../../common/uitls.js");
const pagesMessage_common_vuePy = require("../../common/vue-py.js");
const common_constants = require("../../../common/constants.js");
const store_pageParams = require("../../../store/page-params.js");
const common_assets = require("../../../common/assets.js");
if (!Array) {
  const _easycom_wd_search2 = common_vendor.resolveComponent("wd-search");
  const _easycom_wd_index_anchor2 = common_vendor.resolveComponent("wd-index-anchor");
  const _easycom_wd_img2 = common_vendor.resolveComponent("wd-img");
  const _easycom_wd_cell2 = common_vendor.resolveComponent("wd-cell");
  const _easycom_wd_index_bar2 = common_vendor.resolveComponent("wd-index-bar");
  const _easycom_wd_status_tip2 = common_vendor.resolveComponent("wd-status-tip");
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  (_easycom_wd_search2 + _easycom_wd_index_anchor2 + _easycom_wd_img2 + _easycom_wd_cell2 + _easycom_wd_index_bar2 + _easycom_wd_status_tip2 + _easycom_z_paging2)();
}
const _easycom_wd_search = () => "../../../node-modules/wot-design-uni/components/wd-search/wd-search.js";
const _easycom_wd_index_anchor = () => "../../../node-modules/wot-design-uni/components/wd-index-anchor/wd-index-anchor.js";
const _easycom_wd_img = () => "../../../node-modules/wot-design-uni/components/wd-img/wd-img.js";
const _easycom_wd_cell = () => "../../../node-modules/wot-design-uni/components/wd-cell/wd-cell.js";
const _easycom_wd_index_bar = () => "../../../node-modules/wot-design-uni/components/wd-index-bar/wd-index-bar.js";
const _easycom_wd_status_tip = () => "../../../node-modules/wot-design-uni/components/wd-status-tip/wd-status-tip.js";
const _easycom_z_paging = () => "../../../uni_modules/z-paging/components/z-paging/z-paging.js";
if (!Math) {
  (_easycom_wd_search + _easycom_wd_index_anchor + _easycom_wd_img + _easycom_wd_cell + _easycom_wd_index_bar + _easycom_wd_status_tip + _easycom_z_paging)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "workmate",
  props: ["workType", "tenantId"],
  setup(__props) {
    const props = __props;
    const toast = common_vendor.useToast();
    const userStore = store_user.useUserStore();
    const paramsStore = store_pageParams.useParamsStore();
    const paging = common_vendor.ref(null);
    const router = plugin_uniMiniRouter_core_index.useRouter();
    const dataList = common_vendor.ref([]);
    const originData = common_vendor.ref([]);
    const keyword = common_vendor.ref("");
    const dataSource = common_vendor.ref([]);
    const api = {
      appQueryUser: "/sys/user/appQueryUser",
      getMyGroupList: "/eoa/im/newApi/getMyGroupList"
    };
    const queryList = (pageNo, pageSize) => {
      const pararms = { pageNo, pageSize, tenantId: props.tenantId };
      let url;
      if (props.workType) {
        url = api.getMyGroupList;
        pararms["type"] = props.workType;
      } else {
        url = api.appQueryUser;
      }
      utils_http.http.get(url, pararms).then((res) => {
        if (res.success && res.result.length) {
          paging.value.complete(res.result);
        } else {
          paging.value.complete(false);
        }
      }).catch((res) => {
        paging.value.complete(false);
      });
    };
    common_vendor.watch(dataList, () => {
      let result = handleResult(dataList.value);
      result = transformData(result);
      result.sort((a, b) => a.index.localeCompare(b.index));
      originData.value = [...result];
      dataSource.value = result;
    });
    function handleSearch() {
      dataSource.value = [];
      common_vendor.nextTick$1(() => {
        if (keyword.value) {
          dataSource.value = originData.value.filter((item) => {
            return item.data.some((inItem) => {
              return inItem.realname.indexOf(keyword.value) != -1;
            });
          });
        } else {
          dataSource.value = originData.value;
        }
      });
    }
    function handleClear() {
      keyword.value = "";
      handleSearch();
    }
    const handleClick = (index, data) => {
      if (props.workType) {
        toast.warning("群组聊天暂未开发~");
      } else {
        paramsStore.setPageParams("personPage", { data });
        router.push({ name: "personPage" });
      }
    };
    const transformData = (data) => {
      const grouped = data.reduce((acc, item) => {
        const key = item.szm;
        if (!acc[key]) {
          acc[key] = [];
        }
        acc[key].push(item);
        return acc;
      }, {});
      return Object.entries(grouped).map(([index, data2]) => ({ index, data: data2 })).sort((a, b) => b.index.localeCompare(a.index));
    };
    const handleResult = (arr) => {
      let newArr = [];
      arr.forEach((item) => {
        let { id, realname, avatar, username, phone, email, post, orgCodeTxt } = item;
        if (username !== userStore.userInfo.username) {
          let pinYin = realname;
          if (realname) {
            if (/.*[\u4e00-\u9fa5]+.*$/.test(realname)) {
              pinYin = pagesMessage_common_vuePy.vPinyin.chineseToPinYin(realname);
            }
          }
          if (avatar) {
            avatar = common_uitls.getFileAccessHttpUrl(avatar);
          }
          let szm = pinYin.substr(0, 1);
          var numReg = /^[0-9]*$/;
          var numRe = new RegExp(numReg);
          szm = !numRe.test(szm) ? szm.toUpperCase() : "#";
          newArr.push({
            id,
            realname,
            avatar,
            username,
            phone,
            email,
            post,
            orgCodeTxt,
            szm
          });
        }
      });
      return newArr;
    };
    common_vendor.onLoad(() => {
      const tenantList = common_uitls.cache(common_constants.TENANT_LIST);
      tenantList == null ? void 0 : tenantList.map((item) => {
        return { key: item.id, title: item.name };
      });
    });
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.o(handleSearch),
        b: common_vendor.o(handleClear),
        c: common_vendor.o(($event) => keyword.value = $event),
        d: common_vendor.p({
          ["hide-cancel"]: true,
          placeholder: "我要去哪里？",
          modelValue: keyword.value
        }),
        e: dataSource.value.length
      }, dataSource.value.length ? {
        f: common_vendor.f(dataSource.value, (item, k0, i0) => {
          return {
            a: "06ddada0-3-" + i0 + ",06ddada0-2",
            b: common_vendor.p({
              index: item.index
            }),
            c: common_vendor.f(item.data, (inItem, k1, i1) => {
              var _a;
              return {
                a: "06ddada0-5-" + i0 + "-" + i1 + "," + ("06ddada0-4-" + i0 + "-" + i1),
                b: common_vendor.p({
                  customClass: "avatar",
                  width: 50,
                  height: 50,
                  src: inItem.avatar || common_vendor.unref(common_assets.defaultAvatar)
                }),
                c: common_vendor.t(inItem.realname),
                d: common_vendor.t((_a = inItem.orgCodeTxt) != null ? _a : "暂无"),
                e: common_vendor.o(($event) => handleClick(item.index, inItem), item.username),
                f: "06ddada0-4-" + i0 + "-" + i1 + ",06ddada0-2"
              };
            }),
            d: item.username,
            e: item.index
          };
        }),
        g: common_vendor.p({
          border: true,
          clickable: true
        }),
        h: common_vendor.p({
          sticky: true
        })
      } : {
        i: common_vendor.p({
          image: "content",
          tip: "暂无内容"
        })
      }, {
        j: common_vendor.sr(paging, "06ddada0-0", {
          "k": "paging"
        }),
        k: common_vendor.o(queryList),
        l: common_vendor.o(($event) => dataList.value = $event),
        m: common_vendor.p({
          fixed: false,
          ["default-page-size"]: 100,
          modelValue: dataList.value
        })
      });
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-06ddada0"]]);
wx.createComponent(Component);
//# sourceMappingURL=workmate.js.map
