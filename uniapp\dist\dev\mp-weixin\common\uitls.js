"use strict";
const common_vendor = require("./vendor.js");
const common_constants = require("./constants.js");
const globalStyle = {
  navigationStyle: "default",
  navigationBarTitleText: "uniapp",
  navigationBarBackgroundColor: "#f8f8f8",
  navigationBarTextStyle: "black",
  backgroundColor: "#FFFFFF"
};
const easycom = {
  autoscan: true,
  custom: {
    "^wd-(.*)": "wot-design-uni/components/wd-$1/wd-$1.vue",
    "^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)": "z-paging/components/z-paging$1/z-paging$1.vue"
  }
};
const tabBar = {
  color: "#aaa",
  selectedColor: "#39b54a",
  backgroundColor: "#F8F8F8",
  borderStyle: "black",
  height: "50px",
  fontSize: "11px",
  iconWidth: "24px",
  spacing: "3px",
  list: [
    {
      iconPath: "static/tabbar/tabbar-home-2.png",
      selectedIconPath: "static/tabbar/tabbar-home.png",
      pagePath: "pages/index/index",
      text: "主页"
    },
    {
      iconPath: "static/tabbar/tabbar-data-2.png",
      selectedIconPath: "static/tabbar/tabbar-data.png",
      pagePath: "pages/data/index",
      text: "数据"
    },
    {
      iconPath: "static/tabbar/tabbar-user-2.png",
      selectedIconPath: "static/tabbar/tabbar-user.png",
      pagePath: "pages/user/people",
      text: "我"
    }
  ]
};
const subPackages = [
  {
    root: "pages-home",
    pages: [
      {
        path: "home/home",
        type: "page",
        layout: "default",
        style: {
          navigationBarTitleText: ""
        }
      }
    ]
  },
  {
    root: "pages-message",
    pages: [
      {
        path: "chat/chat",
        type: "page",
        layout: "default",
        style: {
          navigationBarTitleText: "聊天",
          navigationStyle: "custom"
        }
      },
      {
        path: "contacts/contacts",
        type: "page",
        layout: "default",
        style: {
          navigationStyle: "custom",
          navigationBarTitleText: "联系人"
        }
      },
      {
        path: "personPage/personPage",
        type: "page",
        layout: "default",
        style: {
          navigationBarTitleText: "",
          navigationStyle: "custom"
        }
      },
      {
        path: "tenant/tenant",
        type: "page",
        layout: "default",
        style: {
          navigationBarTitleText: "",
          navigationStyle: "custom",
          styleIsolation: "apply-shared"
        }
      }
    ]
  },
  {
    root: "pages-user",
    pages: [
      {
        path: "location/location",
        type: "page",
        layout: "default",
        style: {
          navigationBarTitleText: "",
          navigationStyle: "custom"
        }
      },
      {
        path: "userEdit/userEdit",
        type: "page",
        layout: "default",
        style: {
          navigationBarTitleText: "",
          navigationStyle: "custom"
        }
      }
    ]
  },
  {
    root: "pages-work",
    pages: [
      {
        path: "dragPage/index",
        type: "page",
        layout: "default",
        style: {
          navigationStyle: "custom",
          navigationBarTitleText: "仪表盘"
        }
      },
      {
        path: "onlinePage/onlineAdd",
        type: "page",
        layout: "default",
        style: {
          navigationStyle: "custom",
          navigationBarTitleText: "Online表单新增"
        }
      },
      {
        path: "onlinePage/onlineDetail",
        type: "page",
        layout: "default",
        style: {
          navigationStyle: "custom",
          navigationBarTitleText: "Online表单新增"
        }
      },
      {
        path: "onlinePage/onlineEdit",
        type: "page",
        layout: "default",
        style: {
          navigationStyle: "custom",
          navigationBarTitleText: "Online表单新增"
        }
      }
    ]
  },
  {
    root: "pages-sub",
    pages: [
      {
        path: "online/online",
        type: "page",
        layout: "default",
        style: {
          navigationBarTitleText: "online",
          navigationStyle: "custom"
        }
      },
      {
        path: "online/onlineCard",
        type: "page",
        layout: "default",
        style: {
          navigationBarTitleText: "",
          navigationStyle: "custom"
        }
      },
      {
        path: "online/onlineTable",
        type: "page",
        layout: "default",
        style: {
          navigationBarTitleText: "",
          navigationStyle: "custom"
        }
      }
    ]
  },
  {
    root: "pages-data",
    pages: [
      {
        path: "medication/list",
        style: {
          navigationBarTitleText: "用药情况",
          navigationStyle: "custom"
        }
      },
      {
        path: "medication/form",
        style: {
          navigationBarTitleText: "用药情况",
          navigationStyle: "custom"
        }
      },
      {
        path: "vital-signs/list",
        style: {
          navigationBarTitleText: "日常体征监测",
          navigationStyle: "custom"
        }
      },
      {
        path: "vital-signs/form",
        style: {
          navigationBarTitleText: "日常体征监测",
          navigationStyle: "custom"
        }
      },
      {
        path: "examinationReport/list",
        style: {
          navigationBarTitleText: "院外检查报告",
          navigationStyle: "custom"
        }
      },
      {
        path: "examinationReport/form",
        style: {
          navigationBarTitleText: "院外检查报告",
          navigationStyle: "custom"
        }
      },
      {
        path: "monitor/list",
        style: {
          navigationBarTitleText: "慢性心衰患者监测表",
          navigationStyle: "custom"
        }
      },
      {
        path: "monitor/form",
        style: {
          navigationBarTitleText: "慢性心衰患者监测表",
          navigationStyle: "custom"
        }
      },
      {
        path: "psychology/list",
        style: {
          navigationBarTitleText: "慢性心衰患者心理量表",
          navigationStyle: "custom"
        }
      },
      {
        path: "psychology/form",
        style: {
          navigationBarTitleText: "慢性心衰患者心理量表",
          navigationStyle: "custom"
        }
      },
      {
        path: "dailyLife/list",
        style: {
          navigationBarTitleText: "慢性心衰患者日常生活指数评估",
          navigationStyle: "custom"
        }
      },
      {
        path: "dailyLife/form",
        style: {
          navigationBarTitleText: "慢性心衰患者日常生活指数评估",
          navigationStyle: "custom"
        }
      },
      {
        path: "registration/form",
        style: {
          navigationBarTitleText: "知情同意与登记",
          navigationStyle: "custom"
        }
      },
      {
        path: "registration/consent",
        style: {
          navigationBarTitleText: "知情同意与登记",
          navigationStyle: "custom"
        }
      },
      {
        path: "doctor/form",
        style: {
          navigationBarTitleText: "医生注册",
          navigationStyle: "custom"
        }
      },
      {
        path: "social/form",
        style: {
          navigationBarTitleText: "社工/社区医生注册",
          navigationStyle: "custom"
        }
      },
      {
        path: "chat/chat",
        style: {
          navigationBarTitleText: "医患沟通",
          navigationStyle: "custom"
        }
      },
      {
        path: "chat/chatDetail",
        style: {
          navigationBarTitleText: "医患沟通",
          navigationStyle: "custom"
        }
      },
      {
        path: "chat/addChat",
        style: {
          navigationBarTitleText: "新增医患沟通",
          navigationStyle: "custom"
        }
      }
    ]
  }
];
const pages = [
  {
    path: "pages/index/index",
    type: "home",
    layout: "default",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: ""
    }
  },
  {
    path: "pages/about/about",
    type: "page",
    style: {
      navigationBarTitleText: "关于"
    }
  },
  {
    path: "pages/annotation/annotationDetail",
    type: "page",
    layout: "default",
    style: {
      navigationBarTitleText: "",
      navigationStyle: "custom"
    }
  },
  {
    path: "pages/annotation/annotationList",
    type: "page",
    layout: "default",
    style: {
      navigationBarTitleText: "我的消息",
      navigationStyle: "custom"
    }
  },
  {
    path: "pages/data/detail",
    type: "page",
    layout: "default",
    style: {
      navigationBarTitleText: "患者详情数据",
      navigationStyle: "custom"
    }
  },
  {
    path: "pages/data/index",
    type: "page",
    layout: "default",
    style: {
      navigationBarTitleText: "",
      navigationStyle: "custom"
    }
  },
  {
    path: "pages/demo/demo",
    type: "page",
    layout: "default",
    style: {
      navigationBarTitleText: "demo演示",
      navigationStyle: "custom"
    }
  },
  {
    path: "pages/demo/form",
    type: "page",
    layout: "default",
    style: {
      navigationBarTitleText: "表单",
      navigationStyle: "custom"
    }
  },
  {
    path: "pages/demo/indexBar",
    type: "page",
    layout: "default",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "通讯录"
    }
  },
  {
    path: "pages/demo/selectPicker",
    type: "page",
    layout: "default",
    style: {
      navigationBarTitleText: "单选多选",
      navigationStyle: "custom"
    }
  },
  {
    path: "pages/demo/tree",
    type: "page",
    layout: "default",
    style: {
      navigationBarTitleText: "树示例",
      navigationStyle: "custom"
    }
  },
  {
    path: "pages/index/data",
    type: "page",
    layout: "default",
    style: {
      navigationBarTitleText: "更多数据",
      navigationStyle: "custom"
    }
  },
  {
    path: "pages/login/login",
    type: "page",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: ""
    }
  },
  {
    path: "pages/message/message",
    type: "page",
    layout: "default",
    style: {
      navigationBarTitleText: "",
      navigationStyle: "custom"
    }
  },
  {
    path: "pages/more/more",
    type: "page",
    layout: "default",
    style: {
      navigationBarTitleText: "更多",
      navigationStyle: "custom"
    }
  },
  {
    path: "pages/user/people",
    type: "page",
    layout: "default",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "个人"
    }
  },
  {
    path: "pages/workHome/index",
    type: "page",
    layout: "default",
    style: {
      navigationBarTitleText: "工作台",
      navigationStyle: "custom"
    }
  }
];
const e = {
  globalStyle,
  easycom,
  tabBar,
  subPackages,
  pages
};
function cache(key, value = null, seconds = 2 * 3600) {
  var timestamp = +/* @__PURE__ */ new Date() / 1e3;
  if (key && value === null) {
    var val = common_vendor.index.getStorageSync(key);
    if (val && val.length > 0) {
      var tmp = val.split("|");
      if (!tmp[2] || timestamp >= tmp[2]) {
        console.log("key已失效");
        common_vendor.index.removeStorageSync(key);
        return "";
      } else {
        console.log("key未失效");
        if (tmp[1] == "json") {
          return JSON.parse(tmp[0]);
        }
        return tmp[0];
      }
    }
  } else if (key && value) {
    var expire = timestamp + seconds;
    console.log("typeof value", typeof value);
    if (typeof value == "object") {
      value = JSON.stringify(value) + "|json|" + expire;
    } else {
      value = value + "|string|" + expire;
    }
    common_vendor.index.setStorageSync(key, value);
  } else {
    console.log("key不能空");
  }
}
const getStaticDomainURL = () => {
  return "https://www.mograine.cn/api/sys/common/static";
};
const getFileAccessHttpUrl = function(avatar, subStr) {
  if (!avatar)
    return "";
  if (!subStr)
    subStr = "http";
  if (avatar) {
    avatar = avatar.replace(/user_imgs\\/, "user_imgs/");
  }
  if (avatar && avatar.startsWith(subStr)) {
    return avatar;
  } else {
    return getStaticDomainURL() + "/" + avatar;
  }
};
const hasRoute = ({ name, path, routeList }) => {
  routeList = routeList != null ? routeList : common_vendor.e(e);
  if (path) {
    return !!routeList.find((item) => item.path === path);
  }
  if (name) {
    return !!routeList.find((item) => item.path.split("/").pop() === name);
  }
};
function beautifyTime(datetime = "") {
  if (datetime == null) {
    return "";
  }
  datetime = datetime.toString().replace(/-/g, "/");
  let time = /* @__PURE__ */ new Date();
  let outTime = new Date(datetime);
  if (/^[1-9]\d*$/.test(datetime)) {
    outTime = new Date(parseInt(datetime));
  }
  if (time.getTime() < outTime.getTime()) {
    return parseTime(outTime, "{y}/{m}/{d}");
  }
  if (time.getFullYear() != outTime.getFullYear()) {
    return parseTime(outTime, "{y}/{m}/{d}");
  }
  if (time.getMonth() != outTime.getMonth()) {
    return parseTime(outTime, "{m}/{d}");
  }
  if (time.getDate() != outTime.getDate()) {
    let day = outTime.getDate() - time.getDate();
    if (day == -1) {
      return parseTime(outTime, "昨天 {h}:{i}");
    }
    if (day == -2) {
      return parseTime(outTime, "前天 {h}:{i}");
    }
    return parseTime(outTime, "{m}-{d}");
  }
  if (time.getHours() != outTime.getHours()) {
    return parseTime(outTime, "{h}:{i}");
  }
  let minutes = outTime.getMinutes() - time.getMinutes();
  if (minutes == 0) {
    return "刚刚";
  }
  minutes = Math.abs(minutes);
  return `${minutes}分钟前`;
}
function parseTime(time, cFormat) {
  if (arguments.length === 0) {
    return null;
  }
  let date;
  const format = cFormat || "{y}-{m}-{d} {h}:{i}:{s}";
  if (typeof time === "object") {
    date = time;
  } else {
    if (typeof time === "string" && /^[0-9]+$/.test(time)) {
      time = parseInt(time);
    } else {
      time = new Date(time);
    }
    date = new Date(time.toString().replace(/-/g, "/"));
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  };
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key];
    if (key === "a") {
      return ["日", "一", "二", "三", "四", "五", "六"][value];
    }
    return value.toString().padStart(2, "0");
  });
  return time_str;
}
function randomString(length, chats) {
  if (!chats)
    chats = "0123456789qwertyuioplkjhgfdsazxcvbnm";
  let str = "";
  for (let i = 0; i < length; i++) {
    let num = randomNumber(0, chats.length - 1);
    str += chats[num];
  }
  return str;
}
function randomNumber() {
  const random = (min, max) => {
    return Math.floor(Math.random() * (max - min + 1) + min);
  };
  if (arguments.length === 1) {
    let [length] = arguments;
    let nums = [...Array(length).keys()].map((i) => i > 0 ? random(0, 9) : random(1, 9));
    return parseInt(nums.join(""));
  } else if (arguments.length >= 2) {
    let [min, max] = arguments;
    return random(min, max);
  } else {
    return Number.NaN;
  }
}
function formatDate(value, fmt) {
  var regPos = /^\d+(\.\d+)?$/;
  if (regPos.test(value)) {
    let getDate = new Date(value);
    let o = {
      "M+": getDate.getMonth() + 1,
      "d+": getDate.getDate(),
      "h+": getDate.getHours(),
      "H+": getDate.getHours(),
      "m+": getDate.getMinutes(),
      "s+": getDate.getSeconds(),
      "q+": Math.floor((getDate.getMonth() + 3) / 3),
      S: getDate.getMilliseconds()
    };
    if (/(y+)/.test(fmt)) {
      fmt = fmt.replace(RegExp.$1, (getDate.getFullYear() + "").substr(4 - RegExp.$1.length));
    }
    for (let k in o) {
      if (new RegExp("(" + k + ")").test(fmt)) {
        fmt = fmt.replace(
          RegExp.$1,
          RegExp.$1.length === 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length)
        );
      }
    }
    return fmt;
  } else {
    if (value && value.length > 0) {
      value = value.trim();
      return value.substr(0, fmt.length);
    }
    return value;
  }
}
function getWeekMonthQuarterYear(date) {
  const getISOWeek = (date2) => {
    const jan4 = new Date(date2.getFullYear(), 0, 4);
    const oneDay = 864e5;
    return Math.ceil(((date2 - jan4.getTime()) / oneDay + jan4.getDay() + 1) / 7);
  };
  const dateObj = new Date(date);
  const week = getISOWeek(dateObj);
  const month = dateObj.getMonth() + 1;
  const quarter = Math.floor(dateObj.getMonth() / 3) + 1;
  const year = dateObj.getFullYear();
  return {
    year: `${year}`,
    month: `${year}-${month.toString().padStart(2, "0")}`,
    week: `${year}-${week}周`,
    quarter: `${year}-Q${quarter}`
  };
}
function getRandomIntBetweenOneAndTen() {
  return Math.floor(Math.random() * 10) + 1;
}
function getRandomColor() {
  return common_constants.colorPanel["natural"][getRandomIntBetweenOneAndTen()] || "#00bcd4";
}
const getPlaceholder = (attrs = {}) => {
  let label = attrs.label;
  if (label.endsWith("：") || label.endsWith(":")) {
    label = label.substr(0, label.length - 1);
  }
  return `请选择${label}`;
};
exports.beautifyTime = beautifyTime;
exports.cache = cache;
exports.e = e;
exports.formatDate = formatDate;
exports.getFileAccessHttpUrl = getFileAccessHttpUrl;
exports.getPlaceholder = getPlaceholder;
exports.getRandomColor = getRandomColor;
exports.getWeekMonthQuarterYear = getWeekMonthQuarterYear;
exports.hasRoute = hasRoute;
exports.pages = pages;
exports.randomString = randomString;
exports.subPackages = subPackages;
//# sourceMappingURL=uitls.js.map
