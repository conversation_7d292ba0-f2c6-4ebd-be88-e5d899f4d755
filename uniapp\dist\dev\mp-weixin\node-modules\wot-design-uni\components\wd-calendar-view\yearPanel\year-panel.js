"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../../../../common/vendor.js");
if (!Math) {
  Year();
}
const Year = () => "../year/year.js";
const __default__ = {
  options: {
    addGlobalClass: true,
    virtualHost: true,
    styleIsolation: "shared"
  }
};
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, __default__), {
  __name: "year-panel",
  props: common_vendor.yearPanelProps,
  emits: ["change"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const scrollTop = common_vendor.ref(0);
    const scrollIndex = common_vendor.ref(0);
    const scrollHeight = common_vendor.computed(() => {
      const scrollHeight2 = props.panelHeight + (props.showPanelTitle ? 26 : 16);
      return scrollHeight2;
    });
    const years = common_vendor.computed(() => {
      return common_vendor.getYears(props.minDate, props.maxDate).map((year, index) => {
        return {
          date: year,
          height: index === 0 ? 200 : 245
        };
      });
    });
    const title = common_vendor.computed(() => {
      return common_vendor.formatYearTitle(years.value[scrollIndex.value].date);
    });
    common_vendor.onMounted(() => {
      scrollIntoView();
    });
    function scrollIntoView() {
      return __async(this, null, function* () {
        yield common_vendor.pause();
        let activeDate = null;
        if (common_vendor.isArray(props.value)) {
          activeDate = props.value[0];
        } else if (common_vendor.isNumber(props.value)) {
          activeDate = props.value;
        }
        if (!activeDate) {
          activeDate = Date.now();
        }
        let top = 0;
        for (let index = 0; index < years.value.length; index++) {
          if (common_vendor.compareYear(years.value[index].date, activeDate) === 0) {
            break;
          }
          top += years.value[index] ? Number(years.value[index].height) : 0;
        }
        scrollTop.value = 0;
        if (top > 0) {
          yield common_vendor.pause();
          scrollTop.value = top + 45;
        }
      });
    }
    const yearScroll = (event) => {
      if (years.value.length <= 1) {
        return;
      }
      const scrollTop2 = Math.max(0, event.detail.scrollTop);
      doSetSubtitle(scrollTop2);
    };
    function doSetSubtitle(scrollTop2) {
      let height = 0;
      for (let index = 0; index < years.value.length; index++) {
        height = height + years.value[index].height;
        if (scrollTop2 < height) {
          scrollIndex.value = index;
          return;
        }
      }
    }
    function handleDateChange({ value }) {
      emit("change", {
        value
      });
    }
    __expose({
      scrollIntoView
    });
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: _ctx.showPanelTitle
      }, _ctx.showPanelTitle ? {
        b: common_vendor.t(title.value)
      } : {}, {
        c: common_vendor.f(years.value, (item, index, i0) => {
          return {
            a: common_vendor.o(handleDateChange, index),
            b: "f948783e-0-" + i0,
            c: common_vendor.p({
              type: _ctx.type,
              date: item.date,
              value: _ctx.value,
              ["min-date"]: _ctx.minDate,
              ["max-date"]: _ctx.maxDate,
              ["max-range"]: _ctx.maxRange,
              formatter: _ctx.formatter,
              ["range-prompt"]: _ctx.rangePrompt,
              ["allow-same-day"]: _ctx.allowSameDay,
              ["default-time"]: _ctx.defaultTime,
              showTitle: index !== 0
            }),
            d: index,
            e: `year${index}`
          };
        }),
        d: common_vendor.s(`height: ${scrollHeight.value}px`),
        e: common_vendor.o(yearScroll),
        f: scrollTop.value
      });
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f948783e"]]);
wx.createComponent(Component);
//# sourceMappingURL=year-panel.js.map
