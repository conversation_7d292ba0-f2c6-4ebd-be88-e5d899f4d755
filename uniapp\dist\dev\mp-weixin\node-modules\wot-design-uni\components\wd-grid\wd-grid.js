"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../../common/vendor.js");
const __default__ = {
  name: "wd-grid",
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: "shared"
  }
};
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, __default__), {
  props: common_vendor.gridProps,
  setup(__props) {
    const nextTick = () => new Promise((resolve) => setTimeout(resolve, 20));
    const props = __props;
    const { linkChildren, children } = common_vendor.useChildren(common_vendor.GRID_KEY);
    linkChildren({ props });
    common_vendor.watch(
      () => props.column,
      (val, oldVal) => {
        if (val === oldVal)
          return;
        if (!val || val <= 0) {
          console.error(
            "The number of columns attribute value is invalid. The attribute must be greater than 0 and it is not recommended to use a larger value attribute."
          );
        }
        oldVal && init();
      },
      {
        deep: true,
        immediate: true
      }
    );
    common_vendor.watch(
      () => props.border,
      (val) => {
        val && Promise.resolve().then(nextTick).then(() => {
          init();
        });
      },
      {
        deep: true,
        immediate: true
      }
    );
    common_vendor.watch(
      () => children,
      () => {
        handleChildrenChange();
      },
      {
        deep: true
      }
    );
    const rootStyle = common_vendor.computed(() => {
      return `${props.gutter ? "padding-left:" + props.gutter + "px;padding-bottom:" + props.gutter + "px;" : ""}${props.customStyle}`;
    });
    const handleChildrenChange = common_vendor.debounce(() => {
      init();
    }, 50);
    function init() {
      if (!children)
        return;
      children.forEach((item, index) => {
        if (props.border) {
          const { column } = props;
          if (column) {
            const isRightItem = children.length - 1 === index || (index + 1) % column === 0;
            const isFirstLine = index + 1 <= column;
            isFirstLine && item.$.exposed.setiIemClass("is-first");
            isRightItem && item.$.exposed.setiIemClass("is-right");
            !isFirstLine && item.$.exposed.setiIemClass("is-border");
          } else {
            item.$.exposed.setiIemClass("is-first");
          }
          children.length - 1 === index && item.$.exposed.setiIemClass(item.$.exposed.itemClass.value + " is-last");
        }
        item.$.exposed.init();
      });
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.n(`wd-grid ${_ctx.customClass}`),
        b: common_vendor.s(rootStyle.value)
      };
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-31468b8a"]]);
wx.createComponent(Component);
//# sourceMappingURL=wd-grid.js.map
