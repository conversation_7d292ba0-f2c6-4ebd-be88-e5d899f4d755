"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../common/vendor.js");
const utils_http = require("../../../utils/http.js");
require("../../../plugin/uni-mini-router/router/index.js");
const plugin_uniMiniRouter_core_index = require("../../../plugin/uni-mini-router/core/index.js");
const common_uitls = require("../../../common/uitls.js");
const store_pageParams = require("../../../store/page-params.js");
if (!Array) {
  const _easycom_uni_list_chat2 = common_vendor.resolveComponent("uni-list-chat");
  const _easycom_uni_list2 = common_vendor.resolveComponent("uni-list");
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  const _easycom_BottomOperate2 = common_vendor.resolveComponent("BottomOperate");
  const _easycom_wd_toast2 = common_vendor.resolveComponent("wd-toast");
  (_easycom_uni_list_chat2 + _easycom_uni_list2 + _easycom_z_paging2 + _easycom_BottomOperate2 + _easycom_wd_toast2)();
}
const _easycom_uni_list_chat = () => "../../../uni_modules/uni-list/components/uni-list-chat/uni-list-chat.js";
const _easycom_uni_list = () => "../../../uni_modules/uni-list/components/uni-list/uni-list.js";
const _easycom_z_paging = () => "../../../uni_modules/z-paging/components/z-paging/z-paging.js";
const _easycom_BottomOperate = () => "../../../components/BottomOperate/BottomOperate.js";
const _easycom_wd_toast = () => "../../../node-modules/wot-design-uni/components/wd-toast/wd-toast.js";
if (!Math) {
  (_easycom_uni_list_chat + _easycom_uni_list + _easycom_z_paging + _easycom_BottomOperate + _easycom_wd_toast)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "chatList",
  options: {
    styleIsolation: "shared"
  }
}), {
  __name: "chatList",
  setup(__props) {
    const toast = common_vendor.useToast();
    const router = plugin_uniMiniRouter_core_index.useRouter();
    const paramsStore = store_pageParams.useParamsStore();
    const paging = common_vendor.ref(null);
    common_vendor.ref();
    const dataList = common_vendor.ref([]);
    const options = [
      { key: "backtop", icon: "backtop", label: "置顶" },
      { key: "cancelbacktop", icon: "translate-bold", label: "取消置顶" },
      { key: "delete", icon: "delete", label: "删除", color: "red" }
    ];
    const bottomOperatePopup = common_vendor.reactive({
      show: false,
      title: "",
      data: {},
      options: []
    });
    const queryList = () => {
      utils_http.http.get("/eoa/im/newApi/getChatList").then((res) => {
        if (res.success) {
          paging.value.complete(res.result.logVoList);
          common_vendor.nextTick$1(() => {
            setTimeout(() => {
              sortByIzTop(dataList.value);
              dataList.value = [...dataList.value];
            }, 10);
          });
        } else {
          paging.value.complete(false);
        }
      }).catch((res) => {
        paging.value.complete(false);
      });
    };
    const sortByIzTop = (arr) => {
      return arr.sort((a, b) => {
        if (a.izTop && !b.izTop) {
          return -1;
        } else if (!a.izTop && b.izTop) {
          return 1;
        } else {
          return 0;
        }
      });
    };
    const getFirstStr = (val) => {
      return val ? val.substr(0, 1) : val;
    };
    const getAvatar = (item) => {
      var _a;
      if (["systemNotice"].includes(item.type))
        ;
      else if (["group"].includes(item.type))
        ;
      return (_a = item.fromAvatar) != null ? _a : "";
    };
    const getChatType = (item) => {
      switch (item.type) {
        case "discussion":
          return "[组消息]";
        case "systemNotice":
          return "[系统消息]";
        case "friend":
          return item.status == "offline" ? "[离线]" : "[在线]";
        case "group":
          return "[群消息]";
        default:
          return "";
      }
    };
    const handleLongPress = (item) => {
      bottomOperatePopup.show = true;
      bottomOperatePopup.title = item.fromUserName;
      bottomOperatePopup.data = item;
      bottomOperatePopup.options = options.filter((o) => {
        if (o.key == "backtop" && item.izTop == 1) {
          return false;
        } else if (o.key == "cancelbacktop" && item.izTop == 0) {
          return false;
        }
        return true;
      });
    };
    const handleChange = ({ option, data }) => {
      if (["cancelbacktop", "backtop"].includes(option.key)) {
        let izTop = 1;
        if (option.key === "cancelbacktop") {
          izTop = 0;
        }
        utils_http.http.post("/eoa/im/newApi/chatToTop", {
          id: data.id,
          izTop
        }).then((res) => {
          if (res.success) {
            paging.value.reload();
          }
        });
      } else if (option.key === "delete") {
        utils_http.http.post("/eoa/im/newApi/removeChat", {
          id: data.id
        }).then((res) => {
          if (res.success) {
            paging.value.reload();
          }
        });
      }
    };
    const handleGo = (item) => {
      if (["systemNotice"].includes(item.type)) {
        router.push({ name: "annotationList", params: { backRouteName: "message" } });
      } else if (["friend"].includes(item.type)) {
        paramsStore.setPageParams("chat", { data: item });
        router.push({ name: "chat" });
      } else {
        toast.warning("暂不支持");
      }
    };
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.f(common_vendor.unref(dataList), (item, index, i0) => {
          return common_vendor.e$1({
            a: ["systemNotice"].includes(item.type)
          }, ["systemNotice"].includes(item.type) ? {} : {}, {
            b: ["group"].includes(item.type) && !item.fromAvatar
          }, ["group"].includes(item.type) && !item.fromAvatar ? {
            c: common_vendor.t(getFirstStr(item.fromUserName))
          } : {}, {
            d: common_vendor.o(($event) => handleGo(item), index),
            e: "c0fbcb96-2-" + i0 + ",c0fbcb96-1",
            f: common_vendor.p({
              avatarCircle: true,
              clickable: true,
              title: item.fromUserName,
              avatar: getAvatar(item),
              note: getChatType(item),
              time: common_vendor.unref(common_uitls.beautifyTime)(item.sendTime),
              ["badge-positon"]: "left",
              ["badge-text"]: item.unreadNum
            }),
            g: item.izTop == 1 ? 1 : "",
            h: common_vendor.o(($event) => handleLongPress(item), index),
            i: common_vendor.o(($event) => handleGo(item), index),
            j: index
          });
        }),
        b: common_vendor.sr(paging, "c0fbcb96-0", {
          "k": "paging"
        }),
        c: common_vendor.o(queryList),
        d: common_vendor.o(($event) => common_vendor.isRef(dataList) ? dataList.value = $event : null),
        e: common_vendor.p({
          fixed: false,
          ["default-page-size"]: 100,
          modelValue: common_vendor.unref(dataList)
        }),
        f: common_vendor.unref(bottomOperatePopup).show
      }, common_vendor.unref(bottomOperatePopup).show ? {
        g: common_vendor.o(() => common_vendor.unref(bottomOperatePopup).show = false),
        h: common_vendor.o(handleChange),
        i: common_vendor.p(__spreadValues({}, common_vendor.unref(bottomOperatePopup)))
      } : {});
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-c0fbcb96"]]);
wx.createComponent(Component);
//# sourceMappingURL=chatList.js.map
