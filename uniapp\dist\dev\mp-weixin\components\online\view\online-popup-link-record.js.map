{"version": 3, "file": "online-popup-link-record.js", "sources": ["../../../../../../src/components/online/view/online-popup-link-record.vue", "../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvY29tcG9uZW50cy9vbmxpbmUvdmlldy9vbmxpbmUtcG9wdXAtbGluay1yZWNvcmQudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"Popup\">\r\n    <view @click=\"handleClick\">\r\n      <wd-input\r\n        :placeholder=\"`请选择${$attrs.label}`\"\r\n        type=\"text\"\r\n        readonly\r\n        v-model=\"showText\"\r\n        clearable\r\n        v-bind=\"$attrs\"\r\n      />\r\n    </view>\r\n    <LinkRecordsModal\r\n      v-if=\"reportModal.show\"\r\n       ref=\"lrmRef\"\r\n      :dictCode=\"dictCode\"\r\n      :dictTable=\"dictTable\"\r\n      :dictText=\"dictText\"\r\n      :multi=\"multi\"\r\n      :imageField=\"imageField\"\r\n      @close=\"handleClose\"\r\n      @change=\"handleChange\"\r\n    ></LinkRecordsModal>\r\n  </view>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, watch, useAttrs } from 'vue'\r\nimport { useToast } from 'wot-design-uni'\r\nimport LinkRecordsModal from './link-records-modal.vue'\r\nimport {http} from \"@/utils/http\";\r\ndefineOptions({\r\n  name: 'onlinePopupLinkRecord.vue',\r\n  options: {\r\n    styleIsolation: 'shared',\r\n  },\r\n})\r\nconst props = defineProps({\r\n  value: {\r\n    type: String,\r\n    required: false,\r\n  },\r\n  name: {\r\n    type: String,\r\n    required: false,\r\n  },\r\n  disabled: {\r\n    type: Boolean,\r\n    default: true,\r\n    required: false,\r\n  },\r\n  required: {\r\n    type: Boolean,\r\n    default: true,\r\n    required: false,\r\n  },\r\n  formSchema: {\r\n    type: Object,\r\n    required: true,\r\n  },\r\n})\r\nconst emit = defineEmits(['change', 'update:value','selected'])\r\n\r\nconst toast = useToast()\r\nconst lrmRef = ref()\r\nconst showText = ref('')\r\nconst selectVal = ref([])\r\nconst attrs: any = useAttrs()\r\nconst reportModal = reactive({\r\n  show: false,\r\n})\r\n//字典code\r\nconst dictCode = computed(() => props.formSchema?.dictCode)\r\n//字典table\r\nconst dictTable = computed(() => props.formSchema?.dictTable)\r\n//字典文本\r\nconst dictText = computed(() => props.formSchema?.dictText)\r\n//是否多选\r\nconst multi = computed(() => {\r\n  if(props.formSchema?.fieldExtendJson){\r\n    const extendJson = JSON.parse(props.formSchema.fieldExtendJson)\r\n    return extendJson?.multiSelect\r\n  }\r\n  return false\r\n})\r\n//图片字段\r\nconst imageField = computed(() => {\r\n  if(props.formSchema?.fieldExtendJson){\r\n    const extendJson = JSON.parse(props.formSchema.fieldExtendJson)\r\n    return extendJson?.imageField\r\n  }\r\n  return ''\r\n})\r\n//首次加载\r\nconst firstLoad = ref(true);\r\n/**\r\n * 监听value数值\r\n */\r\nwatch(\r\n  () => props.value,\r\n  (val) => {\r\n    val && loadValue()\r\n  },\r\n  { immediate: true },\r\n)\r\n//加载数据\r\nfunction loadValue(){\r\n  console.log('关联记录loadValue',firstLoad.value)\r\n  if(!firstLoad.value){\r\n    return\r\n  }\r\n  let linkTableSelectFields = dictCode.value + ',' + dictText.value;\r\n  let superQueryParams = [{\"field\":\"id\",\"rule\":\"in\",\"val\": props.value}];\r\n  let param = {\r\n    linkTableSelectFields,\r\n    superQueryMatchType:\"and\",\r\n    superQueryParams: encodeURI(JSON.stringify(superQueryParams))\r\n  };\r\n  let titleField = props.formSchema?.dictText && props.formSchema?.dictText.split(\",\")[0];\r\n  http.get(`/online/cgform/api/getData/${dictTable.value}`,param).then(res=>{\r\n    if(res.success){\r\n      let selectedList = res.result.records || [];\r\n      let labels = [];\r\n      let values = [];\r\n      selectedList.forEach(item=>{\r\n        if(item.id){\r\n          values.push(item.id);\r\n          labels.push(item[titleField]);\r\n        }\r\n      })\r\n      showText.value = labels.join(',');\r\n      selectVal.value = values;\r\n      emit('selected', selectedList,props.name);\r\n    }\r\n  })\r\n  firstLoad.value = false;\r\n}\r\n\r\n//回显数值\r\nfunction callBack(rows) {\r\n  //匹配popup设置的回调值\r\n  let values = []\r\n  let labels = []\r\n  let titleField = props.formSchema?.dictText && props.formSchema?.dictText.split(\",\")[0];\r\n  rows.forEach(item=>{\r\n    if(item.id){\r\n      values.push(item.id);\r\n      labels.push(item[titleField]);\r\n    }\r\n  })\r\n  showText.value = labels.join(',')\r\n  selectVal.value = values\r\n  emit('selected', rows,props.name)\r\n  emit('change', values.join(','))\r\n  emit('update:value', values.join(','))\r\n}\r\n//点击事件\r\nconst handleClick = () => {\r\n  if (!attrs.disabled) {\r\n    reportModal.show = true\r\n    //lrmRef.value.beforeOpen(selectVal.value)\r\n  }\r\n}\r\n//关闭事件\r\nconst handleClose = () => {\r\n  reportModal.show = false\r\n}\r\nconst handleChange = (data) => {\r\n  console.log('选中的值：', data)\r\n  callBack(data)\r\n}\r\n</script>\r\n\r\n<style scoped></style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/components/online/view/online-popup-link-record.vue'\nwx.createComponent(Component)"], "names": ["useToast", "ref", "useAttrs", "reactive", "computed", "watch", "http", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA,MAAA,mBAA6B,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7B,UAAM,QAAQ;AAwBd,UAAM,OAAO;AAECA,kBAAS,SAAA;AACvB,UAAM,SAASC,cAAAA,IAAI;AACb,UAAA,WAAWA,kBAAI,EAAE;AACjB,UAAA,YAAYA,cAAI,IAAA,EAAE;AACxB,UAAM,QAAaC,cAAAA,SAAS;AAC5B,UAAM,cAAcC,cAAAA,SAAS;AAAA,MAC3B,MAAM;AAAA,IAAA,CACP;AAED,UAAM,WAAWC,cAAA,SAAS,MAAM;;AAAA,yBAAM,eAAN,mBAAkB;AAAA,KAAQ;AAE1D,UAAM,YAAYA,cAAA,SAAS,MAAM;;AAAA,yBAAM,eAAN,mBAAkB;AAAA,KAAS;AAE5D,UAAM,WAAWA,cAAA,SAAS,MAAM;;AAAA,yBAAM,eAAN,mBAAkB;AAAA,KAAQ;AAEpD,UAAA,QAAQA,cAAAA,SAAS,MAAM;;AACxB,WAAA,WAAM,eAAN,mBAAkB,iBAAgB;AACnC,cAAM,aAAa,KAAK,MAAM,MAAM,WAAW,eAAe;AAC9D,eAAO,yCAAY;AAAA,MAAA;AAEd,aAAA;AAAA,IAAA,CACR;AAEK,UAAA,aAAaA,cAAAA,SAAS,MAAM;;AAC7B,WAAA,WAAM,eAAN,mBAAkB,iBAAgB;AACnC,cAAM,aAAa,KAAK,MAAM,MAAM,WAAW,eAAe;AAC9D,eAAO,yCAAY;AAAA,MAAA;AAEd,aAAA;AAAA,IAAA,CACR;AAEK,UAAA,YAAYH,kBAAI,IAAI;AAI1BI,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,QAAQ;AACP,eAAO,UAAU;AAAA,MACnB;AAAA,MACA,EAAE,WAAW,KAAK;AAAA,IACpB;AAEA,aAAS,YAAW;;AACV,cAAA,IAAI,iBAAgB,UAAU,KAAK;AACxC,UAAA,CAAC,UAAU,OAAM;AAClB;AAAA,MAAA;AAEF,UAAI,wBAAwB,SAAS,QAAQ,MAAM,SAAS;AACxD,UAAA,mBAAmB,CAAC,EAAC,SAAQ,MAAK,QAAO,MAAK,OAAO,MAAM,OAAM;AACrE,UAAI,QAAQ;AAAA,QACV;AAAA,QACA,qBAAoB;AAAA,QACpB,kBAAkB,UAAU,KAAK,UAAU,gBAAgB,CAAC;AAAA,MAC9D;AACI,UAAA,eAAa,WAAM,eAAN,mBAAkB,eAAY,WAAM,eAAN,mBAAkB,SAAS,MAAM,KAAK;AAChFC,iBAAAA,KAAA,IAAI,8BAA8B,UAAU,KAAK,IAAG,KAAK,EAAE,KAAK,CAAK,QAAA;AACxE,YAAG,IAAI,SAAQ;AACb,cAAI,eAAe,IAAI,OAAO,WAAW,CAAC;AAC1C,cAAI,SAAS,CAAC;AACd,cAAI,SAAS,CAAC;AACd,uBAAa,QAAQ,CAAM,SAAA;AACzB,gBAAG,KAAK,IAAG;AACF,qBAAA,KAAK,KAAK,EAAE;AACZ,qBAAA,KAAK,KAAK,UAAU,CAAC;AAAA,YAAA;AAAA,UAC9B,CACD;AACQ,mBAAA,QAAQ,OAAO,KAAK,GAAG;AAChC,oBAAU,QAAQ;AACb,eAAA,YAAY,cAAa,MAAM,IAAI;AAAA,QAAA;AAAA,MAC1C,CACD;AACD,gBAAU,QAAQ;AAAA,IAAA;AAIpB,aAAS,SAAS,MAAM;;AAEtB,UAAI,SAAS,CAAC;AACd,UAAI,SAAS,CAAC;AACV,UAAA,eAAa,WAAM,eAAN,mBAAkB,eAAY,WAAM,eAAN,mBAAkB,SAAS,MAAM,KAAK;AACrF,WAAK,QAAQ,CAAM,SAAA;AACjB,YAAG,KAAK,IAAG;AACF,iBAAA,KAAK,KAAK,EAAE;AACZ,iBAAA,KAAK,KAAK,UAAU,CAAC;AAAA,QAAA;AAAA,MAC9B,CACD;AACQ,eAAA,QAAQ,OAAO,KAAK,GAAG;AAChC,gBAAU,QAAQ;AACb,WAAA,YAAY,MAAK,MAAM,IAAI;AAChC,WAAK,UAAU,OAAO,KAAK,GAAG,CAAC;AAC/B,WAAK,gBAAgB,OAAO,KAAK,GAAG,CAAC;AAAA,IAAA;AAGvC,UAAM,cAAc,MAAM;AACpB,UAAA,CAAC,MAAM,UAAU;AACnB,oBAAY,OAAO;AAAA,MAAA;AAAA,IAGvB;AAEA,UAAM,cAAc,MAAM;AACxB,kBAAY,OAAO;AAAA,IACrB;AACM,UAAA,eAAe,CAAC,SAAS;AACrB,cAAA,IAAI,SAAS,IAAI;AACzB,eAAS,IAAI;AAAA,IACf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzKA,GAAG,gBAAgBC,SAAS;"}