{"version": 3, "file": "wd-badge.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-badge/wd-badge.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1iYWRnZS93ZC1iYWRnZS52dWU"], "sourcesContent": ["<template>\n  <view :class=\"['wd-badge', customClass]\" :style=\"customStyle\">\n    <slot></slot>\n    <view\n      v-if=\"shouldShowBadge\"\n      :class=\"['wd-badge__content', 'is-fixed', type ? 'wd-badge__content--' + type : '', isDot ? 'is-dot' : '']\"\n      :style=\"contentStyle\"\n    >\n      {{ content }}\n    </view>\n  </view>\n</template>\n<script lang=\"ts\">\nexport default {\n  name: 'wd-badge',\n  options: {\n    addGlobalClass: true,\n    virtualHost: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n<script lang=\"ts\" setup>\nimport { computed, type CSSProperties } from 'vue'\nimport { badgeProps } from './types'\nimport { addUnit, isDef, isNumber, objToStyle } from '../common/util'\n\nconst props = defineProps(badgeProps)\nconst content = computed(() => {\n  const { modelValue, max, isDot } = props\n  if (isDot) return ''\n  let value = modelValue\n  if (value && max && isNumber(value) && !Number.isNaN(value) && !Number.isNaN(max)) {\n    value = max < value ? `${max}+` : value\n  }\n  return value\n})\n\nconst contentStyle = computed(() => {\n  const style: CSSProperties = {}\n  if (isDef(props.bgColor)) {\n    style.backgroundColor = props.bgColor\n  }\n\n  if (isDef(props.top)) {\n    style.top = addUnit(props.top)\n  }\n\n  if (isDef(props.right)) {\n    style.right = addUnit(props.right)\n  }\n  return objToStyle(style)\n})\n\n// 是否展示徽标数字\nconst shouldShowBadge = computed(() => !props.hidden && (content.value || (content.value === 0 && props.showZero) || props.isDot))\n</script>\n\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-badge/wd-badge.vue'\nwx.createComponent(Component)"], "names": ["computed", "isNumber", "isDef", "addUnit", "objToStyle"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAaA,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,gBAAgB;AAAA,EAAA;AAEpB;;;;AAOA,UAAM,QAAQ;AACR,UAAA,UAAUA,cAAAA,SAAS,MAAM;AAC7B,YAAM,EAAE,YAAY,KAAK,MAAU,IAAA;AAC/B,UAAA;AAAc,eAAA;AAClB,UAAI,QAAQ;AACZ,UAAI,SAAS,OAAOC,uBAAS,KAAK,KAAK,CAAC,OAAO,MAAM,KAAK,KAAK,CAAC,OAAO,MAAM,GAAG,GAAG;AACjF,gBAAQ,MAAM,QAAQ,GAAG,GAAG,MAAM;AAAA,MAAA;AAE7B,aAAA;AAAA,IAAA,CACR;AAEK,UAAA,eAAeD,cAAAA,SAAS,MAAM;AAClC,YAAM,QAAuB,CAAC;AAC1B,UAAAE,cAAA,MAAM,MAAM,OAAO,GAAG;AACxB,cAAM,kBAAkB,MAAM;AAAA,MAAA;AAG5B,UAAAA,cAAA,MAAM,MAAM,GAAG,GAAG;AACd,cAAA,MAAMC,sBAAQ,MAAM,GAAG;AAAA,MAAA;AAG3B,UAAAD,cAAA,MAAM,MAAM,KAAK,GAAG;AAChB,cAAA,QAAQC,sBAAQ,MAAM,KAAK;AAAA,MAAA;AAEnC,aAAOC,cAAAA,WAAW,KAAK;AAAA,IAAA,CACxB;AAGD,UAAM,kBAAkBJ,cAAAA,SAAS,MAAM,CAAC,MAAM,WAAW,QAAQ,SAAU,QAAQ,UAAU,KAAK,MAAM,YAAa,MAAM,MAAM;;;;;;;;;;;;;;;;;ACtDjI,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}