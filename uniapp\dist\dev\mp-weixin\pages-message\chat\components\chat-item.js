"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../common/vendor.js");
const store_user = require("../../../store/user.js");
const common_uitls = require("../../../common/uitls.js");
if (!Array) {
  const _easycom_wd_img2 = common_vendor.resolveComponent("wd-img");
  _easycom_wd_img2();
}
const _easycom_wd_img = () => "../../../node-modules/wot-design-uni/components/wd-img/wd-img.js";
if (!Math) {
  _easycom_wd_img();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, { name: "chat-item" }), {
  __name: "chat-item",
  props: {
    playMsgid: {
      type: String,
      default: ""
    },
    item: {
      type: Object,
      default: function() {
        return {
          sendTime: "",
          fromAvatar: "",
          fromUserName: "",
          msgData: ""
        };
      }
    }
  },
  emits: ["playVoice"],
  setup(__props, { emit: __emit }) {
    const userStore = store_user.useUserStore();
    const emit = __emit;
    const isMe = (item) => {
      return item.msgFrom == userStore.userInfo.userid;
    };
    const playVoice = (item) => {
      emit("playVoice", item);
    };
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: __props.item.sendTime && __props.item.sendTime.length
      }, __props.item.sendTime && __props.item.sendTime.length ? {
        b: common_vendor.t(__props.item.sendTime)
      } : {}, {
        c: __props.item.fromAvatar,
        d: common_vendor.t(__props.item.fromUserName),
        e: isMe(__props.item) ? 1 : "",
        f: ["text"].includes(__props.item.msgType)
      }, ["text"].includes(__props.item.msgType) ? {
        g: __props.item.msgData,
        h: isMe(__props.item) ? 1 : "",
        i: isMe(__props.item) ? 1 : ""
      } : ["image"].includes(__props.item.msgType) ? {
        k: common_vendor.p({
          width: "200",
          height: "200",
          ["enable-preview"]: true,
          radius: 10,
          src: common_vendor.unref(common_uitls.getFileAccessHttpUrl)(__props.item.msgData)
        })
      } : ["voice"].includes(__props.item.msgType) ? {
        m: common_vendor.t(__props.item.msgData.length),
        n: isMe(__props.item) ? 1 : "",
        o: __props.playMsgid == __props.item.id ? 1 : "",
        p: common_vendor.o(($event) => playVoice(__props.item))
      } : ["file"].includes(__props.item.msgType) ? {} : {}, {
        j: ["image"].includes(__props.item.msgType),
        l: ["voice"].includes(__props.item.msgType),
        q: ["file"].includes(__props.item.msgType),
        r: isMe(__props.item) ? 1 : "",
        s: !isMe(__props.item) ? 1 : "",
        t: isMe(__props.item) ? 1 : ""
      });
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-adacc9d7"]]);
wx.createComponent(Component);
//# sourceMappingURL=chat-item.js.map
