{"version": 3, "file": "form.js", "sources": ["../../../../../src/pages-data/vital-signs/form.vue", "../../../../../uniPage:/cGFnZXMtZGF0YVx2aXRhbC1zaWduc1xmb3JtLnZ1ZQ"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n    <PageLayout> <template #navbar>\r\n            <NavBar :title=\"mode === 'add' ? '新增' : '编辑'\" :showBack=\"true\" />\r\n        </template>\r\n\r\n        <scroll-view class=\"page-scroll-view\" scroll-y=\"true\">\r\n            <view class=\"form-container\">\r\n                <view class=\"form-header\">\r\n                    <text class=\"form-title\">日常体征监测</text>\r\n                </view>\r\n\r\n                <view class=\"form-section\">\r\n                    <view class=\"form-item\">\r\n                        <text class=\"form-label\"><text class=\"required\">*</text>1.血压</text>\r\n                        <view class=\"inline-form-item\">\r\n                            <input class=\"inline-form-input\" v-model=\"formData.bloodPressure\"\r\n                                placeholder=\"请输入血压（mmHg）\" />\r\n                        </view>\r\n                    </view>\r\n\r\n                    <view class=\"form-item\">\r\n                        <text class=\"form-label\"><text class=\"required\">*</text>2.心率</text>\r\n                        <view class=\"inline-form-item\">\r\n                            <input class=\"inline-form-input\" v-model=\"formData.heartRate\" placeholder=\"请输入心率（bpm）\" />\r\n                        </view>\r\n                    </view>\r\n\r\n                    <view class=\"form-item\">\r\n                        <text class=\"form-label\"><text class=\"required\">*</text>3.体重</text>\r\n                        <view class=\"inline-form-item\">\r\n                            <input class=\"inline-form-input\" v-model=\"formData.weight\" placeholder=\"请输入体重（kg）\" />\r\n                        </view>\r\n                    </view>\r\n\r\n                </view> \r\n                <!-- 提交按钮 -->\r\n                <view class=\"submit-section\">\r\n                    <button class=\"submit-btn\" @click=\"submitForm\">{{ mode === 'add' ? '提交' : '保存' }}</button>\r\n                </view>\r\n            </view>\r\n        </scroll-view>\r\n    </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { ref, computed, onMounted, watch } from 'vue'\r\nimport { fastLerp } from 'zrender/lib/tool/color'\r\nimport { useUserStore } from '@/store/user'\r\n\r\nconst userStore = useUserStore()\r\n\r\ndefineOptions({\r\n    name: 'vitalSignsForm',\r\n})\r\n// 获取页面参数\r\nconst query = ref<any>({})\r\nonMounted(() => {\r\n    const pages = getCurrentPages()\r\n    const currentPage = pages[pages.length - 1]\r\n    query.value = (currentPage as any).options || {}\r\n\r\n    // 如果是查看模式，加载数据\r\n    if (query.value.mode === 'view' && query.value.id) {\r\n        loadFormData(query.value.id)\r\n    }\r\n})\r\n\r\n// 表单模式：add 或 view\r\nconst mode = computed(() => query.value.mode || 'add')\r\nconst isViewMode = computed(() => mode.value === 'view')\r\n\r\n// 表单数据\r\nconst formData = ref({\r\n    bloodPressure: '',\r\n    heartRate: '',\r\n    weight: ''\r\n})\r\n\r\n// 加载表单数据（根据ID获取数据）\r\nconst loadFormData = (id: string) => {\r\n    console.log('开始加载体征详情数据，ID:', id)\r\n\r\n    // 显示加载提示\r\n    uni.showLoading({\r\n        title: '加载中...'\r\n    })\r\n\r\n    // 根据您提供的API路径格式构建URL\r\n    const url = `${import.meta.env.VITE_SERVER_BASEURL}/patient/${id}/dailysigns`\r\n    console.log('请求详情URL:', url)\r\n\r\n    uni.request({\r\n        url,\r\n        method: 'GET',\r\n        success: (res: any) => {\r\n            const response = res.data\r\n            console.log('详情接口返回数据:', response)\r\n\r\n            if (response && response.code === 200) {\r\n                // 解析返回的详细数据并填充表单\r\n                if (response.result) {\r\n                    formData.value = {\r\n                        bloodPressure: response.result.bloodPressure || '',\r\n                        heartRate: response.result.heartRate || '',\r\n                        weight: response.result.weight || ''\r\n                    }\r\n                    console.log('解析后的详情数据:', formData.value)\r\n                } else {\r\n                    console.log('没有找到详情数据')\r\n                    uni.showToast({\r\n                        title: '未找到记录详情',\r\n                        icon: 'none'\r\n                    })\r\n                }\r\n            } else {\r\n                uni.showToast({\r\n                    title: response.msg || '获取详情失败',\r\n                    icon: 'none'\r\n                })\r\n            }\r\n        },\r\n        fail: (err: any) => {\r\n            console.error('获取体征详情失败:', err)\r\n            uni.showToast({\r\n                title: '网络异常，请稍后重试',\r\n                icon: 'none'\r\n            })\r\n        },\r\n        complete: () => {\r\n            uni.hideLoading()\r\n        }\r\n    })\r\n}\r\n\r\n// 提交表单\r\nconst submitForm = () => {\r\n\r\n    console.log('提交表单数据:', formData.value)\r\n\r\n    // 表单验证\r\n    if (!formData.value.bloodPressure) {\r\n        uni.showToast({\r\n            title: '请输入血压',\r\n            icon: 'none'\r\n        })\r\n        return\r\n    }\r\n\r\n    if (!formData.value.heartRate) {\r\n        uni.showToast({\r\n            title: '请输入心率',\r\n            icon: 'none'\r\n        })\r\n        return\r\n    }\r\n\r\n    if (!formData.value.weight) {\r\n        uni.showToast({\r\n            title: '请输入体重',\r\n            icon: 'none'\r\n        })\r\n        return\r\n    }\r\n\r\n    // 显示加载提示\r\n    uni.showLoading({\r\n        title: mode.value === 'add' ? '提交中...' : '保存中...'\r\n    })\r\n    const requestData = {\r\n        bloodPressure: formData.value.bloodPressure,\r\n        heartRate: formData.value.heartRate,\r\n        weight: formData.value.weight,\r\n        userId: userStore.userInfo.userid\r\n    }\r\n\r\n    // 如果是查看模式，并且有id，则添加id到请求数据中\r\n    if (mode.value === 'view') {\r\n        requestData.id = query.value.id;\r\n        requestData.updateUserId = userStore.userInfo.userid\r\n    }\r\n\r\n    console.log('formData为：', formData.value)\r\n    console.log('requestData为', requestData)\r\n\r\n    // 调用保存接口\r\n    uni.request({\r\n        url: `${import.meta.env.VITE_SERVER_BASEURL}/patient/savedailysigns`,\r\n        method: 'POST',\r\n        data: requestData,\r\n        success: (res) => {\r\n            uni.hideLoading(); if (res.data?.success) {\r\n                uni.showModal({\r\n                    title: mode.value === 'add' ? '提交成功' : '保存成功',\r\n                    showCancel: false,\r\n                    success: () => {\r\n                        uni.navigateBack();\r\n                    }\r\n                });\r\n            } else {\r\n                const errorMsg = res.data?.message || '提交失败，未知错误';\r\n                uni.showModal({\r\n                    title: mode.value === 'add' ? '提交失败' : '保存失败',\r\n                    content: errorMsg,\r\n                    showCancel: false\r\n                });\r\n            }\r\n        },\r\n        fail: (err) => {\r\n            uni.hideLoading();\r\n            const errorMsg = err.errMsg || '网络错误，请稍后重试';\r\n            uni.showModal({\r\n                title: '提交失败',\r\n                content: errorMsg,\r\n                showCancel: false\r\n            });\r\n        }\r\n    })\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-scroll-view {\r\n    height: calc(100vh - 44px);\r\n    /* 减去导航栏高度 */\r\n    width: 100%;\r\n}\r\n\r\n.form-container {\r\n    padding: 20rpx;\r\n    padding-bottom: 120rpx;\r\n    /* 增加底部内边距，防止内容被遮挡 */\r\n\r\n    .form-header {\r\n        margin-bottom: 20rpx;\r\n        text-align: center;\r\n        /* 标题居中 */\r\n\r\n        .form-title {\r\n            font-size: 32rpx;\r\n            font-weight: bold;\r\n            color: #333;\r\n        }\r\n    }\r\n\r\n    .form-section {\r\n        background-color: #FFFFFF;\r\n        border-radius: 12rpx;\r\n        padding: 20rpx;\r\n        margin-bottom: 20rpx;\r\n\r\n        .form-item {\r\n            margin-bottom: 30rpx;\r\n\r\n            .form-label {\r\n                display: block;\r\n                font-size: 28rpx;\r\n                color: #333;\r\n                margin-bottom: 20rpx;\r\n            }\r\n\r\n            .inline-form-item {\r\n                display: flex;\r\n                align-items: center;\r\n                margin-bottom: 20rpx;\r\n\r\n                .inline-form-label {\r\n                    font-size: 28rpx;\r\n                    color: #333;\r\n                    min-width: 160rpx;\r\n                }\r\n\r\n                .inline-form-input {\r\n                    flex: 1;\r\n                    background-color: #F7F7F7;\r\n                    border-radius: 8rpx;\r\n                    padding: 20rpx;\r\n                    font-size: 28rpx;\r\n                    color: #333;\r\n                    box-sizing: border-box;\r\n                    min-height: 80rpx;\r\n\r\n                    &:disabled {\r\n                        background-color: #F5F5F5;\r\n                        color: #666;\r\n                    }\r\n                }\r\n            }\r\n\r\n            .date-picker {\r\n                display: flex;\r\n                justify-content: space-between;\r\n                align-items: center;\r\n                background-color: #F7F7F7;\r\n                border-radius: 8rpx;\r\n                padding: 20rpx;\r\n                font-size: 28rpx;\r\n                color: #333;\r\n                box-sizing: border-box;\r\n                min-height: 80rpx;\r\n\r\n                .placeholder {\r\n                    color: #999;\r\n                }\r\n            }\r\n\r\n            .form-input,\r\n            .form-textarea {\r\n                width: 100%;\r\n                background-color: #F7F7F7;\r\n                border-radius: 8rpx;\r\n                padding: 20rpx;\r\n                font-size: 28rpx;\r\n                color: #333;\r\n                box-sizing: border-box;\r\n                min-height: 80rpx;\r\n\r\n                &:disabled {\r\n                    background-color: #F5F5F5;\r\n                    color: #666;\r\n                }\r\n            }\r\n\r\n            .form-textarea {\r\n                height: 180rpx;\r\n            }\r\n\r\n            .radio-group {\r\n                display: flex;\r\n                flex-direction: column;\r\n                gap: 30rpx;\r\n                /* 增加间距 */\r\n\r\n                .radio-row {\r\n                    display: flex;\r\n                    gap: 150rpx;\r\n                    /* 增加间距 */\r\n                    margin-left: 20rpx;\r\n                    /* 增加左边距，实现对齐 */\r\n                    justify-content: flex-start;\r\n                    /* 添加左对齐 */\r\n                }\r\n\r\n                .radio-item {\r\n                    display: flex;\r\n                    align-items: center;\r\n                    min-width: 180rpx;\r\n                    width: 240rpx;\r\n                    /* 确保宽度一致，增大宽度值以容纳更长的文本 */\r\n\r\n                    .radio-btn {\r\n                        width: 36rpx;\r\n                        height: 36rpx;\r\n                        border-radius: 50%;\r\n                        border: 2rpx solid #CCCCCC;\r\n                        margin-right: 10rpx;\r\n                        position: relative;\r\n\r\n                        &.checked {\r\n                            border-color: #07C160;\r\n\r\n                            &:after {\r\n                                content: '';\r\n                                position: absolute;\r\n                                width: 24rpx;\r\n                                height: 24rpx;\r\n                                background-color: #07C160;\r\n                                border-radius: 50%;\r\n                                top: 50%;\r\n                                left: 50%;\r\n                                transform: translate(-50%, -50%);\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    text {\r\n                        font-size: 28rpx;\r\n                        color: #333;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    // 添加省市区选择器样式\r\n    .area-picker-container {\r\n        display: flex;\r\n        flex-direction: column;\r\n        margin-bottom: 20rpx;\r\n\r\n        .inline-form-label {\r\n            font-size: 28rpx;\r\n            color: #333;\r\n            margin-bottom: 10rpx;\r\n            display: block;\r\n        }\r\n\r\n        .area-picker-wrapper {\r\n            width: 100%;\r\n            background-color: #F7F7F7;\r\n            border-radius: 8rpx;\r\n            box-sizing: border-box;\r\n            min-height: 80rpx;\r\n\r\n            :deep(.wd-picker__value) {\r\n                text-align: left;\r\n                color: #333;\r\n            }\r\n\r\n            :deep(.wd-picker__action) {\r\n                display: none;\r\n            }\r\n        }\r\n    }\r\n\r\n    // 行内选择器样式\r\n    .inline-picker-wrapper {\r\n        flex: 1;\r\n        width: 100%;\r\n    }\r\n\r\n    .required {\r\n        color: #FF0000;\r\n        margin-right: 4rpx;\r\n    }\r\n\r\n    .submit-section {\r\n        margin-top: 40rpx;\r\n        margin-bottom: 60rpx;\r\n        /* 增加底部间距 */\r\n\r\n        .submit-btn {\r\n            width: 100%;\r\n            background-color: #07C160;\r\n            color: #FFFFFF;\r\n            border-radius: 8rpx;\r\n            font-size: 32rpx;\r\n            padding: 20rpx 0;\r\n        }\r\n    }\r\n}\r\n\r\n.checkbox-group {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 30rpx;\r\n\r\n    .checkbox-row {\r\n        display: flex;\r\n        gap: 150rpx;\r\n        margin-left: 20rpx;\r\n        justify-content: flex-start;\r\n    }\r\n\r\n    .checkbox-item {\r\n        display: flex;\r\n        align-items: center;\r\n        min-width: 180rpx;\r\n        width: 240rpx;\r\n\r\n        .checkbox-btn {\r\n            width: 36rpx;\r\n            height: 36rpx;\r\n            border-radius: 4rpx;\r\n            border: 2rpx solid #CCCCCC;\r\n            margin-right: 10rpx;\r\n            position: relative;\r\n\r\n            &.checked {\r\n                border-color: #07C160;\r\n                background-color: #07C160;\r\n\r\n                &:after {\r\n                    content: '';\r\n                    position: absolute;\r\n                    width: 20rpx;\r\n                    height: 10rpx;\r\n                    border-left: 4rpx solid #FFFFFF;\r\n                    border-bottom: 4rpx solid #FFFFFF;\r\n                    top: 45%;\r\n                    left: 50%;\r\n                    transform: translate(-50%, -50%) rotate(-45deg);\r\n                }\r\n            }\r\n        }\r\n\r\n        text {\r\n            font-size: 28rpx;\r\n            color: #333;\r\n        }\r\n    }\r\n\r\n    .sub-form-item {\r\n        margin-left: 50rpx;\r\n        margin-top: -10rpx;\r\n        margin-bottom: 20rpx;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .sub-form-label {\r\n            font-size: 26rpx;\r\n            color: #666;\r\n            min-width: 180rpx;\r\n        }\r\n\r\n        .sub-form-input {\r\n            flex: 1;\r\n            background-color: #F7F7F7;\r\n            border-radius: 8rpx;\r\n            padding: 15rpx;\r\n            font-size: 26rpx;\r\n            color: #333;\r\n            min-height: 70rpx;\r\n            width: 100%;\r\n            box-sizing: border-box;\r\n        }\r\n\r\n        .picker {\r\n            flex: 1;\r\n            display: flex;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n            background-color: #F7F7F7;\r\n            border-radius: 8rpx;\r\n            padding: 15rpx;\r\n            font-size: 26rpx;\r\n            color: #333;\r\n            min-height: 70rpx;\r\n            width: 100%;\r\n            box-sizing: border-box;\r\n        }\r\n    }\r\n\r\n    /* 添加新的样式以支持两列布局 */\r\n    .sub-form-items-row {\r\n        display: flex;\r\n        margin-left: 50rpx;\r\n        gap: 20rpx;\r\n        margin-top: -10rpx;\r\n        margin-bottom: 20rpx;\r\n\r\n        .sub-form-item {\r\n            flex: 1;\r\n            width: 50%;\r\n            min-width: calc(50% - 10rpx);\r\n            /* 确保即使只有一个元素也占据一半宽度 */\r\n            margin: 0;\r\n            display: flex;\r\n            align-items: center;\r\n\r\n            .picker-container {\r\n                width: 100%;\r\n                flex: 1;\r\n                display: block;\r\n            }\r\n\r\n            .temp-picker {\r\n                width: 100%;\r\n                flex: 1;\r\n                display: block;\r\n            }\r\n\r\n            .sub-form-input {\r\n                flex: 1;\r\n                background-color: #F7F7F7;\r\n                border-radius: 8rpx;\r\n                padding: 15rpx;\r\n                font-size: 26rpx;\r\n                color: #333;\r\n                min-height: 70rpx;\r\n                width: 100%;\r\n                box-sizing: border-box;\r\n            }\r\n\r\n            .picker {\r\n                flex: 1;\r\n                display: flex;\r\n                justify-content: space-between;\r\n                align-items: center;\r\n                background-color: #F7F7F7;\r\n                border-radius: 8rpx;\r\n                padding: 15rpx;\r\n                font-size: 26rpx;\r\n                color: #333;\r\n                min-height: 70rpx;\r\n                width: 100%;\r\n                box-sizing: border-box;\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-data/vital-signs/form.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "ref", "onMounted", "computed", "uni"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqDA,UAAM,YAAYA,WAAAA,aAAa;AAMzB,UAAA,QAAQC,cAAS,IAAA,EAAE;AACzBC,kBAAAA,UAAU,MAAM;AACZ,YAAM,QAAQ,gBAAgB;AAC9B,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AACpC,YAAA,QAAS,YAAoB,WAAW,CAAC;AAG/C,UAAI,MAAM,MAAM,SAAS,UAAU,MAAM,MAAM,IAAI;AAClC,qBAAA,MAAM,MAAM,EAAE;AAAA,MAAA;AAAA,IAC/B,CACH;AAGD,UAAM,OAAOC,cAAAA,SAAS,MAAM,MAAM,MAAM,QAAQ,KAAK;AAClCA,2BAAS,MAAM,KAAK,UAAU,MAAM;AAGvD,UAAM,WAAWF,cAAAA,IAAI;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,QAAQ;AAAA,IAAA,CACX;AAGK,UAAA,eAAe,CAAC,OAAe;AACzB,cAAA,IAAI,kBAAkB,EAAE;AAGhCG,oBAAAA,MAAI,YAAY;AAAA,QACZ,OAAO;AAAA,MAAA,CACV;AAGD,YAAM,MAAM,GAAG,6BAAmC,YAAY,EAAE;AACxD,cAAA,IAAI,YAAY,GAAG;AAE3BA,oBAAAA,MAAI,QAAQ;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,QACR,SAAS,CAAC,QAAa;AACnB,gBAAM,WAAW,IAAI;AACb,kBAAA,IAAI,aAAa,QAAQ;AAE7B,cAAA,YAAY,SAAS,SAAS,KAAK;AAEnC,gBAAI,SAAS,QAAQ;AACjB,uBAAS,QAAQ;AAAA,gBACb,eAAe,SAAS,OAAO,iBAAiB;AAAA,gBAChD,WAAW,SAAS,OAAO,aAAa;AAAA,gBACxC,QAAQ,SAAS,OAAO,UAAU;AAAA,cACtC;AACQ,sBAAA,IAAI,aAAa,SAAS,KAAK;AAAA,YAAA,OACpC;AACH,sBAAQ,IAAI,UAAU;AACtBA,4BAAAA,MAAI,UAAU;AAAA,gBACV,OAAO;AAAA,gBACP,MAAM;AAAA,cAAA,CACT;AAAA,YAAA;AAAA,UACL,OACG;AACHA,0BAAAA,MAAI,UAAU;AAAA,cACV,OAAO,SAAS,OAAO;AAAA,cACvB,MAAM;AAAA,YAAA,CACT;AAAA,UAAA;AAAA,QAET;AAAA,QACA,MAAM,CAAC,QAAa;AACR,kBAAA,MAAM,aAAa,GAAG;AAC9BA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACT;AAAA,QACL;AAAA,QACA,UAAU,MAAM;AACZA,wBAAAA,MAAI,YAAY;AAAA,QAAA;AAAA,MACpB,CACH;AAAA,IACL;AAGA,UAAM,aAAa,MAAM;AAEb,cAAA,IAAI,WAAW,SAAS,KAAK;AAGjC,UAAA,CAAC,SAAS,MAAM,eAAe;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACT;AACD;AAAA,MAAA;AAGA,UAAA,CAAC,SAAS,MAAM,WAAW;AAC3BA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACT;AACD;AAAA,MAAA;AAGA,UAAA,CAAC,SAAS,MAAM,QAAQ;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACT;AACD;AAAA,MAAA;AAIJA,oBAAAA,MAAI,YAAY;AAAA,QACZ,OAAO,KAAK,UAAU,QAAQ,WAAW;AAAA,MAAA,CAC5C;AACD,YAAM,cAAc;AAAA,QAChB,eAAe,SAAS,MAAM;AAAA,QAC9B,WAAW,SAAS,MAAM;AAAA,QAC1B,QAAQ,SAAS,MAAM;AAAA,QACvB,QAAQ,UAAU,SAAS;AAAA,MAC/B;AAGI,UAAA,KAAK,UAAU,QAAQ;AACX,oBAAA,KAAK,MAAM,MAAM;AACjB,oBAAA,eAAe,UAAU,SAAS;AAAA,MAAA;AAG1C,cAAA,IAAI,cAAc,SAAS,KAAK;AAChC,cAAA,IAAI,gBAAgB,WAAW;AAGvCA,oBAAAA,MAAI,QAAQ;AAAA,QACR,KAAK,GAAG,6BAAmC;AAAA,QAC3C,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,SAAS,CAAC,QAAQ;;AACdA,wBAAAA,MAAI,YAAY;AAAO,eAAA,SAAI,SAAJ,mBAAU,SAAS;AACtCA,0BAAAA,MAAI,UAAU;AAAA,cACV,OAAO,KAAK,UAAU,QAAQ,SAAS;AAAA,cACvC,YAAY;AAAA,cACZ,SAAS,MAAM;AACXA,8BAAAA,MAAI,aAAa;AAAA,cAAA;AAAA,YACrB,CACH;AAAA,UAAA,OACE;AACG,kBAAA,aAAW,SAAI,SAAJ,mBAAU,YAAW;AACtCA,0BAAAA,MAAI,UAAU;AAAA,cACV,OAAO,KAAK,UAAU,QAAQ,SAAS;AAAA,cACvC,SAAS;AAAA,cACT,YAAY;AAAA,YAAA,CACf;AAAA,UAAA;AAAA,QAET;AAAA,QACA,MAAM,CAAC,QAAQ;AACXA,wBAAAA,MAAI,YAAY;AACV,gBAAA,WAAW,IAAI,UAAU;AAC/BA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,UAAA,CACf;AAAA,QAAA;AAAA,MACL,CACH;AAAA,IACL;;;;;;;;;;;;;;;;;;;;AC5NA,GAAG,WAAW,eAAe;"}