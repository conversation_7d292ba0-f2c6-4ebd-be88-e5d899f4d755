{"version": 3, "file": "wd-pagination.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-pagination/wd-pagination.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1wYWdpbmF0aW9uL3dkLXBhZ2luYXRpb24udnVl"], "sourcesContent": ["<template>\n  <view :class=\"`wd-pager ${customClass}`\" :style=\"customStyle\" v-if=\"!(hideIfOnePage && totalPageNum === 1)\">\n    <view class=\"wd-pager__content\">\n      <wd-button :plain=\"modelValue > 1\" type=\"info\" size=\"small\" :disabled=\"modelValue <= 1\" custom-class=\"wd-pager__nav\" @click=\"sub\">\n        <text v-if=\"!showIcon\">{{ prevText || translate('prev') }}</text>\n        <wd-icon\n          v-else\n          :custom-class=\"`wd-pager__left wd-pager__icon ${modelValue <= 1 ? 'wd-pager__nav--disabled' : 'wd-pager__nav--active'}`\"\n          name=\"arrow-right\"\n        ></wd-icon>\n      </wd-button>\n      <view class=\"wd-pager__size\">\n        <text class=\"wd-pager__current\">{{ modelValue }}</text>\n        <text class=\"wd-pager__separator\">/</text>\n        <text>{{ totalPageNum }}</text>\n      </view>\n      <wd-button\n        :plain=\"modelValue < totalPageNum\"\n        type=\"info\"\n        size=\"small\"\n        :disabled=\"modelValue >= totalPageNum\"\n        custom-class=\"wd-pager__nav\"\n        @click=\"add\"\n      >\n        <text v-if=\"!showIcon\">{{ nextText || translate('next') }}</text>\n        <wd-icon\n          v-else\n          :custom-class=\"`wd-pager__icon ${modelValue >= totalPageNum ? 'wd-pager__nav--disabled' : 'wd-pager__nav--active'}`\"\n          name=\"arrow-right\"\n        ></wd-icon>\n      </wd-button>\n    </view>\n    <view class=\"wd-pager__message\" v-if=\"showMessage\">\n      <text>{{ translate('page', modelValue) }}，</text>\n      <text v-if=\"total\">{{ translate('total', total) }}，</text>\n      <text>{{ translate('size', pageSize) }}</text>\n    </view>\n  </view>\n</template>\n\n<script lang=\"ts\">\nexport default {\n  name: 'wd-pagination',\n  options: {\n    virtualHost: true,\n    addGlobalClass: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport wdIcon from '../wd-icon/wd-icon.vue'\nimport wdButton from '../wd-button/wd-button.vue'\nimport { ref, watch } from 'vue'\nimport { useTranslate } from '../composables/useTranslate'\nimport { paginationProps } from './types'\n\nconst { translate } = useTranslate('pagination')\n\nconst props = defineProps(paginationProps)\nconst emit = defineEmits(['change', 'update:modelValue'])\n\nconst totalPageNum = ref<number>(0) // 总页数\n\nwatch(\n  () => props.totalPage,\n  (newValue) => {\n    if (!totalPageNum.value && newValue) {\n      totalPageNum.value = newValue\n    }\n  },\n  { immediate: true, deep: true }\n)\n\nwatch(\n  () => props.total,\n  () => {\n    updateTotalPage()\n  },\n  { immediate: true, deep: true }\n)\n\nfunction add() {\n  const { modelValue } = props\n  if (modelValue > totalPageNum.value - 1) {\n    return\n  }\n  emit('change', { value: modelValue + 1 })\n  emit('update:modelValue', modelValue + 1)\n}\n\nfunction sub() {\n  const { modelValue } = props\n  if (modelValue < 2) {\n    return\n  }\n  emit('change', { value: modelValue - 1 })\n  emit('update:modelValue', modelValue - 1)\n}\n\nfunction updateTotalPage() {\n  const { total, pageSize } = props\n  totalPageNum.value = Math.ceil(total / pageSize)\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-pagination/wd-pagination.vue'\nwx.createComponent(Component)"], "names": ["useTranslate", "ref", "watch"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAoDA,MAAA,SAAmB,MAAA;AACnB,MAAA,WAAqB,MAAA;AAZrB,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,EAAA;AAEpB;;;;;AAUA,UAAM,EAAE,UAAA,IAAcA,cAAA,aAAa,YAAY;AAE/C,UAAM,QAAQ;AACd,UAAM,OAAO;AAEP,UAAA,eAAeC,kBAAY,CAAC;AAElCC,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,aAAa;AACR,YAAA,CAAC,aAAa,SAAS,UAAU;AACnC,uBAAa,QAAQ;AAAA,QAAA;AAAA,MAEzB;AAAA,MACA,EAAE,WAAW,MAAM,MAAM,KAAK;AAAA,IAChC;AAEAA,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,MAAM;AACY,wBAAA;AAAA,MAClB;AAAA,MACA,EAAE,WAAW,MAAM,MAAM,KAAK;AAAA,IAChC;AAEA,aAAS,MAAM;AACP,YAAA,EAAE,eAAe;AACnB,UAAA,aAAa,aAAa,QAAQ,GAAG;AACvC;AAAA,MAAA;AAEF,WAAK,UAAU,EAAE,OAAO,aAAa,GAAG;AACnC,WAAA,qBAAqB,aAAa,CAAC;AAAA,IAAA;AAG1C,aAAS,MAAM;AACP,YAAA,EAAE,eAAe;AACvB,UAAI,aAAa,GAAG;AAClB;AAAA,MAAA;AAEF,WAAK,UAAU,EAAE,OAAO,aAAa,GAAG;AACnC,WAAA,qBAAqB,aAAa,CAAC;AAAA,IAAA;AAG1C,aAAS,kBAAkB;AACnB,YAAA,EAAE,OAAO,SAAA,IAAa;AAC5B,mBAAa,QAAQ,KAAK,KAAK,QAAQ,QAAQ;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtGjD,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}