{"version": 3, "file": "wd-upload.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-upload/wd-upload.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC11cGxvYWQvd2QtdXBsb2FkLnZ1ZQ"], "sourcesContent": ["<template>\n  <view :class=\"['wd-upload', customClass]\" :style=\"customStyle\">\n    <!-- 预览列表 -->\n    <view :class=\"['wd-upload__preview', customPreviewClass]\" v-for=\"(file, index) in uploadFiles\" :key=\"index\">\n      <!-- 成功时展示图片 -->\n      <view class=\"wd-upload__status-content\">\n        <image v-if=\"isImage(file)\" :src=\"file.url\" :mode=\"imageMode\" class=\"wd-upload__picture\" @click=\"onPreviewImage(file)\" />\n        <template v-else-if=\"isVideo(file)\">\n          <view class=\"wd-upload__video\" v-if=\"file.thumb\" @click=\"onPreviewVideo(file)\">\n            <image :src=\"file.thumb\" :mode=\"imageMode\" class=\"wd-upload__picture\" />\n            <wd-icon name=\"play-circle-filled\" custom-class=\"wd-upload__video-paly\"></wd-icon>\n          </view>\n          <view v-else class=\"wd-upload__video\" @click=\"onPreviewVideo(file)\">\n\n\n\n\n\n            <video\n              :src=\"file.url\"\n              :title=\"file.name || '视频' + index\"\n              object-fit=\"contain\"\n              :controls=\"false\"\n              :poster=\"file.thumb\"\n              :autoplay=\"false\"\n              :show-center-play-btn=\"false\"\n              :show-fullscreen-btn=\"false\"\n              :show-play-btn=\"false\"\n              :show-loading=\"false\"\n              :show-progress=\"false\"\n              :show-mute-btn=\"false\"\n              :enable-progress-gesture=\"false\"\n              :enableNative=\"true\"\n              class=\"wd-upload__video\"\n            ></video>\n            <wd-icon name=\"play-circle-filled\" custom-class=\"wd-upload__video-paly\"></wd-icon>\n\n\n          </view>\n        </template>\n\n        <view v-else class=\"wd-upload__file\" @click=\"onPreviewFile(file)\">\n          <wd-icon name=\"file\" custom-class=\"wd-upload__file-icon\"></wd-icon>\n          <view class=\"wd-upload__file-name\">{{ file.name || file.url }}</view>\n        </view>\n      </view>\n\n      <view v-if=\"file[props.statusKey] !== 'success'\" class=\"wd-upload__mask wd-upload__status-content\">\n        <!-- loading时展示loading图标和进度 -->\n        <view v-if=\"file[props.statusKey] === 'loading'\" class=\"wd-upload__status-content\">\n          <wd-loading :type=\"loadingType\" :size=\"loadingSize\" :color=\"loadingColor\" />\n          <text class=\"wd-upload__progress-txt\">{{ file.percent }}%</text>\n        </view>\n        <!-- 失败时展示失败图标以及失败信息 -->\n        <view v-if=\"file[props.statusKey] === 'fail'\" class=\"wd-upload__status-content\">\n          <wd-icon name=\"close-outline\" custom-class=\"wd-upload__icon\"></wd-icon>\n          <text class=\"wd-upload__progress-txt\">{{ file.error || translate('error') }}</text>\n        </view>\n      </view>\n      <!-- 上传状态为上传中时不展示移除按钮 -->\n      <wd-icon\n        v-if=\"file[props.statusKey] !== 'loading' && !disabled\"\n        name=\"error-fill\"\n        custom-class=\"wd-upload__close\"\n        @click=\"removeFile(index)\"\n      ></wd-icon>\n      <!-- 自定义预览样式 -->\n      <slot name=\"preview-cover\" v-if=\"$slots['preview-cover']\" :file=\"file\" :index=\"index\"></slot>\n    </view>\n\n    <template v-if=\"showUpload\">\n      <view :class=\"['wd-upload__evoke-slot', customEvokeClass]\" v-if=\"$slots.default\" @click=\"handleChoose\">\n        <slot></slot>\n      </view>\n      <!-- 唤起项 -->\n      <view v-else @click=\"handleChoose\" :class=\"['wd-upload__evoke', disabled ? 'is-disabled' : '', customEvokeClass]\">\n        <!-- 唤起项图标 -->\n        <wd-icon class=\"wd-upload__evoke-icon\" name=\"fill-camera\"></wd-icon>\n        <!-- 有限制个数时确认是否展示限制个数 -->\n        <view v-if=\"limit && showLimitNum\" class=\"wd-upload__evoke-num\">（{{ uploadFiles.length }}/{{ limit }}）</view>\n      </view>\n    </template>\n  </view>\n  <wd-video-preview ref=\"videoPreview\"></wd-video-preview>\n</template>\n\n<script lang=\"ts\">\nexport default {\n  name: 'wd-upload',\n  options: {\n    addGlobalClass: true,\n    virtualHost: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport wdIcon from '../wd-icon/wd-icon.vue'\nimport wdVideoPreview from '../wd-video-preview/wd-video-preview.vue'\nimport wdLoading from '../wd-loading/wd-loading.vue'\n\nimport { computed, ref, watch } from 'vue'\nimport { context, isEqual, isImageUrl, isVideoUrl, isFunction, isDef, deepClone } from '../common/util'\nimport { useTranslate } from '../composables/useTranslate'\nimport { useUpload } from '../composables/useUpload'\nimport {\n  uploadProps,\n  type UploadFileItem,\n  type ChooseFile,\n  type UploadExpose,\n  type UploadErrorEvent,\n  type UploadChangeEvent,\n  type UploadSuccessEvent,\n  type UploadProgressEvent,\n  type UploadOversizeEvent,\n  type UploadRemoveEvent,\n  type UploadMethod\n} from './types'\nimport type { VideoPreviewInstance } from '../wd-video-preview/types'\n\nconst props = defineProps(uploadProps)\n\nconst emit = defineEmits<{\n  (e: 'fail', value: UploadErrorEvent): void\n  (e: 'change', value: UploadChangeEvent): void\n  (e: 'success', value: UploadSuccessEvent): void\n  (e: 'progress', value: UploadProgressEvent): void\n  (e: 'oversize', value: UploadOversizeEvent): void\n  (e: 'chooseerror', value: any): void\n  (e: 'remove', value: UploadRemoveEvent): void\n  (e: 'update:fileList', value: UploadFileItem[]): void\n}>()\n\ndefineExpose<UploadExpose>({\n  submit: () => startUploadFiles(),\n  abort: () => abort()\n})\n\nconst { translate } = useTranslate('upload')\n\nconst uploadFiles = ref<UploadFileItem[]>([])\n\nconst showUpload = computed(() => !props.limit || uploadFiles.value.length < props.limit)\n\nconst videoPreview = ref<VideoPreviewInstance>()\n\nconst { startUpload, abort, chooseFile, UPLOAD_STATUS } = useUpload()\n\nwatch(\n  () => props.fileList,\n  (val) => {\n    const { statusKey } = props\n    if (isEqual(val, uploadFiles.value)) return\n    const uploadFileList: UploadFileItem[] = val.map((item) => {\n      item[statusKey] = item[statusKey] || 'success'\n      item.response = item.response || ''\n      return { ...item, uid: context.id++ }\n    })\n    uploadFiles.value = uploadFileList\n  },\n  {\n    deep: true,\n    immediate: true\n  }\n)\n\nwatch(\n  () => props.limit,\n  (val) => {\n    if (val && val < uploadFiles.value.length) {\n      console.error('[wot-design]Error: props limit must less than fileList.length')\n    }\n  },\n  {\n    deep: true,\n    immediate: true\n  }\n)\n\nwatch(\n  () => props.beforePreview,\n  (fn) => {\n    if (fn && !isFunction(fn)) {\n      console.error('The type of beforePreview must be Function')\n    }\n  },\n  {\n    deep: true,\n    immediate: true\n  }\n)\n\nwatch(\n  () => props.onPreviewFail,\n  (fn) => {\n    if (fn && !isFunction(fn)) {\n      console.error('The type of onPreviewFail must be Function')\n    }\n  },\n  {\n    deep: true,\n    immediate: true\n  }\n)\n\nwatch(\n  () => props.beforeRemove,\n  (fn) => {\n    if (fn && !isFunction(fn)) {\n      console.error('The type of beforeRemove must be Function')\n    }\n  },\n  {\n    deep: true,\n    immediate: true\n  }\n)\n\nwatch(\n  () => props.beforeUpload,\n  (fn) => {\n    if (fn && !isFunction(fn)) {\n      console.error('The type of beforeUpload must be Function')\n    }\n  },\n  {\n    deep: true,\n    immediate: true\n  }\n)\n\nwatch(\n  () => props.beforeChoose,\n  (fn) => {\n    if (fn && !isFunction(fn)) {\n      console.error('The type of beforeChoose must be Function')\n    }\n  },\n  {\n    deep: true,\n    immediate: true\n  }\n)\n\nwatch(\n  () => props.buildFormData,\n  (fn) => {\n    if (fn && !isFunction(fn)) {\n      console.error('The type of buildFormData must be Function')\n    }\n  },\n  {\n    deep: true,\n    immediate: true\n  }\n)\n\nfunction emitFileList() {\n  emit('update:fileList', uploadFiles.value)\n}\n\n/**\n *  开始上传文件\n */\nfunction startUploadFiles() {\n  const { buildFormData, formData = {}, statusKey } = props\n  const { action, name, header = {}, accept, successStatus, uploadMethod } = props\n  const statusCode = isDef(successStatus) ? successStatus : 200\n\n  for (const uploadFile of uploadFiles.value) {\n    // 仅开始未上传的文件\n    if (uploadFile[statusKey] === UPLOAD_STATUS.PENDING) {\n      if (buildFormData) {\n        buildFormData({\n          file: uploadFile,\n          formData,\n          resolve: (formData: Record<string, any>) => {\n            formData &&\n              startUpload(uploadFile, {\n                action,\n                header,\n                name,\n                formData,\n                fileType: accept as 'image' | 'video' | 'audio',\n                statusCode,\n                statusKey,\n                uploadMethod,\n                onSuccess: handleSuccess,\n                onError: handleError,\n                onProgress: handleProgress\n              })\n          }\n        })\n      } else {\n        startUpload(uploadFile, {\n          action,\n          header,\n          name,\n          formData,\n          fileType: accept as 'image' | 'video' | 'audio',\n          statusCode,\n          statusKey,\n          uploadMethod,\n          onSuccess: handleSuccess,\n          onError: handleError,\n          onProgress: handleProgress\n        })\n      }\n    }\n  }\n}\n\n/**\n * 获取图片信息\n * @param img\n */\nfunction getImageInfo(img: string) {\n  return new Promise<UniApp.GetImageInfoSuccessData>((resolve, reject) => {\n    uni.getImageInfo({\n      src: img,\n      success: (res) => {\n        resolve(res)\n      },\n      fail: (error) => {\n        reject(error)\n      }\n    })\n  })\n}\n\n/**\n * @description 初始化文件数据\n * @param {Object} file 上传的文件\n */\nfunction initFile(file: ChooseFile, currentIndex?: number) {\n  const { statusKey } = props\n  // 状态初始化\n  const initState: UploadFileItem = {\n    uid: context.id++,\n    // 仅h5支持 name\n    name: file.name || '',\n    thumb: file.thumb || '',\n    [statusKey]: 'pending',\n    size: file.size || 0,\n    url: file.path,\n    percent: 0\n  }\n  if (typeof currentIndex === 'number') {\n    uploadFiles.value.splice(currentIndex, 1, initState)\n  } else {\n    uploadFiles.value.push(initState)\n  }\n  if (props.autoUpload) {\n    startUploadFiles()\n  }\n}\n\n/**\n * @description 上传失败捕获\n * @param {Object} err 错误返回信息\n * @param {Object} file 上传的文件\n */\nfunction handleError(err: Record<string, any>, file: UploadFileItem, formData: Record<string, any>) {\n  const { statusKey } = props\n  const index = uploadFiles.value.findIndex((item) => item.uid === file.uid)\n  if (index > -1) {\n    uploadFiles.value[index][statusKey] = 'fail'\n    uploadFiles.value[index].error = err.message\n    uploadFiles.value[index].response = err\n    emit('fail', { error: err, file, formData })\n    emitFileList()\n  }\n}\n\n/**\n * @description 上传成功捕获\n * @param {Object} res 接口返回信息\n * @param {Object} file 上传的文件\n */\nfunction handleSuccess(res: Record<string, any>, file: UploadFileItem, formData: Record<string, any>) {\n  const { statusKey } = props\n  const index = uploadFiles.value.findIndex((item) => item.uid === file.uid)\n  if (index > -1) {\n    uploadFiles.value[index][statusKey] = 'success'\n    uploadFiles.value[index].response = res.data\n    emit('change', { fileList: uploadFiles.value })\n    emit('success', { file, fileList: uploadFiles.value, formData })\n    emitFileList()\n  }\n}\n\n/**\n * @description 上传中捕获\n * @param {Object} res 接口返回信息\n * @param {Object} file 上传的文件\n */\nfunction handleProgress(res: UniApp.OnProgressUpdateResult, file: UploadFileItem) {\n  const index = uploadFiles.value.findIndex((item) => item.uid === file.uid)\n  if (index > -1) {\n    uploadFiles.value[index].percent = res.progress\n    emit('progress', { response: res, file })\n  }\n}\n\n/**\n * @description 选择文件的实际操作，将chooseFile自己用promise包了一层\n */\nfunction onChooseFile(currentIndex?: number) {\n  const { multiple, maxSize, accept, sizeType, limit, sourceType, compressed, maxDuration, camera, beforeUpload, extension } = props\n\n  chooseFile({\n    multiple,\n    sizeType,\n    sourceType,\n    maxCount: limit ? limit - uploadFiles.value.length : 9,\n    accept,\n    compressed,\n    maxDuration,\n    camera,\n    extension\n  })\n    .then((res) => {\n      // 成功选择初始化file\n      let files = res\n      // 单选只有一个\n      if (!multiple) {\n        files = files.slice(0, 1)\n      }\n      // 遍历列表逐个初始化上传参数\n      const mapFiles = async (files: ChooseFile[]) => {\n        for (let index = 0; index < files.length; index++) {\n          const file = files[index]\n          if (file.type === 'image' && !file.size) {\n            const imageInfo = await getImageInfo(file.path)\n            file.size = imageInfo.width * imageInfo.height\n          }\n          Number(file.size) <= maxSize ? initFile(file, currentIndex) : emit('oversize', { file })\n        }\n      }\n\n      // 上传前的钩子\n      if (beforeUpload) {\n        beforeUpload({\n          files,\n          fileList: uploadFiles.value,\n          resolve: (isPass: boolean) => {\n            isPass && mapFiles(files)\n          }\n        })\n      } else {\n        mapFiles(files)\n      }\n    })\n    .catch((error) => {\n      emit('chooseerror', { error })\n    })\n}\n\n/**\n * @description 选择文件，内置拦截选择操作\n */\nfunction handleChoose(index?: number) {\n  if (props.disabled) return\n  const { beforeChoose } = props\n\n  // 选择图片前的钩子\n  if (beforeChoose) {\n    beforeChoose({\n      fileList: uploadFiles.value,\n      resolve: (isPass: boolean) => {\n        isPass && onChooseFile(index)\n      }\n    })\n  } else {\n    onChooseFile(index)\n  }\n}\n\n/**\n * @description 移除文件\n * @param {Object} file 上传的文件\n * @param {Number} index 删除\n */\nfunction handleRemove(file: UploadFileItem) {\n  uploadFiles.value.splice(\n    uploadFiles.value.findIndex((item) => item.uid === file.uid),\n    1\n  )\n  emit('change', {\n    fileList: uploadFiles.value\n  })\n  emit('remove', { file })\n  emitFileList()\n}\n\nfunction removeFile(index: number) {\n  const { beforeRemove } = props\n  const intIndex: number = index\n  const file = uploadFiles.value[intIndex]\n  if (beforeRemove) {\n    beforeRemove({\n      file,\n      index: intIndex,\n      fileList: uploadFiles.value,\n      resolve: (isPass: boolean) => {\n        isPass && handleRemove(file)\n      }\n    })\n  } else {\n    handleRemove(file)\n  }\n}\n\n/**\n * 预览文件\n * @param file\n */\nfunction handlePreviewFile(file: UploadFileItem) {\n  uni.openDocument({\n    filePath: file.url,\n    showMenu: true\n  })\n}\n\n/**\n * 预览图片\n * @param index\n * @param lists\n */\nfunction handlePreviewImage(index: number, lists: string[]) {\n  const { onPreviewFail } = props\n  uni.previewImage({\n    urls: lists,\n    current: lists[index],\n    fail() {\n      if (onPreviewFail) {\n        onPreviewFail({\n          index,\n          imgList: lists\n        })\n      } else {\n        uni.showToast({ title: '预览图片失败', icon: 'none' })\n      }\n    }\n  })\n}\n\n/**\n * 预览视频\n * @param index\n * @param lists\n */\nfunction handlePreviewVieo(index: number, lists: UploadFileItem[]) {\n  const { onPreviewFail } = props\n\n  uni.previewMedia({\n    current: index,\n    sources: lists.map((file) => {\n      return {\n        url: file.url,\n        type: 'video',\n        poster: file.thumb\n      }\n    }),\n    fail() {\n      if (onPreviewFail) {\n        onPreviewFail({\n          index,\n          imgList: []\n        })\n      } else {\n        uni.showToast({ title: '预览视频失败', icon: 'none' })\n      }\n    }\n  })\n\n\n\n\n\n}\n\nfunction onPreviewImage(file: UploadFileItem) {\n  const { beforePreview, reupload } = props\n  const fileList = deepClone(uploadFiles.value)\n  const index: number = fileList.findIndex((item) => item.url === file.url)\n  const imgList = fileList.filter((file) => isImage(file)).map((file) => file.url)\n  const imgIndex: number = imgList.findIndex((item) => item === file.url)\n  if (reupload) {\n    handleChoose(index)\n  } else {\n    if (beforePreview) {\n      beforePreview({\n        file,\n        index,\n        fileList: fileList,\n        imgList: imgList,\n        resolve: (isPass: boolean) => {\n          isPass && handlePreviewImage(imgIndex, imgList)\n        }\n      })\n    } else {\n      handlePreviewImage(imgIndex, imgList)\n    }\n  }\n}\n\nfunction onPreviewVideo(file: UploadFileItem) {\n  const { beforePreview, reupload } = props\n  const fileList = deepClone(uploadFiles.value)\n  const index: number = fileList.findIndex((item) => item.url === file.url)\n  const videoList = fileList.filter((file) => isVideo(file))\n  const videoIndex: number = videoList.findIndex((item) => item.url === file.url)\n  if (reupload) {\n    handleChoose(index)\n  } else {\n    if (beforePreview) {\n      beforePreview({\n        file,\n        index,\n        imgList: [],\n        fileList,\n        resolve: (isPass: boolean) => {\n          isPass && handlePreviewVieo(videoIndex, videoList)\n        }\n      })\n    } else {\n      handlePreviewVieo(videoIndex, videoList)\n    }\n  }\n}\n\nfunction onPreviewFile(file: UploadFileItem) {\n  const { beforePreview, reupload } = props\n  const fileList = deepClone(uploadFiles.value)\n  const index: number = fileList.findIndex((item) => item.url === file.url)\n  if (reupload) {\n    handleChoose(index)\n  } else {\n    if (beforePreview) {\n      beforePreview({\n        file,\n        index,\n        imgList: [],\n        fileList,\n        resolve: (isPass: boolean) => {\n          isPass && handlePreviewFile(file)\n        }\n      })\n    } else {\n      handlePreviewFile(file)\n    }\n  }\n}\n\nfunction isVideo(file: UploadFileItem) {\n  return (file.name && isVideoUrl(file.name)) || isVideoUrl(file.url)\n}\n\nfunction isImage(file: UploadFileItem) {\n  return (file.name && isImageUrl(file.name)) || isImageUrl(file.url)\n}\n</script>\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-upload/wd-upload.vue'\nwx.createComponent(Component)"], "names": ["useTranslate", "ref", "computed", "useUpload", "watch", "isEqual", "context", "isFunction", "isDef", "formData", "uni", "files", "deepClone", "file", "isVideoUrl", "isImageUrl"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkGA,MAAA,SAAmB,MAAA;AACnB,MAAA,iBAA2B,MAAA;AAC3B,MAAA,YAAsB,MAAA;AAbtB,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,gBAAgB;AAAA,EAAA;AAEpB;;;;;AA2BA,UAAM,QAAQ;AAEd,UAAM,OAAO;AAWc,aAAA;AAAA,MACzB,QAAQ,MAAM,iBAAiB;AAAA,MAC/B,OAAO,MAAM,MAAM;AAAA,IAAA,CACpB;AAED,UAAM,EAAE,UAAA,IAAcA,cAAA,aAAa,QAAQ;AAErC,UAAA,cAAcC,cAAsB,IAAA,EAAE;AAEtC,UAAA,aAAaC,cAAAA,SAAS,MAAM,CAAC,MAAM,SAAS,YAAY,MAAM,SAAS,MAAM,KAAK;AAExF,UAAM,eAAeD,cAAAA,IAA0B;AAE/C,UAAM,EAAE,aAAa,OAAO,YAAY,cAAA,IAAkBE,cAAAA,UAAU;AAEpEC,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,QAAQ;AACD,cAAA,EAAE,cAAc;AAClB,YAAAC,sBAAQ,KAAK,YAAY,KAAK;AAAG;AACrC,cAAM,iBAAmC,IAAI,IAAI,CAAC,SAAS;AACzD,eAAK,SAAS,IAAI,KAAK,SAAS,KAAK;AAChC,eAAA,WAAW,KAAK,YAAY;AACjC,iBAAO,iCAAK,OAAL,EAAW,KAAKC,sBAAQ,KAAK;AAAA,QAAA,CACrC;AACD,oBAAY,QAAQ;AAAA,MACtB;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,WAAW;AAAA,MAAA;AAAA,IAEf;AAEAF,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,QAAQ;AACP,YAAI,OAAO,MAAM,YAAY,MAAM,QAAQ;AACzC,kBAAQ,MAAM,+DAA+D;AAAA,QAAA;AAAA,MAEjF;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,WAAW;AAAA,MAAA;AAAA,IAEf;AAEAA,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,OAAO;AACN,YAAI,MAAM,CAACG,yBAAW,EAAE,GAAG;AACzB,kBAAQ,MAAM,4CAA4C;AAAA,QAAA;AAAA,MAE9D;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,WAAW;AAAA,MAAA;AAAA,IAEf;AAEAH,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,OAAO;AACN,YAAI,MAAM,CAACG,yBAAW,EAAE,GAAG;AACzB,kBAAQ,MAAM,4CAA4C;AAAA,QAAA;AAAA,MAE9D;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,WAAW;AAAA,MAAA;AAAA,IAEf;AAEAH,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,OAAO;AACN,YAAI,MAAM,CAACG,yBAAW,EAAE,GAAG;AACzB,kBAAQ,MAAM,2CAA2C;AAAA,QAAA;AAAA,MAE7D;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,WAAW;AAAA,MAAA;AAAA,IAEf;AAEAH,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,OAAO;AACN,YAAI,MAAM,CAACG,yBAAW,EAAE,GAAG;AACzB,kBAAQ,MAAM,2CAA2C;AAAA,QAAA;AAAA,MAE7D;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,WAAW;AAAA,MAAA;AAAA,IAEf;AAEAH,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,OAAO;AACN,YAAI,MAAM,CAACG,yBAAW,EAAE,GAAG;AACzB,kBAAQ,MAAM,2CAA2C;AAAA,QAAA;AAAA,MAE7D;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,WAAW;AAAA,MAAA;AAAA,IAEf;AAEAH,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,OAAO;AACN,YAAI,MAAM,CAACG,yBAAW,EAAE,GAAG;AACzB,kBAAQ,MAAM,4CAA4C;AAAA,QAAA;AAAA,MAE9D;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,WAAW;AAAA,MAAA;AAAA,IAEf;AAEA,aAAS,eAAe;AACjB,WAAA,mBAAmB,YAAY,KAAK;AAAA,IAAA;AAM3C,aAAS,mBAAmB;AAC1B,YAAM,EAAE,eAAe,WAAW,CAAA,GAAI,UAAc,IAAA;AAC9C,YAAA,EAAE,QAAQ,MAAM,SAAS,CAAI,GAAA,QAAQ,eAAe,aAAA,IAAiB;AAC3E,YAAM,aAAaC,cAAA,MAAM,aAAa,IAAI,gBAAgB;AAE/C,iBAAA,cAAc,YAAY,OAAO;AAE1C,YAAI,WAAW,SAAS,MAAM,cAAc,SAAS;AACnD,cAAI,eAAe;AACH,0BAAA;AAAA,cACZ,MAAM;AAAA,cACN;AAAA,cACA,SAAS,CAACC,cAAkC;AAC1CA,6BACE,YAAY,YAAY;AAAA,kBACtB;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA,UAAAA;AAAAA,kBACA,UAAU;AAAA,kBACV;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA,WAAW;AAAA,kBACX,SAAS;AAAA,kBACT,YAAY;AAAA,gBAAA,CACb;AAAA,cAAA;AAAA,YACL,CACD;AAAA,UAAA,OACI;AACL,wBAAY,YAAY;AAAA,cACtB;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA,UAAU;AAAA,cACV;AAAA,cACA;AAAA,cACA;AAAA,cACA,WAAW;AAAA,cACX,SAAS;AAAA,cACT,YAAY;AAAA,YAAA,CACb;AAAA,UAAA;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAOF,aAAS,aAAa,KAAa;AACjC,aAAO,IAAI,QAAwC,CAAC,SAAS,WAAW;AACtEC,sBAAAA,MAAI,aAAa;AAAA,UACf,KAAK;AAAA,UACL,SAAS,CAAC,QAAQ;AAChB,oBAAQ,GAAG;AAAA,UACb;AAAA,UACA,MAAM,CAAC,UAAU;AACf,mBAAO,KAAK;AAAA,UAAA;AAAA,QACd,CACD;AAAA,MAAA,CACF;AAAA,IAAA;AAOM,aAAA,SAAS,MAAkB,cAAuB;AACnD,YAAA,EAAE,cAAc;AAEtB,YAAM,YAA4B;AAAA,QAChC,KAAKJ,cAAQ,QAAA;AAAA;AAAA,QAEb,MAAM,KAAK,QAAQ;AAAA,QACnB,OAAO,KAAK,SAAS;AAAA,QACrB,CAAC,SAAS,GAAG;AAAA,QACb,MAAM,KAAK,QAAQ;AAAA,QACnB,KAAK,KAAK;AAAA,QACV,SAAS;AAAA,MACX;AACI,UAAA,OAAO,iBAAiB,UAAU;AACpC,oBAAY,MAAM,OAAO,cAAc,GAAG,SAAS;AAAA,MAAA,OAC9C;AACO,oBAAA,MAAM,KAAK,SAAS;AAAA,MAAA;AAElC,UAAI,MAAM,YAAY;AACH,yBAAA;AAAA,MAAA;AAAA,IACnB;AAQO,aAAA,YAAY,KAA0B,MAAsB,UAA+B;AAC5F,YAAA,EAAE,cAAc;AAChB,YAAA,QAAQ,YAAY,MAAM,UAAU,CAAC,SAAS,KAAK,QAAQ,KAAK,GAAG;AACzE,UAAI,QAAQ,IAAI;AACd,oBAAY,MAAM,KAAK,EAAE,SAAS,IAAI;AACtC,oBAAY,MAAM,KAAK,EAAE,QAAQ,IAAI;AACzB,oBAAA,MAAM,KAAK,EAAE,WAAW;AACpC,aAAK,QAAQ,EAAE,OAAO,KAAK,MAAM,UAAU;AAC9B,qBAAA;AAAA,MAAA;AAAA,IACf;AAQO,aAAA,cAAc,KAA0B,MAAsB,UAA+B;AAC9F,YAAA,EAAE,cAAc;AAChB,YAAA,QAAQ,YAAY,MAAM,UAAU,CAAC,SAAS,KAAK,QAAQ,KAAK,GAAG;AACzE,UAAI,QAAQ,IAAI;AACd,oBAAY,MAAM,KAAK,EAAE,SAAS,IAAI;AACtC,oBAAY,MAAM,KAAK,EAAE,WAAW,IAAI;AACxC,aAAK,UAAU,EAAE,UAAU,YAAY,OAAO;AAC9C,aAAK,WAAW,EAAE,MAAM,UAAU,YAAY,OAAO,UAAU;AAClD,qBAAA;AAAA,MAAA;AAAA,IACf;AAQO,aAAA,eAAe,KAAoC,MAAsB;AAC1E,YAAA,QAAQ,YAAY,MAAM,UAAU,CAAC,SAAS,KAAK,QAAQ,KAAK,GAAG;AACzE,UAAI,QAAQ,IAAI;AACd,oBAAY,MAAM,KAAK,EAAE,UAAU,IAAI;AACvC,aAAK,YAAY,EAAE,UAAU,KAAK,MAAM;AAAA,MAAA;AAAA,IAC1C;AAMF,aAAS,aAAa,cAAuB;AAC3C,YAAM,EAAE,UAAU,SAAS,QAAQ,UAAU,OAAO,YAAY,YAAY,aAAa,QAAQ,cAAc,UAAc,IAAA;AAElH,iBAAA;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAU,QAAQ,QAAQ,YAAY,MAAM,SAAS;AAAA,QACrD;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MAAA,CACD,EACE,KAAK,CAAC,QAAQ;AAEb,YAAI,QAAQ;AAEZ,YAAI,CAAC,UAAU;AACL,kBAAA,MAAM,MAAM,GAAG,CAAC;AAAA,QAAA;AAGpB,cAAA,WAAW,CAAOK,WAAwB;AAC9C,mBAAS,QAAQ,GAAG,QAAQA,OAAM,QAAQ,SAAS;AAC3C,kBAAA,OAAOA,OAAM,KAAK;AACxB,gBAAI,KAAK,SAAS,WAAW,CAAC,KAAK,MAAM;AACvC,oBAAM,YAAY,MAAM,aAAa,KAAK,IAAI;AACzC,mBAAA,OAAO,UAAU,QAAQ,UAAU;AAAA,YAAA;AAE1C,mBAAO,KAAK,IAAI,KAAK,UAAU,SAAS,MAAM,YAAY,IAAI,KAAK,YAAY,EAAE,KAAA,CAAM;AAAA,UAAA;AAAA,QAE3F;AAGA,YAAI,cAAc;AACH,uBAAA;AAAA,YACX;AAAA,YACA,UAAU,YAAY;AAAA,YACtB,SAAS,CAAC,WAAoB;AAC5B,wBAAU,SAAS,KAAK;AAAA,YAAA;AAAA,UAC1B,CACD;AAAA,QAAA,OACI;AACL,mBAAS,KAAK;AAAA,QAAA;AAAA,MAChB,CACD,EACA,MAAM,CAAC,UAAU;AACX,aAAA,eAAe,EAAE,OAAO;AAAA,MAAA,CAC9B;AAAA,IAAA;AAML,aAAS,aAAa,OAAgB;AACpC,UAAI,MAAM;AAAU;AACd,YAAA,EAAE,iBAAiB;AAGzB,UAAI,cAAc;AACH,qBAAA;AAAA,UACX,UAAU,YAAY;AAAA,UACtB,SAAS,CAAC,WAAoB;AAC5B,sBAAU,aAAa,KAAK;AAAA,UAAA;AAAA,QAC9B,CACD;AAAA,MAAA,OACI;AACL,qBAAa,KAAK;AAAA,MAAA;AAAA,IACpB;AAQF,aAAS,aAAa,MAAsB;AAC1C,kBAAY,MAAM;AAAA,QAChB,YAAY,MAAM,UAAU,CAAC,SAAS,KAAK,QAAQ,KAAK,GAAG;AAAA,QAC3D;AAAA,MACF;AACA,WAAK,UAAU;AAAA,QACb,UAAU,YAAY;AAAA,MAAA,CACvB;AACI,WAAA,UAAU,EAAE,MAAM;AACV,mBAAA;AAAA,IAAA;AAGf,aAAS,WAAW,OAAe;AAC3B,YAAA,EAAE,iBAAiB;AACzB,YAAM,WAAmB;AACnB,YAAA,OAAO,YAAY,MAAM,QAAQ;AACvC,UAAI,cAAc;AACH,qBAAA;AAAA,UACX;AAAA,UACA,OAAO;AAAA,UACP,UAAU,YAAY;AAAA,UACtB,SAAS,CAAC,WAAoB;AAC5B,sBAAU,aAAa,IAAI;AAAA,UAAA;AAAA,QAC7B,CACD;AAAA,MAAA,OACI;AACL,qBAAa,IAAI;AAAA,MAAA;AAAA,IACnB;AAOF,aAAS,kBAAkB,MAAsB;AAC/CD,oBAAAA,MAAI,aAAa;AAAA,QACf,UAAU,KAAK;AAAA,QACf,UAAU;AAAA,MAAA,CACX;AAAA,IAAA;AAQM,aAAA,mBAAmB,OAAe,OAAiB;AACpD,YAAA,EAAE,kBAAkB;AAC1BA,oBAAAA,MAAI,aAAa;AAAA,QACf,MAAM;AAAA,QACN,SAAS,MAAM,KAAK;AAAA,QACpB,OAAO;AACL,cAAI,eAAe;AACH,0BAAA;AAAA,cACZ;AAAA,cACA,SAAS;AAAA,YAAA,CACV;AAAA,UAAA,OACI;AACLA,0BAAA,MAAI,UAAU,EAAE,OAAO,UAAU,MAAM,QAAQ;AAAA,UAAA;AAAA,QACjD;AAAA,MACF,CACD;AAAA,IAAA;AAQM,aAAA,kBAAkB,OAAe,OAAyB;AAC3D,YAAA,EAAE,kBAAkB;AAE1BA,oBAAAA,MAAI,aAAa;AAAA,QACf,SAAS;AAAA,QACT,SAAS,MAAM,IAAI,CAAC,SAAS;AACpB,iBAAA;AAAA,YACL,KAAK,KAAK;AAAA,YACV,MAAM;AAAA,YACN,QAAQ,KAAK;AAAA,UACf;AAAA,QAAA,CACD;AAAA,QACD,OAAO;AACL,cAAI,eAAe;AACH,0BAAA;AAAA,cACZ;AAAA,cACA,SAAS,CAAA;AAAA,YAAC,CACX;AAAA,UAAA,OACI;AACLA,0BAAA,MAAI,UAAU,EAAE,OAAO,UAAU,MAAM,QAAQ;AAAA,UAAA;AAAA,QACjD;AAAA,MACF,CACD;AAAA,IAAA;AAQH,aAAS,eAAe,MAAsB;AACtC,YAAA,EAAE,eAAe,SAAA,IAAa;AAC9B,YAAA,WAAWE,cAAAA,UAAU,YAAY,KAAK;AACtC,YAAA,QAAgB,SAAS,UAAU,CAAC,SAAS,KAAK,QAAQ,KAAK,GAAG;AACxE,YAAM,UAAU,SAAS,OAAO,CAACC,UAAS,QAAQA,KAAI,CAAC,EAAE,IAAI,CAACA,UAASA,MAAK,GAAG;AAC/E,YAAM,WAAmB,QAAQ,UAAU,CAAC,SAAS,SAAS,KAAK,GAAG;AACtE,UAAI,UAAU;AACZ,qBAAa,KAAK;AAAA,MAAA,OACb;AACL,YAAI,eAAe;AACH,wBAAA;AAAA,YACZ;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,SAAS,CAAC,WAAoB;AAClB,wBAAA,mBAAmB,UAAU,OAAO;AAAA,YAAA;AAAA,UAChD,CACD;AAAA,QAAA,OACI;AACL,6BAAmB,UAAU,OAAO;AAAA,QAAA;AAAA,MACtC;AAAA,IACF;AAGF,aAAS,eAAe,MAAsB;AACtC,YAAA,EAAE,eAAe,SAAA,IAAa;AAC9B,YAAA,WAAWD,cAAAA,UAAU,YAAY,KAAK;AACtC,YAAA,QAAgB,SAAS,UAAU,CAAC,SAAS,KAAK,QAAQ,KAAK,GAAG;AACxE,YAAM,YAAY,SAAS,OAAO,CAACC,UAAS,QAAQA,KAAI,CAAC;AACnD,YAAA,aAAqB,UAAU,UAAU,CAAC,SAAS,KAAK,QAAQ,KAAK,GAAG;AAC9E,UAAI,UAAU;AACZ,qBAAa,KAAK;AAAA,MAAA,OACb;AACL,YAAI,eAAe;AACH,wBAAA;AAAA,YACZ;AAAA,YACA;AAAA,YACA,SAAS,CAAC;AAAA,YACV;AAAA,YACA,SAAS,CAAC,WAAoB;AAClB,wBAAA,kBAAkB,YAAY,SAAS;AAAA,YAAA;AAAA,UACnD,CACD;AAAA,QAAA,OACI;AACL,4BAAkB,YAAY,SAAS;AAAA,QAAA;AAAA,MACzC;AAAA,IACF;AAGF,aAAS,cAAc,MAAsB;AACrC,YAAA,EAAE,eAAe,SAAA,IAAa;AAC9B,YAAA,WAAWD,cAAAA,UAAU,YAAY,KAAK;AACtC,YAAA,QAAgB,SAAS,UAAU,CAAC,SAAS,KAAK,QAAQ,KAAK,GAAG;AACxE,UAAI,UAAU;AACZ,qBAAa,KAAK;AAAA,MAAA,OACb;AACL,YAAI,eAAe;AACH,wBAAA;AAAA,YACZ;AAAA,YACA;AAAA,YACA,SAAS,CAAC;AAAA,YACV;AAAA,YACA,SAAS,CAAC,WAAoB;AAC5B,wBAAU,kBAAkB,IAAI;AAAA,YAAA;AAAA,UAClC,CACD;AAAA,QAAA,OACI;AACL,4BAAkB,IAAI;AAAA,QAAA;AAAA,MACxB;AAAA,IACF;AAGF,aAAS,QAAQ,MAAsB;AAC7B,aAAA,KAAK,QAAQE,yBAAW,KAAK,IAAI,KAAMA,cAAAA,WAAW,KAAK,GAAG;AAAA,IAAA;AAGpE,aAAS,QAAQ,MAAsB;AAC7B,aAAA,KAAK,QAAQC,yBAAW,KAAK,IAAI,KAAMA,cAAAA,WAAW,KAAK,GAAG;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACppBpE,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}