"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Math) {
  (JBar + JBackgroundBar + JDynamicBar + JMixLineBar + JStackBar + JMultipleBar + JNegativeBar + JLine + JStepLine + JSmoothLine + JMultipleLine + JPie + JRing + JFunnel + JPyramidFunnel + JRadar + JCircleRadar + JGauge + JColorGauge + JScatter + JBubble + DoubleLineBar + JRose + JHorizontalBar + JArea + JPictorial + JPictorialBar + JBubbleMap + JBarMap + JHeatMap + JAreaMap)();
}
const JBar = () => "./JBar/index.js";
const JStackBar = () => "./JStackBar/index.js";
const JMultipleBar = () => "./JMultipleBar/index.js";
const JNegativeBar = () => "./JNegativeBar/index.js";
const JBackgroundBar = () => "./JBackgroundBar/index.js";
const JDynamicBar = () => "./JDynamicBar/index.js";
const JMixLineBar = () => "./JMixLineBar/index.js";
const JStepLine = () => "./JStepLine/index.js";
const JSmoothLine = () => "./JSmoothLine/index.js";
const JLine = () => "./JLine/index.js";
const JMultipleLine = () => "./JMultipleLine/index.js";
const JPie = () => "./JPie/index.js";
const JRing = () => "./JRing/index.js";
const JFunnel = () => "./JFunnel/index.js";
const JPyramidFunnel = () => "./JPyramidFunnel/index.js";
const JRadar = () => "./JRadar/index.js";
const JCircleRadar = () => "./JCircleRadar/index.js";
const JGauge = () => "./JGauge/index.js";
const JColorGauge = () => "./JColorGauge/index.js";
const JScatter = () => "./JScatter/index.js";
const JBubble = () => "./JBubble/index.js";
const DoubleLineBar = () => "./DoubleLineBar/index.js";
const JRose = () => "./JRose/index.js";
const JHorizontalBar = () => "./JHorizontalBar/index.js";
const JArea = () => "./JArea/index.js";
const JBubbleMap = () => "./map/JBubbleMap/index.js";
const JBarMap = () => "./map/JBarMap/index.js";
const JHeatMap = () => "./map/JHeatMap/index.js";
const JAreaMap = () => "./map/JAreaMap/index.js";
const JPictorial = () => "./JPictorial/index.js";
const JPictorialBar = () => "./JPictorialBar/index.js";
const _sfc_main = {
  __name: "dynamic-component",
  props: {
    compName: {
      type: String,
      default: ""
    },
    i: {
      type: [String, Number],
      default: ""
    },
    config: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  setup(__props) {
    const props = __props;
    const comp = common_vendor.computed(() => {
      console.log("组件名称：compName:", props.compName);
      console.log("组件数据：config:", props.config);
      return props.compName;
    });
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.unref(comp) == "JBar"
      }, common_vendor.unref(comp) == "JBar" ? {
        b: common_vendor.p({
          compName: __props.compName,
          i: __props.i,
          config: __props.config
        })
      } : {}, {
        c: common_vendor.unref(comp) == "JBackgroundBar"
      }, common_vendor.unref(comp) == "JBackgroundBar" ? {
        d: common_vendor.p({
          compName: __props.compName,
          i: __props.i,
          config: __props.config
        })
      } : {}, {
        e: common_vendor.unref(comp) == "JDynamicBar"
      }, common_vendor.unref(comp) == "JDynamicBar" ? {
        f: common_vendor.p({
          compName: __props.compName,
          i: __props.i,
          config: __props.config
        })
      } : {}, {
        g: common_vendor.unref(comp) == "JMixLineBar"
      }, common_vendor.unref(comp) == "JMixLineBar" ? {
        h: common_vendor.p({
          compName: __props.compName,
          i: __props.i,
          config: __props.config
        })
      } : common_vendor.unref(comp).indexOf("JStackBar") >= 0 ? {
        j: common_vendor.p({
          compName: __props.compName,
          i: __props.i,
          config: __props.config
        })
      } : common_vendor.unref(comp).indexOf("JMultipleBar") >= 0 ? {
        l: common_vendor.p({
          compName: __props.compName,
          i: __props.i,
          config: __props.config
        })
      } : common_vendor.unref(comp).indexOf("JNegativeBar") >= 0 ? {
        n: common_vendor.p({
          compName: __props.compName,
          i: __props.i,
          config: __props.config
        })
      } : common_vendor.unref(comp).indexOf("JLine") >= 0 ? {
        p: common_vendor.p({
          compName: __props.compName,
          i: __props.i,
          config: __props.config
        })
      } : common_vendor.unref(comp).indexOf("JStepLine") >= 0 ? {
        r: common_vendor.p({
          compName: __props.compName,
          i: __props.i,
          config: __props.config
        })
      } : common_vendor.unref(comp).indexOf("JSmoothLine") >= 0 ? {
        t: common_vendor.p({
          compName: __props.compName,
          i: __props.i,
          config: __props.config
        })
      } : common_vendor.unref(comp).indexOf("JMultipleLine") >= 0 ? {
        w: common_vendor.p({
          compName: __props.compName,
          i: __props.i,
          config: __props.config
        })
      } : common_vendor.unref(comp).indexOf("JPie") >= 0 ? {
        y: common_vendor.p({
          compName: __props.compName,
          i: __props.i,
          config: __props.config
        })
      } : common_vendor.unref(comp).indexOf("JRing") >= 0 ? {
        A: common_vendor.p({
          compName: __props.compName,
          i: __props.i,
          config: __props.config
        })
      } : common_vendor.unref(comp).indexOf("JFunnel") >= 0 ? {
        C: common_vendor.p({
          compName: __props.compName,
          i: __props.i,
          config: __props.config
        })
      } : common_vendor.unref(comp).indexOf("JPyramidFunnel") >= 0 ? {
        E: common_vendor.p({
          compName: __props.compName,
          i: __props.i,
          config: __props.config
        })
      } : common_vendor.unref(comp).indexOf("JRadar") >= 0 ? {
        G: common_vendor.p({
          compName: __props.compName,
          i: __props.i,
          config: __props.config
        })
      } : common_vendor.unref(comp).indexOf("JCircleRadar") >= 0 ? {
        I: common_vendor.p({
          compName: __props.compName,
          i: __props.i,
          config: __props.config
        })
      } : common_vendor.unref(comp).indexOf("JGauge") >= 0 ? {
        K: common_vendor.p({
          compName: __props.compName,
          i: __props.i,
          config: __props.config
        })
      } : common_vendor.unref(comp).indexOf("JColorGauge") >= 0 ? {
        M: common_vendor.p({
          compName: __props.compName,
          i: __props.i,
          config: __props.config
        })
      } : common_vendor.unref(comp).indexOf("JScatter") >= 0 ? {
        O: common_vendor.p({
          compName: __props.compName,
          i: __props.i,
          config: __props.config
        })
      } : common_vendor.unref(comp) == "JBubble" ? {
        Q: common_vendor.p({
          compName: __props.compName,
          i: __props.i,
          config: __props.config
        })
      } : common_vendor.unref(comp).indexOf("DoubleLineBar") >= 0 ? {
        S: common_vendor.p({
          compName: __props.compName,
          i: __props.i,
          config: __props.config
        })
      } : common_vendor.unref(comp).indexOf("JRose") >= 0 ? {
        U: common_vendor.p({
          compName: __props.compName,
          i: __props.i,
          config: __props.config
        })
      } : {}, {
        i: common_vendor.unref(comp).indexOf("JStackBar") >= 0,
        k: common_vendor.unref(comp).indexOf("JMultipleBar") >= 0,
        m: common_vendor.unref(comp).indexOf("JNegativeBar") >= 0,
        o: common_vendor.unref(comp).indexOf("JLine") >= 0,
        q: common_vendor.unref(comp).indexOf("JStepLine") >= 0,
        s: common_vendor.unref(comp).indexOf("JSmoothLine") >= 0,
        v: common_vendor.unref(comp).indexOf("JMultipleLine") >= 0,
        x: common_vendor.unref(comp).indexOf("JPie") >= 0,
        z: common_vendor.unref(comp).indexOf("JRing") >= 0,
        B: common_vendor.unref(comp).indexOf("JFunnel") >= 0,
        D: common_vendor.unref(comp).indexOf("JPyramidFunnel") >= 0,
        F: common_vendor.unref(comp).indexOf("JRadar") >= 0,
        H: common_vendor.unref(comp).indexOf("JCircleRadar") >= 0,
        J: common_vendor.unref(comp).indexOf("JGauge") >= 0,
        L: common_vendor.unref(comp).indexOf("JColorGauge") >= 0,
        N: common_vendor.unref(comp).indexOf("JScatter") >= 0,
        P: common_vendor.unref(comp) == "JBubble",
        R: common_vendor.unref(comp).indexOf("DoubleLineBar") >= 0,
        T: common_vendor.unref(comp).indexOf("JRose") >= 0,
        V: common_vendor.unref(comp).indexOf("JHorizontalBar") >= 0
      }, common_vendor.unref(comp).indexOf("JHorizontalBar") >= 0 ? {
        W: common_vendor.p({
          compName: __props.compName,
          i: __props.i,
          config: __props.config
        })
      } : common_vendor.unref(comp) == "JArea" ? {
        Y: common_vendor.p({
          compName: __props.compName,
          i: __props.i,
          config: __props.config
        })
      } : common_vendor.unref(comp) == "JPictorial" ? {
        aa: common_vendor.p({
          compName: __props.compName,
          i: __props.i,
          config: __props.config
        })
      } : common_vendor.unref(comp) == "JPictorialBar" ? {
        ac: common_vendor.p({
          compName: __props.compName,
          i: __props.i,
          config: __props.config
        })
      } : common_vendor.unref(comp) == "JBubbleMap" ? {
        ae: common_vendor.p({
          compName: __props.compName,
          i: __props.i,
          config: __props.config
        })
      } : common_vendor.unref(comp).indexOf("JBarMap") >= 0 ? {
        ag: common_vendor.p({
          compName: __props.compName,
          i: __props.i,
          config: __props.config
        })
      } : common_vendor.unref(comp).indexOf("JHeatMap") >= 0 ? {
        ai: common_vendor.p({
          compName: __props.compName,
          i: __props.i,
          config: __props.config
        })
      } : common_vendor.unref(comp).indexOf("JAreaMap") >= 0 ? {
        ak: common_vendor.p({
          compName: __props.compName,
          i: __props.i,
          config: __props.config
        })
      } : {}, {
        X: common_vendor.unref(comp) == "JArea",
        Z: common_vendor.unref(comp) == "JPictorial",
        ab: common_vendor.unref(comp) == "JPictorialBar",
        ad: common_vendor.unref(comp) == "JBubbleMap",
        af: common_vendor.unref(comp).indexOf("JBarMap") >= 0,
        ah: common_vendor.unref(comp).indexOf("JHeatMap") >= 0,
        aj: common_vendor.unref(comp).indexOf("JAreaMap") >= 0
      });
    };
  }
};
wx.createComponent(_sfc_main);
//# sourceMappingURL=dynamic-component.js.map
