{"version": 3, "file": "wd-sticky.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-sticky/wd-sticky.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1zdGlja3kvd2Qtc3RpY2t5LnZ1ZQ"], "sourcesContent": ["<template>\n  <view :style=\"`${rootStyle};display: inline-block;`\">\n    <view :class=\"`wd-sticky ${customClass}`\" :style=\"stickyStyle\" :id=\"styckyId\">\n      <view class=\"wd-sticky__container\" :style=\"containerStyle\">\n        <wd-resize @resize=\"handleResize\" custom-style=\"display: inline-block;\">\n          <slot />\n        </wd-resize>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script lang=\"ts\">\nexport default {\n  name: 'wd-sticky',\n  options: {\n    addGlobalClass: true,\n    virtualHost: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport wdResize from '../wd-resize/wd-resize.vue'\nimport { computed, getCurrentInstance, reactive, ref, type CSSProperties } from 'vue'\nimport { addUnit, getRect, objToStyle, pause, uuid } from '../common/util'\nimport { stickyProps } from './types'\nimport { useParent } from '../composables/useParent'\nimport { STICKY_BOX_KEY } from '../wd-sticky-box/types'\n\nconst props = defineProps(stickyProps)\nconst styckyId = ref<string>(`wd-sticky${uuid()}`)\nconst observerList = ref<UniApp.IntersectionObserver[]>([])\n\nconst stickyState = reactive({\n  position: 'absolute',\n  boxLeaved: false,\n  top: 0,\n  height: 0,\n  width: 0,\n  state: ''\n})\n\nconst { parent: stickyBox } = useParent(STICKY_BOX_KEY)\n\nconst { proxy } = getCurrentInstance() as any\n\nconst rootStyle = computed(() => {\n  const style: CSSProperties = {\n    'z-index': props.zIndex,\n    height: addUnit(stickyState.height),\n    width: addUnit(stickyState.width)\n  }\n  if (!stickyState.boxLeaved) {\n    style['position'] = 'relative'\n  }\n  return `${objToStyle(style)}${props.customStyle}`\n})\n\nconst stickyStyle = computed(() => {\n  const style: CSSProperties = {\n    'z-index': props.zIndex,\n    height: addUnit(stickyState.height),\n    width: addUnit(stickyState.width)\n  }\n  if (!stickyState.boxLeaved) {\n    style['position'] = 'relative'\n  }\n  return `${objToStyle(style)}`\n})\n\nconst containerStyle = computed(() => {\n  const style: CSSProperties = {\n    position: stickyState.position as 'static' | 'relative' | 'absolute' | 'sticky' | 'fixed',\n    top: addUnit(stickyState.top)\n  }\n  return objToStyle(style)\n})\n\nconst innerOffsetTop = computed(() => {\n  let top: number = 0\n\n\n\n\n\n\n  return top + props.offsetTop\n})\n\n/**\n * 清除对当前组件的监听\n */\nfunction clearObserver() {\n  while (observerList.value.length !== 0) {\n    observerList.value.pop()!.disconnect()\n  }\n}\n/**\n * 添加对当前组件的监听\n */\nfunction createObserver() {\n  const observer = uni.createIntersectionObserver(proxy, { thresholds: [0, 0.5] })\n  observerList.value.push(observer)\n  return observer\n}\n/**\n *  当前内容高度发生变化时重置监听\n */\nasync function handleResize(detail: any) {\n  stickyState.width = detail.width\n  stickyState.height = detail.height\n  await pause()\n  observerContentScroll()\n  if (!stickyBox || !stickyBox.observerForChild) return\n  stickyBox.observerForChild(proxy)\n}\n/**\n *  监听吸顶元素滚动事件\n */\nfunction observerContentScroll() {\n  if (stickyState.height === 0 && stickyState.width === 0) return\n  const offset = innerOffsetTop.value + stickyState.height\n  clearObserver()\n  createObserver()\n    .relativeToViewport({\n      top: -offset\n    })\n    .observe(`#${styckyId.value}`, (result) => {\n      handleRelativeTo(result)\n    })\n  getRect(`#${styckyId.value}`, false, proxy).then((res) => {\n\n\n\n\n    if (Number(res.bottom) <= offset) handleRelativeTo({ boundingClientRect: res })\n  })\n}\n/**\n * @description 根据位置进行吸顶\n */\nfunction handleRelativeTo({ boundingClientRect }: any) {\n  // sticky 高度大于或等于 wd-sticky-box，使用 wd-sticky-box 无任何意义\n  if (stickyBox && stickyBox.boxStyle && stickyState.height >= stickyBox.boxStyle.height) {\n    stickyState.position = 'absolute'\n    stickyState.top = 0\n    return\n  }\n\n  let isStycky = boundingClientRect.top <= innerOffsetTop.value\n\n\n\n\n  if (isStycky) {\n    stickyState.state = 'sticky'\n    stickyState.boxLeaved = false\n    stickyState.position = 'fixed'\n    stickyState.top = innerOffsetTop.value\n  } else {\n    stickyState.state = 'normal'\n    stickyState.boxLeaved = false\n    stickyState.position = 'absolute'\n    stickyState.top = 0\n  }\n}\n\n/**\n * 设置位置\n * @param setboxLeaved\n * @param setPosition\n * @param setTop\n */\nfunction setPosition(boxLeaved: boolean, position: string, top: number) {\n  stickyState.boxLeaved = boxLeaved\n  stickyState.position = position\n  stickyState.top = top\n}\n\ndefineExpose({\n  setPosition,\n  stickyState,\n  offsetTop: props.offsetTop\n})\n</script>\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-sticky/wd-sticky.vue'\nwx.createComponent(Component)"], "names": ["ref", "uuid", "reactive", "useParent", "STICKY_BOX_KEY", "getCurrentInstance", "computed", "addUnit", "objToStyle", "uni", "pause", "getRect"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,MAAA,WAAqB,MAAA;AAXrB,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,gBAAgB;AAAA,EAAA;AAEpB;;;;AAWA,UAAM,QAAQ;AACd,UAAM,WAAWA,cAAAA,IAAY,YAAYC,cAAA,KAAA,CAAM,EAAE;AAC3C,UAAA,eAAeD,cAAmC,IAAA,EAAE;AAE1D,UAAM,cAAcE,cAAAA,SAAS;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,MACX,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,OAAO;AAAA,IAAA,CACR;AAED,UAAM,EAAE,QAAQ,cAAcC,cAAAA,UAAUC,cAAAA,cAAc;AAEhD,UAAA,EAAE,MAAM,IAAIC,iCAAmB;AAE/B,UAAA,YAAYC,cAAAA,SAAS,MAAM;AAC/B,YAAM,QAAuB;AAAA,QAC3B,WAAW,MAAM;AAAA,QACjB,QAAQC,cAAAA,QAAQ,YAAY,MAAM;AAAA,QAClC,OAAOA,cAAAA,QAAQ,YAAY,KAAK;AAAA,MAClC;AACI,UAAA,CAAC,YAAY,WAAW;AAC1B,cAAM,UAAU,IAAI;AAAA,MAAA;AAEtB,aAAO,GAAGC,cAAAA,WAAW,KAAK,CAAC,GAAG,MAAM,WAAW;AAAA,IAAA,CAChD;AAEK,UAAA,cAAcF,cAAAA,SAAS,MAAM;AACjC,YAAM,QAAuB;AAAA,QAC3B,WAAW,MAAM;AAAA,QACjB,QAAQC,cAAAA,QAAQ,YAAY,MAAM;AAAA,QAClC,OAAOA,cAAAA,QAAQ,YAAY,KAAK;AAAA,MAClC;AACI,UAAA,CAAC,YAAY,WAAW;AAC1B,cAAM,UAAU,IAAI;AAAA,MAAA;AAEf,aAAA,GAAGC,cAAAA,WAAW,KAAK,CAAC;AAAA,IAAA,CAC5B;AAEK,UAAA,iBAAiBF,cAAAA,SAAS,MAAM;AACpC,YAAM,QAAuB;AAAA,QAC3B,UAAU,YAAY;AAAA,QACtB,KAAKC,cAAAA,QAAQ,YAAY,GAAG;AAAA,MAC9B;AACA,aAAOC,cAAAA,WAAW,KAAK;AAAA,IAAA,CACxB;AAEK,UAAA,iBAAiBF,cAAAA,SAAS,MAAM;AACpC,UAAI,MAAc;AAOlB,aAAO,MAAM,MAAM;AAAA,IAAA,CACpB;AAKD,aAAS,gBAAgB;AAChB,aAAA,aAAa,MAAM,WAAW,GAAG;AACzB,qBAAA,MAAM,IAAI,EAAG,WAAW;AAAA,MAAA;AAAA,IACvC;AAKF,aAAS,iBAAiB;AAClB,YAAA,WAAWG,oBAAI,2BAA2B,OAAO,EAAE,YAAY,CAAC,GAAG,GAAG,GAAG;AAClE,mBAAA,MAAM,KAAK,QAAQ;AACzB,aAAA;AAAA,IAAA;AAKT,aAAe,aAAa,QAAa;AAAA;AACvC,oBAAY,QAAQ,OAAO;AAC3B,oBAAY,SAAS,OAAO;AAC5B,cAAMC,oBAAM;AACU,8BAAA;AAClB,YAAA,CAAC,aAAa,CAAC,UAAU;AAAkB;AAC/C,kBAAU,iBAAiB,KAAK;AAAA,MAAA;AAAA;AAKlC,aAAS,wBAAwB;AAC/B,UAAI,YAAY,WAAW,KAAK,YAAY,UAAU;AAAG;AACnD,YAAA,SAAS,eAAe,QAAQ,YAAY;AACpC,oBAAA;AACd,qBAAA,EACG,mBAAmB;AAAA,QAClB,KAAK,CAAC;AAAA,MAAA,CACP,EACA,QAAQ,IAAI,SAAS,KAAK,IAAI,CAAC,WAAW;AACzC,yBAAiB,MAAM;AAAA,MAAA,CACxB;AACKC,oBAAAA,QAAA,IAAI,SAAS,KAAK,IAAI,OAAO,KAAK,EAAE,KAAK,CAAC,QAAQ;AAKpD,YAAA,OAAO,IAAI,MAAM,KAAK;AAAyB,2BAAA,EAAE,oBAAoB,KAAK;AAAA,MAAA,CAC/E;AAAA,IAAA;AAKM,aAAA,iBAAiB,EAAE,sBAA2B;AAErD,UAAI,aAAa,UAAU,YAAY,YAAY,UAAU,UAAU,SAAS,QAAQ;AACtF,oBAAY,WAAW;AACvB,oBAAY,MAAM;AAClB;AAAA,MAAA;AAGE,UAAA,WAAW,mBAAmB,OAAO,eAAe;AAKxD,UAAI,UAAU;AACZ,oBAAY,QAAQ;AACpB,oBAAY,YAAY;AACxB,oBAAY,WAAW;AACvB,oBAAY,MAAM,eAAe;AAAA,MAAA,OAC5B;AACL,oBAAY,QAAQ;AACpB,oBAAY,YAAY;AACxB,oBAAY,WAAW;AACvB,oBAAY,MAAM;AAAA,MAAA;AAAA,IACpB;AASO,aAAA,YAAY,WAAoB,UAAkB,KAAa;AACtE,kBAAY,YAAY;AACxB,kBAAY,WAAW;AACvB,kBAAY,MAAM;AAAA,IAAA;AAGP,aAAA;AAAA,MACX;AAAA,MACA;AAAA,MACA,WAAW,MAAM;AAAA,IAAA,CAClB;;;;;;;;;;;;;;;;;ACxLD,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}