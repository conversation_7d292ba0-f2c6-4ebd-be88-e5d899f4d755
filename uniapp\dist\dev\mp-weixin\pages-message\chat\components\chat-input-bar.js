"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../common/vendor.js");
const pagesMessage_chat_emojis = require("../emojis.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "chat-input-bar",
  options: {
    // apply-shared‌：当前页面样式会影响到子组件样式.(小程序)
    // shared‌：当前页面样式影响到子组件，子组件样式也会影响到当前页面.(小程序)
    styleIsolation: "‌apply-shared‌"
  }
}), {
  __name: "chat-input-bar",
  emits: ["emojiTypeChange", "send", "image"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emit = __emit;
    let emojiArray = pagesMessage_chat_emojis.getEmojiImageUrl();
    const msg = common_vendor.ref("");
    const focus = common_vendor.ref(false);
    const chatBarType = common_vendor.ref("");
    const getEmoji = common_vendor.computed(() => {
      let img;
      if (["", "more"].includes(chatBarType.value)) {
        img = "emoji";
      } else if (["emoji"].includes(chatBarType.value)) {
        img = "keyboard";
      }
      return `/static/chat/${img}.png`;
    });
    const updateKeyboardHeightChange = (res) => {
      if (res.height > 0) {
        chatBarType.value = "";
      }
    };
    const hidedKeyboard = () => {
      if (["emoji", "more"].includes(chatBarType.value)) {
        chatBarType.value = "";
      }
    };
    const tooglePanl = (val) => {
      if (chatBarType.value == val) {
        focus.value = true;
        chatBarType.value = "";
      } else {
        focus.value = false;
        common_vendor.index.hideKeyboard();
        chatBarType.value = val;
      }
    };
    const emojiClick = (em) => {
      msg.value += em.alt;
    };
    const sendClick = () => {
      if (!msg.value.length)
        return;
      emit("send", msg.value);
      msg.value = "";
    };
    const getImage = (type) => {
      emit("image", type);
    };
    __expose({
      updateKeyboardHeightChange,
      hidedKeyboard
    });
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.o(($event) => tooglePanl("more")),
        b: common_vendor.unref(focus),
        c: common_vendor.o(sendClick),
        d: common_vendor.unref(msg),
        e: common_vendor.o(($event) => common_vendor.isRef(msg) ? msg.value = $event.detail.value : null),
        f: getEmoji.value,
        g: common_vendor.o(($event) => tooglePanl("emoji")),
        h: common_vendor.o(sendClick),
        i: ["emoji"].includes(common_vendor.unref(chatBarType))
      }, ["emoji"].includes(common_vendor.unref(chatBarType)) ? {
        j: common_vendor.f(common_vendor.unref(emojiArray), (page, pid, i0) => {
          return {
            a: common_vendor.f(page, (em, eid, i1) => {
              return {
                a: em.url,
                b: eid,
                c: common_vendor.o(($event) => emojiClick(em), eid)
              };
            }),
            b: pid
          };
        })
      } : {}, {
        k: ["more"].includes(common_vendor.unref(chatBarType))
      }, ["more"].includes(common_vendor.unref(chatBarType)) ? {
        l: common_vendor.o(($event) => getImage("album")),
        m: common_vendor.o(($event) => getImage("camera"))
      } : {}, {
        n: common_vendor.s({
          height: ["emoji", "more"].includes(common_vendor.unref(chatBarType)) ? "320rpx" : "0px"
        })
      });
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-5a847842"]]);
wx.createComponent(Component);
//# sourceMappingURL=chat-input-bar.js.map
