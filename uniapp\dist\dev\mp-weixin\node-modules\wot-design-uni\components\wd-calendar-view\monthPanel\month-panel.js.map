{"version": 3, "file": "month-panel.js", "sources": ["../../../../../../../../node_modules/wot-design-uni/components/wd-calendar-view/monthPanel/month-panel.vue", "../../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1jYWxlbmRhci12aWV3L21vbnRoUGFuZWwvbW9udGgtcGFuZWwudnVl"], "sourcesContent": ["<template>\n  <view class=\"wd-month-panel\">\n    <view v-if=\"showPanelTitle\" class=\"wd-month-panel__title\">\n      {{ title }}\n    </view>\n    <view class=\"wd-month-panel__weeks\">\n      <view v-for=\"item in 7\" :key=\"item\" class=\"wd-month-panel__week\">{{ weekLabel(item + firstDayOfWeek) }}</view>\n    </view>\n    <scroll-view\n      :class=\"`wd-month-panel__container ${!!timeType ? 'wd-month-panel__container--time' : ''}`\"\n      :style=\"`height: ${scrollHeight}px`\"\n      scroll-y\n      @scroll=\"monthScroll\"\n      :scroll-top=\"scrollTop\"\n    >\n      <view v-for=\"(item, index) in months\" :key=\"index\" :id=\"`month${index}`\">\n        <month\n          :type=\"type\"\n          :date=\"item.date\"\n          :value=\"value\"\n          :min-date=\"minDate\"\n          :max-date=\"maxDate\"\n          :first-day-of-week=\"firstDayOfWeek\"\n          :formatter=\"formatter\"\n          :max-range=\"maxRange\"\n          :range-prompt=\"rangePrompt\"\n          :allow-same-day=\"allowSameDay\"\n          :default-time=\"defaultTime\"\n          :showTitle=\"index !== 0\"\n          @change=\"handleDateChange\"\n        />\n      </view>\n    </scroll-view>\n    <view v-if=\"timeType\" class=\"wd-month-panel__time\">\n      <view v-if=\"type === 'datetimerange'\" class=\"wd-month-panel__time-label\">\n        <view class=\"wd-month-panel__time-text\">{{ timeType === 'start' ? translate('startTime') : translate('endTime') }}</view>\n      </view>\n      <view class=\"wd-month-panel__time-picker\">\n        <wd-picker-view\n          v-if=\"timeData.length\"\n          v-model=\"timeValue\"\n          :columns=\"timeData\"\n          :columns-height=\"125\"\n          :immediate-change=\"immediateChange\"\n          @change=\"handleTimeChange\"\n          @pickstart=\"handlePickStart\"\n          @pickend=\"handlePickEnd\"\n        />\n      </view>\n    </view>\n  </view>\n</template>\n\n<script lang=\"ts\">\nexport default {\n  options: {\n    addGlobalClass: true,\n    virtualHost: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport wdPickerView from '../../wd-picker-view/wd-picker-view.vue'\nimport { computed, ref, watch, onMounted } from 'vue'\nimport { debounce, isArray, isEqual, isNumber, pause } from '../../common/util'\nimport { compareMonth, formatMonthTitle, getMonthEndDay, getMonths, getTimeData, getWeekLabel } from '../utils'\nimport Month from '../month/month.vue'\nimport { monthPanelProps, type MonthInfo, type MonthPanelTimeType, type MonthPanelExpose } from './types'\nimport { useTranslate } from '../../composables/useTranslate'\nimport type { CalendarItem } from '../types'\n\nconst props = defineProps(monthPanelProps)\nconst emit = defineEmits(['change', 'pickstart', 'pickend'])\n\nconst { translate } = useTranslate('calendar-view')\n\nconst scrollTop = ref<number>(0) // 滚动位置\nconst scrollIndex = ref<number>(0) // 当前显示的月份索引\nconst timeValue = ref<number[]>([]) // 当前选中的时分秒\n\nconst timeType = ref<MonthPanelTimeType>('') // 当前时间类型，是开始还是结束\nconst innerValue = ref<string | number | (number | null)[]>('') // 内部保存一个值，用于判断新老值，避免监听器触发\n\nconst handleChange = debounce((value) => {\n  emit('change', {\n    value\n  })\n}, 50)\n\n// 时间picker的列数据\nconst timeData = computed<Array<CalendarItem[]>>(() => {\n  let timeColumns: Array<CalendarItem[]> = []\n  if (props.type === 'datetime' && isNumber(props.value)) {\n    const date = new Date(props.value)\n    date.setHours(timeValue.value[0])\n    date.setMinutes(timeValue.value[1])\n    date.setSeconds(props.hideSecond ? 0 : timeValue.value[2])\n    const dateTime = date.getTime()\n    timeColumns = getTime(dateTime) || []\n  } else if (isArray(props.value) && props.type === 'datetimerange') {\n    const [start, end] = props.value!\n    const dataValue = timeType.value === 'start' ? start : end\n    const date = new Date(dataValue || '')\n    date.setHours(timeValue.value[0])\n    date.setMinutes(timeValue.value[1])\n    date.setSeconds(props.hideSecond ? 0 : timeValue.value[2])\n    const dateTime = date.getTime()\n    const finalValue = [start, end]\n    if (timeType.value === 'start') {\n      finalValue[0] = dateTime\n    } else {\n      finalValue[1] = dateTime\n    }\n    timeColumns = getTime(finalValue, timeType.value) || []\n  }\n  return timeColumns\n})\n\n// 标题\nconst title = computed(() => {\n  return formatMonthTitle(months.value[scrollIndex.value].date)\n})\n\n// 周标题\nconst weekLabel = computed(() => {\n  return (index: number) => {\n    return getWeekLabel(index - 1)\n  }\n})\n\n// 滚动区域的高度\nconst scrollHeight = computed(() => {\n  const scrollHeight: number = timeType.value ? props.panelHeight - 125 : props.panelHeight\n  return scrollHeight\n})\n\n// 月份日期和月份高度\nconst months = computed<MonthInfo[]>(() => {\n  return getMonths(props.minDate, props.maxDate).map((month, index) => {\n    const offset = (7 + new Date(month).getDay() - props.firstDayOfWeek) % 7\n    const totalDay = getMonthEndDay(new Date(month).getFullYear(), new Date(month).getMonth() + 1)\n    const rows = Math.ceil((offset + totalDay) / 7)\n    return {\n      height: rows * 64 + (rows - 1) * 4 + (index === 0 ? 0 : 45), // 每行64px高度,除最后一行外每行加4px margin,加上标题45px\n      date: month\n    }\n  })\n})\n\nwatch(\n  () => props.type,\n  (val) => {\n    if (\n      (val === 'datetime' && props.value) ||\n      (val === 'datetimerange' && isArray(props.value) && props.value && props.value.length > 0 && props.value[0])\n    ) {\n      setTime(props.value, 'start')\n    }\n  },\n  {\n    deep: true,\n    immediate: true\n  }\n)\n\nwatch(\n  () => props.value,\n  (val) => {\n    if (isEqual(val, innerValue.value)) return\n\n    if ((props.type === 'datetime' && val) || (props.type === 'datetimerange' && val && isArray(val) && val.length > 0 && val[0])) {\n      setTime(val, 'start')\n    }\n  },\n  {\n    deep: true,\n    immediate: true\n  }\n)\n\nonMounted(() => {\n  scrollIntoView()\n})\n\n/**\n * 使当前日期或者选中日期滚动到可视区域\n */\nasync function scrollIntoView() {\n  // 等待渲染完毕\n  await pause()\n  let activeDate: number | null = 0\n  if (isArray(props.value)) {\n    // 对数组按时间排序,取第一个值\n    const sortedValue = [...props.value].sort((a, b) => (a || 0) - (b || 0))\n    activeDate = sortedValue[0]\n  } else if (isNumber(props.value)) {\n    activeDate = props.value\n  }\n\n  if (!activeDate) {\n    activeDate = Date.now()\n  }\n\n  let top: number = 0\n  let activeMonthIndex = -1\n  for (let index = 0; index < months.value.length; index++) {\n    if (compareMonth(months.value[index].date, activeDate) === 0) {\n      activeMonthIndex = index\n      // 找到选中月份后,计算选中日期在月份中的位置\n      const date = new Date(activeDate)\n      const day = date.getDate()\n      const firstDay = new Date(date.getFullYear(), date.getMonth(), 1)\n      const offset = (7 + firstDay.getDay() - props.firstDayOfWeek) % 7\n      const row = Math.floor((offset + day - 1) / 7)\n      // 每行高度64px,每行加4px margin\n      top += row * 64 + row * 4\n      break\n    }\n    top += months.value[index] ? Number(months.value[index].height) : 0\n  }\n  scrollTop.value = 0\n  if (top > 0) {\n    await pause()\n    // 如果不是第一个月才加45\n    scrollTop.value = top + (activeMonthIndex > 0 ? 45 : 0)\n  }\n}\n/**\n * 获取时间 picker 的数据\n * @param {timestamp|array} value 当前时间\n * @param {string} type 类型，是开始还是结束\n */\nfunction getTime(value: number | (number | null)[], type?: string) {\n  if (props.type === 'datetime') {\n    return getTimeData({\n      date: value as number,\n      minDate: props.minDate,\n      maxDate: props.maxDate,\n      filter: props.timeFilter,\n      isHideSecond: props.hideSecond\n    })\n  } else {\n    if (type === 'start' && isArray(props.value)) {\n      return getTimeData({\n        date: (value as Array<number>)[0],\n        minDate: props.minDate,\n        maxDate: props.value[1] ? props.value[1] : props.maxDate,\n        filter: props.timeFilter,\n        isHideSecond: props.hideSecond\n      })\n    } else {\n      return getTimeData({\n        date: (value as Array<number>)[1],\n        minDate: (value as Array<number>)[0],\n        maxDate: props.maxDate,\n        filter: props.timeFilter,\n        isHideSecond: props.hideSecond\n      })\n    }\n  }\n}\n/**\n * 获取 date 的时分秒\n * @param {timestamp} date 时间\n * @param {string} type 类型，是开始还是结束\n */\nfunction getTimeValue(date: number | (number | null)[], type: MonthPanelTimeType) {\n  let dateValue: Date = new Date()\n  if (props.type === 'datetime') {\n    dateValue = new Date(date as number)\n  } else if (isArray(date)) {\n    if (type === 'start') {\n      dateValue = new Date(date[0] || '')\n    } else {\n      dateValue = new Date(date[1] || '')\n    }\n  }\n\n  const hour = dateValue.getHours()\n  const minute = dateValue.getMinutes()\n  const second = dateValue.getSeconds()\n  return props.hideSecond ? [hour, minute] : [hour, minute, second]\n}\n\nfunction setTime(value: number | (number | null)[], type?: MonthPanelTimeType) {\n  if (isArray(value) && value[0] && value[1] && type === 'start' && timeType.value === 'start') {\n    type = 'end'\n  }\n  timeType.value = type || ''\n  timeValue.value = getTimeValue(value, type || '')\n}\nfunction handleDateChange({ value, type }: { value: number | (number | null)[]; type?: MonthPanelTimeType }) {\n  if (!isEqual(value, props.value)) {\n    // 内部保存一个值，用于判断新老值，避免监听器触发\n    innerValue.value = value\n    handleChange(value)\n  }\n  // datetime 和 datetimerange 类型，需要计算 timeData 并做展示\n  if (props.type.indexOf('time') > -1) {\n    setTime(value, type)\n  }\n}\nfunction handleTimeChange({ value }: { value: any[] }) {\n  if (!props.value) {\n    return\n  }\n  if (props.type === 'datetime' && isNumber(props.value)) {\n    const date = new Date(props.value)\n    date.setHours(value[0])\n    date.setMinutes(value[1])\n    date.setSeconds(props.hideSecond ? 0 : value[2])\n    const dateTime = date.getTime()\n    handleChange(dateTime)\n  } else if (isArray(props.value) && props.type === 'datetimerange') {\n    const [start, end] = props.value!\n    const dataValue = timeType.value === 'start' ? start : end\n    const date = new Date(dataValue || '')\n    date.setHours(value[0])\n    date.setMinutes(value[1])\n    date.setSeconds(props.hideSecond ? 0 : value[2])\n    const dateTime = date.getTime()\n\n    if (dateTime === dataValue) return\n\n    const finalValue = [start, end]\n    if (timeType.value === 'start') {\n      finalValue[0] = dateTime\n    } else {\n      finalValue[1] = dateTime\n    }\n    innerValue.value = finalValue // 内部保存一个值，用于判断新老值，避免监听器触发\n    handleChange(finalValue)\n  }\n}\nfunction handlePickStart() {\n  emit('pickstart')\n}\nfunction handlePickEnd() {\n  emit('pickend')\n}\n\nconst monthScroll = (event: { detail: { scrollTop: number } }) => {\n  if (months.value.length <= 1) {\n    return\n  }\n  const scrollTop = Math.max(0, event.detail.scrollTop)\n  doSetSubtitle(scrollTop)\n}\n\n/**\n * 设置小标题\n * scrollTop 滚动条位置\n */\nfunction doSetSubtitle(scrollTop: number) {\n  let height: number = 0 // 月份高度和\n  for (let index = 0; index < months.value.length; index++) {\n    height = height + months.value[index].height\n    if (scrollTop < height) {\n      scrollIndex.value = index\n      return\n    }\n  }\n}\n\ndefineExpose<MonthPanelExpose>({\n  scrollIntoView\n})\n</script>\n\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-calendar-view/monthPanel/month-panel.vue'\nwx.createComponent(Component)"], "names": ["useTranslate", "ref", "debounce", "computed", "isNumber", "isArray", "formatMonthTitle", "getWeekLabel", "scrollHeight", "getMonths", "getMonthEndDay", "watch", "isEqual", "onMounted", "pause", "compareMonth", "getTimeData", "scrollTop"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgEA,MAAA,eAAyB,MAAA;AAIzB,MAAA,QAAkB,MAAA;AAdlB,MAAe,cAAA;AAAA,EACb,SAAS;AAAA,IACP,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,gBAAgB;AAAA,EAAA;AAEpB;;;;;;AAaA,UAAM,QAAQ;AACd,UAAM,OAAO;AAEb,UAAM,EAAE,UAAA,IAAcA,cAAA,aAAa,eAAe;AAE5C,UAAA,YAAYC,kBAAY,CAAC;AACzB,UAAA,cAAcA,kBAAY,CAAC;AAC3B,UAAA,YAAYA,cAAc,IAAA,EAAE;AAE5B,UAAA,WAAWA,kBAAwB,EAAE;AACrC,UAAA,aAAaA,kBAAyC,EAAE;AAExD,UAAA,eAAeC,uBAAS,CAAC,UAAU;AACvC,WAAK,UAAU;AAAA,QACb;AAAA,MAAA,CACD;AAAA,OACA,EAAE;AAGC,UAAA,WAAWC,cAAAA,SAAgC,MAAM;AACrD,UAAI,cAAqC,CAAC;AAC1C,UAAI,MAAM,SAAS,cAAcC,cAAAA,SAAS,MAAM,KAAK,GAAG;AACtD,cAAM,OAAO,IAAI,KAAK,MAAM,KAAK;AACjC,aAAK,SAAS,UAAU,MAAM,CAAC,CAAC;AAChC,aAAK,WAAW,UAAU,MAAM,CAAC,CAAC;AAClC,aAAK,WAAW,MAAM,aAAa,IAAI,UAAU,MAAM,CAAC,CAAC;AACnD,cAAA,WAAW,KAAK,QAAQ;AAChB,sBAAA,QAAQ,QAAQ,KAAK,CAAC;AAAA,MAAA,WAC3BC,cAAQ,QAAA,MAAM,KAAK,KAAK,MAAM,SAAS,iBAAiB;AACjE,cAAM,CAAC,OAAO,GAAG,IAAI,MAAM;AAC3B,cAAM,YAAY,SAAS,UAAU,UAAU,QAAQ;AACvD,cAAM,OAAO,IAAI,KAAK,aAAa,EAAE;AACrC,aAAK,SAAS,UAAU,MAAM,CAAC,CAAC;AAChC,aAAK,WAAW,UAAU,MAAM,CAAC,CAAC;AAClC,aAAK,WAAW,MAAM,aAAa,IAAI,UAAU,MAAM,CAAC,CAAC;AACnD,cAAA,WAAW,KAAK,QAAQ;AACxB,cAAA,aAAa,CAAC,OAAO,GAAG;AAC1B,YAAA,SAAS,UAAU,SAAS;AAC9B,qBAAW,CAAC,IAAI;AAAA,QAAA,OACX;AACL,qBAAW,CAAC,IAAI;AAAA,QAAA;AAElB,sBAAc,QAAQ,YAAY,SAAS,KAAK,KAAK,CAAC;AAAA,MAAA;AAEjD,aAAA;AAAA,IAAA,CACR;AAGK,UAAA,QAAQF,cAAAA,SAAS,MAAM;AAC3B,aAAOG,cAAAA,iBAAiB,OAAO,MAAM,YAAY,KAAK,EAAE,IAAI;AAAA,IAAA,CAC7D;AAGK,UAAA,YAAYH,cAAAA,SAAS,MAAM;AAC/B,aAAO,CAAC,UAAkB;AACjB,eAAAI,cAAA,aAAa,QAAQ,CAAC;AAAA,MAC/B;AAAA,IAAA,CACD;AAGK,UAAA,eAAeJ,cAAAA,SAAS,MAAM;AAClC,YAAMK,gBAAuB,SAAS,QAAQ,MAAM,cAAc,MAAM,MAAM;AACvEA,aAAAA;AAAAA,IAAA,CACR;AAGK,UAAA,SAASL,cAAAA,SAAsB,MAAM;AAClC,aAAAM,cAAA,UAAU,MAAM,SAAS,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO,UAAU;AAC7D,cAAA,UAAU,IAAI,IAAI,KAAK,KAAK,EAAE,OAAW,IAAA,MAAM,kBAAkB;AACvE,cAAM,WAAWC,cAAA,eAAe,IAAI,KAAK,KAAK,EAAE,eAAe,IAAI,KAAK,KAAK,EAAE,SAAA,IAAa,CAAC;AAC7F,cAAM,OAAO,KAAK,MAAM,SAAS,YAAY,CAAC;AACvC,eAAA;AAAA,UACL,QAAQ,OAAO,MAAM,OAAO,KAAK,KAAK,UAAU,IAAI,IAAI;AAAA;AAAA,UACxD,MAAM;AAAA,QACR;AAAA,MAAA,CACD;AAAA,IAAA,CACF;AAEDC,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,QAAQ;AACP,YACG,QAAQ,cAAc,MAAM,SAC5B,QAAQ,mBAAmBN,sBAAQ,MAAM,KAAK,KAAK,MAAM,SAAS,MAAM,MAAM,SAAS,KAAK,MAAM,MAAM,CAAC,GAC1G;AACQ,kBAAA,MAAM,OAAO,OAAO;AAAA,QAAA;AAAA,MAEhC;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,WAAW;AAAA,MAAA;AAAA,IAEf;AAEAM,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,QAAQ;AACH,YAAAC,sBAAQ,KAAK,WAAW,KAAK;AAAG;AAEpC,YAAK,MAAM,SAAS,cAAc,OAAS,MAAM,SAAS,mBAAmB,OAAOP,cAAAA,QAAQ,GAAG,KAAK,IAAI,SAAS,KAAK,IAAI,CAAC,GAAI;AAC7H,kBAAQ,KAAK,OAAO;AAAA,QAAA;AAAA,MAExB;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,WAAW;AAAA,MAAA;AAAA,IAEf;AAEAQ,kBAAAA,UAAU,MAAM;AACC,qBAAA;AAAA,IAAA,CAChB;AAKD,aAAe,iBAAiB;AAAA;AAE9B,cAAMC,oBAAM;AACZ,YAAI,aAA4B;AAC5B,YAAAT,cAAA,QAAQ,MAAM,KAAK,GAAG;AAExB,gBAAM,cAAc,CAAC,GAAG,MAAM,KAAK,EAAE,KAAK,CAAC,GAAG,OAAO,KAAK,MAAM,KAAK,EAAE;AACvE,uBAAa,YAAY,CAAC;AAAA,QACjB,WAAAD,cAAA,SAAS,MAAM,KAAK,GAAG;AAChC,uBAAa,MAAM;AAAA,QAAA;AAGrB,YAAI,CAAC,YAAY;AACf,uBAAa,KAAK,IAAI;AAAA,QAAA;AAGxB,YAAI,MAAc;AAClB,YAAI,mBAAmB;AACvB,iBAAS,QAAQ,GAAG,QAAQ,OAAO,MAAM,QAAQ,SAAS;AACpD,cAAAW,cAAA,aAAa,OAAO,MAAM,KAAK,EAAE,MAAM,UAAU,MAAM,GAAG;AACzC,+BAAA;AAEb,kBAAA,OAAO,IAAI,KAAK,UAAU;AAC1B,kBAAA,MAAM,KAAK,QAAQ;AACnB,kBAAA,WAAW,IAAI,KAAK,KAAK,eAAe,KAAK,SAAS,GAAG,CAAC;AAChE,kBAAM,UAAU,IAAI,SAAS,WAAW,MAAM,kBAAkB;AAChE,kBAAM,MAAM,KAAK,OAAO,SAAS,MAAM,KAAK,CAAC;AAEtC,mBAAA,MAAM,KAAK,MAAM;AACxB;AAAA,UAAA;AAEK,iBAAA,OAAO,MAAM,KAAK,IAAI,OAAO,OAAO,MAAM,KAAK,EAAE,MAAM,IAAI;AAAA,QAAA;AAEpE,kBAAU,QAAQ;AAClB,YAAI,MAAM,GAAG;AACX,gBAAMD,oBAAM;AAEZ,oBAAU,QAAQ,OAAO,mBAAmB,IAAI,KAAK;AAAA,QAAA;AAAA,MACvD;AAAA;AAOO,aAAA,QAAQ,OAAmC,MAAe;AAC7D,UAAA,MAAM,SAAS,YAAY;AAC7B,eAAOE,0BAAY;AAAA,UACjB,MAAM;AAAA,UACN,SAAS,MAAM;AAAA,UACf,SAAS,MAAM;AAAA,UACf,QAAQ,MAAM;AAAA,UACd,cAAc,MAAM;AAAA,QAAA,CACrB;AAAA,MAAA,OACI;AACL,YAAI,SAAS,WAAWX,cAAQ,QAAA,MAAM,KAAK,GAAG;AAC5C,iBAAOW,0BAAY;AAAA,YACjB,MAAO,MAAwB,CAAC;AAAA,YAChC,SAAS,MAAM;AAAA,YACf,SAAS,MAAM,MAAM,CAAC,IAAI,MAAM,MAAM,CAAC,IAAI,MAAM;AAAA,YACjD,QAAQ,MAAM;AAAA,YACd,cAAc,MAAM;AAAA,UAAA,CACrB;AAAA,QAAA,OACI;AACL,iBAAOA,0BAAY;AAAA,YACjB,MAAO,MAAwB,CAAC;AAAA,YAChC,SAAU,MAAwB,CAAC;AAAA,YACnC,SAAS,MAAM;AAAA,YACf,QAAQ,MAAM;AAAA,YACd,cAAc,MAAM;AAAA,UAAA,CACrB;AAAA,QAAA;AAAA,MACH;AAAA,IACF;AAOO,aAAA,aAAa,MAAkC,MAA0B;AAC5E,UAAA,gCAAsB,KAAK;AAC3B,UAAA,MAAM,SAAS,YAAY;AACjB,oBAAA,IAAI,KAAK,IAAc;AAAA,MAAA,WAC1BX,cAAAA,QAAQ,IAAI,GAAG;AACxB,YAAI,SAAS,SAAS;AACpB,sBAAY,IAAI,KAAK,KAAK,CAAC,KAAK,EAAE;AAAA,QAAA,OAC7B;AACL,sBAAY,IAAI,KAAK,KAAK,CAAC,KAAK,EAAE;AAAA,QAAA;AAAA,MACpC;AAGI,YAAA,OAAO,UAAU,SAAS;AAC1B,YAAA,SAAS,UAAU,WAAW;AAC9B,YAAA,SAAS,UAAU,WAAW;AAC7B,aAAA,MAAM,aAAa,CAAC,MAAM,MAAM,IAAI,CAAC,MAAM,QAAQ,MAAM;AAAA,IAAA;AAGzD,aAAA,QAAQ,OAAmC,MAA2B;AAC7E,UAAIA,cAAQ,QAAA,KAAK,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,SAAS,WAAW,SAAS,UAAU,SAAS;AACrF,eAAA;AAAA,MAAA;AAET,eAAS,QAAQ,QAAQ;AACzB,gBAAU,QAAQ,aAAa,OAAO,QAAQ,EAAE;AAAA,IAAA;AAElD,aAAS,iBAAiB,EAAE,OAAO,QAA0E;AAC3G,UAAI,CAACO,cAAAA,QAAQ,OAAO,MAAM,KAAK,GAAG;AAEhC,mBAAW,QAAQ;AACnB,qBAAa,KAAK;AAAA,MAAA;AAGpB,UAAI,MAAM,KAAK,QAAQ,MAAM,IAAI,IAAI;AACnC,gBAAQ,OAAO,IAAI;AAAA,MAAA;AAAA,IACrB;AAEO,aAAA,iBAAiB,EAAE,SAA2B;AACjD,UAAA,CAAC,MAAM,OAAO;AAChB;AAAA,MAAA;AAEF,UAAI,MAAM,SAAS,cAAcR,cAAAA,SAAS,MAAM,KAAK,GAAG;AACtD,cAAM,OAAO,IAAI,KAAK,MAAM,KAAK;AAC5B,aAAA,SAAS,MAAM,CAAC,CAAC;AACjB,aAAA,WAAW,MAAM,CAAC,CAAC;AACxB,aAAK,WAAW,MAAM,aAAa,IAAI,MAAM,CAAC,CAAC;AACzC,cAAA,WAAW,KAAK,QAAQ;AAC9B,qBAAa,QAAQ;AAAA,MAAA,WACZC,cAAQ,QAAA,MAAM,KAAK,KAAK,MAAM,SAAS,iBAAiB;AACjE,cAAM,CAAC,OAAO,GAAG,IAAI,MAAM;AAC3B,cAAM,YAAY,SAAS,UAAU,UAAU,QAAQ;AACvD,cAAM,OAAO,IAAI,KAAK,aAAa,EAAE;AAChC,aAAA,SAAS,MAAM,CAAC,CAAC;AACjB,aAAA,WAAW,MAAM,CAAC,CAAC;AACxB,aAAK,WAAW,MAAM,aAAa,IAAI,MAAM,CAAC,CAAC;AACzC,cAAA,WAAW,KAAK,QAAQ;AAE9B,YAAI,aAAa;AAAW;AAEtB,cAAA,aAAa,CAAC,OAAO,GAAG;AAC1B,YAAA,SAAS,UAAU,SAAS;AAC9B,qBAAW,CAAC,IAAI;AAAA,QAAA,OACX;AACL,qBAAW,CAAC,IAAI;AAAA,QAAA;AAElB,mBAAW,QAAQ;AACnB,qBAAa,UAAU;AAAA,MAAA;AAAA,IACzB;AAEF,aAAS,kBAAkB;AACzB,WAAK,WAAW;AAAA,IAAA;AAElB,aAAS,gBAAgB;AACvB,WAAK,SAAS;AAAA,IAAA;AAGV,UAAA,cAAc,CAAC,UAA6C;AAC5D,UAAA,OAAO,MAAM,UAAU,GAAG;AAC5B;AAAA,MAAA;AAEF,YAAMY,aAAY,KAAK,IAAI,GAAG,MAAM,OAAO,SAAS;AACpD,oBAAcA,UAAS;AAAA,IACzB;AAMA,aAAS,cAAcA,YAAmB;AACxC,UAAI,SAAiB;AACrB,eAAS,QAAQ,GAAG,QAAQ,OAAO,MAAM,QAAQ,SAAS;AACxD,iBAAS,SAAS,OAAO,MAAM,KAAK,EAAE;AACtC,YAAIA,aAAY,QAAQ;AACtB,sBAAY,QAAQ;AACpB;AAAA,QAAA;AAAA,MACF;AAAA,IACF;AAG6B,aAAA;AAAA,MAC7B;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/WD,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}