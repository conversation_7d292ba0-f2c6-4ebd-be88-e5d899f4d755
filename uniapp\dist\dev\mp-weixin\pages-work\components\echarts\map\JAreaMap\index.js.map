{"version": 3, "file": "index.js", "sources": ["../../../../../../../../src/pages-work/components/echarts/map/JAreaMap/index.vue", "../../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvcGFnZXMtd29yay9jb21wb25lbnRzL2VjaGFydHMvbWFwL0pBcmVhTWFwL2luZGV4LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"content\">\r\n    <statusTip v-if=\"pageTips.show\" :status=\"pageTips.status\"></statusTip>\r\n    <!-- #ifdef APP-PLUS || H5 -->\r\n<!--    <EchartsMap v-else v-model:option=\"option\" v-model:map=\"mapObject\" v-model:echartId=\"echartId\" />-->\r\n    <!-- #endif -->\r\n    <!-- #ifdef APP-PLUS || H5 || MP-WEIXIN -->\r\n    <echartsUniapp v-else v-model:option=\"option\" v-model:mapName=\"mapName\" v-model:mapData=\"mapObject.data\"></echartsUniapp>\r\n    <!-- #endif -->\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted } from 'vue';\r\nimport { echartProps } from '@/pages-work/components/echarts/props'\r\nimport EchartsMap from \"../index.vue\";\r\nimport echartsUniapp from \"../../index.vue\";\r\nimport { deepMerge, handleTotalAndUnit, disposeGridLayout } from '../../../common/echartUtil'\r\nimport useChartHook from '@/pages-work/components/hooks/useEchartMap'\r\nimport {merge} from \"lodash-es\";\r\nimport {deepClone} from \"wot-design-uni/components/common/util\";\r\n// 定义 props\r\nconst props = defineProps({\r\n  ...echartProps\r\n});\r\n// 定义响应式数据\r\nconst option = ref({});\r\nconst chartOption = ref({\r\n  geo: {\r\n    map: '',\r\n    itemStyle: {},\r\n  },\r\n  tooltip: {\r\n    textStyle: {\r\n      color: \"#fff\"\r\n    },\r\n    padding: 5,\r\n    formatter: null\r\n  }\r\n});\r\nconst mapName = ref(\"\");\r\nlet [{ dataSource, reload, pageTips, config,mapDataJson,getAreaCode }, { queryData,registerMap,setGeoAreaColor,handleTotalAndUnitMap,handleCommonOpt }] = useChartHook(props, initOption)\r\nconst echartId = ref(\"\");\r\n// 计算属性\r\nconst mapObject = computed(() => ({ code: getAreaCode.value, data: mapDataJson.value }));\r\n// 初始化配置选项\r\nasync function initOption(){\r\n  let chartData = dataSource.value;\r\n  mapName.value = await registerMap();\r\n  try {\r\n    // 使用 registerMap 注册的地图名称\r\n    chartOption.value.geo.map = mapName.value;\r\n    // series 配置数据顺序调整后需要调整视觉映射属性 seriesIndex, seriesIndex 对应数据配置\r\n    chartOption.value.series = [\r\n      {\r\n        name: '地图',\r\n        type: 'map',\r\n        map: mapName.value,\r\n        geoIndex: 0,\r\n        aspectScale: 0.75, // 长宽比\r\n        showLegendSymbol: false, // 存在 legend 时显示\r\n        label: {\r\n          show: true,\r\n          color: '#000',\r\n        },\r\n        emphasis: {\r\n          show: true,\r\n          color: '#000',\r\n          itemStyle: {\r\n            areaColor: '#2B91B7',\r\n          },\r\n        },\r\n        roam: true,\r\n        itemStyle: {\r\n          areaColor: '#3B5077',\r\n          borderColor: '#3B5077',\r\n        },\r\n        animation: true,\r\n        data: chartData && chartData.length > 0 ? chartData : [],\r\n        zlevel: 1,\r\n      }\r\n    ];\r\n    // 合并配置\r\n    if (props.config && props.config.option) {\r\n      merge(chartOption.value, props.config.option);\r\n      chartOption.value = setGeoAreaColor(chartOption.value, props.config);\r\n      chartOption.value = handleTotalAndUnitMap(props.compName, chartOption.value, props.config, chartData);\r\n      chartOption.value = handleCommonOpt(chartOption.value);\r\n      setTimeout(() => {\r\n        // update-begin-author:liaozhiyang date:2023-11-30 for:【QQYUN-7226】地图数据不正确及 tooltip 颜色相近\r\n        chartOption.value.tooltip.textStyle.color = '#fff';\r\n        chartOption.value.tooltip.padding = 5;\r\n        chartOption.value.tooltip.formatter = (data) => {\r\n          if (data.data) {\r\n            return `${data.name}<br>${data.value[data.value.length - 1]}`;\r\n          } else {\r\n            return null;\r\n          }\r\n        };\r\n        // update-begin-author:liaozhiyang date:2023-11-30 for:【QQYUN-7226】地图数据不正确及 tooltip 颜色相近\r\n        option.value = deepClone(chartOption.value);\r\n        console.log(\"区域地图option.value\", option.value);\r\n        pageTips.show = false;\r\n        echartId.value = props.i\r\n      }, 300);\r\n    }\r\n    if (dataSource.value && dataSource.value.length === 0) {\r\n      pageTips.status = 1;\r\n      pageTips.show = true;\r\n    }\r\n  } catch (e) {\r\n    console.log(\"区域地图报错\", e);\r\n  }\r\n};\r\n\r\n// 挂载时查询数据\r\nonMounted(() => {\r\n  queryData();\r\n});\r\n</script>\r\n\r\n<style>\r\n.content {\r\n  margin: 5px;\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-work/components/echarts/map/JAreaMap/index.vue'\nwx.createComponent(Component)"], "names": ["ref", "useChartHook", "computed", "merge", "deepClone", "onMounted", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,MAAM,gBAAgB,MAAW;;;;;AAMjC,UAAM,QAAQ;AAId,UAAM,SAASA,cAAAA,IAAI,CAAA,CAAE;AACrB,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB,KAAK;AAAA,QACH,KAAK;AAAA,QACL,WAAW,CAAE;AAAA,MACd;AAAA,MACD,SAAS;AAAA,QACP,WAAW;AAAA,UACT,OAAO;AAAA,QACR;AAAA,QACD,SAAS;AAAA,QACT,WAAW;AAAA,MACZ;AAAA,IACH,CAAC;AACD,UAAM,UAAUA,cAAAA,IAAI,EAAE;AACtB,QAAI,CAAC,EAAE,YAAY,QAAQ,UAAU,QAAO,aAAY,eAAe,EAAE,WAAU,aAAY,iBAAgB,uBAAsB,gBAAiB,CAAA,IAAIC,wCAAY,aAAC,OAAO,UAAU;AACxL,UAAM,WAAWD,cAAAA,IAAI,EAAE;AAEvB,UAAM,YAAYE,cAAQ,SAAC,OAAO,EAAE,MAAM,YAAY,OAAO,MAAM,YAAY,MAAO,EAAC;AAEvF,aAAe,aAAY;AAAA;AACzB,YAAI,YAAY,WAAW;AAC3B,gBAAQ,QAAQ,MAAM;AACtB,YAAI;AAEF,sBAAY,MAAM,IAAI,MAAM,QAAQ;AAEpC,sBAAY,MAAM,SAAS;AAAA,YACzB;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,KAAK,QAAQ;AAAA,cACb,UAAU;AAAA,cACV,aAAa;AAAA;AAAA,cACb,kBAAkB;AAAA;AAAA,cAClB,OAAO;AAAA,gBACL,MAAM;AAAA,gBACN,OAAO;AAAA,cACR;AAAA,cACD,UAAU;AAAA,gBACR,MAAM;AAAA,gBACN,OAAO;AAAA,gBACP,WAAW;AAAA,kBACT,WAAW;AAAA,gBACZ;AAAA,cACF;AAAA,cACD,MAAM;AAAA,cACN,WAAW;AAAA,gBACT,WAAW;AAAA,gBACX,aAAa;AAAA,cACd;AAAA,cACD,WAAW;AAAA,cACX,MAAM,aAAa,UAAU,SAAS,IAAI,YAAY,CAAE;AAAA,cACxD,QAAQ;AAAA,YACT;AAAA,UACP;AAEI,cAAI,MAAM,UAAU,MAAM,OAAO,QAAQ;AACvCC,0BAAK,MAAC,YAAY,OAAO,MAAM,OAAO,MAAM;AAC5C,wBAAY,QAAQ,gBAAgB,YAAY,OAAO,MAAM,MAAM;AACnE,wBAAY,QAAQ,sBAAsB,MAAM,UAAU,YAAY,OAAO,MAAM,QAAQ,SAAS;AACpG,wBAAY,QAAQ,gBAAgB,YAAY,KAAK;AACrD,uBAAW,MAAM;AAEf,0BAAY,MAAM,QAAQ,UAAU,QAAQ;AAC5C,0BAAY,MAAM,QAAQ,UAAU;AACpC,0BAAY,MAAM,QAAQ,YAAY,CAAC,SAAS;AAC9C,oBAAI,KAAK,MAAM;AACb,yBAAO,GAAG,KAAK,IAAI,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC,CAAC;AAAA,gBACvE,OAAiB;AACL,yBAAO;AAAA,gBACR;AAAA,cACX;AAEQ,qBAAO,QAAQC,cAAAA,UAAU,YAAY,KAAK;AAC1C,sBAAQ,IAAI,oBAAoB,OAAO,KAAK;AAC5C,uBAAS,OAAO;AAChB,uBAAS,QAAQ,MAAM;AAAA,YACxB,GAAE,GAAG;AAAA,UACP;AACD,cAAI,WAAW,SAAS,WAAW,MAAM,WAAW,GAAG;AACrD,qBAAS,SAAS;AAClB,qBAAS,OAAO;AAAA,UACjB;AAAA,QACF,SAAQ,GAAG;AACV,kBAAQ,IAAI,UAAU,CAAC;AAAA,QACxB;AAAA,MACH;AAAA;AAGAC,kBAAAA,UAAU,MAAM;AACd;IACF,CAAC;;;;;;;;;;;;;;;;;;;;;ACrHD,GAAG,gBAAgBC,SAAS;"}