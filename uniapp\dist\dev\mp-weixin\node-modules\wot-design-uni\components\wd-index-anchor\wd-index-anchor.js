"use strict";
const common_vendor = require("../../../../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "wd-index-anchor",
  props: common_vendor.indexAnchorProps,
  setup(__props, { expose: __expose }) {
    const props = __props;
    const { parent: indexBar } = common_vendor.useParent(common_vendor.indexBarInjectionKey);
    const indexAnchorId = common_vendor.ref(`indexBar${common_vendor.uuid()}`);
    const { proxy } = common_vendor.getCurrentInstance();
    const top = common_vendor.ref(0);
    const isSticky = common_vendor.computed(() => {
      return indexBar && indexBar.props.sticky && indexBar.anchorState.activeIndex === props.index;
    });
    function getInfo() {
      common_vendor.getRect(`#${indexAnchorId.value}`, false, proxy).then((res) => {
        if (common_vendor.isDef(indexBar)) {
          top.value = Math.floor(res.top);
        }
      });
    }
    common_vendor.onMounted(() => {
      getInfo();
    });
    __expose({
      top
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.t(_ctx.index),
        b: common_vendor.n(`wd-index-anchor ${isSticky.value ? "is-sticky" : ""} ${_ctx.customClass}`),
        c: common_vendor.s(_ctx.customStyle),
        d: indexAnchorId.value
      };
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-4166cacc"]]);
wx.createComponent(Component);
//# sourceMappingURL=wd-index-anchor.js.map
