"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const service_app_pet = require("./pet.js");
function findPetsByStatusQueryOptions(options) {
  return common_vendor.queryOptions({
    queryFn: (_0) => __async(this, [_0], function* ({ queryKey }) {
      return service_app_pet.findPetsByStatus(queryKey[1]);
    }),
    queryKey: ["findPetsByStatus", options]
  });
}
exports.findPetsByStatusQueryOptions = findPetsByStatusQueryOptions;
//# sourceMappingURL=pet.vuequery.js.map
