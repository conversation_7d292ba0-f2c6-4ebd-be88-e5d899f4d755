"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
const utils_http = require("../../utils/http.js");
require("../../plugin/uni-mini-router/router/index.js");
const plugin_uniMiniRouter_core_index = require("../../plugin/uni-mini-router/core/index.js");
const store_user = require("../../store/user.js");
const store_pageParams = require("../../store/page-params.js");
const common_uitls = require("../../common/uitls.js");
if (!Array) {
  const _easycom_wd_search2 = common_vendor.resolveComponent("wd-search");
  const _easycom_wd_swipe_action2 = common_vendor.resolveComponent("wd-swipe-action");
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_search2 + _easycom_wd_swipe_action2 + _easycom_z_paging2 + _easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_wd_search = () => "../../node-modules/wot-design-uni/components/wd-search/wd-search.js";
const _easycom_wd_swipe_action = () => "../../node-modules/wot-design-uni/components/wd-swipe-action/wd-swipe-action.js";
const _easycom_z_paging = () => "../../uni_modules/z-paging/components/z-paging/z-paging.js";
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  (_easycom_wd_search + _easycom_wd_swipe_action + _easycom_z_paging + _easycom_PageLayout)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "online",
  options: {
    styleIsolation: "shared"
  }
}), {
  __name: "online",
  setup(__props) {
    const toast = common_vendor.useToast();
    const router = plugin_uniMiniRouter_core_index.useRouter();
    store_user.useUserStore();
    const paramsStore = store_pageParams.useParamsStore();
    const paging = common_vendor.ref(null);
    const dataList = common_vendor.ref([]);
    const keyword = common_vendor.ref("");
    const itemBgColor = [];
    const handleGo = (item) => {
      if (item.tableType === 3) {
        toast.warning("附表无列表页~");
      } else {
        paramsStore.setPageParams("onlineCard", { data: item });
        router.push({ name: "onlineCard" });
      }
    };
    function handleClear() {
      keyword.value = "";
      handleSearch();
    }
    function handleSearch() {
      queryList(1, 10);
    }
    const handleAction = (val, item) => {
      if (val == "del") {
        utils_http.http.delete("/online/cgform/head/delete", { id: item }).then((res) => {
          if (res.success) {
            toast.success("删除成功~");
            paging.value.reload();
          } else {
            toast.warning("删除失败~");
          }
        }).catch((res) => {
          toast.error("删除失败~");
        });
      } else if (val = "remove") {
        utils_http.http.delete("/online/cgform/head/removeRecord", { id: item }).then((res) => {
          if (res.success) {
            toast.success("移除成功~");
            paging.value.reload();
          } else {
            toast.warning("移除失败~");
          }
        }).catch((res) => {
          toast.error("移除失败~");
        });
      }
    };
    const getParams = ({ pageNo, pageSize }) => {
      const params = {
        pageNo,
        pageSize,
        order: "desc",
        column: "createTime"
      };
      if (keyword.value.length) {
        params.tableTxt = `*${keyword.value}*`;
      }
      return params;
    };
    const queryList = (pageNo, pageSize) => {
      const params = getParams({ pageNo, pageSize });
      utils_http.http.get("/online/cgform/head/list", __spreadValues({}, params)).then((res) => {
        var _a;
        if (res.success && ((_a = res.result) == null ? void 0 : _a.records)) {
          if (pageNo === 1) {
            dataList.value = [];
          }
          paging.value.complete(res.result.records);
        } else {
          paging.value.complete(false);
        }
      }).catch((res) => {
        paging.value.complete(false);
      });
    };
    const getBackgroundColor = (item, index) => {
      return itemBgColor[index % itemBgColor.length];
    };
    for (let i = 0; i < 50; i++) {
      itemBgColor.push(common_uitls.getRandomColor());
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(handleSearch),
        b: common_vendor.o(handleClear),
        c: common_vendor.o(common_vendor.m(($event) => common_vendor.isRef(keyword) ? keyword.value = $event : null, {
          trim: true
        }, true)),
        d: common_vendor.p({
          ["hide-cancel"]: true,
          placeholder: "请输入表描述",
          modelValue: common_vendor.unref(keyword)
        }),
        e: common_vendor.f(common_vendor.unref(dataList), (item, index, i0) => {
          return common_vendor.e$1({
            a: item.tableType != 3
          }, item.tableType != 3 ? {
            b: getBackgroundColor(item, index),
            c: common_vendor.t(item.tableTxt),
            d: common_vendor.t(item.createTime.substring(0, 10)),
            e: common_vendor.o(($event) => handleGo(item), item.id),
            f: common_vendor.o(($event) => handleAction("del", item), item.id),
            g: common_vendor.o(($event) => handleAction("remove", item), item.id),
            h: "b3a2b4cd-4-" + i0 + ",b3a2b4cd-2"
          } : {}, {
            i: item.id
          });
        }),
        f: common_vendor.sr(paging, "b3a2b4cd-2,b3a2b4cd-1", {
          "k": "paging"
        }),
        g: common_vendor.o(queryList),
        h: common_vendor.o(($event) => common_vendor.isRef(dataList) ? dataList.value = $event : null),
        i: common_vendor.p({
          fixed: false,
          ["default-page-size"]: 20,
          modelValue: common_vendor.unref(dataList)
        }),
        j: common_vendor.p({
          navTitle: "online表单开发",
          backRouteName: "index",
          routeMethod: "pushTab"
        })
      };
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-b3a2b4cd"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=online.js.map
