"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
const pagesWork_components_common_concants = require("../components/common/concants.js");
const utils_http = require("../../utils/http.js");
if (!Array) {
  const _easycom_wd_icon2 = common_vendor.resolveComponent("wd-icon");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_icon2 + _easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_wd_icon = () => "../../node-modules/wot-design-uni/components/wd-icon/wd-icon.js";
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  (dynamicComponent + _easycom_wd_icon + _easycom_PageLayout)();
}
const dynamicComponent = () => "../components/echarts/dynamic-component.js";
const scrollAnimation = false;
const scrollTop = 0;
const scrollToView = "";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "dragPage"
}), {
  __name: "index",
  setup(__props) {
    const title = common_vendor.ref("仪表盘示例");
    const pageId = common_vendor.ref("");
    common_vendor.ref("");
    common_vendor.ref({});
    const dragData = common_vendor.ref({
      name: "",
      compsData: [],
      style: "default"
    });
    const getStyle = common_vendor.computed(() => {
      return (component) => {
        let isSetHeight = component === "JDragEditor" ? false : true;
        if (component === "JText") {
          return {
            height: "auto",
            zIndex: 1e3
          };
        }
        if (component === "JCalendar") {
          return {
            minHeight: "600rpx",
            height: "auto",
            zIndex: 1e3
          };
        }
        if (component === "JList") {
          return {
            minHeight: "400rpx",
            height: "auto",
            zIndex: 1e3
          };
        }
        if (component === "JFilterQuery") {
          return {
            height: "auto",
            zIndex: 1e3
          };
        }
        return {
          minHeight: "600rpx",
          height: isSetHeight ? "600rpx" : "auto",
          zIndex: 1e3
        };
      };
    });
    function queryData() {
      utils_http.http.get("/drag/page/queryById", { id: common_vendor.unref(pageId) }).then((res) => {
        if (res.success && res.result) {
          let result = res.result;
          let template = result.template ? JSON.parse(result.template) : [];
          dragData.value.name = result.name;
          dragData.value.style = (result == null ? void 0 : result.style) || "default";
          title.value = result.name;
          template.forEach((item) => {
            if (item.component === "JFilterQuery") {
              item["mobileY"] = 0;
            } else {
              item["mobileY"] = item["mobileY"] || item["mobileY"] == 0 ? item["mobileY"] : 1;
            }
            if (item.config.filter && !item.config.filter.customTime) {
              item.config.filter["customTime"] = [];
            }
          });
          template.sort((a, b) => a.mobileY - b.mobileY);
          dragData.value.compsData = template || [];
        }
      });
    }
    common_vendor.onLoad((option) => {
      let params = option;
      pageId.value = params.id;
      queryData();
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.f(common_vendor.unref(dragData).compsData, (item, index, i0) => {
          var _a;
          return common_vendor.e$1({
            a: common_vendor.unref(pagesWork_components_common_concants.compList).includes(item.component)
          }, common_vendor.unref(pagesWork_components_common_concants.compList).includes(item.component) ? {
            b: "72b6585f-2-" + i0 + ",72b6585f-1",
            c: common_vendor.p({
              compName: item.component,
              i: item.i,
              config: item.config,
              size: (_a = item.config) == null ? void 0 : _a.size
            })
          } : {
            d: "72b6585f-3-" + i0 + ",72b6585f-1",
            e: common_vendor.p({
              name: "info-circle-filled",
              size: "64px"
            })
          }, {
            f: "drag" + item.i,
            g: common_vendor.s(common_vendor.unref(getStyle)(item.component)),
            h: index
          });
        }),
        b: common_vendor.n(common_vendor.unref(dragData).style == "bigScreen" ? "bg-white" : "bg-white"),
        c: scrollAnimation,
        d: scrollTop,
        e: scrollToView,
        f: common_vendor.p({
          navTitle: common_vendor.unref(title)
        })
      };
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-72b6585f"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=index.js.map
