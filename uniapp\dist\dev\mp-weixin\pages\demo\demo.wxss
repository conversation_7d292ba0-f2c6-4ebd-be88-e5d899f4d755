/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.mb-2.data-v-ef01f733 {
  margin-bottom: 10px;
}
.box.data-v-ef01f733 {
  background-color: #fff;
  margin: 25px 16px;
}
.box .title.data-v-ef01f733 {
  padding: 10px;
  padding-bottom: 0;
  font-size: 15;
  color: #666666;
  margin-bottom: 20rpx;
}
.box .content.data-v-ef01f733 .wd-button,
.box .content.data-v-ef01f733 .imgView {
  margin: 10px;
}
.box .content.data-v-ef01f733 .wd-button.info {
  background-color: #909cb8;
}
.box .content.data-v-ef01f733 .wd-button.warning {
  background-color: #f0863b;
}
.box .content.data-v-ef01f733 .wd-button.success {
  background-color: #33d19d;
}
.box .content.data-v-ef01f733 .wd-button.error {
  background-color: #f04550;
}
.router.data-v-ef01f733 {
  padding: 30px 15px;
  display: flex;
  flex-wrap: wrap;
}
.router .wd-button.data-v-ef01f733 {
  margin-bottom: 15px;
}
.router .wd-button.data-v-ef01f733:nth-child(3), .router .wd-button.data-v-ef01f733:nth-child(4) {
  margin-bottom: 0;
}
.data-v-ef01f733 .wd-cell-group {
  margin: 0 26rpx;
  border-radius: 18rpx;
  overflow: hidden;
  --wot-cell-line-height: 32px;
}
.data-v-ef01f733 .wd-cell-group .wd-icon {
  margin-right: 10px;
}
.data-v-ef01f733 .wd-cell-group .wd-cell {
  --wot-cell-title-fs: 15px;
  --wot-cell-title-color: var(--color-gray);
}
.data-v-ef01f733 .wd-cell-group .wd-cell .wd-cell__left {
  font-size: 15px;
  font-weight: 300;
}