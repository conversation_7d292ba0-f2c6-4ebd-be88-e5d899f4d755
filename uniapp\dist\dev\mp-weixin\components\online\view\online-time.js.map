{"version": 3, "file": "online-time.js", "sources": ["../../../../../../src/components/online/view/online-time.vue", "../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvY29tcG9uZW50cy9vbmxpbmUvdmlldy9vbmxpbmUtdGltZS52dWU"], "sourcesContent": ["<template>\r\n  <wd-datetime-picker :disabled=\"disabled\" type=\"time\" :labelWidth=\"labelWidth\" v-model=\"currentTime\" :label=\"label\" @confirm=\"handleConfirm\" />\r\n</template>\r\n\r\n<script setup>\r\n// 定义 props\r\nconst props = defineProps({\r\n  label: {\r\n    type: String,\r\n    default: '',\r\n    required: false,\r\n  },\r\n  labelWidth: {\r\n    type: String,\r\n    default: '80px',\r\n    required: false,\r\n  },\r\n  name: {\r\n    type: String,\r\n    default: '',\r\n    required: false,\r\n  },\r\n  type: {\r\n    type: String,\r\n    default: '',\r\n    required: false,\r\n  },\r\n  value: {\r\n    type: [String, Number],\r\n    required: false,\r\n  },\r\n  disabled: {\r\n    type: Boolean,\r\n    default: false,\r\n    required: false,\r\n  },\r\n})\r\n\r\n// 定义 emits\r\nconst emit = defineEmits(['input', 'change', 'update:value'])\r\n// 定义响应式数据;\r\nconst currentTime = ref('')\r\n\r\n// 监听 value 的变化\r\nwatch(\r\n    () => props.value,\r\n    (val) => {\r\n        currentTime.value  = val?val:'';\r\n    }\r\n)\r\n// 选择器改变事件处理函数\r\nconst handleConfirm = (e) => {\r\n  emit('update:value', currentTime.value+':00')\r\n  emit('change', currentTime.value+':00')\r\n}\r\n</script>\r\n\r\n<style></style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/components/online/view/online-time.vue'\nwx.createComponent(Component)"], "names": ["ref", "watch", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,UAAM,QAAQ;AAiCd,UAAM,OAAO;AAEb,UAAM,cAAcA,cAAG,IAAC,EAAE;AAG1BC,kBAAK;AAAA,MACD,MAAM,MAAM;AAAA,MACZ,CAAC,QAAQ;AACL,oBAAY,QAAS,MAAI,MAAI;AAAA,MAChC;AAAA,IACL;AAEA,UAAM,gBAAgB,CAAC,MAAM;AAC3B,WAAK,gBAAgB,YAAY,QAAM,KAAK;AAC5C,WAAK,UAAU,YAAY,QAAM,KAAK;AAAA,IACxC;;;;;;;;;;;;;;;;ACrDA,GAAG,gBAAgBC,SAAS;"}