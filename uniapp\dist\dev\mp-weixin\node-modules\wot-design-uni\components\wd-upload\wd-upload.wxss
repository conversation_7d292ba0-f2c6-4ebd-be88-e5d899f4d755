/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 辅助函数
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/* 转换成字符串 */
/* 判断是否存在 Modifier */
/* 判断是否存在伪类 */
/**
 * 主题色切换
 * @params $theme-color 主题色
 * @params $type 变暗’dark‘ 变亮 'light'
 * @params $mix-color 自己设置的混色
 */
/**
 * 颜色结果切换， 如果开启线性渐变色 使用渐变色，如果没有开启，那么使用主题色
 * @params $open-linear 是否开启线性渐变色
 * @params $deg 渐变色角度
 * @params $theme-color 当前配色
 * @params [Array] $set 主题色明暗设置，与 $color-list 数量对应
 * @params [Array] $color-list 渐变色顺序， $color-list 和 $per-list 数量相同
 * @params [Array] $per-list 渐变色比例
 */
/**
 * UI规范基础变量
 */
/*----------------------------------------- Theme color. start ----------------------------------------*/
/* 主题颜色 */
/* 辅助色 */
/* 文字颜色（默认浅色背景下 */
/* 暗黑模式 */
/* 图形颜色 */
/*----------------------------------------- Theme color. end -------------------------------------------*/
/*-------------------------------- Theme color application size.  start --------------------------------*/
/* 文字字号 */
/* 文字字重 */
/* 尺寸 */
/*-------------------------------- Theme color application size.  end --------------------------------*/
/* component var */
/* action-sheet */
/* badge */
/* button */
/* cell */
/* calendar */
/* checkbox */
/* collapse */
/* divider */
/* drop-menu */
/* input-number */
/* input */
/* textarea */
/* loadmore */
/* message-box */
/* notice-bar */
/* pagination */
/* picker */
/* col-picker */
/* overlay */
/* popup */
/* progress */
/* radio */
/* search */
/* slider */
/* sort-button */
/* steps */
/* switch */
/* tabs */
/* tag */
/* toast */
/* loading */
/* tooltip */
/* popover */
/* grid-item */
/* statustip */
/* card */
/* upload */
/* curtain */
/* notify */
/* skeleton */
/* circle */
/* swiper */
/* swiper-nav */
/* segmented */
/* tabbar */
/* tabbar-item */
/* navbar */
/* navbar-capsule */
/* table */
/* sidebar */
/* sidebar-item */
/* fab */
/* count-down */
/* keyboard */
/* number-keyboard */
/* passwod-input */
/* form-item */
/* backtop */
/* index-bar */
/* text */
/* video-preview */
/* img-cropper */
/* floating-panel */
/* signature */
/**
 * 混合宏
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/**
 * 辅助函数
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/* 转换成字符串 */
/* 判断是否存在 Modifier */
/* 判断是否存在伪类 */
/**
 * 主题色切换
 * @params $theme-color 主题色
 * @params $type 变暗’dark‘ 变亮 'light'
 * @params $mix-color 自己设置的混色
 */
/**
 * 颜色结果切换， 如果开启线性渐变色 使用渐变色，如果没有开启，那么使用主题色
 * @params $open-linear 是否开启线性渐变色
 * @params $deg 渐变色角度
 * @params $theme-color 当前配色
 * @params [Array] $set 主题色明暗设置，与 $color-list 数量对应
 * @params [Array] $color-list 渐变色顺序， $color-list 和 $per-list 数量相同
 * @params [Array] $per-list 渐变色比例
 */
/**
  * BEM，定义块（b)
  */
/* 定义元素（e），对于伪类，会自动将 e 嵌套在 伪类 底下 */
/* 此方法用于生成穿透样式 */
/* 定义元素（e），对于伪类，会自动将 e 嵌套在 伪类 底下 */
/* 定义状态（m） */
/* 定义状态（m） */
/* 对于需要需要嵌套在 m 底下的 e，调用这个混合宏，一般在切换整个组件的状态，如切换颜色的时候 */
/* 状态，生成 is-$state 类名 */
/**
  * 常用混合宏
  */
/* 单行超出隐藏 */
/* 多行超出隐藏 */
/* 清除浮动 */
/* 0.5px 边框 指定方向*/
/* 0.5px 边框 环绕 */
/**
  * 三角形实现尖角样式，适用于背景透明情况
  * @param $size 三角形高，底边为 $size * 2
  * @param $bg 三角形背景颜色
  */
/**
  * 正方形实现尖角样式，适用于背景不透明情况
  * @param $size 正方形边长
  * @param $bg 正方形背景颜色
  * @param $z-index z-index属性值，不得大于外部包裹器
  * @param $box-shadow 阴影
*/
.wot-theme-dark .wd-upload__evoke.data-v-7c014dc0 {
  background-color: var(--wot-dark-background4, #323233);
  color: var(--wot-dark-color3, rgba(232, 230, 227, 0.8));
}
.wot-theme-dark .wd-upload__evoke.is-disabled.data-v-7c014dc0 {
  color: var(--wot-dark-color-gray, var(--wot-color-secondary, #595959));
}
.wot-theme-dark .wd-upload__file.data-v-7c014dc0 {
  background-color: var(--wot-dark-background4, #323233);
}
.wot-theme-dark .wd-upload__file-name.data-v-7c014dc0 {
  color: var(--wot-dark-color3, rgba(232, 230, 227, 0.8));
}
.wd-upload.data-v-7c014dc0 {
  position: relative;
  display: flex;
  flex-wrap: wrap;
}
.wd-upload__evoke.data-v-7c014dc0 {
  position: relative;
  display: inline-flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: var(--wot-upload-size, 80px);
  height: var(--wot-upload-size, 80px);
  font-size: var(--wot-upload-evoke-icon-size, 32px);
  background-color: var(--wot-upload-evoke-bg, rgba(0, 0, 0, 0.04));
  color: var(--wot-upload-evoke-color, rgba(0, 0, 0, 0.25));
  margin-bottom: 12px;
}
.wd-upload__evoke.is-disabled.data-v-7c014dc0 {
  color: var(--wot-upload-evoke-disabled-color, rgba(0, 0, 0, 0.09));
}
.wd-upload__evoke-num.data-v-7c014dc0 {
  font-size: 14px;
  line-height: 14px;
  margin-top: 8px;
}
.data-v-7c014dc0  .wd-upload__evoke-icon {
  width: 32px;
  height: 32px;
}
.wd-upload__input.data-v-7c014dc0 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  opacity: 0;
}
.wd-upload__preview.data-v-7c014dc0 {
  position: relative;
  width: var(--wot-upload-size, 80px);
  height: var(--wot-upload-size, 80px);
  margin: 0 12px 12px 0;
}
.wd-upload__preview-list.data-v-7c014dc0 {
  display: flex;
}
.wd-upload__picture.data-v-7c014dc0, .wd-upload__file.data-v-7c014dc0, .wd-upload__video.data-v-7c014dc0 {
  position: relative;
  display: block;
  width: 100%;
  height: 100%;
}
.wd-upload__file.data-v-7c014dc0, .wd-upload__video.data-v-7c014dc0 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--wot-upload-evoke-bg, rgba(0, 0, 0, 0.04));
}
.wd-upload__video-icon.data-v-7c014dc0, .wd-upload__file-icon.data-v-7c014dc0 {
  font-size: var(--wot-upload-cover-icon-size, 22px);
}
.wd-upload__file-name.data-v-7c014dc0, .wd-upload__video-name.data-v-7c014dc0 {
  width: 100%;
  font-size: var(--wot-upload-file-fs, 12px);
  color: var(--wot-upload-file-color, var(--wot-color-secondary, #595959));
  box-sizing: border-box;
  padding: 0 4px;
  text-align: center;
  margin-top: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.data-v-7c014dc0  .wd-upload__video-paly {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24px;
  color: var(--wot-color-white, rgb(255, 255, 255));
}
.data-v-7c014dc0  .wd-upload__video-paly::before {
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
}
.data-v-7c014dc0  .wd-upload__close {
  position: absolute;
  right: calc(var(--wot-upload-close-icon-size, 16px) / 2 * -1);
  top: calc(var(--wot-upload-close-icon-size, 16px) / 2 * -1);
  font-size: var(--wot-upload-close-icon-size, 16px);
  z-index: 1;
  color: var(--wot-upload-close-icon-color, rgba(0, 0, 0, 0.65));
  width: var(--wot-upload-close-icon-size, 16px);
  height: var(--wot-upload-close-icon-size, 16px);
  line-height: var(--wot-upload-close-icon-size, 16px);
}
.data-v-7c014dc0  .wd-upload__close::after {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: var(--wot-color-white, rgb(255, 255, 255));
  left: 0;
  z-index: -1;
}
.wd-upload__mask.data-v-7c014dc0 {
  position: absolute;
  top: 0;
  left: 0;
  background-color: var(--wot-upload-preview-name-bg, rgba(0, 0, 0, 0.6));
}
.wd-upload__progress-txt.data-v-7c014dc0 {
  font-size: var(--wot-upload-progress-fs, 14px);
  line-height: var(--wot-upload-progress-fs, 14px);
  margin-top: 9px;
  color: var(--wot-color-white, rgb(255, 255, 255));
}
.data-v-7c014dc0  .wd-upload__icon {
  font-size: var(--wot-upload-preview-icon-size, 24px);
  color: var(--wot-color-white, rgb(255, 255, 255));
}
.wd-upload__status-content.data-v-7c014dc0 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}