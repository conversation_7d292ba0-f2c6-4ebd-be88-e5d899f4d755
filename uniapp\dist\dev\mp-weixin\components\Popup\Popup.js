"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _easycom_wd_input2 = common_vendor.resolveComponent("wd-input");
  _easycom_wd_input2();
}
const _easycom_wd_input = () => "../../node-modules/wot-design-uni/components/wd-input/wd-input.js";
if (!Math) {
  (_easycom_wd_input + popupReportModal)();
}
const popupReportModal = () => "./components/popupReportModal.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "Popup",
  options: {
    styleIsolation: "shared"
  }
}), {
  __name: "Popup",
  props: {
    code: {
      type: String,
      required: true,
      default: ""
    },
    fieldConfig: {
      type: Array,
      required: true,
      default: () => []
    },
    setFieldsValue: {
      type: Function,
      required: true,
      default: () => {
      }
    },
    modelValue: {
      type: String,
      default: ""
    },
    multi: {
      type: Boolean,
      default: false
    },
    spliter: {
      type: String,
      default: ","
    }
  },
  emits: ["change", "update:modelValue"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const toast = common_vendor.useToast();
    const showText = common_vendor.ref("");
    const attrs = common_vendor.useAttrs();
    const reportModal = common_vendor.reactive({
      show: false,
      showFiled: props.fieldConfig.map((item) => item["target"]).join(",")
    });
    if (!props.code || props.fieldConfig.length == 0) {
      toast.error("popup参数未正确配置!");
    }
    common_vendor.watch(
      () => props.modelValue,
      (val) => {
        showText.value = val && val.length > 0 ? val.split(props.spliter).join(",") : "";
      },
      { immediate: true }
    );
    function callBack(rows) {
      let fieldConfig = props.fieldConfig;
      let values = {};
      let labels = [];
      for (let item of fieldConfig) {
        let val = rows.map((row) => row[item.source]);
        val = val.length == 1 ? val[0] : val.join(",");
        item.target.split(",").forEach((target) => {
          values[target] = val;
        });
      }
      showText.value = labels.join(",");
      props.setFieldsValue(values);
      emit("change", values);
    }
    const handleClick = () => {
      if (!attrs.disabled) {
        reportModal.show = true;
      }
    };
    const handleClose = () => {
      reportModal.show = false;
    };
    const handleChange = (data) => {
      console.log("选中的值：", data);
      callBack(data);
    };
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.o(($event) => showText.value = $event),
        b: common_vendor.p(__spreadProps(__spreadValues({
          placeholder: `请选择${_ctx.$attrs.label}`,
          type: "text",
          readonly: true,
          clearable: true
        }, _ctx.$attrs), {
          modelValue: showText.value
        })),
        c: common_vendor.o(handleClick),
        d: common_vendor.unref(reportModal).show
      }, common_vendor.unref(reportModal).show ? {
        e: common_vendor.o(handleClose),
        f: common_vendor.o(handleChange),
        g: common_vendor.p({
          code: __props.code,
          showFiled: common_vendor.unref(reportModal).showFiled,
          multi: __props.multi
        })
      } : {});
    };
  }
}));
wx.createComponent(_sfc_main);
//# sourceMappingURL=Popup.js.map
