{"version": 3, "file": "useEchart.js", "sources": ["../../../../../../src/pages-work/components/hooks/useEchart.ts"], "sourcesContent": ["import {\r\n  packageParams,\r\n  handleCalcFields,\r\n  handleDateFields,\r\n  getGeoCoordMap,\r\n  handleParam,\r\n  checkUrlPrefix,\r\n  dictTransform,\r\n  getUrlParams\r\n} from '../common/echartUtil'\r\nimport {\r\n  fieldMappings\r\n} from '../common/concants'\r\nimport { china } from '../common/china'\r\nimport {isFunction, isUrl} from '@/utils/is'\r\nimport { http } from '@/utils/http'\r\nimport { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'\r\nimport {isArray} from \"../../common/is\";\r\nexport default function useChartHook(props, initOption?, echarts?) {\r\n  const config = props.config\r\n  const dataSource = ref([])\r\n  const reload = ref(true)\r\n  const pageTips = reactive({\r\n    show: true,\r\n    status: 0, // 0:loading,1:暂无数据,2:网络超时\r\n  })\r\n  const toast = useToast()\r\n  //地图数据\r\n  const areaCode = ref('')\r\n  const mapDataJson = ref({})\r\n  //操作图表配置项\r\n  let chartOption = {\r\n    title: {\r\n      show: true,\r\n    },\r\n    card: {\r\n      title: '',\r\n    },\r\n    tooltip: {\r\n      formatter: '',\r\n    },\r\n    legend: {\r\n      bottom: '5%',\r\n      left: 'center',\r\n    },\r\n    xAxis: {\r\n      type: 'category',\r\n      data: [],\r\n    },\r\n    yAxis: {\r\n      type: 'value',\r\n    },\r\n    series: [{}] as any,\r\n  }\r\n  //监听配置修改\r\n  watch(\r\n    props.config,\r\n    (config) => {\r\n      if (!props?.isView) {\r\n        console.log('=======props.config============')\r\n        queryData()\r\n      }\r\n    },\r\n    { deep: true },\r\n  )\r\n\r\n  /**\r\n   * 查询数据\r\n   * @param compConfig\r\n   * @param queryParams\r\n   */\r\n  function queryData(compConfig?, queryParams?) {\r\n    let config = compConfig ? compConfig : { ...props.config }\r\n    if (config.dataType == 2) {\r\n      //判断是否走代理\r\n      if (config.dataSetId && config.dataSetType == 'api' && config.dataSetIzAgent !== '1') {\r\n        //不走代理直接请求接口  url参数处理\r\n        let { url, dataMap } = handleParam(config)\r\n        //TODO 联动钻取处理\r\n        let linkParams = {}\r\n        queryParams = Object.assign({}, dataMap, queryParams, linkParams)\r\n        if (url.startsWith('#{api_base_path}') || url.startsWith('{{ domainURL }}')) {\r\n          getAgentData(queryParams, config)\r\n        } else {\r\n          let checkUrl = checkUrlPrefix(url)\r\n          if (checkUrl.isDiffProtocol) {\r\n            toast.warning('请求API地址需要https协议接口！')\r\n            return\r\n          }\r\n          getCompData({ url, queryParams }).then((res: any) => {\r\n            dataSource.value = res.data || res\r\n            if (res?.result && isArray(res?.result)) {\r\n              dataSource.value = res.result\r\n            } else if (res?.result?.records && isArray(res?.result.records)) {\r\n              dataSource.value = res.result.records\r\n            }\r\n            getDataCallBack()\r\n          })\r\n        }\r\n      }else if (config.dataSetType == 'websocket'){\r\n\r\n      }else {\r\n        let { dataMap } = handleParam(config)\r\n        //TODO 联动钻取处理\r\n        let linkParams = {}\r\n        queryParams = Object.assign({}, dataMap, queryParams, linkParams)\r\n        getAgentData(queryParams,config);\r\n      }\r\n    } else if (config.dataType == 4) {\r\n      //查询配置\r\n      let params = getParams(config, queryParams)\r\n      //查询数据\r\n      http.post('/drag/onlDragDatasetHead/getTotalData', params).then((res: any) => {\r\n        if (res.success) {\r\n          let result = res.result.chartData\r\n          if (result && result.length > 0) {\r\n            try {\r\n              let arr = JSON.parse(JSON.stringify(result))\r\n              dataSource.value = handleDateFields(arr, config)\r\n              dataSource.value = handleCalcFields(arr, config.valueFields, config.assistYFields)\r\n              initOption && isFunction(initOption) && initOption()\r\n            } catch (e) {\r\n              console.log('查询数据报错', e)\r\n            }\r\n          } else {\r\n            dataSource.value = []\r\n            initOption && isFunction(initOption) && initOption()\r\n          }\r\n        }\r\n      })\r\n    } else {\r\n      //静态数据\r\n      let chartData = props.config.chartData\r\n      if (typeof chartData === 'string') {\r\n        try {\r\n          chartData = JSON.parse(chartData as string)\r\n        } catch (e) {}\r\n      }\r\n      dataSource.value = chartData\r\n      initOption && initOption(chartData)\r\n    }\r\n  }\r\n  /**\r\n   * 根据接口获取组件数据\r\n   * @param option\r\n   */\r\n   const getCompData = (option) => {\r\n    //接口地址\r\n    let { url, params:queryParams } = getUrlParams(option.url);\r\n    if (!isUrl(url)) {\r\n      console.info('url', option.url);\r\n      console.info('请求地址有问题', option.url);\r\n      return;\r\n    }\r\n    //请求类型默认get\r\n    let method = option.method ? option.method : 'get';\r\n    //请求参数\r\n    let params = option.params ? option.params : {};\r\n    Object.assign(params,queryParams)\r\n    //是否使用服务的代理\r\n    let serverAgent = option.serverAgent ? option.serverAgent : false;\r\n    if (serverAgent) {\r\n      //使用服务端代理是传递option到代理服务器\r\n      params = option;\r\n    }\r\n    return new Promise((resolve, reject) => {\r\n      uni.request({\r\n        method: method,\r\n        params: params,\r\n        transformRequest: [\r\n          function (data) {\r\n            //格式化为字符串。\r\n            return JSON.stringify({ ...data });\r\n          },\r\n        ],\r\n        url: url,\r\n      })\r\n      .then((res) => {\r\n        resolve(res.data);\r\n      })\r\n      .catch((err) => {\r\n        reject(err.data);\r\n      });\r\n    });\r\n  };\r\n  /**\r\n   * 获取后端接口请求的数据\r\n   * @param params\r\n   */\r\n  function getAgentData(params, config) {\r\n    http\r\n      .post('/drag/onlDragDatasetHead/getAllChartData', {\r\n        id: config.dataSetId,\r\n        params,\r\n        dataMapping: config.dataMapping,\r\n      })\r\n      .then((res: any) => {\r\n        if (res.success) {\r\n          let result = res.result\r\n          let data = result.data || []\r\n          dataSource.value = JSON.parse(JSON.stringify(data))\r\n          //字典翻译\r\n          dataSource.value = dictTransform(dataSource.value, result.dictOptions)\r\n          config.dictOptions = result.dictOptions\r\n          getDataCallBack()\r\n        } else {\r\n          dataSource.value = []\r\n          toast.warning('查询失败')\r\n        }\r\n      })\r\n  }\r\n\r\n  /**\r\n   * 获取数据后刷新图表\r\n   * @param chartData\r\n   */\r\n  function getDataCallBack() {\r\n    //映射转换\r\n    dataSource.value = dataTransform(dataSource.value, config.dataMapping)\r\n    //返回数据处理\r\n    handleData()\r\n    //渲染数据\r\n    console.log(\"getDataCallBack\",dataSource.value)\r\n    initOption && initOption(dataSource.value)\r\n  }\r\n    /**\r\n     * 数据处理 转换 条数过滤\r\n     * @param chartData\r\n     * @param dataMapping\r\n     */\r\n    function handleData() {\r\n      //先处理数据过滤，再处理数据返回条数\r\n      //数据过滤\r\n      dataFilter();\r\n      //数据条数过滤\r\n      dataNumFilter();\r\n    }\r\n\r\n  /**\r\n   * 过滤数据\r\n   */\r\n  function dataFilter() {\r\n    if (config.dataFilter) {\r\n      //执行数据过滤器\r\n      try {\r\n        const func = new Function('data', config.dataFilter);\r\n        dataSource.value = func(dataSource.value);\r\n        console.info('过滤后的数据:', dataSource.value);\r\n      } catch (e) {\r\n        console.info('过滤器异常:', e);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 数据条数控制\r\n   */\r\n  function dataNumFilter() {\r\n    let value = dataSource.value;\r\n    if (value && isArray(value) && value.length > 0) {\r\n      let totalNum = value.length;\r\n      let dataNum = config.dataNum || 0;\r\n      if (dataNum > 0 && dataNum < totalNum) {\r\n        dataSource.value = dataSource.value.slice(0, dataNum);\r\n      }\r\n    }\r\n  }\r\n  /**\r\n   * 图表的转换数据\r\n   */\r\n  function dataTransform(chartData, dataMapping) {\r\n    if (dataMapping && Array.isArray(chartData)) {\r\n      let newChartData = []\r\n      chartData.forEach((data) => {\r\n        let obj: any = { ...data }\r\n        try {\r\n          //执行数据转换\r\n          dataMapping.forEach((item) => {\r\n            //update-begin-author:liusq---date:2024-12-18--for:  issues/7554统计卡片，动态数据，某些数值、总数等无法展示，大小写导致\r\n            let value =\r\n              item['mapping'] != null\r\n                ? data[item['mapping']] ??\r\n                  data[\r\n                    typeof item['mapping'] === 'string' ? item['mapping'].toUpperCase() : null\r\n                  ] ??\r\n                  null\r\n                : null\r\n            //update-end-author:liusq---date:2024-12-18--for:  issues/7554统计卡片，动态数据，某些数值、总数等无法展示，大小写导致\r\n            fieldMappings.forEach((field) => {\r\n              if (item['filed'] == field['label']) {\r\n                //防止返回数据本身就有name、value、type等和映射字段冲突问题\r\n                if (['name', 'type', 'value'].includes(field['key'])) {\r\n                  obj[field['key']] = value || value == 0 ? value : ''\r\n                } else {\r\n                  obj[field['key']] =\r\n                    value || value == 0\r\n                      ? value\r\n                      : data[field['key']] || data[field['key'].toUpperCase()]\r\n                }\r\n              }\r\n            })\r\n          })\r\n        } catch (e) {\r\n          console.info('转换异常:', e)\r\n        }\r\n        newChartData.push(obj)\r\n      })\r\n      console.info('转换后的数据:', newChartData)\r\n      return newChartData\r\n    }\r\n    return chartData\r\n  }\r\n  /**\r\n   * 获取参数\r\n   * @param config\r\n   * @param params\r\n   */\r\n  function getParams(config, params) {\r\n    let queryParams = packageParams(config, params)\r\n    return {\r\n      tableName: config.tableName,\r\n      compName: config.compName,\r\n      config: {\r\n        type: config.typeFields || [],\r\n        name: config.nameFields || [],\r\n        value: config.valueFields || [],\r\n        assistValue: config.assistYFields || [],\r\n        assistType: config.assistTypeFields || [],\r\n        formType: config.formType,\r\n      },\r\n      condition: {\r\n        ...queryParams,\r\n      },\r\n    }\r\n  }\r\n\r\n  return [\r\n    { dataSource, reload, pageTips, config, chartOption, mapDataJson },\r\n    { queryData },\r\n  ]\r\n}\r\n"], "names": ["ref", "reactive", "useToast", "watch", "config", "handleParam", "checkUrlPrefix", "isArray", "http", "handleDateFields", "handleCalcFields", "isFunction", "getUrlParams", "isUrl", "uni", "dictTransform", "fieldMappings", "packageParams"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAkBwB,SAAA,aAAa,OAAO,YAAa,SAAU;AACjE,QAAM,SAAS,MAAM;AACf,QAAA,aAAaA,cAAI,IAAA,EAAE;AACnB,QAAA,SAASA,kBAAI,IAAI;AACvB,QAAM,WAAWC,cAAAA,SAAS;AAAA,IACxB,MAAM;AAAA,IACN,QAAQ;AAAA;AAAA,EAAA,CACT;AACD,QAAM,QAAQC,cAAAA,SAAS;AAENF,gBAAAA,IAAI,EAAE;AACjB,QAAA,cAAcA,cAAI,IAAA,EAAE;AAE1B,MAAI,cAAc;AAAA,IAChB,OAAO;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,MAAM;AAAA,MACJ,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,IACb;AAAA,IACA,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAA;AAAA,IACR;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,QAAQ,CAAC,CAAE,CAAA;AAAA,EACb;AAEAG,gBAAA;AAAA,IACE,MAAM;AAAA,IACN,CAACC,YAAW;AACN,UAAA,EAAC,+BAAO,SAAQ;AAClB,gBAAQ,IAAI,iCAAiC;AACnC,kBAAA;AAAA,MAAA;AAAA,IAEd;AAAA,IACA,EAAE,MAAM,KAAK;AAAA,EACf;AAOS,WAAA,UAAU,YAAa,aAAc;AAC5C,QAAIA,UAAS,aAAa,aAAa,mBAAK,MAAM;AAC9CA,QAAAA,QAAO,YAAY,GAAG;AAExB,UAAIA,QAAO,aAAaA,QAAO,eAAe,SAASA,QAAO,mBAAmB,KAAK;AAEpF,YAAI,EAAE,KAAK,YAAYC,uCAAAA,YAAYD,OAAM;AAEzC,YAAI,aAAa,CAAC;AAClB,sBAAc,OAAO,OAAO,CAAI,GAAA,SAAS,aAAa,UAAU;AAChE,YAAI,IAAI,WAAW,kBAAkB,KAAK,IAAI,WAAW,iBAAiB,GAAG;AAC3E,uBAAa,aAAaA,OAAM;AAAA,QAAA,OAC3B;AACD,cAAA,WAAWE,sDAAe,GAAG;AACjC,cAAI,SAAS,gBAAgB;AAC3B,kBAAM,QAAQ,qBAAqB;AACnC;AAAA,UAAA;AAEF,sBAAY,EAAE,KAAK,YAAa,CAAA,EAAE,KAAK,CAAC,QAAa;;AACxC,uBAAA,QAAQ,IAAI,QAAQ;AAC/B,iBAAI,2BAAK,WAAUC,oBAAQ,QAAA,2BAAK,MAAM,GAAG;AACvC,yBAAW,QAAQ,IAAI;AAAA,YAAA,aACd,gCAAK,WAAL,mBAAa,YAAWA,4BAAQ,2BAAK,OAAO,OAAO,GAAG;AACpD,yBAAA,QAAQ,IAAI,OAAO;AAAA,YAAA;AAEhB,4BAAA;AAAA,UAAA,CACjB;AAAA,QAAA;AAAA,MAEL,WAAUH,QAAO,eAAe;AAAY;AAAA,WAEtC;AACJ,YAAI,EAAE,QAAA,IAAYC,uCAAA,YAAYD,OAAM;AAEpC,YAAI,aAAa,CAAC;AAClB,sBAAc,OAAO,OAAO,CAAI,GAAA,SAAS,aAAa,UAAU;AAChE,qBAAa,aAAYA,OAAM;AAAA,MAAA;AAAA,IACjC,WACSA,QAAO,YAAY,GAAG;AAE3B,UAAA,SAAS,UAAUA,SAAQ,WAAW;AAE1CI,iBAAA,KAAK,KAAK,yCAAyC,MAAM,EAAE,KAAK,CAAC,QAAa;AAC5E,YAAI,IAAI,SAAS;AACX,cAAA,SAAS,IAAI,OAAO;AACpB,cAAA,UAAU,OAAO,SAAS,GAAG;AAC3B,gBAAA;AACF,kBAAI,MAAM,KAAK,MAAM,KAAK,UAAU,MAAM,CAAC;AAChC,yBAAA,QAAQC,wDAAiB,KAAKL,OAAM;AAC/C,yBAAW,QAAQM,wDAAiB,KAAKN,QAAO,aAAaA,QAAO,aAAa;AACnE,4BAAAO,SAAA,WAAW,UAAU,KAAK,WAAW;AAAA,qBAC5C,GAAG;AACF,sBAAA,IAAI,UAAU,CAAC;AAAA,YAAA;AAAA,UACzB,OACK;AACL,uBAAW,QAAQ,CAAC;AACN,0BAAAA,SAAA,WAAW,UAAU,KAAK,WAAW;AAAA,UAAA;AAAA,QACrD;AAAA,MACF,CACD;AAAA,IAAA,OACI;AAED,UAAA,YAAY,MAAM,OAAO;AACzB,UAAA,OAAO,cAAc,UAAU;AAC7B,YAAA;AACU,sBAAA,KAAK,MAAM,SAAmB;AAAA,iBACnC,GAAG;AAAA,QAAA;AAAA,MAAC;AAEf,iBAAW,QAAQ;AACnB,oBAAc,WAAW,SAAS;AAAA,IAAA;AAAA,EACpC;AAMK,QAAA,cAAc,CAAC,WAAW;AAE/B,QAAI,EAAE,KAAK,QAAO,YAAgB,IAAAC,uCAAA,aAAa,OAAO,GAAG;AACrD,QAAA,CAACC,SAAAA,MAAM,GAAG,GAAG;AACP,cAAA,KAAK,OAAO,OAAO,GAAG;AACtB,cAAA,KAAK,WAAW,OAAO,GAAG;AAClC;AAAA,IAAA;AAGF,QAAI,SAAS,OAAO,SAAS,OAAO,SAAS;AAE7C,QAAI,SAAS,OAAO,SAAS,OAAO,SAAS,CAAC;AACvC,WAAA,OAAO,QAAO,WAAW;AAEhC,QAAI,cAAc,OAAO,cAAc,OAAO,cAAc;AAC5D,QAAI,aAAa;AAEN,eAAA;AAAA,IAAA;AAEX,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCC,oBAAAA,MAAI,QAAQ;AAAA,QACV;AAAA,QACA;AAAA,QACA,kBAAkB;AAAA,UAChB,SAAU,MAAM;AAEd,mBAAO,KAAK,UAAU,mBAAK,KAAM;AAAA,UAAA;AAAA,QAErC;AAAA,QACA;AAAA,MAAA,CACD,EACA,KAAK,CAAC,QAAQ;AACb,gBAAQ,IAAI,IAAI;AAAA,MAAA,CACjB,EACA,MAAM,CAAC,QAAQ;AACd,eAAO,IAAI,IAAI;AAAA,MAAA,CAChB;AAAA,IAAA,CACF;AAAA,EACH;AAKS,WAAA,aAAa,QAAQV,SAAQ;AACpCI,eAAA,KACG,KAAK,4CAA4C;AAAA,MAChD,IAAIJ,QAAO;AAAA,MACX;AAAA,MACA,aAAaA,QAAO;AAAA,IAAA,CACrB,EACA,KAAK,CAAC,QAAa;AAClB,UAAI,IAAI,SAAS;AACf,YAAI,SAAS,IAAI;AACb,YAAA,OAAO,OAAO,QAAQ,CAAC;AAC3B,mBAAW,QAAQ,KAAK,MAAM,KAAK,UAAU,IAAI,CAAC;AAElD,mBAAW,QAAQW,uCAAA,cAAc,WAAW,OAAO,OAAO,WAAW;AACrEX,gBAAO,cAAc,OAAO;AACZ,wBAAA;AAAA,MAAA,OACX;AACL,mBAAW,QAAQ,CAAC;AACpB,cAAM,QAAQ,MAAM;AAAA,MAAA;AAAA,IACtB,CACD;AAAA,EAAA;AAOL,WAAS,kBAAkB;AAEzB,eAAW,QAAQ,cAAc,WAAW,OAAO,OAAO,WAAW;AAE1D,eAAA;AAEH,YAAA,IAAI,mBAAkB,WAAW,KAAK;AAChC,kBAAA,WAAW,WAAW,KAAK;AAAA,EAAA;AAOzC,WAAS,aAAa;AAGT,eAAA;AAEG,kBAAA;AAAA,EAAA;AAMlB,WAAS,aAAa;AACpB,QAAI,OAAO,YAAY;AAEjB,UAAA;AACF,cAAM,OAAO,IAAI,SAAS,QAAQ,OAAO,UAAU;AACxC,mBAAA,QAAQ,KAAK,WAAW,KAAK;AAChC,gBAAA,KAAK,WAAW,WAAW,KAAK;AAAA,eACjC,GAAG;AACF,gBAAA,KAAK,UAAU,CAAC;AAAA,MAAA;AAAA,IAC1B;AAAA,EACF;AAMF,WAAS,gBAAgB;AACvB,QAAI,QAAQ,WAAW;AACvB,QAAI,SAASG,oBAAAA,QAAQ,KAAK,KAAK,MAAM,SAAS,GAAG;AAC/C,UAAI,WAAW,MAAM;AACjB,UAAA,UAAU,OAAO,WAAW;AAC5B,UAAA,UAAU,KAAK,UAAU,UAAU;AACrC,mBAAW,QAAQ,WAAW,MAAM,MAAM,GAAG,OAAO;AAAA,MAAA;AAAA,IACtD;AAAA,EACF;AAKO,WAAA,cAAc,WAAW,aAAa;AAC7C,QAAI,eAAe,MAAM,QAAQ,SAAS,GAAG;AAC3C,UAAI,eAAe,CAAC;AACV,gBAAA,QAAQ,CAAC,SAAS;AACtB,YAAA,MAAW,mBAAK;AAChB,YAAA;AAEU,sBAAA,QAAQ,CAAC,SAAS;;AAExB,gBAAA,QACF,KAAK,SAAS,KAAK,QACf,gBAAK,KAAK,SAAS,CAAC,MAApB,YACA,KACE,OAAO,KAAK,SAAS,MAAM,WAAW,KAAK,SAAS,EAAE,gBAAgB,IACxE,MAHA,YAIA,OACA;AAEQS,+DAAA,QAAQ,CAAC,UAAU;AAC/B,kBAAI,KAAK,OAAO,KAAK,MAAM,OAAO,GAAG;AAE/B,oBAAA,CAAC,QAAQ,QAAQ,OAAO,EAAE,SAAS,MAAM,KAAK,CAAC,GAAG;AACpD,sBAAI,MAAM,KAAK,CAAC,IAAI,SAAS,SAAS,IAAI,QAAQ;AAAA,gBAAA,OAC7C;AACL,sBAAI,MAAM,KAAK,CAAC,IACd,SAAS,SAAS,IACd,QACA,KAAK,MAAM,KAAK,CAAC,KAAK,KAAK,MAAM,KAAK,EAAE,aAAa;AAAA,gBAAA;AAAA,cAC7D;AAAA,YACF,CACD;AAAA,UAAA,CACF;AAAA,iBACM,GAAG;AACF,kBAAA,KAAK,SAAS,CAAC;AAAA,QAAA;AAEzB,qBAAa,KAAK,GAAG;AAAA,MAAA,CACtB;AACO,cAAA,KAAK,WAAW,YAAY;AAC7B,aAAA;AAAA,IAAA;AAEF,WAAA;AAAA,EAAA;AAOA,WAAA,UAAUZ,SAAQ,QAAQ;AAC7B,QAAA,cAAca,uCAAAA,cAAcb,SAAQ,MAAM;AACvC,WAAA;AAAA,MACL,WAAWA,QAAO;AAAA,MAClB,UAAUA,QAAO;AAAA,MACjB,QAAQ;AAAA,QACN,MAAMA,QAAO,cAAc,CAAC;AAAA,QAC5B,MAAMA,QAAO,cAAc,CAAC;AAAA,QAC5B,OAAOA,QAAO,eAAe,CAAC;AAAA,QAC9B,aAAaA,QAAO,iBAAiB,CAAC;AAAA,QACtC,YAAYA,QAAO,oBAAoB,CAAC;AAAA,QACxC,UAAUA,QAAO;AAAA,MACnB;AAAA,MACA,WAAW,mBACN;AAAA,IAEP;AAAA,EAAA;AAGK,SAAA;AAAA,IACL,EAAE,YAAY,QAAQ,UAAU,QAAQ,aAAa,YAAY;AAAA,IACjE,EAAE,UAAU;AAAA,EACd;AACF;;"}