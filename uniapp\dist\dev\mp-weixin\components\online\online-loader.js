"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const components_online_FormProperty = require("./FormProperty.js");
const components_online_defaultVal = require("./defaultVal.js");
const utils_http = require("../../utils/http.js");
const utils_is = require("../../utils/is.js");
const common_uitls = require("../../common/uitls.js");
const service_api = require("../../service/api.js");
if (!Array) {
  const _easycom_wd_cell2 = common_vendor.resolveComponent("wd-cell");
  const _easycom_wd_input2 = common_vendor.resolveComponent("wd-input");
  const _easycom_wd_switch2 = common_vendor.resolveComponent("wd-switch");
  const _easycom_wd_textarea2 = common_vendor.resolveComponent("wd-textarea");
  const _easycom_PopupDict2 = common_vendor.resolveComponent("PopupDict");
  const _easycom_Popup2 = common_vendor.resolveComponent("Popup");
  const _easycom_CategorySelect2 = common_vendor.resolveComponent("CategorySelect");
  const _easycom_TreeSelect2 = common_vendor.resolveComponent("TreeSelect");
  const _easycom_wd_cell_group2 = common_vendor.resolveComponent("wd-cell-group");
  const _easycom_wd_form2 = common_vendor.resolveComponent("wd-form");
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  (_easycom_wd_cell2 + _easycom_wd_input2 + _easycom_wd_switch2 + _easycom_wd_textarea2 + _easycom_PopupDict2 + _easycom_Popup2 + _easycom_CategorySelect2 + _easycom_TreeSelect2 + _easycom_wd_cell_group2 + _easycom_wd_form2 + _easycom_wd_button2)();
}
const _easycom_wd_cell = () => "../../node-modules/wot-design-uni/components/wd-cell/wd-cell.js";
const _easycom_wd_input = () => "../../node-modules/wot-design-uni/components/wd-input/wd-input.js";
const _easycom_wd_switch = () => "../../node-modules/wot-design-uni/components/wd-switch/wd-switch.js";
const _easycom_wd_textarea = () => "../../node-modules/wot-design-uni/components/wd-textarea/wd-textarea.js";
const _easycom_PopupDict = () => "../PopupDict/PopupDict.js";
const _easycom_Popup = () => "../Popup/Popup.js";
const _easycom_CategorySelect = () => "../CategorySelect/CategorySelect.js";
const _easycom_TreeSelect = () => "../TreeSelect/TreeSelect.js";
const _easycom_wd_cell_group = () => "../../node-modules/wot-design-uni/components/wd-cell-group/wd-cell-group.js";
const _easycom_wd_form = () => "../../node-modules/wot-design-uni/components/wd-form/wd-form.js";
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
if (!Math) {
  (OnlineImage + _easycom_wd_cell + OnlineDatetime + OnlineDate + OnlineTime + OnlineSelect + OnlineCheckbox + OnlineRadio + OnlineMulti + OnlinePca + _easycom_wd_input + _easycom_wd_switch + _easycom_wd_textarea + _easycom_PopupDict + _easycom_Popup + OnlinePopupLinkRecord + SelectUser + SelectDept + _easycom_CategorySelect + _easycom_TreeSelect + _easycom_wd_cell_group + _easycom_wd_form + _easycom_wd_button)();
}
const OnlineImage = () => "./view/online-image.js";
const OnlineSelect = () => "./view/online-select.js";
const OnlineTime = () => "./view/online-time.js";
const OnlineDatetime = () => "./view/online-datetime.js";
const OnlineDate = () => "./view/online-date.js";
const OnlineRadio = () => "./view/online-radio.js";
const OnlineCheckbox = () => "./view/online-checkbox.js";
const OnlineMulti = () => "./view/online-multi.js";
const OnlinePca = () => "./view/online-pca.js";
const OnlinePopupLinkRecord = () => "./view/online-popup-link-record.js";
const SelectDept = () => "../SelectDept/SelectDept.js";
const SelectUser = () => "../SelectUser/SelectUser.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "online-loader",
  props: {
    showHeader: {
      type: Boolean,
      required: false,
      default: false
    },
    table: {
      type: String,
      default: "",
      required: false
    },
    taskId: {
      type: String,
      default: "",
      required: false
    },
    showFooter: {
      type: Boolean,
      required: false,
      default: true
    },
    edit: {
      type: Boolean,
      default: false,
      required: false
    },
    flowEdit: {
      type: Boolean,
      default: false,
      required: false
    },
    dataId: {
      type: String,
      default: "",
      required: false
    },
    title: {
      type: String,
      default: "",
      required: false
    },
    disabled: {
      type: Boolean,
      default: false,
      required: false
    },
    onlyBackData: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  emits: ["back", "success", "formSuccess"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    const toast = common_vendor.useToast();
    const onlinekey = common_vendor.ref(1);
    const reFresh = common_vendor.ref(false);
    const url = common_vendor.ref({
      load: "/online/cgform/api/getFormItemBytbname/",
      optPre: "/online/cgform/api/form/"
    });
    const code = common_vendor.ref("");
    const single = common_vendor.ref(false);
    const treeForm = common_vendor.ref(false);
    const tableTxt = common_vendor.ref("");
    const tableName = common_vendor.ref("");
    const tableType = common_vendor.ref(1);
    const formData = common_vendor.ref({});
    const hasFillRuleFields = common_vendor.ref("");
    const hasRequiredFields = common_vendor.ref([]);
    const rootProperties = common_vendor.ref([]);
    common_vendor.ref([]);
    const loading = common_vendor.ref(false);
    const formDataId = common_vendor.ref("");
    const navTitle = common_vendor.computed(() => {
      if (!props.title || props.title.length === 0) {
        return tableTxt.value;
      } else {
        return props.title;
      }
    });
    const labelWidth = common_vendor.computed(() => {
      return "100px";
    });
    const get4Label = common_vendor.computed(() => {
      return (lable) => {
        return `${lable && lable.length > 4 ? lable.substring(0, 4) : lable}：`;
      };
    });
    common_vendor.onMounted(() => {
      console.log("开始渲染online表单");
    });
    const getDateExtendType = (formSchema) => {
      if (formSchema.fieldExtendJson) {
        let fieldExtendJson = JSON.parse(formSchema.fieldExtendJson);
        let mapField = {
          month: "year-month",
          year: "year",
          quarter: "date",
          week: "date",
          day: "date"
        };
        return (fieldExtendJson == null ? void 0 : fieldExtendJson.picker) && mapField[fieldExtendJson == null ? void 0 : fieldExtendJson.picker] ? mapField[fieldExtendJson == null ? void 0 : fieldExtendJson.picker] : "date";
      }
      return "date";
    };
    const isChecked = (opts, value) => {
      return opts && opts.length > 0 ? value === opts[0] : false;
    };
    const switchOpt = (opts, index) => {
      const options = Array.isArray(opts) && opts.length > 0 ? opts : ["Y", "N"];
      return options[index] + "";
    };
    const componentDisabled = (item) => {
      if (props.disabled === true || !props.showFooter && !props.onlyBackData) {
        return true;
      }
      return item.disabled;
    };
    const fieldRequired = (item) => {
      return (item == null ? void 0 : item.key) && hasRequiredFields.value.includes(item.key);
    };
    const linkRecordChange = (linkRecord, key) => {
      let linkFieldArr = rootProperties.value.filter(
        (item) => {
          var _a;
          return item.type === "link_table_field" && ((_a = item == null ? void 0 : item.formSchema) == null ? void 0 : _a.dictTable) == key;
        }
      );
      linkFieldArr.forEach((field) => {
        let value = linkRecord.map((record) => record[field.formSchema.dictText]).join(",");
        common_vendor.nextTick$1(() => {
          formData.value[field.key] = value;
        });
      });
    };
    const backRoute = () => {
      emits("back");
    };
    function handleMultiOrDateField() {
      let finalData = common_vendor.deepClone(formData.value);
      let dateFieldArr = rootProperties.value.filter(
        (item) => item.type === "date" || item.type === "datetime"
      );
      let pcaArr = rootProperties.value.filter((item) => item.type === "pca");
      finalData = Object.keys(finalData).reduce((acc, key) => {
        let value = finalData[key];
        if (value && pcaArr.length > 0 && pcaArr.map((item) => item.key).includes(key)) {
          console.log("省市区获取最后一位value", value);
          value = utils_is.isArray(value) ? value[2] : value.split(",")[2];
        }
        if (value && utils_is.isArray(value)) {
          value = value.join(",");
        }
        if (dateFieldArr.length > 0) {
          const dateField = dateFieldArr.find((obj) => obj.key === key);
          if (dateField) {
            value = value && utils_is.isNumber(value) ? common_uitls.formatDate(
              value,
              dateField.type === "datetime" ? "yyyy-MM-dd HH:mm:ss" : "yyyy-MM-dd"
            ) : value;
          }
        }
        acc[key] = value;
        return acc;
      }, {});
      return finalData;
    }
    const formSubmit = (e) => __async(this, null, function* () {
      if (yield fieldCheck(formData.value)) {
        return;
      }
      let finalData = yield handleMultiOrDateField();
      if (props.onlyBackData) {
        emits("success", finalData);
        return;
      }
      if (props.flowEdit === true)
        ;
      else if (props.edit === true) {
        yield handleEdit(finalData);
      } else {
        yield handleSave(finalData);
      }
    });
    const fieldCheck = (values) => __async(this, null, function* () {
      var _a, _b;
      let flag = false;
      for (const item of rootProperties.value) {
        const tip = (msg) => {
          toast.warning(msg);
          flag = true;
        };
        if (fieldRequired(item) && !values[item.key]) {
          tip(`${item.label}不能为空！`);
          break;
        }
        let pattern = (_a = item == null ? void 0 : item.formSchema) == null ? void 0 : _a.pattern;
        if (pattern) {
          if (pattern == "only") {
            const res = yield service_api.duplicateCheck({
              tableName: tableName.value,
              fieldName: item.key,
              fieldVal: values[item.key],
              dataId: formDataId.value
            });
            if (!res.success) {
              tip(`${item.label} ${res.message}`);
              break;
            }
          } else {
            const regex = new RegExp(pattern);
            if (values[item.key] && pattern && !regex.test(values[item.key])) {
              let errorInfo = ((_b = item == null ? void 0 : item.formSchema) == null ? void 0 : _b.errorInfo) || "格式不正确!";
              tip(`${item.label}${errorInfo}`);
              break;
            }
          }
        }
      }
      return flag;
    });
    const handleSave = (finalData) => {
      if (finalData == null ? void 0 : finalData.bpm_status) {
        finalData.bpm_status = "1";
      }
      console.log("===onlineForm表单组件走新增保存  handleSave===", finalData);
      utils_http.http.post(`${url.value.optPre}${code.value}?tabletype=${tableType.value}`, finalData).then((res) => {
        if (res.success) {
          emits("success", res.result);
        } else {
          toast.warning(res.message);
        }
      });
    };
    const handleEdit = (finalData) => {
      utils_http.http.put(`${url.value.optPre}${code.value}?tabletype=${tableType.value}`, finalData).then((res) => {
        if (res.success) {
          emits("success", formData.value.id);
        } else {
          toast.warning(res.message);
        }
      });
    };
    const loadByTableName = (dataID) => {
      formDataId.value = props.dataId;
      if (dataID && dataID.length > 0) {
        formDataId.value = dataID;
      }
      let urlStr = url.value.load + props.table;
      utils_http.http.get(urlStr, { taskId: props.taskId }).then((res) => {
        if (res.success) {
          console.log("===onlineForm加载表单数据 schema===", res.result.schema);
          let config = res.result;
          code.value = config.head.id;
          single.value = config.head.tableType === 1;
          tableType.value = config.head.tableType;
          createRootProperties(config.schema);
          treeForm.value = config.head.isTree === "Y";
          tableTxt.value = config.head.tableTxt;
          if (props.edit === true || props.flowEdit === true) {
            loadFormData();
          } else {
            handleDefaultValue();
          }
        } else {
          toast.info(res.message);
        }
      });
    };
    const createRootProperties = (formSchema) => {
      var _a;
      tableName.value = formSchema.table;
      formData.value = {};
      hasFillRuleFields.value = formSchema.hasFillRuleFields;
      hasRequiredFields.value = (_a = formSchema == null ? void 0 : formSchema.required) != null ? _a : [];
      const properties = formSchema.properties;
      let rootProps = [];
      console.log("===onlineForm表单配置项 properties===", properties);
      Object.keys(properties).map((key) => {
        if (key) {
          const item = properties[key];
          if (item.view === "tab")
            ;
          else {
            formData.value[key] = "";
            let fp = components_online_FormProperty.FormProperty(key, item, formSchema.required);
            rootProps.push(fp);
          }
        }
      });
      rootProps.sort((one, next) => {
        return one.formSchema.order - next.formSchema.order;
      });
      rootProperties.value = [...rootProps];
      console.log("--rootProperties--", rootProps);
      common_vendor.nextTick$1(() => {
        reFresh.value = true;
        onlinekey.value += 1;
      });
      emits("formSuccess", true);
    };
    const getFieldNumberType = (item) => {
      return item.onlyInteger === true ? "digit" : "number";
    };
    const loadFormData = () => {
      let urlStr = url.value.optPre + code.value + "/" + formDataId.value;
      urlStr = urlStr.replace(/"/g, "");
      utils_http.http.get(urlStr).then((res) => {
        if (res.success) {
          formData.value = __spreadValues({}, res.result);
          console.log("===onlineForm表单组件走获取表单数据 loadFormData===", formData.value);
          reFresh.value = false;
          common_vendor.nextTick$1(() => {
            reFresh.value = true;
          });
        }
      });
    };
    const handleDefaultValue = () => {
      console.log("===onlineForm表单组件走默认值 handleDefaultValue===");
      rootProperties.value.forEach((item) => {
        let field = item.key;
        let { defVal, type } = item.formSchema;
        components_online_defaultVal.loadOneFieldDefVal(defVal, type, (value) => {
          formData.value[field] = value;
        });
      });
    };
    const setFieldsValue = (data) => {
      formData.value = __spreadValues(__spreadValues({}, formData.value), data);
    };
    const getPopupFieldConfig = (item) => {
      const { formSchema } = item;
      const { destFields = "", orgFields = "" } = formSchema;
      const result = orgFields.split(",").map((oField, index) => {
        return {
          source: oField,
          target: destFields.split(",")[index]
        };
      });
      return result;
    };
    __expose({
      navTitle,
      getDateExtendType,
      isChecked,
      componentDisabled,
      fieldRequired,
      backRoute,
      formSubmit,
      loadByTableName,
      getFieldNumberType
    });
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.f(common_vendor.unref(rootProperties), (item, index, i0) => {
          var _a, _b;
          return common_vendor.e$1({
            a: item.type == "image"
          }, item.type == "image" ? {
            b: index,
            c: "091ea86c-3-" + i0 + "," + ("091ea86c-2-" + i0),
            d: common_vendor.o(($event) => common_vendor.unref(formData)[item.key] = $event, index),
            e: common_vendor.p({
              name: item.key,
              disabled: componentDisabled(item),
              value: common_vendor.unref(formData)[item.key]
            }),
            f: "091ea86c-2-" + i0 + ",091ea86c-1",
            g: common_vendor.p({
              name: item.key,
              title: common_vendor.unref(get4Label)(item.label),
              ["title-width"]: common_vendor.unref(labelWidth),
              required: fieldRequired(item)
            })
          } : item.type == "file" ? {
            i: index,
            j: "091ea86c-5-" + i0 + "," + ("091ea86c-4-" + i0),
            k: common_vendor.o(($event) => common_vendor.unref(formData)[item.key] = $event, index),
            l: common_vendor.p({
              uploadFileType: "all",
              name: item.key,
              disabled: componentDisabled(item),
              value: common_vendor.unref(formData)[item.key]
            }),
            m: "091ea86c-4-" + i0 + ",091ea86c-1",
            n: common_vendor.p({
              name: item.key,
              title: common_vendor.unref(get4Label)(item.label),
              ["title-width"]: common_vendor.unref(labelWidth),
              required: fieldRequired(item)
            })
          } : item.type === "datetime" ? {
            p: "091ea86c-6-" + i0 + ",091ea86c-1",
            q: common_vendor.o(($event) => common_vendor.unref(formData)[item.key] = $event, index),
            r: common_vendor.p({
              label: common_vendor.unref(get4Label)(item.label),
              labelWidth: common_vendor.unref(labelWidth),
              name: item.key,
              disabled: componentDisabled(item),
              required: fieldRequired(item),
              value: common_vendor.unref(formData)[item.key]
            })
          } : item.type === "date" ? {
            t: "091ea86c-7-" + i0 + ",091ea86c-1",
            v: common_vendor.o(($event) => common_vendor.unref(formData)[item.key] = $event, index),
            w: common_vendor.p({
              label: common_vendor.unref(get4Label)(item.label),
              labelWidth: common_vendor.unref(labelWidth),
              name: item.key,
              type: getDateExtendType(item.formSchema),
              disabled: componentDisabled(item),
              required: fieldRequired(item),
              value: common_vendor.unref(formData)[item.key]
            })
          } : item.type === "time" ? {
            y: "091ea86c-8-" + i0 + ",091ea86c-1",
            z: common_vendor.o(($event) => common_vendor.unref(formData)[item.key] = $event, index),
            A: common_vendor.p({
              label: common_vendor.unref(get4Label)(item.label),
              labelWidth: common_vendor.unref(labelWidth),
              name: item.key,
              disabled: componentDisabled(item),
              required: fieldRequired(item),
              value: common_vendor.unref(formData)[item.key]
            })
          } : item.type === "list" || item.type === "sel_search" ? {
            C: "091ea86c-9-" + i0 + ",091ea86c-1",
            D: common_vendor.o(($event) => common_vendor.unref(formData)[item.key] = $event, index),
            E: common_vendor.p({
              label: common_vendor.unref(get4Label)(item.label),
              labelWidth: common_vendor.unref(labelWidth),
              name: item.key,
              type: item.type,
              dict: item.listSource,
              dictStr: item.dictStr,
              disabled: componentDisabled(item),
              required: fieldRequired(item),
              modelValue: common_vendor.unref(formData)[item.key]
            })
          } : item.type === "checkbox" ? {
            G: "091ea86c-10-" + i0 + ",091ea86c-1",
            H: common_vendor.o(($event) => common_vendor.unref(formData)[item.key] = $event, index),
            I: common_vendor.p({
              name: item.key,
              type: item.type,
              label: common_vendor.unref(get4Label)(item.label),
              labelWidth: common_vendor.unref(labelWidth),
              dict: item.listSource,
              disabled: componentDisabled(item),
              required: fieldRequired(item),
              modelValue: common_vendor.unref(formData)[item.key]
            })
          } : item.type === "radio" ? {
            K: "091ea86c-11-" + i0 + ",091ea86c-1",
            L: common_vendor.o(($event) => common_vendor.unref(formData)[item.key] = $event, index),
            M: common_vendor.p({
              name: item.key,
              label: common_vendor.unref(get4Label)(item.label),
              labelWidth: common_vendor.unref(labelWidth),
              type: item.type,
              dict: item.listSource,
              disabled: componentDisabled(item),
              required: fieldRequired(item),
              modelValue: common_vendor.unref(formData)[item.key]
            })
          } : item.type === "list_multi" ? {
            O: "091ea86c-12-" + i0 + ",091ea86c-1",
            P: common_vendor.o(($event) => common_vendor.unref(formData)[item.key] = $event, index),
            Q: common_vendor.p({
              label: common_vendor.unref(get4Label)(item.label),
              labelWidth: common_vendor.unref(labelWidth),
              name: item.key,
              dict: item.listSource,
              disabled: componentDisabled(item),
              required: fieldRequired(item),
              modelValue: common_vendor.unref(formData)[item.key]
            })
          } : item.type === "pca" ? {
            S: "091ea86c-13-" + i0 + ",091ea86c-1",
            T: common_vendor.o(($event) => common_vendor.unref(formData)[item.key] = $event, index),
            U: common_vendor.p({
              name: item.key,
              label: common_vendor.unref(get4Label)(item.label),
              labelWidth: common_vendor.unref(labelWidth),
              disabled: componentDisabled(item),
              required: fieldRequired(item),
              value: common_vendor.unref(formData)[item.key]
            })
          } : item.type === "number" && (!item.onlyInteger || item.onlyInteger == false) ? {
            W: "091ea86c-14-" + i0 + ",091ea86c-1",
            X: common_vendor.o(($event) => common_vendor.unref(formData)[item.key] = $event, index),
            Y: common_vendor.p({
              ["label-width"]: common_vendor.unref(labelWidth),
              label: common_vendor.unref(get4Label)(item.label),
              name: item.key,
              inputMode: "decimal",
              disabled: componentDisabled(item),
              placeholder: item.placeholder,
              rules: item.rules,
              modelValue: common_vendor.unref(formData)[item.key]
            })
          } : item.type === "number" && item.onlyInteger === true ? {
            aa: "091ea86c-15-" + i0 + ",091ea86c-1",
            ab: common_vendor.o(($event) => common_vendor.unref(formData)[item.key] = $event, index),
            ac: common_vendor.p({
              ["label-width"]: common_vendor.unref(labelWidth),
              label: common_vendor.unref(get4Label)(item.label),
              name: item.key,
              inputMode: "numeric",
              disabled: componentDisabled(item),
              placeholder: item.placeholder,
              rules: item.rules,
              modelValue: common_vendor.unref(formData)[item.key]
            })
          } : item.type == "switch" ? {
            ae: "091ea86c-17-" + i0 + "," + ("091ea86c-16-" + i0),
            af: common_vendor.o(($event) => common_vendor.unref(formData)[item.key] = $event, index),
            ag: common_vendor.p({
              label: common_vendor.unref(get4Label)(item.label),
              name: item.key,
              size: "18px",
              disabled: componentDisabled(item),
              ["active-value"]: switchOpt((_a = item.formSchema) == null ? void 0 : _a.extendOption, 0),
              ["inactive-value"]: switchOpt((_b = item.formSchema) == null ? void 0 : _b.extendOption, 1),
              modelValue: common_vendor.unref(formData)[item.key]
            }),
            ah: "091ea86c-16-" + i0 + ",091ea86c-1",
            ai: common_vendor.p({
              name: item.key,
              title: common_vendor.unref(get4Label)(item.label),
              ["title-width"]: common_vendor.unref(labelWidth),
              center: true,
              required: fieldRequired(item)
            })
          } : ["textarea", "markdown", "umeditor"].includes(item.type) ? {
            ak: "091ea86c-18-" + i0 + ",091ea86c-1",
            al: common_vendor.o(($event) => common_vendor.unref(formData)[item.key] = $event, index),
            am: common_vendor.p({
              ["label-width"]: common_vendor.unref(labelWidth),
              label: common_vendor.unref(get4Label)(item.label),
              name: item.key,
              clearable: true,
              maxlength: 300,
              disabled: componentDisabled(item),
              placeholder: item.placeholder,
              rules: item.rules,
              modelValue: common_vendor.unref(formData)[item.key]
            })
          } : item.type === "password" ? {
            ao: "091ea86c-19-" + i0 + ",091ea86c-1",
            ap: common_vendor.o(($event) => common_vendor.unref(formData)[item.key] = $event, index),
            aq: common_vendor.p({
              ["label-width"]: common_vendor.unref(labelWidth),
              disabled: componentDisabled(item),
              label: common_vendor.unref(get4Label)(item.label),
              name: item.key,
              placeholder: item.placeholder,
              rules: item.rules,
              ["show-password"]: true,
              modelValue: common_vendor.unref(formData)[item.key]
            })
          } : item.type === "popup_dict" ? {
            as: "091ea86c-20-" + i0 + ",091ea86c-1",
            at: common_vendor.o(($event) => common_vendor.unref(formData)[item.key] = $event, index),
            av: common_vendor.p({
              ["label-width"]: common_vendor.unref(labelWidth),
              label: common_vendor.unref(get4Label)(item.label),
              disabled: componentDisabled(item),
              required: fieldRequired(item),
              multi: item.formSchema.popupMulti,
              dictCode: `${item.formSchema.code},${item.formSchema["destFields"]},${item.formSchema["orgFields"]}`,
              modelValue: common_vendor.unref(formData)[item.key]
            })
          } : item.type === "popup" ? {
            ax: "091ea86c-21-" + i0 + ",091ea86c-1",
            ay: common_vendor.o(($event) => common_vendor.unref(formData)[item.key] = $event, index),
            az: common_vendor.p({
              ["label-width"]: common_vendor.unref(labelWidth),
              label: common_vendor.unref(get4Label)(item.label),
              disabled: componentDisabled(item),
              required: fieldRequired(item),
              multi: item.formSchema.popupMulti,
              code: `${item.formSchema.code}`,
              setFieldsValue,
              fieldConfig: getPopupFieldConfig(item),
              modelValue: common_vendor.unref(formData)[item.key]
            })
          } : item.type === "link_table" ? {
            aB: common_vendor.o(linkRecordChange, index),
            aC: "091ea86c-22-" + i0 + ",091ea86c-1",
            aD: common_vendor.o(($event) => item.formSchema = $event, index),
            aE: common_vendor.o(($event) => common_vendor.unref(formData)[item.key] = $event, index),
            aF: common_vendor.p({
              ["label-width"]: common_vendor.unref(labelWidth),
              label: common_vendor.unref(get4Label)(item.label),
              name: item.key,
              disabled: componentDisabled(item),
              required: fieldRequired(item),
              formSchema: item.formSchema,
              value: common_vendor.unref(formData)[item.key]
            })
          } : item.type === "link_table_field" ? {
            aH: "091ea86c-23-" + i0 + ",091ea86c-1",
            aI: common_vendor.o(($event) => common_vendor.unref(formData)[item.key] = $event, index),
            aJ: common_vendor.p({
              ["label-width"]: common_vendor.unref(labelWidth),
              disabled: true,
              label: common_vendor.unref(get4Label)(item.label),
              name: item.key,
              modelValue: common_vendor.unref(formData)[item.key]
            })
          } : item.type === "sel_user" ? {
            aL: "091ea86c-24-" + i0 + ",091ea86c-1",
            aM: common_vendor.o(($event) => common_vendor.unref(formData)[item.key] = $event, index),
            aN: common_vendor.p({
              ["label-width"]: common_vendor.unref(labelWidth),
              name: item.key,
              label: common_vendor.unref(get4Label)(item.label),
              disabled: componentDisabled(item),
              required: fieldRequired(item),
              modelValue: common_vendor.unref(formData)[item.key]
            })
          } : item.type === "sel_depart" ? {
            aP: "091ea86c-25-" + i0 + ",091ea86c-1",
            aQ: common_vendor.o(($event) => common_vendor.unref(formData)[item.key] = $event, index),
            aR: common_vendor.p({
              ["label-width"]: common_vendor.unref(labelWidth),
              name: item.key,
              label: common_vendor.unref(get4Label)(item.label),
              labelKey: "departName",
              disabled: componentDisabled(item),
              required: fieldRequired(item),
              modelValue: common_vendor.unref(formData)[item.key]
            })
          } : item.type === "cat_tree" ? {
            aT: "091ea86c-26-" + i0 + ",091ea86c-1",
            aU: common_vendor.o(($event) => common_vendor.unref(formData)[item.key] = $event, index),
            aV: common_vendor.p({
              ["label-width"]: common_vendor.unref(labelWidth),
              label: common_vendor.unref(get4Label)(item.label),
              disabled: componentDisabled(item),
              required: fieldRequired(item),
              pid: `${item.formSchema.pidValue}`,
              modelValue: common_vendor.unref(formData)[item.key]
            })
          } : item.type === "sel_tree" ? {
            aX: "091ea86c-27-" + i0 + ",091ea86c-1",
            aY: common_vendor.o(($event) => common_vendor.unref(formData)[item.key] = $event, index),
            aZ: common_vendor.p({
              ["label-width"]: common_vendor.unref(labelWidth),
              label: common_vendor.unref(get4Label)(item.label),
              disabled: componentDisabled(item),
              required: fieldRequired(item),
              dict: `${item.formSchema.dict}`,
              pidField: `${item.formSchema.pidField}`,
              pidValue: `${item.formSchema.pidValue}`,
              hasChildField: `${item.formSchema.hasChildField}`,
              modelValue: common_vendor.unref(formData)[item.key]
            })
          } : {
            ba: "091ea86c-28-" + i0 + ",091ea86c-1",
            bb: common_vendor.o(($event) => common_vendor.unref(formData)[item.key] = $event, index),
            bc: common_vendor.p({
              ["label-width"]: common_vendor.unref(labelWidth),
              disabled: componentDisabled(item),
              label: common_vendor.unref(get4Label)(item.label),
              name: item.key,
              placeholder: item.placeholder,
              rules: item.rules,
              clearable: true,
              modelValue: common_vendor.unref(formData)[item.key]
            })
          }, {
            h: item.type == "file",
            o: item.type === "datetime",
            s: item.type === "date",
            x: item.type === "time",
            B: item.type === "list" || item.type === "sel_search",
            F: item.type === "checkbox",
            J: item.type === "radio",
            N: item.type === "list_multi",
            R: item.type === "pca",
            V: item.type === "number" && (!item.onlyInteger || item.onlyInteger == false),
            Z: item.type === "number" && item.onlyInteger === true,
            ad: item.type == "switch",
            aj: ["textarea", "markdown", "umeditor"].includes(item.type),
            an: item.type === "password",
            ar: item.type === "popup_dict",
            aw: item.type === "popup",
            aA: item.type === "link_table",
            aG: item.type === "link_table_field",
            aK: item.type === "sel_user",
            aO: item.type === "sel_depart",
            aS: item.type === "cat_tree",
            aW: item.type === "sel_tree",
            bd: index,
            be: index % 2 == 0 ? 1 : ""
          });
        }),
        b: common_vendor.p({
          border: true
        }),
        c: common_vendor.sr("form", "091ea86c-0"),
        d: common_vendor.p({
          model: common_vendor.unref(formData)
        }),
        e: __props.showFooter
      }, __props.showFooter ? {
        f: common_vendor.o(formSubmit),
        g: common_vendor.p({
          disabled: common_vendor.unref(loading),
          block: true,
          loading: common_vendor.unref(loading)
        })
      } : {});
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-091ea86c"]]);
wx.createComponent(Component);
//# sourceMappingURL=online-loader.js.map
