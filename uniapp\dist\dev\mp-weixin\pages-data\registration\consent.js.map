{"version": 3, "file": "consent.js", "sources": ["../../../../../src/pages-data/registration/consent.vue", "../../../../../uniPage:/cGFnZXMtZGF0YVxyZWdpc3RyYXRpb25cY29uc2VudC52dWU"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n    <PageLayout>\r\n        <template #navbar>\r\n            <NavBar :title=\"mode === 'add' ? '新增' : '查看'\" :showBack=\"true\" />\r\n        </template>\r\n\r\n        <scroll-view class=\"page-scroll-view\" scroll-y=\"true\">\r\n            <view class=\"form-container\">\r\n                <view class=\"consent-content\">\r\n                    <view class=\"form-header\">\r\n                        <text class=\"form-title\">慢性心衰患者知情同意</text>\r\n                    </view>\r\n                    <view class=\"consent-text\">\r\n                        <view class=\"paragraph\">\r\n                            <text>尊敬的患者：</text>\r\n                        </view>\r\n                        <view class=\"paragraph\">\r\n                            <text\r\n                                style=\"text-indent: 2em; display: inline-block;\">我们邀请您参加院内慢性心力衰竭患者院外管理中应用研究\"课题研究。本研究将在武汉亚心总医院医院开展。本研究已经得到伦理委员会的审查和批准。本研究的目的是探索在现有的慢性心力衰竭随访计划中增加线上远程随访的模式和效果，探索线上远程随访能否改善慢性心衰患者的身体机能、心理状况、生活质量、自我管理行为以及死亡率和再住院率，以推动慢性心衰患者线上远程随访模式的建立和发展，推动慢性心衰患者院外健康管理的智能化发展，为慢性心衰患者的院外健康管理提供进一步的支持。</text>\r\n                        </view>\r\n                        <view class=\"center\">\r\n                            <text>让我们稍微了解下您吧～</text>\r\n                        </view>\r\n                        <view class=\"center\">\r\n                            <text>我们会完全保密的，请放心填写～</text>\r\n                        </view>\r\n                        <view class=\"divider\"></view>\r\n                        <view class=\"center\">\r\n                            <text style=\"font-weight: bold;\">尊敬的患者</text>\r\n                        </view>\r\n                        <view class=\"paragraph\">\r\n                            <text\r\n                                style=\"text-indent: 2em; display: inline-block;\">我们邀请您参加\"院内慢性心力衰竭患者院外管理中应用研究\"课题研究。本研究将在武汉亚心总医院医院开展，估计将有300名受试者自愿参加。本研究已经得到伦理委员会的审查和批准。</text>\r\n                        </view>\r\n                        <view class=\"paragraph\">\r\n                            <text style=\"display: inline-block; font-weight: bold;\">研究目的：</text>\r\n                        </view>\r\n                        <view class=\"paragraph\">\r\n                            <text\r\n                                style=\"text-indent: 2em; display: inline-block;\">本研究的目的是探索在现有的慢性心力衰竭随访计划中增加线上远程随访的模式和效果，探索线上远程随访能否改善慢性心衰患者的身体机能、心理状况、生活质量、自我管理行为以及死亡率和再住院率，以推动慢性心衰患者线上远程随访模式的建立和发展，推动慢性心衰患者院外健康管理的智能化发展，为慢性心衰患者的院外健康管理提供进一步的支持。</text>\r\n                        </view>\r\n                        <view class=\"paragraph\">\r\n                            <text style=\"display: inline-block; font-weight: bold;\">研究过程和方法：</text>\r\n                        </view>\r\n                        <view class=\"paragraph\">\r\n                            <text\r\n                                style=\"text-indent: 2em; display: inline-block;\">本研究计划于2025年3月至2025年9月招募在武汉亚心总医院医院因心力衰竭住院治疗的患者，本研究拟采用双盲随机对照试验，计划招募300名参与者，并对签署知情同意书的参与者使用分层区组随机分配的方法、按照1.2:1的比例分配到实验组和对照组。</text>\r\n                        </view>\r\n                        <view class=\"paragraph\">\r\n                            <text\r\n                                style=\"text-indent: 2em; display: inline-block;\">实验组采用基于智能APP平台戴设备的线上随访方式，对照组采用基于电话的常规随访方式。在研究中，医疗团队工作人员对于两组参与者在出院后的干预服务均持续6个月。在实验组中，医疗团队工作人员通过智能平台对患者的相关健康数据以及自我管理目标完成情况进行监测并向患者提供反馈。在对照组中，医疗团队工作人员在患者出院后1个月、3个月、6个月时对患者进行电话随访和健康宣教。所有的参与者需要在出院前进行基线测量（T0），在出院后1个月（T1）、3个月（T2）接受中测，并在完成为期6个月的随访后接受后测（T3）。</text>\r\n                        </view>\r\n                        <view class=\"paragraph\">\r\n                            <text style=\"display: inline-block; font-weight: bold; \">研究可能的受益：</text>\r\n                        </view>\r\n                        <view class=\"paragraph\">\r\n                            <text\r\n                                style=\"text-indent: 2em; display: inline-block;\">通过对您的标本进行检测将有助于对疾病作出诊断，为您的治疗提供必要的医疗建议，或为疾病的研究提供有益的信息。</text>\r\n                        </view>\r\n                        <view class=\"paragraph\">\r\n                            <text style=\"display: inline-block; font-weight: bold;\">研究风险与不适：</text>\r\n                        </view>\r\n                        <view class=\"paragraph\">\r\n                            <text\r\n                                style=\"text-indent: 2em; display: inline-block;\">参与研究可能需要您花费一定的时间和精力，可能会对你的日常生活造成一定的影响。</text>\r\n                        </view>\r\n                        <view class=\"paragraph\">\r\n                            <text style=\"display: inline-block; font-weight: bold;\">其他治疗干预方式：</text>\r\n                        </view>\r\n                        <view class=\"paragraph\">\r\n                            <text style=\"text-indent: 2em; display: inline-block;\">告知受试者除参加此研究外，是否还有其他的干预或治疗措施。</text>\r\n                        </view>\r\n                        <view class=\"paragraph\">\r\n                            <text style=\"display: inline-block; font-weight: bold;\">隐私问题：</text>\r\n                        </view>\r\n                        <view class=\"paragraph\">\r\n                            <text\r\n                                style=\"text-indent: 2em; display: inline-block;\">如果您决定参加本项研究，您参加试验及在试验中的个人资料均属保密。对于您来说，所有的信息将是保密的。例如：您的血/尿标本将以研究编号数字而非您的姓名加以标识。可以识别您身份的信息将不会透露给研究小组以外的成员，除非获得您的许可。如果标识符必须保留，说明为什么。说明何时销毁（书写的或用其他方式记录的）研究资料。如果研究结束时，资料没有销毁，介绍资料保存在何处和保存多久。说明在未来将如何使用保存的资料，以及如何获得受试者允许在未来使用他们的资料。所有的研究成员和研究申办方都被要求对您的身份保密。您的档案仅供研究人员查阅。为确保研究按照规定进行，必要时，政府管理部门或伦理审查委员会的成员按规定可以在研究单位查阅您的个人资料。</text>\r\n                        </view>\r\n                        <view class=\"paragraph\">\r\n                            <text style=\"text-indent: 2em; display: inline-block;\">这项研究结果发表时，也需要对保密方面进行承诺。</text>\r\n                        </view>\r\n                        <view class=\"paragraph\">\r\n                            <text style=\"display: inline-block; font-weight: bold;\">费用和补偿：</text>\r\n                        </view>\r\n                        <view class=\"paragraph\">\r\n                            <text\r\n                                style=\"text-indent: 2em; display: inline-block;\">如果您因参与这项研究而受到伤害：如发生与该项临床研究相关的损害时，您可以获得免费治疗和／或相应的补偿。治疗费用由武汉亚心总医院提供。</text>\r\n                        </view>\r\n                        <view class=\"paragraph\">\r\n                            <text style=\"display: inline-block; font-weight: bold;\">自愿参加和自由退出：</text>\r\n                        </view>\r\n                        <view class=\"paragraph\">\r\n                            <text\r\n                                style=\"text-indent: 2em; display: inline-block;\">作为受试者，您可随时了解与本研究有关的信息资料和研究进展，自愿决定（继续）参加还是不（继续）参加。参加后，无论是否发生伤害，或是否严重，您可以选择在任何时候通知研究者要求退出研究，您的数据将不纳入研究结果，您的任何医疗待遇与权益不会因此而受到影响。如果继续参加研究，会对您造成严重的伤害，研究者也将会中止研究的进行。</text>\r\n                        </view>\r\n                        <view class=\"paragraph\">\r\n                            <text\r\n                                style=\"text-indent: 2em; display: inline-block;\">但在参加研究期间，请您提供有关自身病史和当前身体状况的真实情况；告诉研究医生自己在本次研究期间所出现的任何不适；不得服用受限制的药物、食物等；告诉研究医生自己在最近是否曾参与其他研究，或目前正参与其他研究。如果因为您没有遵守研究计划，或者发生了与研究相关的损伤或者有任何其它原因，研究医师可以终止您继续参与本项研究。</text>\r\n                        </view>\r\n                        <view class=\"paragraph\">\r\n                            <text style=\"display: inline-block; font-weight: bold;\">联系方式：\r\n                            </text>\r\n                        </view>\r\n                        <view class=\"paragraph\">\r\n                            <text\r\n                                style=\"text-indent: 2em; display: inline-block;\">如果您有与本研究有关的问题，或您在研究过程中发生了任何不适与损伤，或有关于本项研究参加者权益方面的问题，您可以与陈红联系，电话84788999.</text>\r\n                        </view>\r\n                        <view class=\"paragraph\">\r\n                            <text style=\"display: inline-block; font-weight: bold;\">知情同意签字：</text>\r\n                        </view>\r\n                        <view class=\"paragraph\">\r\n                            <text style=\"text-indent: 2em; display: inline-block;\">\r\n                                我已经阅读了本知情同意书，并且我的医生\r\n                                陈静（签字）已经将此次临床试验的目的、内容、风险和受益情况向我作了详细的解释说明，对我询问的所有问题也给予了解答，我对此项临床研究已经了解，我自愿参加本项研究。\r\n                            </text>\r\n                        </view>\r\n                    </view>\r\n                </view>\r\n\r\n                <view class=\"signature-section\">\r\n                    <button class=\"signature-btn\" @click=\"openSignature\">打开签名版</button>\r\n                    <view class=\"signature-preview\" v-if=\"signatureData\">\r\n                        <image class=\"signature-thumbnail\" :src=\"signatureData\" mode=\"aspectFit\"></image>\r\n                    </view>\r\n                    <view class=\"error-tip\" v-if=\"showError && !signatureData\">\r\n                        <text class=\"error-text\">请先完成签名</text>\r\n                    </view>\r\n                </view>\r\n\r\n                <view class=\"submit-section\" v-if=\"!isViewMode\">\r\n                    <button class=\"submit-btn\" @click=\"submitForm\">提交</button>\r\n                </view>\r\n            </view>\r\n        </scroll-view>\r\n\r\n        <!-- 签名覆盖层 -->\r\n        <view class=\"signature-overlay\" v-if=\"showSignature\">\r\n            <view class=\"signature-container landscape-signature\">\r\n                <view class=\"signature-wrapper\">\r\n                    <wd-signature ref=\"signatureRef\" v-if=\"inited\" :height=\"height\" :width=\"width\" enable-history\r\n                        pressure :min-width=\"1\" :max-width=\"5\" :min-speed=\"1.5\" :export-scale=\"3\" file-type=\"png\"\r\n                        background-color=\"#f5f5f5\" @confirm=\"handleConfirm\" @start=\"updateCanUndoRedo\"\r\n                        @end=\"updateCanUndoRedo\">\r\n                        <template #footer>\r\n                            <view class=\"signature-actions\">\r\n                                <view class=\"button-group\">\r\n                                    <wd-button size=\"small\" plain @click=\"clear\"\r\n                                        custom-style=\"min-width: 30rpx; height: 25rpx; line-height: 68rpx; font-size: 16rpx;\">清除</wd-button>\r\n                                    <wd-button size=\"small\" type=\"primary\" @click=\"confirm\"\r\n                                        custom-style=\"min-width: 30rpx; height: 25rpx; line-height: 68rpx; font-size: 16rpx;\">完成</wd-button>\r\n                                    <wd-button size=\"small\" plain @click=\"cancelSignature\"\r\n                                        custom-style=\"min-width: 30rpx; height: 25rpx; line-height: 68rpx; font-size: 16rpx;\">取消</wd-button>\r\n                                </view>\r\n                            </view>\r\n                        </template>\r\n                    </wd-signature>\r\n                </view>\r\n            </view>\r\n        </view>\r\n    </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { ref, computed, onMounted, watch, nextTick } from 'vue'\r\nimport { fastLerp } from 'zrender/lib/tool/color'\r\nimport { useUserStore } from '@/store/user'\r\nimport { pause } from 'wot-design-uni/components/common/util'\r\n\r\nconst height = ref(0)\r\nconst width = ref(0)\r\nconst inited = ref(false)\r\nconst showSignature = ref(false)\r\nconst showLandscapeGuide = ref(true)\r\nconst signatureData = ref('')\r\nconst showError = ref(false)\r\nconst canUndo = ref(false)\r\nconst canRedo = ref(false)\r\nconst signatureRef = ref(null)\r\n\r\nconst userStore = useUserStore()\r\n\r\ndefineOptions({\r\n    name: 'vitalSignsForm',\r\n})\r\n// 获取页面参数\r\nconst query = ref<any>({})\r\nonMounted(() => {\r\n    const pages = getCurrentPages()\r\n    const currentPage = pages[pages.length - 1]\r\n    query.value = (currentPage as any).options || {}\r\n\r\n    // 如果是查看模式，加载数据\r\n    if (query.value.mode === 'view' && query.value.id) {\r\n        loadFormData(query.value.id)\r\n    }\r\n\r\n    // 预设置签名尺寸，但不初始化，等打开签名弹窗时再初始化\r\n    const { windowWidth } = uni.getSystemInfoSync()\r\n    width.value = Math.min(windowWidth * 0.85, 600)\r\n    height.value = width.value * 0.6\r\n})\r\n\r\n// 表单模式：add 或 view\r\nconst mode = computed(() => query.value.mode || 'add')\r\nconst isViewMode = computed(() => mode.value === 'view')\r\n\r\n// 表单数据\r\nconst formData = ref({\r\n    date: '',\r\n    signature: ''\r\n})\r\n\r\n// 加载表单数据（根据ID获取数据）\r\nconst loadFormData = (id: string) => {\r\n    // 这里应该是从API获取数据的逻辑\r\n}\r\n\r\n// 打开签名版\r\nconst openSignature = () => {\r\n    showSignature.value = true\r\n    showLandscapeGuide.value = true // 初始显示横屏指引\r\n\r\n    // 获取窗口尺寸\r\n    const { windowWidth, windowHeight } = uni.getSystemInfoSync()\r\n\r\n    // 判断当前是否已经是横屏\r\n    const isLandscape = windowWidth > windowHeight\r\n\r\n    if (isLandscape) {\r\n        // 已经是横屏，直接调整尺寸\r\n        width.value = windowWidth * 0.85\r\n        height.value = windowHeight * 0.7\r\n        showLandscapeGuide.value = false // 已经是横屏，不显示指引\r\n    } else {\r\n        // 竖屏状态，需要提示用户旋转\r\n        width.value = windowHeight * 0.85 // 预设横屏尺寸\r\n        height.value = windowWidth * 0.7\r\n    }\r\n\r\n\r\n    // 微信小程序支持横屏API\r\n    try {\r\n        // @ts-ignore\r\n        wx.setPageOrientation({\r\n            orientation: 'landscape',\r\n            success: () => {\r\n                console.log('已切换到横屏模式')\r\n                showLandscapeGuide.value = false // 切换成功后隐藏指引\r\n\r\n                // 重新获取窗口尺寸并调整画布\r\n                setTimeout(() => {\r\n                    const { windowWidth, windowHeight } = uni.getSystemInfoSync()\r\n                    width.value = windowWidth * 0.85\r\n                    height.value = windowHeight * 0.7\r\n                }, 300)\r\n            },\r\n            fail: (err) => {\r\n                console.error('切换横屏模式失败:', err)\r\n            }\r\n        })\r\n    } catch (error) {\r\n        console.error('设置横屏失败:', error)\r\n    }\r\n\r\n\r\n    // 监听设备方向/窗口尺寸变化\r\n    uni.onWindowResize(handleSizeChange)\r\n\r\n    // 延迟初始化以确保DOM已渲染\r\n    pause(300).then(() => {\r\n        inited.value = true\r\n    })\r\n}\r\n\r\n// 处理窗口尺寸变化\r\nconst handleSizeChange = () => {\r\n    // 当窗口尺寸变化时检测横屏状态\r\n    const { windowWidth, windowHeight } = uni.getSystemInfoSync()\r\n    const isLandscape = windowWidth > windowHeight\r\n\r\n    if (isLandscape && showSignature.value) {\r\n        showLandscapeGuide.value = false // 横屏时隐藏指引\r\n\r\n        // 调整尺寸\r\n        width.value = windowWidth * 0.85\r\n        height.value = windowHeight * 0.7\r\n    } else if (showSignature.value) {\r\n        showLandscapeGuide.value = true // 竖屏时显示指引\r\n    }\r\n}\r\n\r\n// 取消签名\r\nconst cancelSignature = () => {\r\n    showSignature.value = false\r\n\r\n    // 移除窗口尺寸变化监听\r\n    uni.offWindowResize(handleSizeChange)\r\n\r\n\r\n    // 恢复为竖屏模式\r\n    try {\r\n        // @ts-ignore\r\n        wx.setPageOrientation({\r\n            orientation: 'portrait',\r\n            success: () => {\r\n                console.log('已恢复竖屏模式')\r\n            }\r\n        })\r\n    } catch (error) {\r\n        console.error('设置竖屏失败:', error)\r\n    }\r\n\r\n}\r\n\r\n// 处理签名确认\r\nconst handleConfirm = (result) => {\r\n    if (result && result.success && result.tempFilePath) {\r\n        signatureData.value = result.tempFilePath\r\n        formData.value.signature = result.tempFilePath\r\n        showError.value = false\r\n\r\n        // 显示签名结果预览\r\n        uni.showToast({\r\n            title: '签名已保存',\r\n            icon: 'success',\r\n            duration: 2000\r\n        })\r\n    }\r\n    showSignature.value = false\r\n\r\n    // 移除窗口尺寸变化监听\r\n    uni.offWindowResize(handleSizeChange)\r\n\r\n\r\n    // 恢复为竖屏模式\r\n    try {\r\n        // @ts-ignore\r\n        wx.setPageOrientation({\r\n            orientation: 'portrait',\r\n            success: () => {\r\n                console.log('已恢复竖屏模式')\r\n            }\r\n        })\r\n    } catch (error) {\r\n        console.error('设置竖屏失败:', error)\r\n    }\r\n\r\n}\r\n\r\nconst clear = () => {\r\n    if (signatureRef.value) {\r\n        signatureRef.value.clear && signatureRef.value.clear()\r\n        canUndo.value = false\r\n        canRedo.value = false\r\n    }\r\n}\r\n\r\nconst confirm = () => {\r\n    if (signatureRef.value) {\r\n        signatureRef.value.confirm && signatureRef.value.confirm()\r\n\r\n        // 恢复为竖屏模式将由handleConfirm处理\r\n    }\r\n}\r\n\r\n// 更新撤销/恢复按钮状态\r\nconst updateCanUndoRedo = () => {\r\n    if (signatureRef.value) {\r\n        nextTick(() => {\r\n            if (typeof signatureRef.value.canUndo === 'function') {\r\n                canUndo.value = signatureRef.value.canUndo()\r\n            }\r\n            if (typeof signatureRef.value.canRedo === 'function') {\r\n                canRedo.value = signatureRef.value.canRedo()\r\n            }\r\n        })\r\n    }\r\n}\r\n\r\n// 提交表单\r\nconst submitForm = () => {\r\n    if (!signatureData.value) {\r\n        showError.value = true\r\n        uni.showToast({\r\n            title: '请先完成签名',\r\n            icon: 'none',\r\n            duration: 2000\r\n        })\r\n        return\r\n    }\r\n\r\n    // 显示加载中\r\n    uni.showLoading({\r\n        title: '提交中...'\r\n    })\r\n\r\n    // 准备提交的数据\r\n    const submitData = {\r\n        userId: userStore.userInfo.userid\r\n    }\r\n\r\n    // 打印将要提交的数据\r\n    console.log('提交到/patient/savesignature的数据:', {\r\n        signature: signatureData.value, // 签名图片临时文件路径\r\n        formData: submitData // 表单数据，包含userId\r\n    })\r\n\r\n    // 提交数据到后端\r\n    uni.uploadFile({\r\n        url: `${import.meta.env.VITE_SERVER_BASEURL}/patient/savesignature`, // 后端签名接口\r\n        filePath: signatureData.value, // 签名图片路径\r\n        name: 'file',\r\n        formData: submitData,\r\n        success: (res) => {\r\n            // 解析返回的JSON数据\r\n            let response\r\n            try {\r\n                response = JSON.parse(res.data)\r\n            } catch (e) {\r\n                response = res.data\r\n            }\r\n\r\n            // 判断是否成功\r\n            if (res.statusCode === 200 && response.success) {\r\n                uni.hideLoading()\r\n                uni.showToast({\r\n                    title: '提交成功',\r\n                    icon: 'success',\r\n                    duration: 2000\r\n                })                // 提交成功后跳转\r\n                setTimeout(() => {\r\n                    uni.navigateTo({\r\n                        url: '/pages-data/registration/form'\r\n                    })\r\n                }, 1000)\r\n            } else {\r\n                // 提交失败\r\n                uni.hideLoading()\r\n                uni.showToast({\r\n                    title: response.message || '提交失败，请重试',\r\n                    icon: 'none',\r\n                    duration: 2000\r\n                })\r\n            }\r\n        },\r\n        fail: (err) => {\r\n            console.error('提交失败:', err)\r\n            uni.hideLoading()\r\n            uni.showToast({\r\n                title: '网络错误，请稍后重试',\r\n                icon: 'none',\r\n                duration: 2000\r\n            })\r\n        }\r\n    })\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-scroll-view {\r\n    height: calc(100vh - 44px);\r\n    /* 减去导航栏高度 */\r\n    width: 100%;\r\n}\r\n\r\n.form-container {\r\n    padding: 20rpx;\r\n    padding-bottom: 120rpx;\r\n    /* 增加底部内边距，防止内容被遮挡 */\r\n\r\n    .form-header {\r\n        margin-bottom: 20rpx;\r\n        text-align: center;\r\n        /* 标题居中 */\r\n\r\n        .form-title {\r\n            font-size: 32rpx;\r\n            font-weight: bold;\r\n            color: #333;\r\n        }\r\n    }\r\n\r\n    .form-section {\r\n        background-color: #FFFFFF;\r\n        border-radius: 12rpx;\r\n        padding: 20rpx;\r\n        margin-bottom: 20rpx;\r\n\r\n        .form-item {\r\n            margin-bottom: 30rpx;\r\n\r\n            .form-label {\r\n                display: block;\r\n                font-size: 28rpx;\r\n                color: #333;\r\n                margin-bottom: 20rpx;\r\n            }\r\n\r\n            .inline-form-item {\r\n                display: flex;\r\n                align-items: center;\r\n                margin-bottom: 20rpx;\r\n\r\n                .inline-form-label {\r\n                    font-size: 28rpx;\r\n                    color: #333;\r\n                    min-width: 160rpx;\r\n                }\r\n\r\n                .inline-form-input {\r\n                    flex: 1;\r\n                    background-color: #F7F7F7;\r\n                    border-radius: 8rpx;\r\n                    padding: 20rpx;\r\n                    font-size: 28rpx;\r\n                    color: #333;\r\n                    box-sizing: border-box;\r\n                    min-height: 80rpx;\r\n\r\n                    &:disabled {\r\n                        background-color: #F5F5F5;\r\n                        color: #666;\r\n                    }\r\n                }\r\n            }\r\n\r\n            .form-input,\r\n            .form-textarea {\r\n                width: 100%;\r\n                background-color: #F7F7F7;\r\n                border-radius: 8rpx;\r\n                padding: 20rpx;\r\n                font-size: 28rpx;\r\n                color: #333;\r\n                box-sizing: border-box;\r\n                min-height: 80rpx;\r\n\r\n                &:disabled {\r\n                    background-color: #F5F5F5;\r\n                    color: #666;\r\n                }\r\n            }\r\n\r\n            .form-textarea {\r\n                height: 180rpx;\r\n            }\r\n\r\n            .radio-group {\r\n                display: flex;\r\n                flex-direction: column;\r\n                gap: 30rpx;\r\n                /* 增加间距 */\r\n\r\n                .radio-row {\r\n                    display: flex;\r\n                    gap: 150rpx;\r\n                    /* 增加间距 */\r\n                    margin-left: 20rpx;\r\n                    /* 增加左边距，实现对齐 */\r\n                    justify-content: flex-start;\r\n                    /* 添加左对齐 */\r\n                }\r\n\r\n                .radio-item {\r\n                    display: flex;\r\n                    align-items: center;\r\n                    min-width: 180rpx;\r\n                    width: 240rpx;\r\n                    /* 确保宽度一致，增大宽度值以容纳更长的文本 */\r\n\r\n                    .radio-btn {\r\n                        width: 36rpx;\r\n                        height: 36rpx;\r\n                        border-radius: 50%;\r\n                        border: 2rpx solid #CCCCCC;\r\n                        margin-right: 10rpx;\r\n                        position: relative;\r\n\r\n                        &.checked {\r\n                            border-color: #07C160;\r\n\r\n                            &:after {\r\n                                content: '';\r\n                                position: absolute;\r\n                                width: 24rpx;\r\n                                height: 24rpx;\r\n                                background-color: #07C160;\r\n                                border-radius: 50%;\r\n                                top: 50%;\r\n                                left: 50%;\r\n                                transform: translate(-50%, -50%);\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    text {\r\n                        font-size: 28rpx;\r\n                        color: #333;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    // 行内选择器样式\r\n    .inline-picker-wrapper {\r\n        flex: 1;\r\n        width: 100%;\r\n    }\r\n\r\n    .required {\r\n        color: #FF0000;\r\n        margin-right: 4rpx;\r\n    }\r\n\r\n    .submit-section {\r\n        margin-top: 40rpx;\r\n        margin-bottom: 60rpx;\r\n        /* 增加底部间距 */\r\n\r\n        .submit-btn {\r\n            width: 100%;\r\n            background-color: #07C160;\r\n            color: #FFFFFF;\r\n            border-radius: 8rpx;\r\n            font-size: 32rpx;\r\n            padding: 20rpx 0;\r\n        }\r\n    }\r\n}\r\n\r\n.checkbox-group {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 30rpx;\r\n\r\n    .checkbox-row {\r\n        display: flex;\r\n        gap: 150rpx;\r\n        margin-left: 20rpx;\r\n        justify-content: flex-start;\r\n    }\r\n\r\n    .checkbox-item {\r\n        display: flex;\r\n        align-items: center;\r\n        min-width: 180rpx;\r\n        width: 240rpx;\r\n\r\n        .checkbox-btn {\r\n            width: 36rpx;\r\n            height: 36rpx;\r\n            border-radius: 4rpx;\r\n            border: 2rpx solid #CCCCCC;\r\n            margin-right: 10rpx;\r\n            position: relative;\r\n\r\n            &.checked {\r\n                border-color: #07C160;\r\n                background-color: #07C160;\r\n\r\n                &:after {\r\n                    content: '';\r\n                    position: absolute;\r\n                    width: 20rpx;\r\n                    height: 10rpx;\r\n                    border-left: 4rpx solid #FFFFFF;\r\n                    border-bottom: 4rpx solid #FFFFFF;\r\n                    top: 45%;\r\n                    left: 50%;\r\n                    transform: translate(-50%, -50%) rotate(-45deg);\r\n                }\r\n            }\r\n        }\r\n\r\n        text {\r\n            font-size: 28rpx;\r\n            color: #333;\r\n        }\r\n    }\r\n\r\n    .sub-form-item {\r\n        margin-left: 50rpx;\r\n        margin-top: -10rpx;\r\n        margin-bottom: 20rpx;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .sub-form-label {\r\n            font-size: 26rpx;\r\n            color: #666;\r\n            min-width: 180rpx;\r\n        }\r\n\r\n        .sub-form-input {\r\n            flex: 1;\r\n            background-color: #F7F7F7;\r\n            border-radius: 8rpx;\r\n            padding: 15rpx;\r\n            font-size: 26rpx;\r\n            color: #333;\r\n            min-height: 70rpx;\r\n            width: 100%;\r\n            box-sizing: border-box;\r\n        }\r\n\r\n        .picker {\r\n            flex: 1;\r\n            display: flex;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n            background-color: #F7F7F7;\r\n            border-radius: 8rpx;\r\n            padding: 15rpx;\r\n            font-size: 26rpx;\r\n            color: #333;\r\n            min-height: 70rpx;\r\n            width: 100%;\r\n            box-sizing: border-box;\r\n        }\r\n    }\r\n\r\n    /* 添加新的样式以支持两列布局 */\r\n    .sub-form-items-row {\r\n        display: flex;\r\n        margin-left: 50rpx;\r\n        gap: 20rpx;\r\n        margin-top: -10rpx;\r\n        margin-bottom: 20rpx;\r\n\r\n        .sub-form-item {\r\n            flex: 1;\r\n            width: 50%;\r\n            min-width: calc(50% - 10rpx);\r\n            /* 确保即使只有一个元素也占据一半宽度 */\r\n            margin: 0;\r\n            display: flex;\r\n            align-items: center;\r\n\r\n            .picker-container {\r\n                width: 100%;\r\n                flex: 1;\r\n                display: block;\r\n            }\r\n\r\n            .temp-picker {\r\n                width: 100%;\r\n                flex: 1;\r\n                display: block;\r\n            }\r\n\r\n            .sub-form-input {\r\n                flex: 1;\r\n                background-color: #F7F7F7;\r\n                border-radius: 8rpx;\r\n                padding: 15rpx;\r\n                font-size: 26rpx;\r\n                color: #333;\r\n                min-height: 70rpx;\r\n                width: 100%;\r\n                box-sizing: border-box;\r\n            }\r\n\r\n            .picker {\r\n                flex: 1;\r\n                display: flex;\r\n                justify-content: space-between;\r\n                align-items: center;\r\n                background-color: #F7F7F7;\r\n                border-radius: 8rpx;\r\n                padding: 15rpx;\r\n                font-size: 26rpx;\r\n                color: #333;\r\n                min-height: 70rpx;\r\n                width: 100%;\r\n                box-sizing: border-box;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.consent-container {\r\n    padding: 40rpx;\r\n    min-height: 100vh;\r\n    background-color: #fff;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n\r\n    .consent-content {\r\n        .consent-title {\r\n            font-size: 36rpx;\r\n            font-weight: bold;\r\n            text-align: center;\r\n            margin-bottom: 40rpx;\r\n            color: #333;\r\n        }\r\n\r\n        .consent-text {\r\n            text {\r\n                display: block;\r\n                line-height: 1.6;\r\n                color: #666;\r\n                font-size: 28rpx;\r\n            }\r\n\r\n            .paragraph {\r\n                margin-bottom: 30rpx;\r\n            }\r\n\r\n            .center-paragraph {\r\n                text-align: center;\r\n            }\r\n\r\n            .indent {\r\n                text-indent: 2em;\r\n            }\r\n\r\n            .center {\r\n                text-align: center;\r\n            }\r\n\r\n            .spacer {\r\n                height: 30rpx;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.divider {\r\n    height: 1px;\r\n    background-color: #666;\r\n    /* 浅灰色分割线 */\r\n    margin: 12px 0;\r\n    /* 上下间距 */\r\n    width: 100%;\r\n    /* 分割线宽度 */\r\n}\r\n\r\n.signature-section {\r\n    margin: 40rpx 0;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n\r\n    .signature-btn {\r\n        background-color: #07C160;\r\n        color: #FFFFFF;\r\n        border-radius: 8rpx;\r\n        font-size: 32rpx;\r\n        padding: 20rpx 40rpx;\r\n        width: 240rpx;\r\n        text-align: center;\r\n        box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.3);\r\n    }\r\n\r\n    .signature-preview {\r\n        margin-top: 20rpx;\r\n        width: 90%;\r\n        height: 200rpx;\r\n        background-color: #f5f5f5;\r\n        border-radius: 8rpx;\r\n        padding: 10rpx;\r\n        box-sizing: border-box;\r\n        border: 1px solid #e0e0e0;\r\n\r\n        .signature-thumbnail {\r\n            width: 100%;\r\n            height: 100%;\r\n            object-fit: contain;\r\n        }\r\n    }\r\n\r\n    .error-tip {\r\n        margin-top: 10rpx;\r\n\r\n        .error-text {\r\n            color: #ff4d4f;\r\n            font-size: 28rpx;\r\n        }\r\n    }\r\n}\r\n\r\n.signature-overlay {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100vw;\r\n    height: 100vh;\r\n    background-color: rgba(0, 0, 0, 0.5);\r\n    z-index: 998;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n}\r\n\r\n.signature-container {\r\n    width: 90%;\r\n    max-width: 600px;\r\n    padding: 30rpx 20rpx;\r\n    background-color: #ffffff;\r\n    border-radius: 16rpx;\r\n    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);\r\n    overflow: hidden;\r\n    z-index: 999;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n\r\n    &.landscape-signature {\r\n        width: 95vw;\r\n        max-width: 95vw;\r\n        height: 85vh;\r\n        padding: 20rpx;\r\n        transform: rotate(90deg);\r\n        transform-origin: center;\r\n        padding-bottom: 40rpx;\r\n\r\n        .signature-wrapper {\r\n            width: 100%;\r\n            height: calc(100% - 120rpx);\r\n            display: flex;\r\n            flex-direction: column;\r\n            justify-content: flex-start;\r\n            align-items: center;\r\n            position: relative;\r\n\r\n            :deep(.wd-signature__footer) {\r\n                position: relative;\r\n                z-index: 10;\r\n                margin-top: 20rpx;\r\n            }\r\n        }\r\n\r\n        @media screen and (min-width: 500px) {\r\n            transform: none;\r\n            margin-top: 0;\r\n        }\r\n    }\r\n\r\n    .rotate-hint {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        margin-bottom: 20rpx;\r\n\r\n        text {\r\n            font-size: 28rpx;\r\n            color: #333;\r\n            font-weight: bold;\r\n        }\r\n    }\r\n\r\n    .signature-title {\r\n        font-size: 32rpx;\r\n        font-weight: bold;\r\n        color: #333;\r\n        margin-bottom: 20rpx;\r\n        text-align: center;\r\n    }\r\n\r\n    .signature-actions {\r\n        margin-top: -15rpx;\r\n        width: 100%;\r\n        display: flex;\r\n        justify-content: center;\r\n        padding-bottom: 10rpx;\r\n\r\n        .button-group {\r\n            display: flex;\r\n            flex-direction: row;\r\n            gap: 16rpx;\r\n            flex-wrap: nowrap;\r\n            justify-content: center;\r\n        }\r\n    }\r\n}\r\n\r\n@keyframes rotate {\r\n    0% {\r\n        transform: rotate(0deg);\r\n    }\r\n\r\n    25% {\r\n        transform: rotate(90deg);\r\n    }\r\n\r\n    50% {\r\n        transform: rotate(90deg);\r\n    }\r\n\r\n    75% {\r\n        transform: rotate(0deg);\r\n    }\r\n\r\n    100% {\r\n        transform: rotate(0deg);\r\n    }\r\n}\r\n\r\n@keyframes fadeIn {\r\n    from {\r\n        opacity: 0;\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n    }\r\n}\r\n</style>", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-data/registration/consent.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "useUserStore", "onMounted", "uni", "computed", "wx", "windowWidth", "windowHeight", "pause", "nextTick"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6KM,UAAA,SAASA,kBAAI,CAAC;AACd,UAAA,QAAQA,kBAAI,CAAC;AACb,UAAA,SAASA,kBAAI,KAAK;AAClB,UAAA,gBAAgBA,kBAAI,KAAK;AACzB,UAAA,qBAAqBA,kBAAI,IAAI;AAC7B,UAAA,gBAAgBA,kBAAI,EAAE;AACtB,UAAA,YAAYA,kBAAI,KAAK;AACrB,UAAA,UAAUA,kBAAI,KAAK;AACnB,UAAA,UAAUA,kBAAI,KAAK;AACnB,UAAA,eAAeA,kBAAI,IAAI;AAE7B,UAAM,YAAYC,WAAAA,aAAa;AAMzB,UAAA,QAAQD,cAAS,IAAA,EAAE;AACzBE,kBAAAA,UAAU,MAAM;AACZ,YAAM,QAAQ,gBAAgB;AAC9B,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AACpC,YAAA,QAAS,YAAoB,WAAW,CAAC;AAG/C,UAAI,MAAM,MAAM,SAAS,UAAU,MAAM,MAAM,IAAI;AAClC,qBAAA,MAAM,MAAM,EAAE;AAAA,MAAA;AAI/B,YAAM,EAAE,YAAA,IAAgBC,cAAA,MAAI,kBAAkB;AAC9C,YAAM,QAAQ,KAAK,IAAI,cAAc,MAAM,GAAG;AACvC,aAAA,QAAQ,MAAM,QAAQ;AAAA,IAAA,CAChC;AAGD,UAAM,OAAOC,cAAAA,SAAS,MAAM,MAAM,MAAM,QAAQ,KAAK;AACrD,UAAM,aAAaA,cAAA,SAAS,MAAM,KAAK,UAAU,MAAM;AAGvD,UAAM,WAAWJ,cAAAA,IAAI;AAAA,MACjB,MAAM;AAAA,MACN,WAAW;AAAA,IAAA,CACd;AAGK,UAAA,eAAe,CAAC,OAAe;AAAA,IAErC;AAGA,UAAM,gBAAgB,MAAM;AACxB,oBAAc,QAAQ;AACtB,yBAAmB,QAAQ;AAG3B,YAAM,EAAE,aAAa,iBAAiBG,cAAAA,MAAI,kBAAkB;AAG5D,YAAM,cAAc,cAAc;AAElC,UAAI,aAAa;AAEb,cAAM,QAAQ,cAAc;AAC5B,eAAO,QAAQ,eAAe;AAC9B,2BAAmB,QAAQ;AAAA,MAAA,OACxB;AAEH,cAAM,QAAQ,eAAe;AAC7B,eAAO,QAAQ,cAAc;AAAA,MAAA;AAK7B,UAAA;AAEAE,sBAAAA,KAAG,mBAAmB;AAAA,UAClB,aAAa;AAAA,UACb,SAAS,MAAM;AACX,oBAAQ,IAAI,UAAU;AACtB,+BAAmB,QAAQ;AAG3B,uBAAW,MAAM;AACb,oBAAM,EAAE,aAAAC,cAAa,cAAAC,cAAa,IAAIJ,oBAAI,kBAAkB;AAC5D,oBAAM,QAAQG,eAAc;AAC5B,qBAAO,QAAQC,gBAAe;AAAA,eAC/B,GAAG;AAAA,UACV;AAAA,UACA,MAAM,CAAC,QAAQ;AACH,oBAAA,MAAM,aAAa,GAAG;AAAA,UAAA;AAAA,QAClC,CACH;AAAA,eACI,OAAO;AACJ,gBAAA,MAAM,WAAW,KAAK;AAAA,MAAA;AAKlCJ,oBAAA,MAAI,eAAe,gBAAgB;AAG7BK,0BAAA,GAAG,EAAE,KAAK,MAAM;AAClB,eAAO,QAAQ;AAAA,MAAA,CAClB;AAAA,IACL;AAGA,UAAM,mBAAmB,MAAM;AAE3B,YAAM,EAAE,aAAa,iBAAiBL,cAAAA,MAAI,kBAAkB;AAC5D,YAAM,cAAc,cAAc;AAE9B,UAAA,eAAe,cAAc,OAAO;AACpC,2BAAmB,QAAQ;AAG3B,cAAM,QAAQ,cAAc;AAC5B,eAAO,QAAQ,eAAe;AAAA,MAAA,WACvB,cAAc,OAAO;AAC5B,2BAAmB,QAAQ;AAAA,MAAA;AAAA,IAEnC;AAGA,UAAM,kBAAkB,MAAM;AAC1B,oBAAc,QAAQ;AAGtBA,oBAAA,MAAI,gBAAgB,gBAAgB;AAIhC,UAAA;AAEAE,sBAAAA,KAAG,mBAAmB;AAAA,UAClB,aAAa;AAAA,UACb,SAAS,MAAM;AACX,oBAAQ,IAAI,SAAS;AAAA,UAAA;AAAA,QACzB,CACH;AAAA,eACI,OAAO;AACJ,gBAAA,MAAM,WAAW,KAAK;AAAA,MAAA;AAAA,IAGtC;AAGM,UAAA,gBAAgB,CAAC,WAAW;AAC9B,UAAI,UAAU,OAAO,WAAW,OAAO,cAAc;AACjD,sBAAc,QAAQ,OAAO;AACpB,iBAAA,MAAM,YAAY,OAAO;AAClC,kBAAU,QAAQ;AAGlBF,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAAA,CACb;AAAA,MAAA;AAEL,oBAAc,QAAQ;AAGtBA,oBAAA,MAAI,gBAAgB,gBAAgB;AAIhC,UAAA;AAEAE,sBAAAA,KAAG,mBAAmB;AAAA,UAClB,aAAa;AAAA,UACb,SAAS,MAAM;AACX,oBAAQ,IAAI,SAAS;AAAA,UAAA;AAAA,QACzB,CACH;AAAA,eACI,OAAO;AACJ,gBAAA,MAAM,WAAW,KAAK;AAAA,MAAA;AAAA,IAGtC;AAEA,UAAM,QAAQ,MAAM;AAChB,UAAI,aAAa,OAAO;AACpB,qBAAa,MAAM,SAAS,aAAa,MAAM,MAAM;AACrD,gBAAQ,QAAQ;AAChB,gBAAQ,QAAQ;AAAA,MAAA;AAAA,IAExB;AAEA,UAAM,UAAU,MAAM;AAClB,UAAI,aAAa,OAAO;AACpB,qBAAa,MAAM,WAAW,aAAa,MAAM,QAAQ;AAAA,MAAA;AAAA,IAIjE;AAGA,UAAM,oBAAoB,MAAM;AAC5B,UAAI,aAAa,OAAO;AACpBI,sBAAAA,WAAS,MAAM;AACX,cAAI,OAAO,aAAa,MAAM,YAAY,YAAY;AAC1C,oBAAA,QAAQ,aAAa,MAAM,QAAQ;AAAA,UAAA;AAE/C,cAAI,OAAO,aAAa,MAAM,YAAY,YAAY;AAC1C,oBAAA,QAAQ,aAAa,MAAM,QAAQ;AAAA,UAAA;AAAA,QAC/C,CACH;AAAA,MAAA;AAAA,IAET;AAGA,UAAM,aAAa,MAAM;AACjB,UAAA,CAAC,cAAc,OAAO;AACtB,kBAAU,QAAQ;AAClBN,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAAA,CACb;AACD;AAAA,MAAA;AAIJA,oBAAAA,MAAI,YAAY;AAAA,QACZ,OAAO;AAAA,MAAA,CACV;AAGD,YAAM,aAAa;AAAA,QACf,QAAQ,UAAU,SAAS;AAAA,MAC/B;AAGA,cAAQ,IAAI,iCAAiC;AAAA,QACzC,WAAW,cAAc;AAAA;AAAA,QACzB,UAAU;AAAA;AAAA,MAAA,CACb;AAGDA,oBAAAA,MAAI,WAAW;AAAA,QACX,KAAK,GAAG,6BAAmC;AAAA;AAAA,QAC3C,UAAU,cAAc;AAAA;AAAA,QACxB,MAAM;AAAA,QACN,UAAU;AAAA,QACV,SAAS,CAAC,QAAQ;AAEV,cAAA;AACA,cAAA;AACW,uBAAA,KAAK,MAAM,IAAI,IAAI;AAAA,mBACzB,GAAG;AACR,uBAAW,IAAI;AAAA,UAAA;AAInB,cAAI,IAAI,eAAe,OAAO,SAAS,SAAS;AAC5CA,0BAAAA,MAAI,YAAY;AAChBA,0BAAAA,MAAI,UAAU;AAAA,cACV,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YAAA,CACb;AACD,uBAAW,MAAM;AACbA,4BAAAA,MAAI,WAAW;AAAA,gBACX,KAAK;AAAA,cAAA,CACR;AAAA,eACF,GAAI;AAAA,UAAA,OACJ;AAEHA,0BAAAA,MAAI,YAAY;AAChBA,0BAAAA,MAAI,UAAU;AAAA,cACV,OAAO,SAAS,WAAW;AAAA,cAC3B,MAAM;AAAA,cACN,UAAU;AAAA,YAAA,CACb;AAAA,UAAA;AAAA,QAET;AAAA,QACA,MAAM,CAAC,QAAQ;AACH,kBAAA,MAAM,SAAS,GAAG;AAC1BA,wBAAAA,MAAI,YAAY;AAChBA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAAA,CACb;AAAA,QAAA;AAAA,MACL,CACH;AAAA,IACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3cA,GAAG,WAAW,eAAe;"}