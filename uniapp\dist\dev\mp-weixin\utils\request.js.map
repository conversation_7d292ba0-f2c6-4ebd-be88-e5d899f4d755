{"version": 3, "file": "request.js", "sources": ["../../../../src/utils/request.ts"], "sourcesContent": ["import { CustomRequestOptions } from '@/interceptors/request'\r\n\r\n/**\r\n * 请求方法: 主要是对 uni.request 的封装，去适配 openapi-ts-request 的 request 方法\r\n * @param options 请求参数\r\n * @returns 返回 Promise 对象\r\n */\r\nconst http = <T>(options: CustomRequestOptions) => {\r\n  // 1. 返回 Promise 对象\r\n  return new Promise<T>((resolve, reject) => {\r\n    uni.request({\r\n      ...options,\r\n      dataType: 'json',\r\n      // #ifndef MP-WEIXIN\r\n      responseType: 'json',\r\n      // #endif\r\n      // 响应成功\r\n      success(res) {\r\n        // 状态码 2xx，参考 axios 的设计\r\n        if (res.statusCode >= 200 && res.statusCode < 300) {\r\n          // 2.1 提取核心数据 res.data\r\n          resolve(res.data as T)\r\n        } else if (res.statusCode === 401) {\r\n          // 401错误  -> 清理用户信息，跳转到登录页\r\n          // userStore.clearUserInfo()\r\n          // uni.navigateTo({ url: '/pages/login/login' })\r\n          reject(res)\r\n        } else {\r\n          // 其他错误 -> 根据后端错误信息轻提示\r\n          !options.hideErrorToast &&\r\n            uni.showToast({\r\n              icon: 'none',\r\n              title: (res.data as T & { msg?: string })?.msg || '请求错误',\r\n            })\r\n          reject(res)\r\n        }\r\n      },\r\n      // 响应失败\r\n      fail(err) {\r\n        uni.showToast({\r\n          icon: 'none',\r\n          title: '网络错误，换个网络试试',\r\n        })\r\n        reject(err)\r\n      },\r\n    })\r\n  })\r\n}\r\n\r\n/*\r\n * openapi-ts-request 工具的 request 跨客户端适配方法\r\n */\r\nexport default function request<T = unknown>(\r\n  url: string,\r\n  options: Omit<CustomRequestOptions, 'url'> & {\r\n    params?: Record<string, unknown>\r\n    headers?: Record<string, unknown>\r\n  },\r\n) {\r\n  const requestOptions = {\r\n    url,\r\n    ...options,\r\n  }\r\n\r\n  if (options.params) {\r\n    requestOptions.query = requestOptions.params\r\n    delete requestOptions.params\r\n  }\r\n\r\n  if (options.headers) {\r\n    requestOptions.header = options.headers\r\n    delete requestOptions.headers\r\n  }\r\n\r\n  return http<T>(requestOptions)\r\n}\r\n"], "names": ["uni"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAOA,MAAM,OAAO,CAAI,YAAkC;AAEjD,SAAO,IAAI,QAAW,CAAC,SAAS,WAAW;AACzCA,kBAAAA,MAAI,QAAQ,iCACP,UADO;AAAA,MAEV,UAAU;AAAA;AAAA,MAKV,QAAQ,KAAK;;AAEX,YAAI,IAAI,cAAc,OAAO,IAAI,aAAa,KAAK;AAEjD,kBAAQ,IAAI,IAAS;AAAA,QAAA,WACZ,IAAI,eAAe,KAAK;AAIjC,iBAAO,GAAG;AAAA,QAAA,OACL;AAEJ,WAAA,QAAQ,kBACPA,cAAA,MAAI,UAAU;AAAA,YACZ,MAAM;AAAA,YACN,SAAQ,SAAI,SAAJ,mBAAmC,QAAO;AAAA,UAAA,CACnD;AACH,iBAAO,GAAG;AAAA,QAAA;AAAA,MAEd;AAAA;AAAA,MAEA,KAAK,KAAK;AACRA,sBAAAA,MAAI,UAAU;AAAA,UACZ,MAAM;AAAA,UACN,OAAO;AAAA,QAAA,CACR;AACD,eAAO,GAAG;AAAA,MAAA;AAAA,IACZ,EACD;AAAA,EAAA,CACF;AACH;AAKwB,SAAA,QACtB,KACA,SAIA;AACA,QAAM,iBAAiB;AAAA,IACrB;AAAA,KACG;AAGL,MAAI,QAAQ,QAAQ;AAClB,mBAAe,QAAQ,eAAe;AACtC,WAAO,eAAe;AAAA,EAAA;AAGxB,MAAI,QAAQ,SAAS;AACnB,mBAAe,SAAS,QAAQ;AAChC,WAAO,eAAe;AAAA,EAAA;AAGxB,SAAO,KAAQ,cAAc;AAC/B;;"}