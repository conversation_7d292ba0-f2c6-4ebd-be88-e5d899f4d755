{"version": 3, "file": "wd-picker-view.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-picker-view/wd-picker-view.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1waWNrZXItdmlldy93ZC1waWNrZXItdmlldy52dWU"], "sourcesContent": ["<template>\n  <view :class=\"`wd-picker-view ${customClass}`\" :style=\"customStyle\">\n    <view class=\"wd-picker-view__loading\" v-if=\"loading\">\n      <wd-loading :color=\"loadingColor\" />\n    </view>\n    <view :style=\"`height: ${columnsHeight - 20}px;`\">\n      <picker-view\n        mask-class=\"wd-picker-view__mask\"\n        indicator-class=\"wd-picker-view__roller\"\n        :indicator-style=\"`height: ${itemHeight}px;`\"\n        :style=\"`height: ${columnsHeight - 20}px;`\"\n        :value=\"selectedIndex\"\n        :immediate-change=\"immediateChange\"\n        @change=\"onChange\"\n        @pickstart=\"onPickStart\"\n        @pickend=\"onPickEnd\"\n      >\n        <picker-view-column v-for=\"(col, colIndex) in formatColumns\" :key=\"colIndex\" class=\"wd-picker-view-column\">\n          <view\n            v-for=\"(row, rowIndex) in col\"\n            :key=\"rowIndex\"\n            :class=\"`wd-picker-view-column__item ${row['disabled'] ? 'wd-picker-view-column__item--disabled' : ''}  ${\n              selectedIndex[colIndex] == rowIndex ? 'wd-picker-view-column__item--active' : ''\n            }`\"\n            :style=\"`line-height: ${itemHeight}px;`\"\n          >\n            {{ row[labelKey] }}\n          </view>\n        </picker-view-column>\n      </picker-view>\n    </view>\n  </view>\n</template>\n\n<script lang=\"ts\">\nexport default {\n  name: 'wd-picker-view',\n  options: {\n    virtualHost: true,\n    addGlobalClass: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n<script lang=\"ts\" setup>\nimport wdLoading from '../wd-loading/wd-loading.vue'\nimport { getCurrentInstance, ref, watch, nextTick } from 'vue'\nimport { deepClone, getType, isArray, isDef, isEqual, range } from '../common/util'\nimport { formatArray, pickerViewProps, type ColumnItem, type PickerViewExpose } from './types'\n\nconst props = defineProps(pickerViewProps)\nconst emit = defineEmits(['change', 'pickstart', 'pickend', 'update:modelValue'])\n\nconst formatColumns = ref<ColumnItem[][]>([]) // 格式化后的列数据\nconst itemHeight = ref<number>(35)\nconst selectedIndex = ref<Array<number>>([]) // 格式化之后，每列选中的下标集合\n\nwatch(\n  [() => props.modelValue, () => props.columns],\n  (newValue, oldValue) => {\n    if (!isEqual(oldValue[1], newValue[1])) {\n      if (isArray(newValue[1]) && newValue[1].length > 0) {\n        formatColumns.value = formatArray(newValue[1], props.valueKey, props.labelKey)\n      } else {\n        // 当 columns 变为空时，清空 formatColumns 和 selectedIndex\n        formatColumns.value = []\n        selectedIndex.value = []\n      }\n    }\n    if (isDef(newValue[0])) {\n      selectWithValue(newValue[0])\n    }\n  },\n  {\n    deep: true,\n    immediate: true\n  }\n)\n\nconst { proxy } = getCurrentInstance() as any\n\n/**\n * 根据传入的value，寻找对应的索引，并传递给原生选择器。\n * 需要保证formatColumns先设置，之后会修改selectedIndex。\n * @param {String|Number|Boolean|Array<String|Number|Boolean|Array<any>>}value\n */\nfunction selectWithValue(value: string | number | boolean | number[] | string[] | boolean[]) {\n  if (formatColumns.value.length === 0) {\n    selectedIndex.value = [] // 如果列为空，直接清空选中索引\n    return\n  }\n  // 使其默认选中首项\n  if (value === '' || !isDef(value) || (isArray(value) && value.length === 0)) {\n    value = formatColumns.value.map((col) => {\n      return col[0][props.valueKey]\n    })\n  }\n  const valueType = getType(value)\n  const type = ['string', 'number', 'boolean', 'array']\n  if (type.indexOf(valueType) === -1) console.error(`value must be one of ${type.toString()}`)\n\n  /**\n   * 1.单key转为Array<key>\n   * 2.根据formatColumns的长度截取Array<String>，保证下面的遍历不溢出\n   * 3.根据每列的key值找到选项中value为此key的下标并记录\n   */\n  value = isArray(value) ? value : [value as string]\n  value = value.slice(0, formatColumns.value.length)\n\n  let selected: number[] = deepClone(selectedIndex.value)\n  value.forEach((target, col) => {\n    let row = formatColumns.value[col].findIndex((row) => {\n      return row[props.valueKey].toString() === target.toString()\n    })\n    row = row === -1 ? 0 : row\n    selected = correctSelectedIndex(col, row, selected)\n  })\n  /** 根据formatColumns的长度去除selectWithIndex无用的部分。\n   * 始终保持value、selectWithIndex、formatColumns长度一致\n   */\n  selectedIndex.value = selected.slice(0, value.length)\n}\n\n/**\n * 修正选中项的值\n * @param value 当前picker选择器选中的值\n * @param origin 原始选中的值\n */\nfunction correctSelected(value: number[]) {\n  let selected = deepClone(value)\n  value.forEach((row, col) => {\n    row = range(row, 0, formatColumns.value[col].length - 1)\n    selected = correctSelectedIndex(col, row, selected)\n  })\n  return selected\n}\n\n/**\n * 修正选中项指定列行的值\n * @param columnIndex 列下标\n * @param rowIndex 行下标\n * @param selected 选中值列表\n */\nfunction correctSelectedIndex(columnIndex: number, rowIndex: number, selected: number[]) {\n  const col = formatColumns.value[columnIndex]\n  if (!col || !col[rowIndex]) {\n    throw Error(`The value to select with Col:${columnIndex} Row:${rowIndex} is incorrect`)\n  }\n  const select: number[] = deepClone(selected)\n  select[columnIndex] = rowIndex\n\n  // 被禁用的无法选中，选中距离它最近的未被禁用的\n  if (col[rowIndex].disabled) {\n    // 寻找值为0或最最近的未被禁用的节点的索引\n    const prev = col\n      .slice(0, rowIndex)\n      .reverse()\n      .findIndex((s) => !s.disabled)\n    const next = col.slice(rowIndex + 1).findIndex((s) => !s.disabled)\n    if (prev !== -1) {\n      select[columnIndex] = rowIndex - 1 - prev\n    } else if (next !== -1) {\n      select[columnIndex] = rowIndex + 1 + next\n    } else if (select[columnIndex] === undefined) {\n      select[columnIndex] = 0\n    }\n  }\n  return select\n}\n\n/**\n * 选择器选中项变化时触发\n * @param param0\n */\nfunction onChange({ detail: { value } }: { detail: { value: number[] } }) {\n  value = value.map((v: any) => {\n    return Number(v || 0)\n  })\n  const index = getChangeDiff(value)\n  // 先将picker选择器的值赋给selectedIndex，然后重新赋予修正后的值，防止两次操作修正结果一致时pikcer视图不刷新\n  selectedIndex.value = deepClone(value)\n  nextTick(() => {\n    // 重新赋予修正后的值\n    selectedIndex.value = correctSelected(value)\n    if (props.columnChange) {\n      // columnsChange 可能有异步操作，需要添加 resolve 进行回调通知，形参小于4个则为同步\n      if (props.columnChange.length < 4) {\n        props.columnChange(proxy.$.exposed, getSelects(), index || 0, () => {})\n        handleChange(index || 0)\n      } else {\n        props.columnChange(proxy.$.exposed, getSelects(), index || 0, () => {\n          // 如果selectedIndex只有一列，返回此项；如果是多项，返回所有选中项。\n          handleChange(index || 0)\n        })\n      }\n    } else {\n      // 如果selectedIndex只有一列，返回此项；如果是多项，返回所有选中项。\n      handleChange(index || 0)\n    }\n  })\n}\n\n/**\n * 获取选中项变化的列的下标\n * @param now 当前选中项值\n * @param origin 旧选中项值\n */\nfunction getChangeColumn(now: number[], origin: number[]) {\n  if (!now || !origin) return -1\n  const index = now.findIndex((row, index) => row !== origin[index])\n  return index\n}\n\nfunction getChangeDiff(value: number[]) {\n  value = value.slice(0, formatColumns.value.length)\n\n  // 保留选中前的\n  const origin: number[] = deepClone(selectedIndex.value)\n  // 存储赋值旧值，便于外部比较\n  let selected: number[] = deepClone(selectedIndex.value)\n\n  value.forEach((row, col) => {\n    row = range(row, 0, formatColumns.value[col].length - 1)\n    if (row === origin[col]) return\n    selected = correctSelectedIndex(col, row, selected)\n  })\n\n  // 值变化的列\n  const diffCol = getChangeColumn(selected, origin)\n  if (diffCol === -1) return\n\n  // 获取变化的的行\n  const diffRow = selected[diffCol]\n\n  // 如果selectedIndex只有一列，返回选中项的索引；如果是多项，返回选中项所在的列。\n  return selected.length === 1 ? diffRow : diffCol\n}\n\n/**\n * 列更新\n * @param index 列下标\n */\nfunction handleChange(index: number) {\n  const value = getValues()\n\n  // 避免多次触发change\n  if (isEqual(value, props.modelValue)) return\n\n  emit('update:modelValue', value)\n  // 延迟一下，避免组件刚渲染时调用者的事件未初始化好\n  setTimeout(() => {\n    emit('change', {\n      picker: proxy.$.exposed,\n      value,\n      index\n    })\n  }, 0)\n}\n\n/**\n * @description 获取所有列选中项，返回值为一个数组\n */\nfunction getSelects() {\n  const selects = selectedIndex.value.map((row, col) => formatColumns.value[col][row])\n  // 单列选择器，则返回单项\n  if (selects.length === 1) {\n    return selects[0]\n  }\n  return selects\n}\n\n/**\n * 获取所有列的选中值\n * 如果values只有一项则将第一项返回\n */\nfunction getValues() {\n  const { valueKey } = props\n  const values = selectedIndex.value.map((row, col) => {\n    return formatColumns.value[col][row][valueKey]\n  })\n\n  if (values.length === 1) {\n    return values[0]\n  }\n  return values\n}\n\n/**\n * 获取所有列选中项的label，返回值为一个数组\n */\nfunction getLabels() {\n  const { labelKey } = props\n  return selectedIndex.value.map((row, col) => formatColumns.value[col][row][labelKey])\n}\n\n/**\n * 获取某一列的选中项下标\n * @param {Number} columnIndex 列的下标\n * @returns {Number} 下标\n */\nfunction getColumnIndex(columnIndex: number) {\n  return selectedIndex.value[columnIndex]\n}\n\n/**\n *  获取某一列的选项\n * @param {Number} columnIndex 列的下标\n * @returns {Array<{valueKey,labelKey}>} 当前列的集合\n */\nfunction getColumnData(columnIndex: number) {\n  return formatColumns.value[columnIndex]\n}\n\n/**\n * 设置列数据\n * @param columnIndex 列下标\n * @param data // 列数据\n * @param rowIndex // 行下标\n */\nfunction setColumnData(columnIndex: number, data: Array<string | number | ColumnItem | Array<string | number | ColumnItem>>, rowIndex: number = 0) {\n  formatColumns.value[columnIndex] = formatArray(data, props.valueKey, props.labelKey).reduce((acc, val) => acc.concat(val), [])\n  selectedIndex.value = correctSelectedIndex(columnIndex, rowIndex, selectedIndex.value)\n}\n\n/**\n * 获取列数据\n */\nfunction getColumnsData() {\n  return deepClone(formatColumns.value)\n}\n\n/**\n * 获取选中数据\n */\nfunction getSelectedIndex() {\n  return selectedIndex.value\n}\n\n/**\n * 用于重置列数据为指定列数据\n */\nfunction resetColumns(columns: (string | number | string[] | number[] | ColumnItem | ColumnItem[])[]) {\n  if (isArray(columns) && columns.length) {\n    formatColumns.value = formatArray(columns, props.valueKey, props.labelKey)\n  }\n}\n\nfunction onPickStart() {\n  emit('pickstart')\n}\n\nfunction onPickEnd() {\n  emit('pickend')\n}\n\ndefineExpose<PickerViewExpose>({\n  getSelects,\n  getValues,\n  setColumnData,\n  getColumnsData,\n  getColumnData,\n  getColumnIndex,\n  getLabels,\n  getSelectedIndex,\n  resetColumns\n})\n</script>\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-picker-view/wd-picker-view.vue'\nwx.createComponent(Component)"], "names": ["ref", "watch", "isEqual", "isArray", "formatArray", "isDef", "getCurrentInstance", "getType", "deepClone", "row", "range", "nextTick", "index"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AA6CA,MAAA,YAAsB,MAAA;AAVtB,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,EAAA;AAEpB;;;;;AAQA,UAAM,QAAQ;AACd,UAAM,OAAO;AAEP,UAAA,gBAAgBA,cAAoB,IAAA,EAAE;AACtC,UAAA,aAAaA,kBAAY,EAAE;AAC3B,UAAA,gBAAgBA,cAAmB,IAAA,EAAE;AAE3CC,kBAAA;AAAA,MACE,CAAC,MAAM,MAAM,YAAY,MAAM,MAAM,OAAO;AAAA,MAC5C,CAAC,UAAU,aAAa;AAClB,YAAA,CAACC,sBAAQ,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG;AAClC,cAAAC,cAAA,QAAQ,SAAS,CAAC,CAAC,KAAK,SAAS,CAAC,EAAE,SAAS,GAAG;AACpC,0BAAA,QAAQC,0BAAY,SAAS,CAAC,GAAG,MAAM,UAAU,MAAM,QAAQ;AAAA,UAAA,OACxE;AAEL,0BAAc,QAAQ,CAAC;AACvB,0BAAc,QAAQ,CAAC;AAAA,UAAA;AAAA,QACzB;AAEF,YAAIC,oBAAM,SAAS,CAAC,CAAC,GAAG;AACN,0BAAA,SAAS,CAAC,CAAC;AAAA,QAAA;AAAA,MAE/B;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,WAAW;AAAA,MAAA;AAAA,IAEf;AAEM,UAAA,EAAE,MAAM,IAAIC,iCAAmB;AAOrC,aAAS,gBAAgB,OAAoE;AACvF,UAAA,cAAc,MAAM,WAAW,GAAG;AACpC,sBAAc,QAAQ,CAAC;AACvB;AAAA,MAAA;AAGE,UAAA,UAAU,MAAM,CAACD,cAAAA,MAAM,KAAK,KAAMF,cAAA,QAAQ,KAAK,KAAK,MAAM,WAAW,GAAI;AAC3E,gBAAQ,cAAc,MAAM,IAAI,CAAC,QAAQ;AACvC,iBAAO,IAAI,CAAC,EAAE,MAAM,QAAQ;AAAA,QAAA,CAC7B;AAAA,MAAA;AAEG,YAAA,YAAYI,sBAAQ,KAAK;AAC/B,YAAM,OAAO,CAAC,UAAU,UAAU,WAAW,OAAO;AAChD,UAAA,KAAK,QAAQ,SAAS,MAAM;AAAI,gBAAQ,MAAM,wBAAwB,KAAK,SAAU,CAAA,EAAE;AAO3F,cAAQJ,cAAAA,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAe;AACjD,cAAQ,MAAM,MAAM,GAAG,cAAc,MAAM,MAAM;AAE7C,UAAA,WAAqBK,cAAAA,UAAU,cAAc,KAAK;AAChD,YAAA,QAAQ,CAAC,QAAQ,QAAQ;AAC7B,YAAI,MAAM,cAAc,MAAM,GAAG,EAAE,UAAU,CAACC,SAAQ;AACpD,iBAAOA,KAAI,MAAM,QAAQ,EAAE,SAAS,MAAM,OAAO,SAAS;AAAA,QAAA,CAC3D;AACK,cAAA,QAAQ,KAAK,IAAI;AACZ,mBAAA,qBAAqB,KAAK,KAAK,QAAQ;AAAA,MAAA,CACnD;AAID,oBAAc,QAAQ,SAAS,MAAM,GAAG,MAAM,MAAM;AAAA,IAAA;AAQtD,aAAS,gBAAgB,OAAiB;AACpC,UAAA,WAAWD,wBAAU,KAAK;AACxB,YAAA,QAAQ,CAAC,KAAK,QAAQ;AACpB,cAAAE,cAAAA,MAAM,KAAK,GAAG,cAAc,MAAM,GAAG,EAAE,SAAS,CAAC;AAC5C,mBAAA,qBAAqB,KAAK,KAAK,QAAQ;AAAA,MAAA,CACnD;AACM,aAAA;AAAA,IAAA;AASA,aAAA,qBAAqB,aAAqB,UAAkB,UAAoB;AACjF,YAAA,MAAM,cAAc,MAAM,WAAW;AAC3C,UAAI,CAAC,OAAO,CAAC,IAAI,QAAQ,GAAG;AAC1B,cAAM,MAAM,gCAAgC,WAAW,QAAQ,QAAQ,eAAe;AAAA,MAAA;AAElF,YAAA,SAAmBF,wBAAU,QAAQ;AAC3C,aAAO,WAAW,IAAI;AAGlB,UAAA,IAAI,QAAQ,EAAE,UAAU;AAE1B,cAAM,OAAO,IACV,MAAM,GAAG,QAAQ,EACjB,QAAQ,EACR,UAAU,CAAC,MAAM,CAAC,EAAE,QAAQ;AACzB,cAAA,OAAO,IAAI,MAAM,WAAW,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,EAAE,QAAQ;AACjE,YAAI,SAAS,IAAI;AACR,iBAAA,WAAW,IAAI,WAAW,IAAI;AAAA,QAAA,WAC5B,SAAS,IAAI;AACf,iBAAA,WAAW,IAAI,WAAW,IAAI;AAAA,QAC5B,WAAA,OAAO,WAAW,MAAM,QAAW;AAC5C,iBAAO,WAAW,IAAI;AAAA,QAAA;AAAA,MACxB;AAEK,aAAA;AAAA,IAAA;AAOT,aAAS,SAAS,EAAE,QAAQ,EAAE,WAA4C;AAChE,cAAA,MAAM,IAAI,CAAC,MAAW;AACrB,eAAA,OAAO,KAAK,CAAC;AAAA,MAAA,CACrB;AACK,YAAA,QAAQ,cAAc,KAAK;AAEnB,oBAAA,QAAQA,wBAAU,KAAK;AACrCG,oBAAAA,WAAS,MAAM;AAEC,sBAAA,QAAQ,gBAAgB,KAAK;AAC3C,YAAI,MAAM,cAAc;AAElB,cAAA,MAAM,aAAa,SAAS,GAAG;AAC3B,kBAAA,aAAa,MAAM,EAAE,SAAS,WAAc,GAAA,SAAS,GAAG,MAAM;AAAA,YAAA,CAAE;AACtE,yBAAa,SAAS,CAAC;AAAA,UAAA,OAClB;AACC,kBAAA,aAAa,MAAM,EAAE,SAAS,WAAc,GAAA,SAAS,GAAG,MAAM;AAElE,2BAAa,SAAS,CAAC;AAAA,YAAA,CACxB;AAAA,UAAA;AAAA,QACH,OACK;AAEL,uBAAa,SAAS,CAAC;AAAA,QAAA;AAAA,MACzB,CACD;AAAA,IAAA;AAQM,aAAA,gBAAgB,KAAe,QAAkB;AACpD,UAAA,CAAC,OAAO,CAAC;AAAe,eAAA;AACtB,YAAA,QAAQ,IAAI,UAAU,CAAC,KAAKC,WAAU,QAAQ,OAAOA,MAAK,CAAC;AAC1D,aAAA;AAAA,IAAA;AAGT,aAAS,cAAc,OAAiB;AACtC,cAAQ,MAAM,MAAM,GAAG,cAAc,MAAM,MAAM;AAG3C,YAAA,SAAmBJ,cAAAA,UAAU,cAAc,KAAK;AAElD,UAAA,WAAqBA,cAAAA,UAAU,cAAc,KAAK;AAEhD,YAAA,QAAQ,CAAC,KAAK,QAAQ;AACpB,cAAAE,cAAAA,MAAM,KAAK,GAAG,cAAc,MAAM,GAAG,EAAE,SAAS,CAAC;AACnD,YAAA,QAAQ,OAAO,GAAG;AAAG;AACd,mBAAA,qBAAqB,KAAK,KAAK,QAAQ;AAAA,MAAA,CACnD;AAGK,YAAA,UAAU,gBAAgB,UAAU,MAAM;AAChD,UAAI,YAAY;AAAI;AAGd,YAAA,UAAU,SAAS,OAAO;AAGzB,aAAA,SAAS,WAAW,IAAI,UAAU;AAAA,IAAA;AAO3C,aAAS,aAAa,OAAe;AACnC,YAAM,QAAQ,UAAU;AAGpB,UAAAR,sBAAQ,OAAO,MAAM,UAAU;AAAG;AAEtC,WAAK,qBAAqB,KAAK;AAE/B,iBAAW,MAAM;AACf,aAAK,UAAU;AAAA,UACb,QAAQ,MAAM,EAAE;AAAA,UAChB;AAAA,UACA;AAAA,QAAA,CACD;AAAA,SACA,CAAC;AAAA,IAAA;AAMN,aAAS,aAAa;AACpB,YAAM,UAAU,cAAc,MAAM,IAAI,CAAC,KAAK,QAAQ,cAAc,MAAM,GAAG,EAAE,GAAG,CAAC;AAE/E,UAAA,QAAQ,WAAW,GAAG;AACxB,eAAO,QAAQ,CAAC;AAAA,MAAA;AAEX,aAAA;AAAA,IAAA;AAOT,aAAS,YAAY;AACb,YAAA,EAAE,aAAa;AACrB,YAAM,SAAS,cAAc,MAAM,IAAI,CAAC,KAAK,QAAQ;AACnD,eAAO,cAAc,MAAM,GAAG,EAAE,GAAG,EAAE,QAAQ;AAAA,MAAA,CAC9C;AAEG,UAAA,OAAO,WAAW,GAAG;AACvB,eAAO,OAAO,CAAC;AAAA,MAAA;AAEV,aAAA;AAAA,IAAA;AAMT,aAAS,YAAY;AACb,YAAA,EAAE,aAAa;AACrB,aAAO,cAAc,MAAM,IAAI,CAAC,KAAK,QAAQ,cAAc,MAAM,GAAG,EAAE,GAAG,EAAE,QAAQ,CAAC;AAAA,IAAA;AAQtF,aAAS,eAAe,aAAqB;AACpC,aAAA,cAAc,MAAM,WAAW;AAAA,IAAA;AAQxC,aAAS,cAAc,aAAqB;AACnC,aAAA,cAAc,MAAM,WAAW;AAAA,IAAA;AASxC,aAAS,cAAc,aAAqB,MAAiF,WAAmB,GAAG;AACjJ,oBAAc,MAAM,WAAW,IAAIE,0BAAY,MAAM,MAAM,UAAU,MAAM,QAAQ,EAAE,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,GAAG,GAAG,EAAE;AAC7H,oBAAc,QAAQ,qBAAqB,aAAa,UAAU,cAAc,KAAK;AAAA,IAAA;AAMvF,aAAS,iBAAiB;AACjB,aAAAI,cAAA,UAAU,cAAc,KAAK;AAAA,IAAA;AAMtC,aAAS,mBAAmB;AAC1B,aAAO,cAAc;AAAA,IAAA;AAMvB,aAAS,aAAa,SAAgF;AACpG,UAAIL,sBAAQ,OAAO,KAAK,QAAQ,QAAQ;AACtC,sBAAc,QAAQC,0BAAY,SAAS,MAAM,UAAU,MAAM,QAAQ;AAAA,MAAA;AAAA,IAC3E;AAGF,aAAS,cAAc;AACrB,WAAK,WAAW;AAAA,IAAA;AAGlB,aAAS,YAAY;AACnB,WAAK,SAAS;AAAA,IAAA;AAGe,aAAA;AAAA,MAC7B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5WD,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}