{"version": 3, "file": "wd-sticky-box.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-sticky-box/wd-sticky-box.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1zdGlja3ktYm94L3dkLXN0aWNreS1ib3gudnVl"], "sourcesContent": ["<template>\n  <view style=\"position: relative\">\n    <view :class=\"`wd-sticky-box ${props.customClass}`\" :style=\"customStyle\" :id=\"styckyBoxId\">\n      <wd-resize @resize=\"handleResize\">\n        <slot />\n      </wd-resize>\n    </view>\n  </view>\n</template>\n\n<script lang=\"ts\">\nexport default {\n  name: 'wd-sticky-box',\n  options: {\n    addGlobalClass: true,\n    // virtualHost: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport wdResize from '../wd-resize/wd-resize.vue'\nimport { getCurrentInstance, onBeforeMount, reactive, ref } from 'vue'\nimport { getRect, uuid } from '../common/util'\nimport { baseProps } from '../common/props'\nimport { STICKY_BOX_KEY } from './types'\nimport { useChildren } from '../composables/useChildren'\n\nconst props = defineProps(baseProps)\n\nconst styckyBoxId = ref<string>(`wd-sticky-box${uuid()}`)\n\nconst observerMap = ref<Map<any, any>>(new Map())\n\nconst boxStyle = reactive({\n  height: 0,\n  width: 0\n})\n\nconst { proxy } = getCurrentInstance() as any\n\nconst { children: stickyList, linkChildren } = useChildren(STICKY_BOX_KEY)\nlinkChildren({\n  boxStyle: boxStyle,\n  observerForChild\n})\n\nonBeforeMount(() => {\n  observerMap.value = new Map()\n})\n\n/**\n * 容器大小变化后重新监听sticky组件与box组件的交叉状态\n * @param detail\n */\nfunction handleResize(detail: any) {\n  // 相对的容器大小改变后，同步设置 wd-sticky-box 的大小\n  boxStyle.width = detail.width\n  boxStyle.height = detail.height\n  // wd-sticky-box 大小变化时，重新监听所有吸顶元素\n  const temp = observerMap.value\n  observerMap.value = new Map()\n  for (const [uid] of temp) {\n    const child = stickyList.find((sticky) => {\n      return sticky.$.uid === uid\n    })\n    observerForChild(child)\n  }\n  temp.forEach((observer) => {\n    observer.disconnect()\n  })\n  temp.clear()\n}\n/**\n * 删除对指定sticky的监听\n * @param child 指定的子组件\n */\nfunction deleteObserver(child: any) {\n  const observer = observerMap.value.get(child.$.uid)\n  if (!observer) return\n  observer.disconnect()\n  observerMap.value.delete(child.$.uid)\n}\n/**\n * 针对指定sticky添加监听\n * @param child 指定的子组件\n */\nfunction createObserver(child: any) {\n  const observer = uni.createIntersectionObserver(proxy, { thresholds: [0, 0.5] })\n  observerMap.value.set(child.$.uid, observer)\n  return observer\n}\n/**\n * 监听子组件\n * @param child 子组件\n */\nfunction observerForChild(child: any) {\n  deleteObserver(child)\n  const observer = createObserver(child)\n  const exposed = child.$.exposed\n  let offset = exposed.stickyState.height + exposed.offsetTop\n\n\n\n\n\n\n  if (boxStyle.height <= exposed.stickyState.height) {\n    exposed.setPosition(false, 'absolute', 0)\n  }\n  observer.relativeToViewport({ top: -offset }).observe(`#${styckyBoxId.value}`, (result) => {\n    handleRelativeTo(exposed, result)\n  })\n  // 当子组件默认处于边界外且永远不会进入边界内时，需要手动调用一次\n  getRect(`#${styckyBoxId.value}`, false, proxy)\n    .then((res) => {\n\n\n\n\n      if (Number(res.bottom) <= offset) handleRelativeTo(exposed, { boundingClientRect: res })\n    })\n    .catch((res) => {\n      console.log(res)\n    })\n}\n/**\n *  监听容器组件\n * @param {Object} exposed wd-sticky实例暴露出的事件\n * @param {Object} boundingClientRect 边界信息\n */\nfunction handleRelativeTo(exposed: any, { boundingClientRect }: any) {\n  let childOffsetTop = exposed.offsetTop\n\n\n\n\n\n  const offset = exposed.stickyState.height + childOffsetTop\n  let isAbsolute = boundingClientRect.bottom <= offset\n\n\n\n  if (isAbsolute) {\n    exposed.setPosition(true, 'absolute', boundingClientRect.height - exposed.stickyState.height)\n  } else if (boundingClientRect.top <= offset && !isAbsolute) {\n    if (exposed.stickyState.state === 'normal') return\n    exposed.setPosition(false, 'fixed', childOffsetTop)\n  }\n}\n</script>\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-sticky-box/wd-sticky-box.vue'\nwx.createComponent(Component)"], "names": ["ref", "uuid", "reactive", "getCurrentInstance", "useChildren", "STICKY_BOX_KEY", "onBeforeMount", "uni", "getRect"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAsBA,MAAA,WAAqB,MAAA;AAXrB,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,gBAAgB;AAAA;AAAA,IAEhB,gBAAgB;AAAA,EAAA;AAEpB;;;;AAWA,UAAM,QAAQ;AAEd,UAAM,cAAcA,cAAAA,IAAY,gBAAgBC,cAAA,KAAA,CAAM,EAAE;AAExD,UAAM,cAAcD,cAAAA,IAAuB,oBAAA,KAAK;AAEhD,UAAM,WAAWE,cAAAA,SAAS;AAAA,MACxB,QAAQ;AAAA,MACR,OAAO;AAAA,IAAA,CACR;AAEK,UAAA,EAAE,MAAM,IAAIC,iCAAmB;AAErC,UAAM,EAAE,UAAU,YAAY,aAAa,IAAIC,cAAAA,YAAYC,cAAAA,cAAc;AAC5D,iBAAA;AAAA,MACX;AAAA,MACA;AAAA,IAAA,CACD;AAEDC,kBAAAA,cAAc,MAAM;AACN,kBAAA,4BAAY,IAAI;AAAA,IAAA,CAC7B;AAMD,aAAS,aAAa,QAAa;AAEjC,eAAS,QAAQ,OAAO;AACxB,eAAS,SAAS,OAAO;AAEzB,YAAM,OAAO,YAAY;AACb,kBAAA,4BAAY,IAAI;AACjB,iBAAA,CAAC,GAAG,KAAK,MAAM;AACxB,cAAM,QAAQ,WAAW,KAAK,CAAC,WAAW;AACjC,iBAAA,OAAO,EAAE,QAAQ;AAAA,QAAA,CACzB;AACD,yBAAiB,KAAK;AAAA,MAAA;AAEnB,WAAA,QAAQ,CAAC,aAAa;AACzB,iBAAS,WAAW;AAAA,MAAA,CACrB;AACD,WAAK,MAAM;AAAA,IAAA;AAMb,aAAS,eAAe,OAAY;AAClC,YAAM,WAAW,YAAY,MAAM,IAAI,MAAM,EAAE,GAAG;AAClD,UAAI,CAAC;AAAU;AACf,eAAS,WAAW;AACpB,kBAAY,MAAM,OAAO,MAAM,EAAE,GAAG;AAAA,IAAA;AAMtC,aAAS,eAAe,OAAY;AAC5B,YAAA,WAAWC,oBAAI,2BAA2B,OAAO,EAAE,YAAY,CAAC,GAAG,GAAG,GAAG;AAC/E,kBAAY,MAAM,IAAI,MAAM,EAAE,KAAK,QAAQ;AACpC,aAAA;AAAA,IAAA;AAMT,aAAS,iBAAiB,OAAY;AACpC,qBAAe,KAAK;AACd,YAAA,WAAW,eAAe,KAAK;AAC/B,YAAA,UAAU,MAAM,EAAE;AACxB,UAAI,SAAS,QAAQ,YAAY,SAAS,QAAQ;AAOlD,UAAI,SAAS,UAAU,QAAQ,YAAY,QAAQ;AACzC,gBAAA,YAAY,OAAO,YAAY,CAAC;AAAA,MAAA;AAE1C,eAAS,mBAAmB,EAAE,KAAK,CAAC,OAAO,CAAC,EAAE,QAAQ,IAAI,YAAY,KAAK,IAAI,CAAC,WAAW;AACzF,yBAAiB,SAAS,MAAM;AAAA,MAAA,CACjC;AAEOC,oBAAAA,QAAA,IAAI,YAAY,KAAK,IAAI,OAAO,KAAK,EAC1C,KAAK,CAAC,QAAQ;AAKT,YAAA,OAAO,IAAI,MAAM,KAAK;AAAQ,2BAAiB,SAAS,EAAE,oBAAoB,IAAA,CAAK;AAAA,MAAA,CACxF,EACA,MAAM,CAAC,QAAQ;AACd,gBAAQ,IAAI,GAAG;AAAA,MAAA,CAChB;AAAA,IAAA;AAOL,aAAS,iBAAiB,SAAc,EAAE,sBAA2B;AACnE,UAAI,iBAAiB,QAAQ;AAMvB,YAAA,SAAS,QAAQ,YAAY,SAAS;AACxC,UAAA,aAAa,mBAAmB,UAAU;AAI9C,UAAI,YAAY;AACd,gBAAQ,YAAY,MAAM,YAAY,mBAAmB,SAAS,QAAQ,YAAY,MAAM;AAAA,MACnF,WAAA,mBAAmB,OAAO,UAAU,CAAC,YAAY;AACtD,YAAA,QAAQ,YAAY,UAAU;AAAU;AACpC,gBAAA,YAAY,OAAO,SAAS,cAAc;AAAA,MAAA;AAAA,IACpD;;;;;;;;;;;;ACpJF,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}