"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
const common_vendor = require("../common/vendor.js");
const initState = {};
const useParamsStore = common_vendor.defineStore(
  "page-params",
  () => {
    const params = common_vendor.ref(__spreadValues({}, initState));
    const setPageParams = (key, options) => {
      params.value = __spreadValues(__spreadValues({}, params.value), { [key]: options });
    };
    const getPageParams = (key) => {
      return params.value[key];
    };
    const clearPageParams = (key) => {
      delete params.value[key];
    };
    const reset = () => {
      params.value = {};
    };
    return {
      params,
      setPageParams,
      clearPageParams,
      getPageParams,
      reset
    };
  },
  {
    persist: true
  }
);
exports.useParamsStore = useParamsStore;
//# sourceMappingURL=page-params.js.map
