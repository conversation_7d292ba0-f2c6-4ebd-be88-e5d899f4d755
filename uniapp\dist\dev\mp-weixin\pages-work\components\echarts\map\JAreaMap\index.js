"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../../../../common/vendor.js");
const pagesWork_components_echarts_props = require("../../props.js");
require("../../../common/echartUtil.js");
const pagesWork_components_hooks_useEchartMap = require("../../../hooks/useEchartMap.js");
if (!Array) {
  const _component_statusTip = common_vendor.resolveComponent("statusTip");
  _component_statusTip();
}
if (!Math) {
  echartsUniapp();
}
const echartsUniapp = () => "../../index.js";
const _sfc_main = {
  __name: "index",
  props: __spreadValues({}, pagesWork_components_echarts_props.echartProps),
  setup(__props) {
    const props = __props;
    const option = common_vendor.ref({});
    const chartOption = common_vendor.ref({
      geo: {
        map: "",
        itemStyle: {}
      },
      tooltip: {
        textStyle: {
          color: "#fff"
        },
        padding: 5,
        formatter: null
      }
    });
    const mapName = common_vendor.ref("");
    let [{ dataSource, reload, pageTips, config, mapDataJson, getAreaCode }, { queryData, registerMap, setGeoAreaColor, handleTotalAndUnitMap, handleCommonOpt }] = pagesWork_components_hooks_useEchartMap.useChartHook(props, initOption);
    const echartId = common_vendor.ref("");
    const mapObject = common_vendor.computed(() => ({ code: getAreaCode.value, data: mapDataJson.value }));
    function initOption() {
      return __async(this, null, function* () {
        let chartData = dataSource.value;
        mapName.value = yield registerMap();
        try {
          chartOption.value.geo.map = mapName.value;
          chartOption.value.series = [
            {
              name: "地图",
              type: "map",
              map: mapName.value,
              geoIndex: 0,
              aspectScale: 0.75,
              // 长宽比
              showLegendSymbol: false,
              // 存在 legend 时显示
              label: {
                show: true,
                color: "#000"
              },
              emphasis: {
                show: true,
                color: "#000",
                itemStyle: {
                  areaColor: "#2B91B7"
                }
              },
              roam: true,
              itemStyle: {
                areaColor: "#3B5077",
                borderColor: "#3B5077"
              },
              animation: true,
              data: chartData && chartData.length > 0 ? chartData : [],
              zlevel: 1
            }
          ];
          if (props.config && props.config.option) {
            common_vendor.merge(chartOption.value, props.config.option);
            chartOption.value = setGeoAreaColor(chartOption.value, props.config);
            chartOption.value = handleTotalAndUnitMap(props.compName, chartOption.value, props.config, chartData);
            chartOption.value = handleCommonOpt(chartOption.value);
            setTimeout(() => {
              chartOption.value.tooltip.textStyle.color = "#fff";
              chartOption.value.tooltip.padding = 5;
              chartOption.value.tooltip.formatter = (data) => {
                if (data.data) {
                  return `${data.name}<br>${data.value[data.value.length - 1]}`;
                } else {
                  return null;
                }
              };
              option.value = common_vendor.deepClone(chartOption.value);
              console.log("区域地图option.value", option.value);
              pageTips.show = false;
              echartId.value = props.i;
            }, 300);
          }
          if (dataSource.value && dataSource.value.length === 0) {
            pageTips.status = 1;
            pageTips.show = true;
          }
        } catch (e) {
          console.log("区域地图报错", e);
        }
      });
    }
    common_vendor.onMounted(() => {
      queryData();
    });
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.unref(pageTips).show
      }, common_vendor.unref(pageTips).show ? {
        b: common_vendor.p({
          status: common_vendor.unref(pageTips).status
        })
      } : {
        c: common_vendor.o(($event) => option.value = $event),
        d: common_vendor.o(($event) => mapName.value = $event),
        e: common_vendor.o(($event) => mapObject.value.data = $event),
        f: common_vendor.p({
          option: option.value,
          mapName: mapName.value,
          mapData: mapObject.value.data
        })
      });
    };
  }
};
wx.createComponent(_sfc_main);
//# sourceMappingURL=index.js.map
