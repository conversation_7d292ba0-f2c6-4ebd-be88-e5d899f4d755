/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.user-header-section.data-v-dc103050 {
  padding: 24rpx;
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}
.user-info-card.data-v-dc103050 {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #f0f0f0;
}
.user-avatar.data-v-dc103050 {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  border: 3rpx solid #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.user-details.data-v-dc103050 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
}
.user-name.data-v-dc103050 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
  line-height: 1.2;
}
.user-role.data-v-dc103050 {
  font-size: 24rpx;
  color: #666;
  background: #f0f7ff;
  color: #1890ff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  align-self: flex-start;
  font-weight: 500;
}
.chat-list.data-v-dc103050 {
  flex: 1;
  overflow: auto;
  padding: 0 0 40rpx 0;
}
.form-content.data-v-dc103050 {
  padding: 24rpx;
}
.doctor-select-container.data-v-dc103050 {
  border: 1rpx solid #d9d9d9;
  border-radius: 8rpx;
  background: #fff;
  overflow: hidden;
}
.doctor-picker.data-v-dc103050 {
  width: 100%;
}
.picker-display.data-v-dc103050 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 16rpx;
  min-height: 80rpx;
  box-sizing: border-box;
}
.picker-text.data-v-dc103050 {
  font-size: 28rpx;
  color: #666;
  flex: 1;
}
.picker-arrow.data-v-dc103050 {
  font-size: 24rpx;
  color: #999;
  margin-left: 16rpx;
}
.selected-doctors-display.data-v-dc103050 {
  border-top: 1rpx solid #f0f0f0;
  padding: 16rpx;
  background: #f8f9fa;
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}
.doctor-tag.data-v-dc103050 {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
  border: 1rpx solid #d4edda;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
}
.doctor-name.data-v-dc103050 {
  color: #07C160;
  font-weight: 500;
  margin-right: 8rpx;
}
.remove-doctor.data-v-dc103050 {
  color: #999;
  font-size: 28rpx;
  font-weight: bold;
  cursor: pointer;
  width: 24rpx;
  height: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.1);
}
.remove-doctor.data-v-dc103050:hover {
  background: rgba(0, 0, 0, 0.2);
  color: #666;
}
.form-item.data-v-dc103050 {
  margin-bottom: 32rpx;
}
.form-label.data-v-dc103050 {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}
.required.data-v-dc103050 {
  color: #ff4d4f;
  margin-left: 4rpx;
}
.optional.data-v-dc103050 {
  color: #999;
  font-weight: normal;
  font-size: 24rpx;
}
.form-input.data-v-dc103050 {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #d9d9d9;
  border-radius: 8rpx;
  padding: 0 16rpx;
  font-size: 28rpx;
  background: #fff;
  box-sizing: border-box;
}
.form-input.data-v-dc103050:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2rpx rgba(24, 144, 255, 0.2);
}
.form-textarea.data-v-dc103050 {
  width: 100%;
  min-height: 160rpx;
  border: 1rpx solid #d9d9d9;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 28rpx;
  background: #fff;
  box-sizing: border-box;
  line-height: 1.5;
}
.form-textarea.data-v-dc103050:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2rpx rgba(24, 144, 255, 0.2);
}
.char-count.data-v-dc103050 {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}
.image-upload-area.data-v-dc103050 {
  border: 2rpx dashed #d9d9d9;
  border-radius: 8rpx;
  padding: 24rpx;
  background: #fafafa;
}
.uploaded-images.data-v-dc103050 {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 24rpx;
}
.image-item.data-v-dc103050 {
  position: relative;
  width: 160rpx;
  height: 160rpx;
}
.uploaded-image.data-v-dc103050 {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
  -o-object-fit: cover;
     object-fit: cover;
}
.remove-image.data-v-dc103050 {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background: #ff4d4f;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
  cursor: pointer;
}
.upload-buttons.data-v-dc103050 {
  display: flex;
  gap: 16rpx;
}
.upload-btn.data-v-dc103050 {
  flex: 1;
  height: 80rpx;
  background: #fff;
  border: 1rpx solid #d9d9d9;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}
.upload-btn.data-v-dc103050:disabled {
  background: #f5f5f5;
  color: #ccc;
  border-color: #f0f0f0;
}
.upload-btn.data-v-dc103050:not(:disabled):active {
  background: #f0f0f0;
}
.submit-section.data-v-dc103050 {
  padding: 40rpx 24rpx;
  background: #fff;
  border-top: 1rpx solid #f0f0f0;
}
.submit-btn.data-v-dc103050 {
  width: 100%;
  height: 88rpx;
  background: #07C160;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}
.submit-btn.disabled.data-v-dc103050 {
  background: #d9d9d9;
  color: #fff;
}
.submit-btn.data-v-dc103050:not(.disabled):active {
  background: #06A050;
}