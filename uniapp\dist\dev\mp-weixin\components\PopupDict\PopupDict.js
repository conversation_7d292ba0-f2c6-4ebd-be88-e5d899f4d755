"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
const utils_http = require("../../utils/http.js");
if (!Array) {
  const _easycom_wd_select_picker2 = common_vendor.resolveComponent("wd-select-picker");
  _easycom_wd_select_picker2();
}
const _easycom_wd_select_picker = () => "../../node-modules/wot-design-uni/components/wd-select-picker/wd-select-picker.js";
if (!Math) {
  (_easycom_wd_select_picker + popupReportModal)();
}
const popupReportModal = () => "../Popup/components/popupReportModal.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "PopupDict",
  options: {
    styleIsolation: "shared"
  }
}), {
  __name: "PopupDict",
  props: {
    dictCode: {
      type: String,
      required: true,
      default: ""
    },
    modelValue: {
      type: String,
      default: ""
    },
    multi: {
      type: Boolean,
      default: false
    },
    spliter: {
      type: String,
      default: ","
    }
  },
  emits: ["change", "update:modelValue"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const toast = common_vendor.useToast();
    const showText = common_vendor.ref(props.multi ? [] : "");
    const options = common_vendor.ref([]);
    const cgRpConfigId = common_vendor.ref("");
    const code = common_vendor.ref(props.dictCode.split(",")[0]);
    const labelFiled = common_vendor.ref(props.dictCode.split(",")[1]);
    const valueFiled = common_vendor.ref(props.dictCode.split(",")[2]);
    const reportModal = common_vendor.reactive({
      show: false
    });
    const configUrl = common_vendor.reactive({
      getColumns: "/online/cgreport/api/getRpColumns/",
      getData: "/online/cgreport/api/getData/"
    });
    if (!code.value || !valueFiled.value || !labelFiled.value) {
      toast.error("popupDict参数未正确配置!");
    }
    common_vendor.watch(
      () => props.modelValue,
      (val) => {
        const callBack2 = () => {
          if (props.multi) {
            showText.value = val && val.length > 0 ? val.split(props.spliter) : [];
          } else {
            showText.value = val != null ? val : "";
          }
        };
        if (props.modelValue) {
          if (cgRpConfigId.value) {
            loadData({ callBack: callBack2 });
          } else {
            loadColumnsInfo({ callBack: callBack2 });
          }
        } else {
          callBack2();
        }
      },
      { immediate: true }
    );
    common_vendor.watch(
      () => showText.value,
      (val) => {
        let result;
        if (props.multi) {
          result = val.join(",");
        } else {
          result = val;
        }
        common_vendor.nextTick$1(() => {
          emit("change", result);
          emit("update:modelValue", result);
        });
      }
    );
    function loadColumnsInfo({ callBack: callBack2 }) {
      let url = `${configUrl.getColumns}${code.value}`;
      utils_http.http.get(url).then((res) => {
        if (res.success) {
          cgRpConfigId.value = res.result.cgRpConfigId;
          loadData({ callBack: callBack2 });
        }
      }).catch((err) => {
        callBack2 == null ? void 0 : callBack2();
      });
    }
    function loadData({ callBack: callBack2 }) {
      let url = `${configUrl.getData}${common_vendor.unref(cgRpConfigId)}`;
      utils_http.http.get(url, { ["force_" + valueFiled.value]: props.modelValue }).then((res) => {
        var _a;
        let data = res.result;
        if ((_a = data.records) == null ? void 0 : _a.length) {
          options.value = data.records.map((item) => {
            return { value: item[valueFiled.value], label: item[labelFiled.value] };
          });
        }
      }).finally(() => {
        callBack2 == null ? void 0 : callBack2();
      });
    }
    function callBack(rows) {
      const dataOptions = [];
      const dataValue = [];
      let result;
      rows.forEach((item) => {
        dataOptions.push({ value: item[valueFiled.value], label: item[labelFiled.value] });
        dataValue.push(item[valueFiled.value]);
      });
      options.value = dataOptions;
      if (props.multi) {
        showText.value = dataValue;
        result = dataValue.join(props.spliter);
      } else {
        showText.value = dataValue[0];
        result = dataValue[0];
      }
      common_vendor.nextTick$1(() => {
        emit("change", result);
        emit("update:modelValue", result);
      });
    }
    const handleClick = () => {
      reportModal.show = true;
    };
    const handleClose = () => {
      reportModal.show = false;
    };
    const handleChange = (data) => {
      console.log("选中的值：", data);
      callBack(data);
    };
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.o(() => common_vendor.unref(reportModal).show = true),
        b: common_vendor.o(($event) => showText.value = $event),
        c: common_vendor.p(__spreadProps(__spreadValues({
          columns: options.value,
          readonly: true,
          type: __props.multi ? "checkbox" : "radio"
        }, _ctx.$attrs), {
          modelValue: showText.value
        })),
        d: common_vendor.o(handleClick),
        e: common_vendor.unref(reportModal).show
      }, common_vendor.unref(reportModal).show ? {
        f: common_vendor.o(handleClose),
        g: common_vendor.o(handleChange),
        h: common_vendor.p({
          code: code.value,
          showFiled: labelFiled.value,
          multi: __props.multi
        })
      } : {});
    };
  }
}));
wx.createComponent(_sfc_main);
//# sourceMappingURL=PopupDict.js.map
