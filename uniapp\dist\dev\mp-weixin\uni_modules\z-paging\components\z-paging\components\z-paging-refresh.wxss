/* [z-paging]公用的静态css资源 */
.zp-line-loading-image.data-v-62605190 {

	animation: loading-flower-62605190 1s steps(12) infinite;

	color: #666666;
}
.zp-line-loading-image-rpx.data-v-62605190 {
	margin-right: 8rpx;
	width: 34rpx;
	height: 34rpx;
}
.zp-line-loading-image-px.data-v-62605190 {
	margin-right: 4px;
	width: 17px;
	height: 17px;
}
.zp-loading-image-ios-rpx.data-v-62605190 {
	width: 40rpx;
	height: 40rpx;
}
.zp-loading-image-ios-px.data-v-62605190 {
	width: 20px;
	height: 20px;
}
.zp-loading-image-android-rpx.data-v-62605190 {
	width: 34rpx;
	height: 34rpx;
}
.zp-loading-image-android-px.data-v-62605190 {
	width: 17px;
	height: 17px;
}
@keyframes loading-flower-62605190 {
0% {
		transform: rotate(0deg);
}
to {
		transform: rotate(1turn);
}
}
.zp-r-container.data-v-62605190 {

		display: flex;
		height: 100%;

		flex-direction: row;
		justify-content: center;
		align-items: center;
}
.zp-r-container-padding.data-v-62605190 {
}
.zp-r-left.data-v-62605190 {

		display: flex;

		flex-direction: row;
		align-items: center;
		overflow: hidden;
}
.zp-r-left-image.data-v-62605190 {
		transition-duration: .2s;
		transition-property: transform;
		color: #666666;
}
.zp-r-left-image-pre-size-rpx.data-v-62605190 {

		width: 34rpx;
		height: 34rpx;
		overflow: hidden;
}
.zp-r-left-image-pre-size-px.data-v-62605190 {

		width: 17px;
		height: 17px;
		overflow: hidden;
}
.zp-r-arrow-top.data-v-62605190 {
		transform: rotate(0deg);
}
.zp-r-arrow-down.data-v-62605190 {
		transform: rotate(180deg);
}
.zp-r-right.data-v-62605190 {

		display: flex;

		flex-direction: column;
		align-items: center;
		justify-content: center;
}
.zp-r-right-time-text-rpx.data-v-62605190 {
		margin-top: 10rpx;
		font-size: 26rpx;
}
.zp-r-right-time-text-px.data-v-62605190 {
		margin-top: 5px;
		font-size: 13px;
}
