{"version": 3, "file": "form.js", "sources": ["../../../../../src/pages-data/examinationReport/form.vue", "../../../../../uniPage:/cGFnZXMtZGF0YVxleGFtaW5hdGlvblJlcG9ydFxmb3JtLnZ1ZQ"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n    <PageLayout>\r\n        <template #navbar>\r\n            <NavBar :title=\"mode === 'add' ? '新增' : '编辑'\" :showBack=\"true\" />\r\n        </template>\r\n\r\n        <scroll-view class=\"page-scroll-view\" scroll-y=\"true\">\r\n            <view class=\"form-container\">\r\n                <view class=\"form-header\">\r\n                    <text class=\"form-title\">院外检查报告</text>\r\n                </view>\r\n\r\n                <!-- 报告类型选择区域 -->\r\n                <view class=\"form-section\" v-if=\"mode === 'add'\">\r\n                    <view class=\"form-item\">\r\n                        <view class=\"inline-form-item\">\r\n                            <!-- 报告类型选择器 -->\r\n                            <view class=\"inline-form-label\"><text class=\"required\">*</text>报告类型</view>\r\n                            <view class=\"inline-picker-wrapper\">\r\n                                <picker :disabled=\"isViewMode\" :value=\"selectedReportTypeIndex\"\r\n                                    :range=\"reportTypeOptions\" @change=\"onReportTypeChange\">\r\n                                    <view class=\"picker\">\r\n                                        <text :class=\"{ placeholder: !selectedReportType }\">{{ selectedReportType ||\r\n                                            '请选择报告类型'\r\n                                            }}</text>\r\n                                        <uni-icons type=\"bottom\" size=\"12\" color=\"#999999\" />\r\n                                    </view>\r\n                                </picker>\r\n                            </view>\r\n                            <button class=\"add-btn\" @click=\"addReportType\"\r\n                                :disabled=\"isViewMode || !canAddReportType\">添加</button>\r\n                        </view>\r\n\r\n                        <!-- 其他报告类型输入框\r\n                        <view class=\"inline-form-item\" v-if=\"selectedReportType === '其他'\">\r\n                            <view class=\"inline-form-label\">具体报告类型</view>\r\n                            <input type=\"text\" class=\"inline-form-input\" placeholder=\"请输入具体报告类型\"\r\n                                v-model=\"otherReportTypeName\" />\r\n                        </view> -->\r\n                    </view>\r\n                </view>\r\n\r\n                <!-- 已添加报告类型列表 -->\r\n                <view class=\"form-section\" v-if=\"formData.reportItems.length > 0\">\r\n                    <view class=\"report-list-header\">\r\n                        <text class=\"report-list-title\">已添加报告</text>\r\n                    </view>\r\n                    <view class=\"report-item\" v-for=\"(report, index) in formData.reportItems\" :key=\"index\">\r\n                        <view class=\"report-name\">\r\n                            <view class=\"report-type-picker\">\r\n                                <picker :disabled=\"isViewMode\" :range=\"reportTypeOptions\"\r\n                                    :value=\"reportTypeOptions.indexOf(report.type)\"\r\n                                    @change=\"(e) => onExistingReportTypeChange(e, index)\">\r\n                                    <view class=\"picker report-type-picker-inner\">\r\n                                        <text>{{ report.type }}</text>\r\n                                        <uni-icons type=\"bottom\" size=\"12\" color=\"#999999\" />\r\n                                    </view>\r\n                                </picker>\r\n                            </view>\r\n                            <text v-if=\"report.otherName\" class=\"report-other-name\">({{ report.otherName }})</text>\r\n\r\n                            <view class=\"date-picker-wrapper\">\r\n                                <picker mode=\"date\" :value=\"report.checkDate\" :disabled=\"isViewMode\"\r\n                                    @change=\"(e) => onDateChange(e, index)\">\r\n                                    <view class=\"date-picker\">\r\n                                        <text v-if=\"report.checkDate\">{{ report.checkDate }}</text>\r\n                                        <text v-else class=\"placeholder\">请选择检查日期</text>\r\n                                        <uni-icons type=\"calendar\" size=\"16\" color=\"#999\"></uni-icons>\r\n                                    </view>\r\n                                </picker>\r\n                            </view>\r\n\r\n                            <view class=\"delete-report-btn\" v-if=\"!isViewMode\" @click=\"removeReportType(index)\">\r\n                                <uni-icons type=\"trash\" size=\"18\" color=\"#FF0000\"></uni-icons>\r\n                            </view>\r\n                        </view>\r\n                        <view class=\"report-details\">\r\n                            <view class=\"inline-form-item\">\r\n                                <view class=\"inline-form-label\"><text class=\"required\">*</text>报告照片</view>\r\n                                <view class=\"image-upload-area\">\r\n                                    <view class=\"image-list\">\r\n                                        <view class=\"image-item\" v-for=\"(item, imgIndex) in report.images\"\r\n                                            :key=\"imgIndex\">\r\n                                            <image :src=\"item.fileUrl\" mode=\"aspectFill\"\r\n                                                @click=\"previewImage(index, imgIndex)\">\r\n                                            </image>\r\n                                            <view class=\"delete-btn\" @click.stop=\"deleteImage(index, imgIndex)\">\r\n                                                <uni-icons type=\"close\" size=\"16\" color=\"#fff\"></uni-icons>\r\n                                            </view>\r\n                                        </view>\r\n                                        <view class=\"upload-btn\" @click=\"chooseImage(index)\"\r\n                                            v-if=\"!isViewMode && report.images.length < 9\">\r\n                                            <uni-icons type=\"camera-filled\" size=\"24\" color=\"#999\"></uni-icons>\r\n                                            <text>上传照片</text>\r\n                                        </view>\r\n                                    </view>\r\n                                    <text class=\"tips\">最多可上传9张照片</text>\r\n                                </view>\r\n                            </view>\r\n\r\n                            <!-- 肾功能特殊字段 -->\r\n                            <view class=\"kidney-function-fields\" v-if=\"report.type === '肾功能'\">\r\n                                <view class=\"inline-form-item\">\r\n                                    <view class=\"inline-form-label\"><text class=\"required\"></text>血清肌酐</view>\r\n                                    <view class=\"value-unit-input\">\r\n                                        <view class=\"input-with-picker\">\r\n                                            <input type=\"number\" class=\"form-input\" v-model=\"report.bloodCreatinine\"\r\n                                                placeholder=\"请输入数值\" :disabled=\"isViewMode\" />\r\n                                            <picker class=\"unit-picker\"\r\n                                                :value=\"unitOptions.indexOf(report.creatinineUnit)\" :range=\"unitOptions\"\r\n                                                :disabled=\"isViewMode\"\r\n                                                @change=\"(e) => handleUnitChange(e, index, 'creatinineUnit')\">\r\n                                                <view class=\"picker-view\">\r\n                                                    <text style=\"color: #999\">{{ report.creatinineUnit || '单位' }}</text>\r\n                                                    <uni-icons type=\"bottom\" size=\"12\" color=\"#999999\" />\r\n                                                </view>\r\n                                            </picker>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                                <view class=\"inline-form-item\">\r\n                                    <view class=\"inline-form-label\"><text class=\"required\"></text>尿酸</view>\r\n                                    <view class=\"value-unit-input\">\r\n                                        <view class=\"input-with-picker\">\r\n                                            <input type=\"number\" class=\"form-input\" v-model=\"report.uricAcid\"\r\n                                                placeholder=\"请输入数值\" :disabled=\"isViewMode\" />\r\n                                            <picker class=\"unit-picker\"\r\n                                                :value=\"unitOptions.indexOf(report.uricAcidUnit)\" :range=\"unitOptions\"\r\n                                                :disabled=\"isViewMode\"\r\n                                                @change=\"(e) => handleUnitChange(e, index, 'uricAcidUnit')\">\r\n                                                <view class=\"picker-view\">\r\n                                                    <text style=\"color: #999\">{{ report.uricAcidUnit || '单位' }}</text>\r\n                                                    <uni-icons type=\"bottom\" size=\"12\" color=\"#999999\" />\r\n                                                </view>\r\n                                            </picker>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                                <view class=\"inline-form-item\">\r\n                                    <view class=\"inline-form-label\"><text class=\"required\"></text>肾小球过滤率</view>\r\n                                    <view class=\"value-unit-input\">\r\n                                        <view class=\"input-with-fixed-unit\">\r\n                                            <input type=\"number\" class=\"form-input\" v-model=\"report.gfr\"\r\n                                                placeholder=\"请输入数值\" :disabled=\"isViewMode\" />\r\n                                            <view class=\"fixed-unit\">\r\n                                                <text>ml/min</text>\r\n                                            </view>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                            </view> <!-- NT-proBNP特殊字段 -->\r\n                            <view class=\"kidney-function-fields\" v-if=\"report.type === 'NT-proBNP'\">\r\n                                <view class=\"inline-form-item\">\r\n                                    <view class=\"inline-form-label\"><text class=\"required\"></text>NT-proBNP</view>\r\n                                    <view class=\"value-unit-input\">\r\n                                        <view class=\"input-with-picker\">\r\n                                            <input type=\"number\" class=\"form-input\" v-model=\"report.ntProBnpValue\"\r\n                                                placeholder=\"请输入数值\" :disabled=\"isViewMode\" />\r\n                                            <picker class=\"unit-picker\"\r\n                                                :value=\"bnpUnitOptions.indexOf(report.ntProBnpUnit)\"\r\n                                                :range=\"bnpUnitOptions\" :disabled=\"isViewMode\"\r\n                                                @change=\"(e) => handleBnpUnitChange(e, index, 'ntProBnpUnit')\">\r\n                                                <view class=\"picker-view\">\r\n                                                    <text style=\"color: #999\">{{ report.ntProBnpUnit || '单位' }}</text>\r\n                                                    <uni-icons type=\"bottom\" size=\"12\" color=\"#999999\" />\r\n                                                </view>\r\n                                            </picker>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                            </view>\r\n\r\n                            <!-- BNP特殊字段 -->\r\n                            <view class=\"kidney-function-fields\" v-if=\"report.type === 'BNP'\">\r\n                                <view class=\"inline-form-item\">\r\n                                    <view class=\"inline-form-label\"><text class=\"required\"></text>BNP</view>\r\n                                    <view class=\"value-unit-input\">\r\n                                        <view class=\"input-with-picker\">\r\n                                            <input type=\"number\" class=\"form-input\" v-model=\"report.bnpValue\"\r\n                                                placeholder=\"请输入数值\" :disabled=\"isViewMode\" />\r\n                                            <picker class=\"unit-picker\" :value=\"bnpUnitOptions.indexOf(report.bnpUnit)\"\r\n                                                :range=\"bnpUnitOptions\" :disabled=\"isViewMode\"\r\n                                                @change=\"(e) => handleBnpUnitChange(e, index, 'bnpUnit')\">\r\n                                                <view class=\"picker-view\">\r\n                                                    <text style=\"color: #999\">{{ report.bnpUnit || '单位' }}</text>\r\n                                                    <uni-icons type=\"bottom\" size=\"12\" color=\"#999999\" />\r\n                                                </view>\r\n                                            </picker>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                            </view>\r\n\r\n                            <!-- 电解质特殊字段 -->\r\n                            <view class=\"kidney-function-fields\" v-if=\"report.type === '电解质'\">\r\n                                <view class=\"inline-form-item\">\r\n                                    <view class=\"inline-form-label\"><text class=\"required\"></text>血钠</view>\r\n                                    <view class=\"value-unit-input\">\r\n                                        <view class=\"input-with-picker\">\r\n                                            <input type=\"number\" class=\"form-input\" v-model=\"report.sodium\"\r\n                                                placeholder=\"请输入数值\" :disabled=\"isViewMode\" />\r\n                                            <picker class=\"unit-picker\"\r\n                                                :value=\"electrolyteUnitOptions.indexOf(report.sodiumUnit)\"\r\n                                                :range=\"electrolyteUnitOptions\" :disabled=\"isViewMode\"\r\n                                                @change=\"(e) => handleElectrolyteUnitChange(e, index, 'sodiumUnit')\">\r\n                                                <view class=\"picker-view\">\r\n                                                    <text style=\"color: #999\">{{ report.sodiumUnit || '单位' }}</text>\r\n                                                    <uni-icons type=\"bottom\" size=\"12\" color=\"#999999\" />\r\n                                                </view>\r\n                                            </picker>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                                <view class=\"inline-form-item\">\r\n                                    <view class=\"inline-form-label\"><text class=\"required\"></text>血钾</view>\r\n                                    <view class=\"value-unit-input\">\r\n                                        <view class=\"input-with-picker\">\r\n                                            <input type=\"number\" class=\"form-input\" v-model=\"report.potassium\"\r\n                                                placeholder=\"请输入数值\" :disabled=\"isViewMode\" />\r\n                                            <picker class=\"unit-picker\"\r\n                                                :value=\"electrolyteUnitOptions.indexOf(report.potassiumUnit)\"\r\n                                                :range=\"electrolyteUnitOptions\" :disabled=\"isViewMode\"\r\n                                                @change=\"(e) => handleElectrolyteUnitChange(e, index, 'potassiumUnit')\">\r\n                                                <view class=\"picker-view\">\r\n                                                    <text style=\"color: #999\">{{ report.potassiumUnit || '单位' }}</text>\r\n                                                    <uni-icons type=\"bottom\" size=\"12\" color=\"#999999\" />\r\n                                                </view>\r\n                                            </picker>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                            </view>\r\n                            <!-- 血常规特殊字段 -->\r\n                            <view class=\"kidney-function-fields\" v-if=\"report.type === '血常规'\">\r\n                                <view class=\"inline-form-item\">\r\n                                    <view class=\"inline-form-label\"><text class=\"required\"></text>血红蛋白</view>\r\n                                    <view class=\"value-unit-input\">\r\n                                        <view class=\"input-with-picker\">\r\n                                            <input type=\"number\" class=\"form-input\" v-model=\"report.hemoglobin\"\r\n                                                placeholder=\"请输入数值\" :disabled=\"isViewMode\" />\r\n                                            <picker class=\"unit-picker\"\r\n                                                :value=\"hemoglobinUnitOptions.indexOf(report.hemoglobinUnit)\"\r\n                                                :range=\"hemoglobinUnitOptions\" :disabled=\"isViewMode\"\r\n                                                @change=\"(e) => handleHemoglobinUnitChange(e, index, 'hemoglobinUnit')\">\r\n                                                <view class=\"picker-view\">\r\n                                                    <text style=\"color: #999\">{{ report.hemoglobinUnit || '单位' }}</text>\r\n                                                    <uni-icons type=\"bottom\" size=\"12\" color=\"#999999\" />\r\n                                                </view>\r\n                                            </picker>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                            </view>\r\n\r\n                            <!-- 血糖特殊字段 -->\r\n                            <view class=\"kidney-function-fields\" v-if=\"report.type === '血糖'\">\r\n                                <view class=\"inline-form-item\">\r\n                                    <view class=\"inline-form-label\"><text class=\"required\"></text>空腹血糖</view>\r\n                                    <view class=\"value-unit-input\">\r\n                                        <view class=\"input-with-picker\">\r\n                                            <input type=\"number\" class=\"form-input\" v-model=\"report.fastingGlucose\"\r\n                                                placeholder=\"请输入数值\" :disabled=\"isViewMode\" />\r\n                                            <picker class=\"unit-picker\"\r\n                                                :value=\"glucoseUnitOptions.indexOf(report.fastingGlucoseUnit)\"\r\n                                                :range=\"glucoseUnitOptions\" :disabled=\"isViewMode\"\r\n                                                @change=\"(e) => handleGlucoseUnitChange(e, index, 'fastingGlucoseUnit')\">\r\n                                                <view class=\"picker-view\">\r\n                                                    <text style=\"color: #999\">{{ report.fastingGlucoseUnit || '单位'\r\n                                                        }}</text>\r\n                                                    <uni-icons type=\"bottom\" size=\"12\" color=\"#999999\" />\r\n                                                </view>\r\n                                            </picker>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                                <view class=\"inline-form-item\">\r\n                                    <view class=\"inline-form-label\"><text class=\"required\"></text>糖化血红蛋白(HbA1c)</view>\r\n                                    <view class=\"value-unit-input\">\r\n                                        <view class=\"input-with-fixed-unit\">\r\n                                            <input type=\"number\" class=\"form-input\" v-model=\"report.hbA1c\"\r\n                                                placeholder=\"请输入数值\" :disabled=\"isViewMode\" />\r\n                                            <view class=\"fixed-unit\">\r\n                                                <text>%</text>\r\n                                            </view>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                            </view> <!-- 血脂特殊字段 -->\r\n                            <view class=\"kidney-function-fields\" v-if=\"report.type === '血脂'\">\r\n                                <view class=\"inline-form-item\">\r\n                                    <view class=\"inline-form-label\"><text class=\"required\"></text>总胆固醇</view>\r\n                                    <view class=\"value-unit-input\">\r\n                                        <view class=\"input-with-picker\">\r\n                                            <input type=\"number\" class=\"form-input\" v-model=\"report.totalCholesterol\"\r\n                                                placeholder=\"请输入数值\" :disabled=\"isViewMode\" />\r\n                                            <picker class=\"unit-picker\"\r\n                                                :value=\"lipidUnitOptions.indexOf(report.totalCholesterolUnit)\"\r\n                                                :range=\"lipidUnitOptions\" :disabled=\"isViewMode\"\r\n                                                @change=\"(e) => handleLipidUnitChange(e, index, 'totalCholesterolUnit')\">\r\n                                                <view class=\"picker-view\">\r\n                                                    <text style=\"color: #999\">{{ report.totalCholesterolUnit || '单位'\r\n                                                    }}</text>\r\n                                                    <uni-icons type=\"bottom\" size=\"12\" color=\"#999999\" />\r\n                                                </view>\r\n                                            </picker>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                                <view class=\"inline-form-item\">\r\n                                    <view class=\"inline-form-label\"><text class=\"required\"></text>低密度脂蛋白</view>\r\n                                    <view class=\"value-unit-input\">\r\n                                        <view class=\"input-with-picker\">\r\n                                            <input type=\"number\" class=\"form-input\" v-model=\"report.ldl\"\r\n                                                placeholder=\"请输入数值\" :disabled=\"isViewMode\" />\r\n                                            <picker class=\"unit-picker\"\r\n                                                :value=\"lipidUnitOptions.indexOf(report.ldlUnit)\"\r\n                                                :range=\"lipidUnitOptions\" :disabled=\"isViewMode\"\r\n                                                @change=\"(e) => handleLipidUnitChange(e, index, 'ldlUnit')\">\r\n                                                <view class=\"picker-view\">\r\n                                                    <text style=\"color: #999\">{{ report.ldlUnit || '单位' }}</text>\r\n                                                    <uni-icons type=\"bottom\" size=\"12\" color=\"#999999\" />\r\n                                                </view>\r\n                                            </picker>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                            </view>\r\n\r\n                            <!-- 肝功能特殊字段 -->\r\n                            <view class=\"kidney-function-fields\" v-if=\"report.type === '肝功能'\">\r\n                                <view class=\"inline-form-item\">\r\n                                    <view class=\"inline-form-label\"><text class=\"required\"></text>总胆红素</view>\r\n                                    <view class=\"value-unit-input\">\r\n                                        <view class=\"input-with-fixed-unit\">\r\n                                            <input type=\"number\" class=\"form-input\" v-model=\"report.totalBilirubin\"\r\n                                                placeholder=\"请输入数值\" :disabled=\"isViewMode\" />\r\n                                            <view class=\"fixed-unit\">\r\n                                                <text>μmol/L</text>\r\n                                            </view>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                                <view class=\"inline-form-item\">\r\n                                    <view class=\"inline-form-label\"><text class=\"required\"></text>直接胆红素</view>\r\n                                    <view class=\"value-unit-input\">\r\n                                        <view class=\"input-with-fixed-unit\">\r\n                                            <input type=\"number\" class=\"form-input\" v-model=\"report.directBilirubin\"\r\n                                                placeholder=\"请输入数值\" :disabled=\"isViewMode\" />\r\n                                            <view class=\"fixed-unit\">\r\n                                                <text>μmol/L</text>\r\n                                            </view>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                                <view class=\"inline-form-item\">\r\n                                    <view class=\"inline-form-label\"><text class=\"required\"></text>间接胆红素</view>\r\n                                    <view class=\"value-unit-input\">\r\n                                        <view class=\"input-with-fixed-unit\">\r\n                                            <input type=\"number\" class=\"form-input\" v-model=\"report.indirectBilirubin\"\r\n                                                placeholder=\"请输入数值\" :disabled=\"isViewMode\" />\r\n                                            <view class=\"fixed-unit\">\r\n                                                <text>μmol/L</text>\r\n                                            </view>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                                <view class=\"inline-form-item\">\r\n                                    <view class=\"inline-form-label\"><text class=\"required\"></text>丙氨酸氨基转氨酶</view>\r\n                                    <view class=\"value-unit-input\">\r\n                                        <view class=\"input-with-fixed-unit\">\r\n                                            <input type=\"number\" class=\"form-input\" v-model=\"report.alt\"\r\n                                                placeholder=\"请输入数值\" :disabled=\"isViewMode\" />\r\n                                            <view class=\"fixed-unit\">\r\n                                                <text>U/L</text>\r\n                                            </view>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                            </view>\r\n\r\n                            <!-- 心电图特殊字段 -->\r\n                            <view class=\"kidney-function-fields\" v-if=\"report.type === '心电图'\">\r\n                                <view class=\"inline-form-item\">\r\n                                    <view class=\"inline-form-label\"><text class=\"required\"></text>心率</view>\r\n                                    <view class=\"value-unit-input\">\r\n                                        <view class=\"input-with-fixed-unit\">\r\n                                            <input type=\"number\" class=\"form-input\" v-model=\"report.heartRate\"\r\n                                                placeholder=\"请输入数值\" :disabled=\"isViewMode\" />\r\n                                            <view class=\"fixed-unit\">\r\n                                                <text>次/分</text>\r\n                                            </view>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                                <view class=\"inline-form-item\">\r\n                                    <view class=\"inline-form-label\"><text class=\"required\"></text>心电图节律</view>\r\n                                    <view class=\"checkbox-group\">\r\n                                        <view class=\"checkbox-row\">\r\n                                            <view class=\"checkbox-item\" v-for=\"(item, i) in rhythmOptions\" :key=\"i\"\r\n                                                @click=\"!isViewMode && toggleRhythm(index, item)\">\r\n                                                <view class=\"checkbox-btn\"\r\n                                                    :class=\"{ 'checked': report.rhythms && report.rhythms.includes(item) }\">\r\n                                                </view>\r\n                                                <text>{{ item }}</text>\r\n                                            </view>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                                <view class=\"inline-form-item\">\r\n                                    <view class=\"inline-form-label\"><text class=\"required\"></text>左束支传导阻滞</view>\r\n                                    <view class=\"radio-group\">\r\n                                        <view class=\"radio-row\">\r\n                                            <view class=\"radio-item\" v-for=\"(item, i) in lbbbOptions\" :key=\"i\"\r\n                                                @click=\"!isViewMode && setLbbb(index, item)\">\r\n                                                <view class=\"radio-btn\" :class=\"{ 'checked': report.lbbb === item }\">\r\n                                                </view>\r\n                                                <text>{{ item }}</text>\r\n                                            </view>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                                <view class=\"inline-form-item\">\r\n                                    <view class=\"inline-form-label\"><text class=\"required\"></text>QRS时限</view>\r\n                                    <view class=\"value-unit-input\">\r\n                                        <view class=\"input-with-fixed-unit\">\r\n                                            <input type=\"number\" class=\"form-input\" v-model=\"report.qrsDuration\"\r\n                                                placeholder=\"请输入数值\" :disabled=\"isViewMode\" />\r\n                                            <view class=\"fixed-unit\">\r\n                                                <text>ms</text>\r\n                                            </view>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                            </view>\r\n\r\n                            <!-- 心脏彩超特殊字段 -->\r\n                            <view class=\"kidney-function-fields\" v-if=\"report.type === '心脏彩超'\">\r\n                                <view class=\"inline-form-item\">\r\n                                    <view class=\"inline-form-label\"><text class=\"required\"></text>射血分数</view>\r\n                                    <view class=\"value-unit-input\">\r\n                                        <view class=\"input-with-fixed-unit\">\r\n                                            <input type=\"number\" class=\"form-input\" v-model=\"report.ejectionFraction\"\r\n                                                placeholder=\"请输入数值\" :disabled=\"isViewMode\" />\r\n                                            <view class=\"fixed-unit\">\r\n                                                <text>%</text>\r\n                                            </view>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                                <view class=\"inline-form-item\">\r\n                                    <view class=\"inline-form-label\"><text class=\"required\"></text>左心室舒张末径</view>\r\n                                    <view class=\"value-unit-input\">\r\n                                        <view class=\"input-with-fixed-unit\">\r\n                                            <input type=\"number\" class=\"form-input\"\r\n                                                v-model=\"report.leftVentricularEndDiastolicDiameter\" placeholder=\"请输入数值\"\r\n                                                :disabled=\"isViewMode\" />\r\n                                            <view class=\"fixed-unit\">\r\n                                                <text>mm</text>\r\n                                            </view>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                                <view class=\"inline-form-item\">\r\n                                    <view class=\"inline-form-label\"><text class=\"required\"></text>左房扩大</view>\r\n                                    <view class=\"radio-group\">\r\n                                        <view class=\"radio-row\">\r\n                                            <view class=\"radio-item\" v-for=\"(item, i) in yesNoOptions\" :key=\"i\"\r\n                                                @click=\"!isViewMode && setLeftAtriumEnlarged(index, item)\">\r\n                                                <view class=\"radio-btn\"\r\n                                                    :class=\"{ 'checked': report.leftAtriumEnlarged === item }\">\r\n                                                </view>\r\n                                                <text>{{ item }}</text>\r\n                                            </view>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                                <view class=\"inline-form-item\">\r\n                                    <view class=\"inline-form-label\"><text class=\"required\"></text>左心室肥厚</view>\r\n                                    <view class=\"radio-group\">\r\n                                        <view class=\"radio-row\">\r\n                                            <view class=\"radio-item\" v-for=\"(item, i) in yesNoOptions\" :key=\"i\"\r\n                                                @click=\"!isViewMode && setLeftVentricularHypertrophy(index, item)\">\r\n                                                <view class=\"radio-btn\"\r\n                                                    :class=\"{ 'checked': report.leftVentricularHypertrophy === item }\">\r\n                                                </view>\r\n                                                <text>{{ item }}</text>\r\n                                            </view>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                                <view class=\"inline-form-item\">\r\n                                    <view class=\"inline-form-label\"><text class=\"required\"></text>室间隔厚度(IVSd)</view>\r\n                                    <view class=\"value-unit-input\">\r\n                                        <view class=\"input-with-fixed-unit\">\r\n                                            <input type=\"number\" class=\"form-input\" v-model=\"report.septalThickness\"\r\n                                                placeholder=\"请输入数值\" :disabled=\"isViewMode\" />\r\n                                            <view class=\"fixed-unit\">\r\n                                                <text>mm</text>\r\n                                            </view>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                                <view class=\"inline-form-item\">\r\n                                    <view class=\"inline-form-label\"><text class=\"required\"></text>左室舒张功能下降</view>\r\n                                    <view class=\"radio-group\">\r\n                                        <view class=\"radio-row\">\r\n                                            <view class=\"radio-item\" v-for=\"(item, i) in yesNoOptions\" :key=\"i\"\r\n                                                @click=\"!isViewMode && setLeftVentricularDiastolicDysfunction(index, item)\">\r\n                                                <view class=\"radio-btn\"\r\n                                                    :class=\"{ 'checked': report.leftVentricularDiastolicDysfunction === item }\">\r\n                                                </view>\r\n                                                <text>{{ item }}</text>\r\n                                            </view>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                            </view>\r\n\r\n                            <view class=\"inline-form-item\">\r\n                                <view class=\"inline-form-label\">备注</view>\r\n                                <textarea class=\"form-textarea\" v-model=\"report.remark\" placeholder=\"请输入备注信息\"\r\n                                    :disabled=\"isViewMode\"\r\n                                    style=\"width: 100%; margin-bottom: 10rpx; background-color: #F5F5F5; border: 1px solid #E0E7F1; border-radius: 8rpx; padding: 20rpx; font-size: 28rpx; box-sizing: border-box; min-height: 180rpx;\"></textarea>\r\n                            </view>\r\n                        </view>\r\n                    </view>\r\n                </view>\r\n\r\n                <!-- 提交按钮，仅在新增模式显示 -->\r\n                <view class=\"submit-section\" v-if=\"!isViewMode\">\r\n                    <button class=\"submit-btn\" @click=\"submitForm\">{{ mode === 'add' ? '提交' : '保存' }}</button>\r\n                </view>\r\n            </view>\r\n        </scroll-view>\r\n    </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { ref, computed, onMounted, watch } from 'vue'\r\nimport { fastLerp } from 'zrender/lib/tool/color'\r\nimport { useUserStore } from '@/store/user'\r\n\r\nconst userStore = useUserStore()\r\n\r\ndefineOptions({\r\n    name: 'examinationReportForm',\r\n})\r\n\r\n// 存储待删除的报告ID列表\r\nconst deleteIds = ref<string[]>([])\r\n\r\n// 获取页面参数\r\nconst query = ref<any>({})\r\nonMounted(() => {\r\n    const pages = getCurrentPages()\r\n    const currentPage = pages[pages.length - 1]\r\n    query.value = (currentPage as any).options || {}\r\n\r\n    // 如果是查看/编辑模式，加载数据\r\n    if (query.value.mode === 'view' && query.value.idList) {\r\n        loadFormData(query.value.idList)\r\n    }\r\n})\r\n\r\n// 表单模式：add 或 view(编辑模式)\r\nconst mode = computed(() => query.value.mode || 'add')\r\n// 在编辑模式下允许编辑\r\nconst isViewMode = computed(() => false)\r\n\r\n// 报告类型选项\r\nconst reportTypeOptions = [\r\n    '血常规',\r\n    '血糖',\r\n    '血脂',\r\n    '肝功能',\r\n    '肾功能',\r\n    'NT-proBNP',\r\n    'BNP',\r\n    '电解质',\r\n    '心电图',\r\n    '心脏彩超',\r\n    // '其他'\r\n]\r\n\r\n// 肾功能单位选项\r\nconst unitOptions = ['mg/dL', 'μmol/L']\r\n// BNP单位选项\r\nconst bnpUnitOptions = ['pg/mL', 'pmol/L']\r\n// 电解质单位选项\r\nconst electrolyteUnitOptions = ['mEq/L', 'mmol/L']\r\n// 血常规单位选项\r\nconst hemoglobinUnitOptions = ['g/dL', 'g/L']\r\n// 血糖单位选项\r\nconst glucoseUnitOptions = ['mg/dL', 'mmol/L']\r\n// 血脂单位选项\r\nconst lipidUnitOptions = ['mg/dL', 'mmol/L']\r\n// 心电图节律选项\r\nconst rhythmOptions = ['窦性心律', '心房颤动/扑动', '起搏心律', '室性早搏']\r\n// 左束支传导阻滞选项\r\nconst lbbbOptions = ['有', '无']\r\n// 是/否选项\r\nconst yesNoOptions = ['是', '否']\r\n\r\n// 选中的报告类型\r\nconst selectedReportTypeIndex = ref(0)\r\nconst selectedReportType = ref('')\r\nconst otherReportTypeName = ref('')\r\n\r\n// 表单数据\r\nconst formData = ref({\r\n    reportItems: [] as {\r\n        id: string; // 添加id字段，用于编辑模式\r\n        type: string;\r\n        otherName?: string;\r\n        checkDate: string;\r\n        images: { fileName: string; fileUrl: string; }[];\r\n        remark: string;\r\n        // 肾功能字段\r\n        bloodCreatinine?: number;\r\n        creatinineUnit?: string;\r\n        uricAcid?: number;\r\n        uricAcidUnit?: string;\r\n        gfr?: number;\r\n        // BNP相关字段\r\n        bnpValue?: number;\r\n        bnpUnit?: string;\r\n        ntProBnpValue?: number;\r\n        ntProBnpUnit?: string;\r\n        // 电解质相关字段\r\n        sodium?: number;\r\n        sodiumUnit?: string;\r\n        potassium?: number;\r\n        potassiumUnit?: string;\r\n        // 血常规相关字段\r\n        hemoglobin?: number;\r\n        hemoglobinUnit?: string;\r\n        // 血糖相关字段\r\n        fastingGlucose?: number;\r\n        fastingGlucoseUnit?: string;\r\n        hbA1c?: number;\r\n        // 血脂相关字段\r\n        totalCholesterol?: number;\r\n        totalCholesterolUnit?: string;\r\n        ldl?: number;\r\n        ldlUnit?: string;\r\n        // 肝功能相关字段\r\n        totalBilirubin?: number; // 总胆红素\r\n        directBilirubin?: number; // 直接胆红素\r\n        indirectBilirubin?: number; // 间接胆红素\r\n        alt?: number; // 丙氨酸氨基转氨酶        // 心电图相关字段\r\n        heartRate?: number; // 心率\r\n        rhythms?: string[]; // 心电图节律\r\n        lbbb?: string; // 左束支传导阻滞\r\n        qrsDuration?: number; // QRS时限\r\n        // 心脏彩超相关字段\r\n        ejectionFraction?: number; // 射血分数\r\n        leftVentricularEndDiastolicDiameter?: number; // 左心室舒张末径        \r\n        leftAtriumEnlarged?: string; // 左房扩大\r\n        leftVentricularHypertrophy?: string; // 左心室肥厚\r\n        septalThickness?: number; // 室间隔厚度\r\n        leftVentricularDiastolicDysfunction?: string; // 左室舒张功能下降\r\n    }[]\r\n})\r\n\r\n// 判断是否可以添加当前选择的报告类型\r\nconst canAddReportType = computed(() => {\r\n    // 如果没有选择报告类型，则不能添加\r\n    if (!selectedReportType.value) return false\r\n\r\n    // 如果是\"其他\"，需要填写具体名称\r\n    if (selectedReportType.value === '其他') {\r\n        return otherReportTypeName.value.trim() !== ''\r\n    }\r\n\r\n    // 其他报告类型不能重复添加\r\n    return !formData.value.reportItems.some(item => item.type === selectedReportType.value)\r\n})\r\n\r\n// 报告类型选择变化\r\nconst onReportTypeChange = (e: any) => {\r\n    const index = e.detail.value\r\n    selectedReportTypeIndex.value = index\r\n    selectedReportType.value = reportTypeOptions[index]\r\n\r\n    // 如果不是\"其他\"，清空具体报告类型名称\r\n    if (selectedReportType.value !== '其他') {\r\n        otherReportTypeName.value = ''\r\n    }\r\n}\r\n\r\n// 添加报告类型\r\nconst addReportType = () => {\r\n    if (!canAddReportType.value) return;\r\n    const newReportItem = {\r\n        type: selectedReportType.value,\r\n        checkDate: '',\r\n        images: [] as { fileName: string; fileUrl: string; }[],\r\n        remark: '',\r\n        // 特殊字段初始化\r\n        bloodCreatinine: undefined,\r\n        creatinineUnit: undefined,\r\n        uricAcid: undefined,\r\n        uricAcidUnit: undefined,\r\n        gfr: undefined,\r\n        bnpValue: undefined,\r\n        bnpUnit: undefined,\r\n        ntProBnpValue: undefined,\r\n        ntProBnpUnit: undefined,\r\n        sodium: undefined,\r\n        sodiumUnit: undefined,\r\n        potassium: undefined,\r\n        potassiumUnit: undefined,\r\n        hemoglobin: undefined,\r\n        hemoglobinUnit: undefined,\r\n        fastingGlucose: undefined,\r\n        fastingGlucoseUnit: undefined,\r\n        hbA1c: undefined,\r\n        // 血脂字段初始化\r\n        totalCholesterol: undefined,\r\n        totalCholesterolUnit: undefined,\r\n        ldl: undefined,\r\n        ldlUnit: undefined,\r\n        // 肝功能字段初始化\r\n        totalBilirubin: undefined,\r\n        directBilirubin: undefined,\r\n        indirectBilirubin: undefined,\r\n        alt: undefined,\r\n        // 心电图字段初始化\r\n        heartRate: undefined,\r\n        lbbb: undefined,\r\n        qrsDuration: undefined,\r\n        // 心脏彩超字段初始化\r\n        ejectionFraction: undefined,\r\n        leftVentricularEndDiastolicDiameter: undefined,\r\n        leftAtriumEnlarged: undefined,\r\n        leftVentricularHypertrophy: undefined,\r\n        septalThickness: undefined,\r\n        leftVentricularDiastolicDysfunction: undefined,\r\n    } as any\r\n\r\n    // 只有心电图类型才添加rhythms字段\r\n    if (selectedReportType.value === '心电图') {\r\n        newReportItem.rhythms = []\r\n    }\r\n\r\n    // 如果是\"其他\"，添加具体报告类型名称\r\n    if (selectedReportType.value === '其他') {\r\n        newReportItem.otherName = otherReportTypeName.value\r\n    }\r\n\r\n    formData.value.reportItems.push(newReportItem)\r\n\r\n    // 重置选择\r\n    if (selectedReportType.value !== '其他') {\r\n        selectedReportType.value = ''\r\n        selectedReportTypeIndex.value = 0\r\n    } else {\r\n        // 对于\"其他\"，仅清空具体报告类型名称，保持选择\"其他\"\r\n        otherReportTypeName.value = ''\r\n    }\r\n}\r\n\r\n// 修改已添加报告的类型\r\nconst onExistingReportTypeChange = (e: any, index: number) => {\r\n    const typeIndex = e.detail.value\r\n    const newType = reportTypeOptions[typeIndex]\r\n    const oldType = formData.value.reportItems[index].type\r\n\r\n    // 检查是否有重复的报告类型\r\n    const isDuplicate = formData.value.reportItems.some((item, i) =>\r\n        i !== index && item.type === newType\r\n    )\r\n\r\n    if (isDuplicate) {\r\n        uni.showToast({\r\n            title: '该报告类型已存在',\r\n            icon: 'none'\r\n        })\r\n        return\r\n    }\r\n\r\n    // 更新报告类型\r\n    formData.value.reportItems[index].type = newType\r\n\r\n    // 重置相关特殊字段\r\n    if (oldType !== newType) {\r\n        // 重置旧类型的特殊字段\r\n        switch (oldType) {\r\n            case '肾功能':\r\n                formData.value.reportItems[index].bloodCreatinine = undefined;\r\n                formData.value.reportItems[index].creatinineUnit = undefined;\r\n                formData.value.reportItems[index].uricAcid = undefined;\r\n                formData.value.reportItems[index].uricAcidUnit = undefined;\r\n                formData.value.reportItems[index].gfr = undefined;\r\n                break;\r\n            case 'NT-proBNP':\r\n                formData.value.reportItems[index].ntProBnpValue = undefined;\r\n                formData.value.reportItems[index].ntProBnpUnit = undefined;\r\n                break;\r\n            case 'BNP':\r\n                formData.value.reportItems[index].bnpValue = undefined;\r\n                formData.value.reportItems[index].bnpUnit = undefined;\r\n                break;\r\n            case '电解质':\r\n                formData.value.reportItems[index].sodium = undefined;\r\n                formData.value.reportItems[index].sodiumUnit = undefined;\r\n                formData.value.reportItems[index].potassium = undefined;\r\n                formData.value.reportItems[index].potassiumUnit = undefined;\r\n                break;\r\n            case '血常规':\r\n                formData.value.reportItems[index].hemoglobin = undefined;\r\n                formData.value.reportItems[index].hemoglobinUnit = undefined;\r\n                break;\r\n            case '血糖':\r\n                formData.value.reportItems[index].fastingGlucose = undefined;\r\n                formData.value.reportItems[index].fastingGlucoseUnit = undefined;\r\n                formData.value.reportItems[index].hbA1c = undefined;\r\n                break;\r\n            case '血脂': formData.value.reportItems[index].totalCholesterol = undefined;\r\n                formData.value.reportItems[index].totalCholesterolUnit = undefined;\r\n                formData.value.reportItems[index].ldl = undefined;\r\n                formData.value.reportItems[index].ldlUnit = undefined;\r\n                break;\r\n            case '肝功能':\r\n                formData.value.reportItems[index].totalBilirubin = undefined;\r\n                formData.value.reportItems[index].directBilirubin = undefined;\r\n                formData.value.reportItems[index].indirectBilirubin = undefined;\r\n                formData.value.reportItems[index].alt = undefined;\r\n                break;\r\n            case '心电图':\r\n                formData.value.reportItems[index].heartRate = undefined;\r\n                formData.value.reportItems[index].rhythms = undefined;\r\n                formData.value.reportItems[index].lbbb = undefined;\r\n                formData.value.reportItems[index].qrsDuration = undefined;\r\n                break;\r\n            case '心脏彩超':\r\n                formData.value.reportItems[index].ejectionFraction = undefined;\r\n                formData.value.reportItems[index].leftVentricularEndDiastolicDiameter = undefined;\r\n                formData.value.reportItems[index].leftAtriumEnlarged = undefined;\r\n                formData.value.reportItems[index].leftVentricularHypertrophy = undefined;\r\n                formData.value.reportItems[index].septalThickness = undefined;\r\n                formData.value.reportItems[index].leftVentricularDiastolicDysfunction = undefined;\r\n                break;\r\n        }\r\n    }\r\n}\r\n\r\n// 移除报告类型\r\nconst removeReportType = (index: number) => {\r\n    // 如果是编辑模式且报告有ID，则将ID添加到待删除列表\r\n    const reportId = formData.value.reportItems[index].id\r\n    if (mode.value === 'view' && reportId) {\r\n        deleteIds.value.push(reportId)\r\n    }\r\n    // 从数据列表中移除\r\n    formData.value.reportItems.splice(index, 1)\r\n}\r\n\r\n// 日期变化处理\r\nconst onDateChange = (e: any, index: number) => {\r\n    console.log('日期选择事件:', e)\r\n    if (e && e.detail && e.detail.value) {\r\n        formData.value.reportItems[index].checkDate = e.detail.value\r\n    }\r\n}\r\n\r\n// 选择图片\r\nconst chooseImage = (index: number) => {\r\n    const reportItem = formData.value.reportItems[index]\r\n    uni.chooseImage({\r\n        count: 9 - reportItem.images.length,\r\n        sizeType: ['compressed'],\r\n        sourceType: ['album', 'camera'],\r\n        success: (res) => {\r\n            // 显示上传中\r\n            uni.showLoading({\r\n                title: '上传中...'\r\n            }); const uploadPromises = (res.tempFilePaths as string[]).map(tempPath => {\r\n                return new Promise<{ fileName: string, fileUrl: string }>((resolve, reject) => {\r\n                    uni.uploadFile({\r\n                        url: `${import.meta.env.VITE_SERVER_BASEURL}/file/uploadreport`,\r\n                        filePath: tempPath,\r\n                        name: 'file',\r\n                        header: {\r\n                            'X-Access-Token': userStore.userInfo.token\r\n                        },\r\n                        success: (uploadRes) => {\r\n                            try {\r\n                                const data = JSON.parse(uploadRes.data);\r\n                                if (data.success) {\r\n                                    resolve({\r\n                                        fileName: data.result.fileName,\r\n                                        fileUrl: data.result.fileUrl\r\n                                    });\r\n                                    console.log('图片上传成功:', data.result.fileName, data.result.fileUrl);\r\n                                } else {\r\n                                    reject(new Error(data.message || '上传失败'));\r\n                                }\r\n                            } catch (err) {\r\n                                reject(new Error('解析上传响应失败'));\r\n                            }\r\n                        },\r\n                        fail: (err) => {\r\n                            reject(err);\r\n                        }\r\n                    });\r\n                });\r\n            }); Promise.all(uploadPromises)\r\n                .then(imageInfos => {\r\n                    // 所有图片上传成功，添加到图片数组\r\n                    reportItem.images = [...reportItem.images, ...imageInfos];\r\n                    uni.hideLoading();\r\n                })\r\n                .catch(err => {\r\n                    uni.hideLoading();\r\n                    uni.showToast({\r\n                        title: '图片上传失败: ' + err.message,\r\n                        icon: 'none',\r\n                        duration: 2000\r\n                    });\r\n                });\r\n        }\r\n    });\r\n}\r\n\r\n// 预览图片\r\nconst previewImage = (reportIndex: number, imageIndex: number) => {\r\n    const reportItem = formData.value.reportItems[reportIndex]\r\n    // 提取所有图片的fileUrl用于预览\r\n    const imageUrls = reportItem.images.map(img => img.fileUrl)\r\n    uni.previewImage({\r\n        current: imageUrls[imageIndex],  // 使用URL字符串而不是索引\r\n        urls: imageUrls\r\n    })\r\n}\r\n\r\n// 删除图片\r\nconst deleteImage = (reportIndex: number, imageIndex: number) => {\r\n    if (isViewMode.value) return\r\n    formData.value.reportItems[reportIndex].images.splice(imageIndex, 1)\r\n}\r\n\r\n// 加载表单数据（根据ID获取数据）\r\nconst loadFormData = (id: string) => {\r\n    // 显示加载中\r\n    uni.showLoading({\r\n        title: '加载中...'\r\n    })\r\n\r\n    // 调用接口获取数据\r\n    uni.request({\r\n        url: `${import.meta.env.VITE_SERVER_BASEURL}/patient/getReportList`,\r\n        method: 'POST',\r\n        data: {\r\n            idList: id.split(',').filter(id => id)\r\n        },\r\n        success: (res: any) => {\r\n            console.log('获取检查报告数据:', res.data)\r\n\r\n            if (res.data && res.data.code === 200 && res.data.result) {\r\n                // 获取记录数组，后端返回结构是 result.records\r\n                const reportData = res.data.result.records || [];\r\n\r\n                // 清空现有数据\r\n                formData.value.reportItems = []\r\n\r\n                // 处理返回的报告数据\r\n                if (Array.isArray(reportData)) {\r\n                    reportData.forEach((item: any) => {\r\n                        // 优先使用imageUrlList作为图片数据源\r\n                        const imageUrls = item.imageUrlList || [];\r\n\r\n                        // 如果没有imageUrlList，尝试使用images字段\r\n                        const images = !imageUrls.length && item.images ?\r\n                            (typeof item.images === 'string' ?\r\n                                JSON.parse(item.images) : item.images) : [];\r\n\r\n                        // 格式化图片数据确保包含fileName和fileUrl\r\n                        const formattedImages = imageUrls.length ? imageUrls.map((url: string) => {\r\n                            // 从URL中提取文件名（如果需要）\r\n                            const fileName = url.split('/').pop()?.split('?')[0] || '';\r\n                            return {\r\n                                fileName: fileName,\r\n                                fileUrl: url\r\n                            };\r\n                        }) : images.map((img: any) => {\r\n                            if (typeof img === 'string') {\r\n                                return {\r\n                                    fileName: img,\r\n                                    fileUrl: `${import.meta.env.VITE_SERVER_BASEURL}/file/view/${img}`\r\n                                };\r\n                            }\r\n                            return {\r\n                                fileName: img.fileName || img.name || '',\r\n                                fileUrl: img.fileUrl || img.url || `${import.meta.env.VITE_SERVER_BASEURL}/file/view/${img.fileName || img.name || ''}`\r\n                            };\r\n                        });\r\n\r\n                        // 创建报告对象，确保保留原始ID用于编辑\r\n                        const reportItem: any = {\r\n                            id: item.id,  // 保存ID用于后续编辑操作\r\n                            type: item.type,\r\n                            checkDate: item.checkDate || '',\r\n                            images: formattedImages,\r\n                            remark: item.remark || ''\r\n                        };\r\n\r\n                        // 根据报告类型添加特定字段\r\n                        if (item.type === '肾功能') {\r\n                            reportItem.bloodCreatinine = item.bloodCreatinine;\r\n                            reportItem.creatinineUnit = item.creatinineUnit;\r\n                            reportItem.uricAcid = item.uricAcid;\r\n                            reportItem.uricAcidUnit = item.uricAcidUnit;\r\n                            reportItem.gfr = item.gfr;\r\n                        } else if (item.type === 'NT-proBNP') {\r\n                            reportItem.ntProBnpValue = item.ntProBnpValue;\r\n                            reportItem.ntProBnpUnit = item.ntProBnpUnit;\r\n                        } else if (item.type === 'BNP') {\r\n                            reportItem.bnpValue = item.bnpValue;\r\n                            reportItem.bnpUnit = item.bnpUnit;\r\n                        } else if (item.type === '电解质') {\r\n                            reportItem.sodium = item.sodium;\r\n                            reportItem.sodiumUnit = item.sodiumUnit;\r\n                            reportItem.potassium = item.potassium;\r\n                            reportItem.potassiumUnit = item.potassiumUnit;\r\n                        } else if (item.type === '血常规') {\r\n                            reportItem.hemoglobin = item.hemoglobin;\r\n                            reportItem.hemoglobinUnit = item.hemoglobinUnit;\r\n                        } else if (item.type === '血糖') {\r\n                            reportItem.fastingGlucose = item.fastingGlucose;\r\n                            reportItem.fastingGlucoseUnit = item.fastingGlucoseUnit;\r\n                            reportItem.hbA1c = item.hbA1c;\r\n                        } else if (item.type === '血脂') {\r\n                            reportItem.totalCholesterol = item.totalCholesterol;\r\n                            reportItem.totalCholesterolUnit = item.totalCholesterolUnit;\r\n                            reportItem.ldl = item.ldl;\r\n                            reportItem.ldlUnit = item.ldlUnit;\r\n                        } else if (item.type === '肝功能') {\r\n                            reportItem.totalBilirubin = item.totalBilirubin;\r\n                            reportItem.directBilirubin = item.directBilirubin;\r\n                            reportItem.indirectBilirubin = item.indirectBilirubin;\r\n                            reportItem.alt = item.alt;\r\n                        } else if (item.type === '心电图') {\r\n                            reportItem.heartRate = item.heartRate;\r\n                            reportItem.rhythms = item.rhythms ?\r\n                                (typeof item.rhythms === 'string' ? item.rhythms.split(',') : item.rhythms) : [];\r\n                            reportItem.lbbb = item.lbbb;\r\n                            reportItem.qrsDuration = item.qrsDuration;\r\n                        } else if (item.type === '心脏彩超') {\r\n                            reportItem.ejectionFraction = item.ejectionFraction;\r\n                            reportItem.leftVentricularEndDiastolicDiameter = item.leftVentricularEndDiastolicDiameter;\r\n                            reportItem.leftAtriumEnlarged = item.leftAtriumEnlarged;\r\n                            reportItem.leftVentricularHypertrophy = item.leftVentricularHypertrophy;\r\n                            reportItem.septalThickness = item.septalThickness;\r\n                            reportItem.leftVentricularDiastolicDysfunction = item.leftVentricularDiastolicDysfunction;\r\n                        }\r\n\r\n                        formData.value.reportItems.push(reportItem);\r\n                    });\r\n                }\r\n\r\n                console.log('处理后的表单数据:', formData.value);\r\n            } else {\r\n                uni.showToast({\r\n                    title: res.data?.message || '获取数据失败',\r\n                    icon: 'none'\r\n                })\r\n            }\r\n        },\r\n        fail: (err) => {\r\n            console.error('获取数据失败:', err)\r\n            uni.showToast({\r\n                title: '网络异常，请稍后重试',\r\n                icon: 'none'\r\n            })\r\n        },\r\n        complete: () => {\r\n            uni.hideLoading()\r\n        }\r\n    })\r\n}\r\n\r\n// 提交表单\r\nconst submitForm = () => {\r\n    console.log('提交表单数据:', formData.value)\r\n\r\n    // 表单验证\r\n    if (formData.value.reportItems.length === 0) {\r\n        uni.showToast({\r\n            title: '请至少添加一种报告类型',\r\n            icon: 'none'\r\n        })\r\n        return\r\n    }\r\n\r\n    // 检查是否每个报告类型都有日期和图片\r\n    for (let i = 0; i < formData.value.reportItems.length; i++) {\r\n        const report = formData.value.reportItems[i];\r\n\r\n        if (!report.checkDate) {\r\n            uni.showToast({\r\n                title: `请选择${report.type}的检查日期`,\r\n                icon: 'none'\r\n            })\r\n            return\r\n        }\r\n\r\n        if (report.images.length === 0) {\r\n            uni.showToast({\r\n                title: `请上传${report.type}的至少一张报告照片`,\r\n                icon: 'none'\r\n            })\r\n            return\r\n        }\r\n\r\n        // 各种特殊字段验证\r\n        // 如果是肾功能类型报告，检查血清肌酐和尿酸的单位选择\r\n        if (report.type === '肾功能') {\r\n            // 检查血清肌酐\r\n            if (report.bloodCreatinine !== undefined && report.bloodCreatinine !== null && report.bloodCreatinine.toString() !== '') {\r\n                if (!report.creatinineUnit) {\r\n                    uni.showToast({\r\n                        title: `请选择血清肌酐的单位`,\r\n                        icon: 'none'\r\n                    })\r\n                    return\r\n                }\r\n            }\r\n\r\n            // 检查尿酸\r\n            if (report.uricAcid !== undefined && report.uricAcid !== null && report.uricAcid.toString() !== '') {\r\n                if (!report.uricAcidUnit) {\r\n                    uni.showToast({\r\n                        title: `请选择尿酸的单位`,\r\n                        icon: 'none'\r\n                    })\r\n                    return\r\n                }\r\n            }\r\n        }\r\n\r\n        // 如果是血糖类型报告，检查空腹血糖和糖化血红蛋白的单位选择\r\n        if (report.type === '血糖') {            // 检查空腹血糖\r\n            if (report.fastingGlucose !== undefined && report.fastingGlucose !== null && report.fastingGlucose.toString() !== '') {\r\n                if (!report.fastingGlucoseUnit) {\r\n                    uni.showToast({\r\n                        title: `请选择空腹血糖的单位`,\r\n                        icon: 'none'\r\n                    })\r\n                    return\r\n                }\r\n            }\r\n\r\n            // 糖化血红蛋白单位固定为%，不需要验证\r\n        }\r\n\r\n        // 如果是血脂类型报告，检查总胆固醇和低密度脂蛋白的单位选择\r\n        if (report.type === '血脂') {\r\n            // 检查总胆固醇\r\n            if (report.totalCholesterol !== undefined && report.totalCholesterol !== null && report.totalCholesterol.toString() !== '') {\r\n                if (!report.totalCholesterolUnit) {\r\n                    uni.showToast({\r\n                        title: `请选择总胆固醇的单位`,\r\n                        icon: 'none'\r\n                    })\r\n                    return\r\n                }\r\n            }\r\n\r\n            // 检查低密度脂蛋白\r\n            if (report.ldl !== undefined && report.ldl !== null && report.ldl.toString() !== '') {\r\n                if (!report.ldlUnit) {\r\n                    uni.showToast({\r\n                        title: `请选择低密度脂蛋白的单位`,\r\n                        icon: 'none'\r\n                    })\r\n                    return\r\n                }\r\n            }\r\n        }\r\n\r\n        // 如果是BNP类型报告，检查BNP的单位选择\r\n        if (report.type === 'BNP') {\r\n            // 检查BNP\r\n            if (report.bnpValue !== undefined && report.bnpValue !== null && report.bnpValue.toString() !== '') {\r\n                if (!report.bnpUnit) {\r\n                    uni.showToast({\r\n                        title: `请选择BNP的单位`,\r\n                        icon: 'none'\r\n                    })\r\n                    return\r\n                }\r\n            }\r\n        }\r\n\r\n        // 如果是NT-proBNP类型报告，检查NT-proBNP的单位选择\r\n        if (report.type === 'NT-proBNP') {\r\n            // 检查NT-proBNP\r\n            if (report.ntProBnpValue !== undefined && report.ntProBnpValue !== null && report.ntProBnpValue.toString() !== '') {\r\n                if (!report.ntProBnpUnit) {\r\n                    uni.showToast({\r\n                        title: `请选择NT-proBNP的单位`,\r\n                        icon: 'none'\r\n                    })\r\n                    return\r\n                }\r\n            }\r\n        }\r\n\r\n        // 如果是电解质类型报告，检查血钠和血钾的单位选择\r\n        if (report.type === '电解质') {\r\n            // 检查血钠\r\n            if (report.sodium !== undefined && report.sodium !== null && report.sodium.toString() !== '') {\r\n                if (!report.sodiumUnit) {\r\n                    uni.showToast({\r\n                        title: `请选择血钠的单位`,\r\n                        icon: 'none'\r\n                    })\r\n                    return\r\n                }\r\n            }\r\n\r\n            // 检查血钾\r\n            if (report.potassium !== undefined && report.potassium !== null && report.potassium.toString() !== '') {\r\n                if (!report.potassiumUnit) {\r\n                    uni.showToast({\r\n                        title: `请选择血钾的单位`,\r\n                        icon: 'none'\r\n                    })\r\n                    return\r\n                }\r\n            }\r\n        }\r\n\r\n        // 如果是血常规类型报告，检查血红蛋白的单位选择\r\n        if (report.type === '血常规') {\r\n            // 检查血红蛋白\r\n            if (report.hemoglobin !== undefined && report.hemoglobin !== null && report.hemoglobin.toString() !== '') {\r\n                if (!report.hemoglobinUnit) {\r\n                    uni.showToast({\r\n                        title: `请选择血红蛋白的单位`,\r\n                        icon: 'none'\r\n                    })\r\n                    return\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    // 显示加载中\r\n    uni.showLoading({\r\n        title: '提交中...'\r\n    })    // 转换数据，保证提交时只发送fileName而非整个对象\r\n    const processedReportItems = formData.value.reportItems.map(report => {\r\n        // 创建一个深拷贝\r\n        const newReport = JSON.parse(JSON.stringify(report));\r\n        // 替换images数组，只包含fileName\r\n        newReport.images = report.images.map(img => img.fileName);\r\n        // 保留id字段，用于编辑模式\r\n        if (mode.value === 'view') {\r\n            newReport.id = report.id;\r\n        }\r\n        return newReport;\r\n    });\r\n\r\n    // 判断是新增还是编辑模式\r\n    const isEdit = mode.value === 'view';\r\n\r\n    // 构建请求数据\r\n    const requestData: any = {\r\n        reportItems: processedReportItems,\r\n        userId: userStore.userInfo.userid\r\n    };\r\n\r\n    // 在编辑模式下，添加updateUserId参数\r\n    if (isEdit) {\r\n        requestData.updateUserId = userStore.userInfo.userid;\r\n    }\r\n\r\n    const apiUrl = `${import.meta.env.VITE_SERVER_BASEURL}/patient/savereport`;\r\n\r\n    uni.request({\r\n        url: apiUrl,\r\n        method: 'POST',\r\n        data: requestData,\r\n        success: (res: any) => {\r\n            // 如果是编辑模式且有待删除的报告ID，则调用删除接口\r\n            if (isEdit && deleteIds.value.length > 0) {\r\n                deleteReports();\r\n            } else {\r\n                uni.hideLoading();\r\n\r\n                if (res.data?.success) {\r\n                    uni.showModal({\r\n                        title: `${isEdit ? '保存' : '提交'}成功`,\r\n                        showCancel: false,\r\n                        success: () => {\r\n                            uni.navigateBack();\r\n                        }\r\n                    });\r\n                } else {\r\n                    const errorMsg = res.data?.message || `${isEdit ? '保存' : '提交'}失败，未知错误`;\r\n                    uni.showModal({\r\n                        title: `${isEdit ? '保存' : '提交'}失败`,\r\n                        content: errorMsg,\r\n                        showCancel: false\r\n                    });\r\n                }\r\n            }\r\n        },\r\n        fail: (err) => {\r\n            uni.hideLoading();\r\n            const errorMsg = err.errMsg || '网络错误，请稍后重试';\r\n            uni.showModal({\r\n                title: `${isEdit ? '保存' : '提交'}失败`,\r\n                content: errorMsg,\r\n                showCancel: false\r\n            });\r\n        }\r\n    })\r\n}\r\n\r\n// 删除选中的报告\r\nconst deleteReports = () => {\r\n    // 如果没有待删除的报告ID，直接返回\r\n    if (deleteIds.value.length === 0) {\r\n        uni.hideLoading();\r\n        uni.showModal({\r\n            title: '保存成功',\r\n            showCancel: false,\r\n            success: () => {\r\n                uni.navigateBack();\r\n            }\r\n        });\r\n        return;\r\n    }\r\n\r\n    // 调用删除接口\r\n    uni.request({\r\n        url: `${import.meta.env.VITE_SERVER_BASEURL}/patient/deletereport`,\r\n        method: 'POST',\r\n        data: {\r\n            deleteId: deleteIds.value,\r\n        },\r\n        success: (res) => {\r\n            uni.hideLoading();\r\n\r\n            if (res.data?.success) {\r\n                uni.showModal({\r\n                    title: '保存成功',\r\n                    showCancel: false,\r\n                    success: () => {\r\n                        uni.navigateBack();\r\n                    }\r\n                });\r\n            } else {\r\n                const errorMsg = res.data?.message || '删除报告失败，未知错误';\r\n                uni.showModal({\r\n                    title: '保存部分成功',\r\n                    content: '新报告已保存，但删除部分报告失败：' + errorMsg,\r\n                    showCancel: false,\r\n                    success: () => {\r\n                        uni.navigateBack();\r\n                    }\r\n                });\r\n            }\r\n        },\r\n        fail: (err) => {\r\n            uni.hideLoading();\r\n            const errorMsg = err.errMsg || '网络错误，请稍后重试';\r\n            uni.showModal({\r\n                title: '保存部分成功',\r\n                content: '新报告已保存，但删除部分报告失败：' + errorMsg,\r\n                showCancel: false,\r\n                success: () => {\r\n                    uni.navigateBack();\r\n                }\r\n            });\r\n        }\r\n    });\r\n}\r\n\r\n// 处理单位变化\r\nconst handleUnitChange = (e: any, index: number, field: string) => {\r\n    if (e && e.detail && e.detail.value !== undefined) {\r\n        const selectedIndex = parseInt(e.detail.value);\r\n        formData.value.reportItems[index][field] = unitOptions[selectedIndex];\r\n    }\r\n}\r\n\r\n// 处理BNP和NT-proBNP单位变化\r\nconst handleBnpUnitChange = (e: any, index: number, field: string) => {\r\n    if (e && e.detail && e.detail.value !== undefined) {\r\n        const selectedIndex = parseInt(e.detail.value);\r\n        formData.value.reportItems[index][field] = bnpUnitOptions[selectedIndex];\r\n    }\r\n}\r\n\r\n// 处理电解质单位变化\r\nconst handleElectrolyteUnitChange = (e: any, index: number, field: string) => {\r\n    if (e && e.detail && e.detail.value !== undefined) {\r\n        const selectedIndex = parseInt(e.detail.value);\r\n        formData.value.reportItems[index][field] = electrolyteUnitOptions[selectedIndex];\r\n    }\r\n}\r\n\r\n// 处理血红蛋白单位变化\r\nconst handleHemoglobinUnitChange = (e: any, index: number, field: string) => {\r\n    if (e && e.detail && e.detail.value !== undefined) {\r\n        const selectedIndex = parseInt(e.detail.value);\r\n        formData.value.reportItems[index][field] = hemoglobinUnitOptions[selectedIndex];\r\n    }\r\n}\r\n\r\n// 处理血糖单位变化\r\nconst handleGlucoseUnitChange = (e: any, index: number, field: string) => {\r\n    if (e && e.detail && e.detail.value !== undefined) {\r\n        const selectedIndex = parseInt(e.detail.value);\r\n        formData.value.reportItems[index][field] = glucoseUnitOptions[selectedIndex];\r\n    }\r\n}\r\n\r\n// 处理血脂单位变化\r\nconst handleLipidUnitChange = (e: any, index: number, field: string) => {\r\n    if (e && e.detail && e.detail.value !== undefined) {\r\n        const selectedIndex = parseInt(e.detail.value);\r\n        formData.value.reportItems[index][field] = lipidUnitOptions[selectedIndex];\r\n    }\r\n}\r\n\r\n// 处理心电图节律多选变化\r\nconst toggleRhythm = (index: number, rhythmValue: string) => {\r\n    const report = formData.value.reportItems[index];\r\n\r\n    // 只有心电图类型的报告才应该处理rhythms\r\n    if (report.type !== '心电图') return;\r\n\r\n    // 确保rhythms数组已初始化\r\n    if (!report.rhythms) {\r\n        report.rhythms = [];\r\n    }\r\n\r\n    // 判断是否已选中\r\n    const existingIndex = report.rhythms.indexOf(rhythmValue);\r\n    if (existingIndex !== -1) {\r\n        // 已选中，取消选择\r\n        report.rhythms.splice(existingIndex, 1);\r\n    } else {\r\n        // 未选中，添加选择\r\n        report.rhythms.push(rhythmValue);\r\n    }\r\n}\r\n\r\n// 设置左束支传导阻滞\r\nconst setLbbb = (index: number, value: string) => {\r\n    formData.value.reportItems[index].lbbb = value\r\n}\r\n\r\n// 设置左房扩大\r\nconst setLeftAtriumEnlarged = (index: number, value: string) => {\r\n    formData.value.reportItems[index].leftAtriumEnlarged = value\r\n}\r\n\r\n// 设置左心室肥厚\r\nconst setLeftVentricularHypertrophy = (index: number, value: string) => {\r\n    formData.value.reportItems[index].leftVentricularHypertrophy = value\r\n}\r\n\r\n// 设置左室舒张功能下降\r\nconst setLeftVentricularDiastolicDysfunction = (index: number, value: string) => {\r\n    formData.value.reportItems[index].leftVentricularDiastolicDysfunction = value\r\n}\r\n\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-scroll-view {\r\n    height: calc(100vh - 44px);\r\n    /* 减去导航栏高度 */\r\n    width: 100%;\r\n}\r\n\r\n.form-container {\r\n    padding: 20rpx;\r\n    padding-bottom: 120rpx;\r\n    /* 增加底部内边距，防止内容被遮挡 */\r\n\r\n    .form-header {\r\n        margin-bottom: 20rpx;\r\n        text-align: center;\r\n        /* 标题居中 */\r\n\r\n        .form-title {\r\n            font-size: 32rpx;\r\n            font-weight: bold;\r\n            color: #333;\r\n        }\r\n    }\r\n\r\n    .form-section {\r\n        background-color: #FFFFFF;\r\n        border-radius: 12rpx;\r\n        padding: 20rpx;\r\n        margin-bottom: 20rpx;\r\n\r\n        .form-item {\r\n            margin-bottom: 30rpx;\r\n\r\n            .form-label {\r\n                display: block;\r\n                font-size: 28rpx;\r\n                color: #333;\r\n                margin-bottom: 20rpx;\r\n            }\r\n\r\n            .inline-form-item {\r\n                display: flex;\r\n                align-items: center;\r\n                margin-bottom: 20rpx;\r\n\r\n                .inline-form-label {\r\n                    font-size: 28rpx;\r\n                    color: #333;\r\n                    min-width: 160rpx;\r\n                }\r\n\r\n                .inline-form-input {\r\n                    flex: 1;\r\n                    background-color: #F5F5F5;\r\n                    /* 灰色背景 */\r\n                    border-radius: 8rpx;\r\n                    padding: 20rpx;\r\n                    font-size: 28rpx;\r\n                    color: #333;\r\n                    box-sizing: border-box;\r\n                    min-height: 80rpx;\r\n\r\n                    &:disabled {\r\n                        background-color: #F5F5F5;\r\n                        color: #666;\r\n                    }\r\n                }\r\n            }\r\n\r\n            .date-picker-wrapper {\r\n                width: 100%;\r\n            }\r\n\r\n            .date-picker {\r\n                display: flex;\r\n                justify-content: space-between;\r\n                align-items: center;\r\n                background-color: #F5F5F5;\r\n                /* 灰色背景 */\r\n                border-radius: 8rpx;\r\n                padding: 20rpx;\r\n                font-size: 28rpx;\r\n                color: #333;\r\n                box-sizing: border-box;\r\n                min-height: 80rpx;\r\n                width: 100%;\r\n                border: 1px solid #E0E7F1;\r\n                /* 添加边框 */\r\n            }\r\n        }\r\n    }\r\n\r\n    // 图片上传区域样式\r\n    .image-upload-area {\r\n        width: 100%;\r\n\r\n        .image-list {\r\n            display: flex;\r\n            flex-wrap: wrap;\r\n            gap: 20rpx;\r\n            margin-bottom: 10rpx;\r\n\r\n            .image-item {\r\n                width: 160rpx;\r\n                height: 160rpx;\r\n                border-radius: 8rpx;\r\n                overflow: hidden;\r\n                position: relative;\r\n\r\n                image {\r\n                    width: 100%;\r\n                    height: 100%;\r\n                    object-fit: cover;\r\n                }\r\n\r\n                .delete-btn {\r\n                    position: absolute;\r\n                    right: 0;\r\n                    top: 0;\r\n                    width: 36rpx;\r\n                    height: 36rpx;\r\n                    background-color: rgba(0, 0, 0, 0.5);\r\n                    border-radius: 0 0 0 8rpx;\r\n                    display: flex;\r\n                    justify-content: center;\r\n                    align-items: center;\r\n                }\r\n            }\r\n\r\n            .upload-btn {\r\n                width: 160rpx;\r\n                height: 160rpx;\r\n                background-color: #F7F7F7;\r\n                border-radius: 8rpx;\r\n                border: 1rpx dashed #CCCCCC;\r\n                display: flex;\r\n                flex-direction: column;\r\n                justify-content: center;\r\n                align-items: center;\r\n\r\n                text {\r\n                    font-size: 24rpx;\r\n                    color: #999;\r\n                    margin-top: 10rpx;\r\n                }\r\n            }\r\n        }\r\n\r\n        .tips {\r\n            font-size: 24rpx;\r\n            color: #999;\r\n        }\r\n    }\r\n\r\n    // 行内选择器样式\r\n    .inline-picker-wrapper {\r\n        flex: 1;\r\n        width: 100%;\r\n    }\r\n\r\n    .required {\r\n        color: #FF0000;\r\n        margin-right: 4rpx;\r\n    }\r\n\r\n    .submit-section {\r\n        margin-top: 40rpx;\r\n        margin-bottom: 60rpx;\r\n        /* 增加底部间距 */\r\n\r\n        .submit-btn {\r\n            width: 100%;\r\n            background-color: #07C160;\r\n            color: #FFFFFF;\r\n            border-radius: 8rpx;\r\n            font-size: 32rpx;\r\n            padding: 20rpx 0;\r\n        }\r\n    }\r\n}\r\n\r\n/* 添加药物选择相关样式 */\r\n.add-btn {\r\n    background-color: #07C160;\r\n    color: #FFFFFF;\r\n    font-size: 28rpx;\r\n    padding: 15rpx 25rpx;\r\n    border-radius: 8rpx;\r\n    margin-left: 20rpx;\r\n\r\n    &:disabled {\r\n        background-color: #cccccc;\r\n    }\r\n}\r\n\r\n.picker {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    background-color: #F5F5F5;\r\n    /* 灰色背景 */\r\n    border-radius: 8rpx;\r\n    padding: 20rpx;\r\n    font-size: 28rpx;\r\n    color: #999;\r\n    box-sizing: border-box;\r\n    min-height: 80rpx;\r\n    border: 1px solid #E0E7F1;\r\n    /* 添加边框 */\r\n\r\n}\r\n\r\n.report-list-header {\r\n    margin-bottom: 20rpx;\r\n\r\n    .report-list-title {\r\n        font-size: 30rpx;\r\n        font-weight: bold;\r\n        color: #333;\r\n    }\r\n}\r\n\r\n.report-item {\r\n    background-color: #F7F7F7;\r\n    border-radius: 12rpx;\r\n    padding: 20rpx;\r\n    margin-bottom: 30rpx;\r\n    position: relative;\r\n    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\r\n\r\n    .report-name {\r\n        display: flex;\r\n        align-items: center;\r\n        font-size: 32rpx;\r\n        font-weight: bold;\r\n        color: #333;\r\n        margin-bottom: 20rpx;\r\n        padding-bottom: 15rpx;\r\n        border-bottom: 1rpx solid #eaeaea;\r\n\r\n        .report-type-picker {\r\n            flex: 0 0 auto;\r\n        }\r\n\r\n        .report-type-picker-inner {\r\n            background-color: #f5f5f5;\r\n            padding: 10rpx 20rpx;\r\n            min-width: 180rpx;\r\n            border: 1px solid #E0E7F1;\r\n        }\r\n\r\n        .report-other-name {\r\n            font-weight: normal;\r\n            font-size: 28rpx;\r\n            color: #666;\r\n            margin-left: 10rpx;\r\n        }\r\n\r\n        .date-picker-wrapper {\r\n            margin-left: auto;\r\n            width: 280rpx;\r\n        }\r\n\r\n        .date-picker {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n            background-color: #F5F5F5;\r\n            /* 灰色背景 */\r\n            border-radius: 8rpx;\r\n            padding: 20rpx;\r\n            font-size: 28rpx;\r\n            color: #333;\r\n            min-height: 60rpx;\r\n            width: 100%;\r\n            border: 1px solid #eaeaea;\r\n\r\n            .placeholder {\r\n                color: #999;\r\n            }\r\n        }\r\n\r\n        .delete-report-btn {\r\n            margin-left: 15rpx;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            padding: 5rpx;\r\n        }\r\n    }\r\n\r\n    .report-details {\r\n        display: flex;\r\n        flex-direction: column;\r\n        background-color: #fff;\r\n        border-radius: 8rpx;\r\n        padding: 15rpx;\r\n\r\n        .inline-form-item {\r\n            margin-bottom: 25rpx;\r\n\r\n            &:last-child {\r\n                margin-bottom: 0;\r\n            }\r\n\r\n            .inline-form-label {\r\n                margin-bottom: 12rpx;\r\n                font-weight: 500;\r\n                color: #2C2C2C;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.kidney-function-fields {\r\n    margin-bottom: 20rpx;\r\n\r\n    .value-unit-input {\r\n        display: flex;\r\n        flex-direction: column;\r\n        margin-bottom: 20rpx;\r\n\r\n        .input-with-picker {\r\n            display: flex;\r\n            align-items: center;\r\n            width: 100%;\r\n        }\r\n\r\n        .form-input {\r\n            flex: 1;\r\n            margin-right: 10rpx;\r\n            background-color: #F5F5F5;\r\n            border: 1px solid #E0E7F1;\r\n            border-radius: 8rpx;\r\n            padding: 20rpx;\r\n            font-size: 28rpx;\r\n            box-sizing: border-box;\r\n            min-height: 80rpx;\r\n        }\r\n\r\n        .input-with-fixed-unit {\r\n            display: flex;\r\n            align-items: center;\r\n            width: 100%;\r\n\r\n            .form-input {\r\n                flex: 1;\r\n                background-color: #F5F5F5;\r\n                border: 1px solid #E0E7F1;\r\n                border-radius: 8rpx;\r\n                padding: 20rpx;\r\n                font-size: 28rpx;\r\n                box-sizing: border-box;\r\n                min-height: 80rpx;\r\n                border-right: none;\r\n            }\r\n\r\n            .fixed-unit {\r\n                width: 180rpx;\r\n                /* 与 unit-picker 相同的宽度 */\r\n                height: 80rpx;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                background-color: #F5F5F5;\r\n                border: 1px solid #E0E7F1;\r\n                border-left: none;\r\n                border-radius: 8rpx;\r\n                font-size: 28rpx;\r\n                color: #666;\r\n                padding: 0 20rpx;\r\n                box-sizing: border-box;\r\n            }\r\n        }\r\n\r\n        .unit-picker {\r\n            width: 180rpx;\r\n        }\r\n\r\n        .picker-view {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n            background-color: #F5F5F5;\r\n            border: 1px solid #E0E7F1;\r\n            border-radius: 8rpx;\r\n            padding: 0 20rpx;\r\n            font-size: 28rpx;\r\n            box-sizing: border-box;\r\n            min-height: 80rpx;\r\n        }\r\n\r\n        .unit-selector {\r\n            display: flex;\r\n            flex-wrap: wrap;\r\n\r\n            radio-group {\r\n                display: flex;\r\n                flex-wrap: wrap;\r\n                width: 100%;\r\n            }\r\n\r\n            label {\r\n                margin-right: 20rpx;\r\n                display: flex;\r\n                align-items: center;\r\n                font-size: 28rpx;\r\n                margin-bottom: 10rpx;\r\n\r\n                radio {\r\n                    margin-right: 4rpx;\r\n                    transform: scale(0.8);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    // 心电图相关样式\r\n    .radio-group {\r\n        display: flex;\r\n        flex-direction: column;\r\n        margin-top: 10rpx;\r\n\r\n        .radio-row {\r\n            display: flex;\r\n            gap: 50rpx;\r\n            margin-left: 20rpx;\r\n            justify-content: flex-start;\r\n\r\n            .radio-item {\r\n                display: flex;\r\n                align-items: center;\r\n                min-width: 100rpx;\r\n\r\n                .radio-btn {\r\n                    width: 36rpx;\r\n                    height: 36rpx;\r\n                    border-radius: 50%;\r\n                    border: 2rpx solid #CCCCCC;\r\n                    margin-right: 10rpx;\r\n                    position: relative;\r\n\r\n                    &.checked {\r\n                        border-color: #07C160;\r\n\r\n                        &:after {\r\n                            content: '';\r\n                            position: absolute;\r\n                            width: 24rpx;\r\n                            height: 24rpx;\r\n                            background-color: #07C160;\r\n                            border-radius: 50%;\r\n                            top: 50%;\r\n                            left: 50%;\r\n                            transform: translate(-50%, -50%);\r\n                        }\r\n                    }\r\n                }\r\n\r\n                text {\r\n                    font-size: 28rpx;\r\n                    color: #333;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .checkbox-group {\r\n        display: flex;\r\n        flex-direction: column;\r\n        margin-top: 10rpx;\r\n\r\n        .checkbox-row {\r\n            display: flex;\r\n            flex-wrap: wrap;\r\n            gap: 30rpx;\r\n            margin-left: 20rpx;\r\n            justify-content: flex-start;\r\n\r\n            .checkbox-item {\r\n                display: flex;\r\n                align-items: center;\r\n                min-width: 180rpx;\r\n                margin-bottom: 20rpx;\r\n\r\n                .checkbox-btn {\r\n                    width: 36rpx;\r\n                    height: 36rpx;\r\n                    border-radius: 4rpx;\r\n                    border: 2rpx solid #CCCCCC;\r\n                    margin-right: 10rpx;\r\n                    position: relative;\r\n\r\n                    &.checked {\r\n                        border-color: #07C160;\r\n                        background-color: #07C160;\r\n\r\n                        &:after {\r\n                            content: '';\r\n                            position: absolute;\r\n                            width: 20rpx;\r\n                            height: 10rpx;\r\n                            border-left: 4rpx solid #FFFFFF;\r\n                            border-bottom: 4rpx solid #FFFFFF;\r\n                            top: 45%;\r\n                            left: 50%;\r\n                            transform: translate(-50%, -50%) rotate(-45deg);\r\n                        }\r\n                    }\r\n                }\r\n\r\n                text {\r\n                    font-size: 28rpx;\r\n                    color: #333;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    // 添加心电图相关样式\r\n    .rhythm-container {\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n\r\n        .inline-form-label {\r\n            margin-bottom: 15rpx;\r\n        }\r\n    }\r\n\r\n    /* 最外层样式结束 */\r\n}\r\n</style>", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-data/examinationReport/form.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "ref", "onMounted", "computed", "uni", "id", "_a"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkiBA,UAAM,YAAYA,WAAAA,aAAa;AAOzB,UAAA,YAAYC,cAAc,IAAA,EAAE;AAG5B,UAAA,QAAQA,cAAS,IAAA,EAAE;AACzBC,kBAAAA,UAAU,MAAM;AACZ,YAAM,QAAQ,gBAAgB;AAC9B,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AACpC,YAAA,QAAS,YAAoB,WAAW,CAAC;AAG/C,UAAI,MAAM,MAAM,SAAS,UAAU,MAAM,MAAM,QAAQ;AACtC,qBAAA,MAAM,MAAM,MAAM;AAAA,MAAA;AAAA,IACnC,CACH;AAGD,UAAM,OAAOC,cAAAA,SAAS,MAAM,MAAM,MAAM,QAAQ,KAAK;AAE/C,UAAA,aAAaA,uBAAS,MAAM,KAAK;AAGvC,UAAM,oBAAoB;AAAA,MACtB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,IAEJ;AAGM,UAAA,cAAc,CAAC,SAAS,QAAQ;AAEhC,UAAA,iBAAiB,CAAC,SAAS,QAAQ;AAEnC,UAAA,yBAAyB,CAAC,SAAS,QAAQ;AAE3C,UAAA,wBAAwB,CAAC,QAAQ,KAAK;AAEtC,UAAA,qBAAqB,CAAC,SAAS,QAAQ;AAEvC,UAAA,mBAAmB,CAAC,SAAS,QAAQ;AAE3C,UAAM,gBAAgB,CAAC,QAAQ,WAAW,QAAQ,MAAM;AAElD,UAAA,cAAc,CAAC,KAAK,GAAG;AAEvB,UAAA,eAAe,CAAC,KAAK,GAAG;AAGxB,UAAA,0BAA0BF,kBAAI,CAAC;AAC/B,UAAA,qBAAqBA,kBAAI,EAAE;AAC3B,UAAA,sBAAsBA,kBAAI,EAAE;AAGlC,UAAM,WAAWA,cAAAA,IAAI;AAAA,MACjB,aAAa,CAAA;AAAA,IAAC,CAoDjB;AAGK,UAAA,mBAAmBE,cAAAA,SAAS,MAAM;AAEpC,UAAI,CAAC,mBAAmB;AAAc,eAAA;AAGlC,UAAA,mBAAmB,UAAU,MAAM;AAC5B,eAAA,oBAAoB,MAAM,KAAA,MAAW;AAAA,MAAA;AAIzC,aAAA,CAAC,SAAS,MAAM,YAAY,KAAK,CAAQ,SAAA,KAAK,SAAS,mBAAmB,KAAK;AAAA,IAAA,CACzF;AAGK,UAAA,qBAAqB,CAAC,MAAW;AAC7B,YAAA,QAAQ,EAAE,OAAO;AACvB,8BAAwB,QAAQ;AACb,yBAAA,QAAQ,kBAAkB,KAAK;AAG9C,UAAA,mBAAmB,UAAU,MAAM;AACnC,4BAAoB,QAAQ;AAAA,MAAA;AAAA,IAEpC;AAGA,UAAM,gBAAgB,MAAM;AACxB,UAAI,CAAC,iBAAiB;AAAO;AAC7B,YAAM,gBAAgB;AAAA,QAClB,MAAM,mBAAmB;AAAA,QACzB,WAAW;AAAA,QACX,QAAQ,CAAC;AAAA,QACT,QAAQ;AAAA;AAAA,QAER,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,UAAU;AAAA,QACV,cAAc;AAAA,QACd,KAAK;AAAA,QACL,UAAU;AAAA,QACV,SAAS;AAAA,QACT,eAAe;AAAA,QACf,cAAc;AAAA,QACd,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,QAChB,oBAAoB;AAAA,QACpB,OAAO;AAAA;AAAA,QAEP,kBAAkB;AAAA,QAClB,sBAAsB;AAAA,QACtB,KAAK;AAAA,QACL,SAAS;AAAA;AAAA,QAET,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,mBAAmB;AAAA,QACnB,KAAK;AAAA;AAAA,QAEL,WAAW;AAAA,QACX,MAAM;AAAA,QACN,aAAa;AAAA;AAAA,QAEb,kBAAkB;AAAA,QAClB,qCAAqC;AAAA,QACrC,oBAAoB;AAAA,QACpB,4BAA4B;AAAA,QAC5B,iBAAiB;AAAA,QACjB,qCAAqC;AAAA,MACzC;AAGI,UAAA,mBAAmB,UAAU,OAAO;AACpC,sBAAc,UAAU,CAAC;AAAA,MAAA;AAIzB,UAAA,mBAAmB,UAAU,MAAM;AACnC,sBAAc,YAAY,oBAAoB;AAAA,MAAA;AAGzC,eAAA,MAAM,YAAY,KAAK,aAAa;AAGzC,UAAA,mBAAmB,UAAU,MAAM;AACnC,2BAAmB,QAAQ;AAC3B,gCAAwB,QAAQ;AAAA,MAAA,OAC7B;AAEH,4BAAoB,QAAQ;AAAA,MAAA;AAAA,IAEpC;AAGM,UAAA,6BAA6B,CAAC,GAAQ,UAAkB;AACpD,YAAA,YAAY,EAAE,OAAO;AACrB,YAAA,UAAU,kBAAkB,SAAS;AAC3C,YAAM,UAAU,SAAS,MAAM,YAAY,KAAK,EAAE;AAG5C,YAAA,cAAc,SAAS,MAAM,YAAY;AAAA,QAAK,CAAC,MAAM,MACvD,MAAM,SAAS,KAAK,SAAS;AAAA,MACjC;AAEA,UAAI,aAAa;AACbC,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACT;AACD;AAAA,MAAA;AAIJ,eAAS,MAAM,YAAY,KAAK,EAAE,OAAO;AAGzC,UAAI,YAAY,SAAS;AAErB,gBAAQ,SAAS;AAAA,UACb,KAAK;AACD,qBAAS,MAAM,YAAY,KAAK,EAAE,kBAAkB;AACpD,qBAAS,MAAM,YAAY,KAAK,EAAE,iBAAiB;AACnD,qBAAS,MAAM,YAAY,KAAK,EAAE,WAAW;AAC7C,qBAAS,MAAM,YAAY,KAAK,EAAE,eAAe;AACjD,qBAAS,MAAM,YAAY,KAAK,EAAE,MAAM;AACxC;AAAA,UACJ,KAAK;AACD,qBAAS,MAAM,YAAY,KAAK,EAAE,gBAAgB;AAClD,qBAAS,MAAM,YAAY,KAAK,EAAE,eAAe;AACjD;AAAA,UACJ,KAAK;AACD,qBAAS,MAAM,YAAY,KAAK,EAAE,WAAW;AAC7C,qBAAS,MAAM,YAAY,KAAK,EAAE,UAAU;AAC5C;AAAA,UACJ,KAAK;AACD,qBAAS,MAAM,YAAY,KAAK,EAAE,SAAS;AAC3C,qBAAS,MAAM,YAAY,KAAK,EAAE,aAAa;AAC/C,qBAAS,MAAM,YAAY,KAAK,EAAE,YAAY;AAC9C,qBAAS,MAAM,YAAY,KAAK,EAAE,gBAAgB;AAClD;AAAA,UACJ,KAAK;AACD,qBAAS,MAAM,YAAY,KAAK,EAAE,aAAa;AAC/C,qBAAS,MAAM,YAAY,KAAK,EAAE,iBAAiB;AACnD;AAAA,UACJ,KAAK;AACD,qBAAS,MAAM,YAAY,KAAK,EAAE,iBAAiB;AACnD,qBAAS,MAAM,YAAY,KAAK,EAAE,qBAAqB;AACvD,qBAAS,MAAM,YAAY,KAAK,EAAE,QAAQ;AAC1C;AAAA,UACJ,KAAK;AAAM,qBAAS,MAAM,YAAY,KAAK,EAAE,mBAAmB;AAC5D,qBAAS,MAAM,YAAY,KAAK,EAAE,uBAAuB;AACzD,qBAAS,MAAM,YAAY,KAAK,EAAE,MAAM;AACxC,qBAAS,MAAM,YAAY,KAAK,EAAE,UAAU;AAC5C;AAAA,UACJ,KAAK;AACD,qBAAS,MAAM,YAAY,KAAK,EAAE,iBAAiB;AACnD,qBAAS,MAAM,YAAY,KAAK,EAAE,kBAAkB;AACpD,qBAAS,MAAM,YAAY,KAAK,EAAE,oBAAoB;AACtD,qBAAS,MAAM,YAAY,KAAK,EAAE,MAAM;AACxC;AAAA,UACJ,KAAK;AACD,qBAAS,MAAM,YAAY,KAAK,EAAE,YAAY;AAC9C,qBAAS,MAAM,YAAY,KAAK,EAAE,UAAU;AAC5C,qBAAS,MAAM,YAAY,KAAK,EAAE,OAAO;AACzC,qBAAS,MAAM,YAAY,KAAK,EAAE,cAAc;AAChD;AAAA,UACJ,KAAK;AACD,qBAAS,MAAM,YAAY,KAAK,EAAE,mBAAmB;AACrD,qBAAS,MAAM,YAAY,KAAK,EAAE,sCAAsC;AACxE,qBAAS,MAAM,YAAY,KAAK,EAAE,qBAAqB;AACvD,qBAAS,MAAM,YAAY,KAAK,EAAE,6BAA6B;AAC/D,qBAAS,MAAM,YAAY,KAAK,EAAE,kBAAkB;AACpD,qBAAS,MAAM,YAAY,KAAK,EAAE,sCAAsC;AACxE;AAAA,QAAA;AAAA,MACR;AAAA,IAER;AAGM,UAAA,mBAAmB,CAAC,UAAkB;AAExC,YAAM,WAAW,SAAS,MAAM,YAAY,KAAK,EAAE;AAC/C,UAAA,KAAK,UAAU,UAAU,UAAU;AACzB,kBAAA,MAAM,KAAK,QAAQ;AAAA,MAAA;AAGjC,eAAS,MAAM,YAAY,OAAO,OAAO,CAAC;AAAA,IAC9C;AAGM,UAAA,eAAe,CAAC,GAAQ,UAAkB;AACpC,cAAA,IAAI,WAAW,CAAC;AACxB,UAAI,KAAK,EAAE,UAAU,EAAE,OAAO,OAAO;AACjC,iBAAS,MAAM,YAAY,KAAK,EAAE,YAAY,EAAE,OAAO;AAAA,MAAA;AAAA,IAE/D;AAGM,UAAA,cAAc,CAAC,UAAkB;AACnC,YAAM,aAAa,SAAS,MAAM,YAAY,KAAK;AACnDA,oBAAAA,MAAI,YAAY;AAAA,QACZ,OAAO,IAAI,WAAW,OAAO;AAAA,QAC7B,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AAEdA,wBAAAA,MAAI,YAAY;AAAA,YACZ,OAAO;AAAA,UAAA,CACV;AAAG,gBAAM,iBAAkB,IAAI,cAA2B,IAAI,CAAY,aAAA;AACvE,mBAAO,IAAI,QAA+C,CAAC,SAAS,WAAW;AAC3EA,4BAAAA,MAAI,WAAW;AAAA,gBACX,KAAK,GAAG,6BAAmC;AAAA,gBAC3C,UAAU;AAAA,gBACV,MAAM;AAAA,gBACN,QAAQ;AAAA,kBACJ,kBAAkB,UAAU,SAAS;AAAA,gBACzC;AAAA,gBACA,SAAS,CAAC,cAAc;AAChB,sBAAA;AACA,0BAAM,OAAO,KAAK,MAAM,UAAU,IAAI;AACtC,wBAAI,KAAK,SAAS;AACN,8BAAA;AAAA,wBACJ,UAAU,KAAK,OAAO;AAAA,wBACtB,SAAS,KAAK,OAAO;AAAA,sBAAA,CACxB;AACD,8BAAQ,IAAI,WAAW,KAAK,OAAO,UAAU,KAAK,OAAO,OAAO;AAAA,oBAAA,OAC7D;AACH,6BAAO,IAAI,MAAM,KAAK,WAAW,MAAM,CAAC;AAAA,oBAAA;AAAA,2BAEvC,KAAK;AACH,2BAAA,IAAI,MAAM,UAAU,CAAC;AAAA,kBAAA;AAAA,gBAEpC;AAAA,gBACA,MAAM,CAAC,QAAQ;AACX,yBAAO,GAAG;AAAA,gBAAA;AAAA,cACd,CACH;AAAA,YAAA,CACJ;AAAA,UAAA,CACJ;AAAG,kBAAQ,IAAI,cAAc,EACzB,KAAK,CAAc,eAAA;AAEhB,uBAAW,SAAS,CAAC,GAAG,WAAW,QAAQ,GAAG,UAAU;AACxDA,0BAAAA,MAAI,YAAY;AAAA,UAAA,CACnB,EACA,MAAM,CAAO,QAAA;AACVA,0BAAAA,MAAI,YAAY;AAChBA,0BAAAA,MAAI,UAAU;AAAA,cACV,OAAO,aAAa,IAAI;AAAA,cACxB,MAAM;AAAA,cACN,UAAU;AAAA,YAAA,CACb;AAAA,UAAA,CACJ;AAAA,QAAA;AAAA,MACT,CACH;AAAA,IACL;AAGM,UAAA,eAAe,CAAC,aAAqB,eAAuB;AAC9D,YAAM,aAAa,SAAS,MAAM,YAAY,WAAW;AAEzD,YAAM,YAAY,WAAW,OAAO,IAAI,CAAA,QAAO,IAAI,OAAO;AAC1DA,oBAAAA,MAAI,aAAa;AAAA,QACb,SAAS,UAAU,UAAU;AAAA;AAAA,QAC7B,MAAM;AAAA,MAAA,CACT;AAAA,IACL;AAGM,UAAA,cAAc,CAAC,aAAqB,eAAuB;AAC7D,UAAI,WAAW;AAAO;AACtB,eAAS,MAAM,YAAY,WAAW,EAAE,OAAO,OAAO,YAAY,CAAC;AAAA,IACvE;AAGM,UAAA,eAAe,CAAC,OAAe;AAEjCA,oBAAAA,MAAI,YAAY;AAAA,QACZ,OAAO;AAAA,MAAA,CACV;AAGDA,oBAAAA,MAAI,QAAQ;AAAA,QACR,KAAK,GAAG,6BAAmC;AAAA,QAC3C,QAAQ;AAAA,QACR,MAAM;AAAA,UACF,QAAQ,GAAG,MAAM,GAAG,EAAE,OAAO,CAAAC,QAAMA,GAAE;AAAA,QACzC;AAAA,QACA,SAAS,CAAC,QAAa;;AACX,kBAAA,IAAI,aAAa,IAAI,IAAI;AAE7B,cAAA,IAAI,QAAQ,IAAI,KAAK,SAAS,OAAO,IAAI,KAAK,QAAQ;AAEtD,kBAAM,aAAa,IAAI,KAAK,OAAO,WAAW,CAAC;AAGtC,qBAAA,MAAM,cAAc,CAAC;AAG1B,gBAAA,MAAM,QAAQ,UAAU,GAAG;AAChB,yBAAA,QAAQ,CAAC,SAAc;AAExB,sBAAA,YAAY,KAAK,gBAAgB,CAAC;AAGxC,sBAAM,SAAS,CAAC,UAAU,UAAU,KAAK,SACpC,OAAO,KAAK,WAAW,WACpB,KAAK,MAAM,KAAK,MAAM,IAAI,KAAK,SAAU,CAAC;AAGlD,sBAAM,kBAAkB,UAAU,SAAS,UAAU,IAAI,CAAC,QAAgB;;AAEhE,wBAAA,aAAWC,MAAA,IAAI,MAAM,GAAG,EAAE,IAAI,MAAnB,gBAAAA,IAAsB,MAAM,KAAK,OAAM;AACjD,yBAAA;AAAA,oBACH;AAAA,oBACA,SAAS;AAAA,kBACb;AAAA,gBACH,CAAA,IAAI,OAAO,IAAI,CAAC,QAAa;AACtB,sBAAA,OAAO,QAAQ,UAAU;AAClB,2BAAA;AAAA,sBACH,UAAU;AAAA,sBACV,SAAS,GAAG,6BAAmC,cAAc,GAAG;AAAA,oBACpE;AAAA,kBAAA;AAEG,yBAAA;AAAA,oBACH,UAAU,IAAI,YAAY,IAAI,QAAQ;AAAA,oBACtC,SAAS,IAAI,WAAW,IAAI,OAAO,GAAG,6BAAmC,cAAc,IAAI,YAAY,IAAI,QAAQ,EAAE;AAAA,kBACzH;AAAA,gBAAA,CACH;AAGD,sBAAM,aAAkB;AAAA,kBACpB,IAAI,KAAK;AAAA;AAAA,kBACT,MAAM,KAAK;AAAA,kBACX,WAAW,KAAK,aAAa;AAAA,kBAC7B,QAAQ;AAAA,kBACR,QAAQ,KAAK,UAAU;AAAA,gBAC3B;AAGI,oBAAA,KAAK,SAAS,OAAO;AACrB,6BAAW,kBAAkB,KAAK;AAClC,6BAAW,iBAAiB,KAAK;AACjC,6BAAW,WAAW,KAAK;AAC3B,6BAAW,eAAe,KAAK;AAC/B,6BAAW,MAAM,KAAK;AAAA,gBAAA,WACf,KAAK,SAAS,aAAa;AAClC,6BAAW,gBAAgB,KAAK;AAChC,6BAAW,eAAe,KAAK;AAAA,gBAAA,WACxB,KAAK,SAAS,OAAO;AAC5B,6BAAW,WAAW,KAAK;AAC3B,6BAAW,UAAU,KAAK;AAAA,gBAAA,WACnB,KAAK,SAAS,OAAO;AAC5B,6BAAW,SAAS,KAAK;AACzB,6BAAW,aAAa,KAAK;AAC7B,6BAAW,YAAY,KAAK;AAC5B,6BAAW,gBAAgB,KAAK;AAAA,gBAAA,WACzB,KAAK,SAAS,OAAO;AAC5B,6BAAW,aAAa,KAAK;AAC7B,6BAAW,iBAAiB,KAAK;AAAA,gBAAA,WAC1B,KAAK,SAAS,MAAM;AAC3B,6BAAW,iBAAiB,KAAK;AACjC,6BAAW,qBAAqB,KAAK;AACrC,6BAAW,QAAQ,KAAK;AAAA,gBAAA,WACjB,KAAK,SAAS,MAAM;AAC3B,6BAAW,mBAAmB,KAAK;AACnC,6BAAW,uBAAuB,KAAK;AACvC,6BAAW,MAAM,KAAK;AACtB,6BAAW,UAAU,KAAK;AAAA,gBAAA,WACnB,KAAK,SAAS,OAAO;AAC5B,6BAAW,iBAAiB,KAAK;AACjC,6BAAW,kBAAkB,KAAK;AAClC,6BAAW,oBAAoB,KAAK;AACpC,6BAAW,MAAM,KAAK;AAAA,gBAAA,WACf,KAAK,SAAS,OAAO;AAC5B,6BAAW,YAAY,KAAK;AAC5B,6BAAW,UAAU,KAAK,UACrB,OAAO,KAAK,YAAY,WAAW,KAAK,QAAQ,MAAM,GAAG,IAAI,KAAK,UAAW,CAAC;AACnF,6BAAW,OAAO,KAAK;AACvB,6BAAW,cAAc,KAAK;AAAA,gBAAA,WACvB,KAAK,SAAS,QAAQ;AAC7B,6BAAW,mBAAmB,KAAK;AACnC,6BAAW,sCAAsC,KAAK;AACtD,6BAAW,qBAAqB,KAAK;AACrC,6BAAW,6BAA6B,KAAK;AAC7C,6BAAW,kBAAkB,KAAK;AAClC,6BAAW,sCAAsC,KAAK;AAAA,gBAAA;AAGjD,yBAAA,MAAM,YAAY,KAAK,UAAU;AAAA,cAAA,CAC7C;AAAA,YAAA;AAGG,oBAAA,IAAI,aAAa,SAAS,KAAK;AAAA,UAAA,OACpC;AACHF,0BAAAA,MAAI,UAAU;AAAA,cACV,SAAO,SAAI,SAAJ,mBAAU,YAAW;AAAA,cAC5B,MAAM;AAAA,YAAA,CACT;AAAA,UAAA;AAAA,QAET;AAAA,QACA,MAAM,CAAC,QAAQ;AACH,kBAAA,MAAM,WAAW,GAAG;AAC5BA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACT;AAAA,QACL;AAAA,QACA,UAAU,MAAM;AACZA,wBAAAA,MAAI,YAAY;AAAA,QAAA;AAAA,MACpB,CACH;AAAA,IACL;AAGA,UAAM,aAAa,MAAM;AACb,cAAA,IAAI,WAAW,SAAS,KAAK;AAGrC,UAAI,SAAS,MAAM,YAAY,WAAW,GAAG;AACzCA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACT;AACD;AAAA,MAAA;AAIJ,eAAS,IAAI,GAAG,IAAI,SAAS,MAAM,YAAY,QAAQ,KAAK;AACxD,cAAM,SAAS,SAAS,MAAM,YAAY,CAAC;AAEvC,YAAA,CAAC,OAAO,WAAW;AACnBA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO,MAAM,OAAO,IAAI;AAAA,YACxB,MAAM;AAAA,UAAA,CACT;AACD;AAAA,QAAA;AAGA,YAAA,OAAO,OAAO,WAAW,GAAG;AAC5BA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO,MAAM,OAAO,IAAI;AAAA,YACxB,MAAM;AAAA,UAAA,CACT;AACD;AAAA,QAAA;AAKA,YAAA,OAAO,SAAS,OAAO;AAEnB,cAAA,OAAO,oBAAoB,UAAa,OAAO,oBAAoB,QAAQ,OAAO,gBAAgB,SAAS,MAAM,IAAI;AACjH,gBAAA,CAAC,OAAO,gBAAgB;AACxBA,4BAAAA,MAAI,UAAU;AAAA,gBACV,OAAO;AAAA,gBACP,MAAM;AAAA,cAAA,CACT;AACD;AAAA,YAAA;AAAA,UACJ;AAIA,cAAA,OAAO,aAAa,UAAa,OAAO,aAAa,QAAQ,OAAO,SAAS,SAAS,MAAM,IAAI;AAC5F,gBAAA,CAAC,OAAO,cAAc;AACtBA,4BAAAA,MAAI,UAAU;AAAA,gBACV,OAAO;AAAA,gBACP,MAAM;AAAA,cAAA,CACT;AACD;AAAA,YAAA;AAAA,UACJ;AAAA,QACJ;AAIA,YAAA,OAAO,SAAS,MAAM;AAClB,cAAA,OAAO,mBAAmB,UAAa,OAAO,mBAAmB,QAAQ,OAAO,eAAe,SAAS,MAAM,IAAI;AAC9G,gBAAA,CAAC,OAAO,oBAAoB;AAC5BA,4BAAAA,MAAI,UAAU;AAAA,gBACV,OAAO;AAAA,gBACP,MAAM;AAAA,cAAA,CACT;AACD;AAAA,YAAA;AAAA,UACJ;AAAA,QACJ;AAMA,YAAA,OAAO,SAAS,MAAM;AAElB,cAAA,OAAO,qBAAqB,UAAa,OAAO,qBAAqB,QAAQ,OAAO,iBAAiB,SAAS,MAAM,IAAI;AACpH,gBAAA,CAAC,OAAO,sBAAsB;AAC9BA,4BAAAA,MAAI,UAAU;AAAA,gBACV,OAAO;AAAA,gBACP,MAAM;AAAA,cAAA,CACT;AACD;AAAA,YAAA;AAAA,UACJ;AAIA,cAAA,OAAO,QAAQ,UAAa,OAAO,QAAQ,QAAQ,OAAO,IAAI,SAAS,MAAM,IAAI;AAC7E,gBAAA,CAAC,OAAO,SAAS;AACjBA,4BAAAA,MAAI,UAAU;AAAA,gBACV,OAAO;AAAA,gBACP,MAAM;AAAA,cAAA,CACT;AACD;AAAA,YAAA;AAAA,UACJ;AAAA,QACJ;AAIA,YAAA,OAAO,SAAS,OAAO;AAEnB,cAAA,OAAO,aAAa,UAAa,OAAO,aAAa,QAAQ,OAAO,SAAS,SAAS,MAAM,IAAI;AAC5F,gBAAA,CAAC,OAAO,SAAS;AACjBA,4BAAAA,MAAI,UAAU;AAAA,gBACV,OAAO;AAAA,gBACP,MAAM;AAAA,cAAA,CACT;AACD;AAAA,YAAA;AAAA,UACJ;AAAA,QACJ;AAIA,YAAA,OAAO,SAAS,aAAa;AAEzB,cAAA,OAAO,kBAAkB,UAAa,OAAO,kBAAkB,QAAQ,OAAO,cAAc,SAAS,MAAM,IAAI;AAC3G,gBAAA,CAAC,OAAO,cAAc;AACtBA,4BAAAA,MAAI,UAAU;AAAA,gBACV,OAAO;AAAA,gBACP,MAAM;AAAA,cAAA,CACT;AACD;AAAA,YAAA;AAAA,UACJ;AAAA,QACJ;AAIA,YAAA,OAAO,SAAS,OAAO;AAEnB,cAAA,OAAO,WAAW,UAAa,OAAO,WAAW,QAAQ,OAAO,OAAO,SAAS,MAAM,IAAI;AACtF,gBAAA,CAAC,OAAO,YAAY;AACpBA,4BAAAA,MAAI,UAAU;AAAA,gBACV,OAAO;AAAA,gBACP,MAAM;AAAA,cAAA,CACT;AACD;AAAA,YAAA;AAAA,UACJ;AAIA,cAAA,OAAO,cAAc,UAAa,OAAO,cAAc,QAAQ,OAAO,UAAU,SAAS,MAAM,IAAI;AAC/F,gBAAA,CAAC,OAAO,eAAe;AACvBA,4BAAAA,MAAI,UAAU;AAAA,gBACV,OAAO;AAAA,gBACP,MAAM;AAAA,cAAA,CACT;AACD;AAAA,YAAA;AAAA,UACJ;AAAA,QACJ;AAIA,YAAA,OAAO,SAAS,OAAO;AAEnB,cAAA,OAAO,eAAe,UAAa,OAAO,eAAe,QAAQ,OAAO,WAAW,SAAS,MAAM,IAAI;AAClG,gBAAA,CAAC,OAAO,gBAAgB;AACxBA,4BAAAA,MAAI,UAAU;AAAA,gBACV,OAAO;AAAA,gBACP,MAAM;AAAA,cAAA,CACT;AACD;AAAA,YAAA;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAIJA,oBAAAA,MAAI,YAAY;AAAA,QACZ,OAAO;AAAA,MAAA,CACV;AACD,YAAM,uBAAuB,SAAS,MAAM,YAAY,IAAI,CAAU,WAAA;AAElE,cAAM,YAAY,KAAK,MAAM,KAAK,UAAU,MAAM,CAAC;AAEnD,kBAAU,SAAS,OAAO,OAAO,IAAI,CAAA,QAAO,IAAI,QAAQ;AAEpD,YAAA,KAAK,UAAU,QAAQ;AACvB,oBAAU,KAAK,OAAO;AAAA,QAAA;AAEnB,eAAA;AAAA,MAAA,CACV;AAGK,YAAA,SAAS,KAAK,UAAU;AAG9B,YAAM,cAAmB;AAAA,QACrB,aAAa;AAAA,QACb,QAAQ,UAAU,SAAS;AAAA,MAC/B;AAGA,UAAI,QAAQ;AACI,oBAAA,eAAe,UAAU,SAAS;AAAA,MAAA;AAG5C,YAAA,SAAS,GAAG,6BAAmC;AAErDA,oBAAAA,MAAI,QAAQ;AAAA,QACR,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,SAAS,CAAC,QAAa;;AAEnB,cAAI,UAAU,UAAU,MAAM,SAAS,GAAG;AACxB,0BAAA;AAAA,UAAA,OACX;AACHA,0BAAAA,MAAI,YAAY;AAEZ,iBAAA,SAAI,SAAJ,mBAAU,SAAS;AACnBA,4BAAAA,MAAI,UAAU;AAAA,gBACV,OAAO,GAAG,SAAS,OAAO,IAAI;AAAA,gBAC9B,YAAY;AAAA,gBACZ,SAAS,MAAM;AACXA,gCAAAA,MAAI,aAAa;AAAA,gBAAA;AAAA,cACrB,CACH;AAAA,YAAA,OACE;AACH,oBAAM,aAAW,SAAI,SAAJ,mBAAU,YAAW,GAAG,SAAS,OAAO,IAAI;AAC7DA,4BAAAA,MAAI,UAAU;AAAA,gBACV,OAAO,GAAG,SAAS,OAAO,IAAI;AAAA,gBAC9B,SAAS;AAAA,gBACT,YAAY;AAAA,cAAA,CACf;AAAA,YAAA;AAAA,UACL;AAAA,QAER;AAAA,QACA,MAAM,CAAC,QAAQ;AACXA,wBAAAA,MAAI,YAAY;AACV,gBAAA,WAAW,IAAI,UAAU;AAC/BA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO,GAAG,SAAS,OAAO,IAAI;AAAA,YAC9B,SAAS;AAAA,YACT,YAAY;AAAA,UAAA,CACf;AAAA,QAAA;AAAA,MACL,CACH;AAAA,IACL;AAGA,UAAM,gBAAgB,MAAM;AAEpB,UAAA,UAAU,MAAM,WAAW,GAAG;AAC9BA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,SAAS,MAAM;AACXA,0BAAAA,MAAI,aAAa;AAAA,UAAA;AAAA,QACrB,CACH;AACD;AAAA,MAAA;AAIJA,oBAAAA,MAAI,QAAQ;AAAA,QACR,KAAK,GAAG,6BAAmC;AAAA,QAC3C,QAAQ;AAAA,QACR,MAAM;AAAA,UACF,UAAU,UAAU;AAAA,QACxB;AAAA,QACA,SAAS,CAAC,QAAQ;;AACdA,wBAAAA,MAAI,YAAY;AAEZ,eAAA,SAAI,SAAJ,mBAAU,SAAS;AACnBA,0BAAAA,MAAI,UAAU;AAAA,cACV,OAAO;AAAA,cACP,YAAY;AAAA,cACZ,SAAS,MAAM;AACXA,8BAAAA,MAAI,aAAa;AAAA,cAAA;AAAA,YACrB,CACH;AAAA,UAAA,OACE;AACG,kBAAA,aAAW,SAAI,SAAJ,mBAAU,YAAW;AACtCA,0BAAAA,MAAI,UAAU;AAAA,cACV,OAAO;AAAA,cACP,SAAS,sBAAsB;AAAA,cAC/B,YAAY;AAAA,cACZ,SAAS,MAAM;AACXA,8BAAAA,MAAI,aAAa;AAAA,cAAA;AAAA,YACrB,CACH;AAAA,UAAA;AAAA,QAET;AAAA,QACA,MAAM,CAAC,QAAQ;AACXA,wBAAAA,MAAI,YAAY;AACV,gBAAA,WAAW,IAAI,UAAU;AAC/BA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,SAAS,sBAAsB;AAAA,YAC/B,YAAY;AAAA,YACZ,SAAS,MAAM;AACXA,4BAAAA,MAAI,aAAa;AAAA,YAAA;AAAA,UACrB,CACH;AAAA,QAAA;AAAA,MACL,CACH;AAAA,IACL;AAGA,UAAM,mBAAmB,CAAC,GAAQ,OAAe,UAAkB;AAC/D,UAAI,KAAK,EAAE,UAAU,EAAE,OAAO,UAAU,QAAW;AAC/C,cAAM,gBAAgB,SAAS,EAAE,OAAO,KAAK;AAC7C,iBAAS,MAAM,YAAY,KAAK,EAAE,KAAK,IAAI,YAAY,aAAa;AAAA,MAAA;AAAA,IAE5E;AAGA,UAAM,sBAAsB,CAAC,GAAQ,OAAe,UAAkB;AAClE,UAAI,KAAK,EAAE,UAAU,EAAE,OAAO,UAAU,QAAW;AAC/C,cAAM,gBAAgB,SAAS,EAAE,OAAO,KAAK;AAC7C,iBAAS,MAAM,YAAY,KAAK,EAAE,KAAK,IAAI,eAAe,aAAa;AAAA,MAAA;AAAA,IAE/E;AAGA,UAAM,8BAA8B,CAAC,GAAQ,OAAe,UAAkB;AAC1E,UAAI,KAAK,EAAE,UAAU,EAAE,OAAO,UAAU,QAAW;AAC/C,cAAM,gBAAgB,SAAS,EAAE,OAAO,KAAK;AAC7C,iBAAS,MAAM,YAAY,KAAK,EAAE,KAAK,IAAI,uBAAuB,aAAa;AAAA,MAAA;AAAA,IAEvF;AAGA,UAAM,6BAA6B,CAAC,GAAQ,OAAe,UAAkB;AACzE,UAAI,KAAK,EAAE,UAAU,EAAE,OAAO,UAAU,QAAW;AAC/C,cAAM,gBAAgB,SAAS,EAAE,OAAO,KAAK;AAC7C,iBAAS,MAAM,YAAY,KAAK,EAAE,KAAK,IAAI,sBAAsB,aAAa;AAAA,MAAA;AAAA,IAEtF;AAGA,UAAM,0BAA0B,CAAC,GAAQ,OAAe,UAAkB;AACtE,UAAI,KAAK,EAAE,UAAU,EAAE,OAAO,UAAU,QAAW;AAC/C,cAAM,gBAAgB,SAAS,EAAE,OAAO,KAAK;AAC7C,iBAAS,MAAM,YAAY,KAAK,EAAE,KAAK,IAAI,mBAAmB,aAAa;AAAA,MAAA;AAAA,IAEnF;AAGA,UAAM,wBAAwB,CAAC,GAAQ,OAAe,UAAkB;AACpE,UAAI,KAAK,EAAE,UAAU,EAAE,OAAO,UAAU,QAAW;AAC/C,cAAM,gBAAgB,SAAS,EAAE,OAAO,KAAK;AAC7C,iBAAS,MAAM,YAAY,KAAK,EAAE,KAAK,IAAI,iBAAiB,aAAa;AAAA,MAAA;AAAA,IAEjF;AAGM,UAAA,eAAe,CAAC,OAAe,gBAAwB;AACzD,YAAM,SAAS,SAAS,MAAM,YAAY,KAAK;AAG/C,UAAI,OAAO,SAAS;AAAO;AAGvB,UAAA,CAAC,OAAO,SAAS;AACjB,eAAO,UAAU,CAAC;AAAA,MAAA;AAItB,YAAM,gBAAgB,OAAO,QAAQ,QAAQ,WAAW;AACxD,UAAI,kBAAkB,IAAI;AAEf,eAAA,QAAQ,OAAO,eAAe,CAAC;AAAA,MAAA,OACnC;AAEI,eAAA,QAAQ,KAAK,WAAW;AAAA,MAAA;AAAA,IAEvC;AAGM,UAAA,UAAU,CAAC,OAAe,UAAkB;AAC9C,eAAS,MAAM,YAAY,KAAK,EAAE,OAAO;AAAA,IAC7C;AAGM,UAAA,wBAAwB,CAAC,OAAe,UAAkB;AAC5D,eAAS,MAAM,YAAY,KAAK,EAAE,qBAAqB;AAAA,IAC3D;AAGM,UAAA,gCAAgC,CAAC,OAAe,UAAkB;AACpE,eAAS,MAAM,YAAY,KAAK,EAAE,6BAA6B;AAAA,IACnE;AAGM,UAAA,yCAAyC,CAAC,OAAe,UAAkB;AAC7E,eAAS,MAAM,YAAY,KAAK,EAAE,sCAAsC;AAAA,IAC5E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACl8CA,GAAG,WAAW,eAAe;"}