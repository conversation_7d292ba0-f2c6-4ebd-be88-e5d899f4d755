<layout-default-uni class="data-v-d9776e99" u-s="{{['d']}}" u-i="d9776e99-0" bind:__l="__l"><page-layout wx:if="{{at}}" class="data-v-d9776e99" u-s="{{['d']}}" u-i="d9776e99-1,d9776e99-0" bind:__l="__l" u-p="{{at}}"><view wx:if="{{a}}" class="doctor-filter-fixed data-v-d9776e99"><view class="filter-section data-v-d9776e99"><view class="date-filter data-v-d9776e99"><text class="filter-label data-v-d9776e99">时间：</text><picker class="data-v-d9776e99" mode="date" value="{{d}}" start="1900-01-01" end="2099-12-31" bindchange="{{e}}"><view class="date-picker data-v-d9776e99"><text class="data-v-d9776e99">{{b}}</text><uni-icons wx:if="{{c}}" class="data-v-d9776e99" u-i="d9776e99-2,d9776e99-1" bind:__l="__l" u-p="{{c}}"/></view></picker><view class="date-separator data-v-d9776e99">至</view><picker class="data-v-d9776e99" mode="date" value="{{h}}" start="1900-01-01" end="2099-12-31" bindchange="{{i}}"><view class="date-picker data-v-d9776e99"><text class="data-v-d9776e99">{{f}}</text><uni-icons wx:if="{{g}}" class="data-v-d9776e99" u-i="d9776e99-3,d9776e99-1" bind:__l="__l" u-p="{{g}}"/></view></picker><view class="{{['reset-btn', 'data-v-d9776e99', k && 'refreshing']}}" bindtap="{{l}}"><uni-icons wx:if="{{j}}" class="data-v-d9776e99" u-i="d9776e99-4,d9776e99-1" bind:__l="__l" u-p="{{j}}"/></view></view><view class="search-row data-v-d9776e99"><text class="search-label data-v-d9776e99">姓名：</text><view class="search-input-container data-v-d9776e99"><input type="text" placeholder="请输入患者姓名" class="search-input name-input data-v-d9776e99" maxlength="{{20}}" bindinput="{{m}}" value="{{n}}"/><text wx:if="{{o}}" class="clear-btn data-v-d9776e99" bindtap="{{p}}"> ✕ </text></view></view><view class="search-row data-v-d9776e99"><text class="search-label data-v-d9776e99">问题：</text><view class="search-input-container data-v-d9776e99"><input type="text" placeholder="请输入问题关键字" class="search-input question-input data-v-d9776e99" maxlength="{{50}}" bindinput="{{q}}" value="{{r}}"/><text wx:if="{{s}}" class="clear-btn data-v-d9776e99" bindtap="{{t}}"> ✕ </text></view></view><view class="search-row data-v-d9776e99"><text class="search-label data-v-d9776e99">状态：</text><view class="status-radio-container data-v-d9776e99"><view wx:for="{{v}}" wx:for-item="option" wx:key="d" class="radio-option data-v-d9776e99" bindtap="{{option.e}}"><view class="{{['radio-circle', 'data-v-d9776e99', option.b && 'radio-checked']}}"><view wx:if="{{option.a}}" class="radio-dot data-v-d9776e99"></view></view><text class="radio-label data-v-d9776e99">{{option.c}}</text></view></view></view><view class="search-row data-v-d9776e99"><text class="search-label data-v-d9776e99">筛选：</text><view class="mention-filter-container data-v-d9776e99"><view class="{{['mention-filter-option', 'data-v-d9776e99', z && 'mention-active']}}" bindtap="{{A}}"><view class="{{['mention-checkbox', 'data-v-d9776e99', y && 'mention-checked']}}"><uni-icons wx:if="{{w}}" class="data-v-d9776e99" u-i="d9776e99-5,d9776e99-1" bind:__l="__l" u-p="{{x}}"></uni-icons></view><text class="mention-label data-v-d9776e99">只看@我的问题</text></view></view></view><view class="button-row data-v-d9776e99"><view class="search-button data-v-d9776e99" bindtap="{{C}}"><uni-icons wx:if="{{B}}" class="data-v-d9776e99" u-i="d9776e99-6,d9776e99-1" bind:__l="__l" u-p="{{B}}"></uni-icons><text class="search-button-text data-v-d9776e99">查询</text></view><view class="clear-all-button data-v-d9776e99" bindtap="{{E}}"><uni-icons wx:if="{{D}}" class="data-v-d9776e99" u-i="d9776e99-7,d9776e99-1" bind:__l="__l" u-p="{{D}}"></uni-icons><text class="clear-all-button-text data-v-d9776e99">清空</text></view></view></view></view><scroll-view wx:if="{{F}}" class="page-scroll-view doctor-scroll data-v-d9776e99" scroll-y="true"><view wx:if="{{G}}" class="loading-container data-v-d9776e99"><wd-loading class="data-v-d9776e99" u-i="d9776e99-8,d9776e99-1" bind:__l="__l"/></view><view wx:if="{{H}}" class="empty-tip data-v-d9776e99"><text class="empty-text data-v-d9776e99">暂时没有问题记录</text><text class="empty-hint data-v-d9776e99">使用上方筛选条件查找患者问题</text><view class="retry-button data-v-d9776e99" bindtap="{{I}}"><text class="retry-text data-v-d9776e99">重新加载</text></view></view><view wx:if="{{J}}" class="chat-list data-v-d9776e99"><view wx:for="{{K}}" wx:for-item="item" wx:key="o" class="chat-item data-v-d9776e99" bindtap="{{item.p}}"><image class="chat-avatar data-v-d9776e99" src="{{item.a}}" mode="aspectFill" binderror="{{item.b}}"/><view class="chat-content data-v-d9776e99"><view class="chat-row data-v-d9776e99"><view class="chat-nickname-container data-v-d9776e99"><text class="chat-nickname data-v-d9776e99">{{item.c}}</text><text wx:if="{{item.d}}" class="reply-text data-v-d9776e99"> 回复 <text class="reply-target data-v-d9776e99">{{item.e}}</text>：<text class="reply-content data-v-d9776e99">{{item.f}}</text></text></view><text class="chat-time data-v-d9776e99">{{item.g}}</text></view><view wx:if="{{item.h}}" class="chat-title-row data-v-d9776e99"><text class="chat-title data-v-d9776e99">{{item.i}}</text></view><view wx:if="{{item.j}}" class="mentioned-doctors-row data-v-d9776e99"><view class="mentioned-doctors data-v-d9776e99"><text wx:for="{{item.k}}" wx:for-item="doctor" wx:key="b" class="mentioned-doctor data-v-d9776e99"> @{{doctor.a}}</text></view></view><view class="chat-bottom-row data-v-d9776e99"><text class="chat-msg data-v-d9776e99">{{item.l}}</text><view class="{{['reply-status-badge', 'data-v-d9776e99', item.n]}}"><text class="status-text data-v-d9776e99">{{item.m}}</text></view></view></view></view></view></scroll-view><view wx:if="{{L}}" class="patient-filter-fixed data-v-d9776e99"><view class="filter-section data-v-d9776e99"><view class="date-filter data-v-d9776e99"><text class="filter-label data-v-d9776e99">时间：</text><picker class="data-v-d9776e99" mode="date" value="{{O}}" start="1900-01-01" end="2099-12-31" bindchange="{{P}}"><view class="date-picker data-v-d9776e99"><text class="data-v-d9776e99">{{M}}</text><uni-icons wx:if="{{N}}" class="data-v-d9776e99" u-i="d9776e99-9,d9776e99-1" bind:__l="__l" u-p="{{N}}"/></view></picker><view class="date-separator data-v-d9776e99">至</view><picker class="data-v-d9776e99" mode="date" value="{{S}}" start="1900-01-01" end="2099-12-31" bindchange="{{T}}"><view class="date-picker data-v-d9776e99"><text class="data-v-d9776e99">{{Q}}</text><uni-icons wx:if="{{R}}" class="data-v-d9776e99" u-i="d9776e99-10,d9776e99-1" bind:__l="__l" u-p="{{R}}"/></view></picker><view class="{{['reset-btn', 'data-v-d9776e99', V && 'refreshing']}}" bindtap="{{W}}"><uni-icons wx:if="{{U}}" class="data-v-d9776e99" u-i="d9776e99-11,d9776e99-1" bind:__l="__l" u-p="{{U}}"/></view></view><view class="search-row data-v-d9776e99"><text class="search-label data-v-d9776e99">问题：</text><view class="search-input-container data-v-d9776e99"><input type="text" placeholder="请输入问题关键字" class="search-input question-input data-v-d9776e99" maxlength="{{50}}" bindinput="{{X}}" value="{{Y}}"/><text wx:if="{{Z}}" class="clear-btn data-v-d9776e99" bindtap="{{aa}}"> ✕ </text></view></view><view class="search-row data-v-d9776e99"><text class="search-label data-v-d9776e99">状态：</text><view class="status-radio-container data-v-d9776e99"><view wx:for="{{ab}}" wx:for-item="option" wx:key="d" class="radio-option data-v-d9776e99" bindtap="{{option.e}}"><view class="{{['radio-circle', 'data-v-d9776e99', option.b && 'radio-checked']}}"><view wx:if="{{option.a}}" class="radio-dot data-v-d9776e99"></view></view><text class="radio-label data-v-d9776e99">{{option.c}}</text></view></view></view><view class="button-row data-v-d9776e99"><view class="search-button data-v-d9776e99" bindtap="{{ad}}"><uni-icons wx:if="{{ac}}" class="data-v-d9776e99" u-i="d9776e99-12,d9776e99-1" bind:__l="__l" u-p="{{ac}}"></uni-icons><text class="search-button-text data-v-d9776e99">查询</text></view><view class="clear-all-button data-v-d9776e99" bindtap="{{af}}"><uni-icons wx:if="{{ae}}" class="data-v-d9776e99" u-i="d9776e99-13,d9776e99-1" bind:__l="__l" u-p="{{ae}}"></uni-icons><text class="clear-all-button-text data-v-d9776e99">清空</text></view></view></view><button class="add-btn data-v-d9776e99" bindtap="{{ag}}" disabled="{{ah}}">发起提问</button></view><scroll-view wx:if="{{ai}}" class="{{['page-scroll-view', 'data-v-d9776e99', ao && 'patient-scroll']}}" scroll-y="true" bindnavLeftClick="{{ap}}"><view wx:if="{{aj}}" class="loading-container data-v-d9776e99"><wd-loading class="data-v-d9776e99" u-i="d9776e99-14,d9776e99-1" bind:__l="__l"/></view><view wx:if="{{ak}}" class="empty-tip data-v-d9776e99"><text class="empty-text data-v-d9776e99">暂时没有问题记录</text><text class="empty-hint data-v-d9776e99">点击下方"发起提问"按钮开始咨询</text><view class="retry-button data-v-d9776e99" bindtap="{{al}}"><text class="retry-text data-v-d9776e99">重新加载</text></view></view><view wx:if="{{am}}" class="chat-list data-v-d9776e99"><view wx:for="{{an}}" wx:for-item="item" wx:key="o" class="chat-item data-v-d9776e99" bindtap="{{item.p}}"><image class="chat-avatar data-v-d9776e99" src="{{item.a}}" mode="aspectFill" binderror="{{item.b}}"/><view class="chat-content data-v-d9776e99"><view class="chat-row data-v-d9776e99"><view class="chat-nickname-container data-v-d9776e99"><text class="chat-nickname data-v-d9776e99">{{item.c}}</text><text wx:if="{{item.d}}" class="reply-text data-v-d9776e99"> 回复 <text class="reply-target data-v-d9776e99">{{item.e}}</text>：<text class="reply-content data-v-d9776e99">{{item.f}}</text></text></view><text class="chat-time data-v-d9776e99">{{item.g}}</text></view><view wx:if="{{item.h}}" class="chat-title-row data-v-d9776e99"><text class="chat-title data-v-d9776e99">{{item.i}}</text></view><view wx:if="{{item.j}}" class="mentioned-doctors-row data-v-d9776e99"><view class="mentioned-doctors data-v-d9776e99"><text wx:for="{{item.k}}" wx:for-item="doctor" wx:key="b" class="mentioned-doctor data-v-d9776e99"> @{{doctor.a}}</text></view></view><view class="chat-bottom-row data-v-d9776e99"><text class="chat-msg data-v-d9776e99">{{item.l}}</text><view class="{{['reply-status-badge', 'data-v-d9776e99', item.n]}}"><text class="status-text data-v-d9776e99">{{item.m}}</text></view></view></view></view></view></scroll-view><uni-calendar wx:if="{{as}}" class="r data-v-d9776e99" u-r="calendar" bindconfirm="{{ar}}" u-i="d9776e99-15,d9776e99-1" bind:__l="__l" u-p="{{as}}"/></page-layout></layout-default-uni>