/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-scroll-view.data-v-db74f9c7 {
  height: 100vh;
  background-color: #f5f5f5;
}
.form-container.data-v-db74f9c7 {
  min-height: 100vh;
  padding: 20rpx;
}
.form-header.data-v-db74f9c7 {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}
.form-header .form-title.data-v-db74f9c7 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
}
.form-section.data-v-db74f9c7 {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}
.form-item.data-v-db74f9c7 {
  margin-bottom: 30rpx;
}
.form-item.data-v-db74f9c7:last-child {
  margin-bottom: 0;
}
.form-label.data-v-db74f9c7 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}
.inline-form-item.data-v-db74f9c7 {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.inline-form-item.data-v-db74f9c7:last-child {
  margin-bottom: 0;
}
.inline-form-label.data-v-db74f9c7 {
  font-size: 30rpx;
  color: #333;
  width: 120rpx;
  flex-shrink: 0;
}
.inline-form-label .required.data-v-db74f9c7 {
  color: #ff4d4f;
  margin-right: 5rpx;
}
.inline-form-input.data-v-db74f9c7 {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fff;
}
.inline-form-input.data-v-db74f9c7:focus {
  border-color: #07C160;
}
.inline-form-input.data-v-db74f9c7:disabled {
  background-color: #f5f5f5;
  color: #999;
}
.btn-container.data-v-db74f9c7 {
  padding: 0 30rpx 40rpx;
}
.submit-btn.data-v-db74f9c7 {
  width: 100%;
  background-color: #07C160;
  color: #FFFFFF;
  border-radius: 8rpx;
  font-size: 32rpx;
  padding: 20rpx 0;
}
.submit-btn.data-v-db74f9c7:disabled {
  background: #cccccc;
}