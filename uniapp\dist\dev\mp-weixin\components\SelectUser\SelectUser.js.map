{"version": 3, "file": "SelectUser.js", "sources": ["../../../../../src/components/SelectUser/SelectUser.vue", "../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvY29tcG9uZW50cy9TZWxlY3RVc2VyL1NlbGVjdFVzZXIudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"SelectUser\">\r\n    <view @click.stop=\"handleClick\">\r\n      <wd-input\r\n        :placeholder=\"getPlaceholder($attrs)\"\r\n        v-bind=\"$attrs\"\r\n        readonly\r\n        v-model=\"showText\"\r\n      ></wd-input>\r\n    </view>\r\n    <SelectUserModal\r\n      v-if=\"modalShow\"\r\n      :selected=\"modelValue\"\r\n      :modalTitle=\"modalTitle\"\r\n      :maxSelectCount=\"maxSelectCount\"\r\n      :multi=\"!isRadioSelection\"\r\n      @change=\"handleChange\"\r\n      @close=\"() => (modalShow = false)\"\r\n    ></SelectUserModal>\r\n  </view>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, watch, useAttrs } from 'vue'\r\nimport { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'\r\nimport { http } from '@/utils/http'\r\nimport DaTree from '@/uni_modules/da-tree/index.vue'\r\nimport { isArray, isString } from '@/utils/is'\r\nimport SelectUserModal from './components/SelectUserModal.vue'\r\nimport { getPlaceholder } from '@/common/uitls'\r\ndefineOptions({\r\n  name: 'SelectUser',\r\n  options: {\r\n    styleIsolation: 'shared',\r\n  },\r\n})\r\nconst props = defineProps({\r\n  modelValue: {\r\n    type: [Array, String],\r\n    default: '',\r\n  },\r\n  labelKey: {\r\n    type: String,\r\n    default: 'realname',\r\n  },\r\n  rowKey: {\r\n    type: String,\r\n    default: 'username',\r\n  },\r\n  isRadioSelection: {\r\n    type: Boolean,\r\n    default: false,\r\n  },\r\n  modalTitle: {\r\n    type: String,\r\n    default: '选择用户',\r\n  },\r\n  maxSelectCount: {\r\n    type: Number,\r\n  },\r\n})\r\nconst emit = defineEmits(['update:modelValue', 'change'])\r\nconst api = {\r\n  list: '/sys/user/list',\r\n}\r\nconst showText = ref('')\r\nconst modalShow = ref(false)\r\n\r\n// 翻译\r\nconst transform = () => {\r\n  let value = props.modelValue\r\n  let len\r\n  if (isArray(value) || isString(value)) {\r\n    if (isArray(value)) {\r\n      len = value.length\r\n      value = value.join(',')\r\n    } else {\r\n      len = value.split(',').length\r\n    }\r\n    value = value.trim()\r\n    if (value) {\r\n      const params = { isMultiTranslate: true, pageSize: len, [props.rowKey]: value }\r\n      http.get(api.list, params).then((res: any) => {\r\n        if (res.success) {\r\n          const records = res.result?.records ?? []\r\n          showText.value = records.map((item) => item[props.labelKey]).join(',')\r\n        } else {\r\n          console.log('翻译失败~')\r\n        }\r\n      })\r\n    }\r\n  } else {\r\n    showText.value = ''\r\n  }\r\n}\r\n// 打开popup\r\nconst handleClick = () => {\r\n  modalShow.value = true\r\n}\r\nconst handleChange = (data) => {\r\n  const rowkey = data.map((item) => item[props.rowKey]).join(',')\r\n  const labelKey = data.map((item) => item[props.labelKey]).join(',')\r\n  showText.value = labelKey\r\n  emit('update:modelValue', rowkey)\r\n  emit('change', rowkey)\r\n}\r\n\r\nwatch(\r\n  () => props.modelValue,\r\n  () => {\r\n    transform()\r\n  },\r\n  { immediate: true },\r\n)\r\n</script>\r\n\r\n<style scoped></style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/components/SelectUser/SelectUser.vue'\nwx.createComponent(Component)"], "names": ["ref", "isArray", "isString", "http", "watch", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,MAAA,kBAA4B,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5B,UAAM,QAAQ;AAyBd,UAAM,OAAO;AACb,UAAM,MAAM;AAAA,MACV,MAAM;AAAA,IACR;AACM,UAAA,WAAWA,kBAAI,EAAE;AACjB,UAAA,YAAYA,kBAAI,KAAK;AAG3B,UAAM,YAAY,MAAM;AACtB,UAAI,QAAQ,MAAM;AACd,UAAA;AACJ,UAAIC,SAAQ,QAAA,KAAK,KAAKC,SAAA,SAAS,KAAK,GAAG;AACjC,YAAAD,SAAAA,QAAQ,KAAK,GAAG;AAClB,gBAAM,MAAM;AACJ,kBAAA,MAAM,KAAK,GAAG;AAAA,QAAA,OACjB;AACC,gBAAA,MAAM,MAAM,GAAG,EAAE;AAAA,QAAA;AAEzB,gBAAQ,MAAM,KAAK;AACnB,YAAI,OAAO;AACH,gBAAA,SAAS,EAAE,kBAAkB,MAAM,UAAU,KAAK,CAAC,MAAM,MAAM,GAAG,MAAM;AAC9EE,qBAAA,KAAK,IAAI,IAAI,MAAM,MAAM,EAAE,KAAK,CAAC,QAAa;;AAC5C,gBAAI,IAAI,SAAS;AACf,oBAAM,WAAU,eAAI,WAAJ,mBAAY,YAAZ,YAAuB,CAAC;AAC/B,uBAAA,QAAQ,QAAQ,IAAI,CAAC,SAAS,KAAK,MAAM,QAAQ,CAAC,EAAE,KAAK,GAAG;AAAA,YAAA,OAChE;AACL,sBAAQ,IAAI,OAAO;AAAA,YAAA;AAAA,UACrB,CACD;AAAA,QAAA;AAAA,MACH,OACK;AACL,iBAAS,QAAQ;AAAA,MAAA;AAAA,IAErB;AAEA,UAAM,cAAc,MAAM;AACxB,gBAAU,QAAQ;AAAA,IACpB;AACM,UAAA,eAAe,CAAC,SAAS;AACvB,YAAA,SAAS,KAAK,IAAI,CAAC,SAAS,KAAK,MAAM,MAAM,CAAC,EAAE,KAAK,GAAG;AACxD,YAAA,WAAW,KAAK,IAAI,CAAC,SAAS,KAAK,MAAM,QAAQ,CAAC,EAAE,KAAK,GAAG;AAClE,eAAS,QAAQ;AACjB,WAAK,qBAAqB,MAAM;AAChC,WAAK,UAAU,MAAM;AAAA,IACvB;AAEAC,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,MAAM;AACM,kBAAA;AAAA,MACZ;AAAA,MACA,EAAE,WAAW,KAAK;AAAA,IACpB;;;;;;;;;;;;;;;;;;;;;;;;;AChHA,GAAG,gBAAgBC,SAAS;"}