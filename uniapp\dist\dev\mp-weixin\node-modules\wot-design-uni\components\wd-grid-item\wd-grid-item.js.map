{"version": 3, "file": "wd-grid-item.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-grid-item/wd-grid-item.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1ncmlkLWl0ZW0vd2QtZ3JpZC1pdGVtLnZ1ZQ"], "sourcesContent": ["<template>\n  <view :class=\"`wd-grid-item ${border && !gutter ? itemClass : ''} ${customClass}`\" @click=\"click\" :style=\"`${style};${customStyle}`\">\n    <view\n      :class=\"`wd-grid-item__content ${square ? 'is-square' : ''} ${border && gutter > 0 ? 'is-round' : ''}`\"\n      :style=\"gutterContentStyle\"\n      :hover-class=\"hoverClass\"\n    >\n      <slot v-if=\"useSlot\" />\n      <template v-else>\n        <view :style=\"'width:' + iconSize + '; height: ' + iconSize\" class=\"wd-grid-item__wrapper\">\n          <wd-badge custom-class=\"badge\" v-bind=\"customBadgeProps\">\n            <template v-if=\"useIconSlot\">\n              <slot name=\"icon\" />\n            </template>\n            <wd-icon v-else :name=\"icon\" :size=\"iconSize\" :custom-class=\"customIcon\" />\n          </wd-badge>\n        </view>\n        <slot name=\"text\" v-if=\"useTextSlot\" />\n        <view v-else class=\"wd-grid-item__text custom-text\">{{ text }}</view>\n      </template>\n    </view>\n  </view>\n</template>\n<script lang=\"ts\">\nexport default {\n  name: 'wd-grid-item',\n  options: {\n    virtualHost: true,\n    addGlobalClass: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport wdIcon from '../wd-icon/wd-icon.vue'\nimport wdBadge from '../wd-badge/wd-badge.vue'\nimport { onMounted, ref, watch, computed } from 'vue'\nimport { useParent } from '../composables/useParent'\nimport { GRID_KEY } from '../wd-grid/types'\nimport { deepAssign, isDef, isUndefined, omitBy } from '../common/util'\nimport { gridItemProps } from './types'\nimport type { BadgeProps } from '../wd-badge/types'\n\nconst props = defineProps(gridItemProps)\nconst emit = defineEmits(['itemclick'])\n\nconst style = ref<string>('')\nconst gutterContentStyle = ref<string>('')\nconst itemClass = ref<string>('')\nconst gutter = ref<number>(0)\nconst square = ref<boolean>(false)\nconst border = ref<boolean>(true)\nconst { parent: grid } = useParent(GRID_KEY)\n\nconst childCount = computed(() => {\n  if (isDef(grid) && isDef(grid.children)) {\n    return grid.children.length\n  } else {\n    return 0\n  }\n})\n\nconst customBadgeProps = computed(() => {\n  const badgeProps: Partial<BadgeProps> = deepAssign(\n    isDef(props.badgeProps) ? omitBy(props.badgeProps, isUndefined) : {},\n    omitBy(\n      {\n        max: props.max,\n        isDot: props.isDot,\n        modelValue: props.value,\n        type: props.type\n      },\n      isUndefined\n    )\n  )\n  return badgeProps\n})\n\nwatch(\n  () => childCount.value,\n  () => {\n    if (!grid) return\n    const width = grid.props.column ? 100 / grid.props.column + '%' : 100 / (childCount.value || 1) + '%'\n    // 单独定义间隔\n    const gutterStyle = grid.props.gutter ? `padding:${grid.props.gutter}px ${grid.props.gutter}px 0 0; background-color: transparent;` : ''\n    // 单独定义正方形\n    const squareStyle = grid.props.square ? `background-color:transparent; padding-bottom: 0; padding-top:${width}` : ''\n    style.value = `width: ${width}; ${squareStyle || gutterStyle}`\n  },\n  {\n    deep: true,\n    immediate: true\n  }\n)\n\nonMounted(() => {\n  init()\n})\n\nfunction init() {\n  if (!grid) return\n  const children = grid.children\n  const width = grid.props.column ? 100 / grid.props.column + '%' : 100 / children.length + '%'\n  // 单独定义间隔\n  const gutterStyle = grid.props.gutter ? `padding:${grid.props.gutter}px ${grid.props.gutter}px 0 0; background-color: transparent;` : ''\n  // 单独定义正方形\n  const squareStyle = grid.props.square ? `background-color:transparent; padding-bottom: 0; padding-top:${width}` : ''\n  // 间隔+正方形\n  gutterContentStyle.value =\n    grid.props.gutter && grid.props.square\n      ? `right: ${grid.props.gutter}px; bottom:${grid.props.gutter}px;height: auto; background-color: ${grid.props.bgColor}`\n      : `background-color: ${grid.props.bgColor}`\n\n  border.value = Boolean(grid.props.border)\n  square.value = Boolean(grid.props.square)\n  gutter.value = Number(grid.props.gutter)\n  style.value = `width: ${width}; ${squareStyle || gutterStyle}`\n}\n\nfunction click() {\n  if (grid && !grid.props.clickable) return\n  const { url, linkType } = props\n  emit('itemclick')\n  if (url) {\n    switch (linkType) {\n      case 'navigateTo':\n        uni.navigateTo({\n          url\n        })\n        break\n      case 'reLaunch':\n        uni.reLaunch({\n          url\n        })\n        break\n      case 'redirectTo':\n        uni.redirectTo({\n          url\n        })\n        break\n      case 'switchTab':\n        uni.switchTab({\n          url\n        })\n        break\n      default:\n        console.error(`[wot-design] warning(wd-grid-item): linkType can not be ${linkType}`)\n        break\n    }\n  }\n}\n/**\n * 设置样式\n * @param classes\n */\nfunction setiIemClass(classes: string) {\n  itemClass.value = classes\n}\n\nconst hoverClass = computed(() => {\n  if (grid?.props.clickable) {\n    return grid.props.hoverClass ? grid.props.hoverClass : 'wd-grid-item__content--hover'\n  }\n  return 'none'\n})\n\ndefineExpose({\n  setiIemClass,\n  itemClass,\n  init\n})\n</script>\n\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-grid-item/wd-grid-item.vue'\nwx.createComponent(Component)"], "names": ["ref", "useParent", "GRID_KEY", "computed", "isDef", "deepAssign", "omitBy", "isUndefined", "watch", "onMounted", "uni"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAmCA,MAAA,SAAmB,MAAA;AACnB,MAAA,UAAoB,MAAA;AAZpB,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,EAAA;AAEpB;;;;;AAaA,UAAM,QAAQ;AACd,UAAM,OAAO;AAEP,UAAA,QAAQA,kBAAY,EAAE;AACtB,UAAA,qBAAqBA,kBAAY,EAAE;AACnC,UAAA,YAAYA,kBAAY,EAAE;AAC1B,UAAA,SAASA,kBAAY,CAAC;AACtB,UAAA,SAASA,kBAAa,KAAK;AAC3B,UAAA,SAASA,kBAAa,IAAI;AAChC,UAAM,EAAE,QAAQ,SAASC,cAAAA,UAAUC,cAAAA,QAAQ;AAErC,UAAA,aAAaC,cAAAA,SAAS,MAAM;AAChC,UAAIC,cAAAA,MAAM,IAAI,KAAKA,cAAM,MAAA,KAAK,QAAQ,GAAG;AACvC,eAAO,KAAK,SAAS;AAAA,MAAA,OAChB;AACE,eAAA;AAAA,MAAA;AAAA,IACT,CACD;AAEK,UAAA,mBAAmBD,cAAAA,SAAS,MAAM;AACtC,YAAM,aAAkCE,cAAA;AAAA,QACtCD,oBAAM,MAAM,UAAU,IAAIE,cAAAA,OAAO,MAAM,YAAYC,cAAW,WAAA,IAAI,CAAC;AAAA,QACnED,cAAA;AAAA,UACE;AAAA,YACE,KAAK,MAAM;AAAA,YACX,OAAO,MAAM;AAAA,YACb,YAAY,MAAM;AAAA,YAClB,MAAM,MAAM;AAAA,UACd;AAAA,UACAC,cAAAA;AAAAA,QAAA;AAAA,MAEJ;AACO,aAAA;AAAA,IAAA,CACR;AAEDC,kBAAA;AAAA,MACE,MAAM,WAAW;AAAA,MACjB,MAAM;AACJ,YAAI,CAAC;AAAM;AACX,cAAM,QAAQ,KAAK,MAAM,SAAS,MAAM,KAAK,MAAM,SAAS,MAAM,OAAO,WAAW,SAAS,KAAK;AAElG,cAAM,cAAc,KAAK,MAAM,SAAS,WAAW,KAAK,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,2CAA2C;AAEtI,cAAM,cAAc,KAAK,MAAM,SAAS,gEAAgE,KAAK,KAAK;AAClH,cAAM,QAAQ,UAAU,KAAK,KAAK,eAAe,WAAW;AAAA,MAC9D;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,WAAW;AAAA,MAAA;AAAA,IAEf;AAEAC,kBAAAA,UAAU,MAAM;AACT,WAAA;AAAA,IAAA,CACN;AAED,aAAS,OAAO;AACd,UAAI,CAAC;AAAM;AACX,YAAM,WAAW,KAAK;AAChB,YAAA,QAAQ,KAAK,MAAM,SAAS,MAAM,KAAK,MAAM,SAAS,MAAM,MAAM,SAAS,SAAS;AAE1F,YAAM,cAAc,KAAK,MAAM,SAAS,WAAW,KAAK,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,2CAA2C;AAEtI,YAAM,cAAc,KAAK,MAAM,SAAS,gEAAgE,KAAK,KAAK;AAE/F,yBAAA,QACjB,KAAK,MAAM,UAAU,KAAK,MAAM,SAC5B,UAAU,KAAK,MAAM,MAAM,cAAc,KAAK,MAAM,MAAM,sCAAsC,KAAK,MAAM,OAAO,KAClH,qBAAqB,KAAK,MAAM,OAAO;AAE7C,aAAO,QAAQ,QAAQ,KAAK,MAAM,MAAM;AACxC,aAAO,QAAQ,QAAQ,KAAK,MAAM,MAAM;AACxC,aAAO,QAAQ,OAAO,KAAK,MAAM,MAAM;AACvC,YAAM,QAAQ,UAAU,KAAK,KAAK,eAAe,WAAW;AAAA,IAAA;AAG9D,aAAS,QAAQ;AACX,UAAA,QAAQ,CAAC,KAAK,MAAM;AAAW;AAC7B,YAAA,EAAE,KAAK,SAAA,IAAa;AAC1B,WAAK,WAAW;AAChB,UAAI,KAAK;AACP,gBAAQ,UAAU;AAAA,UAChB,KAAK;AACHC,0BAAAA,MAAI,WAAW;AAAA,cACb;AAAA,YAAA,CACD;AACD;AAAA,UACF,KAAK;AACHA,0BAAAA,MAAI,SAAS;AAAA,cACX;AAAA,YAAA,CACD;AACD;AAAA,UACF,KAAK;AACHA,0BAAAA,MAAI,WAAW;AAAA,cACb;AAAA,YAAA,CACD;AACD;AAAA,UACF,KAAK;AACHA,0BAAAA,MAAI,UAAU;AAAA,cACZ;AAAA,YAAA,CACD;AACD;AAAA,UACF;AACU,oBAAA,MAAM,2DAA2D,QAAQ,EAAE;AACnF;AAAA,QAAA;AAAA,MACJ;AAAA,IACF;AAMF,aAAS,aAAa,SAAiB;AACrC,gBAAU,QAAQ;AAAA,IAAA;AAGd,UAAA,aAAaP,cAAAA,SAAS,MAAM;AAC5B,UAAA,6BAAM,MAAM,WAAW;AACzB,eAAO,KAAK,MAAM,aAAa,KAAK,MAAM,aAAa;AAAA,MAAA;AAElD,aAAA;AAAA,IAAA,CACR;AAEY,aAAA;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1KD,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}