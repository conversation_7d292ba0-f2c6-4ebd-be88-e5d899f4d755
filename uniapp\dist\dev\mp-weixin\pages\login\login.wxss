/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.header.data-v-cdfe2409 {
  margin-top: var(--status-bar-height);
}
.page-container.data-v-cdfe2409 {
  padding: 0 20rpx;
  padding-top: 8vh;
  position: relative;
  font-size: 15px;
  color: var(--UI-FG-0);
  background-image: url("https://www.mograine.cn/images/background.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.page-container .logo-section.data-v-cdfe2409 {
  text-align: center;
  padding-top: 2vh;
}
.page-container .content-wrapper.data-v-cdfe2409 {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  text-align: center;
  padding-top: 6vh;
}
.page-container .logo.data-v-cdfe2409 {
  width: 400rpx;
  height: 200rpx;
}
.page-container .title.data-v-cdfe2409 {
  font-size: 80rpx;
  color: white !important;
}
.page-container .enter-area.data-v-cdfe2409 {
  padding-top: 8vh;
  width: 87%;
  margin: 0 auto;
}
.page-container .enter-area .box.data-v-cdfe2409 {
  display: flex;
  align-items: center;
  min-height: 100rpx;
  color: #000;
  border: 1px solid #eee;
  background-color: #fff;
  padding: 0 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
}
.page-container .enter-area .box.data-v-cdfe2409 .wd-text {
  margin: 0 10rpx;
  color: white !important;
}
.page-container .enter-area .box.data-v-cdfe2409 .wd-input,
.page-container .enter-area .box.data-v-cdfe2409 .uni-input {
  flex: 1;
}
.page-container .enter-area .box.data-v-cdfe2409 .wd-input::after,
.page-container .enter-area .box.data-v-cdfe2409 .uni-input::after {
  height: 0;
}
.page-container .enter-area .box .uni-input.data-v-cdfe2409 {
  text-align: left;
  font-size: var(--wot-input-fs, var(--wot-cell-title-fs, 14px));
  height: var(--wot-input-inner-height, 34px);
  color: var(--wot-input-color, #262626);
}
.page-container .enter-area .box .uni-input .uni-input-placeholder.data-v-cdfe2409 {
  color: var(--wot-input-placeholder-color, #bfbfbf);
}
.page-container .enter-area .box.user-category.data-v-cdfe2409 {
  flex-direction: column;
  background-color: transparent;
  border: none;
  box-shadow: none;
}
.page-container .enter-area .box.user-category .user-category-options.data-v-cdfe2409 {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-top: 20px;
  gap: 10px;
}
.page-container .enter-area .box.user-category .user-category-options.data-v-cdfe2409 .wd-button {
  --wot-button-medium-height: 50px;
  --wot-button-medium-fs: 16px;
  width: 100%;
  text-align: center;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.3s ease;
  background-color: white;
  color: #333;
}
.page-container .enter-area .box.user-category .user-category-options.data-v-cdfe2409 .wd-button.active {
  background-color: #07C160 !important;
  color: white !important;
  border-color: #07C160 !important;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
  font-weight: 500;
}
.page-container .enter-area .box.user-category .user-category-options .user-category-option.active.data-v-cdfe2409 .wd-button {
  background-color: #07C160 !important;
  color: white !important;
  border-color: #07C160 !important;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
  font-weight: 500;
}
.page-container .enter-area.data-v-cdfe2409 .sendSMSBtn {
  margin-left: 20rpx;
}
.page-container .enter-area.data-v-cdfe2409 .wd-icon-view,
.page-container .enter-area.data-v-cdfe2409 .wd-icon-eye-close {
  color: #555;
}