{"version": 3, "file": "z-paging-enum.js", "sources": ["../../../../../../../../src/uni_modules/z-paging/components/z-paging/js/z-paging-enum.js"], "sourcesContent": ["// [z-paging]枚举\r\n\r\nexport default {\r\n\t// 当前加载类型 refresher:下拉刷新 load-more:上拉加载更多\r\n\tLoadingType: {\r\n\t\tRefresher: 'refresher',\r\n\t\tLoadMore: 'load-more'\r\n\t},\r\n\t// 下拉刷新状态 default:默认状态 release-to-refresh:松手立即刷新 loading:刷新中 complete:刷新结束 go-f2:松手进入二楼\r\n\tRefresher: {\r\n\t\tDefault: 'default',\r\n\t\tReleaseToRefresh: 'release-to-refresh',\r\n\t\tLoading: 'loading',\r\n\t\tComplete: 'complete',\r\n\t\tGoF2: 'go-f2'\r\n\t},\r\n\t// 底部加载更多状态 default:默认状态 loading:加载中 no-more:没有更多数据 fail:加载失败\r\n\tMore: {\r\n\t\tDefault: 'default',\r\n\t\tLoading: 'loading',\r\n\t\tNoMore: 'no-more',\r\n\t\tFail: 'fail'\r\n\t},\r\n\t// @query触发来源 user-pull-down:用户主动下拉刷新 reload:通过reload触发 refresh:通过refresh触发 load-more:通过滚动到底部加载更多或点击底部加载更多触发\r\n\tQueryFrom: {\r\n\t\tUserPullDown: 'user-pull-down',\r\n\t\tReload: 'reload',\r\n\t\tRefresh: 'refresh',\r\n\t\tLoadMore: 'load-more'\r\n\t},\r\n\t// 虚拟列表cell高度模式\r\n\tCellHeightMode: {\r\n\t\t// 固定高度\r\n\t\tFixed: 'fixed',\r\n\t\t// 动态高度\r\n\t\tDynamic: 'dynamic'\r\n\t},\r\n\t// 列表缓存模式\r\n\tCacheMode: {\r\n\t\t// 默认模式，只会缓存一次\r\n\t\tDefault: 'default',\r\n\t\t// 总是缓存，每次列表刷新(下拉刷新、调用reload等)都会更新缓存\r\n\t\tAlways: 'always'\r\n\t}\r\n}"], "names": [], "mappings": ";AAEA,MAAe,OAAA;AAAA;AAAA,EAEd,aAAa;AAAA,IACZ,WAAW;AAAA,IACX,UAAU;AAAA,EACV;AAAA;AAAA,EAED,WAAW;AAAA,IACV,SAAS;AAAA,IACT,kBAAkB;AAAA,IAClB,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,EACN;AAAA;AAAA,EAED,MAAM;AAAA,IACL,SAAS;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,MAAM;AAAA,EACN;AAAA;AAAA,EAED,WAAW;AAAA,IACV,cAAc;AAAA,IACd,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,UAAU;AAAA,EACV;AAAA;AAAA,EAED,gBAAgB;AAAA;AAAA,IAEf,OAAO;AAAA;AAAA,IAEP,SAAS;AAAA,EACT;AAAA;AAAA,EAED,WAAW;AAAA;AAAA,IAEV,SAAS;AAAA;AAAA,IAET,QAAQ;AAAA,EACR;AACF;;"}