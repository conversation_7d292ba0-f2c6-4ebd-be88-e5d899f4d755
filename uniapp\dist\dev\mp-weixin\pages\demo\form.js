"use strict";
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _easycom_wd_input2 = common_vendor.resolveComponent("wd-input");
  const _easycom_wd_picker2 = common_vendor.resolveComponent("wd-picker");
  const _easycom_wd_cell_group2 = common_vendor.resolveComponent("wd-cell-group");
  const _easycom_wd_select_picker2 = common_vendor.resolveComponent("wd-select-picker");
  const _easycom_wd_calendar2 = common_vendor.resolveComponent("wd-calendar");
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _easycom_wd_form2 = common_vendor.resolveComponent("wd-form");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_input2 + _easycom_wd_picker2 + _easycom_wd_cell_group2 + _easycom_wd_select_picker2 + _easycom_wd_calendar2 + _easycom_wd_button2 + _easycom_wd_form2 + _easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_wd_input = () => "../../node-modules/wot-design-uni/components/wd-input/wd-input.js";
const _easycom_wd_picker = () => "../../node-modules/wot-design-uni/components/wd-picker/wd-picker.js";
const _easycom_wd_cell_group = () => "../../node-modules/wot-design-uni/components/wd-cell-group/wd-cell-group.js";
const _easycom_wd_select_picker = () => "../../node-modules/wot-design-uni/components/wd-select-picker/wd-select-picker.js";
const _easycom_wd_calendar = () => "../../node-modules/wot-design-uni/components/wd-calendar/wd-calendar.js";
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
const _easycom_wd_form = () => "../../node-modules/wot-design-uni/components/wd-form/wd-form.js";
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  (_easycom_wd_input + _easycom_wd_picker + _easycom_wd_cell_group + _easycom_wd_select_picker + _easycom_wd_calendar + _easycom_wd_button + _easycom_wd_form + _easycom_PageLayout)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "form",
  setup(__props) {
    const { success: showSuccess } = common_vendor.useToast();
    const columns = common_vendor.ref([
      { value: "1", label: "男" },
      { value: "2", label: "女" }
    ]);
    const selectColumns = common_vendor.ref([
      {
        value: "101",
        label: "篮球"
      },
      {
        value: "102",
        label: "足球"
      },
      {
        value: "103",
        label: "棒球"
      }
    ]);
    const model = common_vendor.reactive({
      value1: "",
      value2: "",
      value3: "",
      value4: [],
      value5: 0
    });
    const form = common_vendor.ref();
    function handleSubmit() {
      form.value.validate().then(({ valid, errors }) => {
        if (valid) {
          console.log("model:", model);
          showSuccess({
            msg: "校验通过"
          });
        }
      }).catch((error) => {
        console.log(error, "error");
      });
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(($event) => common_vendor.unref(model).value1 = $event),
        b: common_vendor.p({
          label: "姓名",
          ["label-width"]: "80px",
          prop: "value1",
          clearable: true,
          placeholder: "姓名",
          rules: [{
            required: true,
            message: "请填写姓名"
          }],
          modelValue: common_vendor.unref(model).value1
        }),
        c: common_vendor.o(($event) => common_vendor.unref(model).value3 = $event),
        d: common_vendor.p({
          label: "性别",
          ["label-width"]: "80px",
          prop: "value3",
          columns: common_vendor.unref(columns),
          rules: [{
            required: true,
            message: "请选择性别"
          }],
          modelValue: common_vendor.unref(model).value3
        }),
        e: common_vendor.p({
          border: true
        }),
        f: common_vendor.o(($event) => common_vendor.unref(model).value2 = $event),
        g: common_vendor.p({
          label: "密码",
          ["label-width"]: "80px",
          prop: "value2",
          ["show-password"]: true,
          clearable: true,
          placeholder: "请输入密码",
          rules: [{
            required: true,
            message: "请填写密码"
          }],
          modelValue: common_vendor.unref(model).value2
        }),
        h: common_vendor.o(($event) => common_vendor.unref(model).value4 = $event),
        i: common_vendor.p({
          label: "爱好",
          ["label-width"]: "80px",
          prop: "value4",
          columns: common_vendor.unref(selectColumns),
          rules: [{
            required: true,
            message: "请选择爱好"
          }],
          modelValue: common_vendor.unref(model).value4
        }),
        j: common_vendor.p({
          border: true
        }),
        k: common_vendor.o(($event) => common_vendor.unref(model).value5 = $event),
        l: common_vendor.p({
          ["label-width"]: "80px",
          prop: "value5",
          label: "   出生",
          modelValue: common_vendor.unref(model).value5
        }),
        m: common_vendor.p({
          border: true
        }),
        n: common_vendor.o(handleSubmit),
        o: common_vendor.p({
          type: "primary",
          size: "large",
          block: true
        }),
        p: common_vendor.sr(form, "c2907ca1-2,c2907ca1-1", {
          "k": "form"
        }),
        q: common_vendor.p({
          model: common_vendor.unref(model)
        }),
        r: common_vendor.p({
          navTitle: "表单"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-c2907ca1"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=form.js.map
