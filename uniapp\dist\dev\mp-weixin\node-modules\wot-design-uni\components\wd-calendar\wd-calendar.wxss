/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 辅助函数
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/* 转换成字符串 */
/* 判断是否存在 Modifier */
/* 判断是否存在伪类 */
/**
 * 主题色切换
 * @params $theme-color 主题色
 * @params $type 变暗’dark‘ 变亮 'light'
 * @params $mix-color 自己设置的混色
 */
/**
 * 颜色结果切换， 如果开启线性渐变色 使用渐变色，如果没有开启，那么使用主题色
 * @params $open-linear 是否开启线性渐变色
 * @params $deg 渐变色角度
 * @params $theme-color 当前配色
 * @params [Array] $set 主题色明暗设置，与 $color-list 数量对应
 * @params [Array] $color-list 渐变色顺序， $color-list 和 $per-list 数量相同
 * @params [Array] $per-list 渐变色比例
 */
/**
 * UI规范基础变量
 */
/*----------------------------------------- Theme color. start ----------------------------------------*/
/* 主题颜色 */
/* 辅助色 */
/* 文字颜色（默认浅色背景下 */
/* 暗黑模式 */
/* 图形颜色 */
/*----------------------------------------- Theme color. end -------------------------------------------*/
/*-------------------------------- Theme color application size.  start --------------------------------*/
/* 文字字号 */
/* 文字字重 */
/* 尺寸 */
/*-------------------------------- Theme color application size.  end --------------------------------*/
/* component var */
/* action-sheet */
/* badge */
/* button */
/* cell */
/* calendar */
/* checkbox */
/* collapse */
/* divider */
/* drop-menu */
/* input-number */
/* input */
/* textarea */
/* loadmore */
/* message-box */
/* notice-bar */
/* pagination */
/* picker */
/* col-picker */
/* overlay */
/* popup */
/* progress */
/* radio */
/* search */
/* slider */
/* sort-button */
/* steps */
/* switch */
/* tabs */
/* tag */
/* toast */
/* loading */
/* tooltip */
/* popover */
/* grid-item */
/* statustip */
/* card */
/* upload */
/* curtain */
/* notify */
/* skeleton */
/* circle */
/* swiper */
/* swiper-nav */
/* segmented */
/* tabbar */
/* tabbar-item */
/* navbar */
/* navbar-capsule */
/* table */
/* sidebar */
/* sidebar-item */
/* fab */
/* count-down */
/* keyboard */
/* number-keyboard */
/* passwod-input */
/* form-item */
/* backtop */
/* index-bar */
/* text */
/* video-preview */
/* img-cropper */
/* floating-panel */
/* signature */
/**
 * 混合宏
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/**
 * 辅助函数
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/* 转换成字符串 */
/* 判断是否存在 Modifier */
/* 判断是否存在伪类 */
/**
 * 主题色切换
 * @params $theme-color 主题色
 * @params $type 变暗’dark‘ 变亮 'light'
 * @params $mix-color 自己设置的混色
 */
/**
 * 颜色结果切换， 如果开启线性渐变色 使用渐变色，如果没有开启，那么使用主题色
 * @params $open-linear 是否开启线性渐变色
 * @params $deg 渐变色角度
 * @params $theme-color 当前配色
 * @params [Array] $set 主题色明暗设置，与 $color-list 数量对应
 * @params [Array] $color-list 渐变色顺序， $color-list 和 $per-list 数量相同
 * @params [Array] $per-list 渐变色比例
 */
/**
  * BEM，定义块（b)
  */
/* 定义元素（e），对于伪类，会自动将 e 嵌套在 伪类 底下 */
/* 此方法用于生成穿透样式 */
/* 定义元素（e），对于伪类，会自动将 e 嵌套在 伪类 底下 */
/* 定义状态（m） */
/* 定义状态（m） */
/* 对于需要需要嵌套在 m 底下的 e，调用这个混合宏，一般在切换整个组件的状态，如切换颜色的时候 */
/* 状态，生成 is-$state 类名 */
/**
  * 常用混合宏
  */
/* 单行超出隐藏 */
/* 多行超出隐藏 */
/* 清除浮动 */
/* 0.5px 边框 指定方向*/
/* 0.5px 边框 环绕 */
/**
  * 三角形实现尖角样式，适用于背景透明情况
  * @param $size 三角形高，底边为 $size * 2
  * @param $bg 三角形背景颜色
  */
/**
  * 正方形实现尖角样式，适用于背景不透明情况
  * @param $size 正方形边长
  * @param $bg 正方形背景颜色
  * @param $z-index z-index属性值，不得大于外部包裹器
  * @param $box-shadow 阴影
*/
.wot-theme-dark .wd-calendar__cell.data-v-f9f098b9 {
  background-color: var(--wot-dark-background2, #1b1b1b);
  color: var(--wot-dark-color, var(--wot-color-white, rgb(255, 255, 255)));
}
.wot-theme-dark .wd-calendar__label.data-v-f9f098b9 {
  color: var(--wot-dark-color, var(--wot-color-white, rgb(255, 255, 255)));
}
.wot-theme-dark .wd-calendar__value.data-v-f9f098b9 {
  color: var(--wot-dark-color, var(--wot-color-white, rgb(255, 255, 255)));
}
.wot-theme-dark .wd-calendar__title.data-v-f9f098b9 {
  color: var(--wot-dark-color, var(--wot-color-white, rgb(255, 255, 255)));
}
.wot-theme-dark .wd-calendar.data-v-f9f098b9 .wd-calendar__arrow,
.wot-theme-dark .wd-calendar.data-v-f9f098b9 .wd-calendar__close {
  color: var(--wot-dark-color, var(--wot-color-white, rgb(255, 255, 255)));
}
.wot-theme-dark .wd-calendar.is-border .wd-calendar__cell.data-v-f9f098b9 {
  position: relative;
}
.wot-theme-dark .wd-calendar.is-border .wd-calendar__cell.data-v-f9f098b9::after {
  position: absolute;
  display: block;
  content: "";
  width: calc(100% - var(--wot-cell-padding, var(--wot-size-side-padding, 15px)));
  height: 1px;
  left: var(--wot-cell-padding, var(--wot-size-side-padding, 15px));
  top: 0;
  transform: scaleY(0.5);
  background: var(--wot-dark-border-color, #3a3a3c);
}
.wot-theme-dark .wd-calendar__range-label-item.data-v-f9f098b9 {
  color: var(--wot-dark-color, var(--wot-color-white, rgb(255, 255, 255)));
}
.wot-theme-dark .wd-calendar__range-label-item.is-placeholder.data-v-f9f098b9 {
  color: var(--wot-dark-color-gray, var(--wot-color-secondary, #595959));
}
.wot-theme-dark .wd-calendar__range-sperator.data-v-f9f098b9 {
  color: var(--wot-dark-color-gray, var(--wot-color-secondary, #595959));
}
.wd-calendar.is-border .wd-calendar__cell.data-v-f9f098b9 {
  position: relative;
}
.wd-calendar.is-border .wd-calendar__cell.data-v-f9f098b9::after {
  position: absolute;
  display: block;
  content: "";
  width: calc(100% - var(--wot-cell-padding, var(--wot-size-side-padding, 15px)));
  height: 1px;
  left: var(--wot-cell-padding, var(--wot-size-side-padding, 15px));
  top: 0;
  transform: scaleY(0.5);
  background: var(--wot-color-border-light, #e8e8e8);
}
.wd-calendar__cell.data-v-f9f098b9 {
  position: relative;
  display: flex;
  padding: var(--wot-cell-wrapper-padding, 10px) var(--wot-cell-padding, var(--wot-size-side-padding, 15px));
  align-items: flex-start;
  background-color: var(--wot-color-white, rgb(255, 255, 255));
  text-decoration: none;
  color: var(--wot-cell-title-color, rgba(0, 0, 0, 0.85));
  font-size: var(--wot-cell-title-fs, 14px);
  overflow: hidden;
  line-height: var(--wot-cell-line-height, 24px);
}
.wd-calendar__cell.is-disabled .wd-calendar__value.data-v-f9f098b9 {
  color: var(--wot-input-disabled-color, #d9d9d9);
}
.wd-calendar__cell.is-align-right .wd-calendar__value.data-v-f9f098b9 {
  text-align: right;
}
.wd-calendar__cell.is-error .wd-calendar__value.data-v-f9f098b9 {
  color: var(--wot-input-error-color, var(--wot-color-danger, #fa4350));
}
.wd-calendar__cell.is-error.data-v-f9f098b9 .wd-calendar__arrow {
  color: var(--wot-input-error-color, var(--wot-color-danger, #fa4350));
}
.wd-calendar__cell.is-large.data-v-f9f098b9 {
  font-size: var(--wot-cell-title-fs-large, 16px);
}
.wd-calendar__cell.is-large.data-v-f9f098b9 .wd-calendar__arrow {
  font-size: var(--wot-cell-icon-size-large, 18px);
}
.wd-calendar__cell.is-center.data-v-f9f098b9 {
  align-items: center;
}
.wd-calendar__cell.is-center.data-v-f9f098b9 .wd-calendar__arrow {
  margin-top: 0;
}
.wd-calendar__error-message.data-v-f9f098b9 {
  color: var(--wot-form-item-error-message-color, var(--wot-color-danger, #fa4350));
  font-size: var(--wot-form-item-error-message-font-size, var(--wot-fs-secondary, 12px));
  line-height: var(--wot-form-item-error-message-line-height, 24px);
  text-align: left;
  vertical-align: middle;
}
.wd-calendar__label.data-v-f9f098b9 {
  position: relative;
  width: var(--wot-input-cell-label-width, 33%);
  margin-right: var(--wot-cell-padding, var(--wot-size-side-padding, 15px));
  color: var(--wot-cell-title-color, rgba(0, 0, 0, 0.85));
  box-sizing: border-box;
}
.wd-calendar__label.is-required.data-v-f9f098b9 {
  padding-left: 12px;
}
.wd-calendar__label.is-required.data-v-f9f098b9::after {
  position: absolute;
  left: 0;
  top: 2px;
  content: "*";
  font-size: var(--wot-cell-required-size, 18px);
  line-height: 1.1;
  color: var(--wot-cell-required-color, var(--wot-color-danger, #fa4350));
}
.wd-calendar__value-wraper.data-v-f9f098b9 {
  display: flex;
}
.wd-calendar__value.data-v-f9f098b9 {
  flex: 1;
  margin-right: 10px;
  color: var(--wot-cell-value-color, rgba(0, 0, 0, 0.85));
}
.wd-calendar__value.is-ellipsis.data-v-f9f098b9 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.wd-calendar__value--placeholder.data-v-f9f098b9 {
  color: var(--wot-input-placeholder-color, #bfbfbf);
}
.wd-calendar__body.data-v-f9f098b9 {
  flex: 1;
}
.data-v-f9f098b9  .wd-calendar__arrow {
  display: block;
  font-size: var(--wot-cell-icon-size, 16px);
  color: var(--wot-cell-arrow-color, rgba(0, 0, 0, 0.25));
  line-height: var(--wot-cell-line-height, 24px);
}
.wd-calendar__header.data-v-f9f098b9 {
  position: relative;
  overflow: hidden;
}
.wd-calendar__title.data-v-f9f098b9 {
  color: var(--wot-action-sheet-color, rgba(0, 0, 0, 0.85));
  height: var(--wot-action-sheet-title-height, 64px);
  line-height: var(--wot-action-sheet-title-height, 64px);
  text-align: center;
  font-size: var(--wot-action-sheet-title-fs, var(--wot-fs-title, 16px));
  font-weight: var(--wot-action-sheet-weight, 500);
}
.data-v-f9f098b9  .wd-calendar__close {
  position: absolute;
  top: var(--wot-action-sheet-close-top, 25px);
  right: var(--wot-action-sheet-close-right, 15px);
  color: var(--wot-action-sheet-close-color, rgba(0, 0, 0, 0.65));
  font-size: var(--wot-action-sheet-close-fs, var(--wot-fs-title, 16px));
  transform: rotate(-45deg);
  line-height: 1.1;
}
.wd-calendar__tabs.data-v-f9f098b9 {
  width: 222px;
  margin: 10px auto 12px;
}
.wd-calendar__shortcuts.data-v-f9f098b9 {
  padding: 20px 0;
  text-align: center;
}
.data-v-f9f098b9  .wd-calendar__tag {
  margin-right: 8px;
}
.wd-calendar__view.is-show-confirm.data-v-f9f098b9 {
  height: 394px;
}
.wd-calendar__view.is-show-confirm.is-range.data-v-f9f098b9 {
  height: 384px;
}
.wd-calendar__range-label.data-v-f9f098b9 {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
}
.wd-calendar__range-label.is-monthrange.data-v-f9f098b9 {
  padding-bottom: 10px;
  box-shadow: 0px 4px 8px 0 rgba(0, 0, 0, 0.02);
}
.wd-calendar__range-label-item.data-v-f9f098b9 {
  flex: 1;
  color: rgba(0, 0, 0, 0.85);
}
.wd-calendar__range-label-item.is-placeholder.data-v-f9f098b9 {
  color: rgba(0, 0, 0, 0.25);
}
.wd-calendar__range-sperator.data-v-f9f098b9 {
  margin: 0 24px;
  color: rgba(0, 0, 0, 0.25);
}
.wd-calendar__confirm.data-v-f9f098b9 {
  padding: 12px 25px 14px;
}