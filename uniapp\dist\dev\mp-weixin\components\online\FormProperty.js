"use strict";
const FormProperty = (propertyId, formSchema, required = []) => {
  const _propertyId = propertyId;
  const _formSchem = formSchema;
  const _required = required;
  const getFormSchema = () => {
    return _formSchem || {};
  };
  const getKey = () => {
    return _propertyId;
  };
  const getType = () => {
    return getFormSchema().view;
  };
  const getDisabled = () => {
    if (_formSchem && _formSchem.ui && _formSchem.ui.widgetattrs && _formSchem.ui.widgetattrs.disabled === true) {
      return true;
    }
    return false;
  };
  const getLabel = () => {
    const schema = getFormSchema();
    return schema.title || getKey();
  };
  const getPlaceholder = () => {
    const viewType = getType();
    const label = getLabel();
    if (viewType.indexOf("date") >= 0 || viewType.indexOf("select") >= 0 || viewType.indexOf("list") >= 0) {
      return "请选择" + label;
    } else if (viewType.indexOf("upload") >= 0 || viewType.indexOf("file") >= 0 || viewType.indexOf("image") >= 0) {
      return "请上传" + label;
    } else {
      return "请输入" + label;
    }
  };
  const getDictStr = () => {
    const viewType = getType();
    if (viewType === "sel_search") {
      const schema = getFormSchema();
      return schema.dictTable + "," + schema.dictText + "," + schema.dictCode;
    }
    return "";
  };
  const getListSource = () => {
    const schema = getFormSchema();
    if (!schema.enum) {
      return [];
    }
    const arr = [...schema.enum];
    for (let a = 0; a < arr.length; a++) {
      if (!arr[a].label) {
        arr[a].label = arr[a].text;
      }
      if (schema.type === "number") {
        arr[a].value = parseInt(arr[a].value);
      }
    }
    return arr;
  };
  const getPopupCode = () => {
    return getFormSchema().code;
  };
  const getDest = () => {
    return getFormSchema().destFields;
  };
  const getOgn = () => {
    return getFormSchema().orgFields;
  };
  const getRules = () => {
    var _a;
    const rules = [];
    const isRequired = (_a = _required == null ? void 0 : _required.includes(getKey())) != null ? _a : false;
    if (isRequired) {
      let msg = getLabel() + "为必填项";
      rules.push({ required: true, message: msg });
    }
    let viewType = getType();
    if ("list" === viewType || "markdown" === viewType || "pca" === viewType) {
      return rules;
    }
    if (viewType.indexOf("upload") >= 0 || viewType.indexOf("file") >= 0 || viewType.indexOf("image") >= 0) {
      return rules;
    }
    const schema = getFormSchema();
    if (schema.pattern) {
      if (schema.pattern === "only") {
        rules.push({ validator: () => {
        } });
      } else if (schema.pattern === "z") {
        if (schema.type === "number" || schema.type === "integer")
          ;
        else {
          rules.push({ pattern: "^-?[1-9]\\d*$", message: "请输入整数" });
        }
      } else {
        let msg = getLabel() + "校验未通过";
        rules.push({ pattern: schema.pattern, message: msg });
      }
    }
    return rules;
  };
  return {
    get formSchema() {
      return getFormSchema();
    },
    get key() {
      return getKey();
    },
    get type() {
      return getType();
    },
    get disabled() {
      return getDisabled();
    },
    get label() {
      return getLabel();
    },
    get placeholder() {
      return getPlaceholder();
    },
    get dictStr() {
      return getDictStr();
    },
    get listSource() {
      return getListSource();
    },
    get popupCode() {
      return getPopupCode();
    },
    get dest() {
      return getDest();
    },
    get ogn() {
      return getOgn();
    },
    get rules() {
      return getRules();
    }
  };
};
exports.FormProperty = FormProperty;
//# sourceMappingURL=FormProperty.js.map
