"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../../../common/vendor.js");
if (!Array) {
  const _easycom_wd_tab2 = common_vendor.resolveComponent("wd-tab");
  const _easycom_wd_tabs2 = common_vendor.resolveComponent("wd-tabs");
  const _easycom_wd_tag2 = common_vendor.resolveComponent("wd-tag");
  (_easycom_wd_tab2 + _easycom_wd_tabs2 + _easycom_wd_tag2)();
}
const _easycom_wd_tab = () => "../wd-tab/wd-tab.js";
const _easycom_wd_tabs = () => "../wd-tabs/wd-tabs.js";
const _easycom_wd_tag = () => "../wd-tag/wd-tag.js";
if (!Math) {
  (wdIcon + _easycom_wd_tab + _easycom_wd_tabs + _easycom_wd_tag + wdCalendarView + wdButton + wdActionSheet)();
}
const wdIcon = () => "../wd-icon/wd-icon.js";
const wdCalendarView = () => "../wd-calendar-view/wd-calendar-view.js";
const wdActionSheet = () => "../wd-action-sheet/wd-action-sheet.js";
const wdButton = () => "../wd-button/wd-button.js";
const __default__ = {
  name: "wd-calendar",
  options: {
    addGlobalClass: true,
    virtualHost: true,
    styleIsolation: "shared"
  }
};
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, __default__), {
  props: common_vendor.calendarProps,
  emits: ["cancel", "change", "update:modelValue", "confirm", "open"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const { translate } = common_vendor.useTranslate("calendar");
    const defaultDisplayFormat = (value, type) => {
      switch (type) {
        case "date":
          return common_vendor.dayjs$1(value).format("YYYY-MM-DD");
        case "dates":
          return value.map((item) => {
            return common_vendor.dayjs$1(item).format("YYYY-MM-DD");
          }).join(", ");
        case "daterange":
          return `${value[0] ? common_vendor.dayjs$1(value[0]).format("YYYY-MM-DD") : translate("startTime")} ${translate("to")} ${value[1] ? common_vendor.dayjs$1(value[1]).format("YYYY-MM-DD") : translate("endTime")}`;
        case "datetime":
          return common_vendor.dayjs$1(value).format("YYYY-MM-DD HH:mm:ss");
        case "datetimerange":
          return `${value[0] ? common_vendor.dayjs$1(value[0]).format(translate("timeFormat")) : translate("startTime")} ${translate(
            "to"
          )}
${value[1] ? common_vendor.dayjs$1(value[1]).format(translate("timeFormat")) : translate("endTime")}`;
        case "week": {
          const date = new Date(value);
          const year = date.getFullYear();
          const week = common_vendor.getWeekNumber(value);
          const weekStart = new Date(date);
          weekStart.setDate(date.getDate() - date.getDay() + 1);
          const weekEnd = new Date(date);
          weekEnd.setDate(date.getDate() + (7 - date.getDay()));
          const adjustedYear = weekEnd.getFullYear() > year ? weekEnd.getFullYear() : year;
          return translate("weekFormat", adjustedYear, common_vendor.padZero(week));
        }
        case "weekrange": {
          const date1 = new Date(value[0]);
          const date2 = new Date(value[1]);
          const year1 = date1.getFullYear();
          const year2 = date2.getFullYear();
          const week1 = common_vendor.getWeekNumber(value[0]);
          const week2 = common_vendor.getWeekNumber(value[1]);
          const weekStart1 = new Date(date1);
          weekStart1.setDate(date1.getDate() - date1.getDay() + 1);
          const weekEnd1 = new Date(date1);
          weekEnd1.setDate(date1.getDate() + (7 - date1.getDay()));
          const weekStart2 = new Date(date2);
          weekStart2.setDate(date2.getDate() - date2.getDay() + 1);
          const weekEnd2 = new Date(date2);
          weekEnd2.setDate(date2.getDate() + (7 - date2.getDay()));
          const adjustedYear1 = weekEnd1.getFullYear() > year1 ? weekEnd1.getFullYear() : year1;
          const adjustedYear2 = weekEnd2.getFullYear() > year2 ? weekEnd2.getFullYear() : year2;
          return `${value[0] ? translate("weekFormat", adjustedYear1, common_vendor.padZero(week1)) : translate("startWeek")} - ${value[1] ? translate("weekFormat", adjustedYear2, common_vendor.padZero(week2)) : translate("endWeek")}`;
        }
        case "month":
          return common_vendor.dayjs$1(value).format("YYYY / MM");
        case "monthrange":
          return `${value[0] ? common_vendor.dayjs$1(value[0]).format("YYYY / MM") : translate("startMonth")} ${translate("to")} ${value[1] ? common_vendor.dayjs$1(value[1]).format("YYYY / MM") : translate("endMonth")}`;
      }
    };
    const formatRange = (value, rangeType, type) => {
      switch (type) {
        case "daterange":
          if (!value) {
            return rangeType === "end" ? translate("endTime") : translate("startTime");
          }
          return common_vendor.dayjs$1(value).format(translate("dateFormat"));
        case "datetimerange":
          if (!value) {
            return rangeType === "end" ? translate("endTime") : translate("startTime");
          }
          return common_vendor.dayjs$1(value).format(translate("timeFormat"));
        case "weekrange": {
          if (!value) {
            return rangeType === "end" ? translate("endWeek") : translate("startWeek");
          }
          const date = new Date(value);
          const year = date.getFullYear();
          const week = common_vendor.getWeekNumber(value);
          return translate("weekFormat", year, common_vendor.padZero(week));
        }
        case "monthrange":
          if (!value) {
            return rangeType === "end" ? translate("endMonth") : translate("startMonth");
          }
          return common_vendor.dayjs$1(value).format(translate("monthFormat"));
      }
    };
    const props = __props;
    const emit = __emit;
    const pickerShow = common_vendor.ref(false);
    const calendarValue = common_vendor.ref(null);
    const lastCalendarValue = common_vendor.ref(null);
    const panelHeight = common_vendor.ref(338);
    const confirmBtnDisabled = common_vendor.ref(true);
    const currentTab = common_vendor.ref(0);
    const lastTab = common_vendor.ref(0);
    const currentType = common_vendor.ref("date");
    const lastCurrentType = common_vendor.ref();
    const inited = common_vendor.ref(false);
    const cell = common_vendor.useCell();
    const calendarView = common_vendor.ref();
    const calendarTabs = common_vendor.ref();
    const rangeLabel = common_vendor.computed(() => {
      const [start, end] = common_vendor.deepClone(common_vendor.isArray(calendarValue.value) ? calendarValue.value : []);
      return [start, end].map((item, index) => {
        return (props.innerDisplayFormat || formatRange)(item, index === 0 ? "start" : "end", currentType.value);
      });
    });
    const showValue = common_vendor.computed(() => {
      if (!common_vendor.isArray(props.modelValue) && props.modelValue || common_vendor.isArray(props.modelValue) && props.modelValue.length) {
        return (props.displayFormat || defaultDisplayFormat)(props.modelValue, lastCurrentType.value || currentType.value);
      } else {
        return "";
      }
    });
    common_vendor.watch(
      () => props.modelValue,
      (val, oldVal) => {
        if (common_vendor.isEqual(val, oldVal))
          return;
        calendarValue.value = common_vendor.deepClone(val);
        confirmBtnDisabled.value = getConfirmBtnStatus(val);
      },
      {
        immediate: true
      }
    );
    common_vendor.watch(
      () => props.type,
      (newValue, oldValue) => {
        if (props.showTypeSwitch) {
          const tabs = ["date", "week", "month"];
          const rangeTabs = ["daterange", "weekrange", "monthrange"];
          const index = newValue.indexOf("range") > -1 ? rangeTabs.indexOf(newValue) || 0 : tabs.indexOf(newValue);
          currentTab.value = index;
        }
        panelHeight.value = props.showConfirm ? 338 : 400;
        currentType.value = common_vendor.deepClone(newValue);
      },
      {
        deep: true,
        immediate: true
      }
    );
    common_vendor.watch(
      () => props.showConfirm,
      (val) => {
        panelHeight.value = val ? 338 : 400;
      },
      {
        deep: true,
        immediate: true
      }
    );
    const { parent: form } = common_vendor.useParent(common_vendor.FORM_KEY);
    const errorMessage = common_vendor.computed(() => {
      if (form && props.prop && form.errorMessages && form.errorMessages[props.prop]) {
        return form.errorMessages[props.prop];
      } else {
        return "";
      }
    });
    const isRequired = common_vendor.computed(() => {
      let formRequired = false;
      if (form && form.props.rules) {
        const rules = form.props.rules;
        for (const key in rules) {
          if (Object.prototype.hasOwnProperty.call(rules, key) && key === props.prop && Array.isArray(rules[key])) {
            formRequired = rules[key].some((rule) => rule.required);
          }
        }
      }
      return props.required || props.rules.some((rule) => rule.required) || formRequired;
    });
    const range = common_vendor.computed(() => {
      return (type) => {
        return common_vendor.isRange(type);
      };
    });
    function scrollIntoView() {
      calendarView.value && calendarView.value && calendarView.value.$.exposed.scrollIntoView();
    }
    function open() {
      return __async(this, null, function* () {
        const { disabled, readonly } = props;
        if (disabled || readonly)
          return;
        inited.value = true;
        pickerShow.value = true;
        lastCalendarValue.value = common_vendor.deepClone(calendarValue.value);
        lastTab.value = currentTab.value;
        lastCurrentType.value = currentType.value;
        yield common_vendor.pause();
        scrollIntoView();
        setTimeout(() => {
          if (props.showTypeSwitch) {
            calendarTabs.value.scrollIntoView();
            calendarTabs.value.updateLineStyle(false);
          }
        }, 250);
        emit("open");
      });
    }
    function close() {
      pickerShow.value = false;
      setTimeout(() => {
        calendarValue.value = common_vendor.deepClone(lastCalendarValue.value);
        currentTab.value = lastTab.value;
        currentType.value = lastCurrentType.value || "date";
        confirmBtnDisabled.value = getConfirmBtnStatus(lastCalendarValue.value);
      }, 250);
      emit("cancel");
    }
    function handleTypeChange({ index }) {
      const tabs = ["date", "week", "month"];
      const rangeTabs = ["daterange", "weekrange", "monthrange"];
      const type = props.type.indexOf("range") > -1 ? rangeTabs[index] : tabs[index];
      currentTab.value = index;
      currentType.value = type;
    }
    function getConfirmBtnStatus(value) {
      let confirmBtnDisabled2 = false;
      if (props.type.indexOf("range") > -1 && (!common_vendor.isArray(value) || !value[0] || !value[1] || !value) || props.type === "dates" && (!common_vendor.isArray(value) || value.length === 0 || !value) || !value) {
        confirmBtnDisabled2 = true;
      }
      return confirmBtnDisabled2;
    }
    function handleChange({ value }) {
      calendarValue.value = common_vendor.deepClone(value);
      confirmBtnDisabled.value = getConfirmBtnStatus(value);
      emit("change", {
        value
      });
      if (!props.showConfirm && !confirmBtnDisabled.value) {
        handleConfirm();
      }
    }
    function handleConfirm() {
      if (props.beforeConfirm) {
        props.beforeConfirm({
          value: calendarValue.value,
          resolve: (isPass) => {
            isPass && onConfirm();
          }
        });
      } else {
        onConfirm();
      }
    }
    function onConfirm() {
      pickerShow.value = false;
      lastCurrentType.value = currentType.value;
      emit("update:modelValue", calendarValue.value);
      emit("confirm", {
        value: calendarValue.value,
        type: currentType.value
      });
    }
    function handleShortcutClick(index) {
      if (props.onShortcutsClick && typeof props.onShortcutsClick === "function") {
        calendarValue.value = common_vendor.deepClone(
          props.onShortcutsClick({
            item: props.shortcuts[index],
            index
          })
        );
        confirmBtnDisabled.value = getConfirmBtnStatus(calendarValue.value);
      }
      if (!props.showConfirm) {
        handleConfirm();
      }
    }
    __expose({
      close,
      open
    });
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: _ctx.withCell
      }, _ctx.withCell ? common_vendor.e$1({
        b: _ctx.$slots.default
      }, _ctx.$slots.default ? {} : common_vendor.e$1({
        c: _ctx.label || _ctx.$slots.label
      }, _ctx.label || _ctx.$slots.label ? {
        d: common_vendor.t(_ctx.label),
        e: common_vendor.n(`wd-calendar__label ${isRequired.value ? "is-required" : ""} ${_ctx.customLabelClass}`),
        f: common_vendor.s(_ctx.labelWidth ? "min-width:" + _ctx.labelWidth + ";max-width:" + _ctx.labelWidth + ";" : "")
      } : {}, {
        g: common_vendor.t(showValue.value || _ctx.placeholder || common_vendor.unref(translate)("placeholder")),
        h: common_vendor.n(`wd-calendar__value ${_ctx.ellipsis ? "is-ellipsis" : ""} ${_ctx.customValueClass} ${showValue.value ? "" : "wd-calendar__value--placeholder"}`),
        i: !_ctx.disabled && !_ctx.readonly
      }, !_ctx.disabled && !_ctx.readonly ? {
        j: common_vendor.p({
          ["custom-class"]: "wd-calendar__arrow",
          name: "arrow-right"
        })
      } : {}, {
        k: errorMessage.value
      }, errorMessage.value ? {
        l: common_vendor.t(errorMessage.value)
      } : {}, {
        m: common_vendor.n(`wd-calendar__cell ${_ctx.disabled ? "is-disabled" : ""} ${props.readonly ? "is-readonly" : ""} ${_ctx.alignRight ? "is-align-right" : ""} ${_ctx.error ? "is-error" : ""} ${_ctx.size ? "is-" + _ctx.size : ""} ${_ctx.center ? "is-center" : ""}`)
      }), {
        n: common_vendor.o(open)
      }) : {}, {
        o: !_ctx.showTypeSwitch && _ctx.shortcuts.length === 0
      }, !_ctx.showTypeSwitch && _ctx.shortcuts.length === 0 ? {
        p: common_vendor.t(_ctx.title || common_vendor.unref(translate)("title"))
      } : {}, {
        q: _ctx.showTypeSwitch
      }, _ctx.showTypeSwitch ? {
        r: common_vendor.p({
          title: common_vendor.unref(translate)("day"),
          name: common_vendor.unref(translate)("day")
        }),
        s: common_vendor.p({
          title: common_vendor.unref(translate)("week"),
          name: common_vendor.unref(translate)("week")
        }),
        t: common_vendor.p({
          title: common_vendor.unref(translate)("month"),
          name: common_vendor.unref(translate)("month")
        }),
        v: common_vendor.sr(calendarTabs, "f9f098b9-2,f9f098b9-1", {
          "k": "calendarTabs"
        }),
        w: common_vendor.o(handleTypeChange),
        x: common_vendor.o(($event) => currentTab.value = $event),
        y: common_vendor.p({
          modelValue: currentTab.value
        })
      } : {}, {
        z: _ctx.shortcuts.length > 0
      }, _ctx.shortcuts.length > 0 ? {
        A: common_vendor.f(_ctx.shortcuts, (item, index, i0) => {
          return {
            a: common_vendor.t(item.text),
            b: index,
            c: common_vendor.o(($event) => handleShortcutClick(index), index),
            d: "f9f098b9-6-" + i0 + ",f9f098b9-1"
          };
        }),
        B: common_vendor.p({
          ["custom-class"]: "wd-calendar__tag",
          type: "primary",
          plain: true,
          round: true
        })
      } : {}, {
        C: common_vendor.o(close),
        D: common_vendor.p({
          ["custom-class"]: "wd-calendar__close",
          name: "add"
        }),
        E: inited.value
      }, inited.value ? common_vendor.e$1({
        F: range.value(_ctx.type)
      }, range.value(_ctx.type) ? {
        G: common_vendor.t(rangeLabel.value[0]),
        H: common_vendor.n(`wd-calendar__range-label-item ${!calendarValue.value || !common_vendor.unref(common_vendor.isArray)(calendarValue.value) || !calendarValue.value[0] ? "is-placeholder" : ""}`),
        I: common_vendor.t(rangeLabel.value[1]),
        J: common_vendor.n(`wd-calendar__range-label-item ${!calendarValue.value || !common_vendor.unref(common_vendor.isArray)(calendarValue.value) || !calendarValue.value[1] ? "is-placeholder" : ""}`),
        K: common_vendor.n(`wd-calendar__range-label ${_ctx.type === "monthrange" ? "is-monthrange" : ""}`)
      } : {}, {
        L: common_vendor.sr(calendarView, "f9f098b9-8,f9f098b9-1", {
          "k": "calendarView"
        }),
        M: common_vendor.o(handleChange),
        N: common_vendor.o(($event) => calendarValue.value = $event),
        O: common_vendor.p({
          type: currentType.value,
          ["min-date"]: _ctx.minDate,
          ["max-date"]: _ctx.maxDate,
          ["first-day-of-week"]: _ctx.firstDayOfWeek,
          formatter: _ctx.formatter,
          ["panel-height"]: panelHeight.value,
          ["max-range"]: _ctx.maxRange,
          ["range-prompt"]: _ctx.rangePrompt,
          ["allow-same-day"]: _ctx.allowSameDay,
          ["default-time"]: _ctx.defaultTime,
          ["time-filter"]: _ctx.timeFilter,
          ["hide-second"]: _ctx.hideSecond,
          ["show-panel-title"]: !range.value(_ctx.type),
          ["immediate-change"]: _ctx.immediateChange,
          modelValue: calendarValue.value
        }),
        P: common_vendor.n(`wd-calendar__view  ${currentType.value.indexOf("range") > -1 ? "is-range" : ""} ${_ctx.showConfirm ? "is-show-confirm" : ""}`)
      }) : {}, {
        Q: _ctx.showConfirm
      }, _ctx.showConfirm ? {
        R: common_vendor.t(_ctx.confirmText || common_vendor.unref(translate)("confirm")),
        S: common_vendor.o(handleConfirm),
        T: common_vendor.p({
          block: true,
          disabled: confirmBtnDisabled.value
        })
      } : {}, {
        U: common_vendor.o(close),
        V: common_vendor.o(($event) => pickerShow.value = $event),
        W: common_vendor.p({
          duration: 250,
          ["close-on-click-modal"]: _ctx.closeOnClickModal,
          ["safe-area-inset-bottom"]: _ctx.safeAreaInsetBottom,
          ["z-index"]: _ctx.zIndex,
          modelValue: pickerShow.value
        }),
        X: common_vendor.n(`wd-calendar ${common_vendor.unref(cell).border.value ? "is-border" : ""} ${_ctx.customClass}`)
      });
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f9f098b9"]]);
wx.createComponent(Component);
//# sourceMappingURL=wd-calendar.js.map
