"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
const utils_http = require("../../utils/http.js");
const utils_is = require("../../utils/is.js");
const common_uitls = require("../../common/uitls.js");
if (!Array) {
  const _easycom_wd_input2 = common_vendor.resolveComponent("wd-input");
  _easycom_wd_input2();
}
const _easycom_wd_input = () => "../../node-modules/wot-design-uni/components/wd-input/wd-input.js";
if (!Math) {
  (_easycom_wd_input + SelectUserModal)();
}
const SelectUserModal = () => "./components/SelectUserModal.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "SelectUser",
  options: {
    styleIsolation: "shared"
  }
}), {
  __name: "SelectUser",
  props: {
    modelValue: {
      type: [Array, String],
      default: ""
    },
    labelKey: {
      type: String,
      default: "realname"
    },
    rowKey: {
      type: String,
      default: "username"
    },
    isRadioSelection: {
      type: Boolean,
      default: false
    },
    modalTitle: {
      type: String,
      default: "选择用户"
    },
    maxSelectCount: {
      type: Number
    }
  },
  emits: ["update:modelValue", "change"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const api = {
      list: "/sys/user/list"
    };
    const showText = common_vendor.ref("");
    const modalShow = common_vendor.ref(false);
    const transform = () => {
      let value = props.modelValue;
      let len;
      if (utils_is.isArray(value) || utils_is.isString(value)) {
        if (utils_is.isArray(value)) {
          len = value.length;
          value = value.join(",");
        } else {
          len = value.split(",").length;
        }
        value = value.trim();
        if (value) {
          const params = { isMultiTranslate: true, pageSize: len, [props.rowKey]: value };
          utils_http.http.get(api.list, params).then((res) => {
            var _a, _b;
            if (res.success) {
              const records = (_b = (_a = res.result) == null ? void 0 : _a.records) != null ? _b : [];
              showText.value = records.map((item) => item[props.labelKey]).join(",");
            } else {
              console.log("翻译失败~");
            }
          });
        }
      } else {
        showText.value = "";
      }
    };
    const handleClick = () => {
      modalShow.value = true;
    };
    const handleChange = (data) => {
      const rowkey = data.map((item) => item[props.rowKey]).join(",");
      const labelKey = data.map((item) => item[props.labelKey]).join(",");
      showText.value = labelKey;
      emit("update:modelValue", rowkey);
      emit("change", rowkey);
    };
    common_vendor.watch(
      () => props.modelValue,
      () => {
        transform();
      },
      { immediate: true }
    );
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.o(($event) => showText.value = $event),
        b: common_vendor.p(__spreadProps(__spreadValues({
          placeholder: common_vendor.unref(common_uitls.getPlaceholder)(_ctx.$attrs)
        }, _ctx.$attrs), {
          readonly: true,
          modelValue: showText.value
        })),
        c: common_vendor.o(handleClick),
        d: modalShow.value
      }, modalShow.value ? {
        e: common_vendor.o(handleChange),
        f: common_vendor.o(() => modalShow.value = false),
        g: common_vendor.p({
          selected: __props.modelValue,
          modalTitle: __props.modalTitle,
          maxSelectCount: __props.maxSelectCount,
          multi: !__props.isRadioSelection
        })
      } : {});
    };
  }
}));
wx.createComponent(_sfc_main);
//# sourceMappingURL=SelectUser.js.map
