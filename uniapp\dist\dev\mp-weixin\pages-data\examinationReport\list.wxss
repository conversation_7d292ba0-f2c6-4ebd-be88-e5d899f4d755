/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-scroll-view.data-v-dc29b5ce {
  height: calc(100vh - 44px);
  width: 100%;
  background-color: #f5f7fa;
}
.form-container.data-v-dc29b5ce {
  padding: 20rpx;
  padding-bottom: 120rpx;
}
.form-header.data-v-dc29b5ce {
  margin-bottom: 20rpx;
  text-align: center;
}
.form-header .form-title.data-v-dc29b5ce {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.list-container .filter-section.data-v-dc29b5ce {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.list-container .filter-section .date-filter.data-v-dc29b5ce {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  flex-wrap: wrap;
  gap: 10rpx 0;
}
.list-container .filter-section .date-filter .filter-label.data-v-dc29b5ce {
  color: #333333;
  font-size: 28rpx;
  margin-right: 10rpx;
  font-weight: 500;
}
.list-container .filter-section .date-filter .date-picker.data-v-dc29b5ce {
  flex: 2;
  min-width: 210rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #F7F7F7;
  padding: 12rpx 20rpx;
  border-radius: 8rpx;
}
.list-container .filter-section .date-filter .date-picker text.data-v-dc29b5ce {
  color: #666666;
  font-size: 26rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.list-container .filter-section .date-filter .date-separator.data-v-dc29b5ce {
  margin: 0 15rpx;
  color: #666666;
  font-size: 28rpx;
}
.list-container .filter-section .date-filter .reset-btn.data-v-dc29b5ce {
  margin-left: 3rpx;
  width: 35rpx;
  height: 35rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(7, 193, 96, 0.1);
  border-radius: 50%;
  transition: all 0.2s;
}
.list-container .filter-section .date-filter .reset-btn.data-v-dc29b5ce:active {
  transform: scale(0.9) rotate(180deg);
}
.list-container .filter-section .buttons-row.data-v-dc29b5ce {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}
.list-container .filter-section .buttons-row .filter-btn.data-v-dc29b5ce,
.list-container .filter-section .buttons-row .add-btn.data-v-dc29b5ce {
  flex: 1;
  background-color: #07C160;
  color: #FFFFFF;
  text-align: center;
  padding: 15rpx 0;
  border-radius: 8rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 8rpx rgba(7, 193, 96, 0.2);
  transition: all 0.3s;
}
.list-container .filter-section .buttons-row .filter-btn.data-v-dc29b5ce:active,
.list-container .filter-section .buttons-row .add-btn.data-v-dc29b5ce:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 4rpx rgba(7, 193, 96, 0.2);
}
.list-container .filter-section .buttons-row .filter-btn text.data-v-dc29b5ce,
.list-container .filter-section .buttons-row .add-btn text.data-v-dc29b5ce {
  margin-left: 10rpx;
}
.list-container .list-content.data-v-dc29b5ce {
  background-color: #F5F7FA;
  border-radius: 12rpx;
  padding: 10rpx;
}
.list-container .list-content .empty-state.data-v-dc29b5ce {
  padding: 60rpx 0;
  text-align: center;
  color: #999999;
  font-size: 28rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.list-container .list-content .empty-state .empty-image.data-v-dc29b5ce {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}
.list-container .list-content .record-card.data-v-dc29b5ce {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background-color: white;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}
.list-container .list-content .record-card.data-v-dc29b5ce:active {
  transform: scale(0.99);
  background-color: #fafafa;
}
.list-container .list-content .record-card .record-info.data-v-dc29b5ce {
  display: flex;
  align-items: flex-start;
  flex: 1;
}
.list-container .list-content .record-card .record-info .record-icon.data-v-dc29b5ce {
  margin-right: 16rpx;
  background-color: rgba(7, 193, 96, 0.1);
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.list-container .list-content .record-card .record-info .record-icon .card-image.data-v-dc29b5ce {
  width: 50rpx;
  height: 50rpx;
  display: block;
}
.list-container .list-content .record-card .record-info .record-details.data-v-dc29b5ce {
  flex: 1;
  overflow: hidden;
}
.list-container .list-content .record-card .record-info .record-details .record-date.data-v-dc29b5ce {
  font-size: 30rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 8rpx;
}
.list-container .list-content .record-card .record-info .record-details .record-values.data-v-dc29b5ce {
  display: flex;
  flex-direction: column;
}
.list-container .list-content .record-card .record-info .record-details .record-values .patient-detail.data-v-dc29b5ce,
.list-container .list-content .record-card .record-info .record-details .record-values .report-type.data-v-dc29b5ce {
  display: flex;
  margin-top: 4rpx;
  align-items: center;
}
.list-container .list-content .record-card .record-info .record-details .record-values .patient-detail .value-label.data-v-dc29b5ce,
.list-container .list-content .record-card .record-info .record-details .record-values .report-type .value-label.data-v-dc29b5ce {
  color: #666666;
  font-size: 24rpx;
  margin-right: 10rpx;
}
.list-container .list-content .record-card .record-info .record-details .record-values .patient-detail .value-item.data-v-dc29b5ce,
.list-container .list-content .record-card .record-info .record-details .record-values .report-type .value-item.data-v-dc29b5ce {
  color: #666666;
  font-size: 24rpx;
  flex: 1;
}
.list-container .list-content .record-card .record-action.data-v-dc29b5ce {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
}
.list-container .list-content .record-card .record-action .action-text.data-v-dc29b5ce {
  color: #07C160;
  font-size: 26rpx;
  margin-right: 10rpx;
}