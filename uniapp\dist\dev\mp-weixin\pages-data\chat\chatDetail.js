"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const utils_http = require("../../utils/http.js");
const store_user = require("../../store/user.js");
if (!Array) {
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  _easycom_PageLayout();
}
const defAvatar = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNGNUY1RjUiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iIzk5OTk5OSIvPgo8cGF0aCBkPSJNMzAgMzJDMzAgMjYuNDc3MSAyNS41MjI5IDIyIDIwIDIyQzE0LjQ3NzEgMjIgMTAgMjYuNDc3MSAxMCAzMkgzMFoiIGZpbGw9IiM5OTk5OTkiLz4KPC9zdmc+";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "chatDetail",
  setup(__props) {
    common_vendor.ref(null);
    const userStore = store_user.useUserStore();
    const replyContent = common_vendor.ref("");
    const replyImages = common_vendor.ref([]);
    const loadingChat = common_vendor.ref(false);
    const problemImages = common_vendor.ref([]);
    const handleSimpleImageError = (event) => {
      var _a;
      try {
        const currentSrc = (_a = event == null ? void 0 : event.target) == null ? void 0 : _a.src;
        console.log("📷 图片加载失败:", currentSrc);
        event.target.src = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjVGNUY1Ii8+Cjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIiBmaWxsPSIjOTk5OTk5IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7lm77niYfliqDovb3lpLHotKU8L3RleHQ+Cjwvc3ZnPg==";
      } catch (error) {
        console.error("📷 图片错误处理失败:", error);
      }
    };
    const previewSimpleImage = (url) => {
      handleImagePreview(url, problemImages.value);
    };
    const reply = common_vendor.ref([]);
    const communicationId = common_vendor.ref("");
    const mentionedDoctors = common_vendor.ref([]);
    const replyTarget = common_vendor.ref(null);
    const textareaFocus = common_vendor.ref(false);
    common_vendor.onLoad((options) => {
      console.log("页面加载时的参数:", options);
      communicationId.value = options.communication_id || "";
      console.log("communicationId:", communicationId.value);
    });
    const getAvatarUrl = (avatar) => {
      if (!avatar || avatar.trim() === "") {
        return defAvatar;
      }
      if (avatar.startsWith("http")) {
        return avatar;
      }
      if (avatar.startsWith("/")) {
        return "https://www.mograine.cn" + avatar;
      }
      return "https://www.mograine.cn/images/" + avatar;
    };
    const handleAvatarError = (event) => {
      event.target.src = defAvatar;
    };
    const avatarSrc = common_vendor.computed(() => {
      return getAvatarUrl(userStore.userInfo.avatar);
    });
    const canSubmit = common_vendor.computed(() => {
      return replyContent.value.trim().length > 0 && !isSubmitting.value;
    });
    const truncatedReplyText = common_vendor.computed(() => {
      if (!replyTarget.value) {
        return "";
      }
      if (replyTarget.value.text && replyTarget.value.text.trim() !== "") {
        return replyTarget.value.text.length > 30 ? replyTarget.value.text.substring(0, 30) + "..." : replyTarget.value.text;
      }
      if (replyTarget.value.image && replyTarget.value.image.trim() !== "") {
        return "[图片]";
      }
      return "原回复内容";
    });
    const findUserNameById = (userId) => {
      if (!userId || userId === "") {
        return null;
      }
      console.log("查找用户ID:", userId);
      console.log("当前回复列表:", reply.value);
      const foundInReply = reply.value.find((r) => r.userId === userId);
      if (foundInReply) {
        console.log("在回复列表中找到用户:", foundInReply.userName);
        return foundInReply.userName;
      }
      if (searchParams.value.problem && searchParams.value.problem.patientName) {
        console.log("未在回复中找到，返回患者名称作为备选:", searchParams.value.problem.patientName);
        return searchParams.value.problem.patientName;
      }
      console.log("未找到用户，返回null");
      return null;
    };
    const getReplyTargetText = (replyId, currentReply) => {
      console.log("🔍 查找被回复内容");
      console.log("  - replyId:", replyId);
      console.log("  - currentReply:", currentReply);
      console.log("  - currentReply.replyContent:", currentReply.replyContent);
      if (!replyId || replyId === "") {
        console.log("❌ replyId为空");
        return "原回复内容";
      }
      if (currentReply && currentReply.replyContent) {
        console.log("🔍 检查当前回复的replyContent对象:", currentReply.replyContent);
        if (typeof currentReply.replyContent === "object" && currentReply.replyContent.text) {
          const replyText = currentReply.replyContent.text.trim();
          if (replyText && replyText !== "") {
            const truncated = replyText.length > 30 ? replyText.substring(0, 30) + "..." : replyText;
            console.log("✅ 从replyContent对象获取到原回复内容:", truncated);
            return truncated;
          }
        }
        if (typeof currentReply.replyContent === "string" && currentReply.replyContent.trim() !== "") {
          const truncated = currentReply.replyContent.length > 30 ? currentReply.replyContent.substring(0, 30) + "..." : currentReply.replyContent;
          console.log("✅ 从replyContent字符串获取到原回复内容:", truncated);
          return truncated;
        }
      }
      const targetReply = reply.value.find((rep) => String(rep.id) === String(replyId));
      console.log("🔍 在回复列表中查找结果:", {
        找到目标回复: !!targetReply,
        回复列表中的所有ID: reply.value.map((r) => r.id),
        查找的replyId: replyId
      });
      if (targetReply) {
        console.log("✅ 找到目标回复:", {
          id: targetReply.id,
          text: targetReply.text,
          hasText: !!targetReply.text,
          textType: typeof targetReply.text,
          textLength: targetReply.text ? targetReply.text.length : 0
        });
        if (targetReply.text && targetReply.text.trim() !== "") {
          const truncated = targetReply.text.length > 30 ? targetReply.text.substring(0, 30) + "..." : targetReply.text;
          return truncated;
        } else {
          if (targetReply.image && targetReply.image.trim() !== "") {
            return "[图片]";
          }
          return "原回复内容";
        }
      }
      console.log("🔍 未在回复中找到，检查原问题...");
      if (searchParams.value.problem && searchParams.value.problem.question) {
        const questionText = searchParams.value.problem.question;
        const truncated = questionText.length > 30 ? questionText.substring(0, 30) + "..." : questionText;
        console.log("📋 使用原问题内容:", truncated);
        return truncated;
      }
      console.log("❌ 未找到任何匹配内容");
      return "原回复内容";
    };
    const getSafeReplyContent = (replyContent2) => {
      if (!replyContent2) {
        return "";
      }
      if (typeof replyContent2 === "object" && replyContent2 !== null) {
        console.log("🔍 replyContent是对象，尝试获取text字段:", replyContent2);
        if (replyContent2.text && typeof replyContent2.text === "string") {
          const trimmed = replyContent2.text.trim();
          if (trimmed && trimmed !== "" && trimmed !== "null" && trimmed !== "undefined") {
            console.log("✅ 从replyContent对象中获取到text:", trimmed);
            return trimmed;
          }
        }
        if (replyContent2.images && replyContent2.images !== null) {
          return "[图片]";
        }
        return "";
      }
      if (typeof replyContent2 === "string") {
        const trimmed = replyContent2.trim();
        if (trimmed === "" || trimmed === "[object Object]" || trimmed === "null" || trimmed === "undefined") {
          return "";
        }
        return trimmed;
      }
      try {
        const converted = String(replyContent2).trim();
        if (converted === "[object Object]" || converted === "null" || converted === "undefined" || converted === "") {
          return "";
        }
        return converted;
      } catch (error) {
        console.warn("replyContent转换失败:", error);
        return "";
      }
    };
    const formatTime = (timeStr) => {
      if (!timeStr || timeStr === "-") {
        return "-";
      }
      try {
        if (timeStr.includes(" ")) {
          const parts = timeStr.split(" ");
          if (parts.length >= 2) {
            const datePart = parts[0];
            const timePart = parts[1];
            if (datePart.includes("-") && timePart.includes(":")) {
              const dateComponents = datePart.split("-");
              const timeComponents = timePart.split(":");
              if (dateComponents.length >= 3 && timeComponents.length >= 2) {
                const year = dateComponents[0];
                const month = dateComponents[1];
                const day = dateComponents[2];
                const hour = timeComponents[0];
                const minute = timeComponents[1];
                return `${year}-${month}-${day} ${hour}:${minute}`;
              }
            }
            return timePart;
          }
        }
        return timeStr;
      } catch (error) {
        console.warn("时间格式化失败:", error);
        return timeStr;
      }
    };
    const hasReplyInfo = (rep) => {
      const hasValidReplyUserId = rep.replyUserId && rep.replyUserId !== "" && rep.replyUserId !== null;
      const hasValidReplyId = rep.replyId && rep.replyId !== "" && rep.replyId !== null;
      const hasInfo = hasValidReplyUserId && hasValidReplyId;
      return hasInfo;
    };
    const setReplyTarget = (rep) => {
      console.log("🎯 设置回复目标:", {
        userId: rep.userId,
        userName: rep.userName,
        id: rep.id,
        text: rep.text
      });
      replyTarget.value = rep;
      textareaFocus.value = false;
      common_vendor.nextTick$1(() => {
        textareaFocus.value = true;
      });
    };
    const searchParams = common_vendor.ref({
      communication_id: "",
      problem: {
        patientName: "",
        patientAvatar: "",
        images: [],
        question: "",
        title: "",
        // 添加title字段
        createTime: "",
        // 添加创建时间字段
        updateTime: ""
        // 添加更新时间字段
      },
      reply: {
        id: "",
        userAvatar: "",
        userName: "",
        replyContent: "",
        replyImage: "",
        image: [],
        text: ""
      }
    });
    const fetchDoctorsByIds = (doctorIds) => __async(this, null, function* () {
      try {
        console.log("获取医生信息，doctorIds:", doctorIds);
        let idsArray = [];
        if (typeof doctorIds === "string") {
          idsArray = doctorIds.split(",").filter((id) => id.trim() !== "");
        } else if (Array.isArray(doctorIds)) {
          idsArray = doctorIds.filter((id) => id && id.trim() !== "");
        }
        if (idsArray.length === 0) {
          console.log("没有有效的医生ID");
          mentionedDoctors.value = [];
          return;
        }
        const response = yield utils_http.http.get("/sys/user/doctorList");
        if (response.success && response.result) {
          const allDoctors = response.result;
          const selectedDoctors = allDoctors.filter(
            (doctor) => idsArray.includes(String(doctor.id)) || idsArray.includes(String(doctor.userId))
          );
          mentionedDoctors.value = selectedDoctors;
          console.log("成功获取@医生信息:", selectedDoctors);
        } else {
          console.error("获取医生列表失败:", response.message);
          mentionedDoctors.value = [];
        }
      } catch (error) {
        console.error("获取医生信息异常:", error);
        mentionedDoctors.value = [];
      }
    });
    const fetchReply = () => __async(this, null, function* () {
      try {
        loadingChat.value = true;
        if (!communicationId.value) {
          showError("页面参数错误，请重新进入");
          return;
        }
        console.log("获取回复列表，communicationId:", communicationId.value);
        const params = {
          communication_id: communicationId.value,
          user_id: userStore.userInfo.userid || "",
          category: Number(userStore.userInfo.userCategory)
        };
        console.log("获取回复列表请求参数:", params);
        utils_http.http.get("/communication/getDetail", params).then((res) => {
          console.log("=== 聊天详情页面接口数据打印 ===");
          console.log("📡 接口地址: GET /communication/getDetail");
          console.log("📤 请求参数:", JSON.stringify(params, null, 2));
          console.log("📥 完整响应数据:", JSON.stringify(res, null, 2));
          console.log("✅ 响应状态:", res.success ? "成功" : "失败");
          console.log("📊 响应代码:", res.code);
          console.log("💬 响应消息:", res.message);
          console.log("⏰ 响应时间戳:", res.timestamp);
          if (res.success && res.result) {
            console.log("🎯 业务数据 (res.result):", JSON.stringify(res.result, null, 2));
            console.log("📋 数据结构分析:");
            console.log("  - communication_id:", res.result.communication_id);
            console.log("  - problem 对象:", res.result.problem ? "存在" : "不存在");
            console.log("  - reply 数组:", res.result.reply ? `存在，长度: ${res.result.reply.length}` : "不存在");
            if (res.result.problem) {
              console.log("🔍 问题详情 (problem):");
            }
            if (res.result.reply && Array.isArray(res.result.reply)) {
              console.log("💬 回复列表 (reply):");
              console.log("  - 回复总数:", res.result.reply.length);
              res.result.reply.forEach((item, index) => {
                console.log(`  📝 回复 ${index + 1}:`, {
                  id: item.id,
                  用户名: item.userName,
                  用户类别: item.userCategory,
                  用户ID: item.userId,
                  回复内容: item.text,
                  图片: item.image,
                  创建时间: item.createTime,
                  更新时间: item.updateTime,
                  回复用户ID: item.replyUserId,
                  回复ID: item.replyId,
                  头像: item.userAvatar
                });
              });
            }
            console.log("================================");
            console.log("API返回的获取沟通记录", res.result);
            console.log("当前 communicationId:", communicationId.value);
            console.log("返回的 communication_id:", res.result.communication_id);
            const rawImages = res.result.problem.images || [];
            console.log("📷 原始图片数据:", rawImages);
            problemImages.value = rawImages.map((filename) => {
              if (filename.startsWith("http")) {
                return filename;
              }
              if (filename.startsWith("/")) {
                return `https://www.mograine.cn${filename}`;
              }
              return `https://www.mograine.cn/tmp/${filename}`;
            });
            console.log("📷 处理后的图片URLs:", problemImages.value);
            const replyList = res.result.reply || [];
            console.log("原始回复列表:", replyList);
            console.log("回复列表长度:", replyList.length);
            const processedReply = replyList;
            processedReply.forEach((item, index) => {
              console.log(`📝 回复 ${index}:`, {
                id: item.id,
                userName: item.userName,
                userAvatar: item.userAvatar,
                text: item.text,
                createTime: item.createTime,
                updateTime: item.updateTime,
                userCategory: item.userCategory,
                userId: item.userId,
                image: item.image,
                imageType: typeof item.image,
                imageLength: Array.isArray(item.image) ? item.image.length : "not array",
                processedImages: getImageList(item.image),
                // 添加处理后的图片数组
                finalImageUrls: getImageList(item.image).map((img) => getReplyImageUrl(img)),
                // 最终的图片URL
                replyUserId: item.replyUserId,
                replyId: item.replyId,
                hasReplyInfo: hasReplyInfo(item)
              });
              if (item.image && getImageList(item.image).length > 0) {
                console.log(`🖼️ 回复 ${index} 图片详情:`, {
                  原始图片数据: item.image,
                  处理后图片数组: getImageList(item.image),
                  最终显示URLs: getImageList(item.image)
                });
              }
            });
            reply.value = processedReply;
            searchParams.value.problem = res.result.problem || {};
            searchParams.value.reply = res.result.reply || {};
            if (res.result.problem && res.result.problem.doctors && Array.isArray(res.result.problem.doctors)) {
              mentionedDoctors.value = res.result.problem.doctors;
              console.log("📋 获取到@医生信息 (doctors字段):", mentionedDoctors.value);
            } else if (res.result.problem && res.result.problem.mentionedDoctors) {
              mentionedDoctors.value = res.result.problem.mentionedDoctors;
              console.log("📋 获取到@医生信息 (mentionedDoctors字段):", mentionedDoctors.value);
            } else if (res.result.problem && res.result.problem.doctorIds) {
              console.log("📋 检测到doctorIds，需要获取医生详细信息:", res.result.problem.doctorIds);
              fetchDoctorsByIds(res.result.problem.doctorIds).catch((error) => {
                console.error("获取医生信息失败:", error);
              });
            } else {
              mentionedDoctors.value = [];
              console.log("📋 未检测到@医生信息");
            }
          } else {
            console.log("❌ 接口调用失败:");
            console.log("  - success:", res.success);
            console.log("  - code:", res.code);
            console.log("  - message:", res.message);
            console.log("  - result:", res.result);
            console.log("  - 完整响应:", JSON.stringify(res, null, 2));
            common_vendor.index.showToast({
              title: res.message || "获取沟通记录失败",
              icon: "none"
            });
          }
        }).catch((error) => {
          console.log("🚨 网络请求异常:");
          console.log("  - 错误类型:", typeof error);
          console.log("  - 错误信息:", error.message || error);
          console.log("  - 完整错误对象:", error);
          console.log("  - 请求参数:", params);
          console.log("  - 接口地址: GET /communication/getDetail");
          common_vendor.index.showToast({
            title: "网络连接失败，请检查网络后重试",
            icon: "none",
            duration: 3e3
          });
        });
      } finally {
        loadingChat.value = false;
      }
    });
    common_vendor.onMounted(() => {
      fetchReply();
    });
    const chooseFromGallery = () => {
      if (replyImages.value.length >= 6) {
        common_vendor.index.showToast({ title: "最多只能上传6张图片", icon: "none" });
        return;
      }
      const remainingCount = 6 - replyImages.value.length;
      common_vendor.index.chooseImage({
        count: remainingCount,
        sourceType: ["album"],
        success: (res) => {
          if (res.tempFilePaths && res.tempFilePaths.length > 0) {
            replyImages.value.push(...res.tempFilePaths);
          }
        },
        fail: (err) => {
          console.error("选择图片失败:", err);
          common_vendor.index.showToast({ title: "选择图片失败", icon: "none" });
        }
      });
    };
    const chooseFromCamera = () => {
      if (replyImages.value.length >= 6) {
        common_vendor.index.showToast({ title: "最多只能上传6张图片", icon: "none" });
        return;
      }
      common_vendor.index.chooseImage({
        count: 1,
        sourceType: ["camera"],
        success: (res) => {
          if (res.tempFilePaths && res.tempFilePaths.length > 0) {
            replyImages.value.push(...res.tempFilePaths);
          }
        },
        fail: (err) => {
          console.error("拍照失败:", err);
          common_vendor.index.showToast({ title: "拍照失败", icon: "none" });
        }
      });
    };
    const chooseImage = () => {
      common_vendor.index.showActionSheet({
        itemList: ["从相册选择", "拍照"],
        success: (res) => {
          if (res.tapIndex === 0) {
            chooseFromGallery();
          } else if (res.tapIndex === 1) {
            chooseFromCamera();
          }
        },
        fail: (err) => {
          console.error("选择操作失败:", err);
        }
      });
    };
    const uploadImage = (tempFilePath) => __async(this, null, function* () {
      return new Promise((resolve, reject) => {
        if (!tempFilePath || tempFilePath.trim() === "") {
          reject(new Error("文件路径无效"));
          return;
        }
        if (!tempFilePath.includes("tmp") && !tempFilePath.includes("temp") && !tempFilePath.startsWith("wxfile://")) {
          reject(new Error("文件路径格式不正确"));
          return;
        }
        const uploadUrl = "https://www.mograine.cn/api/sys/common/upload";
        common_vendor.index.uploadFile({
          url: uploadUrl,
          filePath: tempFilePath,
          name: "file",
          header: {
            "X-Access-Token": userStore.userInfo.token || "",
            "X-Tenant-Id": userStore.userInfo.tenantId || ""
            // 不要设置Content-Type，让浏览器自动设置
          },
          formData: {
            // 可能需要的额外参数
            "biz": "temp"
          },
          success: (uploadRes) => {
            console.log("📤 图片上传响应:", {
              statusCode: uploadRes.statusCode,
              data: uploadRes.data
            });
            try {
              if (uploadRes.statusCode === 404) {
                reject(new Error("上传接口不存在，请检查服务器配置"));
                return;
              }
              if (uploadRes.statusCode === 401 || uploadRes.statusCode === 403) {
                reject(new Error("权限不足，请重新登录"));
                return;
              }
              if (uploadRes.statusCode !== 200) {
                reject(new Error(`上传失败，状态码: ${uploadRes.statusCode}`));
                return;
              }
              let result;
              if (typeof uploadRes.data === "string") {
                result = JSON.parse(uploadRes.data);
              } else {
                result = uploadRes.data;
              }
              console.log("📤 解析后的上传响应:", result);
              if (result.success && result.message) {
                let imagePath = result.message;
                let fileName = imagePath;
                if (imagePath.includes("/")) {
                  fileName = imagePath.split("/").pop() || imagePath;
                }
                console.log("📤 格式1 - 文件名:", fileName);
                resolve(fileName);
              } else if (result.code === 200 && result.result) {
                let imagePath = result.result;
                let fileName = imagePath;
                if (imagePath.includes("/")) {
                  fileName = imagePath.split("/").pop() || imagePath;
                }
                console.log("📤 格式2 - 文件名:", fileName);
                resolve(fileName);
              } else if (result.data) {
                let imagePath = result.data;
                let fileName = imagePath;
                if (imagePath.includes("/")) {
                  fileName = imagePath.split("/").pop() || imagePath;
                }
                console.log("📤 格式3 - 文件名:", fileName);
                resolve(fileName);
              } else {
                console.log("📤 上传响应格式不匹配，完整响应:", result);
                reject(new Error(result.message || result.msg || "上传失败，响应格式不正确"));
              }
            } catch (parseError) {
              reject(new Error("服务器响应格式错误"));
            }
          },
          fail: () => {
            reject(new Error("上传失败，请重试"));
          }
        });
      });
    });
    console.log("uploadedUrl", replyImages.value);
    const isSubmitting = common_vendor.ref(false);
    const showError = (message) => {
      common_vendor.index.showToast({ title: message, icon: "none" });
    };
    const validateSubmission = () => {
      if (isSubmitting.value) {
        showError("正在提交中，请勿重复操作");
        return false;
      }
      if (!replyContent.value.trim()) {
        showError("请输入回复内容");
        textareaFocus.value = false;
        common_vendor.nextTick$1(() => {
          textareaFocus.value = true;
        });
        return false;
      }
      if (replyContent.value.trim().length > 1e3) {
        showError("回复内容不能超过1000个字符");
        return false;
      }
      if (!communicationId.value) {
        showError("页面参数错误，请重新进入");
        return false;
      }
      if (!userStore.userInfo.userid) {
        showError("用户信息错误，请重新登录");
        return false;
      }
      return true;
    };
    const submitReply = () => __async(this, null, function* () {
      if (!validateSubmission())
        return;
      try {
        isSubmitting.value = true;
        common_vendor.index.showLoading({ title: "提交中..." });
        const requestData = {
          communicationId: String(communicationId.value),
          // 问题id (string) - 必填
          userId: String(userStore.userInfo.userid),
          // 用户id (string) - 必填
          replyUserId: "",
          // 被回复的用户id (string) - 可选，没有时传空字符串
          replyId: "",
          // 被回复的信息的id (string) - 可选，没有时传空字符串
          text: "",
          // 回复内容 (string) - 可选，没有时传空字符串
          images: []
          // 回复的图片 (string | null) - 可选，注意字段名是 images
        };
        const trimmedText = replyContent.value.trim();
        if (trimmedText && trimmedText.length > 0) {
          requestData.text = trimmedText;
        }
        if (replyImages.value.length > 0) {
          try {
            common_vendor.index.showLoading({ title: "上传图片中..." });
            const uploadedUrls = [];
            for (const tempPath of replyImages.value) {
              if (!tempPath.includes("/") && !tempPath.startsWith("http")) {
                uploadedUrls.push(tempPath);
              } else {
                const uploadedFileName = yield uploadImage(tempPath);
                if (uploadedFileName) {
                  uploadedUrls.push(uploadedFileName);
                } else {
                  common_vendor.index.hideLoading();
                  showError("图片上传失败，请重试");
                  return;
                }
              }
            }
            common_vendor.index.hideLoading();
            if (uploadedUrls.length === 0) {
              showError("图片上传失败，请重新选择图片");
              return;
            }
            console.log("📤 上传完成的文件名数组:", uploadedUrls);
            requestData.images = uploadedUrls;
            if (uploadedUrls.length === 1) {
              requestData.image = uploadedUrls[0];
            } else if (uploadedUrls.length > 1) {
              requestData.image = uploadedUrls.join(",");
            }
          } catch (error) {
            common_vendor.index.hideLoading();
            console.error("图片上传失败:", error);
            showError("图片上传失败，请重试");
            return;
          }
        }
        if (replyTarget.value && replyTarget.value.userId && replyTarget.value.id) {
          requestData.replyUserId = String(replyTarget.value.userId);
          requestData.replyId = String(replyTarget.value.id);
          if (replyTarget.value.text && replyTarget.value.text.trim() !== "") {
            requestData.replyContent = replyTarget.value.text.trim();
          }
        }
        const validateRequestData = (data) => {
          if (!data.communicationId || !data.userId) {
            showError("数据验证失败，请重试");
            return false;
          }
          if (data.text && data.text.length > 2e3) {
            showError("回复内容不能超过2000个字符");
            return false;
          }
          if (!/^\d+$/.test(data.communicationId) || !/^\d+$/.test(data.userId)) {
            showError("数据格式错误");
            return false;
          }
          if (!data.text && !data.images) {
            showError("请输入回复内容或选择图片");
            return false;
          }
          return true;
        };
        if (!validateRequestData(requestData))
          return;
        console.log("📤 提交回复请求数据:", __spreadProps(__spreadValues({}, requestData), {
          imagesCount: requestData.images ? requestData.images.length : 0,
          hasImages: !!requestData.images && requestData.images.length > 0,
          imageUrls: requestData.images || [],
          imageField: requestData.image || "undefined"
        }));
        if (requestData.images && requestData.images.length > 0) {
          console.log("📸 提交的图片数据:", {
            images数组: requestData.images,
            image字段: requestData.image,
            图片数量: requestData.images.length
          });
        }
        try {
          const response = yield utils_http.http.post("/communication/reply", requestData);
          console.log("📥 提交回复响应数据:", response);
          if (response && response.result) {
            console.log("📝 新创建的回复数据:", response.result);
          }
          if (response && response.success) {
            common_vendor.index.showToast({ title: "提交成功", icon: "success" });
            const newReply = {
              id: Date.now().toString(),
              // 临时ID
              userId: userStore.userInfo.userid,
              userName: userStore.userInfo.realname || userStore.userInfo.username,
              userAvatar: userStore.userInfo.avatar,
              text: requestData.text || "",
              image: requestData.images && requestData.images.length > 0 ? requestData.images.join(",") : "",
              createTime: (/* @__PURE__ */ new Date()).toISOString().replace("T", " ").substring(0, 19),
              updateTime: (/* @__PURE__ */ new Date()).toISOString().replace("T", " ").substring(0, 19),
              replyUserId: requestData.replyUserId || "",
              replyId: requestData.replyId || "",
              replyContent: requestData.replyContent || null,
              userCategory: userStore.userInfo.userCategory || 1
            };
            console.log("📝 本地添加新回复，图片文件名:", newReply.image);
            console.log("📝 本地回复完整数据:", newReply);
            reply.value.push(newReply);
            console.log("📝 本地添加新回复:", newReply);
            replyContent.value = "";
            replyImages.value = [];
            replyTarget.value = null;
          } else {
            let errorMsg = "提交失败";
            if (response == null ? void 0 : response.message) {
              errorMsg = response.message;
              if (response.message.includes("完整性例如") || response.message.includes("字段内容超出长度")) {
                errorMsg = "内容过长或格式不正确，请检查后重试";
              } else if (response.message.includes("违反唯一约束")) {
                errorMsg = "数据重复，请勿重复提交";
              } else if (response.message.includes("违反非空限制")) {
                errorMsg = "必填信息缺失，请完善后重试";
              }
            } else if (response == null ? void 0 : response.msg) {
              errorMsg = response.msg;
            }
            console.error("提交失败详情:", {
              response,
              requestData,
              errorMsg
            });
            common_vendor.index.showToast({
              title: errorMsg,
              icon: "none",
              duration: 3e3
              // 延长显示时间
            });
          }
        } catch (error) {
          console.error("提交异常:", error);
          common_vendor.index.showToast({
            title: "网络连接异常，请检查网络后重试",
            icon: "none",
            duration: 3e3
          });
        }
      } finally {
        isSubmitting.value = false;
        common_vendor.index.hideLoading();
      }
    });
    const deleteReply = (rep) => __async(this, null, function* () {
      try {
        const result = yield new Promise((resolve) => {
          common_vendor.index.showModal({
            title: "确认删除",
            content: "确定要删除这条评论吗？",
            success: (res2) => {
              resolve(res2.confirm);
            },
            fail: () => {
              resolve(false);
            }
          });
        });
        if (!result) {
          return;
        }
        common_vendor.index.showLoading({ title: "删除中..." });
        const queryParams = `reply_id=${rep.id}&user_id=${userStore.userInfo.userid}&communicationId=${communicationId.value}`;
        const res = yield utils_http.http.delete(`/communication/del_reply?${queryParams}`);
        if (res.success) {
          common_vendor.index.showToast({ title: "删除成功", icon: "success" });
          fetchReply();
        } else {
          common_vendor.index.showToast({ title: res.message || "删除失败", icon: "none" });
        }
      } catch (e) {
        console.error("删除评论失败:", e);
        common_vendor.index.showToast({ title: "删除失败", icon: "none" });
      } finally {
        common_vendor.index.hideLoading();
      }
    });
    const handleImagePreview = (url, imageList) => {
      const validUrls = imageList.filter((img) => img && typeof img === "string" && img.trim() !== "");
      if (validUrls.length === 0) {
        showError("没有可预览的图片");
        return;
      }
      const currentUrl = url && typeof url === "string" && url.trim() !== "" ? url : validUrls[0];
      common_vendor.index.previewImage({
        current: currentUrl,
        urls: validUrls
      });
    };
    const removeImageByIndex = (index) => {
      replyImages.value.splice(index, 1);
      common_vendor.index.showToast({
        title: "图片已移除",
        icon: "success",
        duration: 1e3
      });
    };
    const previewImage = (url) => {
      common_vendor.index.previewImage({
        current: url,
        urls: replyImages.value
      });
    };
    const getImageList = (imgField) => {
      if (!imgField)
        return [];
      let imageArray = [];
      if (Array.isArray(imgField)) {
        imageArray = imgField;
      } else if (typeof imgField === "string") {
        if (imgField.startsWith("[")) {
          try {
            const arr = JSON.parse(imgField);
            imageArray = Array.isArray(arr) ? arr : [];
          } catch (e) {
            imageArray = [];
          }
        } else if (imgField.includes(",")) {
          imageArray = imgField.split(",").map((s) => s.trim()).filter(Boolean);
        } else if (imgField.trim() !== "") {
          imageArray = [imgField];
        }
      }
      return imageArray.map((img) => {
        if (!img)
          return "";
        if (img.startsWith("http")) {
          return img;
        }
        if (img.startsWith("/")) {
          return `https://www.mograine.cn${img}`;
        }
        return `https://www.mograine.cn/tmp/${img}`;
      }).filter(Boolean);
    };
    const getReplyImageUrl = (img) => {
      if (!img)
        return "";
      if (img.startsWith("http")) {
        return img;
      }
      if (img.startsWith("/")) {
        return `https://www.mograine.cn${img}`;
      }
      return `https://www.mograine.cn/tmp/${img}`;
    };
    const buildImageUrl = (img) => {
      if (!img)
        return "";
      if (img.startsWith("http")) {
        return img;
      }
      if (img.startsWith("/")) {
        return `https://www.mograine.cn${img}`;
      }
      return `https://www.mograine.cn/tmp/${img}`;
    };
    const previewSingleImage = (img) => {
      const fullUrl = buildImageUrl(img);
      console.log("预览图片:", fullUrl);
      common_vendor.index.previewImage({
        urls: [fullUrl],
        current: fullUrl
      });
    };
    const handleImageError = (e) => {
      var _a;
      console.error("❌ 图片加载失败:", e);
      console.error("❌ 失败的图片URL:", ((_a = e.target) == null ? void 0 : _a.src) || "未知URL");
      console.error("❌ 错误详情:", e.detail || "无详情");
      if (e.target) {
        e.target.style.display = "none";
      }
    };
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: getAvatarUrl(searchParams.value.problem.patientAvatar),
        b: common_vendor.o(handleAvatarError),
        c: common_vendor.t(searchParams.value.problem.patientName),
        d: common_vendor.t(formatTime(searchParams.value.problem.createTime || searchParams.value.problem.updateTime || "-")),
        e: searchParams.value.problem.title && searchParams.value.problem.title.trim()
      }, searchParams.value.problem.title && searchParams.value.problem.title.trim() ? {
        f: common_vendor.t(searchParams.value.problem.title)
      } : {}, {
        g: common_vendor.t(searchParams.value.problem.question),
        h: problemImages.value.length > 0
      }, problemImages.value.length > 0 ? {
        i: common_vendor.t(problemImages.value.length),
        j: common_vendor.f(problemImages.value, (img, index, i0) => {
          return {
            a: img,
            b: common_vendor.o(handleSimpleImageError, index),
            c: index,
            d: common_vendor.o(($event) => previewSimpleImage(img), index)
          };
        })
      } : {}, {
        k: mentionedDoctors.value && mentionedDoctors.value.length > 0
      }, mentionedDoctors.value && mentionedDoctors.value.length > 0 ? {
        l: common_vendor.f(mentionedDoctors.value, (doctor, index, i0) => {
          return {
            a: common_vendor.t(doctor.realname || doctor.realName || doctor.username || "医生"),
            b: doctor.userId || doctor.id || index
          };
        })
      } : {}, {
        m: reply.value && reply.value.length > 0
      }, reply.value && reply.value.length > 0 ? {
        n: common_vendor.f(reply.value, (rep, index, i0) => {
          return common_vendor.e$1({
            a: getAvatarUrl(rep.userAvatar),
            b: common_vendor.o(handleAvatarError, index),
            c: rep.userCategory === 0
          }, rep.userCategory === 0 ? {
            d: common_vendor.t(rep.userName || "未知用户")
          } : {
            e: common_vendor.t(rep.userName || "未知用户")
          }, {
            f: rep.replyUserId && rep.replyUserId !== "" && rep.replyUserId !== null
          }, rep.replyUserId && rep.replyUserId !== "" && rep.replyUserId !== null ? {
            g: common_vendor.t(findUserNameById(rep.replyUserId) || "未知用户"),
            h: common_vendor.t(getSafeReplyContent(rep.replyContent) || getReplyTargetText(rep.replyId, rep) || "原回复内容")
          } : {}, {
            i: common_vendor.t(rep.text || "内容为空"),
            j: getImageList(rep.image).length > 0
          }, getImageList(rep.image).length > 0 ? {
            k: common_vendor.f(getImageList(rep.image), (img, imgIndex, i1) => {
              return {
                a: img,
                b: common_vendor.o(($event) => previewSingleImage(img), imgIndex),
                c: common_vendor.o(handleImageError, imgIndex),
                d: imgIndex
              };
            })
          } : {}, {
            l: rep.userId === common_vendor.unref(userStore).userInfo.userid
          }, rep.userId === common_vendor.unref(userStore).userInfo.userid ? {
            m: common_vendor.o(($event) => deleteReply(rep), index)
          } : {}, {
            n: common_vendor.t(formatTime(rep.createTime || rep.updateTime || "-")),
            o: index,
            p: common_vendor.o(($event) => setReplyTarget(rep), index)
          });
        })
      } : {}, {
        o: common_vendor.p({
          navLeftArrow: true,
          navLeftText: "返回"
        }),
        p: replyTarget.value
      }, replyTarget.value ? {
        q: common_vendor.t(replyTarget.value.userName),
        r: common_vendor.t(truncatedReplyText.value),
        s: common_vendor.o(($event) => replyTarget.value = null)
      } : {}, {
        t: avatarSrc.value,
        v: common_vendor.o(handleAvatarError),
        w: textareaFocus.value,
        x: replyContent.value,
        y: common_vendor.o(($event) => replyContent.value = $event.detail.value),
        z: common_vendor.o(chooseImage),
        A: !canSubmit.value ? 1 : "",
        B: common_vendor.o(submitReply),
        C: !canSubmit.value,
        D: replyImages.value.length > 0
      }, replyImages.value.length > 0 ? {
        E: common_vendor.f(replyImages.value, (img, index, i0) => {
          return {
            a: img,
            b: common_vendor.o(($event) => removeImageByIndex(index), index),
            c: index,
            d: common_vendor.o(($event) => previewImage(img), index)
          };
        })
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-53009239"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=chatDetail.js.map
