{"version": 3, "file": "online.js", "sources": ["../../../../../src/pages-sub/online/online.vue", "../../../../../uniPage:/cGFnZXMtc3ViXG9ubGluZVxvbmxpbmUudnVl"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n  <PageLayout navTitle=\"online表单开发\" backRouteName=\"index\" routeMethod=\"pushTab\">\r\n    <view class=\"wrap\">\r\n      <z-paging\r\n        ref=\"paging\"\r\n        :fixed=\"false\"\r\n        v-model=\"dataList\"\r\n        @query=\"queryList\"\r\n        :default-page-size=\"20\"\r\n      >\r\n        <template #top>\r\n          <wd-search\r\n            hide-cancel\r\n            placeholder=\"请输入表描述\"\r\n            v-model.trim=\"keyword\"\r\n            @search=\"handleSearch\"\r\n            @clear=\"handleClear\"\r\n          />\r\n        </template>\r\n        <template v-for=\"(item, index) in dataList\" :key=\"item.id\">\r\n          <template v-if=\"item.tableType != 3\">\r\n            <wd-swipe-action>\r\n              <view class=\"list\" @click=\"handleGo(item)\">\r\n                <view\r\n                  class=\"cIcon\"\r\n                  :style=\"{ 'background-color': getBackgroundColor(item, index) }\"\r\n                >\r\n                  <view class=\"u-iconfont u-icon-table\"></view>\r\n                </view>\r\n                <view class=\"tableTxt ellipsis\">{{ item.tableTxt }}</view>\r\n                <view class=\"createTime ellipsis\">{{ item.createTime.substring(0, 10) }}</view>\r\n              </view>\r\n              <template #right>\r\n                <view class=\"action\">\r\n                  <view class=\"button\" @click=\"handleAction('del', item)\">删除</view>\r\n                  <view class=\"button\" @click=\"handleAction('remove', item)\">移除</view>\r\n                </view>\r\n              </template>\r\n            </wd-swipe-action>\r\n          </template>\r\n        </template>\r\n      </z-paging>\r\n    </view>\r\n  </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { http } from '@/utils/http'\r\nimport { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'\r\nimport { useRouter } from '@/plugin/uni-mini-router'\r\nimport { useUserStore } from '@/store/user'\r\nimport { useParamsStore } from '@/store/page-params'\r\nimport { getRandomColor } from '@/common/uitls'\r\n\r\ndefineOptions({\r\n  name: 'online',\r\n  options: {\r\n    styleIsolation: 'shared',\r\n  },\r\n})\r\n\r\nconst toast = useToast()\r\nconst router = useRouter()\r\nconst userStore = useUserStore()\r\nconst paramsStore = useParamsStore()\r\nconst paging = ref(null)\r\nconst dataList: any = ref([])\r\nconst keyword = ref('')\r\nconst itemBgColor = []\r\n// 接口拿到的数据处理之后的\r\n\r\nconst handleGo = (item) => {\r\n  if (item.tableType === 3) {\r\n    toast.warning('附表无列表页~')\r\n  } else {\r\n    paramsStore.setPageParams('onlineCard', { data: item })\r\n    router.push({ name: 'onlineCard' })\r\n  }\r\n}\r\n// 清除搜索条件\r\nfunction handleClear() {\r\n  keyword.value = ''\r\n  handleSearch()\r\n}\r\n// 搜索\r\nfunction handleSearch() {\r\n  queryList(1, 10)\r\n}\r\nconst handleAction = (val, item) => {\r\n  if (val == 'del') {\r\n    http\r\n      .delete('/online/cgform/head/delete', { id: item })\r\n      .then((res: any) => {\r\n        if (res.success) {\r\n          toast.success('删除成功~')\r\n          paging.value.reload()\r\n        } else {\r\n          toast.warning('删除失败~')\r\n        }\r\n      })\r\n      .catch((res) => {\r\n        toast.error('删除失败~')\r\n      })\r\n  } else if ((val = 'remove')) {\r\n    http\r\n      .delete('/online/cgform/head/removeRecord', { id: item })\r\n      .then((res: any) => {\r\n        if (res.success) {\r\n          toast.success('移除成功~')\r\n          paging.value.reload()\r\n        } else {\r\n          toast.warning('移除失败~')\r\n        }\r\n      })\r\n      .catch((res) => {\r\n        toast.error('移除失败~')\r\n      })\r\n  }\r\n}\r\nconst getParams = ({ pageNo, pageSize }) => {\r\n  const params: any = {\r\n    pageNo,\r\n    pageSize,\r\n    order: 'desc',\r\n    column: 'createTime',\r\n  }\r\n  if (keyword.value.length) {\r\n    params.tableTxt = `*${keyword.value}*`\r\n  }\r\n  return params\r\n}\r\nconst queryList = (pageNo, pageSize) => {\r\n  const params = getParams({ pageNo, pageSize })\r\n  http\r\n    .get('/online/cgform/head/list', { ...params })\r\n    .then((res: any) => {\r\n      if (res.success && res.result?.records) {\r\n        if (pageNo === 1) {\r\n          dataList.value = []\r\n        }\r\n        paging.value.complete(res.result.records)\r\n      } else {\r\n        paging.value.complete(false)\r\n      }\r\n    })\r\n    .catch((res) => {\r\n      paging.value.complete(false)\r\n    })\r\n}\r\nconst getType = (record) => {\r\n  const type = { 1: '单表', 2: '主表', 3: '附表' }\r\n  let tbTypeText = type[record.tableType]\r\n  // if (record.isTree === 'Y') {\r\n  //   tbTypeText += '(树)'\r\n  // }\r\n  // if (record.themeTemplate === 'innerTable') {\r\n  //   tbTypeText += '(内嵌)'\r\n  // } else if (record.themeTemplate === 'erp') {\r\n  //   tbTypeText += '(ERP)'\r\n  // } else if (record.themeTemplate === 'tab') {\r\n  //   tbTypeText += '(TAB)'\r\n  // }\r\n  return tbTypeText\r\n}\r\nconst getBackgroundColor = (item, index) => {\r\n  return itemBgColor[index % itemBgColor.length]\r\n}\r\nfor (let i = 0; i < 50; i++) {\r\n  itemBgColor.push(getRandomColor())\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n//\r\n:deep(.wd-search) {\r\n  border-bottom: 1px solid #f4f2f2;\r\n}\r\n.wd-swipe-action {\r\n  &:first-child {\r\n    margin-top: 10px;\r\n  }\r\n}\r\n.list {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background-color: #fff;\r\n  border-bottom: 1px solid #eee;\r\n  padding: 16px 12px;\r\n  line-height: 20px;\r\n  margin-bottom: 10px;\r\n  .cIcon {\r\n    text-align: center;\r\n    line-height: 24px;\r\n    color: #fff;\r\n    margin-right: 8px;\r\n    width: 24px;\r\n    height: 24px;\r\n    border-radius: 50%;\r\n    .u-iconfont {\r\n      font-size: 14px;\r\n    }\r\n  }\r\n  .tableTxt {\r\n    flex: 1;\r\n    margin-right: 40px;\r\n  }\r\n  .createTime {\r\n    text-align: right;\r\n    width: 75px;\r\n    font-size: 12px;\r\n    color: #919191;\r\n  }\r\n}\r\n.action {\r\n  width: 100px;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  .button {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    flex: 1;\r\n    height: 100%;\r\n    color: #fff;\r\n    &:first-child {\r\n      background-color: #fa4350;\r\n    }\r\n    &:last-child {\r\n      background-color: #f0883a;\r\n    }\r\n  }\r\n}\r\n.wrap {\r\n  height: 100%;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-sub/online/online.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useToast", "useRouter", "useUserStore", "useParamsStore", "ref", "http", "getRandomColor"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiEA,UAAM,QAAQA,cAAAA,SAAS;AACvB,UAAM,SAASC,gCAAAA,UAAU;AACPC,eAAa,aAAA;AAC/B,UAAM,cAAcC,iBAAAA,eAAe;AAC7B,UAAA,SAASC,kBAAI,IAAI;AACjB,UAAA,WAAgBA,cAAI,IAAA,EAAE;AACtB,UAAA,UAAUA,kBAAI,EAAE;AACtB,UAAM,cAAc,CAAC;AAGf,UAAA,WAAW,CAAC,SAAS;AACrB,UAAA,KAAK,cAAc,GAAG;AACxB,cAAM,QAAQ,SAAS;AAAA,MAAA,OAClB;AACL,oBAAY,cAAc,cAAc,EAAE,MAAM,MAAM;AACtD,eAAO,KAAK,EAAE,MAAM,aAAA,CAAc;AAAA,MAAA;AAAA,IAEtC;AAEA,aAAS,cAAc;AACrB,cAAQ,QAAQ;AACH,mBAAA;AAAA,IAAA;AAGf,aAAS,eAAe;AACtB,gBAAU,GAAG,EAAE;AAAA,IAAA;AAEX,UAAA,eAAe,CAAC,KAAK,SAAS;AAClC,UAAI,OAAO,OAAO;AAEbC,wBAAA,OAAO,8BAA8B,EAAE,IAAI,MAAM,EACjD,KAAK,CAAC,QAAa;AAClB,cAAI,IAAI,SAAS;AACf,kBAAM,QAAQ,OAAO;AACrB,mBAAO,MAAM,OAAO;AAAA,UAAA,OACf;AACL,kBAAM,QAAQ,OAAO;AAAA,UAAA;AAAA,QACvB,CACD,EACA,MAAM,CAAC,QAAQ;AACd,gBAAM,MAAM,OAAO;AAAA,QAAA,CACpB;AAAA,MAAA,WACO,MAAM,UAAW;AAExBA,wBAAA,OAAO,oCAAoC,EAAE,IAAI,MAAM,EACvD,KAAK,CAAC,QAAa;AAClB,cAAI,IAAI,SAAS;AACf,kBAAM,QAAQ,OAAO;AACrB,mBAAO,MAAM,OAAO;AAAA,UAAA,OACf;AACL,kBAAM,QAAQ,OAAO;AAAA,UAAA;AAAA,QACvB,CACD,EACA,MAAM,CAAC,QAAQ;AACd,gBAAM,MAAM,OAAO;AAAA,QAAA,CACpB;AAAA,MAAA;AAAA,IAEP;AACA,UAAM,YAAY,CAAC,EAAE,QAAQ,eAAe;AAC1C,YAAM,SAAc;AAAA,QAClB;AAAA,QACA;AAAA,QACA,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AACI,UAAA,QAAQ,MAAM,QAAQ;AACjB,eAAA,WAAW,IAAI,QAAQ,KAAK;AAAA,MAAA;AAE9B,aAAA;AAAA,IACT;AACM,UAAA,YAAY,CAAC,QAAQ,aAAa;AACtC,YAAM,SAAS,UAAU,EAAE,QAAQ,UAAU;AAE1CA,sBAAA,IAAI,4BAA4B,mBAAK,OAAQ,EAC7C,KAAK,CAAC,QAAa;;AAClB,YAAI,IAAI,aAAW,SAAI,WAAJ,mBAAY,UAAS;AACtC,cAAI,WAAW,GAAG;AAChB,qBAAS,QAAQ,CAAC;AAAA,UAAA;AAEpB,iBAAO,MAAM,SAAS,IAAI,OAAO,OAAO;AAAA,QAAA,OACnC;AACE,iBAAA,MAAM,SAAS,KAAK;AAAA,QAAA;AAAA,MAC7B,CACD,EACA,MAAM,CAAC,QAAQ;AACP,eAAA,MAAM,SAAS,KAAK;AAAA,MAAA,CAC5B;AAAA,IACL;AAgBM,UAAA,qBAAqB,CAAC,MAAM,UAAU;AACnC,aAAA,YAAY,QAAQ,YAAY,MAAM;AAAA,IAC/C;AACA,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACf,kBAAA,KAAKC,aAAAA,gBAAgB;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3KnC,GAAG,WAAW,eAAe;"}