{"version": 3, "file": "wd-form.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-form/wd-form.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1mb3JtL3dkLWZvcm0udnVl"], "sourcesContent": ["<template>\n  <view :class=\"`wd-form ${customClass}`\" :style=\"customStyle\">\n    <slot></slot>\n    <wd-toast v-if=\"props.errorType === 'toast'\" selector=\"wd-form-toast\" />\n  </view>\n</template>\n\n<script lang=\"ts\">\nexport default {\n  name: 'wd-form',\n  options: {\n    addGlobalClass: true,\n    virtualHost: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport wdToast from '../wd-toast/wd-toast.vue'\nimport { reactive, watch } from 'vue'\nimport { deepClone, getPropByPath, isArray, isDef, isPromise, isString } from '../common/util'\nimport { useChildren } from '../composables/useChildren'\nimport { useToast } from '../wd-toast'\nimport { type FormRules, FORM_KEY, type ErrorMessage, formProps, type FormExpose } from './types'\n\nconst { show: showToast } = useToast('wd-form-toast')\nconst props = defineProps(formProps)\n\nconst { children, linkChildren } = useChildren(FORM_KEY)\nlet errorMessages = reactive<Record<string, string>>({})\n\nlinkChildren({ props, errorMessages })\n\nwatch(\n  () => props.model,\n  () => {\n    if (props.resetOnChange) {\n      clearMessage()\n    }\n  },\n  { immediate: true, deep: true }\n)\n\n/**\n * 表单校验\n * @param prop 指定校验字段或字段数组\n */\nasync function validate(prop?: string | string[]): Promise<{ valid: boolean; errors: ErrorMessage[] }> {\n  const errors: ErrorMessage[] = []\n  let valid: boolean = true\n  const promises: Promise<void>[] = []\n  const formRules: FormRules = getMergeRules()\n  const propsToValidate = isArray(prop) ? prop : isDef(prop) ? [prop] : []\n  const rulesToValidate: FormRules =\n    propsToValidate.length > 0\n      ? propsToValidate.reduce((acc, key) => {\n          if (formRules[key]) {\n            acc[key] = formRules[key]\n          }\n          return acc\n        }, {} as FormRules)\n      : formRules\n\n  for (const propName in rulesToValidate) {\n    const rules = rulesToValidate[propName]\n    const value = getPropByPath(props.model, propName)\n\n    if (rules && rules.length > 0) {\n      for (const rule of rules) {\n        if (rule.required && (!isDef(value) || value === '')) {\n          errors.push({\n            prop: propName,\n            message: rule.message\n          })\n          valid = false\n          break\n        }\n        if (rule.pattern && !rule.pattern.test(value)) {\n          errors.push({\n            prop: propName,\n            message: rule.message\n          })\n          valid = false\n          break\n        }\n        const { validator, ...ruleWithoutValidator } = rule\n        if (validator) {\n          const result = validator(value, ruleWithoutValidator)\n          if (isPromise(result)) {\n            promises.push(\n              result\n                .then((res) => {\n                  if (typeof res === 'string') {\n                    errors.push({\n                      prop: propName,\n                      message: res\n                    })\n                    valid = false\n                  } else if (typeof res === 'boolean' && !res) {\n                    errors.push({\n                      prop: propName,\n                      message: rule.message\n                    })\n                    valid = false\n                  }\n                })\n                .catch((error?: string | Error) => {\n                  const message = isDef(error) ? (isString(error) ? error : error.message || rule.message) : rule.message\n                  errors.push({ prop: propName, message })\n                  valid = false\n                })\n            )\n          } else {\n            if (!result) {\n              errors.push({\n                prop: propName,\n                message: rule.message\n              })\n              valid = false\n            }\n          }\n        }\n      }\n    }\n  }\n\n  await Promise.all(promises)\n\n  showMessage(errors)\n\n  if (valid) {\n    if (propsToValidate.length) {\n      propsToValidate.forEach(clearMessage)\n    } else {\n      clearMessage()\n    }\n  }\n\n  return {\n    valid,\n    errors\n  }\n}\n\n// 合并子组件的rules到父组件的rules\nfunction getMergeRules() {\n  const mergedRules: FormRules = deepClone(props.rules)\n  const childrenProps = children.map((child) => child.prop)\n\n  // 过滤掉在 children 中不存在对应子组件的规则\n  Object.keys(mergedRules).forEach((key) => {\n    if (!childrenProps.includes(key)) {\n      delete mergedRules[key]\n    }\n  })\n\n  children.forEach((item) => {\n    if (isDef(item.prop) && isDef(item.rules) && item.rules.length) {\n      if (mergedRules[item.prop]) {\n        mergedRules[item.prop] = [...mergedRules[item.prop], ...item.rules]\n      } else {\n        mergedRules[item.prop] = item.rules\n      }\n    }\n  })\n  return mergedRules\n}\n\nfunction showMessage(errors: ErrorMessage[]) {\n  const childrenProps = children.map((e) => e.prop).filter(Boolean)\n  const messages = errors.filter((error) => error.message && childrenProps.includes(error.prop))\n  if (messages.length) {\n    messages.sort((a, b) => {\n      return childrenProps.indexOf(a.prop) - childrenProps.indexOf(b.prop)\n    })\n    if (props.errorType === 'toast') {\n      showToast(messages[0].message)\n    } else if (props.errorType === 'message') {\n      messages.forEach((error) => {\n        errorMessages[error.prop] = error.message\n      })\n    }\n  }\n}\n\nfunction clearMessage(prop?: string) {\n  if (prop) {\n    errorMessages[prop] = ''\n  } else {\n    Object.keys(errorMessages).forEach((key) => {\n      errorMessages[key] = ''\n    })\n  }\n}\n\n/**\n * 重置表单项的验证提示\n */\nfunction reset() {\n  clearMessage()\n}\n\ndefineExpose<FormExpose>({ validate, reset })\n</script>\n\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-form/wd-form.vue'\nwx.createComponent(Component)"], "names": ["useToast", "useChildren", "FORM_KEY", "reactive", "watch", "isArray", "isDef", "getPropByPath", "isPromise", "isString", "deepClone"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBA,MAAA,UAAoB,MAAA;AAXpB,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,gBAAgB;AAAA,EAAA;AAEpB;;;;AAWA,UAAM,EAAE,MAAM,cAAcA,cAAAA,SAAS,eAAe;AACpD,UAAM,QAAQ;AAEd,UAAM,EAAE,UAAU,iBAAiBC,cAAAA,YAAYC,cAAAA,QAAQ;AACnD,QAAA,gBAAgBC,cAAiC,SAAA,EAAE;AAE1C,iBAAA,EAAE,OAAO,eAAe;AAErCC,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,MAAM;AACJ,YAAI,MAAM,eAAe;AACV,uBAAA;AAAA,QAAA;AAAA,MAEjB;AAAA,MACA,EAAE,WAAW,MAAM,MAAM,KAAK;AAAA,IAChC;AAMA,aAAe,SAAS,MAA+E;AAAA;AACrG,cAAM,SAAyB,CAAC;AAChC,YAAI,QAAiB;AACrB,cAAM,WAA4B,CAAC;AACnC,cAAM,YAAuB,cAAc;AACrC,cAAA,kBAAkBC,sBAAQ,IAAI,IAAI,OAAOC,cAAM,MAAA,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;AACjE,cAAA,kBACJ,gBAAgB,SAAS,IACrB,gBAAgB,OAAO,CAAC,KAAK,QAAQ;AAC/B,cAAA,UAAU,GAAG,GAAG;AACd,gBAAA,GAAG,IAAI,UAAU,GAAG;AAAA,UAAA;AAEnB,iBAAA;AAAA,QAAA,GACN,CAAA,CAAe,IAClB;AAEN,mBAAW,YAAY,iBAAiB;AAChC,gBAAA,QAAQ,gBAAgB,QAAQ;AACtC,gBAAM,QAAQC,cAAA,cAAc,MAAM,OAAO,QAAQ;AAE7C,cAAA,SAAS,MAAM,SAAS,GAAG;AAC7B,uBAAW,QAAQ,OAAO;AACxB,kBAAI,KAAK,aAAa,CAACD,cAAAA,MAAM,KAAK,KAAK,UAAU,KAAK;AACpD,uBAAO,KAAK;AAAA,kBACV,MAAM;AAAA,kBACN,SAAS,KAAK;AAAA,gBAAA,CACf;AACO,wBAAA;AACR;AAAA,cAAA;AAEF,kBAAI,KAAK,WAAW,CAAC,KAAK,QAAQ,KAAK,KAAK,GAAG;AAC7C,uBAAO,KAAK;AAAA,kBACV,MAAM;AAAA,kBACN,SAAS,KAAK;AAAA,gBAAA,CACf;AACO,wBAAA;AACR;AAAA,cAAA;AAEF,oBAA+C,WAAvC,gBAAuC,IAAzB,iCAAyB,IAAzB,CAAd;AACR,kBAAI,WAAW;AACP,sBAAA,SAAS,UAAU,OAAO,oBAAoB;AAChD,oBAAAE,cAAAA,UAAU,MAAM,GAAG;AACZ,2BAAA;AAAA,oBACP,OACG,KAAK,CAAC,QAAQ;AACT,0BAAA,OAAO,QAAQ,UAAU;AAC3B,+BAAO,KAAK;AAAA,0BACV,MAAM;AAAA,0BACN,SAAS;AAAA,wBAAA,CACV;AACO,gCAAA;AAAA,sBACC,WAAA,OAAO,QAAQ,aAAa,CAAC,KAAK;AAC3C,+BAAO,KAAK;AAAA,0BACV,MAAM;AAAA,0BACN,SAAS,KAAK;AAAA,wBAAA,CACf;AACO,gCAAA;AAAA,sBAAA;AAAA,oBACV,CACD,EACA,MAAM,CAAC,UAA2B;AACjC,4BAAM,UAAUF,cAAA,MAAM,KAAK,IAAKG,cAAS,SAAA,KAAK,IAAI,QAAQ,MAAM,WAAW,KAAK,UAAW,KAAK;AAChG,6BAAO,KAAK,EAAE,MAAM,UAAU,SAAS;AAC/B,8BAAA;AAAA,oBACT,CAAA;AAAA,kBACL;AAAA,gBAAA,OACK;AACL,sBAAI,CAAC,QAAQ;AACX,2BAAO,KAAK;AAAA,sBACV,MAAM;AAAA,sBACN,SAAS,KAAK;AAAA,oBAAA,CACf;AACO,4BAAA;AAAA,kBAAA;AAAA,gBACV;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAGI,cAAA,QAAQ,IAAI,QAAQ;AAE1B,oBAAY,MAAM;AAElB,YAAI,OAAO;AACT,cAAI,gBAAgB,QAAQ;AAC1B,4BAAgB,QAAQ,YAAY;AAAA,UAAA,OAC/B;AACQ,yBAAA;AAAA,UAAA;AAAA,QACf;AAGK,eAAA;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MAAA;AAAA;AAIF,aAAS,gBAAgB;AACjB,YAAA,cAAyBC,cAAAA,UAAU,MAAM,KAAK;AACpD,YAAM,gBAAgB,SAAS,IAAI,CAAC,UAAU,MAAM,IAAI;AAGxD,aAAO,KAAK,WAAW,EAAE,QAAQ,CAAC,QAAQ;AACxC,YAAI,CAAC,cAAc,SAAS,GAAG,GAAG;AAChC,iBAAO,YAAY,GAAG;AAAA,QAAA;AAAA,MACxB,CACD;AAEQ,eAAA,QAAQ,CAAC,SAAS;AACrB,YAAAJ,oBAAM,KAAK,IAAI,KAAKA,cAAA,MAAM,KAAK,KAAK,KAAK,KAAK,MAAM,QAAQ;AAC1D,cAAA,YAAY,KAAK,IAAI,GAAG;AACd,wBAAA,KAAK,IAAI,IAAI,CAAC,GAAG,YAAY,KAAK,IAAI,GAAG,GAAG,KAAK,KAAK;AAAA,UAAA,OAC7D;AACO,wBAAA,KAAK,IAAI,IAAI,KAAK;AAAA,UAAA;AAAA,QAChC;AAAA,MACF,CACD;AACM,aAAA;AAAA,IAAA;AAGT,aAAS,YAAY,QAAwB;AACrC,YAAA,gBAAgB,SAAS,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,OAAO;AAC1D,YAAA,WAAW,OAAO,OAAO,CAAC,UAAU,MAAM,WAAW,cAAc,SAAS,MAAM,IAAI,CAAC;AAC7F,UAAI,SAAS,QAAQ;AACV,iBAAA,KAAK,CAAC,GAAG,MAAM;AACf,iBAAA,cAAc,QAAQ,EAAE,IAAI,IAAI,cAAc,QAAQ,EAAE,IAAI;AAAA,QAAA,CACpE;AACG,YAAA,MAAM,cAAc,SAAS;AACrB,oBAAA,SAAS,CAAC,EAAE,OAAO;AAAA,QAAA,WACpB,MAAM,cAAc,WAAW;AAC/B,mBAAA,QAAQ,CAAC,UAAU;AACZ,0BAAA,MAAM,IAAI,IAAI,MAAM;AAAA,UAAA,CACnC;AAAA,QAAA;AAAA,MACH;AAAA,IACF;AAGF,aAAS,aAAa,MAAe;AACnC,UAAI,MAAM;AACR,sBAAc,IAAI,IAAI;AAAA,MAAA,OACjB;AACL,eAAO,KAAK,aAAa,EAAE,QAAQ,CAAC,QAAQ;AAC1C,wBAAc,GAAG,IAAI;AAAA,QAAA,CACtB;AAAA,MAAA;AAAA,IACH;AAMF,aAAS,QAAQ;AACF,mBAAA;AAAA,IAAA;AAGU,aAAA,EAAE,UAAU,OAAO;;;;;;;;;;;;;;;;AC1M5C,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}