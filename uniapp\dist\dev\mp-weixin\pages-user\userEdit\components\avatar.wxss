/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.wrap.data-v-94e9fe44 {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.showArea.data-v-94e9fe44 {
  position: relative;
  width: -moz-fit-content;
  width: fit-content;
}
.showArea .iconBox.data-v-94e9fe44 {
  position: absolute;
  right: 0;
  top: 0;
  border-bottom-left-radius: 3px;
  padding: 3px 6px;
  height: auto;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1;
  color: #fff;
  font-size: 12px;
  line-height: 1;
  border-radius: 2px;
}
.showArea .avatar-button.data-v-94e9fe44 {
  padding: 0;
  border: none;
  background: transparent;
  display: block;
}
.uploadArea.data-v-94e9fe44 {
  width: 60px;
  height: 60px;
  border: none;
  background: transparent;
  padding: 0;
}
.uploadArea .iconBox.data-v-94e9fe44 {
  height: 100%;
  width: 100%;
  border: 2px solid #eee;
  color: #8799a3;
  margin: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  font-size: 27px;
}
.uploadArea.uploadArea-hover .iconBox.data-v-94e9fe44 {
  background-color: #f5f5f5;
}
.avatar-options.data-v-94e9fe44 {
  background: #fff;
  border-radius: 12px 12px 0 0;
  padding: 0;
}
.avatar-options .options-title.data-v-94e9fe44 {
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  padding: 20px 0 10px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 10px;
}
.avatar-options .options-list.data-v-94e9fe44 {
  padding: 0 20px;
}
.avatar-options .options-list .option-item.data-v-94e9fe44 {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border: none;
  background: transparent;
  width: 100%;
  text-align: left;
  font-size: 16px;
  color: #333;
  border-bottom: 1px solid #f5f5f5;
}
.avatar-options .options-list .option-item.data-v-94e9fe44:last-child {
  border-bottom: none;
}
.avatar-options .options-list .option-item.option-item-hover.data-v-94e9fe44 {
  background-color: #f5f5f5;
}
.avatar-options .options-list .option-item .option-icon.data-v-94e9fe44 {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.avatar-options .options-list .option-item .option-icon .cuIcon-weixin.data-v-94e9fe44 {
  color: #07C160;
  font-size: 20px;
}
.avatar-options .options-list .option-item .option-icon .cuIcon-pic.data-v-94e9fe44 {
  color: #576b95;
  font-size: 20px;
}
.avatar-options .options-list .option-item .option-icon .cuIcon-camera.data-v-94e9fe44 {
  color: #ff6b6b;
  font-size: 20px;
}
.avatar-options .options-list .option-item .option-text.data-v-94e9fe44 {
  flex: 1;
  font-size: 16px;
  color: #333;
}
.avatar-options .options-cancel.data-v-94e9fe44 {
  text-align: center;
  padding: 15px 0;
  font-size: 16px;
  color: #666;
  border-top: 8px solid #f5f5f5;
  cursor: pointer;
}
.avatar-options .options-cancel.data-v-94e9fe44:active {
  background-color: #f0f0f0;
}