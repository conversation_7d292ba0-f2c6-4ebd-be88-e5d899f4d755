"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
const utils_http = require("../../utils/http.js");
const utils_is = require("../../utils/is.js");
if (!Array) {
  const _easycom_wd_input2 = common_vendor.resolveComponent("wd-input");
  const _easycom_wd_popup2 = common_vendor.resolveComponent("wd-popup");
  (_easycom_wd_input2 + _easycom_wd_popup2)();
}
const _easycom_wd_input = () => "../../node-modules/wot-design-uni/components/wd-input/wd-input.js";
const _easycom_wd_popup = () => "../../node-modules/wot-design-uni/components/wd-popup/wd-popup.js";
if (!Math) {
  (_easycom_wd_input + DaTree + _easycom_wd_popup)();
}
const DaTree = () => "../../uni_modules/da-tree/index.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "CategorySelect"
}), {
  __name: "CategorySelect",
  props: {
    modelValue: {
      type: [Array, String]
    },
    placeholder: {
      type: String,
      default: "请选择",
      required: false
    },
    condition: {
      type: String,
      default: "",
      required: false
    },
    // 是否支持多选
    multiple: {
      type: Boolean,
      default: false
    },
    pid: {
      type: String,
      default: "",
      required: false
    },
    pcode: {
      type: String,
      default: "",
      required: false
    }
  },
  emits: ["change", "update:modelValue"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const toast = common_vendor.useToast();
    const api = {
      loadDictItem: "/sys/category/loadDictItem/",
      loadTreeData: "/sys/category/loadTreeData"
    };
    const showText = common_vendor.ref("");
    const popupShow = common_vendor.ref(false);
    const treeData = common_vendor.ref([]);
    const treeValue = common_vendor.ref([]);
    const handleClick = () => {
      popupShow.value = true;
    };
    const cancel = () => {
      popupShow.value = false;
    };
    const confirm = () => {
      const titles = treeValue.value.map((item) => item.title);
      const keys = treeValue.value.map((item) => item.key).join(",");
      showText.value = titles.join(",");
      popupShow.value = false;
      emit("update:modelValue", keys);
      emit("change", keys);
    };
    const handleTreeChange = (value, record) => {
      const { originItem, checkedStatus } = record;
      const { key, title } = originItem;
      if (checkedStatus) {
        if (props.multiple) {
          treeValue.value.push({ key, title });
        } else {
          treeValue.value = [{ key, title }];
        }
      } else {
        if (props.multiple) {
          const findIndex = treeValue.value.findIndex((item) => item.key == key);
          if (findIndex != -1) {
            treeValue.value.splice(findIndex, 1);
          }
        } else {
          treeValue.value = [];
        }
      }
    };
    const transformField = (result) => {
      for (let i of result) {
        i.value = i.key;
        if (i.leaf == false) {
          i.isLeaf = false;
        } else if (i.leaf == true) {
          i.isLeaf = true;
        }
      }
    };
    const asyncLoadTreeData = ({ originItem }) => {
      return new Promise((resolve) => {
        let param = {
          pid: originItem.key,
          condition: props.condition
        };
        utils_http.http.get(api.loadTreeData, param).then((res) => {
          if (res.success) {
            const { result } = res;
            transformField(result);
            resolve(result);
          } else {
            resolve(null);
          }
        }).catch((err) => resolve(null));
      });
    };
    function loadRoot() {
      let param = {
        pid: props.pid,
        pcode: !props.pcode ? "0" : props.pcode,
        condition: props.condition
      };
      utils_http.http.get(api.loadTreeData, param).then((res) => {
        if (res.success) {
          const { result } = res;
          if (result && result.length > 0) {
            transformField(result);
            treeData.value = result;
          }
        } else {
          toast.warning("分类字典书组件根节点数据加载失败~");
        }
      }).catch((err) => {
        toast.warning("分类字典书组件根节点数据加载失败~");
      });
    }
    function loadItemByCode() {
      let value = props.modelValue;
      if (utils_is.isArray(props.modelValue)) {
        value = value.join();
      }
      if (value === treeData.value.map((item) => item.key).join(",")) {
        return;
      }
      utils_http.http.get(api.loadDictItem, { ids: value }).then((res) => {
        if (res.success) {
          const { result = [] } = res;
          showText.value = result.join(",");
        }
      }).catch((err) => {
      });
    }
    common_vendor.watch(
      () => props.modelValue,
      () => {
        loadItemByCode();
      },
      { deep: true, immediate: true }
    );
    common_vendor.watch(
      () => props.pcode,
      () => {
        loadRoot();
      },
      { deep: true, immediate: true }
    );
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(($event) => showText.value = $event),
        b: common_vendor.p(__spreadProps(__spreadValues({
          placeholder: `请选择${_ctx.$attrs.label}`
        }, _ctx.$attrs), {
          readonly: true,
          modelValue: showText.value
        })),
        c: common_vendor.o(handleClick),
        d: common_vendor.o(cancel),
        e: common_vendor.o(confirm),
        f: common_vendor.o(handleTreeChange),
        g: common_vendor.p({
          data: treeData.value,
          labelField: "title",
          valueField: "key",
          loadMode: true,
          showCheckbox: __props.multiple,
          showRadioIcon: false,
          checkStrictly: true,
          loadApi: asyncLoadTreeData
        }),
        h: common_vendor.o(($event) => popupShow.value = $event),
        i: common_vendor.p({
          position: "bottom",
          modelValue: popupShow.value
        })
      };
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f91a7110"]]);
wx.createComponent(Component);
//# sourceMappingURL=CategorySelect.js.map
