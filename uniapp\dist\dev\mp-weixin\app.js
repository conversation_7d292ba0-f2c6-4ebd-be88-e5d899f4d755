"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
const router_index = require("./router/index.js");
const interceptors_route = require("./interceptors/route.js");
const interceptors_request = require("./interceptors/request.js");
const interceptors_prototype = require("./interceptors/prototype.js");
const store_index = require("./store/index.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/about/about.js";
  "./pages/annotation/annotationDetail.js";
  "./pages/annotation/annotationList.js";
  "./pages/data/detail.js";
  "./pages/data/index.js";
  "./pages/demo/demo.js";
  "./pages/demo/form.js";
  "./pages/demo/indexBar.js";
  "./pages/demo/selectPicker.js";
  "./pages/demo/tree.js";
  "./pages/index/data.js";
  "./pages/login/login.js";
  "./pages/message/message.js";
  "./pages/more/more.js";
  "./pages/user/people.js";
  "./pages/workHome/index.js";
  "./pages-home/home/<USER>";
  "./pages-message/chat/chat.js";
  "./pages-message/contacts/contacts.js";
  "./pages-message/personPage/personPage.js";
  "./pages-message/tenant/tenant.js";
  "./pages-user/location/location.js";
  "./pages-user/userEdit/userEdit.js";
  "./pages-work/dragPage/index.js";
  "./pages-work/onlinePage/onlineAdd.js";
  "./pages-work/onlinePage/onlineDetail.js";
  "./pages-work/onlinePage/onlineEdit.js";
  "./pages-sub/online/online.js";
  "./pages-sub/online/onlineCard.js";
  "./pages-sub/online/onlineTable.js";
  "./pages-data/medication/list.js";
  "./pages-data/medication/form.js";
  "./pages-data/vital-signs/list.js";
  "./pages-data/vital-signs/form.js";
  "./pages-data/examinationReport/list.js";
  "./pages-data/examinationReport/form.js";
  "./pages-data/monitor/list.js";
  "./pages-data/monitor/form.js";
  "./pages-data/psychology/list.js";
  "./pages-data/psychology/form.js";
  "./pages-data/dailyLife/list.js";
  "./pages-data/dailyLife/form.js";
  "./pages-data/registration/form.js";
  "./pages-data/registration/consent.js";
  "./pages-data/doctor/form.js";
  "./pages-data/social/form.js";
  "./pages-data/chat/chat.js";
  "./pages-data/chat/chatDetail.js";
  "./pages-data/chat/addChat.js";
}
const _sfc_main = {
  onLaunch: function(options) {
    console.log("App Launch");
    console.log("应用启动路径：", options.path);
  },
  onShow: function(options) {
    console.log("App Show");
    console.log("应用启动路径：", options.path);
    setTimeout(() => {
      options.path;
      router_index.beforEach({ path: "/" }, {}, (data) => {
        if (data == null ? void 0 : data.path) {
          common_vendor.index.redirectTo({ url: data.path });
        }
      });
    }, 100);
  },
  onHide: function() {
    console.log("App Hide");
  },
  // 全局变量
  globalData: {
    isLocalConfig: true,
    systemInfo: common_vendor.index.getSystemInfoSync(),
    navHeight: 44
  }
};
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  app.use(store_index.store);
  app.use(router_index.router);
  app.use(interceptors_route.routeInterceptor);
  app.use(interceptors_request.requestInterceptor);
  app.use(interceptors_prototype.prototypeInterceptor);
  app.use(common_vendor.VueQueryPlugin);
  app.component("layout-default-uni", Layout_Default_Uni);
  app.component("layout-demo-uni", Layout_Demo_Uni);
  return {
    app
  };
}
const Layout_Default_Uni = () => "./layouts/default.js";
const Layout_Demo_Uni = () => "./layouts/demo.js";
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=app.js.map
