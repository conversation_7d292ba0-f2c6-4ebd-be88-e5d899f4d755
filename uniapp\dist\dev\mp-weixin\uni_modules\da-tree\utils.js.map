{"version": 3, "file": "utils.js", "sources": ["../../../../../src/uni_modules/da-tree/utils.ts"], "sourcesContent": ["/** 未选 */\r\nexport const unCheckedStatus = 0\r\n/** 半选 */\r\nexport const halfCheckedStatus = 1\r\n/** 选中 */\r\nexport const isCheckedStatus = 2\r\n\r\n/**\r\n * 深拷贝内容\r\n * @param originData 拷贝对象\r\n * <AUTHOR>\r\n */\r\nexport function deepClone(originData) {\r\n  const type = Object.prototype.toString.call(originData)\r\n  let data\r\n  if (type === '[object Array]') {\r\n    data = []\r\n    for (let i = 0; i < originData.length; i++) {\r\n      data.push(deepClone(originData[i]))\r\n    }\r\n  } else if (type === '[object Object]') {\r\n    data = {}\r\n    for (const prop in originData) {\r\n      // eslint-disable-next-line no-prototype-builtins\r\n      if (originData.hasOwnProperty(prop)) { // 非继承属性\r\n        data[prop] = deepClone(originData[prop])\r\n      }\r\n    }\r\n  } else {\r\n    data = originData\r\n  }\r\n  return data\r\n}\r\n\r\n/**\r\n * 获取所有指定的节点\r\n * @param type\r\n * @param value\r\n * <AUTHOR>\r\n */\r\nexport function getAllNodes(list, type, value, packDisabledkey = true) {\r\n  if (!list || list.length === 0) {\r\n    return []\r\n  }\r\n\r\n  const res = []\r\n  for (let i = 0; i < list.length; i++) {\r\n    const item = list[i]\r\n    if (item[type] === value) {\r\n      if ((packDisabledkey && item.disabled) || !item.disabled) {\r\n        res.push(item)\r\n      }\r\n    }\r\n  }\r\n\r\n  return res\r\n}\r\n\r\n/**\r\n * 获取所有指定的key值\r\n * @param type\r\n * @param value\r\n * <AUTHOR>\r\n */\r\nexport function getAllNodeKeys(list, type, value, packDisabledkey = true) {\r\n  if (!list || list.length === 0) {\r\n    return null\r\n  }\r\n\r\n  const res = []\r\n  for (let i = 0; i < list.length; i++) {\r\n    const item = list[i]\r\n    if (item[type] === value) {\r\n      if ((packDisabledkey && item.disabled) || !item.disabled) {\r\n        res.push(item.key)\r\n      }\r\n    }\r\n  }\r\n\r\n  return res.length ? res : null\r\n}\r\n\r\n/**\r\n * 错误输出\r\n *\r\n * @param msg\r\n */\r\nexport function logError(msg, ...args) {\r\n  console.error(`DaTree: ${msg}`, ...args)\r\n}\r\n\r\nconst toString = Object.prototype.toString\r\n\r\nexport function is(val, type) {\r\n  return toString.call(val) === `[object ${type}]`\r\n}\r\n\r\n/**\r\n * 是否对象(Object)\r\n * @param val\r\n\r\n */\r\nexport function isObject(val) {\r\n  return val !== null && is(val, 'Object')\r\n}\r\n\r\n/**\r\n * 是否数字(Number)\r\n * @param val\r\n\r\n */\r\nexport function isNumber(val) {\r\n  return is(val, 'Number')\r\n}\r\n\r\n/**\r\n * 是否字符串(String)\r\n * @param val\r\n\r\n */\r\nexport function isString(val) {\r\n  return is(val, 'String')\r\n}\r\n\r\n/**\r\n * 是否函数方法(Function)\r\n * @param val\r\n\r\n */\r\nexport function isFunction(val) {\r\n  return typeof val === 'function'\r\n}\r\n\r\n/**\r\n * 是否布尔(Boolean)\r\n * @param val\r\n\r\n */\r\nexport function isBoolean(val) {\r\n  return is(val, 'Boolean')\r\n}\r\n\r\n/**\r\n * 是否数组(Array)\r\n * @param val\r\n\r\n */\r\nexport function isArray(val) {\r\n  return val && Array.isArray(val)\r\n}\r\n"], "names": [], "mappings": ";AACO,MAAM,kBAAkB;AAExB,MAAM,oBAAoB;AAE1B,MAAM,kBAAkB;AAOxB,SAAS,UAAU,YAAY;AACpC,QAAM,OAAO,OAAO,UAAU,SAAS,KAAK,UAAU;AAClD,MAAA;AACJ,MAAI,SAAS,kBAAkB;AAC7B,WAAO,CAAC;AACR,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,WAAK,KAAK,UAAU,WAAW,CAAC,CAAC,CAAC;AAAA,IAAA;AAAA,EACpC,WACS,SAAS,mBAAmB;AACrC,WAAO,CAAC;AACR,eAAW,QAAQ,YAAY;AAEzB,UAAA,WAAW,eAAe,IAAI,GAAG;AACnC,aAAK,IAAI,IAAI,UAAU,WAAW,IAAI,CAAC;AAAA,MAAA;AAAA,IACzC;AAAA,EACF,OACK;AACE,WAAA;AAAA,EAAA;AAEF,SAAA;AACT;AAQO,SAAS,YAAY,MAAM,MAAM,OAAO,kBAAkB,MAAM;AACrE,MAAI,CAAC,QAAQ,KAAK,WAAW,GAAG;AAC9B,WAAO,CAAC;AAAA,EAAA;AAGV,QAAM,MAAM,CAAC;AACb,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAC9B,UAAA,OAAO,KAAK,CAAC;AACf,QAAA,KAAK,IAAI,MAAM,OAAO;AACxB,UAAK,mBAAmB,KAAK,YAAa,CAAC,KAAK,UAAU;AACxD,YAAI,KAAK,IAAI;AAAA,MAAA;AAAA,IACf;AAAA,EACF;AAGK,SAAA;AACT;AAQO,SAAS,eAAe,MAAM,MAAM,OAAO,kBAAkB,MAAM;AACxE,MAAI,CAAC,QAAQ,KAAK,WAAW,GAAG;AACvB,WAAA;AAAA,EAAA;AAGT,QAAM,MAAM,CAAC;AACb,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAC9B,UAAA,OAAO,KAAK,CAAC;AACf,QAAA,KAAK,IAAI,MAAM,OAAO;AACxB,UAAK,mBAAmB,KAAK,YAAa,CAAC,KAAK,UAAU;AACpD,YAAA,KAAK,KAAK,GAAG;AAAA,MAAA;AAAA,IACnB;AAAA,EACF;AAGK,SAAA,IAAI,SAAS,MAAM;AAC5B;AAOgB,SAAA,SAAS,QAAQ,MAAM;AACrC,UAAQ,MAAM,WAAW,GAAG,IAAI,GAAG,IAAI;AACzC;AAEA,MAAM,WAAW,OAAO,UAAU;AAElB,SAAA,GAAG,KAAK,MAAM;AAC5B,SAAO,SAAS,KAAK,GAAG,MAAM,WAAW,IAAI;AAC/C;AAgBO,SAAS,SAAS,KAAK;AACrB,SAAA,GAAG,KAAK,QAAQ;AACzB;AAOO,SAAS,SAAS,KAAK;AACrB,SAAA,GAAG,KAAK,QAAQ;AACzB;AAOO,SAAS,WAAW,KAAK;AAC9B,SAAO,OAAO,QAAQ;AACxB;AAgBO,SAAS,QAAQ,KAAK;AACpB,SAAA,OAAO,MAAM,QAAQ,GAAG;AACjC;;;;;;;;;;;;"}