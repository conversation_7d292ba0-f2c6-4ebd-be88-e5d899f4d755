{"version": 3, "file": "http.js", "sources": ["../../../../src/utils/http.ts"], "sourcesContent": ["import { CustomRequestOptions } from '@/interceptors/request'\r\nimport { useUserStore } from '@/store/user'\r\nimport signMd5Utils from '@/utils/signMd5Utils'\r\nimport { isH5 } from '@/utils/platform'\r\n\r\nexport const http = <T>(options: CustomRequestOptions) => {\r\n  // 1. 返回 Promise 对象\r\n  return new Promise<IResData<T>>((resolve, reject) => {\r\n    const userStore = useUserStore()\r\n    //update-begin-author:liusq date:20240422 for: post请求接口加签参数设置\r\n    let params = options.query\r\n    if (options.data && Object.keys(options.data).length > 0) {\r\n      params = Object.assign({}, options?.query, options?.data)\r\n    }\r\n    let sign = signMd5Utils.getSign(options.url, params)\r\n    let vSign = signMd5Utils.getVSign(options.data, sign)\r\n    if (JSON.parse(import.meta.env.VITE_USE_MOCK) && JSON.parse(import.meta.env.VITE_APP_PROXY) && isH5) {\r\n      // 开始mock时，加上前缀\r\n      options.url = import.meta.env.VITE_APP_PROXY_PREFIX + options.url\r\n    }\r\n    uni.request({\r\n      dataType: 'json',\r\n      // #ifndef MP-WEIXIN\r\n      responseType: 'json',\r\n      // #endif\r\n      header: {\r\n        'X-Access-Token': userStore.userInfo.token,\r\n        'X-Tenant-Id': userStore.userInfo.tenantId,\r\n        'X-Sign': sign,\r\n        'V-Sign': vSign,\r\n        'X-TIMESTAMP': signMd5Utils.getTimestamp(),\r\n      },\r\n      ...options,\r\n      // 响应成功\r\n      success(res) {\r\n        // 状态码 2xx，参考 axios 的设计\r\n        if (res.statusCode >= 200 && res.statusCode < 300) {\r\n          // 2.1 提取核心数据 res.data\r\n          resolve(res.data as IResData<T>)\r\n        } else {\r\n          switch (res.statusCode) {\r\n            case 401:\r\n              // 401错误  -> 清理用户信息，跳转到登录页\r\n              userStore.clearUserInfo()\r\n              uni.navigateTo({ url: '/pages/login/login' })\r\n              break\r\n            // case 500:\r\n            //   break\r\n            default:\r\n              // 其他错误 -> 根据后端错误信息轻提示\r\n              !options.hideErrorToast &&\r\n                uni.showToast({\r\n                  icon: 'none',\r\n                  title: (res.data as IResData<T>).msg || '请求错误',\r\n                })\r\n          }\r\n          // 使用z-paging，在底层的网络请求抛出异常时uni.$emit('z-paging-error-emit')，业务中可不写\r\n          uni.$emit('z-paging-error-emit')\r\n          reject(res)\r\n        }\r\n      },\r\n      // 响应失败\r\n      fail(err) {\r\n        uni.showToast({\r\n          icon: 'none',\r\n          title: '网络错误，换个网络试试',\r\n        })\r\n        reject(err)\r\n      },\r\n    })\r\n  })\r\n}\r\n\r\n/**\r\n * GET 请求\r\n * @param url 后台地址\r\n * @param query 请求query参数\r\n * @returns\r\n */\r\nexport const httpGet = <T>(url: string, query?: Record<string, any>, header?: any) => {\r\n  return http<T>({\r\n    url,\r\n    query,\r\n    method: 'GET',\r\n  })\r\n}\r\n\r\n/**\r\n * POST 请求\r\n * @param url 后台地址\r\n * @param data 请求body参数\r\n * @param query 请求query参数，post请求也支持query，很多微信接口都需要\r\n * @returns\r\n */\r\nexport const httpPost = <T>(\r\n  url: string,\r\n  data?: Record<string, any>,\r\n  query?: Record<string, any>,\r\n) => {\r\n  return http<T>({\r\n    url,\r\n    query,\r\n    data,\r\n    method: 'POST',\r\n  })\r\n}\r\n/**\r\n * PUT 请求\r\n * @param url 后台地址\r\n * @param data 请求body参数\r\n * @param query 请求query参数，post请求也支持query，很多微信接口都需要\r\n * @returns\r\n */\r\nexport const httpPUT = <T>(\r\n  url: string,\r\n  data?: Record<string, any>,\r\n  query?: Record<string, any>,\r\n) => {\r\n  return http<T>({\r\n    url,\r\n    query,\r\n    data,\r\n    method: 'PUT',\r\n  })\r\n}\r\n/**\r\n * DELETE 请求\r\n * @param url 后台地址\r\n * @param query 请求query参数\r\n * @returns\r\n */\r\nexport const httpDelete = <T>(url: string, query?: Record<string, any>, header?: any) => {\r\n  return http<T>({\r\n    url,\r\n    query,\r\n    method: 'DELETE',\r\n  })\r\n}\r\nhttp.get = httpGet\r\nhttp.post = httpPost\r\nhttp.put = httpPUT\r\nhttp.delete = httpDelete\r\n"], "names": ["useUserStore", "signMd5Utils", "isH5", "uni"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAKa,MAAA,OAAO,CAAI,YAAkC;AAExD,SAAO,IAAI,QAAqB,CAAC,SAAS,WAAW;AACnD,UAAM,YAAYA,WAAAA,aAAa;AAE/B,QAAI,SAAS,QAAQ;AACjB,QAAA,QAAQ,QAAQ,OAAO,KAAK,QAAQ,IAAI,EAAE,SAAS,GAAG;AACxD,eAAS,OAAO,OAAO,CAAA,GAAI,mCAAS,OAAO,mCAAS,IAAI;AAAA,IAAA;AAE1D,QAAI,OAAOC,mBAAAA,aAAa,QAAQ,QAAQ,KAAK,MAAM;AACnD,QAAI,QAAQA,mBAAAA,aAAa,SAAS,QAAQ,MAAM,IAAI;AAChD,QAAA,KAAK,MAAM,MAA6B,KAAK,KAAK,MAAM,MAA8B,KAAKC;AAAM;AAIrGC,kBAAAA,MAAI,QAAQ;AAAA,MACV,UAAU;AAAA,MAIV,QAAQ;AAAA,QACN,kBAAkB,UAAU,SAAS;AAAA,QACrC,eAAe,UAAU,SAAS;AAAA,QAClC,UAAU;AAAA,QACV,UAAU;AAAA,QACV,eAAeF,gCAAa,aAAa;AAAA,MAC3C;AAAA,OACG,UAZO;AAAA;AAAA,MAcV,QAAQ,KAAK;AAEX,YAAI,IAAI,cAAc,OAAO,IAAI,aAAa,KAAK;AAEjD,kBAAQ,IAAI,IAAmB;AAAA,QAAA,OAC1B;AACL,kBAAQ,IAAI,YAAY;AAAA,YACtB,KAAK;AAEH,wBAAU,cAAc;AACxBE,4BAAAA,MAAI,WAAW,EAAE,KAAK,qBAAA,CAAsB;AAC5C;AAAA,YAGF;AAEG,eAAA,QAAQ,kBACPA,cAAA,MAAI,UAAU;AAAA,gBACZ,MAAM;AAAA,gBACN,OAAQ,IAAI,KAAqB,OAAO;AAAA,cAAA,CACzC;AAAA,UAAA;AAGPA,wBAAA,MAAI,MAAM,qBAAqB;AAC/B,iBAAO,GAAG;AAAA,QAAA;AAAA,MAEd;AAAA;AAAA,MAEA,KAAK,KAAK;AACRA,sBAAAA,MAAI,UAAU;AAAA,UACZ,MAAM;AAAA,UACN,OAAO;AAAA,QAAA,CACR;AACD,eAAO,GAAG;AAAA,MAAA;AAAA,IACZ,EACD;AAAA,EAAA,CACF;AACH;AAQO,MAAM,UAAU,CAAI,KAAa,OAA6B,WAAiB;AACpF,SAAO,KAAQ;AAAA,IACb;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,EAAA,CACT;AACH;AASO,MAAM,WAAW,CACtB,KACA,MACA,UACG;AACH,SAAO,KAAQ;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,EAAA,CACT;AACH;AAQO,MAAM,UAAU,CACrB,KACA,MACA,UACG;AACH,SAAO,KAAQ;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,EAAA,CACT;AACH;AAOO,MAAM,aAAa,CAAI,KAAa,OAA6B,WAAiB;AACvF,SAAO,KAAQ;AAAA,IACb;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,EAAA,CACT;AACH;AACA,KAAK,MAAM;AACX,KAAK,OAAO;AACZ,KAAK,MAAM;AACX,KAAK,SAAS;;"}