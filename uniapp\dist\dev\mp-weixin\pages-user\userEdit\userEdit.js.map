{"version": 3, "file": "userEdit.js", "sources": ["../../../../../src/pages-user/userEdit/userEdit.vue", "../../../../../uniPage:/cGFnZXMtdXNlclx1c2VyRWRpdFx1c2VyRWRpdC52dWU"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n  <PageLayout navTitle=\"编辑资料\" backRouteName=\"people\" routeMethod=\"pushTab\">\r\n    <wd-form custom-class=\"pt3\" ref=\"form\" :model=\"model\">\r\n      <wd-cell-group border>\r\n        <wd-input label=\"用户名\" prop=\"username\" clearable label-width=\"70px\" v-model=\"model.username\" :readonly=\"true\"\r\n          placeholder=\"请输入用户名\" /> <wd-input label=\"姓名\" prop=\"realname\" clearable label-width=\"70px\"\r\n          v-model=\"model.realname\" placeholder=\"请输入用户名\" :rules=\"[{ validator: rules.realname }]\" />\r\n        <!-- 病案号字段，仅患者端显示 -->\r\n        <wd-input v-if=\"userStore.userInfo.userCategory === 1\" label=\"病案号\" prop=\"caseNumber\" clearable\r\n          label-width=\"70px\" v-model=\"model.caseNumber\" placeholder=\"请输入病案号\" />\r\n        <wd-cell title=\"头像\" title-width=\"70px\">\r\n          <avatar v-model=\"model.avatar\" @update:fileName=\"avatarFileName = $event\"></avatar>\r\n        </wd-cell>\r\n        <wd-select-picker label=\"性别\" type=\"radio\" v-model=\"model.sex\" :columns=\"columns\" title=\"请选择性别\"\r\n          :safe-area-inset-bottom=\"false\"></wd-select-picker> <wd-input label=\"手机号\" prop=\"phone\" clearable\r\n          label-width=\"70px\" v-model=\"model.phone\" :readonly=\"true\" placeholder=\"请输入手机号\"\r\n          :rules=\"[{ validator: rules.phone }]\" />\r\n        <!-- <wd-input\r\n          label=\"邮箱\"\r\n          prop=\"email\"\r\n          clearable\r\n          label-width=\"70px\"\r\n          v-model=\"model.email\"\r\n          :readonly=\"true\"\r\n          placeholder=\"请输入邮箱\"\r\n          :rules=\"[{ validator: rules.email }]\"\r\n        /> -->\r\n      </wd-cell-group>\r\n      <view class=\"footer p5\">\r\n        <wd-button type=\"primary\" size=\"large\" @click=\"handleSubmit\" block\r\n          style=\"background-color: #07C160; border-color: #07C160;\">提交</wd-button>\r\n      </view>\r\n    </wd-form>\r\n  </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { reactive, ref } from 'vue'\r\nimport { http } from '@/utils/http'\r\nimport { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'\r\nimport { useRouter } from '@/plugin/uni-mini-router'\r\nimport { useUserStore } from '@/store/user'\r\nimport avatar from './components/avatar.vue'\r\n\r\ndefineOptions({\r\n  name: 'chatList',\r\n  options: {\r\n    styleIsolation: 'shared',\r\n  },\r\n})\r\nconst toast = useToast()\r\nconst router = useRouter()\r\nconst userStore = useUserStore()\r\nconst columns = [\r\n  { label: '男', value: 1 },\r\n  { label: '女', value: 2 },\r\n]\r\nconst model = reactive({\r\n  username: userStore.userInfo.username,\r\n  realname: userStore.userInfo.realname,\r\n  avatar: userStore.userInfo.avatar,\r\n  sex: userStore.userInfo.sex,\r\n  phone: userStore.userInfo.phone,\r\n  // email: userStore.userInfo.email,\r\n  caseNumber: userStore.userInfo.caseNumber,\r\n})\r\n\r\n// 保存头像文件名，用于提交给后端\r\nconst avatarFileName = ref('')\r\nconst rules = {\r\n  realname: (value) => {\r\n    if (value?.trim()?.length) {\r\n      return true\r\n    } else {\r\n      toast.warning('请输入名称')\r\n      return false\r\n    }\r\n  },\r\n  phone: (value) => {\r\n    if (value?.trim()?.length) {\r\n      if (/^1[3-9]\\d{9}$/.test(value)) {\r\n        return true\r\n      } else {\r\n        toast.warning('请输入正确的手机号')\r\n        return false\r\n      }\r\n    } else {\r\n      toast.warning('请输入手机号')\r\n      return false\r\n    }\r\n  },\r\n  email: (value) => {\r\n    if (value?.trim()?.length) {\r\n      if (/^[a-zA-Z0-9_%+-]+(\\.[a-zA-Z0-9_%+-]+)*@([a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,}$/.test(value)) {\r\n        return true\r\n      } else {\r\n        toast.warning('请输入正确的邮箱')\r\n        return false\r\n      }\r\n    } else {\r\n      toast.warning('请输入邮箱')\r\n      return false\r\n    }\r\n  },\r\n}\r\nconst form = ref()\r\n\r\nfunction handleSubmit() {\r\n  form.value\r\n    .validate()\r\n    .then(({ valid, errors }) => {\r\n      if (valid) {\r\n        if (!model.avatar) {\r\n          toast.warning('上传头像')\r\n          return\r\n        }\r\n        // toast.success('校验通过')\r\n        const data = { ...model, id: userStore.userInfo.userid }\r\n        delete data.username\r\n\r\n        // 如果有新上传的头像，使用fileName，否则保持原有的avatar值\r\n        if (avatarFileName.value) {\r\n          data.avatar = avatarFileName.value\r\n        }\r\n\r\n        uni.showLoading()\r\n        http\r\n          .post('/sys/user/login/setting/userEdit', { ...data })\r\n          .then((res: any) => {\r\n            uni.hideLoading()\r\n            if (res.success) {\r\n              toast.success('修改成功~')\r\n              setTimeout(() => {\r\n                userStore.editUserInfo({ ...data })\r\n                router.replaceAll({ name: 'people' })\r\n              }, 1e3)\r\n            } else {\r\n              toast.warning(res.message)\r\n            }\r\n          })\r\n          .catch(() => {\r\n            toast.error('提交失败')\r\n            uni.hideLoading()\r\n          })\r\n      }\r\n    })\r\n    .catch((error) => {\r\n      console.log(error, 'error')\r\n    })\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n//\r\n:deep(.wd-cell) {\r\n  .wd-cell__left {\r\n    width: 70px;\r\n    flex: none;\r\n  }\r\n\r\n  .wd-cell__value {\r\n    text-align: left;\r\n  }\r\n}\r\n\r\n// 头像单元格对齐\r\n:deep(.wd-cell) {\r\n  .wd-cell__value {\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-user/userEdit/userEdit.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useToast", "useRouter", "useUserStore", "reactive", "ref", "uni", "http"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CA,MAAA,SAAmB,MAAA;;;;;;;;;AAQnB,UAAM,QAAQA,cAAAA,SAAS;AACvB,UAAM,SAASC,gCAAAA,UAAU;AACzB,UAAM,YAAYC,WAAAA,aAAa;AAC/B,UAAM,UAAU;AAAA,MACd,EAAE,OAAO,KAAK,OAAO,EAAE;AAAA,MACvB,EAAE,OAAO,KAAK,OAAO,EAAE;AAAA,IACzB;AACA,UAAM,QAAQC,cAAAA,SAAS;AAAA,MACrB,UAAU,UAAU,SAAS;AAAA,MAC7B,UAAU,UAAU,SAAS;AAAA,MAC7B,QAAQ,UAAU,SAAS;AAAA,MAC3B,KAAK,UAAU,SAAS;AAAA,MACxB,OAAO,UAAU,SAAS;AAAA;AAAA,MAE1B,YAAY,UAAU,SAAS;AAAA,IAAA,CAChC;AAGK,UAAA,iBAAiBC,kBAAI,EAAE;AAC7B,UAAM,QAAQ;AAAA,MACZ,UAAU,CAAC,UAAU;;AACf,aAAA,oCAAO,WAAP,mBAAe,QAAQ;AAClB,iBAAA;AAAA,QAAA,OACF;AACL,gBAAM,QAAQ,OAAO;AACd,iBAAA;AAAA,QAAA;AAAA,MAEX;AAAA,MACA,OAAO,CAAC,UAAU;;AACZ,aAAA,oCAAO,WAAP,mBAAe,QAAQ;AACrB,cAAA,gBAAgB,KAAK,KAAK,GAAG;AACxB,mBAAA;AAAA,UAAA,OACF;AACL,kBAAM,QAAQ,WAAW;AAClB,mBAAA;AAAA,UAAA;AAAA,QACT,OACK;AACL,gBAAM,QAAQ,QAAQ;AACf,iBAAA;AAAA,QAAA;AAAA,MACT;AAAA,IAeJ;AACA,UAAM,OAAOA,cAAAA,IAAI;AAEjB,aAAS,eAAe;AACjB,WAAA,MACF,SACA,EAAA,KAAK,CAAC,EAAE,OAAO,aAAa;AAC3B,YAAI,OAAO;AACL,cAAA,CAAC,MAAM,QAAQ;AACjB,kBAAM,QAAQ,MAAM;AACpB;AAAA,UAAA;AAGF,gBAAM,OAAO,iCAAK,QAAL,EAAY,IAAI,UAAU,SAAS,OAAO;AACvD,iBAAO,KAAK;AAGZ,cAAI,eAAe,OAAO;AACxB,iBAAK,SAAS,eAAe;AAAA,UAAA;AAG/BC,wBAAAA,MAAI,YAAY;AAEbC,0BAAA,KAAK,oCAAoC,mBAAK,KAAM,EACpD,KAAK,CAAC,QAAa;AAClBD,0BAAAA,MAAI,YAAY;AAChB,gBAAI,IAAI,SAAS;AACf,oBAAM,QAAQ,OAAO;AACrB,yBAAW,MAAM;AACf,0BAAU,aAAa,mBAAK,KAAM;AAClC,uBAAO,WAAW,EAAE,MAAM,SAAA,CAAU;AAAA,iBACnC,GAAG;AAAA,YAAA,OACD;AACC,oBAAA,QAAQ,IAAI,OAAO;AAAA,YAAA;AAAA,UAC3B,CACD,EACA,MAAM,MAAM;AACX,kBAAM,MAAM,MAAM;AAClBA,0BAAAA,MAAI,YAAY;AAAA,UAAA,CACjB;AAAA,QAAA;AAAA,MACL,CACD,EACA,MAAM,CAAC,UAAU;AACR,gBAAA,IAAI,OAAO,OAAO;AAAA,MAAA,CAC3B;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvJL,GAAG,WAAW,eAAe;"}