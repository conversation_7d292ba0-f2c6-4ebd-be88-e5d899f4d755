<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationBarTitleText: '新增医患沟通',
        navigationStyle: 'custom'
      },
    }
</route>
<template>
  <PageLayout :navLeftArrow="true" navLeftText="返回">
    <scroll-view class="chat-list" scroll-y="true">
      <!-- 优化的头像和姓名区域 -->
      <view class="user-header-section">
        <view class="user-info-card">
          <image
            class="user-avatar"
            :src="getAvatarUrl(userStore.userInfo.avatar)"
            mode="aspectFill"
            @error="handleAvatarError"
          />
          <view class="user-details">
            <text class="user-name">{{ userStore.userInfo.realname || '未知用户' }}</text>
            <text class="user-role">{{ getUserRoleText() }}</text>
          </view>
        </view>
      </view>



      <!-- 表单内容区域 -->
      <view class="form-content">
        <!-- 医生选择下拉菜单 -->
        <view class="form-item">
          <view class="form-label">选择医生 <text class="optional">（可选择多个）</text></view>
          <view class="doctor-select-container">
            <!-- 下拉菜单 -->
            <picker
              mode="selector"
              :range="doctorList"
              range-key="realName"
              @change="onDoctorSelect"
              class="doctor-picker"
            >
              <view class="picker-display">
                <text class="picker-text">{{ selectedDoctors.length > 0 ? '点击选择更多医生' : '请选择医生' }}</text>
                <text class="picker-arrow">▼</text>
              </view>
            </picker>

            <!-- 已选择的医生显示 -->
            <view v-if="selectedDoctors.length > 0" class="selected-doctors-display">
              <view
                v-for="(doctor, index) in selectedDoctors"
                :key="doctor.userId || index"
                class="doctor-tag"
                @click="removeDoctorSelection(index)"
              >
                <text class="doctor-name">@{{ doctor.realName || '未知医生' }}</text>
                <text class="remove-doctor">×</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 标题输入框 -->
        <view class="form-item">
          <view class="form-label">问题标题 <text class="optional">（可选）</text></view>
          <input
            class="form-input title-input"
            v-model="formData.title"
            placeholder="请输入问题标题（可选）"
            maxlength="50"
          />
          <view class="char-count">{{ formData.title.length }}/50</view>
        </view>

        <!-- 问题描述输入框 -->
        <view class="form-item">
          <view class="form-label">问题描述 <text class="required">*</text></view>
          <textarea
            class="form-textarea question-input"
            v-model="formData.question"
            placeholder="请详细描述您的问题..."
            maxlength="1000"
            :auto-height="true"
            :show-confirm-bar="false"
          />
          <view class="char-count">{{ formData.question.length }}/1000</view>
        </view>

        <!-- 图片上传区域 -->
        <view class="form-item">
          <view class="form-label">上传图片 <text class="optional">（最多6张）</text></view>
          <view class="image-upload-area">
            <!-- 已选择的图片 -->
            <view class="uploaded-images" v-if="selectedImages.length > 0">
              <view
                class="image-item"
                v-for="(img, index) in selectedImages"
                :key="index"
                @click="previewImage(img)"
              >
                <image class="uploaded-image" :src="img" mode="aspectFill" />
                <view class="remove-image" @click.stop="removeImage(index)">×</view>
              </view>
            </view>
            <!-- 上传按钮 -->
            <view class="upload-buttons">
              <button class="upload-btn" @click="chooseFromGallery" :disabled="selectedImages.length >= 6">
                📷 从相册选择
              </button>
              <button class="upload-btn" @click="takePhoto" :disabled="selectedImages.length >= 6">
                📸 拍照
              </button>
            </view>
          </view>
        </view>

        <!-- 提交按钮 -->
        <view class="submit-section">
          <button
            class="submit-btn"
            @click="submitQuestion"
            :disabled="isSubmitting || !formData.question.trim()"
            :class="{ 'disabled': isSubmitting || !formData.question.trim() }"
          >
            {{ isSubmitting ? '提交中...' : '提交问题' }}
          </button>
        </view>
      </view>
    </scroll-view>
  </PageLayout>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { useUserStore } from '@/store/user';
import { http } from '@/utils/http';

const userStore = useUserStore();

// 表单数据
const formData = ref({
  title: '',
  question: '',
  patientId: ''
});

// 图片相关
const selectedImages = ref<string[]>([]);
const isSubmitting = ref(false);

// 医生选择相关
const doctorList = ref<any[]>([]);
const selectedDoctors = ref<any[]>([]);
const loadingDoctors = ref(false);

// 选中的医生信息（保持兼容性）
const selectedDoctorIds = ref('');
const selectedDoctorNames = ref('');

// 默认头像
const defAvatar = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNGNUY1RjUiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iIzk5OTk5OSIvPgo8cGF0aCBkPSJNMzAgMzJDMzAgMjYuNDc3MSAyNS41MjI5IDIyIDIwIDIyQzE0LjQ3NzEgMjIgMTAgMjYuNDc3MSAxMCAzMkgzMFoiIGZpbGw9IiM5OTk5OTkiLz4KPC9zdmc+'


// 通用头像处理函数
const getAvatarUrl = (avatar: string | null | undefined) => {

  // 如果头像为空、null、undefined或空字符串，返回默认头像
  if (!avatar || avatar.trim() === '') {
    return defAvatar;
  }

  // 检查是否已经是完整URL (http或https开头)
  if (avatar.startsWith('http://') || avatar.startsWith('https://')) {
    return avatar;
  }

  // 检查是否是base64格式
  if (avatar.startsWith('data:image/')) {
    return avatar;
  }

  // 如果是相对路径，拼接基础URL
  if (avatar.startsWith('/')) {
    const fullUrl = 'https://www.mograine.cn' + avatar;
    return fullUrl;
  }

  // 如果是文件名，拼接完整路径
  const fullUrl = 'https://www.mograine.cn/images/' + avatar;
  return fullUrl;
}

// 头像加载错误处理
const handleAvatarError = (event: any) => {
  event.target.src = defAvatar;
}

// 获取用户角色文本
const getUserRoleText = () => {
  const userCategory = Number(userStore.userInfo.userCategory);
  switch (userCategory) {
    case 0:
      return '医生';
    case 1:
      return '患者';
    case 2:
      return '社工/社区医生';
    default:
      return '用户';
  }
}

// 获取医生列表
const fetchDoctorList = async () => {
  try {
    loadingDoctors.value = true;
    const response = await http.get('/sys/user/doctorList');

    if (response.success && response.result) {
      // 如果result是对象且包含records数组，使用records
      let doctorData = response.result;

      console.log('doctorData',doctorData)
            // 处理医生数据，直接使用API返回的realName字段
      const doctors = (doctorData || []).map((doctor: any, index: number) => {

        // 确保使用API返回的原始realName字段
        if (!doctor.realName) {
          console.warn(`⚠️ 医生${index}缺少realName字段:`, doctor);
        }

        return {
          ...doctor,
          // 确保有唯一标识符
          userId: doctor.id || doctor.userId || doctor.username || `doctor_${index}`,
          // 保持API返回的原始realName，不做任何修改
          realName: doctor.realname || '未知医生',
          displayName: doctor.realname || '未知医生' // 为了兼容性
        };
      });

      doctorList.value = doctors;
      console.log('获取医生列表成功，共', doctors.length, '位医生');
      console.log('医生列表详情:', doctors.map(d => ({ userId: d.userId, realName: d.realName, id: d.id })));
    } else {
      console.error('获取医生列表失败:', response.message);
      uni.showToast({
        title: '获取医生列表失败',
        icon: 'none'
      });
    }
  } catch (error: any) {
    console.error('获取医生列表异常:', error);
    uni.showToast({
      title: '获取医生列表失败',
      icon: 'none'
    });
  } finally {
    loadingDoctors.value = false;
  }
}

// 处理医生选择
const onDoctorSelect = (event: any) => {
  const selectedIndex = event.detail.value;
  const selectedDoctor = doctorList.value[selectedIndex];

  if (!selectedDoctor) return;

  // 检查是否已经选择过该医生
  console.log('🔍 检查医生是否已选择:', {
    selectedDoctor: selectedDoctor,
    selectedDoctorUserId: selectedDoctor.userId,
    currentSelectedDoctors: selectedDoctors.value.map(d => ({ userId: d.userId, realName: d.realName }))
  });

  const isAlreadySelected = selectedDoctors.value.some(
    doctor => {
      // 确保比较的是字符串类型
      const doctorId = String(doctor.userId);
      const selectedId = String(selectedDoctor.userId);
      const isSame = doctorId === selectedId;
      console.log(`比较医生: ${doctor.realName}(${doctorId}) vs ${selectedDoctor.realName}(${selectedId}) = ${isSame}`);
      return isSame;
    }
  );

  if (isAlreadySelected) {
    uni.showToast({
      title: '该医生已选择',
      icon: 'none'
    });
    return;
  }

  // 添加到已选择列表
  selectedDoctors.value.push(selectedDoctor);

  // 更新兼容性字段
  updateSelectedDoctorInfo();

  console.log('选择医生:', selectedDoctor);
  console.log('当前已选择医生:', selectedDoctors.value);
}

// 移除医生选择
const removeDoctorSelection = (index: number) => {
  selectedDoctors.value.splice(index, 1);
  updateSelectedDoctorInfo();
  console.log('移除医生后的列表:', selectedDoctors.value);
}

// 更新选中医生的ID和姓名字符串（使用API返回的realName）
const updateSelectedDoctorInfo = () => {
  const doctorIds = selectedDoctors.value.map(doctor => doctor.userId).join(',');
  const doctorNames = selectedDoctors.value.map(doctor => `@${doctor.realName || '未知医生'}`).join(' ');

  selectedDoctorIds.value = doctorIds;
  selectedDoctorNames.value = doctorNames;

  console.log('📝 更新医生信息:', {
    doctorIds: selectedDoctorIds.value,
    doctorNames: selectedDoctorNames.value
  });
}

// 从相册选择图片
function chooseFromGallery() {
  if (selectedImages.value.length >= 6) {
    uni.showToast({ title: '最多只能上传6张图片', icon: 'none' });
    return;
  }

  const remainingCount = 6 - selectedImages.value.length;
  uni.chooseImage({
    count: remainingCount,
    sourceType: ['album'],
    success: (res: any) => {
      if (res.tempFilePaths && res.tempFilePaths.length > 0) {
        selectedImages.value.push(...res.tempFilePaths);
      }
    },
    fail: (err: any) => {
      console.error('选择图片失败:', err);
      uni.showToast({ title: '选择图片失败', icon: 'none' });
    }
  });
}

// 拍照
function takePhoto() {
  if (selectedImages.value.length >= 6) {
    uni.showToast({ title: '最多只能上传6张图片', icon: 'none' });
    return;
  }

  uni.chooseImage({
    count: 1,
    sourceType: ['camera'],
    success: (res: any) => {
      if (res.tempFilePaths && res.tempFilePaths.length > 0) {
        selectedImages.value.push(...res.tempFilePaths);
      }
    },
    fail: (err: any) => {
      console.error('拍照失败:', err);
      uni.showToast({ title: '拍照失败', icon: 'none' });
    }
  });
}

// 移除图片
function removeImage(index: number) {
  selectedImages.value.splice(index, 1);
}

// 预览图片
function previewImage(url: string) {
  uni.previewImage({
    current: url,
    urls: selectedImages.value
  });
}

// 验证表单
function validateForm() {
  if (!formData.value.question.trim()) {
    uni.showToast({ title: '请输入问题描述', icon: 'none' });
    return false;
  }

  // 如果填写了标题，验证长度
  if (formData.value.title.trim() && formData.value.title.length > 50) {
    uni.showToast({ title: '标题不能超过50个字符', icon: 'none' });
    return false;
  }

  if (formData.value.question.length > 1000) {
    uni.showToast({ title: '问题描述不能超过1000个字符', icon: 'none' });
    return false;
  }

  return true;
}

// 提交问题
async function submitQuestion() {
  if (!validateForm()) return;

  if (isSubmitting.value) {
    uni.showToast({ title: '正在提交中，请勿重复操作', icon: 'none' });
    return;
  }

  try {
    isSubmitting.value = true;

    // 先上传图片，获取文件名数组
    let uploadedImageNames: string[] = [];

    if (selectedImages.value.length > 0) {
      console.log('开始上传图片，共', selectedImages.value.length, '张');

      for (let i = 0; i < selectedImages.value.length; i++) {
        const imagePath = selectedImages.value[i];
        console.log(`上传第 ${i + 1} 张图片:`, imagePath);

        try {
          const uploadResult = await new Promise((resolve, reject) => {
            uni.uploadFile({
              url: 'https://www.mograine.cn/communication/uploadImage',
              filePath: imagePath,
              name: 'file',
              success: (res) => {
                console.log(`第 ${i + 1} 张图片上传成功:`, res);
                try {
                  const data = JSON.parse(res.data);
                  if (data.success && data.result) {
                    resolve(data.result);
                  } else {
                    reject(new Error(data.message || '上传失败'));
                  }
                } catch (e) {
                  reject(new Error('解析上传结果失败'));
                }
              },
              fail: (err) => {
                console.error(`第 ${i + 1} 张图片上传失败:`, err);
                reject(err);
              }
            });
          });

          uploadedImageNames.push(uploadResult as string);
        } catch (error) {
          console.error(`图片 ${i + 1} 上传失败:`, error);
          uni.showToast({ title: `第 ${i + 1} 张图片上传失败`, icon: 'none' });
          isSubmitting.value = false;
          return;
        }
      }

      console.log('所有图片上传完成，文件名:', uploadedImageNames);
    }

    // 构建提交数据
    const submitData = {
      title: formData.value.title.trim() || '',
      patientId: userStore.userInfo.userid || '',
      question: formData.value.question.trim(),
      images: uploadedImageNames, // 使用上传后的文件名数组
      doctorIds: selectedDoctors.value.map(doctor => doctor.userId) // 传递选中的医生ID数组
    };

    console.log('提交数据:', submitData);

    // 调用API
    const response = await http.post('/communication/saveCommunication', submitData);

    if (response.success) {
      uni.showToast({
        title: '提交成功',
        icon: 'success',
        duration: 2000
      });

      // 清空当前页面的医生选择状态
      selectedDoctors.value = [];
      selectedDoctorIds.value = '';
      selectedDoctorNames.value = '';

      // 延迟返回上一页，并传递清空标识
      setTimeout(() => {
        uni.navigateBack({
          delta: 1,
          success: () => {
            // 通过全局事件总线通知清空医生选择
            uni.$emit('clearDoctorSelection');
          }
        });
      }, 2000);
    } else {
      throw new Error(response.message || '提交失败');
    }

  } catch (error: any) {
    console.error('提交失败:', error);
    uni.showToast({
      title: error.message || '提交失败，请重试',
      icon: 'none',
      duration: 3000
    });
  } finally {
    isSubmitting.value = false;
  }
}

onLoad((options: any) => {
  // 初始化页面数据
  formData.value.patientId = userStore.userInfo.userid || '';
  // 获取医生列表
  fetchDoctorList();

  // 接收传递的医生信息
  if (options?.selectedDoctorIds) {
    selectedDoctorIds.value = decodeURIComponent(options.selectedDoctorIds);
  }

  if (options?.selectedDoctorNames) {
    selectedDoctorNames.value = decodeURIComponent(options.selectedDoctorNames);
  }

  // 强制触发头像处理
  const avatarUrl = getAvatarUrl(userStore.userInfo.avatar);
});
</script>
<style lang="scss" scoped>


// 用户头像和姓名区域样式
.user-header-section {
  padding: 24rpx;
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.user-info-card {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #f0f0f0;
}

.user-avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  border: 3rpx solid #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.user-details {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
  line-height: 1.2;
}

.user-role {
  font-size: 24rpx;
  color: #666;
  background: #f0f7ff;
  color: #1890ff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  align-self: flex-start;
  font-weight: 500;
}

.chat-list {
  flex: 1;
  overflow: auto;
  padding: 0 0 40rpx 0;
}

// 表单内容样式
.form-content {
  padding: 24rpx;
}

// 医生选择样式
.doctor-select-container {
  border: 1rpx solid #d9d9d9;
  border-radius: 8rpx;
  background: #fff;
  overflow: hidden;
}

.doctor-picker {
  width: 100%;
}

.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 16rpx;
  min-height: 80rpx;
  box-sizing: border-box;
}

.picker-text {
  font-size: 28rpx;
  color: #666;
  flex: 1;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999;
  margin-left: 16rpx;
}

.selected-doctors-display {
  border-top: 1rpx solid #f0f0f0;
  padding: 16rpx;
  background: #f8f9fa;
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.doctor-tag {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
  border: 1rpx solid #d4edda;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
}

.doctor-name {
  color: #07C160;
  font-weight: 500;
  margin-right: 8rpx;
}

.remove-doctor {
  color: #999;
  font-size: 28rpx;
  font-weight: bold;
  cursor: pointer;
  width: 24rpx;
  height: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.1);

  &:hover {
    background: rgba(0, 0, 0, 0.2);
    color: #666;
  }
}

.form-item {
  margin-bottom: 32rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.required {
  color: #ff4d4f;
  margin-left: 4rpx;
}

.optional {
  color: #999;
  font-weight: normal;
  font-size: 24rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #d9d9d9;
  border-radius: 8rpx;
  padding: 0 16rpx;
  font-size: 28rpx;
  background: #fff;
  box-sizing: border-box;

  &:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2rpx rgba(24, 144, 255, 0.2);
  }
}

.form-textarea {
  width: 100%;
  min-height: 160rpx;
  border: 1rpx solid #d9d9d9;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 28rpx;
  background: #fff;
  box-sizing: border-box;
  line-height: 1.5;

  &:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2rpx rgba(24, 144, 255, 0.2);
  }
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

// 图片上传样式
.image-upload-area {
  border: 2rpx dashed #d9d9d9;
  border-radius: 8rpx;
  padding: 24rpx;
  background: #fafafa;
}

.uploaded-images {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.image-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
  object-fit: cover;
}

.remove-image {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background: #ff4d4f;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
  cursor: pointer;
}

.upload-buttons {
  display: flex;
  gap: 16rpx;
}

.upload-btn {
  flex: 1;
  height: 80rpx;
  background: #fff;
  border: 1rpx solid #d9d9d9;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;

  &:disabled {
    background: #f5f5f5;
    color: #ccc;
    border-color: #f0f0f0;
  }

  &:not(:disabled):active {
    background: #f0f0f0;
  }
}

// 提交按钮样式
.submit-section {
  padding: 40rpx 24rpx;
  background: #fff;
  border-top: 1rpx solid #f0f0f0;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: #07C160;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;

  &.disabled {
    background: #d9d9d9;
    color: #fff;
  }

  &:not(.disabled):active {
    background: #06A050;
  }
}
</style>