{"version": 3, "file": "z-paging-interceptor.js", "sources": ["../../../../../../../../src/uni_modules/z-paging/components/z-paging/js/z-paging-interceptor.js"], "sourcesContent": ["// [z-paging]拦截器\r\n\r\nconst queryKey = 'Query';\r\nconst fetchParamsKey = 'FetchParams';\r\nconst fetchResultKey = 'FetchResult';\r\nconst language2LocalKey = 'Language2Local';\r\n\r\n// 拦截&处理@query事件\r\nfunction handleQuery(callback) {\r\n\t_addHandleByKey(queryKey, callback);\r\n\treturn this;\r\n}\r\n\r\n// 拦截&处理@query事件(私有，请勿调用)\r\nfunction _handleQuery(pageNo, pageSize, from, lastItem) {\r\n\tconst callback = _getHandleByKey(queryKey);\r\n\treturn callback ? callback(pageNo, pageSize, from, lastItem) : [pageNo, pageSize, from];\r\n}\r\n\r\n// 拦截&处理:fetch参数\r\nfunction handleFetchParams(callback) {\r\n\t_addHandleByKey(fetchParamsKey, callback);\r\n\treturn this;\r\n}\r\n\r\n// 拦截&处理:fetch参数(私有，请勿调用)\r\nfunction _handleFetchParams(parmas, extraParams) {\r\n\tconst callback = _getHandleByKey(fetchParamsKey);\r\n\treturn callback ? callback(parmas, extraParams || {}) : { pageNo: parmas.pageNo, pageSize: parmas.pageSize, ...(extraParams || {}) };\r\n}\r\n\r\n// 拦截&处理:fetch结果\r\nfunction handleFetchResult(callback) {\r\n\t_addHandleByKey(fetchResultKey, callback);\r\n\treturn this;\r\n}\r\n\r\n// 拦截&处理:fetch结果(私有，请勿调用)\r\nfunction _handleFetchResult(result, paging, params) {\r\n\tconst callback = _getHandleByKey(fetchResultKey);\r\n\tcallback && callback(result, paging, params);\r\n\treturn callback ? true : false;\r\n}\r\n\r\n// 拦截&处理系统language转i18n local\r\nfunction handleLanguage2Local(callback) {\r\n\t_addHandleByKey(language2LocalKey, callback);\r\n\treturn this;\r\n}\r\n\r\n// 拦截&处理系统language转i18n local(私有，请勿调用)\r\nfunction _handleLanguage2Local(language, local) {\r\n\tconst callback = _getHandleByKey(language2LocalKey);\r\n\treturn callback ? callback(language, local) : local;\r\n}\r\n\r\n// 获取当前app对象\r\nfunction _getApp(){\r\n\t// #ifndef APP-NVUE\r\n\treturn getApp();\r\n\t// #endif\r\n\t// #ifdef APP-NVUE\r\n\treturn getApp({ allowDefault: true });\r\n\t// #endif\r\n}\r\n\r\n// 是否可以访问globalData\r\nfunction _hasGlobalData() {\r\n\treturn _getApp() && _getApp().globalData;\r\n}\r\n\r\n// 添加处理函数\r\nfunction _addHandleByKey(key, callback) {\r\n\ttry {\r\n\t\tsetTimeout(function() {\r\n\t\t\tif (_hasGlobalData()) {\r\n\t\t\t\t_getApp().globalData[`zp_handle${key}Callback`] = callback;\r\n\t\t\t}\r\n\t\t}, 1);\r\n\t} catch (_) {}\r\n}\r\n\r\n// 获取处理回调函数\r\nfunction _getHandleByKey(key) {\r\n\treturn _hasGlobalData() ? _getApp().globalData[`zp_handle${key}Callback`] : null;\r\n}\r\n\r\nexport default {\r\n\thandleQuery,\r\n\t_handleQuery,\r\n\thandleFetchParams,\r\n\t_handleFetchParams,\r\n\thandleFetchResult,\r\n\t_handleFetchResult,\r\n\thandleLanguage2Local,\r\n\t_handleLanguage2Local\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA,MAAM,WAAW;AACjB,MAAM,iBAAiB;AACvB,MAAM,iBAAiB;AACvB,MAAM,oBAAoB;AAG1B,SAAS,YAAY,UAAU;AAC9B,kBAAgB,UAAU,QAAQ;AAClC,SAAO;AACR;AAGA,SAAS,aAAa,QAAQ,UAAU,MAAM,UAAU;AACvD,QAAM,WAAW,gBAAgB,QAAQ;AACzC,SAAO,WAAW,SAAS,QAAQ,UAAU,MAAM,QAAQ,IAAI,CAAC,QAAQ,UAAU,IAAI;AACvF;AAGA,SAAS,kBAAkB,UAAU;AACpC,kBAAgB,gBAAgB,QAAQ;AACxC,SAAO;AACR;AAGA,SAAS,mBAAmB,QAAQ,aAAa;AAChD,QAAM,WAAW,gBAAgB,cAAc;AAC/C,SAAO,WAAW,SAAS,QAAQ,eAAe,CAAA,CAAE,IAAI,iBAAE,QAAQ,OAAO,QAAQ,UAAU,OAAO,YAAc,eAAe,CAAE;AAClI;AAGA,SAAS,kBAAkB,UAAU;AACpC,kBAAgB,gBAAgB,QAAQ;AACxC,SAAO;AACR;AAGA,SAAS,mBAAmB,QAAQ,QAAQ,QAAQ;AACnD,QAAM,WAAW,gBAAgB,cAAc;AAC/C,cAAY,SAAS,QAAQ,QAAQ,MAAM;AAC3C,SAAO,WAAW,OAAO;AAC1B;AAGA,SAAS,qBAAqB,UAAU;AACvC,kBAAgB,mBAAmB,QAAQ;AAC3C,SAAO;AACR;AAGA,SAAS,sBAAsB,UAAU,OAAO;AAC/C,QAAM,WAAW,gBAAgB,iBAAiB;AAClD,SAAO,WAAW,SAAS,UAAU,KAAK,IAAI;AAC/C;AAGA,SAAS,UAAS;AAEjB,SAAO,OAAM;AAKd;AAGA,SAAS,iBAAiB;AACzB,SAAO,QAAS,KAAI,QAAS,EAAC;AAC/B;AAGA,SAAS,gBAAgB,KAAK,UAAU;AACvC,MAAI;AACH,eAAW,WAAW;AACrB,UAAI,eAAc,GAAI;AACrB,gBAAO,EAAG,WAAW,YAAY,GAAG,UAAU,IAAI;AAAA,MAClD;AAAA,IACD,GAAE,CAAC;AAAA,EACN,SAAU,GAAG;AAAA,EAAE;AACf;AAGA,SAAS,gBAAgB,KAAK;AAC7B,SAAO,eAAc,IAAK,QAAO,EAAG,WAAW,YAAY,GAAG,UAAU,IAAI;AAC7E;AAEA,MAAe,cAAA;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;;"}