{"version": 3, "file": "index.js", "sources": ["../../../../../../src/plugin/uni-mini-router/router/index.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/ban-types */\r\n/*\r\n * @Author: 徐庆凯\r\n * @Date: 2023-03-13 15:56:28\r\n * @LastEditTime: 2023-12-21 21:37:34\r\n * @LastEditors: weisheng\r\n * @Description:\r\n * @FilePath: \\uni-mini-router\\src\\router\\index.ts\r\n * 记得注释\r\n */\r\n\r\nimport {\r\n  AfterEachGuard,\r\n  BeforeEachGuard,\r\n  HookType,\r\n  NavMethod,\r\n  NAVTYPE,\r\n  NavTypeEnum,\r\n  NextRouteBackLocation,\r\n  NextRouteLocationRaw,\r\n  Route,\r\n  RouteLocationRaw,\r\n  Router\r\n} from '../interfaces'\r\n\r\nimport { beautifyUrl, getUrlParams, queryStringify, setUrlParams } from '../utils'\r\n\r\n// 保留uni默认的NavMethod\r\nconst navMethods: Record<string, Function> = {\r\n  navigateTo: uni.navigateTo,\r\n  redirectTo: uni.redirectTo,\r\n  reLaunch: uni.reLaunch,\r\n  switchTab: uni.switchTab,\r\n  navigateBack: uni.navigateBack\r\n}\r\n\r\n/**\r\n * 跳转至指定路由\r\n * @param to 目标路径\r\n * @param router router实例\r\n * @param navType 跳转类型\r\n * @returns\r\n */\r\nexport function navjump(to: RouteLocationRaw, router: Router, navType: NAVTYPE) {\r\n  const url: string = getRoutePath(to, router)\r\n  switch (navType) {\r\n    case 'push':\r\n      navMethods.navigateTo({ url: url })\r\n      break\r\n    case 'replace':\r\n      navMethods.redirectTo({ url: url })\r\n      break\r\n    case 'pushTab':\r\n      navMethods.switchTab({ url: url })\r\n      break\r\n    case 'replaceAll':\r\n      navMethods.reLaunch({ url: url })\r\n      break\r\n    default:\r\n      throw new Error('无效的路由类型，请确保提供正确的路由类型')\r\n    // throw new Error('Invalid route type provided. Please ensure the provided route is of the correct type.')\r\n  }\r\n  return\r\n}\r\n\r\n/**\r\n * 获取目标路径\r\n * @param to 目标页面\r\n * @param router\r\n * @returns\r\n */\r\nexport function getRoutePath(to: RouteLocationRaw, router: Router): string {\r\n  let url: string = '' // 路径\r\n  let query: Record<string, string> = {}\r\n  if (typeof to === 'string') {\r\n    url = to\r\n  } else {\r\n    if ((to as any).name) {\r\n      // 通过name匹配路由\r\n      const route = router.routes.find((item: { name: string }) => {\r\n        return item.name === (to as any).name\r\n      })\r\n      if (route && route.path) {\r\n        url = route.path\r\n      } else {\r\n        throw new Error('您正在尝试访问的路由未在路由表中定义。请检查您的路由配置。')\r\n        // throw new Error('The route you are trying to access is not defined in the routing table. Please check your routing configuration.')\r\n      }\r\n      query = (to as any).params\r\n    } else if ((to as any).path) {\r\n      url = beautifyUrl(`/${(to as any).path.split('?')[0]}`)\r\n      query = { ...getUrlParams((to as any).path), ...((to as any).query || {}) }\r\n    }\r\n    if (query) {\r\n      query = queryStringify(query)\r\n      url = setUrlParams(url, query)\r\n    }\r\n  }\r\n  return url\r\n}\r\n\r\n/**\r\n * 获取当前页面\r\n * @returns 当前页面\r\n */\r\nexport function getCurrentPage() {\r\n  const pages = getCurrentPages()\r\n  return pages.length > 0 ? pages[pages.length - 1] : undefined\r\n}\r\n\r\n/**\r\n * 保存路由信息到路由实例\r\n * @param router 路由实例\r\n * @param query 路由参数\r\n * @returns\r\n */\r\nexport function saveCurrRouteByCurrPage(router: Router) {\r\n  router.route.value = getCurrentPageRoute(router)\r\n}\r\n\r\n/**\r\n * 获取当前页面的路由信息\r\n * @param router router实例\r\n * @returns\r\n */\r\nexport function getCurrentPageRoute(router: Router): Route {\r\n  const page: any = getCurrentPage()\r\n  if (!page || !page.route || !router.routes) {\r\n    return\r\n  }\r\n  const currRoute: Route = getRouteByPath(`/${page.route}`, router)\r\n  if (page.$page) {\r\n    currRoute.fullPath = page.$page.fullPath ? page.$page.fullPath : ''\r\n    currRoute.query = page.$page.fullPath ? getUrlParams(page.$page.fullPath) : {}\r\n    currRoute.params = page.$page.fullPath ? getUrlParams(page.$page.fullPath) : {}\r\n  }\r\n\r\n  return currRoute\r\n}\r\n\r\n/**\r\n * 通过页面路路径寻找路由信息\r\n * @param path 页面路径\r\n * @param router 路由实例\r\n * @returns 路由信息\r\n */\r\nexport function getRouteByPath(path: string, router: Router): Route {\r\n  path = beautifyUrl(path.split('?')[0])\r\n  const route: Route = router.routes.find((route: Route) => {\r\n    return route.path === path || route.aliasPath === path\r\n  })\r\n  return JSON.parse(JSON.stringify(route))\r\n}\r\n\r\n/**\r\n * 注册守卫钩子\r\n * @param router 路由实例\r\n * @param hookType 钩子类型\r\n * @param userGuard 守卫\r\n */\r\nexport function registerEachHooks(router: Router, hookType: HookType, userGuard: BeforeEachGuard | AfterEachGuard) {\r\n  router.guardHooks[hookType] = [userGuard as any]\r\n}\r\n// 保留uni默认的NavMethod\r\nconst oldMethods: Record<string, Function> = {\r\n  navigateTo: uni.navigateTo,\r\n  redirectTo: uni.redirectTo,\r\n  reLaunch: uni.reLaunch,\r\n  switchTab: uni.switchTab,\r\n  navigateBack: uni.navigateBack\r\n}\r\n\r\n/**\r\n * 重写uni路由相关事件\r\n */\r\nexport function rewriteNavMethod(router: Router) {\r\n  NavMethod.forEach((name) => {\r\n    navMethods[name] = function (options: any) {\r\n      if (name === 'navigateBack') {\r\n        oldMethods[name](options)\r\n      } else {\r\n        if (router.guardHooks.beforeHooks && router.guardHooks.beforeHooks[0]) {\r\n          const to: Route = getRouteByPath(options.url, router)\r\n          guardToPromiseFn(router.guardHooks.beforeHooks[0], to, router.route.value)\r\n            .then((resp) => {\r\n              if (resp === true) {\r\n                oldMethods[name](options)\r\n              } else {\r\n                if (typeof resp === 'string') {\r\n                  const url: string = getRoutePath(resp, router)\r\n                  oldMethods[name]({ url: url })\r\n                } else if ((resp as NextRouteBackLocation).navType === 'back') {\r\n                  oldMethods['navigateBack'](resp)\r\n                } else {\r\n                  const url: string = getRoutePath(resp as RouteLocationRaw, router)\r\n                  oldMethods[resp.navType ? NavTypeEnum[resp.navType] : name]({ url: url })\r\n                }\r\n              }\r\n            })\r\n            .catch((error) => {\r\n              throw error\r\n            })\r\n        } else {\r\n          oldMethods[name](options)\r\n        }\r\n      }\r\n    }\r\n  })\r\n}\r\n\r\n/**\r\n * 用Promise处理守卫方法\r\n * @param guard 守卫\r\n * @param to 目标路由\r\n * @param from 来源路由\r\n * @returns\r\n */\r\nexport function guardToPromiseFn(guard: BeforeEachGuard, to: Route, from: Route) {\r\n  return new Promise<NextRouteLocationRaw | true>((reslove, reject) => {\r\n    const next: ((rule?: NextRouteLocationRaw | boolean) => void) | any = (rule?: NextRouteLocationRaw | boolean) => {\r\n      next._called = true\r\n      if (rule === false) {\r\n        reject({})\r\n      } else if (rule === undefined || rule === true) {\r\n        reslove(true)\r\n      } else {\r\n        reslove(rule)\r\n      }\r\n    }\r\n    const guardReturn = guard.call(undefined, to, from, next)\r\n    let guardCall = Promise.resolve(guardReturn)\r\n    if (guard.length < 3) guardCall = guardCall.then(next)\r\n    if (guard.length > 2) {\r\n      const message = `The \"next\" callback was never called inside of ${\r\n        guard.name ? '\"' + guard.name + '\"' : ''\r\n      }:\\n${guard.toString()}\\n. If you are returning a value instead of calling \"next\", make sure to remove the \"next\" parameter from your function.`\r\n      if (guardReturn !== null && typeof guardReturn === 'object' && 'then' in guardReturn!) {\r\n        guardCall = guardCall.then((resolvedValue) => {\r\n          if (!next._called) {\r\n            console.warn(message)\r\n            return Promise.reject(new Error('Invalid navigation guard'))\r\n          }\r\n          return resolvedValue\r\n        })\r\n      } else {\r\n        if (!next._called) {\r\n          console.warn(message)\r\n          reject(new Error('Invalid navigation guard'))\r\n          return\r\n        }\r\n      }\r\n    }\r\n    guardCall.catch((err) => reject(err))\r\n  })\r\n}\r\n"], "names": ["uni", "beautifyUrl", "getUrlParams", "queryStringify", "setUrlParams", "route", "NavMethod", "NavTypeEnum"], "mappings": ";;;;;;;;;;;;;;;;;;;;AA4BA,MAAM,aAAuC;AAAA,EAC3C,YAAYA,cAAI,MAAA;AAAA,EAChB,YAAYA,cAAI,MAAA;AAAA,EAChB,UAAUA,cAAI,MAAA;AAAA,EACd,WAAWA,cAAI,MAAA;AAAA,EACf,cAAcA,cAAAA,MAAI;AACpB;AASgB,SAAA,QAAQ,IAAsB,QAAgB,SAAkB;AACxE,QAAA,MAAc,aAAa,IAAI,MAAM;AAC3C,UAAQ,SAAS;AAAA,IACf,KAAK;AACQ,iBAAA,WAAW,EAAE,KAAU;AAClC;AAAA,IACF,KAAK;AACQ,iBAAA,WAAW,EAAE,KAAU;AAClC;AAAA,IACF,KAAK;AACQ,iBAAA,UAAU,EAAE,KAAU;AACjC;AAAA,IACF,KAAK;AACQ,iBAAA,SAAS,EAAE,KAAU;AAChC;AAAA,IACF;AACQ,YAAA,IAAI,MAAM,sBAAsB;AAAA,EAAA;AAG1C;AACF;AAQgB,SAAA,aAAa,IAAsB,QAAwB;AACzE,MAAI,MAAc;AAClB,MAAI,QAAgC,CAAC;AACjC,MAAA,OAAO,OAAO,UAAU;AACpB,UAAA;AAAA,EAAA,OACD;AACL,QAAK,GAAW,MAAM;AAEpB,YAAM,QAAQ,OAAO,OAAO,KAAK,CAAC,SAA2B;AACpD,eAAA,KAAK,SAAU,GAAW;AAAA,MAAA,CAClC;AACG,UAAA,SAAS,MAAM,MAAM;AACvB,cAAM,MAAM;AAAA,MAAA,OACP;AACC,cAAA,IAAI,MAAM,+BAA+B;AAAA,MAAA;AAGjD,cAAS,GAAW;AAAA,IAAA,WACV,GAAW,MAAM;AACrB,YAAAC,iCAAA,YAAY,IAAK,GAAW,KAAK,MAAM,GAAG,EAAE,CAAC,CAAC,EAAE;AAC9C,cAAA,kCAAKC,iCAAAA,aAAc,GAAW,IAAI,IAAQ,GAAW,SAAS;IAAI;AAE5E,QAAI,OAAO;AACT,cAAQC,gDAAe,KAAK;AACtB,YAAAC,iCAAAA,aAAa,KAAK,KAAK;AAAA,IAAA;AAAA,EAC/B;AAEK,SAAA;AACT;AAMO,SAAS,iBAAiB;AAC/B,QAAM,QAAQ,gBAAgB;AAC9B,SAAO,MAAM,SAAS,IAAI,MAAM,MAAM,SAAS,CAAC,IAAI;AACtD;AAQO,SAAS,wBAAwB,QAAgB;AAC/C,SAAA,MAAM,QAAQ,oBAAoB,MAAM;AACjD;AAOO,SAAS,oBAAoB,QAAuB;AACzD,QAAM,OAAY,eAAe;AACjC,MAAI,CAAC,QAAQ,CAAC,KAAK,SAAS,CAAC,OAAO,QAAQ;AAC1C;AAAA,EAAA;AAEF,QAAM,YAAmB,eAAe,IAAI,KAAK,KAAK,IAAI,MAAM;AAChE,MAAI,KAAK,OAAO;AACd,cAAU,WAAW,KAAK,MAAM,WAAW,KAAK,MAAM,WAAW;AACvD,cAAA,QAAQ,KAAK,MAAM,WAAWF,8CAAa,KAAK,MAAM,QAAQ,IAAI,CAAC;AACnE,cAAA,SAAS,KAAK,MAAM,WAAWA,8CAAa,KAAK,MAAM,QAAQ,IAAI,CAAC;AAAA,EAAA;AAGzE,SAAA;AACT;AAQgB,SAAA,eAAe,MAAc,QAAuB;AAClE,SAAOD,6CAAY,KAAK,MAAM,GAAG,EAAE,CAAC,CAAC;AACrC,QAAM,QAAe,OAAO,OAAO,KAAK,CAACI,WAAiB;AACxD,WAAOA,OAAM,SAAS,QAAQA,OAAM,cAAc;AAAA,EAAA,CACnD;AACD,SAAO,KAAK,MAAM,KAAK,UAAU,KAAK,CAAC;AACzC;AAQgB,SAAA,kBAAkB,QAAgB,UAAoB,WAA6C;AACjH,SAAO,WAAW,QAAQ,IAAI,CAAC,SAAgB;AACjD;AAEA,MAAM,aAAuC;AAAA,EAC3C,YAAYL,cAAI,MAAA;AAAA,EAChB,YAAYA,cAAI,MAAA;AAAA,EAChB,UAAUA,cAAI,MAAA;AAAA,EACd,WAAWA,cAAI,MAAA;AAAA,EACf,cAAcA,cAAAA,MAAI;AACpB;AAKO,SAAS,iBAAiB,QAAgB;AACrCM,kDAAA,QAAQ,CAAC,SAAS;AACf,eAAA,IAAI,IAAI,SAAU,SAAc;AACzC,UAAI,SAAS,gBAAgB;AAChB,mBAAA,IAAI,EAAE,OAAO;AAAA,MAAA,OACnB;AACL,YAAI,OAAO,WAAW,eAAe,OAAO,WAAW,YAAY,CAAC,GAAG;AACrE,gBAAM,KAAY,eAAe,QAAQ,KAAK,MAAM;AACpD,2BAAiB,OAAO,WAAW,YAAY,CAAC,GAAG,IAAI,OAAO,MAAM,KAAK,EACtE,KAAK,CAAC,SAAS;AACd,gBAAI,SAAS,MAAM;AACN,yBAAA,IAAI,EAAE,OAAO;AAAA,YAAA,OACnB;AACD,kBAAA,OAAO,SAAS,UAAU;AACtB,sBAAA,MAAc,aAAa,MAAM,MAAM;AAC7C,2BAAW,IAAI,EAAE,EAAE,KAAU;AAAA,cAAA,WACnB,KAA+B,YAAY,QAAQ;AAClD,2BAAA,cAAc,EAAE,IAAI;AAAA,cAAA,OAC1B;AACC,sBAAA,MAAc,aAAa,MAA0B,MAAM;AACtD,2BAAA,KAAK,UAAUC,kDAAY,KAAK,OAAO,IAAI,IAAI,EAAE,EAAE,KAAU;AAAA,cAAA;AAAA,YAC1E;AAAA,UACF,CACD,EACA,MAAM,CAAC,UAAU;AACV,kBAAA;AAAA,UAAA,CACP;AAAA,QAAA,OACE;AACM,qBAAA,IAAI,EAAE,OAAO;AAAA,QAAA;AAAA,MAC1B;AAAA,IAEJ;AAAA,EAAA,CACD;AACH;AASgB,SAAA,iBAAiB,OAAwB,IAAW,MAAa;AAC/E,SAAO,IAAI,QAAqC,CAAC,SAAS,WAAW;AAC7D,UAAA,OAAgE,CAAC,SAA0C;AAC/G,WAAK,UAAU;AACf,UAAI,SAAS,OAAO;AAClB,eAAO,CAAA,CAAE;AAAA,MACA,WAAA,SAAS,UAAa,SAAS,MAAM;AAC9C,gBAAQ,IAAI;AAAA,MAAA,OACP;AACL,gBAAQ,IAAI;AAAA,MAAA;AAAA,IAEhB;AACA,UAAM,cAAc,MAAM,KAAK,QAAW,IAAI,MAAM,IAAI;AACpD,QAAA,YAAY,QAAQ,QAAQ,WAAW;AAC3C,QAAI,MAAM,SAAS;AAAe,kBAAA,UAAU,KAAK,IAAI;AACjD,QAAA,MAAM,SAAS,GAAG;AACd,YAAA,UAAU,kDACd,MAAM,OAAO,MAAM,MAAM,OAAO,MAAM,EACxC;AAAA,EAAM,MAAM,SAAU,CAAA;AAAA;AACtB,UAAI,gBAAgB,QAAQ,OAAO,gBAAgB,YAAY,UAAU,aAAc;AACzE,oBAAA,UAAU,KAAK,CAAC,kBAAkB;AACxC,cAAA,CAAC,KAAK,SAAS;AACjB,oBAAQ,KAAK,OAAO;AACpB,mBAAO,QAAQ,OAAO,IAAI,MAAM,0BAA0B,CAAC;AAAA,UAAA;AAEtD,iBAAA;AAAA,QAAA,CACR;AAAA,MAAA,OACI;AACD,YAAA,CAAC,KAAK,SAAS;AACjB,kBAAQ,KAAK,OAAO;AACb,iBAAA,IAAI,MAAM,0BAA0B,CAAC;AAC5C;AAAA,QAAA;AAAA,MACF;AAAA,IACF;AAEF,cAAU,MAAM,CAAC,QAAQ,OAAO,GAAG,CAAC;AAAA,EAAA,CACrC;AACH;;;;;;"}