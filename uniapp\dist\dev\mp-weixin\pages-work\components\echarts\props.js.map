{"version": 3, "file": "props.js", "sources": ["../../../../../../src/pages-work/components/echarts/props.ts"], "sourcesContent": ["\r\n/**\r\n * echart图表props\r\n */\r\nexport const echartProps = {\r\n    i: {\r\n        type: [String,Number],\r\n        default: ''\r\n    },\r\n\tid: {\r\n\t    type: String,\r\n\t    default: ''\r\n\t},\r\n    config: {\r\n        type: Object,\r\n        default: () => ({} as any),\r\n    },\r\n    height:{\r\n        type: Number,\r\n    },\r\n    compName:{\r\n        type: String,\r\n        default: ''\r\n    },\r\n    horizontal:{\r\n        type: Boolean,\r\n        default: false\r\n    },\r\n    appId:{\r\n        type: String,\r\n        default: ''\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AAIO,MAAM,cAAc;AAAA,EACvB,GAAG;AAAA,IACC,MAAM,CAAC,QAAO,MAAM;AAAA,IACpB,SAAS;AAAA,EACb;AAAA,EACH,IAAI;AAAA,IACA,MAAM;AAAA,IACN,SAAS;AAAA,EACb;AAAA,EACG,QAAQ;AAAA,IACJ,MAAM;AAAA,IACN,SAAS,OAAO,CAAC;AAAA,EACrB;AAAA,EACA,QAAO;AAAA,IACH,MAAM;AAAA,EACV;AAAA,EACA,UAAS;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACb;AAAA,EACA,YAAW;AAAA,IACP,MAAM;AAAA,IACN,SAAS;AAAA,EACb;AAAA,EACA,OAAM;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,EAAA;AAEjB;;"}