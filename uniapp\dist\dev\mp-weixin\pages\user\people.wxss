/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.user-card.data-v-0d4590e5 {
  background-color: #fff;
  margin: 20rpx;
  padding: 30rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.user-info.data-v-0d4590e5 {
  display: flex;
  align-items: flex-start;
}
.avatar-wrapper.data-v-0d4590e5 {
  position: relative;
  cursor: pointer;
}
.avatar-wrapper .avatar-button.data-v-0d4590e5 {
  padding: 0;
  border: none;
  background: transparent;
  display: block;
}
.user-badge.data-v-0d4590e5 {
  font-size: 12rpx;
  color: #888;
  margin-top: 8rpx;
  text-align: center;
}
.user-details.data-v-0d4590e5 {
  margin-left: 20rpx;
  flex: 1;
}
.username.data-v-0d4590e5 {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.user-id.data-v-0d4590e5,
.contact.data-v-0d4590e5,
.emergency.data-v-0d4590e5 {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}
.edit-btn.data-v-0d4590e5 {
  background-color: #f0f0f0;
  color: #333;
  font-size: 28rpx;
  padding: 10rpx 60rpx;
  border-radius: 30rpx;
  margin-top: 20rpx;
  text-align: center;
  display: inline-block;
}
.notification-settings.data-v-0d4590e5 {
  display: flex;
  background-color: #fff;
  margin: 20rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 30rpx;
}
.notification-item.data-v-0d4590e5,
.settings-item.data-v-0d4590e5 {
  flex: 1;
  text-align: center;
  padding: 30rpx;
  position: relative;
  font-size: 28rpx;
}
.notification-item.data-v-0d4590e5 {
  border-right: 1px solid #f0f0f0;
}
.badge.data-v-0d4590e5 {
  position: absolute;
  top: 20rpx;
  right: 60rpx;
  background-color: #ff3a3a;
  color: #fff;
  font-size: 24rpx;
  width: 32rpx;
  height: 32rpx;
  line-height: 32rpx;
  text-align: center;
  border-radius: 50%;
}
.data-v-0d4590e5 .wd-cell-group {
  margin: 0 26rpx;
  border-radius: 18rpx;
  overflow: hidden;
  --wot-cell-line-height: 32px;
}
.data-v-0d4590e5 .wd-cell-group .wd-cell {
  --wot-cell-title-fs: 15px;
  --wot-cell-title-color: var(--color-gray);
}
.data-v-0d4590e5 .wd-cell-group .wd-cell .wd-cell__left {
  font-size: 15px;
}
.data-v-0d4590e5  .wd-message__button--confirm {
  color: #07C160 !important;
}
.data-v-0d4590e5  .wd-button--primary {
  background-color: #07C160 !important;
  border-color: #07C160 !important;
}
.avatar-options.data-v-0d4590e5 {
  background: #fff;
  border-radius: 12px 12px 0 0;
  padding: 0;
}
.avatar-options .options-title.data-v-0d4590e5 {
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  padding: 20px 0 10px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 10px;
}
.avatar-options .options-list.data-v-0d4590e5 {
  padding: 0 20px;
}
.avatar-options .options-list .option-item.data-v-0d4590e5 {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border: none;
  background: transparent;
  width: 100%;
  text-align: left;
  font-size: 16px;
  color: #333;
  border-bottom: 1px solid #f5f5f5;
}
.avatar-options .options-list .option-item.data-v-0d4590e5:last-child {
  border-bottom: none;
}
.avatar-options .options-list .option-item.option-item-hover.data-v-0d4590e5 {
  background-color: #f5f5f5;
}
.avatar-options .options-list .option-item .option-icon.data-v-0d4590e5 {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.avatar-options .options-list .option-item .option-icon .cuIcon-pic.data-v-0d4590e5 {
  color: #576b95;
  font-size: 20px;
}
.avatar-options .options-list .option-item .option-icon .cuIcon-camera.data-v-0d4590e5 {
  color: #ff6b6b;
  font-size: 20px;
}
.avatar-options .options-list .option-item .option-text.data-v-0d4590e5 {
  flex: 1;
  font-size: 16px;
  color: #333;
}
.avatar-options .options-cancel.data-v-0d4590e5 {
  text-align: center;
  padding: 15px 0;
  font-size: 16px;
  color: #666;
  border-top: 8px solid #f5f5f5;
  cursor: pointer;
}
.avatar-options .options-cancel.data-v-0d4590e5:active {
  background-color: #f0f0f0;
}