{"version": 3, "file": "PopupDict.js", "sources": ["../../../../../src/components/PopupDict/PopupDict.vue", "../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvY29tcG9uZW50cy9Qb3B1cERpY3QvUG9wdXBEaWN0LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"PopupDict\">\r\n    <view @click.stop=\"handleClick\">\r\n      <wd-select-picker\r\n        v-model=\"showText\"\r\n        :columns=\"options\"\r\n        readonly\r\n        :type=\"multi ? 'checkbox' : 'radio'\"\r\n        @click=\"() => (reportModal.show = true)\"\r\n        v-bind=\"$attrs\"\r\n      ></wd-select-picker>\r\n    </view>\r\n    <popupReportModal\r\n      v-if=\"reportModal.show\"\r\n      :code=\"code\"\r\n      :showFiled=\"labelFiled\"\r\n      :multi=\"multi\"\r\n      @close=\"handleClose\"\r\n      @change=\"handleChange\"\r\n    ></popupReportModal>\r\n  </view>\r\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { ref, watch } from 'vue'\r\nimport { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'\r\nimport { http } from '@/utils/http'\r\nimport popupReportModal from '@/components/Popup/components/popupReportModal.vue'\r\ndefineOptions({\r\n  name: 'PopupDict',\r\n  options: {\r\n    styleIsolation: 'shared'\r\n  }\r\n})\r\nconst props = defineProps({\r\n  dictCode: {\r\n    type: String,\r\n    required: true,\r\n    default: '',\r\n  },\r\n  modelValue: {\r\n    type: String,\r\n    default: '',\r\n  },\r\n  multi: {\r\n    type: Boolean,\r\n    default: false,\r\n  },\r\n  spliter: {\r\n    type: String,\r\n    default: ',',\r\n  },\r\n})\r\nconst emit = defineEmits(['change', 'update:modelValue'])\r\n\r\nconst toast = useToast()\r\nconst showText = ref<any>(props.multi ? [] : '')\r\nconst options = ref<any>([])\r\nconst cgRpConfigId = ref('')\r\nconst code = ref(props.dictCode.split(',')[0])\r\nconst labelFiled = ref(props.dictCode.split(',')[1])\r\nconst valueFiled = ref(props.dictCode.split(',')[2])\r\nconst reportModal = reactive({\r\n  show: false,\r\n})\r\n//定义请求url信息\r\nconst configUrl = reactive({\r\n  getColumns: '/online/cgreport/api/getRpColumns/',\r\n  getData: '/online/cgreport/api/getData/',\r\n})\r\n\r\nif (!code.value || !valueFiled.value || !labelFiled.value) {\r\n  toast.error('popupDict参数未正确配置!')\r\n}\r\n\r\n/**\r\n * 监听value数值\r\n */\r\nwatch(\r\n  () => props.modelValue,\r\n  (val) => {\r\n    const callBack = () => {\r\n      if (props.multi) {\r\n        showText.value = val && val.length > 0 ? val.split(props.spliter) : []\r\n      } else {\r\n        showText.value = val ?? ''\r\n      }\r\n    }\r\n    if (props.modelValue) {\r\n      if (cgRpConfigId.value) {\r\n        loadData({ callBack })\r\n      } else {\r\n        loadColumnsInfo({ callBack })\r\n      }\r\n    } else {\r\n      callBack()\r\n    }\r\n  },\r\n  { immediate: true },\r\n)\r\nwatch(\r\n  () => showText.value,\r\n  (val) => {\r\n    let result\r\n    if (props.multi) {\r\n      result = val.join(',')\r\n    } else {\r\n      result = val\r\n    }\r\n    nextTick(() => {\r\n      emit('change', result)\r\n      emit('update:modelValue', result)\r\n    })\r\n  },\r\n)\r\n\r\n/**\r\n * 加载列信息\r\n */\r\nfunction loadColumnsInfo({ callBack }) {\r\n  let url = `${configUrl.getColumns}${code.value}`\r\n  http\r\n    .get(url)\r\n    .then((res: any) => {\r\n      if (res.success) {\r\n        cgRpConfigId.value = res.result.cgRpConfigId\r\n        loadData({ callBack })\r\n      }\r\n    })\r\n    .catch((err) => {\r\n      callBack?.()\r\n    })\r\n}\r\nfunction loadData({ callBack }) {\r\n  let url = `${configUrl.getData}${unref(cgRpConfigId)}`\r\n  http\r\n    .get(url, { ['force_' + valueFiled.value]: props.modelValue })\r\n    .then((res: any) => {\r\n      let data = res.result\r\n      if (data.records?.length) {\r\n        options.value = data.records.map((item) => {\r\n          return { value: item[valueFiled.value], label: item[labelFiled.value] }\r\n        })\r\n      }\r\n    })\r\n    .finally(() => {\r\n      callBack?.()\r\n    })\r\n}\r\n/**\r\n * 传值回调\r\n */\r\nfunction callBack(rows) {\r\n  const dataOptions: any = []\r\n  const dataValue: any = []\r\n  let result\r\n  rows.forEach((item) => {\r\n    dataOptions.push({ value: item[valueFiled.value], label: item[labelFiled.value] })\r\n    dataValue.push(item[valueFiled.value])\r\n  })\r\n  options.value = dataOptions\r\n  if (props.multi) {\r\n    showText.value = dataValue\r\n    result = dataValue.join(props.spliter)\r\n  } else {\r\n    showText.value = dataValue[0]\r\n    result = dataValue[0]\r\n  }\r\n  nextTick(() => {\r\n    emit('change', result)\r\n    emit('update:modelValue', result)\r\n  })\r\n}\r\nconst handleClick = () => {\r\n   reportModal.show = true\r\n}\r\nconst handleClose = () => {\r\n  reportModal.show = false\r\n}\r\nconst handleChange = (data) => {\r\n  console.log('选中的值：', data)\r\n  callBack(data)\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped></style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/components/PopupDict/PopupDict.vue'\nwx.createComponent(Component)"], "names": ["useToast", "ref", "reactive", "watch", "callBack", "nextTick", "http", "unref", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,MAAA,mBAA6B,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7B,UAAM,QAAQ;AAmBd,UAAM,OAAO;AAEb,UAAM,QAAQA,cAAAA,SAAS;AACvB,UAAM,WAAWC,cAAAA,IAAS,MAAM,QAAQ,CAAA,IAAK,EAAE;AACzC,UAAA,UAAUA,cAAS,IAAA,EAAE;AACrB,UAAA,eAAeA,kBAAI,EAAE;AACrB,UAAA,OAAOA,kBAAI,MAAM,SAAS,MAAM,GAAG,EAAE,CAAC,CAAC;AACvC,UAAA,aAAaA,kBAAI,MAAM,SAAS,MAAM,GAAG,EAAE,CAAC,CAAC;AAC7C,UAAA,aAAaA,kBAAI,MAAM,SAAS,MAAM,GAAG,EAAE,CAAC,CAAC;AACnD,UAAM,cAAcC,cAAAA,SAAS;AAAA,MAC3B,MAAM;AAAA,IAAA,CACP;AAED,UAAM,YAAYA,cAAAA,SAAS;AAAA,MACzB,YAAY;AAAA,MACZ,SAAS;AAAA,IAAA,CACV;AAEG,QAAA,CAAC,KAAK,SAAS,CAAC,WAAW,SAAS,CAAC,WAAW,OAAO;AACzD,YAAM,MAAM,mBAAmB;AAAA,IAAA;AAMjCC,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,QAAQ;AACP,cAAMC,YAAW,MAAM;AACrB,cAAI,MAAM,OAAO;AACN,qBAAA,QAAQ,OAAO,IAAI,SAAS,IAAI,IAAI,MAAM,MAAM,OAAO,IAAI,CAAC;AAAA,UAAA,OAChE;AACL,qBAAS,QAAQ,oBAAO;AAAA,UAAA;AAAA,QAE5B;AACA,YAAI,MAAM,YAAY;AACpB,cAAI,aAAa,OAAO;AACb,qBAAA,EAAE,UAAAA,WAAU;AAAA,UAAA,OAChB;AACW,4BAAA,EAAE,UAAAA,WAAU;AAAA,UAAA;AAAA,QAC9B,OACK;AACLA,oBAAS;AAAA,QAAA;AAAA,MAEb;AAAA,MACA,EAAE,WAAW,KAAK;AAAA,IACpB;AACAD,kBAAA;AAAA,MACE,MAAM,SAAS;AAAA,MACf,CAAC,QAAQ;AACH,YAAA;AACJ,YAAI,MAAM,OAAO;AACN,mBAAA,IAAI,KAAK,GAAG;AAAA,QAAA,OAChB;AACI,mBAAA;AAAA,QAAA;AAEXE,sBAAAA,WAAS,MAAM;AACb,eAAK,UAAU,MAAM;AACrB,eAAK,qBAAqB,MAAM;AAAA,QAAA,CACjC;AAAA,MAAA;AAAA,IAEL;AAKA,aAAS,gBAAgB,EAAE,UAAAD,aAAY;AACrC,UAAI,MAAM,GAAG,UAAU,UAAU,GAAG,KAAK,KAAK;AAC9CE,iBAAAA,KACG,IAAI,GAAG,EACP,KAAK,CAAC,QAAa;AAClB,YAAI,IAAI,SAAS;AACF,uBAAA,QAAQ,IAAI,OAAO;AACvB,mBAAA,EAAE,UAAAF,WAAU;AAAA,QAAA;AAAA,MACvB,CACD,EACA,MAAM,CAAC,QAAQ;AACdA;AAAAA,MAAW,CACZ;AAAA,IAAA;AAEL,aAAS,SAAS,EAAE,UAAAA,aAAY;AAC9B,UAAI,MAAM,GAAG,UAAU,OAAO,GAAGG,cAAA,MAAM,YAAY,CAAC;AACpDD,iBAAAA,KACG,IAAI,KAAK,EAAE,CAAC,WAAW,WAAW,KAAK,GAAG,MAAM,WAAY,CAAA,EAC5D,KAAK,CAAC,QAAa;;AAClB,YAAI,OAAO,IAAI;AACX,aAAA,UAAK,YAAL,mBAAc,QAAQ;AACxB,kBAAQ,QAAQ,KAAK,QAAQ,IAAI,CAAC,SAAS;AAClC,mBAAA,EAAE,OAAO,KAAK,WAAW,KAAK,GAAG,OAAO,KAAK,WAAW,KAAK,EAAE;AAAA,UAAA,CACvE;AAAA,QAAA;AAAA,MACH,CACD,EACA,QAAQ,MAAM;AACbF;AAAAA,MAAW,CACZ;AAAA,IAAA;AAKL,aAAS,SAAS,MAAM;AACtB,YAAM,cAAmB,CAAC;AAC1B,YAAM,YAAiB,CAAC;AACpB,UAAA;AACC,WAAA,QAAQ,CAAC,SAAS;AACrB,oBAAY,KAAK,EAAE,OAAO,KAAK,WAAW,KAAK,GAAG,OAAO,KAAK,WAAW,KAAK,EAAA,CAAG;AACjF,kBAAU,KAAK,KAAK,WAAW,KAAK,CAAC;AAAA,MAAA,CACtC;AACD,cAAQ,QAAQ;AAChB,UAAI,MAAM,OAAO;AACf,iBAAS,QAAQ;AACR,iBAAA,UAAU,KAAK,MAAM,OAAO;AAAA,MAAA,OAChC;AACI,iBAAA,QAAQ,UAAU,CAAC;AAC5B,iBAAS,UAAU,CAAC;AAAA,MAAA;AAEtBC,oBAAAA,WAAS,MAAM;AACb,aAAK,UAAU,MAAM;AACrB,aAAK,qBAAqB,MAAM;AAAA,MAAA,CACjC;AAAA,IAAA;AAEH,UAAM,cAAc,MAAM;AACvB,kBAAY,OAAO;AAAA,IACtB;AACA,UAAM,cAAc,MAAM;AACxB,kBAAY,OAAO;AAAA,IACrB;AACM,UAAA,eAAe,CAAC,SAAS;AACrB,cAAA,IAAI,SAAS,IAAI;AACzB,eAAS,IAAI;AAAA,IACf;;;;;;;;;;;;;;;;;;;;;;;;;;ACrLA,GAAG,gBAAgBG,SAAS;"}