"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const store_user = require("../../store/user.js");
const utils_http = require("../../utils/http.js");
if (!Array) {
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  _easycom_PageLayout();
}
const defAvatar = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNGNUY1RjUiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iIzk5OTk5OSIvPgo8cGF0aCBkPSJNMzAgMzJDMzAgMjYuNDc3MSAyNS41MjI5IDIyIDIwIDIyQzE0LjQ3NzEgMjIgMTAgMjYuNDc3MSAxMCAzMkgzMFoiIGZpbGw9IiM5OTk5OTkiLz4KPC9zdmc+";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "addChat",
  setup(__props) {
    const userStore = store_user.useUserStore();
    const formData = common_vendor.ref({
      title: "",
      question: "",
      patientId: ""
    });
    const selectedImages = common_vendor.ref([]);
    const isSubmitting = common_vendor.ref(false);
    const doctorList = common_vendor.ref([]);
    const selectedDoctors = common_vendor.ref([]);
    const loadingDoctors = common_vendor.ref(false);
    const selectedDoctorIds = common_vendor.ref("");
    const selectedDoctorNames = common_vendor.ref("");
    const getAvatarUrl = (avatar) => {
      if (!avatar || avatar.trim() === "") {
        return defAvatar;
      }
      if (avatar.startsWith("http://") || avatar.startsWith("https://")) {
        return avatar;
      }
      if (avatar.startsWith("data:image/")) {
        return avatar;
      }
      if (avatar.startsWith("/")) {
        const fullUrl2 = "https://www.mograine.cn" + avatar;
        return fullUrl2;
      }
      const fullUrl = "https://www.mograine.cn/images/" + avatar;
      return fullUrl;
    };
    const handleAvatarError = (event) => {
      event.target.src = defAvatar;
    };
    const getUserRoleText = () => {
      const userCategory = Number(userStore.userInfo.userCategory);
      switch (userCategory) {
        case 0:
          return "医生";
        case 1:
          return "患者";
        case 2:
          return "社工/社区医生";
        default:
          return "用户";
      }
    };
    const fetchDoctorList = () => __async(this, null, function* () {
      try {
        loadingDoctors.value = true;
        const response = yield utils_http.http.get("/sys/user/doctorList");
        if (response.success && response.result) {
          let doctorData = response.result;
          console.log("doctorData", doctorData);
          const doctors = (doctorData || []).map((doctor, index) => {
            if (!doctor.realName) {
              console.warn(`⚠️ 医生${index}缺少realName字段:`, doctor);
            }
            return __spreadProps(__spreadValues({}, doctor), {
              // 确保有唯一标识符
              userId: doctor.id || doctor.userId || doctor.username || `doctor_${index}`,
              // 保持API返回的原始realName，不做任何修改
              realName: doctor.realname || "未知医生",
              displayName: doctor.realname || "未知医生"
              // 为了兼容性
            });
          });
          doctorList.value = doctors;
          console.log("获取医生列表成功，共", doctors.length, "位医生");
          console.log("医生列表详情:", doctors.map((d) => ({ userId: d.userId, realName: d.realName, id: d.id })));
        } else {
          console.error("获取医生列表失败:", response.message);
          common_vendor.index.showToast({
            title: "获取医生列表失败",
            icon: "none"
          });
        }
      } catch (error) {
        console.error("获取医生列表异常:", error);
        common_vendor.index.showToast({
          title: "获取医生列表失败",
          icon: "none"
        });
      } finally {
        loadingDoctors.value = false;
      }
    });
    const onDoctorSelect = (event) => {
      const selectedIndex = event.detail.value;
      const selectedDoctor = doctorList.value[selectedIndex];
      if (!selectedDoctor)
        return;
      console.log("🔍 检查医生是否已选择:", {
        selectedDoctor,
        selectedDoctorUserId: selectedDoctor.userId,
        currentSelectedDoctors: selectedDoctors.value.map((d) => ({ userId: d.userId, realName: d.realName }))
      });
      const isAlreadySelected = selectedDoctors.value.some(
        (doctor) => {
          const doctorId = String(doctor.userId);
          const selectedId = String(selectedDoctor.userId);
          const isSame = doctorId === selectedId;
          console.log(`比较医生: ${doctor.realName}(${doctorId}) vs ${selectedDoctor.realName}(${selectedId}) = ${isSame}`);
          return isSame;
        }
      );
      if (isAlreadySelected) {
        common_vendor.index.showToast({
          title: "该医生已选择",
          icon: "none"
        });
        return;
      }
      selectedDoctors.value.push(selectedDoctor);
      updateSelectedDoctorInfo();
      console.log("选择医生:", selectedDoctor);
      console.log("当前已选择医生:", selectedDoctors.value);
    };
    const removeDoctorSelection = (index) => {
      selectedDoctors.value.splice(index, 1);
      updateSelectedDoctorInfo();
      console.log("移除医生后的列表:", selectedDoctors.value);
    };
    const updateSelectedDoctorInfo = () => {
      const doctorIds = selectedDoctors.value.map((doctor) => doctor.userId).join(",");
      const doctorNames = selectedDoctors.value.map((doctor) => `@${doctor.realName || "未知医生"}`).join(" ");
      selectedDoctorIds.value = doctorIds;
      selectedDoctorNames.value = doctorNames;
      console.log("📝 更新医生信息:", {
        doctorIds: selectedDoctorIds.value,
        doctorNames: selectedDoctorNames.value
      });
    };
    function chooseFromGallery() {
      if (selectedImages.value.length >= 6) {
        common_vendor.index.showToast({ title: "最多只能上传6张图片", icon: "none" });
        return;
      }
      const remainingCount = 6 - selectedImages.value.length;
      common_vendor.index.chooseImage({
        count: remainingCount,
        sourceType: ["album"],
        success: (res) => {
          if (res.tempFilePaths && res.tempFilePaths.length > 0) {
            selectedImages.value.push(...res.tempFilePaths);
          }
        },
        fail: (err) => {
          console.error("选择图片失败:", err);
          common_vendor.index.showToast({ title: "选择图片失败", icon: "none" });
        }
      });
    }
    function takePhoto() {
      if (selectedImages.value.length >= 6) {
        common_vendor.index.showToast({ title: "最多只能上传6张图片", icon: "none" });
        return;
      }
      common_vendor.index.chooseImage({
        count: 1,
        sourceType: ["camera"],
        success: (res) => {
          if (res.tempFilePaths && res.tempFilePaths.length > 0) {
            selectedImages.value.push(...res.tempFilePaths);
          }
        },
        fail: (err) => {
          console.error("拍照失败:", err);
          common_vendor.index.showToast({ title: "拍照失败", icon: "none" });
        }
      });
    }
    function removeImage(index) {
      selectedImages.value.splice(index, 1);
    }
    function previewImage(url) {
      common_vendor.index.previewImage({
        current: url,
        urls: selectedImages.value
      });
    }
    function validateForm() {
      if (!formData.value.question.trim()) {
        common_vendor.index.showToast({ title: "请输入问题描述", icon: "none" });
        return false;
      }
      if (formData.value.title.trim() && formData.value.title.length > 50) {
        common_vendor.index.showToast({ title: "标题不能超过50个字符", icon: "none" });
        return false;
      }
      if (formData.value.question.length > 1e3) {
        common_vendor.index.showToast({ title: "问题描述不能超过1000个字符", icon: "none" });
        return false;
      }
      return true;
    }
    function submitQuestion() {
      return __async(this, null, function* () {
        if (!validateForm())
          return;
        if (isSubmitting.value) {
          common_vendor.index.showToast({ title: "正在提交中，请勿重复操作", icon: "none" });
          return;
        }
        try {
          isSubmitting.value = true;
          const submitData = {
            title: formData.value.title.trim() || "",
            patientId: userStore.userInfo.userid || "",
            question: formData.value.question.trim(),
            images: selectedImages.value,
            doctorIds: selectedDoctors.value.map((doctor) => doctor.userId)
            // 传递选中的医生ID数组
          };
          console.log("提交数据:", submitData);
          const response = yield utils_http.http.post("/communication/saveCommunication", submitData);
          if (response.success) {
            common_vendor.index.showToast({
              title: "提交成功",
              icon: "success",
              duration: 2e3
            });
            selectedDoctors.value = [];
            selectedDoctorIds.value = "";
            selectedDoctorNames.value = "";
            setTimeout(() => {
              common_vendor.index.navigateBack({
                delta: 1,
                success: () => {
                  common_vendor.index.$emit("clearDoctorSelection");
                }
              });
            }, 2e3);
          } else {
            throw new Error(response.message || "提交失败");
          }
        } catch (error) {
          console.error("提交失败:", error);
          common_vendor.index.showToast({
            title: error.message || "提交失败，请重试",
            icon: "none",
            duration: 3e3
          });
        } finally {
          isSubmitting.value = false;
        }
      });
    }
    common_vendor.onLoad((options) => {
      formData.value.patientId = userStore.userInfo.userid || "";
      fetchDoctorList();
      if (options == null ? void 0 : options.selectedDoctorIds) {
        selectedDoctorIds.value = decodeURIComponent(options.selectedDoctorIds);
      }
      if (options == null ? void 0 : options.selectedDoctorNames) {
        selectedDoctorNames.value = decodeURIComponent(options.selectedDoctorNames);
      }
      getAvatarUrl(userStore.userInfo.avatar);
    });
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: getAvatarUrl(common_vendor.unref(userStore).userInfo.avatar),
        b: common_vendor.o(handleAvatarError),
        c: common_vendor.t(common_vendor.unref(userStore).userInfo.realname || "未知用户"),
        d: common_vendor.t(getUserRoleText()),
        e: common_vendor.t(selectedDoctors.value.length > 0 ? "点击选择更多医生" : "请选择医生"),
        f: doctorList.value,
        g: common_vendor.o(onDoctorSelect),
        h: selectedDoctors.value.length > 0
      }, selectedDoctors.value.length > 0 ? {
        i: common_vendor.f(selectedDoctors.value, (doctor, index, i0) => {
          return {
            a: common_vendor.t(doctor.realName || "未知医生"),
            b: doctor.userId || index,
            c: common_vendor.o(($event) => removeDoctorSelection(index), doctor.userId || index)
          };
        })
      } : {}, {
        j: formData.value.title,
        k: common_vendor.o(($event) => formData.value.title = $event.detail.value),
        l: common_vendor.t(formData.value.title.length),
        m: formData.value.question,
        n: common_vendor.o(($event) => formData.value.question = $event.detail.value),
        o: common_vendor.t(formData.value.question.length),
        p: selectedImages.value.length > 0
      }, selectedImages.value.length > 0 ? {
        q: common_vendor.f(selectedImages.value, (img, index, i0) => {
          return {
            a: img,
            b: common_vendor.o(($event) => removeImage(index), index),
            c: index,
            d: common_vendor.o(($event) => previewImage(img), index)
          };
        })
      } : {}, {
        r: common_vendor.o(chooseFromGallery),
        s: selectedImages.value.length >= 6,
        t: common_vendor.o(takePhoto),
        v: selectedImages.value.length >= 6,
        w: common_vendor.t(isSubmitting.value ? "提交中..." : "提交问题"),
        x: common_vendor.o(submitQuestion),
        y: isSubmitting.value || !formData.value.question.trim(),
        z: isSubmitting.value || !formData.value.question.trim() ? 1 : "",
        A: common_vendor.p({
          navLeftArrow: true,
          navLeftText: "返回"
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-dc103050"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=addChat.js.map
