"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
const store_user = require("../../store/user.js");
const utils_http = require("../../utils/http.js");
const common_uitls = require("../../common/uitls.js");
const pagesMessage_common_socket = require("../common/socket.js");
const pagesMessage_chat_emojis = require("./emojis.js");
const utils_index = require("../../utils/index.js");
const store_pageParams = require("../../store/page-params.js");
const hooks_useUpload = require("../../hooks/useUpload.js");
if (!Array) {
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_z_paging2 + _easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_z_paging = () => "../../uni_modules/z-paging/components/z-paging/z-paging.js";
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  (chatItem + chatInputBar + _easycom_z_paging + _easycom_PageLayout)();
}
const chatInputBar = () => "./components/chat-input-bar.js";
const chatItem = () => "./components/chat-item.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "chat",
  options: {
    // apply-shared‌：当前页面样式会影响到子组件样式.(小程序)
    // shared‌：当前页面样式影响到子组件，子组件样式也会影响到当前页面.(小程序)
    styleIsolation: "‌apply-shared‌"
  }
}), {
  __name: "chat",
  setup(__props) {
    const api = {
      chatlog: "/eoa/im/newApi/records",
      sendMsg: "/eoa/im/newApi/sendMessage",
      creatFriendSession: "/eoa/im/newApi/creatFriendSession",
      uploadUrl: `${utils_index.getEnvBaseUrl()}/eoa/im/newApi/sendImage`
    };
    const toast = common_vendor.useToast();
    const userStore = store_user.useUserStore();
    const paging = common_vendor.ref(null);
    const chatObj = common_vendor.ref(null);
    const navTitle = common_vendor.ref("");
    const chatto = common_vendor.ref();
    const myuid = common_vendor.ref(userStore.userInfo.userid);
    const msgList = common_vendor.ref([]);
    common_vendor.ref(false);
    common_vendor.ref(false);
    const dataList = common_vendor.ref([]);
    const inputBar = common_vendor.ref(null);
    const AUDIO = common_vendor.index.createInnerAudioContext();
    const playMsgid = common_vendor.ref("");
    let stopWatch = null;
    const paramsStore = store_pageParams.useParamsStore();
    const init = () => {
      const localData = paramsStore.getPageParams("chat");
      const params = localData.data;
      if (!params) {
        return;
      }
      chatObj.value = __spreadValues({}, params);
      navTitle.value = params.fromUserName;
      chatto.value = chatObj.value.msgTo;
      creatFriendSession(chatObj.value.msgTo);
      onSocketOpen();
      onSocketReceive();
    };
    const creatFriendSession = (userId) => {
      utils_http.http.post(api.creatFriendSession, {
        type: "friend",
        userId
      });
    };
    const onSocketOpen = () => {
      console.log("启动webSocket");
      pagesMessage_common_socket.mySocket.init("eoaNewChatSocket");
    };
    const onSocketReceive = () => {
      pagesMessage_common_socket.mySocket.acceptMessage = function(res) {
        console.log("页面收到的消息=====》", res);
        if (res.event == "event_talk_revoke") {
          removeMsg(res);
        } else {
          if (res.type == "friend") {
            screenMsg(res);
            unreadClear();
          }
        }
      };
    };
    const removeMsg = (data) => {
      let arr = msgList.value.filter((item) => item.id != data.id);
      msgList.value = arr;
    };
    const screenMsg = (msg) => {
      if (msg.msgFrom == chatto.value && msg.msgTo == myuid.value) {
        console.log("用户消息");
        let time = common_uitls.formatDate(msg.sendTime, "yyyy-MM-dd hh:mm:ss");
        let id = time.replace(/\:/g, "").replace(/\-/g, "").replace(" ", "");
        let content = msg.msgData;
        if (msg.msgType == "text") {
          content = replaceEmoji(content);
        }
        if (msg.msgType == "voice") {
          content = JSON.parse(content);
        }
        msgList.value.push({
          fromUserName: msg.fromUserName,
          msgTo: msg.msgTo,
          msgFrom: msg.msgFrom,
          msgData: content,
          fromAvatar: msg.fromAvatar,
          sendTime: time,
          msgType: msg.msgType,
          sendTimeId: id,
          fileName: msg.fileName,
          id: msg.id
        });
        if (msg.msgFrom != myuid.value) {
          console.log("振动");
          common_vendor.index.vibrateLong();
        }
      }
    };
    const replaceEmoji = (str) => {
      let temp = pagesMessage_chat_emojis.textReplaceEmoji(str);
      return '<div style="display:inline-block">' + temp + "</div>";
    };
    const queryList = (pageNo, pageSize) => {
      let params = {
        type: "friend",
        pageNo,
        pageSize,
        msgTo: chatto.value,
        id: myuid.value,
        sort: "DESC"
      };
      console.log("params", params);
      utils_http.http.get(api.chatlog, params).then((res) => {
        var _a;
        if (res.success && ((_a = res.result) == null ? void 0 : _a.records)) {
          const records = analysis(res.result.records);
          paging.value.complete(records);
        } else {
          paging.value.complete(false);
        }
      }).catch((res) => {
        paging.value.complete(false);
      });
    };
    const analysis = (data) => {
      let arr = data;
      if (arr.length > 0) {
        let list = arr.map((item) => {
          let id = item.sendTime.replace(/\:/g, "").replace(/\-/g, "").replace(" ", "");
          item.sendTimeId = id;
          let content = item.msgData;
          if (item.msgType == "text") {
            content = replaceEmoji(content);
          }
          if (item.msgType == "voice") {
            content = JSON.parse(content);
          }
          item.msgData = content;
          return item;
        });
        for (let i = 0; i < list.length; i++) {
          if (list[i].msgType == "revoke") {
            continue;
          }
          if (list[i].referenceMsgId) {
            list[i] = handleReplyMsg(list[i], list);
          }
        }
      }
      return data;
    };
    const handleReplyMsg = (item, list) => {
      let tempId = item.referenceMsgId;
      item.reply = true;
      let replyContent = "";
      for (let i = 0; i < list.length; i++) {
        if (list[i].id == tempId) {
          replyContent = '"' + list[i].fromUserName + ":" + list[i].msgData + '"';
          break;
        }
      }
      item.replyContent = replyContent;
      return item;
    };
    const unreadClear = () => {
      utils_http.http.post("/eoa/im/newApi/unreadClear", {
        type: chatObj.value.type,
        msgTo: chatObj.value.msgTo,
        msgFrom: chatObj.value.msgFrom
      }).then((res) => {
        if (res.success)
          ;
      });
    };
    const handlePlayVoice = (item) => {
      if (item.id == playMsgid.value) {
        AUDIO.stop();
        playMsgid.value = "";
      } else {
        playMsgid.value = item.id;
        AUDIO.src = item.msgData.url;
        common_vendor.nextTick$1(function() {
          AUDIO.play();
        });
      }
    };
    AUDIO.onEnded((res) => {
      playMsgid.value = "";
    });
    const keyboardHeightChange = (res) => {
      inputBar.value.updateKeyboardHeightChange(res);
    };
    const hidedKeyboard = () => {
      inputBar.value.hidedKeyboard();
    };
    const doSend = (textMsg) => {
      let content = replaceEmoji(textMsg);
      console.log("content", content);
      let msg = textMsg;
      let time = common_uitls.formatDate((/* @__PURE__ */ new Date()).getTime(), "yyyy-MM-dd hh:mm:ss");
      let id = time.replace(/\:/g, "").replace(/\-/g, "").replace(" ", "");
      sendMsg(msg);
      paging.value.addChatRecordData(
        analysis([
          {
            fromUserName: userStore.userInfo.realname,
            msgTo: chatto.value,
            msgFrom: myuid.value,
            msgData: content,
            fromAvatar: userStore.userInfo.avatar,
            sendTime: time,
            sendTimeId: id,
            msgType: "text"
          }
        ])
      );
    };
    const sendMsg = (content, type) => {
      var obj = {
        mine: {
          avatar: userStore.userInfo.avatar,
          content,
          id: myuid.value,
          mine: true,
          username: userStore.userInfo.username
        },
        to: {
          avatar: chatObj.value.avatar,
          id: chatObj.value.msgTo,
          type: "friend",
          username: chatObj.value.username
        }
      };
      let sendData = {
        type: "chatMessage",
        data: obj
      };
      console.log("sendData======>", sendData);
      let params = {
        type: "friend",
        msgTo: chatObj.value.msgTo,
        text: content,
        msgType: "text"
      };
      utils_http.http.post(api.sendMsg, params).then((res) => {
        console.log("消息发送结果：", res);
        if (!res.success) {
          toast.error(res.message);
        }
      });
    };
    const handleImage = (type) => {
      let time = common_uitls.formatDate((/* @__PURE__ */ new Date()).getTime(), "yyyy-MM-dd hh:mm:ss");
      let id = time.replace(/\:/g, "").replace(/\-/g, "").replace(" ", "");
      let formData = {
        type: "friend",
        msgTo: chatto.value,
        fileId: id,
        msgType: "images",
        fileName: ""
      };
      const { loading, data, error, run } = hooks_useUpload.useUpload(
        __spreadProps(__spreadValues({}, formData), { name: "image" }),
        { url: api.uploadUrl, sourceType: [type] }
      );
      if (stopWatch)
        stopWatch();
      run();
      stopWatch = common_vendor.watch(
        () => [loading.value, error.value, data.value],
        ([loading2, err, data2], oldValue) => {
          if (loading2 == false) {
            if (err) {
              toast.warning("修改失败");
              common_vendor.index.hideLoading();
            } else {
              if (data2) {
                console.log("data::", data2);
              }
            }
          }
        }
      );
    };
    init();
    return (_ctx, _cache) => {
      return {
        a: common_vendor.w(({
          item,
          index
        }, s0, i0) => {
          return {
            a: "6aa5e47b-3-" + i0 + ",6aa5e47b-2",
            b: common_vendor.p({
              item,
              playMsgid: playMsgid.value
            }),
            c: i0,
            d: s0
          };
        }, {
          name: "cell",
          path: "a",
          vueId: "6aa5e47b-2,6aa5e47b-1"
        }),
        b: common_vendor.o(handlePlayVoice),
        c: common_vendor.sr(inputBar, "6aa5e47b-4,6aa5e47b-2", {
          "k": "inputBar"
        }),
        d: common_vendor.o(doSend),
        e: common_vendor.o(handleImage),
        f: common_vendor.sr(paging, "6aa5e47b-2,6aa5e47b-1", {
          "k": "paging"
        }),
        g: common_vendor.o(queryList),
        h: common_vendor.o(keyboardHeightChange),
        i: common_vendor.o(hidedKeyboard),
        j: common_vendor.o(($event) => dataList.value = $event),
        k: common_vendor.p({
          fixed: false,
          ["use-chat-record-mode"]: true,
          ["use-virtual-list"]: true,
          ["cell-height-mode"]: "dynamic",
          ["safe-area-inset-bottom"]: true,
          ["bottom-bg-color"]: "#e5e5e5",
          modelValue: dataList.value
        }),
        l: common_vendor.p({
          navTitle: navTitle.value,
          backRouteName: "message",
          routeMethod: "pushTab"
        })
      };
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-6aa5e47b"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=chat.js.map
