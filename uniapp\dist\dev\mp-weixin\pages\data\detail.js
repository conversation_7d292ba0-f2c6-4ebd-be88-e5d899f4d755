"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const store_user = require("../../store/user.js");
const utils_http = require("../../utils/http.js");
if (!Array) {
  const _component_NavBar = common_vendor.resolveComponent("NavBar");
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  const _easycom_wd_loading2 = common_vendor.resolveComponent("wd-loading");
  const _easycom_wd_text2 = common_vendor.resolveComponent("wd-text");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _easycom_uni_calendar2 = common_vendor.resolveComponent("uni-calendar");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_component_NavBar + _easycom_uni_icons2 + _easycom_wd_loading2 + _easycom_wd_text2 + _easycom_PageLayout2 + _easycom_uni_calendar2 + _component_layout_default_uni)();
}
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
const _easycom_wd_loading = () => "../../node-modules/wot-design-uni/components/wd-loading/wd-loading.js";
const _easycom_wd_text = () => "../../node-modules/wot-design-uni/components/wd-text/wd-text.js";
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
const _easycom_uni_calendar = () => "../../uni_modules/uni-calendar/components/uni-calendar/uni-calendar.js";
if (!Math) {
  (_easycom_uni_icons + _easycom_wd_loading + _easycom_wd_text + _easycom_PageLayout + _easycom_uni_calendar)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "detail",
  setup(__props) {
    store_user.useUserStore();
    const patientId = common_vendor.ref("");
    const patientDataList = common_vendor.ref([]);
    const loadingPatientData = common_vendor.ref(false);
    const startDate = common_vendor.ref("");
    const endDate = common_vendor.ref("");
    const datePickerType = common_vendor.ref("");
    const calendar = common_vendor.ref(null);
    const selectedTypeIndex = common_vendor.ref(-1);
    common_vendor.onLoad((options) => {
      if (options && options.id) {
        patientId.value = options.id;
        getPatientDataList();
      }
    });
    function onStartDateChange(e) {
      startDate.value = e.detail.value;
    }
    function onEndDateChange(e) {
      endDate.value = e.detail.value;
    }
    function onDataTypeChange(e) {
      selectedTypeIndex.value = e.detail.value;
    }
    const dateConfirm = (e) => {
      const selectedDate = e.fulldate;
      if (datePickerType.value === "start") {
        startDate.value = selectedDate;
      } else if (datePickerType.value === "end") {
        endDate.value = selectedDate;
      }
    };
    const resetDateFilter = () => {
      startDate.value = "";
      endDate.value = "";
      selectedTypeIndex.value = -1;
      getPatientDataList();
    };
    const searchByDate = () => {
      fetchData();
    };
    const fetchData = () => {
      loadingPatientData.value = true;
      console.log("开始获取数据...");
      if (selectedTypeIndex.value !== -1) {
        fetchSpecificTypeData();
      } else {
        fetchAllData();
      }
    };
    const fetchSpecificTypeData = () => {
      const selectedType = dataTypes.value[selectedTypeIndex.value];
      const apiUrl = selectedType.api;
      console.log(`获取${selectedType.label}数据...`);
      const params = {
        userId: patientId.value
      };
      if (startDate.value) {
        params.beginDate = startDate.value;
      }
      if (endDate.value) {
        params.endDate = endDate.value;
      }
      common_vendor.index.request({
        url: `${"https://www.mograine.cn/api"}${apiUrl}`,
        method: "GET",
        data: params,
        success: (res) => {
          const response = res.data;
          console.log("接口返回数据:", response);
          if (response && response.code === 200) {
            if (response.result && response.result.records) {
              const processedRecords = (response.result.records || []).map((item) => {
                const iconPath = getIconByType(selectedType.label);
                let createTime = item.createTime;
                if (selectedType.label === "院外检查报告" && item.creatTime) {
                  createTime = item.creatTime;
                }
                return __spreadProps(__spreadValues({}, item), {
                  type: selectedType.label,
                  typeName: selectedType.label,
                  iconPath,
                  createTime
                });
              });
              patientDataList.value = processedRecords;
              console.log("解析后的列表数据:", patientDataList.value);
            } else {
              patientDataList.value = [];
              console.log("没有找到records数据");
            }
          } else {
            console.error("获取数据失败", response.msg);
            common_vendor.index.showToast({
              title: response.msg || "获取数据失败",
              icon: "none"
            });
            patientDataList.value = [];
          }
        },
        fail: (err) => {
          console.error("获取数据异常:", err);
          common_vendor.index.showToast({
            title: "网络异常，请稍后重试",
            icon: "none"
          });
          patientDataList.value = [];
        },
        complete: () => {
          loadingPatientData.value = false;
        }
      });
    };
    const fetchAllData = () => {
      const params = {
        userId: patientId.value
      };
      if (startDate.value) {
        params.beginDate = startDate.value;
      }
      if (endDate.value) {
        params.endDate = endDate.value;
      }
      utils_http.http.get("/patient/getalldata", params).then((res) => {
        if (res.success && res.result) {
          const originalRecords = res.result.records || [];
          const processedRecords = [];
          const medicationTimeMap = /* @__PURE__ */ new Map();
          originalRecords.forEach((item) => {
            if (item.type === "知情同意与登记表") {
              return;
            }
            const iconPath = getIconByType(item.type);
            const processedItem = __spreadProps(__spreadValues({}, item), {
              typeName: item.type,
              iconPath
            });
            if (item.type === "用药情况") {
              const timeKey = item.createTime;
              if (medicationTimeMap.has(timeKey)) {
                const existingItem = medicationTimeMap.get(timeKey);
                if (!existingItem.idList)
                  existingItem.idList = [existingItem.id];
                existingItem.idList.push(item.id);
                existingItem.count = (existingItem.count || 1) + 1;
              } else {
                const medicationItem = __spreadProps(__spreadValues({}, processedItem), { count: 1 });
                medicationTimeMap.set(timeKey, medicationItem);
                processedRecords.push(medicationItem);
              }
            } else {
              processedRecords.push(processedItem);
            }
          });
          patientDataList.value = processedRecords;
          console.log("解析后的列表数据:", patientDataList.value);
        } else {
          console.error("获取数据失败", res.message);
          common_vendor.index.showToast({
            title: res.message || "获取数据失败",
            icon: "none"
          });
          patientDataList.value = [];
        }
      }).catch((err) => {
        console.error("获取数据异常:", err);
        common_vendor.index.showToast({
          title: "网络异常，请稍后重试",
          icon: "none"
        });
        patientDataList.value = [];
      }).finally(() => {
        loadingPatientData.value = false;
      });
    };
    const dataTypes = common_vendor.ref([
      { label: "日常体征监测", value: "vital-signs", api: "/patient/getdailysigns" },
      { label: "用药情况", value: "medication", api: "/patient/getmedications" },
      { label: "院外检查报告", value: "report", api: "/patient/getreport" },
      { label: "监测表", value: "monitor", api: "/patient/getmonitoring" },
      { label: "心理量表", value: "psychology", api: "/patient/getpsychologicaltable" },
      { label: "日常生活指数评估", value: "daily-life", api: "/patient/getdailyassessment" }
    ]);
    const viewDataDetail = (item) => {
      console.log("查看详情，数据类型:", item.type);
      let targetUrl = "";
      let urlParams = "";
      switch (item.type) {
        case "日常体征监测":
          targetUrl = "/pages-data/vital-signs/form";
          common_vendor.index.setStorageSync("detail_query", { id: item.id });
          urlParams = `id=${item.id}&mode=view`;
          break;
        case "用药情况":
          targetUrl = "/pages-data/medication/form";
          urlParams = `idList=${item.idList.join(",")}&mode=view`;
          break;
        case "院外检查报告":
          targetUrl = "/pages-data/examinationReport/form";
          urlParams = `idList=${item.idList.join(",")}&mode=view`;
          break;
        case "监测表":
          targetUrl = "/pages-data/monitor/form";
          common_vendor.index.setStorageSync("detail_query", { id: item.id });
          urlParams = `id=${item.id}&mode=view`;
          break;
        case "心理量表":
          targetUrl = "/pages-data/psychology/form";
          common_vendor.index.setStorageSync("detail_query", { id: item.id });
          urlParams = `id=${item.id}&mode=view`;
          break;
        case "日常生活指数评估":
          targetUrl = "/pages-data/dailyLife/form";
          common_vendor.index.setStorageSync("detail_query", { id: item.id });
          urlParams = `id=${item.id}&mode=view`;
          break;
        default:
          targetUrl = "";
          urlParams = "";
      }
      console.log("跳转到页面:", targetUrl);
      if (targetUrl) {
        common_vendor.index.navigateTo({
          url: `${targetUrl}?${urlParams}`
        });
      }
    };
    const getPatientDataList = () => __async(this, null, function* () {
      loadingPatientData.value = true;
      const params = {
        userId: patientId.value
      };
      utils_http.http.get("/patient/getalldata", params).then((res) => {
        if (res.success && res.result) {
          patientDataList.value = (res.result.records || []).filter((item) => item.type !== "知情同意与登记表").map((item) => {
            const iconPath = getIconByType(item.type);
            return __spreadProps(__spreadValues({}, item), {
              typeName: item.type,
              iconPath
            });
          });
        } else {
          console.error("获取近期数据失败", res.message);
        }
      }).finally(() => {
        loadingPatientData.value = false;
      });
    });
    const getIconByType = (type) => {
      const iconMap = {
        "日常体征监测": "https://www.mograine.cn/images/vital-signs.png",
        "用药情况": "https://www.mograine.cn/images/medication.png",
        "院外检查报告": "https://www.mograine.cn/images/report.png",
        "监测表": "https://www.mograine.cn/images/monitor.png",
        "心理量表": "https://www.mograine.cn/images/psychology.png",
        "日常生活指数评估": "https://www.mograine.cn/images/dailyLife.png",
        "知情同意与登记表": "https://www.mograine.cn/images/registration.png"
      };
      return iconMap[type];
    };
    common_vendor.onMounted(() => {
      getPatientDataList();
    });
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.p({
          title: "患者详情数据"
        }),
        b: common_vendor.t(startDate.value || "开始日期"),
        c: common_vendor.p({
          type: "calendar",
          size: "15",
          color: "#666666"
        }),
        d: startDate.value,
        e: common_vendor.o(onStartDateChange),
        f: common_vendor.t(endDate.value || "结束日期"),
        g: common_vendor.p({
          type: "calendar",
          size: "15",
          color: "#666666"
        }),
        h: endDate.value,
        i: common_vendor.o(onEndDateChange),
        j: common_vendor.p({
          type: "reload",
          size: "18",
          color: "#07C160"
        }),
        k: common_vendor.o(resetDateFilter),
        l: common_vendor.t(selectedTypeIndex.value === -1 ? "全部类型" : dataTypes.value[selectedTypeIndex.value].label),
        m: common_vendor.p({
          type: "down",
          size: "15",
          color: "#666666"
        }),
        n: selectedTypeIndex.value,
        o: dataTypes.value,
        p: common_vendor.o(onDataTypeChange),
        q: common_vendor.p({
          type: "search",
          size: "16",
          color: "#FFFFFF"
        }),
        r: common_vendor.o(searchByDate),
        s: loadingPatientData.value
      }, loadingPatientData.value ? {} : patientDataList.value.length === 0 ? {
        v: common_vendor.p({
          text: "暂无记录"
        })
      } : {
        w: common_vendor.f(patientDataList.value, (item, index, i0) => {
          return {
            a: item.iconPath,
            b: common_vendor.t(item.type),
            c: common_vendor.t(item.userName),
            d: common_vendor.t(item.createTime),
            e: "d014d19c-10-" + i0 + ",d014d19c-1",
            f: index,
            g: common_vendor.o(($event) => viewDataDetail(item), index)
          };
        }),
        x: common_vendor.p({
          type: "right",
          size: "16",
          color: "#07C160"
        })
      }, {
        t: patientDataList.value.length === 0,
        y: common_vendor.sr(calendar, "d014d19c-11,d014d19c-0", {
          "k": "calendar"
        }),
        z: common_vendor.o(dateConfirm),
        A: common_vendor.p({
          insert: false,
          range: false
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d014d19c"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=detail.js.map
