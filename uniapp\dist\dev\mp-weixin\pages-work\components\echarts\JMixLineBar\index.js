"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
const common_vendor = require("../../../../common/vendor.js");
const pagesWork_components_echarts_props = require("../props.js");
const pagesWork_components_common_echartUtil = require("../../common/echartUtil.js");
const pagesWork_components_hooks_useEchart = require("../../hooks/useEchart.js");
const uni_modules_daTree_utils = require("../../../../uni_modules/da-tree/utils.js");
if (!Math) {
  (statusTip + echartsUniapp)();
}
const echartsUniapp = () => "../index.js";
const statusTip = () => "../../statusTip.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  props: __spreadValues({}, pagesWork_components_echarts_props.echartProps),
  setup(__props) {
    const props = __props;
    const option = common_vendor.ref({});
    let chartOption = {
      title: {
        show: true
      },
      legend: {
        show: true,
        data: []
      },
      xAxis: {
        type: "category",
        axisLabel: {
          formatter: function(value, index) {
            return value;
          }
        }
      },
      yAxis: {
        type: "value",
        nameTextStyle: {
          align: "right"
        },
        axisLabel: {
          formatter: function(value, index) {
            return value;
          }
        },
        axisLine: {
          show: true
        }
      },
      series: [],
      dataset: {
        dimensions: [],
        source: []
      }
    };
    let [{ dataSource, reload, pageTips, config }, { queryData }] = pagesWork_components_hooks_useEchart.useChartHook(
      props,
      initOption
    );
    function initOption(data) {
      var _a, _b;
      let chartData = dataSource.value;
      if (typeof chartData === "string") {
        chartData = JSON.parse(chartData);
      }
      initSeriesType(chartData);
      if (chartData && chartData.length > 0) {
        const colors = pagesWork_components_common_echartUtil.getCustomColor((_a = config.option) == null ? void 0 : _a.customColor);
        let configOption = props.config.option;
        let dataset = getDataSet(chartData);
        chartOption.dataset = dataset;
        chartOption.series = [];
        dataset.dimensions.forEach((series, index) => {
          var _a2, _b2;
          if (index > 0) {
            let seriesType = props.config.seriesType.filter((item) => item.series == series);
            chartOption.series.push({
              type: seriesType && seriesType.length > 0 ? seriesType[0]["type"] : "bar",
              color: ((_a2 = colors[index - 1]) == null ? void 0 : _a2.color) ? (_b2 = colors[index - 1]) == null ? void 0 : _b2.color : "",
              series
            });
          }
        });
        chartOption.legend.data = chartOption.series.map((item) => item.series).filter((type) => type);
        chartOption.yAxis.type = common_vendor.pull(["value", "category"], (_b = configOption == null ? void 0 : configOption.xAxis) == null ? void 0 : _b.type)[0];
        if (chartOption.yAxis.type == "category") {
          chartOption.yAxis.average = dataset.average;
        } else {
          chartOption.xAxis.average = dataset.average;
        }
        if (props.config && config.option) {
          common_vendor.merge(chartOption, config.option);
          chartOption = pagesWork_components_common_echartUtil.handleTotalAndUnit(props.compName, chartOption, config, chartData);
          chartOption = pagesWork_components_common_echartUtil.disposeGridLayout(props.compName, chartOption);
          option.value = uni_modules_daTree_utils.deepClone(chartOption);
          pageTips.show = false;
        }
      } else {
        pageTips.status = 1;
        pageTips.show = true;
      }
    }
    function initSeriesType(chartData) {
      let seriesArr = [...new Set(chartData.map((item) => item["type"]))];
      let configSeriesArr = props.config.seriesType || [];
      let oldSeriesArr = [...new Set(configSeriesArr.map((item) => item["series"]))];
      if (!isArrayEqual(seriesArr, oldSeriesArr)) {
        let newSeriesType = seriesArr.map((series) => {
          return { series, type: "bar" };
        });
        props.config.seriesType = newSeriesType;
      }
    }
    function getDataSet(chartData) {
      let dataObj = { dimensions: [], source: [], average: 0 };
      let dataList = [];
      let dimensions = ["stack", ...new Set(chartData.map((item) => item["type"]))];
      let nameArr = [...new Set(chartData.map((item) => item["name"]))];
      nameArr.forEach((name) => {
        let arr = chartData.filter((item) => item["name"] == name);
        let valueList = arr.map((item) => item["value"]);
        valueList.unshift(name);
        dataList.push(valueList);
      });
      dataObj.dimensions = dimensions;
      dataObj.source = dataList;
      let allValue = chartData.filter((chart) => chart.value > 0).map((item) => item["value"]);
      dataObj.average = allValue.length > 0 ? allValue.reduce((a, b) => a + b) / allValue.length : 0;
      return dataObj;
    }
    function isArrayEqual(arr1, arr2) {
      const a1 = arr1.map((i) => i);
      let a2 = arr2.map((i) => i);
      let tempArr = [];
      if (a1.length !== a2.length) {
        return false;
      } else {
        for (let i = 0; i < a1.length; i++) {
          if (a2.indexOf(a1[i]) !== -1) {
            a2.splice(a2.indexOf(a1[i]), 1);
            tempArr.push(a1[i]);
          } else {
            tempArr = [];
            break;
          }
        }
        return tempArr.length === arr2.length;
      }
    }
    common_vendor.onMounted(() => {
      queryData();
    });
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.unref(pageTips).show
      }, common_vendor.unref(pageTips).show ? {
        b: common_vendor.p({
          status: common_vendor.unref(pageTips).status
        })
      } : {
        c: common_vendor.p({
          option: common_vendor.unref(option)
        })
      });
    };
  }
});
wx.createComponent(_sfc_main);
//# sourceMappingURL=index.js.map
