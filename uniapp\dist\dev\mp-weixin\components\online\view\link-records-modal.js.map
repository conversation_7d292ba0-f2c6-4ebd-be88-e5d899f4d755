{"version": 3, "file": "link-records-modal.js", "sources": ["../../../../../../src/components/online/view/link-records-modal.vue", "../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvY29tcG9uZW50cy9vbmxpbmUvdmlldy9saW5rLXJlY29yZHMtbW9kYWwudnVl"], "sourcesContent": ["<template>\r\n  <wd-popup position=\"bottom\" v-model=\"show\">\r\n    <PageLayout\r\n      :navTitle=\"navTitle\"\r\n      type=\"popup\"\r\n      navRightText=\"确定\"\r\n      @navRight=\"handleConfirm\"\r\n      @navBack=\"handleCancel\"\r\n    >\r\n      <z-paging\r\n        ref=\"paging\"\r\n        :fixed=\"false\"\r\n        v-model=\"dataList\"\r\n        @query=\"queryList\"\r\n        :default-page-size=\"15\"\r\n      >\r\n<!--        <template #top>-->\r\n<!--          <wd-search-->\r\n<!--            hide-cancel-->\r\n<!--            :placeholder=\"search.placeholder\"-->\r\n<!--            v-model=\"search.keyword\"-->\r\n<!--            @search=\"handleSearch\"-->\r\n<!--            @clear=\"handleClear\"-->\r\n<!--          />-->\r\n<!--        </template>-->\r\n        <template v-if=\"multi\">\r\n          <wd-checkbox-group shape=\"square\" v-model=\"checkedValue\">\r\n            <template v-for=\"(item, index) in dataList\" :key=\"index\">\r\n              <view class=\"list\" @click=\"hanldeCheck(index)\">\r\n                <view class=\"left text-gray-5\">\r\n                  <view class=\"cu-avatar lg mr-4\" v-if=\"imageField && item[imageField]\" :style=\"[{backgroundImage:'url('+ (item[imageField]) +')'}]\"></view>\r\n                  <view class=\"field-content\">\r\n                    <template v-for=\"(cItem, cIndex) in columns\" :key=\"cIndex\">\r\n                      <view class=\"row\">\r\n                        <text class=\"label\">{{ cItem.title }}：</text>\r\n                        <text class=\"value\">{{ item[cItem.dataIndex] }}</text>\r\n                      </view>\r\n                    </template>\r\n                  </view>\r\n                </view>\r\n                <view class=\"right\" @click.stop>\r\n                  <wd-checkbox ref=\"checkboxRef\" :modelValue=\"index\"></wd-checkbox>\r\n                </view>\r\n              </view>\r\n            </template>\r\n          </wd-checkbox-group>\r\n        </template>\r\n        <template v-else>\r\n          <wd-radio-group shape=\"dot\" v-model=\"checkedValue\">\r\n            <template v-for=\"(item, index) in dataList\" :key=\"index\">\r\n              <wd-cell>\r\n                <view class=\"list\" @click=\"hanldeCheck(index)\">\r\n                  <view class=\"left text-gray-5\">\r\n                    <view class=\"cu-avatar lg mr-4\" v-if=\"imageField && item[imageField]\" :style=\"[{backgroundImage:'url('+ (item[imageField]) +')'}]\"></view>\r\n                    <view class=\"field-content\">\r\n                      <template v-for=\"(cItem, cIndex) in columns\" :key=\"cIndex\">\r\n                        <view class=\"row\">\r\n                          <text class=\"label\">{{ cItem.title }}：</text>\r\n                          <text class=\"value\">{{ item[cItem.dataIndex] }}</text>\r\n                        </view>\r\n                      </template>\r\n                    </view>\r\n                  </view>\r\n                  <view class=\"right\" @click.stop>\r\n                    <wd-radio :value=\"index\"></wd-radio>\r\n                  </view>\r\n                </view>\r\n              </wd-cell>\r\n            </template>\r\n          </wd-radio-group>\r\n        </template>\r\n      </z-paging>\r\n    </PageLayout>\r\n  </wd-popup>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, reactive } from 'vue'\r\nimport { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'\r\nimport { http } from '@/utils/http'\r\nimport { isArray } from '@/utils/is'\r\nimport { getFileAccessHttpUrl } from '@/common/uitls'\r\ndefineOptions({\r\n  name: 'popupReportModal',\r\n  options: {\r\n    styleIsolation: 'shared',\r\n  },\r\n})\r\nconst props = defineProps({\r\n  dictTable:{\r\n    type: String,\r\n    required:true,\r\n  },\r\n  dictCode:{\r\n    type:String,\r\n    required:true,\r\n  },\r\n  dictText:{\r\n    type: String,\r\n    required:true,\r\n  },\r\n  multi:{\r\n    type: Boolean,\r\n    required: false,\r\n  },\r\n  imageField:{\r\n    type: String,\r\n    required:false,\r\n  }\r\n})\r\nconst emit = defineEmits(['change', 'close'])\r\nconst toast = useToast();\r\nconst show = ref(true);\r\nconst api = {\r\n  getColumns: '/online/cgform/api/getColumns',\r\n  getData: '/online/cgform/api/getData'\r\n}\r\nconsole.log('props:::', props)\r\nconst navTitle = ref('');\r\nconst paging = ref(null);\r\nconst dataList = ref([]);\r\n// 报表id\r\nlet rpConfigId = null;\r\nlet loadedColumns = false;\r\nconst columns = ref([]);\r\nconst selectArr = ref([]);\r\nconst checkedValue: any = ref(props.multi ? [] : '')\r\nconst checkboxRef = ref(null)\r\nconst search = reactive({\r\n  keyword: '',\r\n  placeholder: '',\r\n  field: '',\r\n})\r\n\r\nconst handleClose = () => {\r\n  setTimeout(() => {\r\n    emit('close')\r\n  }, 400)\r\n}\r\n\r\nconst beforeOpen = (arr) => {\r\n  selectArr.value = arr || []\r\n}\r\nconst handleConfirm = () => {\r\n  if (checkedValue.value.length == 0) {\r\n    toast.warning('还没选择~')\r\n    return\r\n  }\r\n  const result = []\r\n  let value = checkedValue.value\r\n  if (!Array.isArray(checkedValue.value)) {\r\n    value = [checkedValue.value]\r\n  }\r\n  value.forEach((index) => {\r\n    result.push(dataList.value[index])\r\n  })\r\n  show.value = false\r\n  emit('change', result)\r\n  handleClose()\r\n}\r\nconst handleCancel = () => {\r\n  show.value = false\r\n  handleClose()\r\n  console.log('取消了~')\r\n}\r\n// 搜索\r\nfunction handleSearch() {\r\n  paging.value.reload()\r\n}\r\n// 清除搜索条件\r\nfunction handleClear() {\r\n  search.keyword = ''\r\n  handleSearch()\r\n}\r\nconst hanldeCheck = (index) => {\r\n  if (props.multi) {\r\n    if (Array.isArray(checkboxRef.value)) {\r\n      checkboxRef.value[index].toggle()\r\n    }\r\n  } else {\r\n    checkedValue.value = index\r\n  }\r\n}\r\n\r\nconst getRpColumns = () => {\r\n  return new Promise<void>((resolve, reject) => {\r\n    if (loadedColumns) {\r\n      resolve()\r\n    } else {\r\n      let linkTableSelectFields = props.dictCode + ',' + props.dictText;\r\n      http\r\n        .get(`${api.getColumns}/${props.dictTable}?linkTableSelectFields=${linkTableSelectFields}`)\r\n        .then((res: any) => {\r\n          if (res.success) {\r\n            loadedColumns = true\r\n            const { result } = res\r\n            navTitle.value = result.description\r\n            rpConfigId = result.code\r\n            result.columns?.forEach((item) => {\r\n              if (linkTableSelectFields.includes(item.dataIndex)) {\r\n                columns.value.push(item)\r\n              }\r\n            })\r\n            resolve()\r\n          } else {\r\n            reject()\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          reject()\r\n        })\r\n    }\r\n  })\r\n}\r\n\r\nconst queryList = (pageNo, pageSize) => {\r\n  const pararms = { pageNo, pageSize,linkTableSelectFields:\"\" }\r\n  if (search.keyword) {\r\n    pararms[search.field] = `*${search.keyword}*`\r\n  }\r\n  getRpColumns()\r\n    .then(() => {\r\n      let linkTableSelectFields = props.dictCode + ',' + props.dictText;\r\n      if(props.imageField){\r\n        linkTableSelectFields = linkTableSelectFields + ',' + props.imageField;\r\n      }\r\n      pararms.linkTableSelectFields = linkTableSelectFields;\r\n      http\r\n        .get(`${api.getData}/${props.dictTable}`, pararms)\r\n        .then((res: any) => {\r\n          if (res.success && res.result.records) {\r\n            let dataRecords = res.result.records;\r\n            if(dataRecords && dataRecords.length>0){\r\n              let id = dataRecords[0]['id'];\r\n              for(let item of dataRecords){\r\n                if(!id){\r\n                  item.id = new Date().getTime();\r\n                }\r\n                if(props.imageField && item[props.imageField]){\r\n                  let imgUrlArr = item[props.imageField].split(\",\");\r\n                  item[props.imageField] = imgUrlArr.length>0?getFileAccessHttpUrl(imgUrlArr[0]):\"\";\r\n                }\r\n              }\r\n            }\r\n            //TODO\r\n            if(selectArr.value && isArray(selectArr) && selectArr.length>0){\r\n              //checkedValue.value = [...selectArr]\r\n            }\r\n            paging.value.complete(dataRecords ?? [])\r\n          } else {\r\n            paging.value.complete(false)\r\n          }\r\n        })\r\n        .catch((err) => {})\r\n    })\r\n    .catch((err) => {})\r\n}\r\ndefineExpose({\r\n  beforeOpen\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n:deep(.wd-cell) {\r\n  --wot-color-white: tranparent;\r\n  --wot-cell-padding: 0;\r\n  .wd-cell__wrapper {\r\n    --wot-cell-wrapper-padding: 0;\r\n  }\r\n  .wd-cell__left {\r\n    display: none;\r\n  }\r\n}\r\n:deep(.wd-checkbox-group) {\r\n  --wot-checkbox-bg: tranparent;\r\n}\r\n:deep(.wd-radio-group) {\r\n  --wot-radio-bg: tranparent;\r\n}\r\n.list {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  background: #fff;\r\n  padding: 16px;\r\n  margin-top: 16px;\r\n  .left {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n   .field-content{\r\n     .row {\r\n       display: flex;\r\n     }\r\n   }\r\n  }\r\n  .right {\r\n    :deep(.wd-checkbox) {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/components/online/view/link-records-modal.vue'\nwx.createComponent(Component)"], "names": ["useToast", "ref", "reactive", "http", "getFileAccessHttpUrl", "isArray"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwFA,UAAM,QAAQ;AAsBd,UAAM,OAAO;AACb,UAAM,QAAQA,cAAAA,SAAS;AACjB,UAAA,OAAOC,kBAAI,IAAI;AACrB,UAAM,MAAM;AAAA,MACV,YAAY;AAAA,MACZ,SAAS;AAAA,IACX;AACQ,YAAA,IAAI,YAAY,KAAK;AACvB,UAAA,WAAWA,kBAAI,EAAE;AACjB,UAAA,SAASA,kBAAI,IAAI;AACjB,UAAA,WAAWA,cAAI,IAAA,EAAE;AAGvB,QAAI,gBAAgB;AACd,UAAA,UAAUA,cAAI,IAAA,EAAE;AAChB,UAAA,YAAYA,cAAI,IAAA,EAAE;AACxB,UAAM,eAAoBA,cAAAA,IAAI,MAAM,QAAQ,CAAA,IAAK,EAAE;AAC7C,UAAA,cAAcA,kBAAI,IAAI;AAC5B,UAAM,SAASC,cAAAA,SAAS;AAAA,MACtB,SAAS;AAAA,MACT,aAAa;AAAA,MACb,OAAO;AAAA,IAAA,CACR;AAED,UAAM,cAAc,MAAM;AACxB,iBAAW,MAAM;AACf,aAAK,OAAO;AAAA,SACX,GAAG;AAAA,IACR;AAEM,UAAA,aAAa,CAAC,QAAQ;AAChB,gBAAA,QAAQ,OAAO,CAAC;AAAA,IAC5B;AACA,UAAM,gBAAgB,MAAM;AACtB,UAAA,aAAa,MAAM,UAAU,GAAG;AAClC,cAAM,QAAQ,OAAO;AACrB;AAAA,MAAA;AAEF,YAAM,SAAS,CAAC;AAChB,UAAI,QAAQ,aAAa;AACzB,UAAI,CAAC,MAAM,QAAQ,aAAa,KAAK,GAAG;AAC9B,gBAAA,CAAC,aAAa,KAAK;AAAA,MAAA;AAEvB,YAAA,QAAQ,CAAC,UAAU;AACvB,eAAO,KAAK,SAAS,MAAM,KAAK,CAAC;AAAA,MAAA,CAClC;AACD,WAAK,QAAQ;AACb,WAAK,UAAU,MAAM;AACT,kBAAA;AAAA,IACd;AACA,UAAM,eAAe,MAAM;AACzB,WAAK,QAAQ;AACD,kBAAA;AACZ,cAAQ,IAAI,MAAM;AAAA,IACpB;AAUM,UAAA,cAAc,CAAC,UAAU;AAC7B,UAAI,MAAM,OAAO;AACf,YAAI,MAAM,QAAQ,YAAY,KAAK,GAAG;AACxB,sBAAA,MAAM,KAAK,EAAE,OAAO;AAAA,QAAA;AAAA,MAClC,OACK;AACL,qBAAa,QAAQ;AAAA,MAAA;AAAA,IAEzB;AAEA,UAAM,eAAe,MAAM;AACzB,aAAO,IAAI,QAAc,CAAC,SAAS,WAAW;AAC5C,YAAI,eAAe;AACT,kBAAA;AAAA,QAAA,OACH;AACL,cAAI,wBAAwB,MAAM,WAAW,MAAM,MAAM;AACzDC,qBAAAA,KACG,IAAI,GAAG,IAAI,UAAU,IAAI,MAAM,SAAS,0BAA0B,qBAAqB,EAAE,EACzF,KAAK,CAAC,QAAa;;AAClB,gBAAI,IAAI,SAAS;AACC,8BAAA;AACV,oBAAA,EAAE,WAAW;AACnB,uBAAS,QAAQ,OAAO;AACX,qBAAO;AACb,2BAAA,YAAA,mBAAS,QAAQ,CAAC,SAAS;AAChC,oBAAI,sBAAsB,SAAS,KAAK,SAAS,GAAG;AAC1C,0BAAA,MAAM,KAAK,IAAI;AAAA,gBAAA;AAAA,cACzB;AAEM,sBAAA;AAAA,YAAA,OACH;AACE,qBAAA;AAAA,YAAA;AAAA,UACT,CACD,EACA,MAAM,CAAC,QAAQ;AACP,mBAAA;AAAA,UAAA,CACR;AAAA,QAAA;AAAA,MACL,CACD;AAAA,IACH;AAEM,UAAA,YAAY,CAAC,QAAQ,aAAa;AACtC,YAAM,UAAU,EAAE,QAAQ,UAAS,uBAAsB,GAAG;AAC5D,UAAI,OAAO,SAAS;AAClB,gBAAQ,OAAO,KAAK,IAAI,IAAI,OAAO,OAAO;AAAA,MAAA;AAE/B,mBAAA,EACV,KAAK,MAAM;AACV,YAAI,wBAAwB,MAAM,WAAW,MAAM,MAAM;AACzD,YAAG,MAAM,YAAW;AACM,kCAAA,wBAAwB,MAAM,MAAM;AAAA,QAAA;AAE9D,gBAAQ,wBAAwB;AAChCA,mBAAAA,KACG,IAAI,GAAG,IAAI,OAAO,IAAI,MAAM,SAAS,IAAI,OAAO,EAChD,KAAK,CAAC,QAAa;AAClB,cAAI,IAAI,WAAW,IAAI,OAAO,SAAS;AACjC,gBAAA,cAAc,IAAI,OAAO;AAC1B,gBAAA,eAAe,YAAY,SAAO,GAAE;AACrC,kBAAI,KAAK,YAAY,CAAC,EAAE,IAAI;AAC5B,uBAAQ,QAAQ,aAAY;AAC1B,oBAAG,CAAC,IAAG;AACL,uBAAK,MAAK,oBAAI,KAAK,GAAE,QAAQ;AAAA,gBAAA;AAE/B,oBAAG,MAAM,cAAc,KAAK,MAAM,UAAU,GAAE;AAC5C,sBAAI,YAAY,KAAK,MAAM,UAAU,EAAE,MAAM,GAAG;AAC3C,uBAAA,MAAM,UAAU,IAAI,UAAU,SAAO,IAAEC,kCAAqB,UAAU,CAAC,CAAC,IAAE;AAAA,gBAAA;AAAA,cACjF;AAAA,YACF;AAGF,gBAAG,UAAU,SAASC,SAAA,QAAQ,SAAS,KAAK,UAAU,SAAO;AAAE;AAG/D,mBAAO,MAAM,SAAS,oCAAe,CAAA,CAAE;AAAA,UAAA,OAClC;AACE,mBAAA,MAAM,SAAS,KAAK;AAAA,UAAA;AAAA,QAC7B,CACD,EACA,MAAM,CAAC,QAAQ;AAAA,QAAA,CAAE;AAAA,MAAA,CACrB,EACA,MAAM,CAAC,QAAQ;AAAA,MAAA,CAAE;AAAA,IACtB;AACa,aAAA;AAAA,MACX;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClQD,GAAG,gBAAgB,SAAS;"}