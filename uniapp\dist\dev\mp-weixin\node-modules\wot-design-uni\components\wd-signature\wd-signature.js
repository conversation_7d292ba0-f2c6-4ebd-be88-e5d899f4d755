"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../../common/vendor.js");
if (!Array) {
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  _easycom_wd_button2();
}
const _easycom_wd_button = () => "../wd-button/wd-button.js";
if (!Math) {
  _easycom_wd_button();
}
const __default__ = {
  name: "wd-signature",
  options: {
    addGlobalClass: true,
    virtualHost: true,
    styleIsolation: "shared"
  }
};
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, __default__), {
  props: common_vendor.signatureProps,
  emits: ["start", "end", "signing", "confirm", "clear"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const { translate } = common_vendor.useTranslate("signature");
    const { proxy } = common_vendor.getCurrentInstance();
    const canvasId = common_vendor.ref(`signature${common_vendor.uuid()}`);
    let canvas = null;
    const drawing = common_vendor.ref(false);
    const pixelRatio = common_vendor.ref(1);
    const canvasState = common_vendor.reactive({
      canvasWidth: 0,
      canvasHeight: 0,
      ctx: null
      // canvas上下文
    });
    common_vendor.watch(
      () => props.penColor,
      () => {
        setLine();
      }
    );
    common_vendor.watch(
      () => props.lineWidth,
      () => {
        setLine();
      }
    );
    const canvasStyle = common_vendor.computed(() => {
      const style = {};
      if (common_vendor.isDef(props.width)) {
        style.width = common_vendor.addUnit(props.width);
      }
      if (common_vendor.isDef(props.height)) {
        style.height = common_vendor.addUnit(props.height);
      }
      return `${common_vendor.objToStyle(style)}`;
    });
    const disableScroll = common_vendor.computed(() => props.disableScroll);
    const enableHistory = common_vendor.computed(() => props.enableHistory);
    const lines = common_vendor.ref([]);
    const redoLines = common_vendor.ref([]);
    const currentLine = common_vendor.ref();
    const currentStep = common_vendor.ref(0);
    function calculateLineWidth(speed) {
      if (!props.pressure)
        return props.lineWidth;
      const minSpeed = props.minSpeed || 1.5;
      const limitedSpeed = Math.min(minSpeed * 10, Math.max(minSpeed, speed));
      const addWidth = (props.maxWidth - props.minWidth) * (limitedSpeed - minSpeed) / minSpeed;
      const lineWidth = Math.max(props.maxWidth - addWidth, props.minWidth);
      return Math.min(lineWidth, props.maxWidth);
    }
    const getDefaultLineWidth = () => {
      if (props.pressure) {
        return (props.maxWidth + props.minWidth) / 2;
      }
      return props.lineWidth;
    };
    const startDrawing = (e) => {
      e.preventDefault();
      drawing.value = true;
      setLine();
      emit("start", e);
      const { x, y } = e.touches[0];
      currentLine.value = {
        points: [
          {
            x,
            y,
            t: Date.now()
            // 使用 t 替换 width
          }
        ],
        color: props.penColor,
        width: getDefaultLineWidth(),
        backgroundColor: props.backgroundColor,
        isPressure: props.pressure
        // 添加笔锋模式标记
      };
      redoLines.value = [];
      draw(e);
    };
    const stopDrawing = (e) => {
      e.preventDefault();
      drawing.value = false;
      if (currentLine.value) {
        lines.value.push(__spreadProps(__spreadValues({}, currentLine.value), {
          points: currentLine.value.points.map((point) => __spreadProps(__spreadValues({}, point), {
            t: point.t,
            speed: point.speed,
            distance: point.distance,
            lineWidth: point.lineWidth,
            lastX1: point.lastX1,
            lastY1: point.lastY1,
            lastX2: point.lastX2,
            lastY2: point.lastY2,
            isFirstPoint: point.isFirstPoint
          }))
        }));
        currentStep.value = lines.value.length;
      }
      currentLine.value = void 0;
      const { ctx } = canvasState;
      if (ctx)
        ctx.beginPath();
      emit("end", e);
    };
    const initCanvas = (forceUpdate = false) => {
      if (!forceUpdate && canvasState.canvasHeight && canvasState.canvasWidth) {
        return;
      }
      getContext().then(() => {
        const { ctx } = canvasState;
        if (ctx && common_vendor.isDef(props.backgroundColor)) {
          ctx.setFillStyle(props.backgroundColor);
          ctx.fillRect(0, 0, canvasState.canvasWidth, canvasState.canvasHeight);
          ctx.draw();
        }
      });
    };
    const clear = () => {
      lines.value = [];
      redoLines.value = [];
      currentStep.value = 0;
      clearCanvas();
      emit("clear");
    };
    const confirmSignature = () => {
      canvasToImage();
    };
    const draw = (e) => {
      e.preventDefault();
      const { ctx } = canvasState;
      if (!drawing.value || props.disabled || !ctx)
        return;
      const { x, y } = e.touches[0];
      const point = {
        x,
        y,
        t: Date.now()
      };
      if (currentLine.value) {
        const points = currentLine.value.points;
        const prePoint = points[points.length - 1];
        if (prePoint.t === point.t || prePoint.x === x && prePoint.y === y) {
          return;
        }
        point.distance = Math.sqrt(Math.pow(point.x - prePoint.x, 2) + Math.pow(point.y - prePoint.y, 2));
        point.speed = point.distance / (point.t - prePoint.t || 0.1);
        if (props.pressure) {
          point.lineWidth = calculateLineWidth(point.speed);
          if (points.length >= 2) {
            const prePoint2 = points[points.length - 2];
            if (prePoint2.lineWidth && prePoint.lineWidth) {
              const rate = (point.lineWidth - prePoint.lineWidth) / prePoint.lineWidth;
              const maxRate = 0.2;
              if (Math.abs(rate) > maxRate) {
                const per = rate > 0 ? maxRate : -0.2;
                point.lineWidth = prePoint.lineWidth * (1 + per);
              }
            }
          }
        }
        points.push(point);
        if (!props.pressure) {
          ctx.beginPath();
          ctx.moveTo(prePoint.x, prePoint.y);
          ctx.lineTo(point.x, point.y);
          ctx.stroke();
          ctx.draw(true);
        } else if (points.length >= 2) {
          drawSmoothLine(prePoint, point);
        }
      }
      emit("signing", e);
    };
    const redrawCanvas = () => {
      const { ctx } = canvasState;
      if (!ctx)
        return;
      if (common_vendor.isDef(props.backgroundColor)) {
        ctx.setFillStyle(props.backgroundColor);
        ctx.fillRect(0, 0, canvasState.canvasWidth, canvasState.canvasHeight);
      } else {
        ctx.clearRect(0, 0, canvasState.canvasWidth, canvasState.canvasHeight);
      }
      if (lines.value.length === 0) {
        ctx.draw();
        return;
      }
      lines.value.forEach((line) => {
        if (!line.points.length)
          return;
        ctx.setStrokeStyle(line.color);
        ctx.setLineJoin("round");
        ctx.setLineCap("round");
        if (line.isPressure && props.pressure) {
          line.points.forEach((point, index) => {
            if (index === 0)
              return;
            const prePoint = line.points[index - 1];
            const dis_x = point.x - prePoint.x;
            const dis_y = point.y - prePoint.y;
            const distance = Math.sqrt(dis_x * dis_x + dis_y * dis_y);
            if (distance <= 2) {
              point.lastX1 = point.lastX2 = prePoint.x + dis_x * 0.5;
              point.lastY1 = point.lastY2 = prePoint.y + dis_y * 0.5;
            } else {
              const speed = point.speed || 0;
              const minSpeed = props.minSpeed || 1.5;
              const speedFactor = Math.max(0.1, Math.min(0.9, speed / (minSpeed * 10)));
              point.lastX1 = prePoint.x + dis_x * (0.2 + speedFactor * 0.3);
              point.lastY1 = prePoint.y + dis_y * (0.2 + speedFactor * 0.3);
              point.lastX2 = prePoint.x + dis_x * (0.8 - speedFactor * 0.3);
              point.lastY2 = prePoint.y + dis_y * (0.8 - speedFactor * 0.3);
            }
            const lineWidth = point.lineWidth || line.width;
            if (typeof prePoint.lastX1 === "number") {
              ctx.setLineWidth(lineWidth);
              ctx.beginPath();
              ctx.moveTo(prePoint.lastX2, prePoint.lastY2);
              ctx.quadraticCurveTo(prePoint.x, prePoint.y, point.lastX1, point.lastY1);
              ctx.stroke();
              if (!prePoint.isFirstPoint) {
                ctx.beginPath();
                ctx.moveTo(prePoint.lastX1, prePoint.lastY1);
                ctx.quadraticCurveTo(prePoint.x, prePoint.y, prePoint.lastX2, prePoint.lastY2);
                ctx.stroke();
              }
            } else {
              point.isFirstPoint = true;
            }
          });
        } else {
          ctx.setLineWidth(line.width);
          line.points.forEach((point, index) => {
            if (index === 0)
              return;
            const prePoint = line.points[index - 1];
            ctx.beginPath();
            ctx.moveTo(prePoint.x, prePoint.y);
            ctx.lineTo(point.x, point.y);
            ctx.stroke();
          });
        }
      });
      ctx.draw();
    };
    const revoke = () => {
      if (!lines.value.length)
        return;
      const step = Math.min(props.step, lines.value.length);
      const removedLines = lines.value.splice(lines.value.length - step);
      redoLines.value.push(...removedLines);
      currentStep.value = Math.max(0, currentStep.value - step);
      redrawCanvas();
    };
    const restore = () => {
      if (!redoLines.value.length)
        return;
      const step = Math.min(props.step, redoLines.value.length);
      const restoredLines = redoLines.value.splice(redoLines.value.length - step);
      lines.value.push(...restoredLines);
      currentStep.value = Math.min(lines.value.length, currentStep.value + step);
      redrawCanvas();
    };
    function drawSmoothLine(prePoint, point) {
      const { ctx } = canvasState;
      if (!ctx)
        return;
      const dis_x = point.x - prePoint.x;
      const dis_y = point.y - prePoint.y;
      const distance = Math.sqrt(dis_x * dis_x + dis_y * dis_y);
      if (distance <= 2) {
        point.lastX1 = point.lastX2 = prePoint.x + dis_x * 0.5;
        point.lastY1 = point.lastY2 = prePoint.y + dis_y * 0.5;
      } else {
        const speed = point.speed || 0;
        const minSpeed = props.minSpeed || 1.5;
        const speedFactor = Math.max(0.1, Math.min(0.9, speed / (minSpeed * 10)));
        point.lastX1 = prePoint.x + dis_x * (0.2 + speedFactor * 0.3);
        point.lastY1 = prePoint.y + dis_y * (0.2 + speedFactor * 0.3);
        point.lastX2 = prePoint.x + dis_x * (0.8 - speedFactor * 0.3);
        point.lastY2 = prePoint.y + dis_y * (0.8 - speedFactor * 0.3);
      }
      const lineWidth = point.lineWidth || props.lineWidth;
      if (typeof prePoint.lastX1 === "number") {
        ctx.setLineWidth(lineWidth);
        ctx.beginPath();
        ctx.moveTo(prePoint.lastX2, prePoint.lastY2);
        ctx.quadraticCurveTo(prePoint.x, prePoint.y, point.lastX1, point.lastY1);
        ctx.stroke();
        if (!prePoint.isFirstPoint) {
          ctx.beginPath();
          ctx.moveTo(prePoint.lastX1, prePoint.lastY1);
          ctx.quadraticCurveTo(prePoint.x, prePoint.y, prePoint.lastX2, prePoint.lastY2);
          ctx.stroke();
        }
        ctx.draw(true);
      } else {
        point.isFirstPoint = true;
      }
    }
    common_vendor.onMounted(() => {
      initCanvas();
    });
    common_vendor.onBeforeMount(() => {
      pixelRatio.value = common_vendor.index.getSystemInfoSync().pixelRatio;
    });
    function getContext() {
      return new Promise((resolve) => {
        const { ctx } = canvasState;
        if (ctx) {
          return resolve(ctx);
        }
        common_vendor.getRect(`#${canvasId.value}`, false, proxy, true).then((canvasRect) => {
          if (canvasRect && canvasRect.node && canvasRect.width && canvasRect.height) {
            const canvasInstance = canvasRect.node;
            canvasState.ctx = common_vendor.canvas2dAdapter(canvasInstance.getContext("2d"));
            canvasInstance.width = canvasRect.width * pixelRatio.value;
            canvasInstance.height = canvasRect.height * pixelRatio.value;
            canvasState.ctx.scale(pixelRatio.value, pixelRatio.value);
            canvas = canvasInstance;
            setcanvasState(canvasRect.width, canvasRect.height);
            resolve(canvasState.ctx);
          }
        });
      });
    }
    function setcanvasState(width, height) {
      canvasState.canvasHeight = height * pixelRatio.value;
      canvasState.canvasWidth = width * pixelRatio.value;
    }
    function setLine() {
      const { ctx } = canvasState;
      if (ctx) {
        ctx.setLineWidth(getDefaultLineWidth());
        ctx.setStrokeStyle(props.penColor);
        ctx.setLineJoin("round");
        ctx.setLineCap("round");
      }
    }
    function canvasToImage() {
      const { fileType, quality, exportScale } = props;
      const { canvasWidth, canvasHeight } = canvasState;
      common_vendor.index.canvasToTempFilePath(
        {
          width: canvasWidth * exportScale,
          height: canvasHeight * exportScale,
          destWidth: canvasWidth * exportScale,
          destHeight: canvasHeight * exportScale,
          fileType,
          quality,
          canvasId: canvasId.value,
          canvas,
          success: (res) => {
            const result = {
              tempFilePath: res.tempFilePath,
              width: canvasWidth * exportScale / pixelRatio.value,
              height: canvasHeight * exportScale / pixelRatio.value,
              success: true
            };
            emit("confirm", result);
          },
          fail: () => {
            const result = {
              tempFilePath: "",
              width: canvasWidth * exportScale / pixelRatio.value,
              height: canvasHeight * exportScale / pixelRatio.value,
              success: false
            };
            emit("confirm", result);
          }
        },
        proxy
      );
    }
    function clearCanvas() {
      const { canvasWidth, canvasHeight, ctx } = canvasState;
      if (ctx) {
        ctx.clearRect(0, 0, canvasWidth, canvasHeight);
        if (common_vendor.isDef(props.backgroundColor)) {
          ctx.setFillStyle(props.backgroundColor);
          ctx.fillRect(0, 0, canvasWidth, canvasHeight);
        }
        ctx.draw();
      }
    }
    __expose({
      init: initCanvas,
      clear,
      confirm: confirmSignature,
      restore,
      revoke
    });
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.s(canvasStyle.value),
        b: canvasState.canvasWidth,
        c: canvasState.canvasHeight,
        d: canvasId.value,
        e: canvasId.value,
        f: disableScroll.value,
        g: common_vendor.o(startDrawing),
        h: common_vendor.o(stopDrawing),
        i: common_vendor.o(draw),
        j: enableHistory.value
      }, enableHistory.value ? {
        k: common_vendor.t(_ctx.revokeText || common_vendor.unref(translate)("revokeText")),
        l: common_vendor.o(revoke),
        m: common_vendor.p({
          size: "small",
          plain: true,
          disabled: lines.value.length <= 0
        }),
        n: common_vendor.t(_ctx.restoreText || common_vendor.unref(translate)("restoreText")),
        o: common_vendor.o(restore),
        p: common_vendor.p({
          size: "small",
          plain: true,
          disabled: redoLines.value.length <= 0
        })
      } : {}, {
        q: common_vendor.t(_ctx.clearText || common_vendor.unref(translate)("clearText")),
        r: common_vendor.o(clear),
        s: common_vendor.p({
          size: "small",
          plain: true
        }),
        t: common_vendor.t(_ctx.confirmText || common_vendor.unref(translate)("confirmText")),
        v: common_vendor.o(confirmSignature),
        w: common_vendor.p({
          size: "small"
        }),
        x: common_vendor.r("footer", {
          clear,
          confirm: confirmSignature,
          currentStep: currentStep.value,
          revoke,
          restore,
          canUndo: lines.value.length > 0,
          canRedo: redoLines.value.length > 0,
          historyList: lines.value
        })
      });
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-5bbd3260"]]);
wx.createComponent(Component);
//# sourceMappingURL=wd-signature.js.map
