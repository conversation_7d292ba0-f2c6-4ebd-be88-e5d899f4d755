"use strict";
const common_vendor = require("../../common/vendor.js");
const router_index = require("../../router/index.js");
require("../../utils/http.js");
if (!Array) {
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  (OnlineLoader + _easycom_PageLayout)();
}
const OnlineLoader = () => "../../components/online/online-loader.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "onlineAdd",
  setup(__props) {
    common_vendor.useToast();
    const tableName = common_vendor.ref("");
    const navTitle = common_vendor.ref("");
    common_vendor.ref("onl_");
    common_vendor.ref("/act/process/extActProcess/startMutilProcess");
    const backRouteName = common_vendor.ref("onlineTable");
    const online = common_vendor.ref(null);
    const initForm = (item) => {
      tableName.value = item.desformCode;
      navTitle.value = `表单【${item.desformName}】发起申请`;
      item.backRouteName && (backRouteName.value = item.backRouteName);
      common_vendor.nextTick$1(() => {
        online.value.loadByTableName(tableName.value);
      });
    };
    const backRoute = () => {
      router_index.router.back();
    };
    const handleSuccess = (id) => {
      callPrevPageMethod();
    };
    const callPrevPageMethod = () => {
      common_vendor.index.$emit("refreshList");
      router_index.router.back();
    };
    common_vendor.onLoad((option) => {
      initForm(option);
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.sr(online, "3ec44d6a-2,3ec44d6a-1", {
          "k": "online"
        }),
        b: common_vendor.o(handleSuccess),
        c: common_vendor.o(backRoute),
        d: common_vendor.p({
          table: common_vendor.unref(tableName),
          title: common_vendor.unref(navTitle),
          ["show-footer"]: true
        }),
        e: common_vendor.p({
          navTitle: common_vendor.unref(navTitle),
          backRouteName: common_vendor.unref(backRouteName)
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-3ec44d6a"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=onlineAdd.js.map
