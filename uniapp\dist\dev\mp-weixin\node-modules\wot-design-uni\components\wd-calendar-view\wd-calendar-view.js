"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../../common/vendor.js");
if (!Math) {
  (yearPanel + MonthPanel)();
}
const yearPanel = () => "./yearPanel/year-panel.js";
const MonthPanel = () => "./monthPanel/month-panel.js";
const __default__ = {
  name: "wd-calendar-view",
  options: {
    addGlobalClass: true,
    virtualHost: true,
    styleIsolation: "shared"
  }
};
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, __default__), {
  props: common_vendor.calendarViewProps,
  emits: ["change", "update:modelValue", "pickstart", "pickend"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const formatDefauleTime = common_vendor.ref([]);
    const yearPanelRef = common_vendor.ref();
    const monthPanelRef = common_vendor.ref();
    common_vendor.watch(
      () => props.defaultTime,
      (newValue) => {
        formatDefauleTime.value = common_vendor.getDefaultTime(newValue);
      },
      {
        deep: true,
        immediate: true
      }
    );
    function scrollIntoView() {
      const panel = getPanel();
      panel.scrollIntoView && panel.scrollIntoView();
    }
    function getPanel() {
      return props.type.indexOf("month") > -1 ? yearPanelRef.value : monthPanelRef.value;
    }
    function handleChange({ value }) {
      emit("update:modelValue", value);
      emit("change", {
        value
      });
    }
    function handlePickStart() {
      emit("pickstart");
    }
    function handlePickEnd() {
      emit("pickend");
    }
    __expose({
      scrollIntoView
    });
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: _ctx.type === "month" || _ctx.type === "monthrange"
      }, _ctx.type === "month" || _ctx.type === "monthrange" ? {
        b: common_vendor.sr(yearPanelRef, "f1970b22-0", {
          "k": "yearPanelRef"
        }),
        c: common_vendor.o(handleChange),
        d: common_vendor.p({
          type: _ctx.type,
          value: _ctx.modelValue,
          ["min-date"]: _ctx.minDate,
          ["max-date"]: _ctx.maxDate,
          formatter: _ctx.formatter,
          ["max-range"]: _ctx.maxRange,
          ["range-prompt"]: _ctx.rangePrompt,
          ["allow-same-day"]: _ctx.allowSameDay,
          ["show-panel-title"]: _ctx.showPanelTitle,
          ["default-time"]: formatDefauleTime.value,
          ["panel-height"]: _ctx.panelHeight
        })
      } : {
        e: common_vendor.sr(monthPanelRef, "f1970b22-1", {
          "k": "monthPanelRef"
        }),
        f: common_vendor.o(handleChange),
        g: common_vendor.o(handlePickStart),
        h: common_vendor.o(handlePickEnd),
        i: common_vendor.p({
          type: _ctx.type,
          value: _ctx.modelValue,
          ["min-date"]: _ctx.minDate,
          ["max-date"]: _ctx.maxDate,
          ["first-day-of-week"]: _ctx.firstDayOfWeek,
          formatter: _ctx.formatter,
          ["max-range"]: _ctx.maxRange,
          ["range-prompt"]: _ctx.rangePrompt,
          ["allow-same-day"]: _ctx.allowSameDay,
          ["show-panel-title"]: _ctx.showPanelTitle,
          ["default-time"]: formatDefauleTime.value,
          ["panel-height"]: _ctx.panelHeight,
          ["immediate-change"]: _ctx.immediateChange,
          ["time-filter"]: _ctx.timeFilter,
          ["hide-second"]: _ctx.hideSecond
        })
      }, {
        j: common_vendor.n(`wd-calendar-view ${_ctx.customClass}`)
      });
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f1970b22"]]);
wx.createComponent(Component);
//# sourceMappingURL=wd-calendar-view.js.map
