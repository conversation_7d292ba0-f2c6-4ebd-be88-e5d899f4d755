{"version": 3, "file": "upload.js", "sources": ["../../../../../../src/pages/about/components/upload.vue", "../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvcGFnZXMvYWJvdXQvY29tcG9uZW50cy91cGxvYWQudnVl"], "sourcesContent": ["\r\n\r\n<template>\r\n  <view class=\"p-4 text-center\">\r\n    <wd-button @click=\"run\">选择图片并上传</wd-button>\r\n    <view v-if=\"loading\" class=\"text-blue h-10\">上传...</view>\r\n    <template v-else>\r\n      <view class=\"m-2\">上传后返回的接口数据：</view>\r\n      <view class=\"m-2\">{{ data }}</view>\r\n      <view class=\"h-80 w-full\">\r\n        <image v-if=\"data\" :src=\"data || data\" mode=\"scaleToFill\" />\r\n      </view>\r\n    </template>\r\n  </view>\r\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nconst { loading, data, run } = useUpload({ user: '张三' })\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n//\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages/about/components/upload.vue'\nwx.createComponent(Component)"], "names": ["useUpload"], "mappings": ";;;;;;;;;;;;;;AAiBM,UAAA,EAAE,SAAS,MAAM,IAAA,IAAQA,gBAAAA,UAAU,EAAE,MAAM,MAAM;;;;;;;;;;;;;;;AChBvD,GAAG,gBAAgB,SAAS;"}