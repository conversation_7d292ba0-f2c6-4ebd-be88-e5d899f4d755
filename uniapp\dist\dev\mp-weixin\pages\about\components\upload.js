"use strict";
const common_vendor = require("../../../common/vendor.js");
const hooks_useUpload = require("../../../hooks/useUpload.js");
if (!Array) {
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  _easycom_wd_button2();
}
const _easycom_wd_button = () => "../../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
if (!Math) {
  _easycom_wd_button();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "upload",
  setup(__props) {
    const { loading, data, run } = hooks_useUpload.useUpload({ user: "张三" });
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.o(common_vendor.unref(run)),
        b: common_vendor.unref(loading)
      }, common_vendor.unref(loading) ? {} : common_vendor.e$1({
        c: common_vendor.t(common_vendor.unref(data)),
        d: common_vendor.unref(data)
      }, common_vendor.unref(data) ? {
        e: common_vendor.unref(data) || common_vendor.unref(data)
      } : {}));
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-98fd5dce"]]);
wx.createComponent(Component);
//# sourceMappingURL=upload.js.map
