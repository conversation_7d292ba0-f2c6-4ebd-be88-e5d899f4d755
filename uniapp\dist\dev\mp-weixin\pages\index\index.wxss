/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.swiper-title.data-v-83a5a03c {
  display: flex;
  align-items: center;
  padding-left: 30rpx;
  height: 52px;
  background-color: #fff;
  position: relative;
}
.swiper-title .dot.data-v-83a5a03c {
  width: 14rpx;
  height: 14rpx;
  background-color: #0081ff;
  border-radius: 100%;
  margin-right: 20rpx;
}
.swiper-title .wd-text.data-v-83a5a03c {
  color: #666;
  font-size: 15px;
}
.swiper.data-v-83a5a03c {
  height: 375rpx;
  flex: none;
}
.swiper image.data-v-83a5a03c,
.swiper video.data-v-83a5a03c {
  width: 100%;
  display: block;
  height: 100%;
  margin: 0;
}
.swiper.data-v-83a5a03c .uni-swiper-dot {
  transition: all 400ms ease;
  background-color: rgba(255, 255, 255, 0.4);
  width: 5px;
  height: 5px;
  border-radius: 50%;
  margin: 0 4px;
}
.swiper.data-v-83a5a03c .uni-swiper-dot-active {
  background-color: rgb(255, 255, 255);
  width: 16px;
  border-radius: 2px;
}
.scrollView.data-v-83a5a03c {
  display: flex;
  flex-direction: column;
  min-height: 0;
  background-color: #f1f1f1;
}
.scrollView.data-v-83a5a03c .wd-row {
  background-color: #fff;
  margin-bottom: 32rpx;
}
.scrollView.data-v-83a5a03c .wd-row .wd-col {
  display: flex;
  align-items: center;
  justify-content: center;
}
.scrollView.data-v-83a5a03c .wd-row .wd-col:first-child {
  border-right: 1px solid rgba(165, 165, 165, 0.1);
}
.scrollView.data-v-83a5a03c .wd-row .wd-col .wd-img {
  margin: 20rpx;
  margin-left: 0;
}
.scrollView.data-v-83a5a03c .wd-row .wd-col .textBox {
  text-align: center;
  display: flex;
  flex-direction: column;
}
.scrollView.data-v-83a5a03c .wd-row .wd-col .textBox .wd-text {
  color: #666;
}
.scrollView.data-v-83a5a03c .wd-row .wd-col .textBox .wd-text:last-child {
  font-weight: 200;
}
.scrollView .serveBox.data-v-83a5a03c {
  margin-bottom: 32rpx;
  background-color: #fff;
}
.scrollView .serveBox:last-child .title .dot.data-v-83a5a03c {
  background-color: #fbbd08;
}
.scrollView .serveBox.data-v-83a5a03c .wd-grid-item:not(.enabled) {
  opacity: 0.5;
}
.scrollView .serveBox .title.data-v-83a5a03c {
  display: flex;
  align-items: center;
  padding-left: 30rpx;
  height: 52px;
  position: relative;
}
.scrollView .serveBox .title .dot.data-v-83a5a03c {
  width: 14rpx;
  height: 14rpx;
  background-color: #0081ff;
  border-radius: 100%;
  margin-right: 20rpx;
}
.scrollView .serveBox .title .wd-text.data-v-83a5a03c {
  color: #666;
  font-size: 15px;
}
.scrollView .serveBox .title .view-more.data-v-83a5a03c {
  position: absolute;
  right: 30rpx;
  color: #999;
  font-size: 14px;
}
.scrollView .serveBox .recent-data.data-v-83a5a03c {
  padding: 0 20rpx 20rpx;
  position: relative;
  min-height: 200rpx;
}
.scrollView .serveBox .recent-data .loading-container.data-v-83a5a03c {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
}
.scrollView .serveBox .recent-data .empty-data.data-v-83a5a03c {
  padding: 30rpx 0;
  text-align: center;
  color: #999;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.scrollView .serveBox .recent-data .empty-data .empty-image.data-v-83a5a03c {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}
.scrollView .serveBox .recent-data .data-items .data-card.data-v-83a5a03c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background-color: white;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}
.scrollView .serveBox .recent-data .data-items .data-card.data-v-83a5a03c:active {
  transform: scale(0.99);
  background-color: #fafafa;
}
.scrollView .serveBox .recent-data .data-items .data-card .data-info.data-v-83a5a03c {
  display: flex;
  align-items: flex-start;
  flex: 1;
}
.scrollView .serveBox .recent-data .data-items .data-card .data-info .data-icon.data-v-83a5a03c {
  margin-right: 16rpx;
  background-color: rgba(7, 193, 96, 0.1);
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.scrollView .serveBox .recent-data .data-items .data-card .data-info .card-image.data-v-83a5a03c {
  width: 50rpx;
  height: 50rpx;
  display: block;
}
.scrollView .serveBox .recent-data .data-items .data-card .data-info .data-details.data-v-83a5a03c {
  flex: 1;
  overflow: hidden;
}
.scrollView .serveBox .recent-data .data-items .data-card .data-info .data-details .data-title-row.data-v-83a5a03c {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8rpx;
}
.scrollView .serveBox .recent-data .data-items .data-card .data-info .data-details .data-title-row .data-type.data-v-83a5a03c {
  font-size: 30rpx;
  color: #333333;
  font-weight: 500;
}
.scrollView .serveBox .recent-data .data-items .data-card .data-info .data-details .data-content .data-fields.data-v-83a5a03c {
  display: flex;
  flex-direction: column;
}
.scrollView .serveBox .recent-data .data-items .data-card .data-info .data-details .data-content .data-fields .data-field.data-v-83a5a03c {
  margin-top: 4rpx;
}
.scrollView .serveBox .recent-data .data-items .data-card .data-info .data-details .data-content .data-fields .data-field .field-value.data-v-83a5a03c {
  color: #666666;
  font-size: 24rpx;
  display: block;
}
.scrollView .serveBox .recent-data .data-items .data-card .data-action.data-v-83a5a03c {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
}
.scrollView .serveBox .recent-data .data-items .data-card .data-action .action-text.data-v-83a5a03c {
  color: #07C160;
  font-size: 26rpx;
  margin-right: 10rpx;
}
.registration-button-container.data-v-83a5a03c {
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
}
.registration-button-container .registration-button.data-v-83a5a03c {
  background-color: #07C160;
  color: #fff;
  padding: 15rpx 30rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  width: 100%;
  text-align: center;
  box-shadow: 0 4rpx 8rpx rgba(7, 193, 96, 0.2);
  transition: all 0.3s;
  margin: 0 5rpx;
}
.registration-button-container .registration-button.data-v-83a5a03c:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 4rpx rgba(7, 193, 96, 0.2);
}
.registration-button-container .registration-button.disabled.data-v-83a5a03c {
  background-color: #cccccc;
  color: #999999;
  box-shadow: none;
  cursor: not-allowed;
}
.registration-button-container .registration-button.disabled.data-v-83a5a03c:active {
  transform: none;
  box-shadow: none;
}
.registration-tip.data-v-83a5a03c {
  padding: 0 20rpx 10rpx;
  display: flex;
  justify-content: center;
}
.registration-tip .tip-text.data-v-83a5a03c {
  color: #ff4d4f;
  font-size: 26rpx;
  font-weight: bold;
}