{"version": 3, "file": "wd-tag.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-tag/wd-tag.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC10YWcvd2QtdGFnLnZ1ZQ"], "sourcesContent": ["<template>\n  <view :class=\"rootClass\" :style=\"rootStyle\" @click=\"handleClick\">\n    <view v-if=\"useIconSlot\" class=\"wd-tag__icon\">\n      <slot name=\"icon\" />\n    </view>\n    <wd-icon v-else-if=\"icon\" :name=\"icon\" custom-class=\"wd-tag__icon\" />\n    <view class=\"wd-tag__text\" :style=\"textStyle\">\n      <slot />\n    </view>\n    <view class=\"wd-tag__close\" v-if=\"closable && round\" @click.stop=\"handleClose\">\n      <wd-icon name=\"error-fill\" />\n    </view>\n    <input\n      v-if=\"dynamicInput && dynamic\"\n      class=\"wd-tag__add-text\"\n      :placeholder=\"translate('placeholder')\"\n      type=\"text\"\n      :focus=\"true\"\n      v-model=\"dynamicValue\"\n      @blur=\"handleBlur\"\n      @confirm=\"handleConfirm\"\n    />\n    <view v-else-if=\"dynamic\" class=\"wd-tag__text\" :style=\"textStyle\" @click.stop=\"handleAdd\">\n      <slot name=\"add\" v-if=\"$slots.add\"></slot>\n      <template v-else>\n        <wd-icon name=\"add\" custom-class=\"wd-tag__add wd-tag__icon\" />\n        <text>{{ translate('add') }}</text>\n      </template>\n    </view>\n  </view>\n</template>\n\n<script lang=\"ts\">\nexport default {\n  name: 'wd-tag',\n  options: {\n    addGlobalClass: true,\n    virtualHost: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n<script lang=\"ts\" setup>\nimport wdIcon from '../wd-icon/wd-icon.vue'\nimport { objToStyle } from '../common/util'\nimport { computed, ref, watch } from 'vue'\nimport { useTranslate } from '../composables/useTranslate'\nimport { tagProps } from './types'\n\nconst props = defineProps(tagProps)\nconst emit = defineEmits(['click', 'close', 'confirm'])\n\nconst { translate } = useTranslate('tag')\n\nconst tagClass = ref<string>('')\nconst dynamicValue = ref<string>('')\nconst dynamicInput = ref<boolean>(false)\n\nwatch(\n  [() => props.useIconSlot, () => props.icon, () => props.plain, () => props.dynamic, () => props.round, () => props.mark],\n  () => {\n    computeTagClass()\n  },\n  { deep: true, immediate: true }\n)\n\nwatch(\n  () => props.type,\n  (newValue) => {\n    if (!newValue) return\n    // type: 'primary', 'danger', 'warning', 'success'\n    const type = ['primary', 'danger', 'warning', 'success', 'default']\n    if (type.indexOf(newValue) === -1) console.error(`type must be one of ${type.toString()}`)\n    computeTagClass()\n  },\n  { deep: true, immediate: true }\n)\n\nwatch(\n  () => dynamicInput.value,\n  () => {\n    computeTagClass()\n  },\n  { deep: true, immediate: true }\n)\n\nconst rootClass = computed(() => {\n  return `wd-tag ${props.customClass} ${tagClass.value}`\n})\n\nconst rootStyle = computed(() => {\n  const rootStyle: Record<string, any> = {}\n  if (!props.plain && props.bgColor) {\n    rootStyle['background'] = props.bgColor\n  }\n  if (props.bgColor) {\n    rootStyle['border-color'] = props.bgColor\n  }\n  return `${objToStyle(rootStyle)}${props.customStyle}`\n})\n\nconst textStyle = computed(() => {\n  const textStyle: Record<string, any> = {}\n  if (props.color) {\n    textStyle['color'] = props.color\n  }\n  return objToStyle(textStyle)\n})\n\nfunction computeTagClass() {\n  const { type, plain, round, mark, dynamic, icon, useIconSlot } = props\n  let tagClassList: string[] = []\n  type && tagClassList.push(`is-${type}`)\n  plain && tagClassList.push('is-plain')\n  round && tagClassList.push('is-round')\n  mark && tagClassList.push('is-mark')\n  dynamic && tagClassList.push('is-dynamic')\n  dynamicInput.value && tagClassList.push('is-dynamic-input')\n  if (icon || useIconSlot) tagClassList.push('is-icon')\n  tagClass.value = tagClassList.join(' ')\n}\n\nfunction handleClick(event: any) {\n  emit('click', event)\n}\nfunction handleClose(event: any) {\n  emit('close', event)\n}\nfunction handleAdd() {\n  dynamicInput.value = true\n  dynamicValue.value = ''\n}\nfunction handleBlur() {\n  setDynamicInput()\n}\nfunction handleConfirm(event: any) {\n  setDynamicInput()\n  emit('confirm', {\n    value: event.detail.value\n  })\n}\nfunction setDynamicInput() {\n  dynamicInput.value = false\n}\n</script>\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-tag/wd-tag.vue'\nwx.createComponent(Component)"], "names": ["useTranslate", "ref", "watch", "computed", "rootStyle", "objToStyle", "textStyle"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AA2CA,MAAA,SAAmB,MAAA;AAVnB,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,gBAAgB;AAAA,EAAA;AAEpB;;;;;AASA,UAAM,QAAQ;AACd,UAAM,OAAO;AAEb,UAAM,EAAE,UAAA,IAAcA,cAAA,aAAa,KAAK;AAElC,UAAA,WAAWC,kBAAY,EAAE;AACzB,UAAA,eAAeA,kBAAY,EAAE;AAC7B,UAAA,eAAeA,kBAAa,KAAK;AAEvCC,kBAAA;AAAA,MACE,CAAC,MAAM,MAAM,aAAa,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,SAAS,MAAM,MAAM,OAAO,MAAM,MAAM,IAAI;AAAA,MACvH,MAAM;AACY,wBAAA;AAAA,MAClB;AAAA,MACA,EAAE,MAAM,MAAM,WAAW,KAAK;AAAA,IAChC;AAEAA,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,aAAa;AACZ,YAAI,CAAC;AAAU;AAEf,cAAM,OAAO,CAAC,WAAW,UAAU,WAAW,WAAW,SAAS;AAC9D,YAAA,KAAK,QAAQ,QAAQ,MAAM;AAAI,kBAAQ,MAAM,uBAAuB,KAAK,SAAU,CAAA,EAAE;AACzE,wBAAA;AAAA,MAClB;AAAA,MACA,EAAE,MAAM,MAAM,WAAW,KAAK;AAAA,IAChC;AAEAA,kBAAA;AAAA,MACE,MAAM,aAAa;AAAA,MACnB,MAAM;AACY,wBAAA;AAAA,MAClB;AAAA,MACA,EAAE,MAAM,MAAM,WAAW,KAAK;AAAA,IAChC;AAEM,UAAA,YAAYC,cAAAA,SAAS,MAAM;AAC/B,aAAO,UAAU,MAAM,WAAW,IAAI,SAAS,KAAK;AAAA,IAAA,CACrD;AAEK,UAAA,YAAYA,cAAAA,SAAS,MAAM;AAC/B,YAAMC,aAAiC,CAAC;AACxC,UAAI,CAAC,MAAM,SAAS,MAAM,SAAS;AACjCA,mBAAU,YAAY,IAAI,MAAM;AAAA,MAAA;AAElC,UAAI,MAAM,SAAS;AACjBA,mBAAU,cAAc,IAAI,MAAM;AAAA,MAAA;AAEpC,aAAO,GAAGC,cAAAA,WAAWD,UAAS,CAAC,GAAG,MAAM,WAAW;AAAA,IAAA,CACpD;AAEK,UAAA,YAAYD,cAAAA,SAAS,MAAM;AAC/B,YAAMG,aAAiC,CAAC;AACxC,UAAI,MAAM,OAAO;AACfA,mBAAU,OAAO,IAAI,MAAM;AAAA,MAAA;AAE7B,aAAOD,cAAAA,WAAWC,UAAS;AAAA,IAAA,CAC5B;AAED,aAAS,kBAAkB;AACnB,YAAA,EAAE,MAAM,OAAO,OAAO,MAAM,SAAS,MAAM,gBAAgB;AACjE,UAAI,eAAyB,CAAC;AAC9B,cAAQ,aAAa,KAAK,MAAM,IAAI,EAAE;AAC7B,eAAA,aAAa,KAAK,UAAU;AAC5B,eAAA,aAAa,KAAK,UAAU;AAC7B,cAAA,aAAa,KAAK,SAAS;AACxB,iBAAA,aAAa,KAAK,YAAY;AAC5B,mBAAA,SAAS,aAAa,KAAK,kBAAkB;AAC1D,UAAI,QAAQ;AAAa,qBAAa,KAAK,SAAS;AAC3C,eAAA,QAAQ,aAAa,KAAK,GAAG;AAAA,IAAA;AAGxC,aAAS,YAAY,OAAY;AAC/B,WAAK,SAAS,KAAK;AAAA,IAAA;AAErB,aAAS,YAAY,OAAY;AAC/B,WAAK,SAAS,KAAK;AAAA,IAAA;AAErB,aAAS,YAAY;AACnB,mBAAa,QAAQ;AACrB,mBAAa,QAAQ;AAAA,IAAA;AAEvB,aAAS,aAAa;AACJ,sBAAA;AAAA,IAAA;AAElB,aAAS,cAAc,OAAY;AACjB,sBAAA;AAChB,WAAK,WAAW;AAAA,QACd,OAAO,MAAM,OAAO;AAAA,MAAA,CACrB;AAAA,IAAA;AAEH,aAAS,kBAAkB;AACzB,mBAAa,QAAQ;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7IvB,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}