"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
const store_user = require("../../store/user.js");
if (!Array) {
  const _component_NavBar = common_vendor.resolveComponent("NavBar");
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_component_NavBar + _easycom_uni_icons2 + _easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  (_easycom_uni_icons + _easycom_PageLayout)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "examinationReportForm"
}), {
  __name: "form",
  setup(__props) {
    const userStore = store_user.useUserStore();
    const deleteIds = common_vendor.ref([]);
    const query = common_vendor.ref({});
    common_vendor.onMounted(() => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      query.value = currentPage.options || {};
      if (query.value.mode === "view" && query.value.idList) {
        loadFormData(query.value.idList);
      }
    });
    const mode = common_vendor.computed(() => query.value.mode || "add");
    const isViewMode = common_vendor.computed(() => false);
    const reportTypeOptions = [
      "血常规",
      "血糖",
      "血脂",
      "肝功能",
      "肾功能",
      "NT-proBNP",
      "BNP",
      "电解质",
      "心电图",
      "心脏彩超"
      // '其他'
    ];
    const unitOptions = ["mg/dL", "μmol/L"];
    const bnpUnitOptions = ["pg/mL", "pmol/L"];
    const electrolyteUnitOptions = ["mEq/L", "mmol/L"];
    const hemoglobinUnitOptions = ["g/dL", "g/L"];
    const glucoseUnitOptions = ["mg/dL", "mmol/L"];
    const lipidUnitOptions = ["mg/dL", "mmol/L"];
    const rhythmOptions = ["窦性心律", "心房颤动/扑动", "起搏心律", "室性早搏"];
    const lbbbOptions = ["有", "无"];
    const yesNoOptions = ["是", "否"];
    const selectedReportTypeIndex = common_vendor.ref(0);
    const selectedReportType = common_vendor.ref("");
    const otherReportTypeName = common_vendor.ref("");
    const formData = common_vendor.ref({
      reportItems: []
    });
    const canAddReportType = common_vendor.computed(() => {
      if (!selectedReportType.value)
        return false;
      if (selectedReportType.value === "其他") {
        return otherReportTypeName.value.trim() !== "";
      }
      return !formData.value.reportItems.some((item) => item.type === selectedReportType.value);
    });
    const onReportTypeChange = (e) => {
      const index = e.detail.value;
      selectedReportTypeIndex.value = index;
      selectedReportType.value = reportTypeOptions[index];
      if (selectedReportType.value !== "其他") {
        otherReportTypeName.value = "";
      }
    };
    const addReportType = () => {
      if (!canAddReportType.value)
        return;
      const newReportItem = {
        type: selectedReportType.value,
        checkDate: "",
        images: [],
        remark: "",
        // 特殊字段初始化
        bloodCreatinine: void 0,
        creatinineUnit: void 0,
        uricAcid: void 0,
        uricAcidUnit: void 0,
        gfr: void 0,
        bnpValue: void 0,
        bnpUnit: void 0,
        ntProBnpValue: void 0,
        ntProBnpUnit: void 0,
        sodium: void 0,
        sodiumUnit: void 0,
        potassium: void 0,
        potassiumUnit: void 0,
        hemoglobin: void 0,
        hemoglobinUnit: void 0,
        fastingGlucose: void 0,
        fastingGlucoseUnit: void 0,
        hbA1c: void 0,
        // 血脂字段初始化
        totalCholesterol: void 0,
        totalCholesterolUnit: void 0,
        ldl: void 0,
        ldlUnit: void 0,
        // 肝功能字段初始化
        totalBilirubin: void 0,
        directBilirubin: void 0,
        indirectBilirubin: void 0,
        alt: void 0,
        // 心电图字段初始化
        heartRate: void 0,
        lbbb: void 0,
        qrsDuration: void 0,
        // 心脏彩超字段初始化
        ejectionFraction: void 0,
        leftVentricularEndDiastolicDiameter: void 0,
        leftAtriumEnlarged: void 0,
        leftVentricularHypertrophy: void 0,
        septalThickness: void 0,
        leftVentricularDiastolicDysfunction: void 0
      };
      if (selectedReportType.value === "心电图") {
        newReportItem.rhythms = [];
      }
      if (selectedReportType.value === "其他") {
        newReportItem.otherName = otherReportTypeName.value;
      }
      formData.value.reportItems.push(newReportItem);
      if (selectedReportType.value !== "其他") {
        selectedReportType.value = "";
        selectedReportTypeIndex.value = 0;
      } else {
        otherReportTypeName.value = "";
      }
    };
    const onExistingReportTypeChange = (e, index) => {
      const typeIndex = e.detail.value;
      const newType = reportTypeOptions[typeIndex];
      const oldType = formData.value.reportItems[index].type;
      const isDuplicate = formData.value.reportItems.some(
        (item, i) => i !== index && item.type === newType
      );
      if (isDuplicate) {
        common_vendor.index.showToast({
          title: "该报告类型已存在",
          icon: "none"
        });
        return;
      }
      formData.value.reportItems[index].type = newType;
      if (oldType !== newType) {
        switch (oldType) {
          case "肾功能":
            formData.value.reportItems[index].bloodCreatinine = void 0;
            formData.value.reportItems[index].creatinineUnit = void 0;
            formData.value.reportItems[index].uricAcid = void 0;
            formData.value.reportItems[index].uricAcidUnit = void 0;
            formData.value.reportItems[index].gfr = void 0;
            break;
          case "NT-proBNP":
            formData.value.reportItems[index].ntProBnpValue = void 0;
            formData.value.reportItems[index].ntProBnpUnit = void 0;
            break;
          case "BNP":
            formData.value.reportItems[index].bnpValue = void 0;
            formData.value.reportItems[index].bnpUnit = void 0;
            break;
          case "电解质":
            formData.value.reportItems[index].sodium = void 0;
            formData.value.reportItems[index].sodiumUnit = void 0;
            formData.value.reportItems[index].potassium = void 0;
            formData.value.reportItems[index].potassiumUnit = void 0;
            break;
          case "血常规":
            formData.value.reportItems[index].hemoglobin = void 0;
            formData.value.reportItems[index].hemoglobinUnit = void 0;
            break;
          case "血糖":
            formData.value.reportItems[index].fastingGlucose = void 0;
            formData.value.reportItems[index].fastingGlucoseUnit = void 0;
            formData.value.reportItems[index].hbA1c = void 0;
            break;
          case "血脂":
            formData.value.reportItems[index].totalCholesterol = void 0;
            formData.value.reportItems[index].totalCholesterolUnit = void 0;
            formData.value.reportItems[index].ldl = void 0;
            formData.value.reportItems[index].ldlUnit = void 0;
            break;
          case "肝功能":
            formData.value.reportItems[index].totalBilirubin = void 0;
            formData.value.reportItems[index].directBilirubin = void 0;
            formData.value.reportItems[index].indirectBilirubin = void 0;
            formData.value.reportItems[index].alt = void 0;
            break;
          case "心电图":
            formData.value.reportItems[index].heartRate = void 0;
            formData.value.reportItems[index].rhythms = void 0;
            formData.value.reportItems[index].lbbb = void 0;
            formData.value.reportItems[index].qrsDuration = void 0;
            break;
          case "心脏彩超":
            formData.value.reportItems[index].ejectionFraction = void 0;
            formData.value.reportItems[index].leftVentricularEndDiastolicDiameter = void 0;
            formData.value.reportItems[index].leftAtriumEnlarged = void 0;
            formData.value.reportItems[index].leftVentricularHypertrophy = void 0;
            formData.value.reportItems[index].septalThickness = void 0;
            formData.value.reportItems[index].leftVentricularDiastolicDysfunction = void 0;
            break;
        }
      }
    };
    const removeReportType = (index) => {
      const reportId = formData.value.reportItems[index].id;
      if (mode.value === "view" && reportId) {
        deleteIds.value.push(reportId);
      }
      formData.value.reportItems.splice(index, 1);
    };
    const onDateChange = (e, index) => {
      console.log("日期选择事件:", e);
      if (e && e.detail && e.detail.value) {
        formData.value.reportItems[index].checkDate = e.detail.value;
      }
    };
    const chooseImage = (index) => {
      const reportItem = formData.value.reportItems[index];
      common_vendor.index.chooseImage({
        count: 9 - reportItem.images.length,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          common_vendor.index.showLoading({
            title: "上传中..."
          });
          const uploadPromises = res.tempFilePaths.map((tempPath) => {
            return new Promise((resolve, reject) => {
              common_vendor.index.uploadFile({
                url: `${"https://www.mograine.cn/api"}/file/uploadreport`,
                filePath: tempPath,
                name: "file",
                header: {
                  "X-Access-Token": userStore.userInfo.token
                },
                success: (uploadRes) => {
                  try {
                    const data = JSON.parse(uploadRes.data);
                    if (data.success) {
                      resolve({
                        fileName: data.result.fileName,
                        fileUrl: data.result.fileUrl
                      });
                      console.log("图片上传成功:", data.result.fileName, data.result.fileUrl);
                    } else {
                      reject(new Error(data.message || "上传失败"));
                    }
                  } catch (err) {
                    reject(new Error("解析上传响应失败"));
                  }
                },
                fail: (err) => {
                  reject(err);
                }
              });
            });
          });
          Promise.all(uploadPromises).then((imageInfos) => {
            reportItem.images = [...reportItem.images, ...imageInfos];
            common_vendor.index.hideLoading();
          }).catch((err) => {
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: "图片上传失败: " + err.message,
              icon: "none",
              duration: 2e3
            });
          });
        }
      });
    };
    const previewImage = (reportIndex, imageIndex) => {
      const reportItem = formData.value.reportItems[reportIndex];
      const imageUrls = reportItem.images.map((img) => img.fileUrl);
      common_vendor.index.previewImage({
        current: imageUrls[imageIndex],
        // 使用URL字符串而不是索引
        urls: imageUrls
      });
    };
    const deleteImage = (reportIndex, imageIndex) => {
      if (isViewMode.value)
        return;
      formData.value.reportItems[reportIndex].images.splice(imageIndex, 1);
    };
    const loadFormData = (id) => {
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      common_vendor.index.request({
        url: `${"https://www.mograine.cn/api"}/patient/getReportList`,
        method: "POST",
        data: {
          idList: id.split(",").filter((id2) => id2)
        },
        success: (res) => {
          var _a;
          console.log("获取检查报告数据:", res.data);
          if (res.data && res.data.code === 200 && res.data.result) {
            const reportData = res.data.result.records || [];
            formData.value.reportItems = [];
            if (Array.isArray(reportData)) {
              reportData.forEach((item) => {
                const imageUrls = item.imageUrlList || [];
                const images = !imageUrls.length && item.images ? typeof item.images === "string" ? JSON.parse(item.images) : item.images : [];
                const formattedImages = imageUrls.length ? imageUrls.map((url) => {
                  var _a2;
                  const fileName = ((_a2 = url.split("/").pop()) == null ? void 0 : _a2.split("?")[0]) || "";
                  return {
                    fileName,
                    fileUrl: url
                  };
                }) : images.map((img) => {
                  if (typeof img === "string") {
                    return {
                      fileName: img,
                      fileUrl: `${"https://www.mograine.cn/api"}/file/view/${img}`
                    };
                  }
                  return {
                    fileName: img.fileName || img.name || "",
                    fileUrl: img.fileUrl || img.url || `${"https://www.mograine.cn/api"}/file/view/${img.fileName || img.name || ""}`
                  };
                });
                const reportItem = {
                  id: item.id,
                  // 保存ID用于后续编辑操作
                  type: item.type,
                  checkDate: item.checkDate || "",
                  images: formattedImages,
                  remark: item.remark || ""
                };
                if (item.type === "肾功能") {
                  reportItem.bloodCreatinine = item.bloodCreatinine;
                  reportItem.creatinineUnit = item.creatinineUnit;
                  reportItem.uricAcid = item.uricAcid;
                  reportItem.uricAcidUnit = item.uricAcidUnit;
                  reportItem.gfr = item.gfr;
                } else if (item.type === "NT-proBNP") {
                  reportItem.ntProBnpValue = item.ntProBnpValue;
                  reportItem.ntProBnpUnit = item.ntProBnpUnit;
                } else if (item.type === "BNP") {
                  reportItem.bnpValue = item.bnpValue;
                  reportItem.bnpUnit = item.bnpUnit;
                } else if (item.type === "电解质") {
                  reportItem.sodium = item.sodium;
                  reportItem.sodiumUnit = item.sodiumUnit;
                  reportItem.potassium = item.potassium;
                  reportItem.potassiumUnit = item.potassiumUnit;
                } else if (item.type === "血常规") {
                  reportItem.hemoglobin = item.hemoglobin;
                  reportItem.hemoglobinUnit = item.hemoglobinUnit;
                } else if (item.type === "血糖") {
                  reportItem.fastingGlucose = item.fastingGlucose;
                  reportItem.fastingGlucoseUnit = item.fastingGlucoseUnit;
                  reportItem.hbA1c = item.hbA1c;
                } else if (item.type === "血脂") {
                  reportItem.totalCholesterol = item.totalCholesterol;
                  reportItem.totalCholesterolUnit = item.totalCholesterolUnit;
                  reportItem.ldl = item.ldl;
                  reportItem.ldlUnit = item.ldlUnit;
                } else if (item.type === "肝功能") {
                  reportItem.totalBilirubin = item.totalBilirubin;
                  reportItem.directBilirubin = item.directBilirubin;
                  reportItem.indirectBilirubin = item.indirectBilirubin;
                  reportItem.alt = item.alt;
                } else if (item.type === "心电图") {
                  reportItem.heartRate = item.heartRate;
                  reportItem.rhythms = item.rhythms ? typeof item.rhythms === "string" ? item.rhythms.split(",") : item.rhythms : [];
                  reportItem.lbbb = item.lbbb;
                  reportItem.qrsDuration = item.qrsDuration;
                } else if (item.type === "心脏彩超") {
                  reportItem.ejectionFraction = item.ejectionFraction;
                  reportItem.leftVentricularEndDiastolicDiameter = item.leftVentricularEndDiastolicDiameter;
                  reportItem.leftAtriumEnlarged = item.leftAtriumEnlarged;
                  reportItem.leftVentricularHypertrophy = item.leftVentricularHypertrophy;
                  reportItem.septalThickness = item.septalThickness;
                  reportItem.leftVentricularDiastolicDysfunction = item.leftVentricularDiastolicDysfunction;
                }
                formData.value.reportItems.push(reportItem);
              });
            }
            console.log("处理后的表单数据:", formData.value);
          } else {
            common_vendor.index.showToast({
              title: ((_a = res.data) == null ? void 0 : _a.message) || "获取数据失败",
              icon: "none"
            });
          }
        },
        fail: (err) => {
          console.error("获取数据失败:", err);
          common_vendor.index.showToast({
            title: "网络异常，请稍后重试",
            icon: "none"
          });
        },
        complete: () => {
          common_vendor.index.hideLoading();
        }
      });
    };
    const submitForm = () => {
      console.log("提交表单数据:", formData.value);
      if (formData.value.reportItems.length === 0) {
        common_vendor.index.showToast({
          title: "请至少添加一种报告类型",
          icon: "none"
        });
        return;
      }
      for (let i = 0; i < formData.value.reportItems.length; i++) {
        const report = formData.value.reportItems[i];
        if (!report.checkDate) {
          common_vendor.index.showToast({
            title: `请选择${report.type}的检查日期`,
            icon: "none"
          });
          return;
        }
        if (report.images.length === 0) {
          common_vendor.index.showToast({
            title: `请上传${report.type}的至少一张报告照片`,
            icon: "none"
          });
          return;
        }
        if (report.type === "肾功能") {
          if (report.bloodCreatinine !== void 0 && report.bloodCreatinine !== null && report.bloodCreatinine.toString() !== "") {
            if (!report.creatinineUnit) {
              common_vendor.index.showToast({
                title: `请选择血清肌酐的单位`,
                icon: "none"
              });
              return;
            }
          }
          if (report.uricAcid !== void 0 && report.uricAcid !== null && report.uricAcid.toString() !== "") {
            if (!report.uricAcidUnit) {
              common_vendor.index.showToast({
                title: `请选择尿酸的单位`,
                icon: "none"
              });
              return;
            }
          }
        }
        if (report.type === "血糖") {
          if (report.fastingGlucose !== void 0 && report.fastingGlucose !== null && report.fastingGlucose.toString() !== "") {
            if (!report.fastingGlucoseUnit) {
              common_vendor.index.showToast({
                title: `请选择空腹血糖的单位`,
                icon: "none"
              });
              return;
            }
          }
        }
        if (report.type === "血脂") {
          if (report.totalCholesterol !== void 0 && report.totalCholesterol !== null && report.totalCholesterol.toString() !== "") {
            if (!report.totalCholesterolUnit) {
              common_vendor.index.showToast({
                title: `请选择总胆固醇的单位`,
                icon: "none"
              });
              return;
            }
          }
          if (report.ldl !== void 0 && report.ldl !== null && report.ldl.toString() !== "") {
            if (!report.ldlUnit) {
              common_vendor.index.showToast({
                title: `请选择低密度脂蛋白的单位`,
                icon: "none"
              });
              return;
            }
          }
        }
        if (report.type === "BNP") {
          if (report.bnpValue !== void 0 && report.bnpValue !== null && report.bnpValue.toString() !== "") {
            if (!report.bnpUnit) {
              common_vendor.index.showToast({
                title: `请选择BNP的单位`,
                icon: "none"
              });
              return;
            }
          }
        }
        if (report.type === "NT-proBNP") {
          if (report.ntProBnpValue !== void 0 && report.ntProBnpValue !== null && report.ntProBnpValue.toString() !== "") {
            if (!report.ntProBnpUnit) {
              common_vendor.index.showToast({
                title: `请选择NT-proBNP的单位`,
                icon: "none"
              });
              return;
            }
          }
        }
        if (report.type === "电解质") {
          if (report.sodium !== void 0 && report.sodium !== null && report.sodium.toString() !== "") {
            if (!report.sodiumUnit) {
              common_vendor.index.showToast({
                title: `请选择血钠的单位`,
                icon: "none"
              });
              return;
            }
          }
          if (report.potassium !== void 0 && report.potassium !== null && report.potassium.toString() !== "") {
            if (!report.potassiumUnit) {
              common_vendor.index.showToast({
                title: `请选择血钾的单位`,
                icon: "none"
              });
              return;
            }
          }
        }
        if (report.type === "血常规") {
          if (report.hemoglobin !== void 0 && report.hemoglobin !== null && report.hemoglobin.toString() !== "") {
            if (!report.hemoglobinUnit) {
              common_vendor.index.showToast({
                title: `请选择血红蛋白的单位`,
                icon: "none"
              });
              return;
            }
          }
        }
      }
      common_vendor.index.showLoading({
        title: "提交中..."
      });
      const processedReportItems = formData.value.reportItems.map((report) => {
        const newReport = JSON.parse(JSON.stringify(report));
        newReport.images = report.images.map((img) => img.fileName);
        if (mode.value === "view") {
          newReport.id = report.id;
        }
        return newReport;
      });
      const isEdit = mode.value === "view";
      const requestData = {
        reportItems: processedReportItems,
        userId: userStore.userInfo.userid
      };
      if (isEdit) {
        requestData.updateUserId = userStore.userInfo.userid;
      }
      const apiUrl = `${"https://www.mograine.cn/api"}/patient/savereport`;
      common_vendor.index.request({
        url: apiUrl,
        method: "POST",
        data: requestData,
        success: (res) => {
          var _a, _b;
          if (isEdit && deleteIds.value.length > 0) {
            deleteReports();
          } else {
            common_vendor.index.hideLoading();
            if ((_a = res.data) == null ? void 0 : _a.success) {
              common_vendor.index.showModal({
                title: `${isEdit ? "保存" : "提交"}成功`,
                showCancel: false,
                success: () => {
                  common_vendor.index.navigateBack();
                }
              });
            } else {
              const errorMsg = ((_b = res.data) == null ? void 0 : _b.message) || `${isEdit ? "保存" : "提交"}失败，未知错误`;
              common_vendor.index.showModal({
                title: `${isEdit ? "保存" : "提交"}失败`,
                content: errorMsg,
                showCancel: false
              });
            }
          }
        },
        fail: (err) => {
          common_vendor.index.hideLoading();
          const errorMsg = err.errMsg || "网络错误，请稍后重试";
          common_vendor.index.showModal({
            title: `${isEdit ? "保存" : "提交"}失败`,
            content: errorMsg,
            showCancel: false
          });
        }
      });
    };
    const deleteReports = () => {
      if (deleteIds.value.length === 0) {
        common_vendor.index.hideLoading();
        common_vendor.index.showModal({
          title: "保存成功",
          showCancel: false,
          success: () => {
            common_vendor.index.navigateBack();
          }
        });
        return;
      }
      common_vendor.index.request({
        url: `${"https://www.mograine.cn/api"}/patient/deletereport`,
        method: "POST",
        data: {
          deleteId: deleteIds.value
        },
        success: (res) => {
          var _a, _b;
          common_vendor.index.hideLoading();
          if ((_a = res.data) == null ? void 0 : _a.success) {
            common_vendor.index.showModal({
              title: "保存成功",
              showCancel: false,
              success: () => {
                common_vendor.index.navigateBack();
              }
            });
          } else {
            const errorMsg = ((_b = res.data) == null ? void 0 : _b.message) || "删除报告失败，未知错误";
            common_vendor.index.showModal({
              title: "保存部分成功",
              content: "新报告已保存，但删除部分报告失败：" + errorMsg,
              showCancel: false,
              success: () => {
                common_vendor.index.navigateBack();
              }
            });
          }
        },
        fail: (err) => {
          common_vendor.index.hideLoading();
          const errorMsg = err.errMsg || "网络错误，请稍后重试";
          common_vendor.index.showModal({
            title: "保存部分成功",
            content: "新报告已保存，但删除部分报告失败：" + errorMsg,
            showCancel: false,
            success: () => {
              common_vendor.index.navigateBack();
            }
          });
        }
      });
    };
    const handleUnitChange = (e, index, field) => {
      if (e && e.detail && e.detail.value !== void 0) {
        const selectedIndex = parseInt(e.detail.value);
        formData.value.reportItems[index][field] = unitOptions[selectedIndex];
      }
    };
    const handleBnpUnitChange = (e, index, field) => {
      if (e && e.detail && e.detail.value !== void 0) {
        const selectedIndex = parseInt(e.detail.value);
        formData.value.reportItems[index][field] = bnpUnitOptions[selectedIndex];
      }
    };
    const handleElectrolyteUnitChange = (e, index, field) => {
      if (e && e.detail && e.detail.value !== void 0) {
        const selectedIndex = parseInt(e.detail.value);
        formData.value.reportItems[index][field] = electrolyteUnitOptions[selectedIndex];
      }
    };
    const handleHemoglobinUnitChange = (e, index, field) => {
      if (e && e.detail && e.detail.value !== void 0) {
        const selectedIndex = parseInt(e.detail.value);
        formData.value.reportItems[index][field] = hemoglobinUnitOptions[selectedIndex];
      }
    };
    const handleGlucoseUnitChange = (e, index, field) => {
      if (e && e.detail && e.detail.value !== void 0) {
        const selectedIndex = parseInt(e.detail.value);
        formData.value.reportItems[index][field] = glucoseUnitOptions[selectedIndex];
      }
    };
    const handleLipidUnitChange = (e, index, field) => {
      if (e && e.detail && e.detail.value !== void 0) {
        const selectedIndex = parseInt(e.detail.value);
        formData.value.reportItems[index][field] = lipidUnitOptions[selectedIndex];
      }
    };
    const toggleRhythm = (index, rhythmValue) => {
      const report = formData.value.reportItems[index];
      if (report.type !== "心电图")
        return;
      if (!report.rhythms) {
        report.rhythms = [];
      }
      const existingIndex = report.rhythms.indexOf(rhythmValue);
      if (existingIndex !== -1) {
        report.rhythms.splice(existingIndex, 1);
      } else {
        report.rhythms.push(rhythmValue);
      }
    };
    const setLbbb = (index, value) => {
      formData.value.reportItems[index].lbbb = value;
    };
    const setLeftAtriumEnlarged = (index, value) => {
      formData.value.reportItems[index].leftAtriumEnlarged = value;
    };
    const setLeftVentricularHypertrophy = (index, value) => {
      formData.value.reportItems[index].leftVentricularHypertrophy = value;
    };
    const setLeftVentricularDiastolicDysfunction = (index, value) => {
      formData.value.reportItems[index].leftVentricularDiastolicDysfunction = value;
    };
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.p({
          title: mode.value === "add" ? "新增" : "编辑",
          showBack: true
        }),
        b: mode.value === "add"
      }, mode.value === "add" ? {
        c: common_vendor.t(selectedReportType.value || "请选择报告类型"),
        d: !selectedReportType.value ? 1 : "",
        e: common_vendor.p({
          type: "bottom",
          size: "12",
          color: "#999999"
        }),
        f: isViewMode.value,
        g: selectedReportTypeIndex.value,
        h: reportTypeOptions,
        i: common_vendor.o(onReportTypeChange),
        j: common_vendor.o(addReportType),
        k: isViewMode.value || !canAddReportType.value
      } : {}, {
        l: formData.value.reportItems.length > 0
      }, formData.value.reportItems.length > 0 ? {
        m: common_vendor.f(formData.value.reportItems, (report, index, i0) => {
          return common_vendor.e$1({
            a: common_vendor.t(report.type),
            b: "552db1d6-4-" + i0 + ",552db1d6-1",
            c: reportTypeOptions.indexOf(report.type),
            d: common_vendor.o((e) => onExistingReportTypeChange(e, index), index),
            e: report.otherName
          }, report.otherName ? {
            f: common_vendor.t(report.otherName)
          } : {}, {
            g: report.checkDate
          }, report.checkDate ? {
            h: common_vendor.t(report.checkDate)
          } : {}, {
            i: "552db1d6-5-" + i0 + ",552db1d6-1",
            j: report.checkDate,
            k: common_vendor.o((e) => onDateChange(e, index), index)
          }, !isViewMode.value ? {
            l: "552db1d6-6-" + i0 + ",552db1d6-1",
            m: common_vendor.p({
              type: "trash",
              size: "18",
              color: "#FF0000"
            }),
            n: common_vendor.o(($event) => removeReportType(index), index)
          } : {}, {
            o: common_vendor.f(report.images, (item, imgIndex, i1) => {
              return {
                a: item.fileUrl,
                b: common_vendor.o(($event) => previewImage(index, imgIndex), imgIndex),
                c: "552db1d6-7-" + i0 + "-" + i1 + ",552db1d6-1",
                d: common_vendor.o(($event) => deleteImage(index, imgIndex), imgIndex),
                e: imgIndex
              };
            }),
            p: !isViewMode.value && report.images.length < 9
          }, !isViewMode.value && report.images.length < 9 ? {
            q: "552db1d6-8-" + i0 + ",552db1d6-1",
            r: common_vendor.p({
              type: "camera-filled",
              size: "24",
              color: "#999"
            }),
            s: common_vendor.o(($event) => chooseImage(index), index)
          } : {}, {
            t: report.type === "肾功能"
          }, report.type === "肾功能" ? {
            v: isViewMode.value,
            w: report.bloodCreatinine,
            x: common_vendor.o(($event) => report.bloodCreatinine = $event.detail.value, index),
            y: common_vendor.t(report.creatinineUnit || "单位"),
            z: "552db1d6-9-" + i0 + ",552db1d6-1",
            A: common_vendor.p({
              type: "bottom",
              size: "12",
              color: "#999999"
            }),
            B: unitOptions.indexOf(report.creatinineUnit),
            C: unitOptions,
            D: isViewMode.value,
            E: common_vendor.o((e) => handleUnitChange(e, index, "creatinineUnit"), index),
            F: isViewMode.value,
            G: report.uricAcid,
            H: common_vendor.o(($event) => report.uricAcid = $event.detail.value, index),
            I: common_vendor.t(report.uricAcidUnit || "单位"),
            J: "552db1d6-10-" + i0 + ",552db1d6-1",
            K: common_vendor.p({
              type: "bottom",
              size: "12",
              color: "#999999"
            }),
            L: unitOptions.indexOf(report.uricAcidUnit),
            M: unitOptions,
            N: isViewMode.value,
            O: common_vendor.o((e) => handleUnitChange(e, index, "uricAcidUnit"), index),
            P: isViewMode.value,
            Q: report.gfr,
            R: common_vendor.o(($event) => report.gfr = $event.detail.value, index)
          } : {}, {
            S: report.type === "NT-proBNP"
          }, report.type === "NT-proBNP" ? {
            T: isViewMode.value,
            U: report.ntProBnpValue,
            V: common_vendor.o(($event) => report.ntProBnpValue = $event.detail.value, index),
            W: common_vendor.t(report.ntProBnpUnit || "单位"),
            X: "552db1d6-11-" + i0 + ",552db1d6-1",
            Y: common_vendor.p({
              type: "bottom",
              size: "12",
              color: "#999999"
            }),
            Z: bnpUnitOptions.indexOf(report.ntProBnpUnit),
            aa: bnpUnitOptions,
            ab: isViewMode.value,
            ac: common_vendor.o((e) => handleBnpUnitChange(e, index, "ntProBnpUnit"), index)
          } : {}, {
            ad: report.type === "BNP"
          }, report.type === "BNP" ? {
            ae: isViewMode.value,
            af: report.bnpValue,
            ag: common_vendor.o(($event) => report.bnpValue = $event.detail.value, index),
            ah: common_vendor.t(report.bnpUnit || "单位"),
            ai: "552db1d6-12-" + i0 + ",552db1d6-1",
            aj: common_vendor.p({
              type: "bottom",
              size: "12",
              color: "#999999"
            }),
            ak: bnpUnitOptions.indexOf(report.bnpUnit),
            al: bnpUnitOptions,
            am: isViewMode.value,
            an: common_vendor.o((e) => handleBnpUnitChange(e, index, "bnpUnit"), index)
          } : {}, {
            ao: report.type === "电解质"
          }, report.type === "电解质" ? {
            ap: isViewMode.value,
            aq: report.sodium,
            ar: common_vendor.o(($event) => report.sodium = $event.detail.value, index),
            as: common_vendor.t(report.sodiumUnit || "单位"),
            at: "552db1d6-13-" + i0 + ",552db1d6-1",
            av: common_vendor.p({
              type: "bottom",
              size: "12",
              color: "#999999"
            }),
            aw: electrolyteUnitOptions.indexOf(report.sodiumUnit),
            ax: electrolyteUnitOptions,
            ay: isViewMode.value,
            az: common_vendor.o((e) => handleElectrolyteUnitChange(e, index, "sodiumUnit"), index),
            aA: isViewMode.value,
            aB: report.potassium,
            aC: common_vendor.o(($event) => report.potassium = $event.detail.value, index),
            aD: common_vendor.t(report.potassiumUnit || "单位"),
            aE: "552db1d6-14-" + i0 + ",552db1d6-1",
            aF: common_vendor.p({
              type: "bottom",
              size: "12",
              color: "#999999"
            }),
            aG: electrolyteUnitOptions.indexOf(report.potassiumUnit),
            aH: electrolyteUnitOptions,
            aI: isViewMode.value,
            aJ: common_vendor.o((e) => handleElectrolyteUnitChange(e, index, "potassiumUnit"), index)
          } : {}, {
            aK: report.type === "血常规"
          }, report.type === "血常规" ? {
            aL: isViewMode.value,
            aM: report.hemoglobin,
            aN: common_vendor.o(($event) => report.hemoglobin = $event.detail.value, index),
            aO: common_vendor.t(report.hemoglobinUnit || "单位"),
            aP: "552db1d6-15-" + i0 + ",552db1d6-1",
            aQ: common_vendor.p({
              type: "bottom",
              size: "12",
              color: "#999999"
            }),
            aR: hemoglobinUnitOptions.indexOf(report.hemoglobinUnit),
            aS: hemoglobinUnitOptions,
            aT: isViewMode.value,
            aU: common_vendor.o((e) => handleHemoglobinUnitChange(e, index, "hemoglobinUnit"), index)
          } : {}, {
            aV: report.type === "血糖"
          }, report.type === "血糖" ? {
            aW: isViewMode.value,
            aX: report.fastingGlucose,
            aY: common_vendor.o(($event) => report.fastingGlucose = $event.detail.value, index),
            aZ: common_vendor.t(report.fastingGlucoseUnit || "单位"),
            ba: "552db1d6-16-" + i0 + ",552db1d6-1",
            bb: common_vendor.p({
              type: "bottom",
              size: "12",
              color: "#999999"
            }),
            bc: glucoseUnitOptions.indexOf(report.fastingGlucoseUnit),
            bd: glucoseUnitOptions,
            be: isViewMode.value,
            bf: common_vendor.o((e) => handleGlucoseUnitChange(e, index, "fastingGlucoseUnit"), index),
            bg: isViewMode.value,
            bh: report.hbA1c,
            bi: common_vendor.o(($event) => report.hbA1c = $event.detail.value, index)
          } : {}, {
            bj: report.type === "血脂"
          }, report.type === "血脂" ? {
            bk: isViewMode.value,
            bl: report.totalCholesterol,
            bm: common_vendor.o(($event) => report.totalCholesterol = $event.detail.value, index),
            bn: common_vendor.t(report.totalCholesterolUnit || "单位"),
            bo: "552db1d6-17-" + i0 + ",552db1d6-1",
            bp: common_vendor.p({
              type: "bottom",
              size: "12",
              color: "#999999"
            }),
            bq: lipidUnitOptions.indexOf(report.totalCholesterolUnit),
            br: lipidUnitOptions,
            bs: isViewMode.value,
            bt: common_vendor.o((e) => handleLipidUnitChange(e, index, "totalCholesterolUnit"), index),
            bv: isViewMode.value,
            bw: report.ldl,
            bx: common_vendor.o(($event) => report.ldl = $event.detail.value, index),
            by: common_vendor.t(report.ldlUnit || "单位"),
            bz: "552db1d6-18-" + i0 + ",552db1d6-1",
            bA: common_vendor.p({
              type: "bottom",
              size: "12",
              color: "#999999"
            }),
            bB: lipidUnitOptions.indexOf(report.ldlUnit),
            bC: lipidUnitOptions,
            bD: isViewMode.value,
            bE: common_vendor.o((e) => handleLipidUnitChange(e, index, "ldlUnit"), index)
          } : {}, {
            bF: report.type === "肝功能"
          }, report.type === "肝功能" ? {
            bG: isViewMode.value,
            bH: report.totalBilirubin,
            bI: common_vendor.o(($event) => report.totalBilirubin = $event.detail.value, index),
            bJ: isViewMode.value,
            bK: report.directBilirubin,
            bL: common_vendor.o(($event) => report.directBilirubin = $event.detail.value, index),
            bM: isViewMode.value,
            bN: report.indirectBilirubin,
            bO: common_vendor.o(($event) => report.indirectBilirubin = $event.detail.value, index),
            bP: isViewMode.value,
            bQ: report.alt,
            bR: common_vendor.o(($event) => report.alt = $event.detail.value, index)
          } : {}, {
            bS: report.type === "心电图"
          }, report.type === "心电图" ? {
            bT: isViewMode.value,
            bU: report.heartRate,
            bV: common_vendor.o(($event) => report.heartRate = $event.detail.value, index),
            bW: common_vendor.f(rhythmOptions, (item, i, i1) => {
              return {
                a: report.rhythms && report.rhythms.includes(item) ? 1 : "",
                b: common_vendor.t(item),
                c: i,
                d: common_vendor.o(($event) => !isViewMode.value && toggleRhythm(index, item), i)
              };
            }),
            bX: common_vendor.f(lbbbOptions, (item, i, i1) => {
              return {
                a: report.lbbb === item ? 1 : "",
                b: common_vendor.t(item),
                c: i,
                d: common_vendor.o(($event) => !isViewMode.value && setLbbb(index, item), i)
              };
            }),
            bY: isViewMode.value,
            bZ: report.qrsDuration,
            ca: common_vendor.o(($event) => report.qrsDuration = $event.detail.value, index)
          } : {}, {
            cb: report.type === "心脏彩超"
          }, report.type === "心脏彩超" ? {
            cc: isViewMode.value,
            cd: report.ejectionFraction,
            ce: common_vendor.o(($event) => report.ejectionFraction = $event.detail.value, index),
            cf: isViewMode.value,
            cg: report.leftVentricularEndDiastolicDiameter,
            ch: common_vendor.o(($event) => report.leftVentricularEndDiastolicDiameter = $event.detail.value, index),
            ci: common_vendor.f(yesNoOptions, (item, i, i1) => {
              return {
                a: report.leftAtriumEnlarged === item ? 1 : "",
                b: common_vendor.t(item),
                c: i,
                d: common_vendor.o(($event) => !isViewMode.value && setLeftAtriumEnlarged(index, item), i)
              };
            }),
            cj: common_vendor.f(yesNoOptions, (item, i, i1) => {
              return {
                a: report.leftVentricularHypertrophy === item ? 1 : "",
                b: common_vendor.t(item),
                c: i,
                d: common_vendor.o(($event) => !isViewMode.value && setLeftVentricularHypertrophy(index, item), i)
              };
            }),
            ck: isViewMode.value,
            cl: report.septalThickness,
            cm: common_vendor.o(($event) => report.septalThickness = $event.detail.value, index),
            cn: common_vendor.f(yesNoOptions, (item, i, i1) => {
              return {
                a: report.leftVentricularDiastolicDysfunction === item ? 1 : "",
                b: common_vendor.t(item),
                c: i,
                d: common_vendor.o(($event) => !isViewMode.value && setLeftVentricularDiastolicDysfunction(index, item), i)
              };
            })
          } : {}, {
            co: report.remark,
            cp: common_vendor.o(($event) => report.remark = $event.detail.value, index),
            cq: index
          });
        }),
        n: common_vendor.p({
          type: "bottom",
          size: "12",
          color: "#999999"
        }),
        o: isViewMode.value,
        p: reportTypeOptions,
        q: common_vendor.p({
          type: "calendar",
          size: "16",
          color: "#999"
        }),
        r: isViewMode.value,
        s: !isViewMode.value,
        t: common_vendor.p({
          type: "close",
          size: "16",
          color: "#fff"
        }),
        v: isViewMode.value
      } : {}, {
        w: !isViewMode.value
      }, !isViewMode.value ? {
        x: common_vendor.t(mode.value === "add" ? "提交" : "保存"),
        y: common_vendor.o(submitForm)
      } : {});
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-552db1d6"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=form.js.map
