{"version": 3, "file": "is.js", "sources": ["../../../../../src/pages-work/common/is.ts"], "sourcesContent": ["const toString = Object.prototype.toString\r\n\r\nexport function is(val: unknown, type: string) {\r\n  return toString.call(val) === `[object ${type}]`\r\n}\r\n\r\nexport function isDef<T = unknown>(val?: T): val is T {\r\n  return typeof val !== 'undefined'\r\n}\r\n\r\nexport function isUnDef<T = unknown>(val?: T): val is T {\r\n  return !isDef(val)\r\n}\r\n\r\nexport function isObject(val: any): val is Record<any, any> {\r\n  return val !== null && is(val, 'Object')\r\n}\r\n\r\nexport function isEmpty<T = unknown>(val: T): val is T {\r\n  if (isArray(val) || isString(val)) {\r\n    return val.length === 0\r\n  }\r\n\r\n  if (val instanceof Map || val instanceof Set) {\r\n    return val.size === 0\r\n  }\r\n\r\n  if (isObject(val)) {\r\n    return Object.keys(val).length === 0\r\n  }\r\n\r\n  return false\r\n}\r\n\r\nexport function isDate(val: unknown): val is Date {\r\n  return is(val, 'Date')\r\n}\r\n\r\nexport function isNull(val: unknown): val is null {\r\n  return val === null\r\n}\r\n\r\nexport function isNullAndUnDef(val: unknown): val is null | undefined {\r\n  return isUnDef(val) && isNull(val)\r\n}\r\n\r\nexport function isNullOrUnDef(val: unknown): val is null | undefined {\r\n  return isUnDef(val) || isNull(val)\r\n}\r\n\r\nexport function isNumber(val: unknown): val is number {\r\n  return is(val, 'Number')\r\n}\r\n\r\nexport function isPromise<T = any>(val: any): val is Promise<T> {\r\n  // update-begin--author:sunjianlei---date:20211022---for: 不能既是 Promise 又是 Object --------\r\n  return is(val, 'Promise') && isFunction(val.then) && isFunction(val.catch)\r\n  // update-end--author:sunjianlei---date:20211022---for: 不能既是 Promise 又是 Object --------\r\n}\r\n\r\nexport function isString(val: unknown): val is string {\r\n  return is(val, 'String')\r\n}\r\n\r\nexport function isJsonObjectString(val: string): val is string {\r\n  if (!val) {\r\n    return false\r\n  }\r\n  return val.startsWith('{') && val.endsWith('}')\r\n}\r\n\r\nexport function isFunction(val: unknown): val is Function {\r\n  return typeof val === 'function'\r\n}\r\n\r\nexport function isBoolean(val: unknown): val is boolean {\r\n  return is(val, 'Boolean')\r\n}\r\n\r\nexport function isRegExp(val: unknown): val is RegExp {\r\n  return is(val, 'RegExp')\r\n}\r\n\r\nexport function isArray(val: any): val is Array<any> {\r\n  return val && Array.isArray(val)\r\n}\r\n\r\nexport function isWindow(val: any): val is Window {\r\n  return typeof window !== 'undefined' && is(val, 'Window')\r\n}\r\n\r\nexport function isElement(val: unknown): val is Element {\r\n  return isObject(val) && !!val.tagName\r\n}\r\n\r\nexport function isMap(val: unknown): val is Map<any, any> {\r\n  return is(val, 'Map')\r\n}\r\n\r\nexport const isServer = typeof window === 'undefined'\r\n\r\nexport const isClient = !isServer\r\n\r\nexport function isUrl(path: string): boolean {\r\n  const reg =\r\n    /(((^https?:(?:\\/\\/)?)(?:[-;:&=\\+\\$,\\w]+@)?[A-Za-z0-9.-]+(?::\\d+)?|(?:www.|[-;:&=\\+\\$,\\w]+@)[A-Za-z0-9.-]+)((?:\\/[\\+~%\\/.\\w-_]*)?\\??(?:[-\\+=&;%@.\\w_]*)#?(?:[\\w]*))?)$/\r\n  return reg.test(path)\r\n} "], "names": [], "mappings": ";AAmFO,SAAS,QAAQ,KAA6B;AAC5C,SAAA,OAAO,MAAM,QAAQ,GAAG;AACjC;;"}