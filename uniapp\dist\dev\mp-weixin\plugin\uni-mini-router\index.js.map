{"version": 3, "file": "index.js", "sources": ["../../../../../src/plugin/uni-mini-router/index.ts"], "sourcesContent": ["/*\r\n * @Author: 徐庆凯\r\n * @Date: 2023-03-13 15:48:09\r\n * @LastEditTime: 2023-05-29 14:51:18\r\n * @LastEditors: weisheng\r\n * @Description:\r\n * @FilePath: \\uni-mini-router\\src\\index.ts\r\n * 记得注释\r\n */\r\nimport { routeKey, routerKey } from './symbols'\r\nimport { getCurrentPageRoute, navjump, registerEachHooks, rewriteNavMethod, saveCurrRouteByCurrPage } from './router'\r\nimport type { AfterEachGuard, BeforeEachGuard, Route, RouteBackLocation, RouteLocationRaw, Router, RouterOptions } from './interfaces'\r\nimport { shallowRef, unref } from 'vue'\r\nimport { isEmptyObject } from './utils'\r\n/**\r\n * Creates a Router instance that can be used by a Vue app.\r\n *\r\n */\r\nexport function createRouter(options: RouterOptions): Router {\r\n  const router: Router = {\r\n    routes: options.routes,\r\n    guardHooks: {\r\n      beforeHooks: null,\r\n      afterHooks: null\r\n    },\r\n    push(to: RouteLocationRaw) {\r\n      return navjump(to, this, 'push')\r\n    },\r\n    replace(to: RouteLocationRaw) {\r\n      return navjump(to, this, 'replace')\r\n    },\r\n    replaceAll(to: RouteLocationRaw) {\r\n      return navjump(to, this, 'replaceAll')\r\n    },\r\n    pushTab(to: RouteLocationRaw) {\r\n      return navjump(to, this, 'pushTab')\r\n    },\r\n    back(to?: RouteBackLocation) {\r\n      return uni.navigateBack(to)\r\n    },\r\n    beforeEach(userGuard: BeforeEachGuard) {\r\n      registerEachHooks(router, 'beforeHooks', userGuard)\r\n    },\r\n    afterEach(userGuard: AfterEachGuard) {\r\n      registerEachHooks(router, 'afterHooks', userGuard)\r\n    },\r\n    install: function (app: any): void {\r\n      const router = this\r\n      app.provide(routerKey, this)\r\n      app.provide(routeKey, this.route)\r\n      rewriteNavMethod(router)\r\n      app.mixin({\r\n        beforeCreate() {\r\n          if (this.$mpType === 'page') {\r\n            if (router.guardHooks.afterHooks && router.guardHooks.afterHooks[0]) {\r\n              const from: Route = router.route.value\r\n              const to: Route = getCurrentPageRoute(router) // 当前页面路由信息\r\n              router.guardHooks.afterHooks[0].call(null, to, from)\r\n            }\r\n          }\r\n        },\r\n        onLoad(option: Record<string, any> | undefined) {\r\n          if (!isEmptyObject(option) && isEmptyObject(router.route.value.query) && isEmptyObject(router.route.value.params)) {\r\n            router.route.value = { ...router.route.value, query: option }\r\n          }\r\n        },\r\n        onShow() {\r\n          if (this.$mpType === 'page') {\r\n            saveCurrRouteByCurrPage(router)\r\n          }\r\n        }\r\n      })\r\n      Object.defineProperty(app.config.globalProperties, '$Router', {\r\n        get() {\r\n          return router\r\n        }\r\n      })\r\n      Object.defineProperty(app.config.globalProperties, '$Route', {\r\n        enumerable: true,\r\n        get: () => unref(this.route)\r\n      })\r\n    },\r\n    route: shallowRef<Route>({ path: '/' })\r\n  }\r\n  return router\r\n}\r\n\r\nexport * from './core'\r\nexport * from './interfaces'\r\n"], "names": ["navjump", "uni", "registerEachHooks", "router", "routerKey", "routeKey", "rewriteNavMethod", "getCurrentPageRoute", "isEmptyObject", "saveCurrRouteByCurrPage", "unref", "shallowRef"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAkBO,SAAS,aAAa,SAAgC;AAC3D,QAAM,SAAiB;AAAA,IACrB,QAAQ,QAAQ;AAAA,IAChB,YAAY;AAAA,MACV,aAAa;AAAA,MACb,YAAY;AAAA,IACd;AAAA,IACA,KAAK,IAAsB;AAClB,aAAAA,0CAAQ,IAAI,MAAM,MAAM;AAAA,IACjC;AAAA,IACA,QAAQ,IAAsB;AACrB,aAAAA,0CAAQ,IAAI,MAAM,SAAS;AAAA,IACpC;AAAA,IACA,WAAW,IAAsB;AACxB,aAAAA,0CAAQ,IAAI,MAAM,YAAY;AAAA,IACvC;AAAA,IACA,QAAQ,IAAsB;AACrB,aAAAA,0CAAQ,IAAI,MAAM,SAAS;AAAA,IACpC;AAAA,IACA,KAAK,IAAwB;AACpB,aAAAC,cAAA,MAAI,aAAa,EAAE;AAAA,IAC5B;AAAA,IACA,WAAW,WAA4B;AACnBC,0DAAA,QAAQ,eAAe,SAAS;AAAA,IACpD;AAAA,IACA,UAAU,WAA2B;AACjBA,0DAAA,QAAQ,cAAc,SAAS;AAAA,IACnD;AAAA,IACA,SAAS,SAAU,KAAgB;AACjC,YAAMC,UAAS;AACX,UAAA,QAAQC,8CAAW,IAAI;AACvB,UAAA,QAAQC,6CAAU,KAAK,KAAK;AAChCC,wCAAAA,iBAAiBH,OAAM;AACvB,UAAI,MAAM;AAAA,QACR,eAAe;AACT,cAAA,KAAK,YAAY,QAAQ;AAC3B,gBAAIA,QAAO,WAAW,cAAcA,QAAO,WAAW,WAAW,CAAC,GAAG;AAC7D,oBAAA,OAAcA,QAAO,MAAM;AAC3B,oBAAA,KAAYI,sDAAoBJ,OAAM;AAC5CA,sBAAO,WAAW,WAAW,CAAC,EAAE,KAAK,MAAM,IAAI,IAAI;AAAA,YAAA;AAAA,UACrD;AAAA,QAEJ;AAAA,QACA,OAAO,QAAyC;AAC9C,cAAI,CAACK,iCAAA,cAAc,MAAM,KAAKA,iCAAAA,cAAcL,QAAO,MAAM,MAAM,KAAK,KAAKK,iCAAcL,cAAAA,QAAO,MAAM,MAAM,MAAM,GAAG;AACjHA,oBAAO,MAAM,QAAQ,iCAAKA,QAAO,MAAM,QAAlB,EAAyB,OAAO,OAAO;AAAA,UAAA;AAAA,QAEhE;AAAA,QACA,SAAS;AACH,cAAA,KAAK,YAAY,QAAQ;AAC3BM,8CAAAA,wBAAwBN,OAAM;AAAA,UAAA;AAAA,QAChC;AAAA,MACF,CACD;AACD,aAAO,eAAe,IAAI,OAAO,kBAAkB,WAAW;AAAA,QAC5D,MAAM;AACGA,iBAAAA;AAAAA,QAAA;AAAA,MACT,CACD;AACD,aAAO,eAAe,IAAI,OAAO,kBAAkB,UAAU;AAAA,QAC3D,YAAY;AAAA,QACZ,KAAK,MAAMO,oBAAM,KAAK,KAAK;AAAA,MAAA,CAC5B;AAAA,IACH;AAAA,IACA,OAAOC,cAAA,WAAkB,EAAE,MAAM,IAAK,CAAA;AAAA,EACxC;AACO,SAAA;AACT;;"}