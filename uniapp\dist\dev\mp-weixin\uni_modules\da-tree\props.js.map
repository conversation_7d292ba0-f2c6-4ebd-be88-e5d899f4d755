{"version": 3, "file": "props.js", "sources": ["../../../../../src/uni_modules/da-tree/props.ts"], "sourcesContent": ["export default {\r\n  /**\r\n   * 树的数据\r\n   */\r\n  data: {\r\n    type: Array,\r\n    default: () => [],\r\n  },\r\n  /**\r\n   * 主题色\r\n   */\r\n  themeColor: {\r\n    type: String,\r\n    default: '#007aff',\r\n  },\r\n  /**\r\n   * 是否开启多选，默认单选\r\n   */\r\n  showCheckbox: {\r\n    type: Boolean,\r\n    default: false,\r\n  },\r\n  /**\r\n   * 默认选中的节点，注意单选时为单个key，多选时为key的数组\r\n   */\r\n  defaultCheckedKeys: {\r\n    type: [Array, String, Number],\r\n    default: null,\r\n  },\r\n  /**\r\n   * 是否默认展开全部\r\n   */\r\n  defaultExpandAll: {\r\n    type: Boolean,\r\n    default: false,\r\n  },\r\n  /**\r\n   * 默认展开的节点\r\n   */\r\n  defaultExpandedKeys: {\r\n    type: Array,\r\n    default: null,\r\n  },\r\n  /**\r\n   * 筛选关键词\r\n   */\r\n  filterValue: {\r\n    type: String,\r\n    default: '',\r\n  },\r\n  /**\r\n   * 是否自动展开到选中的节点，默认不展开\r\n   */\r\n  expandChecked: {\r\n    type: Boolean,\r\n    default: false,\r\n  },\r\n\r\n  /**\r\n   * (旧)字段对应内容，默认为 {label: 'label',key: 'key', children: 'children', disabled: 'disabled', append: 'append'}\r\n   * 注意：1.5.0版本后不再兼容\r\n   */\r\n  field: {\r\n    type: Object,\r\n    default: null,\r\n  },\r\n  /**\r\n   * 标签字段(新，拆分了)\r\n   */\r\n  labelField: {\r\n    type: String,\r\n    default: 'label',\r\n  },\r\n  /**\r\n   * 值字段(新，拆分了)\r\n   */\r\n  valueField: {\r\n    type: String,\r\n    default: 'value',\r\n  },\r\n  /**\r\n   * 下级字段(新，拆分了)\r\n   */\r\n  childrenField: {\r\n    type: String,\r\n    default: 'children',\r\n  },\r\n  /**\r\n   * 禁用字段(新，拆分了)\r\n   */\r\n  disabledField: {\r\n    type: String,\r\n    default: 'disabled',\r\n  },\r\n  /**\r\n   * 末级节点字段(新，拆分了)\r\n   */\r\n  leafField: {\r\n    type: String,\r\n    default: 'leaf',\r\n  },\r\n  /**\r\n   * 副标签字段(新，拆分了)\r\n   */\r\n  appendField: {\r\n    type: String,\r\n    default: 'append',\r\n  },\r\n  /**\r\n   * 排序字段(新，拆分了)\r\n   */\r\n  sortField: {\r\n    type: String,\r\n    default: 'sort',\r\n  },\r\n  /**\r\n   * Api数据返回后的结果路径，支持嵌套如`data.list`\r\n   */\r\n  resultField: {\r\n    type: String,\r\n    default: '',\r\n  },\r\n  isLeafFn: {\r\n    type: Function,\r\n    default: null,\r\n  },\r\n  /**\r\n   * 是否显示单选图标，默认显示\r\n   */\r\n  showRadioIcon: {\r\n    type: Boolean,\r\n    default: true,\r\n  },\r\n  /**\r\n   * 单选时只允许选中末级，默认可随意选中\r\n   */\r\n  onlyRadioLeaf: {\r\n    type: Boolean,\r\n    default: false,\r\n  },\r\n  /**\r\n   * 多选时，是否执行父子不关联的任意勾选，默认父子关联\r\n   */\r\n  checkStrictly: {\r\n    type: Boolean,\r\n    default: false,\r\n  },\r\n  /**\r\n   * 为 true 时，空的 children 数组会显示展开图标\r\n   */\r\n  loadMode: {\r\n    type: Boolean,\r\n    default: false,\r\n  },\r\n  /**\r\n   * 异步加载接口\r\n   */\r\n  loadApi: {\r\n    type: Function,\r\n    default: null,\r\n  },\r\n  /**\r\n   * 是否总在首次的时候加载一下内容，来比对是否一致\r\n   */\r\n  alwaysFirstLoad: {\r\n    type: Boolean,\r\n    default: false,\r\n  },\r\n  /**\r\n   * 是否渲染(操作)禁用值\r\n   */\r\n  checkedDisabled: {\r\n    type: Boolean,\r\n    default: false,\r\n  },\r\n  /**\r\n   * 是否返回已禁用的但已选中的key\r\n   */\r\n  packDisabledkey: {\r\n    type: Boolean,\r\n    default: true,\r\n  },\r\n  /**\r\n   * 选择框的位置，可选 left/right\r\n   */\r\n  checkboxPlacement: {\r\n    type: String,\r\n    default: 'left',\r\n  },\r\n  /**\r\n   * 子项缩进距离，默认40，单位rpx\r\n   */\r\n  indent: {\r\n    type: Number,\r\n    default: 40,\r\n  },\r\n}\r\n"], "names": [], "mappings": ";AAAA,MAAe,aAAA;AAAA;AAAA;AAAA;AAAA,EAIb,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,SAAS,MAAM,CAAA;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AAAA,IACZ,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,oBAAoB;AAAA,IAClB,MAAM,CAAC,OAAO,QAAQ,MAAM;AAAA,IAC5B,SAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB;AAAA,IAChB,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,qBAAqB;AAAA,IACnB,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa;AAAA,IACX,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AAAA,IACT,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa;AAAA,IACX,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AAAA,IACT,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa;AAAA,IACX,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AAAA,IACR,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AAAA,IACP,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AAAA,IACf,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AAAA,IACf,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AAAA,IACf,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,mBAAmB;AAAA,IACjB,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,EAAA;AAEb;;"}