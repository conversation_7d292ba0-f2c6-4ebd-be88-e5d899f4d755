{"version": 3, "file": "index.js", "sources": ["../../../../../src/pages/index/index.vue", "../../../../../uniPage:/cGFnZXMvaW5kZXgvaW5kZXgudnVl"], "sourcesContent": ["<!-- 使用 type=\"home\" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->\r\n\r\n<template>\n<layout-default-uni >\r\n  <PageLayout :navLeftArrow=\"false\" navLeftText=\"\">\r\n    <!--轮播图-->\r\n    <!-- prettier-ignore -->\r\n    <scroll-view class=\"scrollView\" :scroll-y=\"true\" scroll-with-animation>\r\n      <view class=\"swiper-title\">\r\n        <view class=\"dot\"></view>\r\n        <wd-text :text=\"currentSwiperTitle\"></wd-text>\r\n      </view>\r\n      <swiper class=\"swiper\" :indicator-dots=\"true\" :circular=\"true\" :autoplay=\"true\" :interval=\"5000\" :duration=\"500\" @change=\"onSwiperChange\">\r\n        <swiper-item v-for=\"(item, index) in carouselList\" :key=\"index\" @click=\"onSwiperClick(item)\">\r\n          <image :src=\"item.imageUrl\" mode=\"aspectFill\" v-if=\"item.imageUrl\"></image>\r\n        </swiper-item>\r\n      </swiper>\r\n      <view class=\"serveBox\">\r\n        <!-- 注册按钮容器 - 当社工已经注册时不显示整个容器 -->\r\n        <view class=\"registration-button-container\"\r\n          v-if=\"!(Number(userStore.userInfo.userCategory) === 2 && isSocialRole)\">\r\n          <!-- 知情同意与登记按钮 - 仅未登记的患者端可见 -->\r\n          <button class=\"registration-button\" @click=\"goToRegistration\"\r\n            v-if=\"Number(userStore.userInfo.userCategory) === 1 && !isPatientRole\">知情同意与登记</button>\r\n          <!-- 医生注册按钮 - 仅未注册的医生端可见 -->\r\n          <button class=\"registration-button\" @click=\"goToDoctorRegistration\"\r\n            v-if=\"Number(userStore.userInfo.userCategory) === 0 && !isDocterRole\">医生注册</button>\r\n          <!-- 社工注册按钮 - 仅未注册的社工端可见 -->\r\n          <button class=\"registration-button\" @click=\"goToSocialWorkerRegistration\"\r\n            v-if=\"Number(userStore.userInfo.userCategory) === 2 && !isSocialRole\">社工/社区医生注册</button>\r\n          <!-- 医患沟通按钮 - 仅患者端、医生端且已注册/登记可见 -->\r\n          <button class=\"registration-button\" @click=\"goToChat\"\r\n            v-if=\"Number(userStore.userInfo.userCategory) !== 2 && hasCommAndDataPermission\">医患沟通</button>\r\n        </view>\r\n        <!-- 注册状态提示信息 -->\r\n        <view class=\"registration-tip\" v-if=\"(Number(userStore.userInfo.userCategory) === 0 && !isDocterRole) ||\r\n          (Number(userStore.userInfo.userCategory) === 1 && !isPatientRole) ||\r\n          (Number(userStore.userInfo.userCategory) === 2 && !isSocialRole)\">\r\n          <text class=\"tip-text\">\r\n            <template v-if=\"Number(userStore.userInfo.userCategory) === 0 && !isDocterRole\">请先进行医生注册！</template>\r\n            <template v-if=\"Number(userStore.userInfo.userCategory) === 1 && !isPatientRole\">请先填写知情同意与登记！</template>\r\n            <template v-if=\"Number(userStore.userInfo.userCategory) === 2 && !isSocialRole\">请先进行社工/社区医生注册！</template>\r\n          </text>\r\n        </view>\r\n\r\n        <!-- 只有当用户有权限时才显示数据标题栏和数据内容 -->\r\n        <view v-if=\"hasCommAndDataPermission\" class=\"data-section\">\r\n          <view class=\"title\">\r\n            <view class=\"dot\"></view>\r\n            <wd-text :text=\"Number(userStore.userInfo.userCategory) === 1 ? '近期录入数据' : '患者近期记录'\"></wd-text>\r\n            <view class=\"view-more\" @click=\"goToDataPage\">查看更多 > </view>\r\n          </view>\r\n\r\n          <view class=\"recent-data\">\r\n            <view class=\"loading-container\" v-if=\"loadingRecentData\">\r\n              <wd-loading />\r\n            </view>\r\n\r\n            <view v-else-if=\"recentDataList.length === 0\" class=\"empty-data\">\r\n              <wd-text text=\"暂无记录\"></wd-text>\r\n            </view>\r\n\r\n            <view v-else class=\"data-items\">\r\n              <view class=\"data-card\" v-for=\"(item, index) in recentDataList\" :key=\"index\"\r\n                @click=\"viewDataDetail(item)\">\r\n                <view class=\"data-info\">\r\n                  <view class=\"data-icon\">\r\n                    <image :src=\"item.iconPath\" class=\"card-image\" mode=\"contain\"></image>\r\n                  </view>\r\n                  <view class=\"data-details\">\r\n                    <view class=\"data-title-row\">\r\n                      <text class=\"data-type\">{{ item.type }}</text>\r\n                    </view>\r\n                    <view class=\"data-content\">\r\n                      <view class=\"data-fields\">\r\n                        <view class=\"data-field\" v-if=\"Number(userStore.userInfo.userCategory) !== 1\">\r\n                          <text class=\"field-value\">姓名：{{ item.userName }}</text>\r\n                        </view>\r\n                        <view class=\"data-field\">\r\n                          <text class=\"field-value\">{{ item.createTime }}</text>\r\n                        </view>\r\n                      </view>\r\n                    </view>\r\n                  </view>\r\n                </view>\r\n                <view class=\"data-action\">\r\n                  <text class=\"action-text\">查看详情</text>\r\n                  <uni-icons type=\"right\" size=\"16\" color=\"#07C160\"></uni-icons>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </scroll-view>\r\n  </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\n// 当前轮播图索引\r\nconst currentSwiper = ref(0)\r\n// 当前轮播图标题\r\nconst currentSwiperTitle = computed(() => {\r\n  if (carouselList.value.length === 0) return ''\r\n  return carouselList.value[currentSwiper.value]?.title || ''\r\n})\r\n\r\n// 轮播图切换事件\r\nconst onSwiperChange = (e) => {\r\n  currentSwiper.value = e.detail.current\r\n}\r\nimport { ref, onMounted, computed } from 'vue'\r\nimport { TestEnum } from '@/typings'\r\nimport { us, os } from '@/common/work'\r\n// 获取当前运行平台\r\nimport PLATFORM from '@/utils/platform'\r\nimport { cache, getFileAccessHttpUrl, hasRoute } from '@/common/uitls'\r\nimport { onLaunch, onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'\r\nimport { useToast, useMessage, useNotify } from 'wot-design-uni'\r\nimport { useRouter } from '@/plugin/uni-mini-router'\r\nimport { useUserStore } from '@/store/user'\r\n// import Grid from '@/components/Grid/Grid.vue'\r\n\r\nimport {\r\n  ACCESS_TOKEN,\r\n  USER_NAME,\r\n  USER_INFO,\r\n  APP_ROUTE,\r\n  APP_CONFIG,\r\n  HOME_CONFIG_EXPIRED_TIME,\r\n} from '@/common/constants'\r\nimport { http } from '@/utils/http'\r\nimport request from '@/utils/request'\r\n\r\ndefineOptions({\r\n  name: 'index',\r\n  options: {\r\n    // apply-shared‌：当前页面样式会影响到子组件样式.(小程序)\r\n    // shared‌：当前页面样式影响到子组件，子组件样式也会影响到当前页面.(小程序)\r\n    styleIsolation: 'shared',\r\n  },\r\n})\r\nconst toast = useToast()\r\nconst router = useRouter()\r\nconst userStore = useUserStore()\r\n// 获取屏幕边界到安全区域距离\r\nconst { safeAreaInsets } = uni.getSystemInfoSync()\r\n// 轮播图只用线上宣教资源接口\r\nconst isLocalConfig = false\r\nconst carouselList = ref([]) // 轮播图数据\r\nconst swiperList = ref([]) // 保留原变量，兼容模板\r\n\r\nconst token = uni.getStorageSync('token') // 或你实际存储 token 的 key\r\n\r\n// 获取文件真实地址（fileUrl）\r\nconst getRealFileUrl = async (objectName: string) => {\r\n  console.log('获取文件URL，objectName:', objectName)\r\n  try {\r\n    const res = await uni.request({\r\n      url: `${import.meta.env.VITE_SERVER_BASEURL}/file/url`,\r\n      method: 'GET',\r\n      data: { objectName },\r\n      header: {\r\n        'X-Access-Token': userStore.userInfo.token\r\n      }\r\n    })\r\n    let data = res && res.data ? res.data : (Array.isArray(res) && res[1] && res[1].data ? res[1].data : null)\r\n    if (data && data.success && data.result && data.result.fileUrl) {\r\n      return data.result.fileUrl\r\n    } else {\r\n      console.warn('响应数据格式异常:', data)\r\n      return ''\r\n    }\r\n  } catch (error) {\r\n    console.error('获取文件URL失败:', error)\r\n    return ''\r\n  }\r\n}\r\n// 获取宣教资源轮播图数据\r\nconst fetchCarouselList = async () => {\r\n  const token = userStore.userInfo.token\r\n  console.log('当前TOKEN:', token)\r\n  const res = await uni.request({\r\n    url: `${import.meta.env.VITE_SERVER_BASEURL}/educationResource/showList`,\r\n    method: 'GET',\r\n    header: {\r\n      'X-Access-Token': userStore.userInfo.token\r\n    }\r\n  })\r\n  // 兼容小程序端（res为对象）和H5端（res为数组）\r\n  console.log('获取宣教资源轮播图数据:', res)\r\n  let data = res && res.data ? res.data : (Array.isArray(res) && res[1] && res[1].data ? res[1].data : null)\r\n  if (data && data.success && Array.isArray(data.result)) {\r\n    const posts = data.result\r\n    // 并发获取图片真实地址\r\n      const imgPromises = posts.map(item => item.image ? getRealFileUrl(item.image) : '')\r\n      const imgUrls = await Promise.all(imgPromises)\r\n    //并发获取视频内容真实地址\r\n      const videoPromises = posts.map(item => item.videoName ? getRealFileUrl(item.videoName) : '')\r\n      const videoUrls = await Promise.all(videoPromises)\r\n\r\n    // 组装轮播图数据，全部展示（即使fileUrl为空也显示）\r\n    carouselList.value = posts.map((item, idx) => ({\r\n      ...item,\r\n      imageUrl: imgUrls[idx],\r\n      videoUrl: videoUrls[idx]\r\n    }))\r\n  } else {\r\n    carouselList.value = []\r\n  }\r\n}\r\n// 轮播图点击事件\r\nconst onSwiperClick = (item) => {\r\n  uni.navigateTo({\r\n    url: `/pages/index/postDetail?title=${encodeURIComponent(item.title)}&videoUrl=${encodeURIComponent(item.videoUrl)}&content=${encodeURIComponent(item.content)}&type=${encodeURIComponent(item.type)}&author=${encodeURIComponent(item.author)}&createTime=${encodeURIComponent(item.createTime)}&updateTime=${encodeURIComponent(item.updateTime)}`\r\n  })\r\n}\r\nconst msgCount = ref(0)\r\nconst dot = ref({ mailHome: false })\r\nconst recentDataList = ref([])\r\nconst loadingRecentData = ref(false)\r\n\r\n// 计算属性：判断当前用户是否为患者角色\r\nconst isPatientRole = computed(() => {\r\n  return userStore.userInfo.roleList?.includes('1') || false\r\n})\r\n\r\nconst isDocterRole = computed(() => {\r\n  return userStore.userInfo.roleList?.includes('0') || false\r\n})\r\n\r\nconst isSocialRole = computed(() => {\r\n  return userStore.userInfo.roleList?.includes('2') || false\r\n})\r\n\r\n// 计算属性：判断用户是否有医患沟通和查看数据的权限\r\nconst hasCommAndDataPermission = computed(() => {\r\n  // 获取当前用户角色列表\r\n  const userRoles = userStore.userInfo.roleList || []\r\n  // 获取用户类别：0-医生，1-患者，2-社工\r\n  const userCategory = Number(userStore.userInfo.userCategory)\r\n\r\n  // 判断逻辑：\r\n  // 1. 医生端(userCategory=0)：只有具有医生角色(roleList包含'0')才能查看数据\r\n  // 2. 患者端(userCategory=1)：只有具有患者角色(roleList包含'1')才能查看数据\r\n  // 3. 社工端(userCategory=1)：只有具有社工角色(roleList包含'2')才能查看数据\r\n  if (userCategory === 0) {\r\n    // 医生必须完成注册（具有医生角色）才能查看数据\r\n    return userRoles.includes('0')\r\n  } else if (userCategory === 1) {\r\n    // 患者必须完成登记（具有患者角色）才能查看数据\r\n    return userRoles.includes('1')\r\n  } else if (userCategory === 2) {\r\n    // 社工必须完成注册（具有社工角色）才能查看数据\r\n    return userRoles.includes('2')\r\n  }\r\n\r\n  return false\r\n})\r\n\r\nconst getAppConfigRoute = () => {\r\n  //判断是否过期\r\n  let config = cache(APP_CONFIG)\r\n  if (config) {\r\n    homeConfig()\r\n  } else {\r\n    //更新首页配置\r\n    http.get('/eoa/sysAppConfig/queryAppConfigRoute').then((res: any) => {\r\n      console.log('更新首页配置res', res)\r\n      let result = res\r\n      if (result.success) {\r\n        cache(APP_ROUTE, result.result.route, HOME_CONFIG_EXPIRED_TIME)\r\n        cache(APP_CONFIG, result.result.config, HOME_CONFIG_EXPIRED_TIME)\r\n        homeConfig()\r\n      }\r\n    })\r\n  }\r\n}\r\nconst homeConfig = () => {\r\n  var indexRouteList = cache(APP_ROUTE)\r\n  var appConfig = cache(APP_CONFIG)\r\n  let carouselImgStr = appConfig[0].carouselImgJson\r\n  var carouselImgArr = carouselImgStr && carouselImgStr.length > 0 ? carouselImgStr.split(',') : []\r\n  carouselList.value = carouselImgArr\r\n}\r\n\r\n// 根据数据类型返回对应的图标\r\nconst getIconByType = (type) => {\r\n  const iconMap = {\r\n    '日常体征监测': 'https://www.mograine.cn/images/vital-signs.png',\r\n    '用药情况': 'https://www.mograine.cn/images/medication.png',\r\n    '院外检查报告': 'https://www.mograine.cn/images/report.png',\r\n    '监测表': 'https://www.mograine.cn/images/monitor.png',\r\n    '心理量表': 'https://www.mograine.cn/images/psychology.png',\r\n    '日常生活指数评估': 'https://www.mograine.cn/images/dailyLife.png',\r\n    '知情同意与登记表': 'https://www.mograine.cn/images/registration.png',\r\n  }\r\n\r\n  // 返回默认图标\r\n  return iconMap[type]\r\n}\r\n\r\nconst goToDataPage = () => {\r\n  uni.navigateTo({\r\n    url: '/pages/index/data'\r\n  })\r\n}\r\n\r\nconst viewDataDetail = (item) => {\r\n  console.log('查看详情，数据类型:', item.type);\r\n\r\n  // 根据数据类型跳转到不同的表单页面\r\n  let targetUrl = '';\r\n  let urlParams = '';\r\n\r\n  switch (item.type) {\r\n    case '日常体征监测':\r\n      targetUrl = '/pages-data/vital-signs/form';\r\n      // 常规数据处理：先保存ID到本地存储\r\n      uni.setStorageSync('detail_query', { id: item.id });\r\n      urlParams = `id=${item.id}&mode=view`;\r\n      break;\r\n    case '用药情况':\r\n      targetUrl = '/pages-data/medication/form';\r\n      // 用药情况需要传递idList参数\r\n      urlParams = `idList=${item.idList.join(',')}&mode=view`;\r\n      break;\r\n    case '院外检查报告':\r\n      targetUrl = '/pages-data/examinationReport/form';\r\n      // 院外检查报告需要传递idList参数\r\n      urlParams = `idList=${item.idList.join(',')}&mode=view`;\r\n      break;\r\n    case '监测表':\r\n      targetUrl = '/pages-data/monitor/form';\r\n      // 常规数据处理：先保存ID到本地存储\r\n      uni.setStorageSync('detail_query', { id: item.id });\r\n      urlParams = `id=${item.id}&mode=view`;\r\n      break;\r\n    case '心理量表':\r\n      targetUrl = '/pages-data/psychology/form';\r\n      // 常规数据处理：先保存ID到本地存储\r\n      uni.setStorageSync('detail_query', { id: item.id });\r\n      urlParams = `id=${item.id}&mode=view`;\r\n      break;\r\n    case '日常生活指数评估':\r\n      targetUrl = '/pages-data/dailyLife/form';\r\n      // 常规数据处理：先保存ID到本地存储\r\n      uni.setStorageSync('detail_query', { id: item.id });\r\n      urlParams = `id=${item.id}&mode=view`;\r\n      break;\r\n    // case '知情同意与登记表':\r\n    //   targetUrl = '/pages-data/registration/form';\r\n    //   // 常规数据处理：先保存ID到本地存储\r\n    //   uni.setStorageSync('detail_query', { id: item.id });\r\n    //   urlParams = `id=${item.id}&mode=view`;\r\n    //   break;\r\n    default:\r\n      targetUrl = '';\r\n      urlParams = '';\r\n  }\r\n\r\n  console.log('跳转到页面:', targetUrl);\r\n\r\n  // 跳转到对应页面\r\n  if (targetUrl) {\r\n    uni.navigateTo({\r\n      url: `${targetUrl}?${urlParams}`\r\n    })\r\n  }\r\n}\r\n\r\nconst getRecentData = () => {\r\n  // 如果用户没有权限（未注册或未登记），则不请求数据\r\n  if (!hasCommAndDataPermission.value) {\r\n    recentDataList.value = [];\r\n    return;\r\n  }\r\n\r\n  loadingRecentData.value = true\r\n\r\n  // 构造请求参数，只有患者端才传递userId\r\n  const params: Record<string, any> = {}\r\n  if (Number(userStore.userInfo.userCategory) === 1) {\r\n    params.userId = userStore.userInfo.userid\r\n  }\r\n\r\n  http.get('/patient/getalldata', params).then((res: any) => {\r\n    if (res.success && res.result) {\r\n      // 处理新的数据结构，从 res.result.records 获取数据，前端限制只显示5条\r\n      recentDataList.value = (res.result.records || [])\r\n        // 过滤掉\"知情同意与登记表\"类型的数据\r\n        .filter(item => item.type !== '知情同意与登记表')\r\n        .slice(0, 5)\r\n        .map(item => {\r\n          // 根据type获取相应的图标\r\n          const iconPath = getIconByType(item.type)\r\n          return {\r\n            ...item,\r\n            typeName: item.type,\r\n            iconPath: iconPath,\r\n          }\r\n        })\r\n    } else {\r\n      console.error('获取近期数据失败', res.message)\r\n    }\r\n  }).finally(() => {\r\n    loadingRecentData.value = false\r\n  })\r\n}\r\n\r\nconst goToRegistration = () => {\r\n  uni.navigateTo({\r\n    url: '/pages-data/registration/consent'\r\n  })\r\n}\r\n\r\nconst goToDoctorRegistration = () => {\r\n  uni.navigateTo({\r\n    url: '/pages-data/doctor/form'\r\n  })\r\n}\r\n\r\nconst goToSocialWorkerRegistration = () => {\r\n  uni.navigateTo({\r\n    url: '/pages-data/social/form'\r\n  })\r\n}\r\n\r\nconst goToChat = () => {\r\n  uni.navigateTo({\r\n    url: '/pages-data/chat/chat'\r\n  })\r\n}\r\n\r\n\r\n// 轮播图只用线上接口\r\n\r\n// 在组件mounted时获取数据\r\nonMounted(() => {\r\n  fetchCarouselList()\r\n  getRecentData()\r\n})\r\n\r\n// 每次页面显示时重新获取数据，确保从其他页面返回时数据是最新的\r\nonShow(() => {\r\n  fetchCarouselList()\r\n  getRecentData()\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.swiper-title {\r\n  display: flex;\r\n  align-items: center;\r\n  padding-left: 30upx;\r\n  height: 52px;\r\n  background-color: #fff;\r\n  position: relative;\r\n\r\n  .dot {\r\n    width: 14upx;\r\n    height: 14upx;\r\n    background-color: #0081ff;\r\n    border-radius: 100%;\r\n    margin-right: 20upx;\r\n  }\r\n\r\n  .wd-text {\r\n    color: #666;\r\n    font-size: 15px;\r\n  }\r\n}\r\n\r\n.swiper {\r\n  height: 375upx;\r\n  flex: none;\r\n\r\n  image,\r\n  video {\r\n    width: 100%;\r\n    display: block;\r\n    height: 100%;\r\n    margin: 0;\r\n  }\r\n\r\n  :deep(.uni-swiper-dot) {\r\n    transition: all 400ms ease;\r\n    background-color: rgba(255, 255, 255, 0.4);\r\n    width: 5px;\r\n    height: 5px;\r\n    border-radius: 50%;\r\n    margin: 0 4px;\r\n  }\r\n\r\n  :deep(.uni-swiper-dot-active) {\r\n    background-color: rgba(255, 255, 255, 1);\r\n    width: 16px;\r\n    border-radius: 2px;\r\n  }\r\n}\r\n\r\n.scrollView {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 0;\r\n  background-color: #f1f1f1;\r\n\r\n  :deep(.wd-row) {\r\n    background-color: #fff;\r\n    margin-bottom: 32upx;\r\n\r\n    .wd-col {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n\r\n      &:first-child {\r\n        border-right: 1px solid rgba(165, 165, 165, 0.1);\r\n      }\r\n\r\n      .wd-img {\r\n        margin: 20upx;\r\n        margin-left: 0;\r\n      }\r\n\r\n      .textBox {\r\n        text-align: center;\r\n        display: flex;\r\n        flex-direction: column;\r\n\r\n        .wd-text {\r\n          color: #666;\r\n\r\n          &:last-child {\r\n            font-weight: 200;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .serveBox {\r\n    margin-bottom: 32upx;\r\n    background-color: #fff;\r\n\r\n    &:last-child {\r\n      .title {\r\n        .dot {\r\n          background-color: #fbbd08;\r\n        }\r\n      }\r\n    }\r\n\r\n    :deep(.wd-grid-item) {\r\n      &:not(.enabled) {\r\n        opacity: 0.5;\r\n      }\r\n    }\r\n\r\n    .title {\r\n      display: flex;\r\n      align-items: center;\r\n      padding-left: 30upx;\r\n      height: 52px;\r\n      position: relative;\r\n\r\n      .dot {\r\n        width: 14upx;\r\n        height: 14upx;\r\n        background-color: #0081ff;\r\n        border-radius: 100%;\r\n        margin-right: 20upx;\r\n      }\r\n\r\n      .wd-text {\r\n        color: #666;\r\n        font-size: 15px;\r\n      }\r\n\r\n      .view-more {\r\n        position: absolute;\r\n        right: 30upx;\r\n        color: #999;\r\n        font-size: 14px;\r\n      }\r\n    }\r\n\r\n    .recent-data {\r\n      padding: 0 20upx 20upx;\r\n      position: relative;\r\n      min-height: 200rpx;\r\n\r\n      .loading-container {\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        position: absolute;\r\n        top: 0;\r\n        left: 0;\r\n        width: 100%;\r\n        height: 100%;\r\n        z-index: 10;\r\n      }\r\n\r\n      .empty-data {\r\n        padding: 30upx 0;\r\n        text-align: center;\r\n        color: #999;\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n\r\n        .empty-image {\r\n          width: 200rpx;\r\n          height: 200rpx;\r\n          margin-bottom: 20rpx;\r\n        }\r\n      }\r\n\r\n      .data-items {\r\n        .data-card {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          padding: 24rpx;\r\n          background-color: white;\r\n          border-radius: 16rpx;\r\n          margin-bottom: 16rpx;\r\n          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\r\n          transition: all 0.3s;\r\n\r\n          &:active {\r\n            transform: scale(0.99);\r\n            background-color: #fafafa;\r\n          }\r\n\r\n          .data-info {\r\n            display: flex;\r\n            align-items: flex-start;\r\n            flex: 1;\r\n\r\n            .data-icon {\r\n              margin-right: 16rpx;\r\n              background-color: rgba(7, 193, 96, 0.1);\r\n              width: 80rpx;\r\n              height: 80rpx;\r\n              border-radius: 50%;\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: center;\r\n              flex-shrink: 0;\r\n            }\r\n\r\n            .card-image {\r\n              width: 50rpx;\r\n              height: 50rpx;\r\n              display: block;\r\n            }\r\n\r\n            .data-details {\r\n              flex: 1;\r\n              overflow: hidden;\r\n\r\n              .data-title-row {\r\n                display: flex;\r\n                justify-content: space-between;\r\n                margin-bottom: 8rpx;\r\n\r\n                .data-type {\r\n                  font-size: 30rpx;\r\n                  color: #333333;\r\n                  font-weight: 500;\r\n                }\r\n              }\r\n\r\n              .data-content {\r\n                .data-fields {\r\n                  display: flex;\r\n                  flex-direction: column;\r\n\r\n                  .data-field {\r\n                    margin-top: 4rpx;\r\n\r\n                    .field-value {\r\n                      color: #666666;\r\n                      font-size: 24rpx;\r\n                      display: block;\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n\r\n          .data-action {\r\n            display: flex;\r\n            align-items: center;\r\n            margin-left: 20rpx;\r\n\r\n            .action-text {\r\n              color: #07C160;\r\n              font-size: 26rpx;\r\n              margin-right: 10rpx;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.registration-button-container {\r\n  padding: 20upx;\r\n  display: flex;\r\n  justify-content: space-between;\r\n\r\n  .registration-button {\r\n    background-color: #07C160;\r\n    color: #fff;\r\n    padding: 15upx 30upx;\r\n    border-radius: 8upx;\r\n    font-size: 28upx;\r\n    width: 100%;\r\n    text-align: center;\r\n    box-shadow: 0 4rpx 8rpx rgba(7, 193, 96, 0.2);\r\n    transition: all 0.3s;\r\n    margin: 0 5upx;\r\n\r\n    &:active {\r\n      transform: scale(0.98);\r\n      box-shadow: 0 2rpx 4rpx rgba(7, 193, 96, 0.2);\r\n    }\r\n\r\n    &.disabled {\r\n      background-color: #cccccc;\r\n      color: #999999;\r\n      box-shadow: none;\r\n      cursor: not-allowed;\r\n\r\n      &:active {\r\n        transform: none;\r\n        box-shadow: none;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.registration-tip {\r\n  padding: 0 20upx 10upx;\r\n  display: flex;\r\n  justify-content: center;\r\n\r\n  .tip-text {\r\n    color: #ff4d4f;\r\n    font-size: 26upx;\r\n    font-weight: bold;\r\n  }\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "useToast", "useRouter", "useUserStore", "uni", "token", "http", "onMounted", "onShow"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqGM,UAAA,gBAAgBA,kBAAI,CAAC;AAErB,UAAA,qBAAqBC,cAAAA,SAAS,MAAM;;AACpC,UAAA,aAAa,MAAM,WAAW;AAAU,eAAA;AAC5C,eAAO,kBAAa,MAAM,cAAc,KAAK,MAAtC,mBAAyC,UAAS;AAAA,IAAA,CAC1D;AAGK,UAAA,iBAAiB,CAAC,MAAM;AACd,oBAAA,QAAQ,EAAE,OAAO;AAAA,IACjC;AAgCcC,kBAAS,SAAA;AACRC,oCAAU,UAAA;AACzB,UAAM,YAAYC,WAAAA,aAAa;AAE/B,UAAM,EAAE,eAAA,IAAmBC,cAAA,MAAI,kBAAkB;AAG3C,UAAA,eAAeL,cAAI,IAAA,EAAE;AACRA,kBAAAA,IAAI,CAAE,CAAA;AAEXK,kBAAI,MAAA,eAAe,OAAO;AAGlC,UAAA,iBAAiB,CAAO,eAAuB;AAC3C,cAAA,IAAI,uBAAuB,UAAU;AACzC,UAAA;AACI,cAAA,MAAM,MAAMA,cAAA,MAAI,QAAQ;AAAA,UAC5B,KAAK,GAAG,6BAAmC;AAAA,UAC3C,QAAQ;AAAA,UACR,MAAM,EAAE,WAAW;AAAA,UACnB,QAAQ;AAAA,YACN,kBAAkB,UAAU,SAAS;AAAA,UAAA;AAAA,QACvC,CACD;AACG,YAAA,OAAO,OAAO,IAAI,OAAO,IAAI,OAAQ,MAAM,QAAQ,GAAG,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,EAAE,OAAO;AACrG,YAAI,QAAQ,KAAK,WAAW,KAAK,UAAU,KAAK,OAAO,SAAS;AAC9D,iBAAO,KAAK,OAAO;AAAA,QAAA,OACd;AACG,kBAAA,KAAK,aAAa,IAAI;AACvB,iBAAA;AAAA,QAAA;AAAA,eAEF,OAAO;AACN,gBAAA,MAAM,cAAc,KAAK;AAC1B,eAAA;AAAA,MAAA;AAAA,IAEX;AAEA,UAAM,oBAAoB,MAAY;AAC9BC,YAAAA,SAAQ,UAAU,SAAS;AACzB,cAAA,IAAI,YAAYA,MAAK;AACvB,YAAA,MAAM,MAAMD,cAAA,MAAI,QAAQ;AAAA,QAC5B,KAAK,GAAG,6BAAmC;AAAA,QAC3C,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN,kBAAkB,UAAU,SAAS;AAAA,QAAA;AAAA,MACvC,CACD;AAEO,cAAA,IAAI,gBAAgB,GAAG;AAC3B,UAAA,OAAO,OAAO,IAAI,OAAO,IAAI,OAAQ,MAAM,QAAQ,GAAG,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,EAAE,OAAO;AACrG,UAAI,QAAQ,KAAK,WAAW,MAAM,QAAQ,KAAK,MAAM,GAAG;AACtD,cAAM,QAAQ,KAAK;AAEX,cAAA,cAAc,MAAM,IAAI,CAAQ,SAAA,KAAK,QAAQ,eAAe,KAAK,KAAK,IAAI,EAAE;AAClF,cAAM,UAAU,MAAM,QAAQ,IAAI,WAAW;AAEvC,cAAA,gBAAgB,MAAM,IAAI,CAAQ,SAAA,KAAK,YAAY,eAAe,KAAK,SAAS,IAAI,EAAE;AAC5F,cAAM,YAAY,MAAM,QAAQ,IAAI,aAAa;AAGnD,qBAAa,QAAQ,MAAM,IAAI,CAAC,MAAM,QAAS,iCAC1C,OAD0C;AAAA,UAE7C,UAAU,QAAQ,GAAG;AAAA,UACrB,UAAU,UAAU,GAAG;AAAA,QAAA,EACvB;AAAA,MAAA,OACG;AACL,qBAAa,QAAQ,CAAC;AAAA,MAAA;AAAA,IAE1B;AAEM,UAAA,gBAAgB,CAAC,SAAS;AAC9BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,iCAAiC,mBAAmB,KAAK,KAAK,CAAC,aAAa,mBAAmB,KAAK,QAAQ,CAAC,YAAY,mBAAmB,KAAK,OAAO,CAAC,SAAS,mBAAmB,KAAK,IAAI,CAAC,WAAW,mBAAmB,KAAK,MAAM,CAAC,eAAe,mBAAmB,KAAK,UAAU,CAAC,eAAe,mBAAmB,KAAK,UAAU,CAAC;AAAA,MAAA,CACnV;AAAA,IACH;AACiBL,kBAAAA,IAAI,CAAC;AACVA,sBAAI,EAAE,UAAU,MAAO,CAAA;AAC7B,UAAA,iBAAiBA,cAAI,IAAA,EAAE;AACvB,UAAA,oBAAoBA,kBAAI,KAAK;AAG7B,UAAA,gBAAgBC,cAAAA,SAAS,MAAM;;AACnC,eAAO,eAAU,SAAS,aAAnB,mBAA6B,SAAS,SAAQ;AAAA,IAAA,CACtD;AAEK,UAAA,eAAeA,cAAAA,SAAS,MAAM;;AAClC,eAAO,eAAU,SAAS,aAAnB,mBAA6B,SAAS,SAAQ;AAAA,IAAA,CACtD;AAEK,UAAA,eAAeA,cAAAA,SAAS,MAAM;;AAClC,eAAO,eAAU,SAAS,aAAnB,mBAA6B,SAAS,SAAQ;AAAA,IAAA,CACtD;AAGK,UAAA,2BAA2BA,cAAAA,SAAS,MAAM;AAE9C,YAAM,YAAY,UAAU,SAAS,YAAY,CAAC;AAElD,YAAM,eAAe,OAAO,UAAU,SAAS,YAAY;AAM3D,UAAI,iBAAiB,GAAG;AAEf,eAAA,UAAU,SAAS,GAAG;AAAA,MAAA,WACpB,iBAAiB,GAAG;AAEtB,eAAA,UAAU,SAAS,GAAG;AAAA,MAAA,WACpB,iBAAiB,GAAG;AAEtB,eAAA,UAAU,SAAS,GAAG;AAAA,MAAA;AAGxB,aAAA;AAAA,IAAA,CACR;AA6BK,UAAA,gBAAgB,CAAC,SAAS;AAC9B,YAAM,UAAU;AAAA,QACd,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,YAAY;AAAA,MACd;AAGA,aAAO,QAAQ,IAAI;AAAA,IACrB;AAEA,UAAM,eAAe,MAAM;AACzBI,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MAAA,CACN;AAAA,IACH;AAEM,UAAA,iBAAiB,CAAC,SAAS;AACvB,cAAA,IAAI,cAAc,KAAK,IAAI;AAGnC,UAAI,YAAY;AAChB,UAAI,YAAY;AAEhB,cAAQ,KAAK,MAAM;AAAA,QACjB,KAAK;AACS,sBAAA;AAEZA,wBAAA,MAAI,eAAe,gBAAgB,EAAE,IAAI,KAAK,IAAI;AACtC,sBAAA,MAAM,KAAK,EAAE;AACzB;AAAA,QACF,KAAK;AACS,sBAAA;AAEZ,sBAAY,UAAU,KAAK,OAAO,KAAK,GAAG,CAAC;AAC3C;AAAA,QACF,KAAK;AACS,sBAAA;AAEZ,sBAAY,UAAU,KAAK,OAAO,KAAK,GAAG,CAAC;AAC3C;AAAA,QACF,KAAK;AACS,sBAAA;AAEZA,wBAAA,MAAI,eAAe,gBAAgB,EAAE,IAAI,KAAK,IAAI;AACtC,sBAAA,MAAM,KAAK,EAAE;AACzB;AAAA,QACF,KAAK;AACS,sBAAA;AAEZA,wBAAA,MAAI,eAAe,gBAAgB,EAAE,IAAI,KAAK,IAAI;AACtC,sBAAA,MAAM,KAAK,EAAE;AACzB;AAAA,QACF,KAAK;AACS,sBAAA;AAEZA,wBAAA,MAAI,eAAe,gBAAgB,EAAE,IAAI,KAAK,IAAI;AACtC,sBAAA,MAAM,KAAK,EAAE;AACzB;AAAA,QAOF;AACc,sBAAA;AACA,sBAAA;AAAA,MAAA;AAGR,cAAA,IAAI,UAAU,SAAS;AAG/B,UAAI,WAAW;AACbA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,GAAG,SAAS,IAAI,SAAS;AAAA,QAAA,CAC/B;AAAA,MAAA;AAAA,IAEL;AAEA,UAAM,gBAAgB,MAAM;AAEtB,UAAA,CAAC,yBAAyB,OAAO;AACnC,uBAAe,QAAQ,CAAC;AACxB;AAAA,MAAA;AAGF,wBAAkB,QAAQ;AAG1B,YAAM,SAA8B,CAAC;AACrC,UAAI,OAAO,UAAU,SAAS,YAAY,MAAM,GAAG;AAC1C,eAAA,SAAS,UAAU,SAAS;AAAA,MAAA;AAGrCE,iBAAA,KAAK,IAAI,uBAAuB,MAAM,EAAE,KAAK,CAAC,QAAa;AACrD,YAAA,IAAI,WAAW,IAAI,QAAQ;AAE7B,yBAAe,SAAS,IAAI,OAAO,WAAW,CAAC,GAE5C,OAAO,CAAA,SAAQ,KAAK,SAAS,UAAU,EACvC,MAAM,GAAG,CAAC,EACV,IAAI,CAAQ,SAAA;AAEL,kBAAA,WAAW,cAAc,KAAK,IAAI;AACjC,mBAAA,iCACF,OADE;AAAA,cAEL,UAAU,KAAK;AAAA,cACf;AAAA,YACF;AAAA,UAAA,CACD;AAAA,QAAA,OACE;AACG,kBAAA,MAAM,YAAY,IAAI,OAAO;AAAA,QAAA;AAAA,MACvC,CACD,EAAE,QAAQ,MAAM;AACf,0BAAkB,QAAQ;AAAA,MAAA,CAC3B;AAAA,IACH;AAEA,UAAM,mBAAmB,MAAM;AAC7BF,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MAAA,CACN;AAAA,IACH;AAEA,UAAM,yBAAyB,MAAM;AACnCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MAAA,CACN;AAAA,IACH;AAEA,UAAM,+BAA+B,MAAM;AACzCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MAAA,CACN;AAAA,IACH;AAEA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MAAA,CACN;AAAA,IACH;AAMAG,kBAAAA,UAAU,MAAM;AACI,wBAAA;AACJ,oBAAA;AAAA,IAAA,CACf;AAGDC,kBAAAA,OAAO,MAAM;AACO,wBAAA;AACJ,oBAAA;AAAA,IAAA,CACf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/bD,GAAG,WAAW,eAAe;"}