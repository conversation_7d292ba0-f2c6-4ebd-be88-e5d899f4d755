{"version": 3, "file": "wd-grid.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-grid/wd-grid.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1ncmlkL3dkLWdyaWQudnVl"], "sourcesContent": ["<template>\n  <view :class=\"`wd-grid ${customClass}`\" :style=\"rootStyle\">\n    <!-- 默认插入的 item -->\n    <slot />\n  </view>\n</template>\n\n<script lang=\"ts\">\nexport default {\n  name: 'wd-grid',\n  options: {\n    virtualHost: true,\n    addGlobalClass: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport { computed, watch } from 'vue'\nimport { useChildren } from '../composables/useChildren'\nimport { GRID_KEY, gridProps } from './types'\nimport { debounce } from '../common/util'\nconst nextTick = () => new Promise((resolve) => setTimeout(resolve, 20))\n\nconst props = defineProps(gridProps)\n\n// 子元素个数\nconst { linkChildren, children } = useChildren(GRID_KEY)\nlinkChildren({ props })\n\nwatch(\n  () => props.column,\n  (val, oldVal) => {\n    if (val === oldVal) return\n    if (!val || val <= 0) {\n      console.error(\n        'The number of columns attribute value is invalid. The attribute must be greater than 0 and it is not recommended to use a larger value attribute.'\n      )\n    }\n    oldVal && init()\n  },\n  {\n    deep: true,\n    immediate: true\n  }\n)\n\nwatch(\n  () => props.border,\n  (val) => {\n    val &&\n      Promise.resolve()\n        .then(nextTick)\n        .then(() => {\n          init()\n        })\n  },\n  {\n    deep: true,\n    immediate: true\n  }\n)\n\nwatch(\n  () => children,\n  () => {\n    handleChildrenChange()\n  },\n  {\n    deep: true\n  }\n)\n\nconst rootStyle = computed(() => {\n  return `${props.gutter ? 'padding-left:' + props.gutter + 'px;' + 'padding-bottom:' + props.gutter + 'px;' : ''}${props.customStyle}`\n})\n\nconst handleChildrenChange = debounce(() => {\n  init()\n}, 50)\n\nfunction init() {\n  if (!children) return\n  children.forEach((item, index) => {\n    if (props.border) {\n      const { column } = props\n      if (column) {\n        const isRightItem = children.length - 1 === index || (index + 1) % column === 0\n        const isFirstLine = index + 1 <= column\n        isFirstLine && item.$.exposed!.setiIemClass('is-first')\n        isRightItem && item.$.exposed!.setiIemClass('is-right')\n        !isFirstLine && item.$.exposed!.setiIemClass('is-border')\n      } else {\n        item.$.exposed!.setiIemClass('is-first')\n      }\n      children.length - 1 === index && item.$.exposed!.setiIemClass(item.$.exposed!.itemClass.value + ' is-last')\n    }\n    item.$.exposed!.init()\n  })\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-grid/wd-grid.vue'\nwx.createComponent(Component)"], "names": ["useChildren", "GRID_KEY", "watch", "computed", "debounce"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAQA,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,EAAA;AAEpB;;;;AAQM,UAAA,WAAW,MAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,EAAE,CAAC;AAEvE,UAAM,QAAQ;AAGd,UAAM,EAAE,cAAc,aAAaA,cAAAA,YAAYC,cAAAA,QAAQ;AAC1C,iBAAA,EAAE,OAAO;AAEtBC,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,KAAK,WAAW;AACf,YAAI,QAAQ;AAAQ;AAChB,YAAA,CAAC,OAAO,OAAO,GAAG;AACZ,kBAAA;AAAA,YACN;AAAA,UACF;AAAA,QAAA;AAEF,kBAAU,KAAK;AAAA,MACjB;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,WAAW;AAAA,MAAA;AAAA,IAEf;AAEAA,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,QAAQ;AACP,eACE,QAAQ,QAAQ,EACb,KAAK,QAAQ,EACb,KAAK,MAAM;AACL,eAAA;AAAA,QAAA,CACN;AAAA,MACP;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,WAAW;AAAA,MAAA;AAAA,IAEf;AAEAA,kBAAA;AAAA,MACE,MAAM;AAAA,MACN,MAAM;AACiB,6BAAA;AAAA,MACvB;AAAA,MACA;AAAA,QACE,MAAM;AAAA,MAAA;AAAA,IAEV;AAEM,UAAA,YAAYC,cAAAA,SAAS,MAAM;AAC/B,aAAO,GAAG,MAAM,SAAS,kBAAkB,MAAM,SAAS,uBAA4B,MAAM,SAAS,QAAQ,EAAE,GAAG,MAAM,WAAW;AAAA,IAAA,CACpI;AAEK,UAAA,uBAAuBC,cAAAA,SAAS,MAAM;AACrC,WAAA;AAAA,OACJ,EAAE;AAEL,aAAS,OAAO;AACd,UAAI,CAAC;AAAU;AACN,eAAA,QAAQ,CAAC,MAAM,UAAU;AAChC,YAAI,MAAM,QAAQ;AACV,gBAAA,EAAE,WAAW;AACnB,cAAI,QAAQ;AACV,kBAAM,cAAc,SAAS,SAAS,MAAM,UAAU,QAAQ,KAAK,WAAW;AACxE,kBAAA,cAAc,QAAQ,KAAK;AACjC,2BAAe,KAAK,EAAE,QAAS,aAAa,UAAU;AACtD,2BAAe,KAAK,EAAE,QAAS,aAAa,UAAU;AACtD,aAAC,eAAe,KAAK,EAAE,QAAS,aAAa,WAAW;AAAA,UAAA,OACnD;AACA,iBAAA,EAAE,QAAS,aAAa,UAAU;AAAA,UAAA;AAEzC,mBAAS,SAAS,MAAM,SAAS,KAAK,EAAE,QAAS,aAAa,KAAK,EAAE,QAAS,UAAU,QAAQ,UAAU;AAAA,QAAA;AAEvG,aAAA,EAAE,QAAS,KAAK;AAAA,MAAA,CACtB;AAAA,IAAA;;;;;;;;;;AClGH,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}