"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "ProgressMap",
  props: {
    title: {
      type: String,
      default: ""
    },
    dataSource: {
      type: Array,
      default: () => []
    }
  },
  setup(__props) {
    return (_ctx, _cache) => {
      return {
        a: common_vendor.t(__props.title),
        b: common_vendor.f(__props.dataSource, (item, index, i0) => {
          return {
            a: common_vendor.f(item.data, (inItem, inIndex, i1) => {
              return {
                a: common_vendor.t(inItem.label),
                b: inIndex
              };
            }),
            b: item.activeStep ? 1 : "",
            c: item.activeStep ? 1 : "",
            d: !item.activeStep ? 1 : "",
            e: item.activeStep ? 1 : "",
            f: index
          };
        })
      };
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-ce046f8e"]]);
wx.createComponent(Component);
//# sourceMappingURL=ProgressMap.js.map
