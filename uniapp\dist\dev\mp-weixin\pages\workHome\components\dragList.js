"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../common/vendor.js");
const utils_http = require("../../../utils/http.js");
require("../../../plugin/uni-mini-router/router/index.js");
const plugin_uniMiniRouter_core_index = require("../../../plugin/uni-mini-router/core/index.js");
const common_uitls = require("../../../common/uitls.js");
const store_pageParams = require("../../../store/page-params.js");
if (!Array) {
  const _easycom_wd_icon2 = common_vendor.resolveComponent("wd-icon");
  const _easycom_wd_col2 = common_vendor.resolveComponent("wd-col");
  const _easycom_wd_row2 = common_vendor.resolveComponent("wd-row");
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  const _easycom_wd_toast2 = common_vendor.resolveComponent("wd-toast");
  (_easycom_wd_icon2 + _easycom_wd_col2 + _easycom_wd_row2 + _easycom_z_paging2 + _easycom_wd_toast2)();
}
const _easycom_wd_icon = () => "../../../node-modules/wot-design-uni/components/wd-icon/wd-icon.js";
const _easycom_wd_col = () => "../../../node-modules/wot-design-uni/components/wd-col/wd-col.js";
const _easycom_wd_row = () => "../../../node-modules/wot-design-uni/components/wd-row/wd-row.js";
const _easycom_z_paging = () => "../../../uni_modules/z-paging/components/z-paging/z-paging.js";
const _easycom_wd_toast = () => "../../../node-modules/wot-design-uni/components/wd-toast/wd-toast.js";
if (!Math) {
  (_easycom_wd_icon + _easycom_wd_col + _easycom_wd_row + _easycom_z_paging + _easycom_wd_toast)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "dragList",
  options: {
    styleIsolation: "shared"
  }
}), {
  __name: "dragList",
  setup(__props) {
    common_vendor.useToast();
    plugin_uniMiniRouter_core_index.useRouter();
    store_pageParams.useParamsStore();
    const paging = common_vendor.ref(null);
    const dataList = common_vendor.ref([]);
    const queryList = (pageNo, pageSize) => {
      utils_http.http.get("/drag/page/list", {
        izTemplate: 0,
        pageNo,
        pageSize,
        style: "default"
      }).then((res) => {
        if (res.success) {
          paging.value.complete(res.result.records);
        } else {
          paging.value.complete(false);
        }
      }).catch((res) => {
        paging.value.complete(false);
      });
    };
    const handleGo = (item) => {
      console.log("handleGo", item);
      common_vendor.index.navigateTo({
        url: "/pages-work/dragPage/index?id=" + item.id
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.f(common_vendor.unref(dataList), (item, index, i0) => {
          return {
            a: "0aee2493-3-" + i0 + "," + ("0aee2493-2-" + i0),
            b: common_vendor.o(($event) => handleGo(item), index),
            c: common_vendor.s({
              backgroundColor: (item == null ? void 0 : item.backgroundColor) ? item.backgroundColor : common_vendor.unref(common_uitls.getRandomColor)()
            }),
            d: common_vendor.t(item.name),
            e: index,
            f: "0aee2493-2-" + i0 + ",0aee2493-1"
          };
        }),
        b: common_vendor.p({
          name: "chart",
          size: "22px"
        }),
        c: common_vendor.p({
          span: 6
        }),
        d: common_vendor.sr(paging, "0aee2493-0", {
          "k": "paging"
        }),
        e: common_vendor.o(queryList),
        f: common_vendor.o(($event) => common_vendor.isRef(dataList) ? dataList.value = $event : null),
        g: common_vendor.p({
          fixed: false,
          modelValue: common_vendor.unref(dataList)
        })
      };
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-0aee2493"]]);
wx.createComponent(Component);
//# sourceMappingURL=dragList.js.map
