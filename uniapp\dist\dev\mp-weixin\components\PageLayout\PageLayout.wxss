/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.pageLayout.data-v-3318346e {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
}
.pageLayout .pageNav.data-v-3318346e {
  background-image: linear-gradient(135deg, #2DCB70 0%, #07C160 100%);
}
.pageLayout .pageNav.transparent.data-v-3318346e {
  background-image: none;
}
.pageLayout .pageNav.fixed.data-v-3318346e {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
}
.pageLayout .pageNav .statusBar.data-v-3318346e {
  width: 100%;
  height: 0;
}
.pageLayout .pageNav.data-v-3318346e .wd-navbar {
  background-color: transparent;
  --wot-navbar-title-font-weight: 700;
  --wot-navbar-title-color: #ffffff;
  --wot-navbar-arrow-size: 18px;
  --wot-navbar-desc-font-size: 14px;
  --wot-navbar-title-font-size: 16px;
}
.pageLayout .pageNav.data-v-3318346e .wd-navbar .wd-navbar__title {
  color: #ffffff !important;
  font-weight: 700 !important;
}
.pageLayout .pageContent.data-v-3318346e {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background-color: #f1f1f1;
}