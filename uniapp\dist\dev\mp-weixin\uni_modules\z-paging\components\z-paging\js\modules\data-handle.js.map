{"version": 3, "file": "data-handle.js", "sources": ["../../../../../../../../../src/uni_modules/z-paging/components/z-paging/js/modules/data-handle.js"], "sourcesContent": ["// [z-paging]数据处理模块\r\nimport u from '.././z-paging-utils'\r\nimport c from '.././z-paging-constant'\r\nimport Enum from '.././z-paging-enum'\r\nimport interceptor from '../z-paging-interceptor'\r\n\r\nexport default {\r\n\tprops: {\r\n\t\t// 自定义初始的pageNo，默认为1\r\n\t\tdefaultPageNo: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: u.gc('defaultPageNo', 1),\r\n\t\t\tobserver: function(newVal) {\r\n\t\t\t\tthis.pageNo = newVal;\r\n\t\t\t},\r\n\t\t},\r\n\t\t// 自定义pageSize，默认为10\r\n\t\tdefaultPageSize: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: u.gc('defaultPageSize', 10),\r\n\t\t\tvalidator: (value) => {\r\n\t\t\t\tif (value <= 0) u.consoleErr('default-page-size必须大于0！');\r\n\t\t\t\treturn value > 0;\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 为保证数据一致，设置当前tab切换时的标识key，并在complete中传递相同key，若二者不一致，则complete将不会生效\r\n\t\tdataKey: {\r\n\t\t\ttype: [Number, String, Object],\r\n\t\t\tdefault: u.gc('dataKey', null),\r\n\t\t},\r\n\t\t// 使用缓存，若开启将自动缓存第一页的数据，默认为否。请注意，因考虑到切换tab时不同tab数据不同的情况，默认仅会缓存组件首次加载时第一次请求到的数据，后续的下拉刷新操作不会更新缓存。\r\n\t\tuseCache: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('useCache', false)\r\n\t\t},\r\n\t\t// 使用缓存时缓存的key，用于区分不同列表的缓存数据，useCache为true时必须设置，否则缓存无效\r\n\t\tcacheKey: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('cacheKey', null)\r\n\t\t},\r\n\t\t// 缓存模式，默认仅会缓存组件首次加载时第一次请求到的数据，可设置为always，即代表总是缓存，每次列表刷新(下拉刷新、调用reload等)都会更新缓存\r\n\t\tcacheMode: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('cacheMode', Enum.CacheMode.Default)\r\n\t\t},\r\n\t\t// 自动注入的list名，可自动修改父view(包含ref=\"paging\")中对应name的list值\r\n\t\tautowireListName: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('autowireListName', '')\r\n\t\t},\r\n\t\t// 自动注入的query名，可自动调用父view(包含ref=\"paging\")中的query方法\r\n\t\tautowireQueryName: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('autowireQueryName', '')\r\n\t\t},\r\n\t\t// 获取分页数据Function，功能与@query类似。若设置了fetch则@query将不再触发\r\n\t\tfetch: {\r\n\t\t\ttype: Function,\r\n\t\t\tdefault: null\r\n\t\t},\r\n\t\t// fetch的附加参数，fetch配置后有效\r\n\t\tfetchParams: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault: u.gc('fetchParams', null)\r\n\t\t},\r\n\t\t// z-paging mounted后自动调用reload方法(mounted后自动调用接口)，默认为是\r\n\t\tauto: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('auto', true)\r\n\t\t},\r\n\t\t// 用户下拉刷新时是否触发reload方法，默认为是\r\n\t\treloadWhenRefresh: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('reloadWhenRefresh', true)\r\n\t\t},\r\n\t\t// reload时自动滚动到顶部，默认为是\r\n\t\tautoScrollToTopWhenReload: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('autoScrollToTopWhenReload', true)\r\n\t\t},\r\n\t\t// reload时立即自动清空原list，默认为是，若立即自动清空，则在reload之后、请求回调之前页面是空白的\r\n\t\tautoCleanListWhenReload: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('autoCleanListWhenReload', true)\r\n\t\t},\r\n\t\t// 列表刷新时自动显示下拉刷新view，默认为否\r\n\t\tshowRefresherWhenReload: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('showRefresherWhenReload', false)\r\n\t\t},\r\n\t\t// 列表刷新时自动显示加载更多view，且为加载中状态，默认为否\r\n\t\tshowLoadingMoreWhenReload: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('showLoadingMoreWhenReload', false)\r\n\t\t},\r\n\t\t// 组件created时立即触发reload(可解决一些情况下先看到页面再看到loading的问题)，auto为true时有效。为否时将在mounted+nextTick后触发reload，默认为否\r\n\t\tcreatedReload: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('createdReload', false)\r\n\t\t},\r\n\t\t// 本地分页时上拉加载更多延迟时间，单位为毫秒，默认200毫秒\r\n\t\tlocalPagingLoadingTime: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: u.gc('localPagingLoadingTime', 200)\r\n\t\t},\r\n\t\t// 自动拼接complete中传过来的数组(使用聊天记录模式时无效)\r\n\t\tconcat: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('concat', true)\r\n\t\t},\r\n\t\t// 请求失败是否触发reject，默认为是\r\n\t\tcallNetworkReject: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('callNetworkReject', true)\r\n\t\t},\r\n\t\t// 父组件v-model所绑定的list的值\r\n\t\tvalue: {\r\n\t\t\ttype: Array,\r\n\t\t\tdefault: function() {\r\n\t\t\t\treturn [];\r\n\t\t\t}\r\n\t\t},\r\n\t\t// #ifdef VUE3\r\n\t\tmodelValue: {\r\n\t\t\ttype: Array,\r\n\t\t\tdefault: function() {\r\n\t\t\t\treturn [];\r\n\t\t\t}\r\n\t\t}\r\n\t\t// #endif\r\n\t},\r\n\tdata (){\r\n\t\treturn {\r\n\t\t\tcurrentData: [],\r\n\t\t\ttotalData: [],\r\n\t\t\trealTotalData: [],\r\n\t\t\ttotalLocalPagingList: [],\r\n\t\t\tdataPromiseResultMap: {\r\n\t\t\t\treload: null,\r\n\t\t\t\tcomplete: null,\r\n\t\t\t\tlocalPaging: null\r\n\t\t\t},\r\n\t\t\tisSettingCacheList: false,\r\n\t\t\tpageNo: 1,\r\n\t\t\tcurrentRefreshPageSize: 0,\r\n\t\t\tisLocalPaging: false,\r\n\t\t\tisAddedData: false,\r\n\t\t\tisTotalChangeFromAddData: false,\r\n\t\t\tprivateConcat: true,\r\n\t\t\tmyParentQuery: -1,\r\n\t\t\tfirstPageLoaded: false,\r\n\t\t\tpagingLoaded: false,\r\n\t\t\tloaded: false,\r\n\t\t\tisUserReload: true,\r\n\t\t\tfromEmptyViewReload: false,\r\n\t\t\tqueryFrom: '',\r\n\t\t\tlistRendering: false,\r\n\t\t\tisHandlingRefreshToPage: false,\r\n\t\t\tisFirstPageAndNoMore: false,\r\n\t\t\ttotalDataChangeThrow: true\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\tpageSize() {\r\n\t\t\treturn this.defaultPageSize;\r\n\t\t},\r\n\t\tfinalConcat() {\r\n\t\t\treturn this.concat && this.privateConcat;\r\n\t\t},\r\n\t\tfinalUseCache() {\r\n\t\t\tif (this.useCache && !this.cacheKey) {\r\n\t\t\t\tu.consoleErr('use-cache为true时，必须设置cache-key，否则缓存无效！');\r\n\t\t\t}\r\n\t\t\treturn this.useCache && !!this.cacheKey;\r\n\t\t},\r\n\t\tfinalCacheKey() {\r\n\t\t\treturn this.cacheKey ? `${c.cachePrefixKey}-${this.cacheKey}` : null; \r\n\t\t},\r\n\t\tisFirstPage() {\r\n\t\t\treturn this.pageNo === this.defaultPageNo;\r\n\t\t}\r\n\t},\r\n\twatch: {\r\n\t\ttotalData(newVal, oldVal) {\r\n\t\t\tthis._totalDataChange(newVal, oldVal, this.totalDataChangeThrow);\r\n\t\t\tthis.totalDataChangeThrow = true;\r\n\t\t},\r\n\t\tcurrentData(newVal, oldVal) {\r\n\t\t\tthis._currentDataChange(newVal, oldVal);\r\n\t\t},\r\n\t\tuseChatRecordMode(newVal, oldVal) {\r\n\t\t\tif (newVal) {\r\n\t\t\t\tthis.nLoadingMoreFixedHeight = false;\r\n\t\t\t}\r\n\t\t},\r\n\t\tvalue: {\r\n\t\t\thandler(newVal) {\r\n\t\t\t\t// 当v-model绑定的数据源被更改时，此时数据源改变不emit input事件，避免循环调用\r\n\t\t\t\tif (newVal !== this.totalData) {\r\n\t\t\t\t\tthis.totalDataChangeThrow = false;\r\n\t\t\t\t\tthis.totalData = newVal;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\timmediate: true\r\n\t\t},\r\n\t\t// #ifdef VUE3\r\n\t\tmodelValue: {\r\n\t\t\thandler(newVal) {\r\n\t\t\t\t// 当v-model绑定的数据源被更改时，此时数据源改变不emit input事件，避免循环调用\r\n\t\t\t\tif (newVal !== this.totalData) {\r\n\t\t\t\t\tthis.totalDataChangeThrow = false;\r\n\t\t\t\t\tthis.totalData = newVal;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\timmediate: true\r\n\t\t}\r\n\t\t// #endif\r\n\t},\r\n\tmethods: {\r\n\t\t// 请求结束(成功或者失败)调用此方法，将请求的结果传递给z-paging处理，第一个参数为请求结果数组，第二个参数为是否成功(默认为是）\r\n\t\tcomplete(data, success = true) {\r\n\t\t\tthis.customNoMore = -1;\r\n\t\t\treturn this.addData(data, success);\r\n\t\t},\r\n\t\t//【保证数据一致】请求结束(成功或者失败)调用此方法，将请求的结果传递给z-paging处理，第一个参数为请求结果数组，第二个参数为dataKey，需与:data-key绑定的一致，第三个参数为是否成功(默认为是）\r\n\t\tcompleteByKey(data, dataKey = null, success = true) {\r\n\t\t\tif (dataKey !== null && this.dataKey !== null && dataKey !== this.dataKey) {\r\n\t\t\t\tthis.isFirstPage && this.endRefresh();\r\n\t\t\t\treturn new Promise(resolve => resolve());\r\n\t\t\t}\r\n\t\t\tthis.customNoMore = -1;\r\n\t\t\treturn this.addData(data, success);\r\n\t\t},\r\n\t\t//【通过total判断是否有更多数据】请求结束(成功或者失败)调用此方法，将请求的结果传递给z-paging处理，第一个参数为请求结果数组，第二个参数为total(列表总数)，第三个参数为是否成功(默认为是）\r\n\t\tcompleteByTotal(data, total, success = true) {\r\n\t\t\tif (total == 'undefined') {\r\n\t\t\t\tthis.customNoMore = -1;\r\n\t\t\t} else {\r\n\t\t\t\tconst dataTypeRes = this._checkDataType(data, success, false);\r\n\t\t\t\tdata = dataTypeRes.data;\r\n\t\t\t\tsuccess = dataTypeRes.success;\r\n\t\t\t\tif (total >= 0 && success) {\r\n\t\t\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\t\tlet nomore = false;\r\n\t\t\t\t\t\t\tconst realTotalDataCount = this.pageNo == this.defaultPageNo ? 0 : this.realTotalData.length;\r\n\t\t\t\t\t\t\tconst dataLength = this.privateConcat ? data.length : 0;\r\n\t\t\t\t\t\t\tlet exceedCount = realTotalDataCount + dataLength - total;\r\n\t\t\t\t\t\t\t// 没有更多数据了\r\n\t\t\t\t\t\t\tif (exceedCount >= 0) {\r\n\t\t\t\t\t\t\t\tnomore = true;\r\n\t\t\t\t\t\t\t\t// 仅截取total内部分的数据\r\n\t\t\t\t\t\t\t\texceedCount = this.defaultPageSize - exceedCount;\r\n\t\t\t\t\t\t\t\tif (this.privateConcat && exceedCount > 0 && exceedCount < data.length) {\r\n\t\t\t\t\t\t\t\t\tdata = data.splice(0, exceedCount);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthis.completeByNoMore(data, nomore, success).then(res => resolve(res)).catch(() => reject());\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn this.addData(data, success);\r\n\t\t},\r\n\t\t//【自行判断是否有更多数据】请求结束(成功或者失败)调用此方法，将请求的结果传递给z-paging处理，第一个参数为请求结果数组，第二个参数为是否没有更多数据，第三个参数为是否成功(默认是是）\r\n\t\tcompleteByNoMore(data, nomore, success = true) {\r\n\t\t\tif (nomore != 'undefined') {\r\n\t\t\t\tthis.customNoMore = nomore == true ? 1 : 0;\r\n\t\t\t}\r\n\t\t\treturn this.addData(data, success);\r\n\t\t},\r\n\t\t// 请求结束且请求失败时调用，支持传入请求失败原因\r\n\t\tcompleteByError(errorMsg) {\r\n\t\t\tthis.customerEmptyViewErrorText = errorMsg;\r\n\t\t\treturn this.complete(false);\r\n\t\t},\r\n\t\t// 与上方complete方法功能一致，新版本中设置服务端回调数组请使用complete方法\r\n\t\taddData(data, success = true) {\r\n\t\t\tif (!this.fromCompleteEmit) {\r\n\t\t\t\tthis.disabledCompleteEmit = true;\r\n\t\t\t\tthis.fromCompleteEmit = false;\r\n\t\t\t}\r\n\t\t\tconst currentTimeStamp = u.getTime();\r\n\t\t\tconst disTime = currentTimeStamp - this.requestTimeStamp;\r\n\t\t\tlet minDelay = this.minDelay;\r\n\t\t\tif (this.isFirstPage && this.finalShowRefresherWhenReload) {\r\n\t\t\t\tminDelay = Math.max(400, minDelay);\r\n\t\t\t}\r\n\t\t\tconst addDataDalay = (this.requestTimeStamp > 0 && disTime < minDelay) ? minDelay - disTime : 0;\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tu.delay(() => {\r\n\t\t\t\t\tthis._addData(data, success, false);\r\n\t\t\t\t}, this.delay > 0 ? this.delay : addDataDalay)\r\n\t\t\t})\r\n\t\t\t\r\n\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\tthis.dataPromiseResultMap.complete = { resolve, reject };\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 从顶部添加数据，不会影响分页的pageNo和pageSize\r\n\t\taddDataFromTop(data, toTop = true, toTopWithAnimate = true) {\r\n\t\t\t// 数据是否拼接到顶部，如果是聊天记录模式并且列表没有倒置，则应该拼接在底部\r\n\t\t\tlet addFromTop = !this.isChatRecordModeAndNotInversion;\r\n\t\t\tdata = Object.prototype.toString.call(data) !== '[object Array]' ? [data] : (addFromTop ? data.reverse() : data);\r\n\t\t\t// #ifndef APP-NVUE\r\n\t\t\tthis.finalUseVirtualList && this._setCellIndex(data, 'top')\r\n\t\t\t// #endif\r\n\t\t\t\r\n\t\t\tthis.totalData = addFromTop ? [...data, ...this.totalData] : [...this.totalData, ...data];\r\n\t\t\tif (toTop) {\r\n\t\t\t\tu.delay(() => this.useChatRecordMode ? this.scrollToBottom(toTopWithAnimate) : this.scrollToTop(toTopWithAnimate));\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 重新设置列表数据，调用此方法不会影响pageNo和pageSize，也不会触发请求。适用场景：当需要删除列表中某一项时，将删除对应项后的数组通过此方法传递给z-paging。(当出现类似的需要修改列表数组的场景时，请使用此方法，请勿直接修改page中:list.sync绑定的数组)\r\n\t\tresetTotalData(data) {\r\n\t\t\tthis.isTotalChangeFromAddData = true;\r\n\t\t\tdata = Object.prototype.toString.call(data) !== '[object Array]' ? [data] : data;\r\n\t\t\tthis.totalData = data;\r\n\t\t},\r\n\t\t// 设置本地分页数据，请求结束(成功或者失败)调用此方法，将请求的结果传递给z-paging作分页处理（若调用了此方法，则上拉加载更多时内部会自动分页，不会触发@query所绑定的事件）\r\n\t\tsetLocalPaging(data, success = true) {\r\n\t\t\tthis.isLocalPaging = true;\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis._addData(data, success, true);\r\n\t\t\t})\r\n\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\tthis.dataPromiseResultMap.localPaging = { resolve, reject };\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 重新加载分页数据，pageNo会恢复为默认值，相当于下拉刷新的效果(animate为true时会展示下拉刷新动画，默认为false)\r\n\t\treload(animate = this.showRefresherWhenReload) {\r\n\t\t\tif (animate) {\r\n\t\t\t\tthis.privateShowRefresherWhenReload = animate;\r\n\t\t\t\tthis.isUserPullDown = true;\r\n\t\t\t}\r\n\t\t\tif (!this.showLoadingMoreWhenReload) {\r\n\t\t\t\tthis.listRendering = true;\r\n\t\t\t}\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis._preReload(animate, false);\r\n\t\t\t})\r\n\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\tthis.dataPromiseResultMap.reload = { resolve, reject };\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 刷新列表数据，pageNo和pageSize不会重置，列表数据会重新从服务端获取。必须保证@query绑定的方法中的pageNo和pageSize和传给服务端的一致\r\n\t\trefresh() {\r\n\t\t\treturn this._handleRefreshWithDisPageNo(this.pageNo - this.defaultPageNo + 1);\r\n\t\t},\r\n\t\t// 刷新列表数据至指定页，例如pageNo=5时则代表刷新列表至第5页，此时pageNo会变为5，列表会展示前5页的数据。必须保证@query绑定的方法中的pageNo和pageSize和传给服务端的一致\r\n\t\trefreshToPage(pageNo) {\r\n\t\t\tthis.isHandlingRefreshToPage = true;\r\n\t\t\treturn this._handleRefreshWithDisPageNo(pageNo + this.defaultPageNo - 1);\r\n\t\t},\r\n\t\t// 手动更新列表缓存数据，将自动截取v-model绑定的list中的前pageSize条覆盖缓存，请确保在list数据更新到预期结果后再调用此方法\r\n\t\tupdateCache() {\r\n\t\t\tif (this.finalUseCache && this.totalData.length) {\r\n\t\t\t\tthis._saveLocalCache(this.totalData.slice(0, Math.min(this.totalData.length, this.pageSize)));\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 清空分页数据\r\n\t\tclean() {\r\n\t\t\tthis._reload(true);\r\n\t\t\tthis._addData([], true, false);\r\n\t\t},\r\n\t\t// 清空分页数据\r\n\t\tclear() {\r\n\t\t\tthis.clean();\r\n\t\t},\r\n\t\t// reload之前的一些处理\r\n\t\t_preReload(animate = this.showRefresherWhenReload, isFromMounted = true, retryCount = 0) {\r\n\t\t\tconst showRefresher = this.finalRefresherEnabled && this.useCustomRefresher;\r\n\t\t\t// #ifndef APP-NVUE\r\n\t\t\t// 如果获取slot=\"refresher\"高度失败，则不触发reload，直到获取slot=\"refresher\"高度成功\r\n\t\t\tif (this.customRefresherHeight === -1 && showRefresher) {\r\n\t\t\t\tu.delay(() => {\r\n\t\t\t\t\tretryCount ++;\r\n\t\t\t\t\t// 如果重试次数是10的倍数(也就是每500毫秒)，尝试重新获取一下slot=\"refresher\"高度\r\n\t\t\t\t\t// 此举是为了解决在某些特殊情况下，z-paging组件mounted了，但是未展示在用户面前，（比如在tabbar页面中，未切换到对应tabbar但是通过代码让z-paging展示了，此时控制台会报Error: Not Found：Page，因为这时候去获取dom节点信息获取不到）\r\n\t\t\t\t\t// 当用户在某个时刻让此z-paging展示在面前时，即可顺利获取到slot=\"refresher\"高度，递归停止\r\n\t\t\t\t\tif (retryCount % 10 === 0) {\r\n\t\t\t\t\t\tthis._updateCustomRefresherHeight();\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis._preReload(animate, isFromMounted, retryCount);\r\n\t\t\t\t}, c.delayTime / 2);\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t\tthis.isUserReload = true;\r\n\t\t\tthis.loadingType = Enum.LoadingType.Refresher;\r\n\t\t\tif (animate) {\r\n\t\t\t\tthis.privateShowRefresherWhenReload = animate;\r\n\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\tif (this.useCustomRefresher) {\r\n\t\t\t\t\tthis._doRefresherRefreshAnimate();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.refresherTriggered = true;\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tthis.refresherStatus = Enum.Refresher.Loading;\r\n\t\t\t\tthis.refresherRevealStackCount ++;\r\n\t\t\t\tu.delay(() => {\r\n\t\t\t\t\tthis._getNodeClientRect('zp-n-refresh-container', false).then((node) => {\r\n\t\t\t\t\t\tif (node) {\r\n\t\t\t\t\t\t\tlet nodeHeight = node[0].height;\r\n\t\t\t\t\t\t\tthis.nShowRefresherReveal = true;\r\n\t\t\t\t\t\t\tthis.nShowRefresherRevealHeight = nodeHeight;\r\n\t\t\t\t\t\t\tu.delay(() => {\r\n\t\t\t\t\t\t\t\tthis._nDoRefresherEndAnimation(0, -nodeHeight, false, false);\r\n\t\t\t\t\t\t\t\tu.delay(() => {\r\n\t\t\t\t\t\t\t\t\tthis._nDoRefresherEndAnimation(nodeHeight, 0);\r\n\t\t\t\t\t\t\t\t}, 10)\r\n\t\t\t\t\t\t\t}, 10)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis._reload(false, isFromMounted);\r\n\t\t\t\t\t\tthis._doRefresherLoad(false);\r\n\t\t\t\t\t});\r\n\t\t\t\t}, this.pagingLoaded ? 10 : 100)\r\n\t\t\t\treturn;\r\n\t\t\t\t// #endif\r\n\t\t\t} else {\r\n\t\t\t\tthis._refresherEnd(false, false, false, false);\r\n\t\t\t}\r\n\t\t\tthis._reload(false, isFromMounted);\r\n\t\t},\r\n\t\t// 重新加载分页数据\r\n\t\t_reload(isClean = false, isFromMounted = false, isUserPullDown = false) {\r\n\t\t\tthis.isAddedData = false;\r\n\t\t\tthis.insideOfPaging = -1;\r\n\t\t\tthis.cacheScrollNodeHeight = -1;\r\n\t\t\tthis.pageNo = this.defaultPageNo;\r\n\t\t\tthis._cleanRefresherEndTimeout();\r\n\t\t\t!this.privateShowRefresherWhenReload && !isClean && this._startLoading(true);\r\n\t\t\tthis.firstPageLoaded = true;\r\n\t\t\tthis.isTotalChangeFromAddData = false;\r\n\t\t\tif (!this.isSettingCacheList) {\r\n\t\t\t\tthis.totalData = [];\r\n\t\t\t}\r\n\t\t\tif (!isClean) {\r\n\t\t\t\tthis._emitQuery(this.pageNo, this.defaultPageSize, isUserPullDown ? Enum.QueryFrom.UserPullDown : Enum.QueryFrom.Reload);\r\n\t\t\t\tlet delay = 0;\r\n\t\t\t\t// #ifdef MP-TOUTIAO\r\n\t\t\t\tdelay = 5;\r\n\t\t\t\t// #endif\r\n\t\t\t\tu.delay(this._callMyParentQuery, delay);\r\n\t\t\t\tif (!isFromMounted && this.autoScrollToTopWhenReload) {\r\n\t\t\t\t\tlet checkedNRefresherLoading = true;\r\n\t\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\t\tcheckedNRefresherLoading = !this.nRefresherLoading;\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tcheckedNRefresherLoading && this._scrollToTop(false);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// #ifdef APP-NVUE\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis.nShowBottom = this.realTotalData.length > 0;\r\n\t\t\t})\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\t// 处理服务端返回的数组\r\n\t\t_addData(data, success, isLocal) {\r\n\t\t\tthis.isAddedData = true;\r\n\t\t\tthis.fromEmptyViewReload = false;\r\n\t\t\tthis.isTotalChangeFromAddData = true;\r\n\t\t\tthis.refresherTriggered = false;\r\n\t\t\tthis._endSystemLoadingAndRefresh();\r\n\t\t\tconst tempIsUserPullDown = this.isUserPullDown;\r\n\t\t\tif (this.showRefresherUpdateTime && this.isFirstPage) {\r\n\t\t\t\tu.setRefesrherTime(u.getTime(), this.refresherUpdateTimeKey);\r\n\t\t\t\tthis.$refs.refresh && this.$refs.refresh.updateTime();\r\n\t\t\t}\r\n\t\t\tif (!isLocal && tempIsUserPullDown && this.isFirstPage) {\r\n\t\t\t\tthis.isUserPullDown = false;\r\n\t\t\t}\r\n\t\t\tthis.listRendering = true;\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tu.delay(() => this.listRendering = false);\r\n\t\t\t})\r\n\t\t\tlet dataTypeRes = this._checkDataType(data, success, isLocal);\r\n\t\t\tdata = dataTypeRes.data;\r\n\t\t\tsuccess = dataTypeRes.success;\r\n\t\t\tlet delayTime = c.delayTime;\r\n\t\t\tif (this.useChatRecordMode) delayTime = 0;\r\n\t\t\tthis.loadingForNow = false;\r\n\t\t\tu.delay(() => {\r\n\t\t\t\tthis.pagingLoaded = true;\r\n\t\t\t\tthis.$nextTick(()=>{\r\n\t\t\t\t\t!isLocal && this._refresherEnd(delayTime > 0, true, tempIsUserPullDown);\r\n\t\t\t\t})\r\n\t\t\t})\r\n\t\t\tif (this.isFirstPage) {\r\n\t\t\t\tthis.isLoadFailed = !success;\r\n\t\t\t\tthis.$emit('isLoadFailedChange', this.isLoadFailed);\r\n\t\t\t\tif (this.finalUseCache && success && (this.cacheMode === Enum.CacheMode.Always ? true : this.isSettingCacheList)) {\r\n\t\t\t\t\tthis._saveLocalCache(data);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tthis.isSettingCacheList = false;\r\n\t\t\tif (success) {\r\n\t\t\t\tif (!(this.privateConcat === false && !this.isHandlingRefreshToPage && this.loadingStatus === Enum.More.NoMore)) {\r\n\t\t\t\t\tthis.loadingStatus = Enum.More.Default;\r\n\t\t\t\t}\r\n\t\t\t\tif (isLocal) {\r\n\t\t\t\t\t// 如果当前是本地分页，则必然是由setLocalPaging方法触发，此时直接本地加载第一页数据即可。后续本地分页加载更多方法由滚动到底部加载更多事件处理\r\n\t\t\t\t\tthis.totalLocalPagingList = data;\r\n\t\t\t\t\tconst localPageNo = this.defaultPageNo;\r\n\t\t\t\t\tconst localPageSize = this.queryFrom !== Enum.QueryFrom.Refresh ? this.defaultPageSize : this.currentRefreshPageSize;\r\n\t\t\t\t\tthis._localPagingQueryList(localPageNo, localPageSize, 0, res => {\r\n\t\t\t\t\t\tu.delay(() => {\r\n\t\t\t\t\t\t\tthis.completeByTotal(res, this.totalLocalPagingList.length);;\r\n\t\t\t\t\t\t}, 0)\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 如果当前不是本地分页，则按照正常分页逻辑进行数据处理&emit数据\r\n\t\t\t\t\tlet dataChangeDelayTime = 0;\r\n\t\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\t\tif (this.privateShowRefresherWhenReload && this.finalNvueListIs === 'waterfall') {\r\n\t\t\t\t\t\tdataChangeDelayTime = 150;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tu.delay(() => {\r\n\t\t\t\t\t\tthis._currentDataChange(data, this.currentData);\r\n\t\t\t\t\t\tthis._callDataPromise(true, this.totalData);\r\n\t\t\t\t\t}, dataChangeDelayTime)\r\n\t\t\t\t}\r\n\t\t\t\tif (this.isHandlingRefreshToPage) {\r\n\t\t\t\t\tthis.isHandlingRefreshToPage = false;\r\n\t\t\t\t\tthis.pageNo = this.defaultPageNo + Math.ceil(data.length / this.pageSize) - 1;\r\n\t\t\t\t\tif (data.length % this.pageSize !== 0) {\r\n\t\t\t\t\t\tthis.customNoMore = 1;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tthis._currentDataChange(data, this.currentData);\r\n\t\t\t\tthis._callDataPromise(false);\r\n\t\t\t\tthis.loadingStatus = Enum.More.Fail;\r\n\t\t\t\tthis.isHandlingRefreshToPage = false;\r\n\t\t\t\tif (this.loadingType === Enum.LoadingType.LoadMore) {\r\n\t\t\t\t\tthis.pageNo --;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 所有数据改变时调用\r\n\t\t_totalDataChange(newVal, oldVal, eventThrow=true) {\r\n\t\t\tif ((!this.isUserReload || !this.autoCleanListWhenReload) && this.firstPageLoaded && !newVal.length && oldVal.length) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tthis._doCheckScrollViewShouldFullHeight(newVal);\r\n\t\t\tif(!this.realTotalData.length && !newVal.length){\r\n\t\t\t\teventThrow = false;\r\n\t\t\t}\r\n\t\t\tthis.realTotalData = newVal;\r\n\t\t\t// emit列表更新事件\r\n\t\t\tif (eventThrow) {\r\n\t\t\t\tthis.$emit('input', newVal);\r\n\t\t\t\t// #ifdef VUE3\r\n\t\t\t\tthis.$emit('update:modelValue', newVal);\r\n\t\t\t\t// #endif\r\n\t\t\t\tthis.$emit('update:list', newVal);\r\n\t\t\t\tthis.$emit('listChange', newVal);\r\n\t\t\t\tthis._callMyParentList(newVal);\r\n\t\t\t}\r\n\t\t\tthis.firstPageLoaded = false;\r\n\t\t\tthis.isTotalChangeFromAddData = false;\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tu.delay(()=>{\r\n\t\t\t\t\t// emit z-paging内容区域高度改变事件\r\n\t\t\t\t\tthis._getNodeClientRect('.zp-paging-container-content').then(res => {\r\n\t\t\t\t\t\tres && this.$emit('contentHeightChanged', res[0].height);\r\n\t\t\t\t\t});\r\n\t\t\t\t}, c.delayTime * (this.isIos ? 1 : 3))\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\t// 在nvue中延时600毫秒展示底部加载更多，避免底部加载更多太早加载闪一下的问题\r\n\t\t\t\tu.delay(() => {\r\n\t\t\t\t\tthis.nShowBottom = true;\r\n\t\t\t\t}, c.delayTime * 6, 'nShowBottomDelay');\r\n\t\t\t\t// #endif\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 当前数据改变时调用\r\n\t\t_currentDataChange(newVal, oldVal) {\r\n\t\t\tnewVal = [...newVal];\r\n\t\t\t// #ifndef APP-NVUE\r\n\t\t\tthis.finalUseVirtualList && this._setCellIndex(newVal, 'bottom');\r\n\t\t\t// #endif\r\n\t\t\tif (this.isFirstPage && this.finalConcat) {\r\n\t\t\t\tthis.totalData = [];\r\n\t\t\t}\r\n\t\t\t// customNoMore：-1代表交由z-paging自行判断；1代表没有更多了；0代表还有更多数据\r\n\t\t\tif (this.customNoMore !== -1) {\r\n\t\t\t\t// 如果customNoMore等于1 或者 customNoMore不是0并且新增数组长度为0(也就是不是明确的还有更多数据并且新增的数组长度为0)，则没有更多数据了\r\n\t\t\t\tif (this.customNoMore === 1 || (this.customNoMore !== 0 && !newVal.length)) {\r\n\t\t\t\t\tthis.loadingStatus = Enum.More.NoMore;\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\t// 如果新增的数据数组长度为0 或者 新增的数组长度小于默认的pageSize，则没有更多数据了\r\n\t\t\t\tif (!newVal.length || (newVal.length && newVal.length < this.defaultPageSize)) {\r\n\t\t\t\t\tthis.loadingStatus = Enum.More.NoMore;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (!this.totalData.length) {\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\t// 如果在聊天记录模式+nvue中，并且数据不满一页时需要将列表倒序，因为此时没有将列表旋转180度，数组中第0条数据应当在最底下显示\r\n\t\t\t\tif (this.useChatRecordMode && this.finalConcat && this.isFirstPage && this.loadingStatus === Enum.More.NoMore) {\r\n\t\t\t\t\tnewVal.reverse();\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\tthis.totalData = newVal;\r\n\t\t\t} else {\r\n\t\t\t\tif (this.finalConcat) {\r\n\t\t\t\t\tconst currentScrollTop = this.oldScrollTop;\r\n\t\t\t\t\tthis.totalData = [...this.totalData, ...newVal];\r\n\t\t\t\t\t// 此处是为了解决在微信小程序中，在某些情况下滚动到底部加载更多后滚动位置直接变为最底部的问题，因此需要通过代码强制滚动回加载更多前的位置\r\n\t\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\t\tif (!this.isIos && !this.refresherOnly && !this.usePageScroll && newVal.length) {\r\n\t\t\t\t\t\tthis.loadingMoreTimeStamp = u.getTime();\r\n\t\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\t\tthis.scrollToY(currentScrollTop);\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.totalData = newVal;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tthis.privateConcat = true;\r\n\t\t},\r\n\t\t// 根据pageNo处理refresh操作\r\n\t\t_handleRefreshWithDisPageNo(pageNo) {\r\n\t\t\tif (!this.isHandlingRefreshToPage && !this.realTotalData.length) return this.reload();\r\n\t\t\tif (pageNo >= 1) {\r\n\t\t\t\tthis.loading = true;\r\n\t\t\t\tthis.privateConcat = false;\r\n\t\t\t\tconst totalPageSize = pageNo * this.pageSize;\r\n\t\t\t\tthis.currentRefreshPageSize = totalPageSize;\r\n\t\t\t\t// 如果调用refresh时是本地分页，则在组件内部自己处理分页逻辑，不emit query相关事件\r\n\t\t\t\tif (this.isLocalPaging && this.isHandlingRefreshToPage) {\r\n\t\t\t\t\tthis._localPagingQueryList(this.defaultPageNo, totalPageSize, 0, res => {\r\n\t\t\t\t\t\tthis.complete(res);\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// emit query相关事件\r\n\t\t\t\t\tthis._emitQuery(this.defaultPageNo, totalPageSize, Enum.QueryFrom.Refresh);\r\n\t\t\t\t\tthis._callMyParentQuery(this.defaultPageNo, totalPageSize);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\tthis.dataPromiseResultMap.reload = { resolve, reject };\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 本地分页请求\r\n\t\t_localPagingQueryList(pageNo, pageSize, localPagingLoadingTime, callback) {\r\n\t\t\tpageNo = Math.max(1, pageNo);\r\n\t\t\tpageSize = Math.max(1, pageSize);\r\n\t\t\tconst totalPagingList = [...this.totalLocalPagingList];\r\n\t\t\tconst pageNoIndex = (pageNo - 1) * pageSize;\r\n\t\t\tconst finalPageNoIndex = Math.min(totalPagingList.length, pageNoIndex + pageSize);\r\n\t\t\tconst resultPagingList = totalPagingList.splice(pageNoIndex, finalPageNoIndex - pageNoIndex);\r\n\t\t\tu.delay(() => callback(resultPagingList), localPagingLoadingTime)\r\n\t\t},\r\n\t\t// 存储列表缓存数据\r\n\t\t_saveLocalCache(data) {\r\n\t\t\tuni.setStorageSync(this.finalCacheKey, data);\r\n\t\t},\r\n\t\t// 通过缓存数据填充列表数据\r\n\t\t_setListByLocalCache() {\r\n\t\t\tthis.totalData = uni.getStorageSync(this.finalCacheKey) || [];\r\n\t\t\tthis.isSettingCacheList = true;\r\n\t\t},\r\n\t\t// 修改父view的list\r\n\t\t_callMyParentList(newVal) {\r\n\t\t\tif (this.autowireListName.length) {\r\n\t\t\t\tconst myParent = u.getParent(this.$parent);\r\n\t\t\t\tif (myParent && myParent[this.autowireListName]) {\r\n\t\t\t\t\tmyParent[this.autowireListName] = newVal;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 调用父view的query\r\n\t\t_callMyParentQuery(customPageNo = 0, customPageSize = 0) {\r\n\t\t\tif (this.autowireQueryName) {\r\n\t\t\t\tif (this.myParentQuery === -1) {\r\n\t\t\t\t\tconst myParent = u.getParent(this.$parent);\r\n\t\t\t\t\tif (myParent && myParent[this.autowireQueryName]) {\r\n\t\t\t\t\t\tthis.myParentQuery = myParent[this.autowireQueryName];\r\n\t\t\t\t\t}\r\n\t\t\t\t} \r\n\t\t\t\tif (this.myParentQuery !== -1) {\r\n\t\t\t\t\tcustomPageSize > 0 ? this.myParentQuery(customPageNo, customPageSize) : this.myParentQuery(this.pageNo, this.defaultPageSize);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t// emit query事件\r\n\t\t_emitQuery(pageNo, pageSize, from){\r\n\t\t\tthis.queryFrom = from;\r\n\t\t\tthis.requestTimeStamp = u.getTime();\r\n\t\t\tconst [lastItem] = this.realTotalData.slice(-1);\r\n\t\t\tif (this.fetch) {\r\n\t\t\t\tconst fetchParams = interceptor._handleFetchParams({pageNo, pageSize, from, lastItem: lastItem || null}, this.fetchParams);\r\n\t\t\t\tconst fetchResult = this.fetch(fetchParams);\r\n\t\t\t\tif (!interceptor._handleFetchResult(fetchResult, this, fetchParams)) {\r\n\t\t\t\t\tu.isPromise(fetchResult) ? fetchResult.then(res => {\r\n\t\t\t\t\t\tthis.complete(res);\r\n\t\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t\tthis.complete(false);\r\n\t\t\t\t\t}) : this.complete(fetchResult)\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tthis.$emit('query', ...interceptor._handleQuery(pageNo, pageSize, from, lastItem || null));\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 触发数据改变promise\r\n\t\t_callDataPromise(success, totalList) {\r\n\t\t\tfor (const key in this.dataPromiseResultMap) {\r\n\t\t\t\tconst obj = this.dataPromiseResultMap[key];\r\n\t\t\t\tif (!obj) continue;\r\n\t\t\t\tsuccess ? obj.resolve({ totalList, noMore: this.loadingStatus === Enum.More.NoMore }) : this.callNetworkReject && obj.reject(`z-paging-${key}-error`);\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 检查complete data的类型\r\n\t\t_checkDataType(data, success, isLocal) {\r\n\t\t\tconst dataType = Object.prototype.toString.call(data);\r\n\t\t\tif (dataType === '[object Boolean]') {\r\n\t\t\t\tsuccess = data;\r\n\t\t\t\tdata = [];\r\n\t\t\t} else if (dataType !== '[object Array]') {\r\n\t\t\t\tdata = [];\r\n\t\t\t\tif (dataType !== '[object Undefined]' && dataType !== '[object Null]') {\r\n\t\t\t\t\tu.consoleErr(`${isLocal ? 'setLocalPaging' : 'complete'}参数类型不正确，第一个参数类型必须为Array!`);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn { data, success };\r\n\t\t},\r\n\t}\r\n}\r\n"], "names": ["u", "Enum", "c", "uni", "interceptor"], "mappings": ";;;;;;AAMA,MAAe,mBAAA;AAAA,EACd,OAAO;AAAA;AAAA,IAEN,eAAe;AAAA,MACd,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,iBAAiB,CAAC;AAAA,MAChC,UAAU,SAAS,QAAQ;AAC1B,aAAK,SAAS;AAAA,MACd;AAAA,IACD;AAAA;AAAA,IAED,iBAAiB;AAAA,MAChB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,mBAAmB,EAAE;AAAA,MACnC,WAAW,CAAC,UAAU;AACrB,YAAI,SAAS;AAAGA,iEAAC,EAAC,WAAW,yBAAyB;AACtD,eAAO,QAAQ;AAAA,MACf;AAAA,IACD;AAAA;AAAA,IAED,SAAS;AAAA,MACR,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,MAC7B,SAASA,uDAAC,EAAC,GAAG,WAAW,IAAI;AAAA,IAC7B;AAAA;AAAA,IAED,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,YAAY,KAAK;AAAA,IAC/B;AAAA;AAAA,IAED,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,YAAY,IAAI;AAAA,IAC9B;AAAA;AAAA,IAED,WAAW;AAAA,MACV,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,aAAaC,sDAAI,KAAC,UAAU,OAAO;AAAA,IACjD;AAAA;AAAA,IAED,kBAAkB;AAAA,MACjB,MAAM;AAAA,MACN,SAASD,uDAAC,EAAC,GAAG,oBAAoB,EAAE;AAAA,IACpC;AAAA;AAAA,IAED,mBAAmB;AAAA,MAClB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,qBAAqB,EAAE;AAAA,IACrC;AAAA;AAAA,IAED,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,aAAa;AAAA,MACZ,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,eAAe,IAAI;AAAA,IACjC;AAAA;AAAA,IAED,MAAM;AAAA,MACL,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,QAAQ,IAAI;AAAA,IAC1B;AAAA;AAAA,IAED,mBAAmB;AAAA,MAClB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,qBAAqB,IAAI;AAAA,IACvC;AAAA;AAAA,IAED,2BAA2B;AAAA,MAC1B,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,6BAA6B,IAAI;AAAA,IAC/C;AAAA;AAAA,IAED,yBAAyB;AAAA,MACxB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,2BAA2B,IAAI;AAAA,IAC7C;AAAA;AAAA,IAED,yBAAyB;AAAA,MACxB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,2BAA2B,KAAK;AAAA,IAC9C;AAAA;AAAA,IAED,2BAA2B;AAAA,MAC1B,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,6BAA6B,KAAK;AAAA,IAChD;AAAA;AAAA,IAED,eAAe;AAAA,MACd,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,iBAAiB,KAAK;AAAA,IACpC;AAAA;AAAA,IAED,wBAAwB;AAAA,MACvB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,0BAA0B,GAAG;AAAA,IAC3C;AAAA;AAAA,IAED,QAAQ;AAAA,MACP,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,UAAU,IAAI;AAAA,IAC5B;AAAA;AAAA,IAED,mBAAmB;AAAA,MAClB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,qBAAqB,IAAI;AAAA,IACvC;AAAA;AAAA,IAED,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,WAAW;AACnB,eAAO;MACP;AAAA,IACD;AAAA,IAED,YAAY;AAAA,MACX,MAAM;AAAA,MACN,SAAS,WAAW;AACnB,eAAO;MACP;AAAA,IACD;AAAA,EAED;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,aAAa,CAAE;AAAA,MACf,WAAW,CAAE;AAAA,MACb,eAAe,CAAE;AAAA,MACjB,sBAAsB,CAAE;AAAA,MACxB,sBAAsB;AAAA,QACrB,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,aAAa;AAAA,MACb;AAAA,MACD,oBAAoB;AAAA,MACpB,QAAQ;AAAA,MACR,wBAAwB;AAAA,MACxB,eAAe;AAAA,MACf,aAAa;AAAA,MACb,0BAA0B;AAAA,MAC1B,eAAe;AAAA,MACf,eAAe;AAAA,MACf,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,qBAAqB;AAAA,MACrB,WAAW;AAAA,MACX,eAAe;AAAA,MACf,yBAAyB;AAAA,MACzB,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,IACtB;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACT,WAAW;AACV,aAAO,KAAK;AAAA,IACZ;AAAA,IACD,cAAc;AACb,aAAO,KAAK,UAAU,KAAK;AAAA,IAC3B;AAAA,IACD,gBAAgB;AACf,UAAI,KAAK,YAAY,CAAC,KAAK,UAAU;AACpCA,iEAAE,WAAW,uCAAuC;AAAA,MACpD;AACD,aAAO,KAAK,YAAY,CAAC,CAAC,KAAK;AAAA,IAC/B;AAAA,IACD,gBAAgB;AACf,aAAO,KAAK,WAAW,GAAGE,4DAAE,cAAc,IAAI,KAAK,QAAQ,KAAK;AAAA,IAChE;AAAA,IACD,cAAc;AACb,aAAO,KAAK,WAAW,KAAK;AAAA,IAC5B;AAAA,EACD;AAAA,EACD,OAAO;AAAA,IACN,UAAU,QAAQ,QAAQ;AACzB,WAAK,iBAAiB,QAAQ,QAAQ,KAAK,oBAAoB;AAC/D,WAAK,uBAAuB;AAAA,IAC5B;AAAA,IACD,YAAY,QAAQ,QAAQ;AAC3B,WAAK,mBAAmB,QAAQ,MAAM;AAAA,IACtC;AAAA,IACD,kBAAkB,QAAQ,QAAQ;AACjC,UAAI,QAAQ;AACX,aAAK,0BAA0B;AAAA,MAC/B;AAAA,IACD;AAAA,IACD,OAAO;AAAA,MACN,QAAQ,QAAQ;AAEf,YAAI,WAAW,KAAK,WAAW;AAC9B,eAAK,uBAAuB;AAC5B,eAAK,YAAY;AAAA,QACjB;AAAA,MACD;AAAA,MACD,WAAW;AAAA,IACX;AAAA,IAED,YAAY;AAAA,MACX,QAAQ,QAAQ;AAEf,YAAI,WAAW,KAAK,WAAW;AAC9B,eAAK,uBAAuB;AAC5B,eAAK,YAAY;AAAA,QACjB;AAAA,MACD;AAAA,MACD,WAAW;AAAA,IACX;AAAA,EAED;AAAA,EACD,SAAS;AAAA;AAAA,IAER,SAAS,MAAM,UAAU,MAAM;AAC9B,WAAK,eAAe;AACpB,aAAO,KAAK,QAAQ,MAAM,OAAO;AAAA,IACjC;AAAA;AAAA,IAED,cAAc,MAAM,UAAU,MAAM,UAAU,MAAM;AACnD,UAAI,YAAY,QAAQ,KAAK,YAAY,QAAQ,YAAY,KAAK,SAAS;AAC1E,aAAK,eAAe,KAAK;AACzB,eAAO,IAAI,QAAQ,aAAW,QAAS,CAAA;AAAA,MACvC;AACD,WAAK,eAAe;AACpB,aAAO,KAAK,QAAQ,MAAM,OAAO;AAAA,IACjC;AAAA;AAAA,IAED,gBAAgB,MAAM,OAAO,UAAU,MAAM;AAC5C,UAAI,SAAS,aAAa;AACzB,aAAK,eAAe;AAAA,MACxB,OAAU;AACN,cAAM,cAAc,KAAK,eAAe,MAAM,SAAS,KAAK;AAC5D,eAAO,YAAY;AACnB,kBAAU,YAAY;AACtB,YAAI,SAAS,KAAK,SAAS;AAC1B,iBAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,iBAAK,UAAU,MAAM;AACpB,kBAAI,SAAS;AACb,oBAAM,qBAAqB,KAAK,UAAU,KAAK,gBAAgB,IAAI,KAAK,cAAc;AACtF,oBAAM,aAAa,KAAK,gBAAgB,KAAK,SAAS;AACtD,kBAAI,cAAc,qBAAqB,aAAa;AAEpD,kBAAI,eAAe,GAAG;AACrB,yBAAS;AAET,8BAAc,KAAK,kBAAkB;AACrC,oBAAI,KAAK,iBAAiB,cAAc,KAAK,cAAc,KAAK,QAAQ;AACvE,yBAAO,KAAK,OAAO,GAAG,WAAW;AAAA,gBACjC;AAAA,cACD;AACD,mBAAK,iBAAiB,MAAM,QAAQ,OAAO,EAAE,KAAK,SAAO,QAAQ,GAAG,CAAC,EAAE,MAAM,MAAM,OAAQ,CAAA;AAAA,YAClG,CAAO;AAAA,UACP,CAAM;AAAA,QACD;AAAA,MACD;AACD,aAAO,KAAK,QAAQ,MAAM,OAAO;AAAA,IACjC;AAAA;AAAA,IAED,iBAAiB,MAAM,QAAQ,UAAU,MAAM;AAC9C,UAAI,UAAU,aAAa;AAC1B,aAAK,eAAe,UAAU,OAAO,IAAI;AAAA,MACzC;AACD,aAAO,KAAK,QAAQ,MAAM,OAAO;AAAA,IACjC;AAAA;AAAA,IAED,gBAAgB,UAAU;AACzB,WAAK,6BAA6B;AAClC,aAAO,KAAK,SAAS,KAAK;AAAA,IAC1B;AAAA;AAAA,IAED,QAAQ,MAAM,UAAU,MAAM;AAC7B,UAAI,CAAC,KAAK,kBAAkB;AAC3B,aAAK,uBAAuB;AAC5B,aAAK,mBAAmB;AAAA,MACxB;AACD,YAAM,mBAAmBF,yDAAE;AAC3B,YAAM,UAAU,mBAAmB,KAAK;AACxC,UAAI,WAAW,KAAK;AACpB,UAAI,KAAK,eAAe,KAAK,8BAA8B;AAC1D,mBAAW,KAAK,IAAI,KAAK,QAAQ;AAAA,MACjC;AACD,YAAM,eAAgB,KAAK,mBAAmB,KAAK,UAAU,WAAY,WAAW,UAAU;AAC9F,WAAK,UAAU,MAAM;AACpBA,+DAAC,EAAC,MAAM,MAAM;AACb,eAAK,SAAS,MAAM,SAAS,KAAK;AAAA,QACvC,GAAO,KAAK,QAAQ,IAAI,KAAK,QAAQ,YAAY;AAAA,MACjD,CAAI;AAED,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,aAAK,qBAAqB,WAAW,EAAE,SAAS,OAAM;AAAA,MAC1D,CAAI;AAAA,IACD;AAAA;AAAA,IAED,eAAe,MAAM,QAAQ,MAAM,mBAAmB,MAAM;AAE3D,UAAI,aAAa,CAAC,KAAK;AACvB,aAAO,OAAO,UAAU,SAAS,KAAK,IAAI,MAAM,mBAAmB,CAAC,IAAI,IAAK,aAAa,KAAK,QAAS,IAAG;AAE3G,WAAK,uBAAuB,KAAK,cAAc,MAAM,KAAK;AAG1D,WAAK,YAAY,aAAa,CAAC,GAAG,MAAM,GAAG,KAAK,SAAS,IAAI,CAAC,GAAG,KAAK,WAAW,GAAG,IAAI;AACxF,UAAI,OAAO;AACVA,+DAAAA,EAAE,MAAM,MAAM,KAAK,oBAAoB,KAAK,eAAe,gBAAgB,IAAI,KAAK,YAAY,gBAAgB,CAAC;AAAA,MACjH;AAAA,IACD;AAAA;AAAA,IAED,eAAe,MAAM;AACpB,WAAK,2BAA2B;AAChC,aAAO,OAAO,UAAU,SAAS,KAAK,IAAI,MAAM,mBAAmB,CAAC,IAAI,IAAI;AAC5E,WAAK,YAAY;AAAA,IACjB;AAAA;AAAA,IAED,eAAe,MAAM,UAAU,MAAM;AACpC,WAAK,gBAAgB;AACrB,WAAK,UAAU,MAAM;AACpB,aAAK,SAAS,MAAM,SAAS,IAAI;AAAA,MACrC,CAAI;AACD,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,aAAK,qBAAqB,cAAc,EAAE,SAAS,OAAM;AAAA,MAC7D,CAAI;AAAA,IACD;AAAA;AAAA,IAED,OAAO,UAAU,KAAK,yBAAyB;AAC9C,UAAI,SAAS;AACZ,aAAK,iCAAiC;AACtC,aAAK,iBAAiB;AAAA,MACtB;AACD,UAAI,CAAC,KAAK,2BAA2B;AACpC,aAAK,gBAAgB;AAAA,MACrB;AACD,WAAK,UAAU,MAAM;AACpB,aAAK,WAAW,SAAS,KAAK;AAAA,MAClC,CAAI;AACD,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,aAAK,qBAAqB,SAAS,EAAE,SAAS,OAAM;AAAA,MACxD,CAAI;AAAA,IACD;AAAA;AAAA,IAED,UAAU;AACT,aAAO,KAAK,4BAA4B,KAAK,SAAS,KAAK,gBAAgB,CAAC;AAAA,IAC5E;AAAA;AAAA,IAED,cAAc,QAAQ;AACrB,WAAK,0BAA0B;AAC/B,aAAO,KAAK,4BAA4B,SAAS,KAAK,gBAAgB,CAAC;AAAA,IACvE;AAAA;AAAA,IAED,cAAc;AACb,UAAI,KAAK,iBAAiB,KAAK,UAAU,QAAQ;AAChD,aAAK,gBAAgB,KAAK,UAAU,MAAM,GAAG,KAAK,IAAI,KAAK,UAAU,QAAQ,KAAK,QAAQ,CAAC,CAAC;AAAA,MAC5F;AAAA,IACD;AAAA;AAAA,IAED,QAAQ;AACP,WAAK,QAAQ,IAAI;AACjB,WAAK,SAAS,CAAA,GAAI,MAAM,KAAK;AAAA,IAC7B;AAAA;AAAA,IAED,QAAQ;AACP,WAAK,MAAK;AAAA,IACV;AAAA;AAAA,IAED,WAAW,UAAU,KAAK,yBAAyB,gBAAgB,MAAM,aAAa,GAAG;AACxF,YAAM,gBAAgB,KAAK,yBAAyB,KAAK;AAGzD,UAAI,KAAK,0BAA0B,MAAM,eAAe;AACvDA,+DAAC,EAAC,MAAM,MAAM;AACb;AAIA,cAAI,aAAa,OAAO,GAAG;AAC1B,iBAAK,6BAA4B;AAAA,UACjC;AACD,eAAK,WAAW,SAAS,eAAe,UAAU;AAAA,QACvD,GAAOE,0DAAC,EAAC,YAAY,CAAC;AAClB;AAAA,MACA;AAED,WAAK,eAAe;AACpB,WAAK,cAAcD,2DAAK,YAAY;AACpC,UAAI,SAAS;AACZ,aAAK,iCAAiC;AAEtC,YAAI,KAAK,oBAAoB;AAC5B,eAAK,2BAA0B;AAAA,QACpC,OAAW;AACN,eAAK,qBAAqB;AAAA,QAC1B;AAAA,MAwBL,OAAU;AACN,aAAK,cAAc,OAAO,OAAO,OAAO,KAAK;AAAA,MAC7C;AACD,WAAK,QAAQ,OAAO,aAAa;AAAA,IACjC;AAAA;AAAA,IAED,QAAQ,UAAU,OAAO,gBAAgB,OAAO,iBAAiB,OAAO;AACvE,WAAK,cAAc;AACnB,WAAK,iBAAiB;AACtB,WAAK,wBAAwB;AAC7B,WAAK,SAAS,KAAK;AACnB,WAAK,0BAAyB;AAC9B,OAAC,KAAK,kCAAkC,CAAC,WAAW,KAAK,cAAc,IAAI;AAC3E,WAAK,kBAAkB;AACvB,WAAK,2BAA2B;AAChC,UAAI,CAAC,KAAK,oBAAoB;AAC7B,aAAK,YAAY;MACjB;AACD,UAAI,CAAC,SAAS;AACb,aAAK,WAAW,KAAK,QAAQ,KAAK,iBAAiB,iBAAiBA,sDAAI,KAAC,UAAU,eAAeA,sDAAI,KAAC,UAAU,MAAM;AACvH,YAAI,QAAQ;AAIZD,+DAAAA,EAAE,MAAM,KAAK,oBAAoB,KAAK;AACtC,YAAI,CAAC,iBAAiB,KAAK,2BAA2B;AAKzB,eAAK,aAAa,KAAK;AAAA,QACnD;AAAA,MACD;AAAA,IAMD;AAAA;AAAA,IAED,SAAS,MAAM,SAAS,SAAS;AAChC,WAAK,cAAc;AACnB,WAAK,sBAAsB;AAC3B,WAAK,2BAA2B;AAChC,WAAK,qBAAqB;AAC1B,WAAK,4BAA2B;AAChC,YAAM,qBAAqB,KAAK;AAChC,UAAI,KAAK,2BAA2B,KAAK,aAAa;AACrDA,+DAAC,EAAC,iBAAiBA,uDAAC,EAAC,QAAO,GAAI,KAAK,sBAAsB;AAC3D,aAAK,MAAM,WAAW,KAAK,MAAM,QAAQ;MACzC;AACD,UAAI,CAAC,WAAW,sBAAsB,KAAK,aAAa;AACvD,aAAK,iBAAiB;AAAA,MACtB;AACD,WAAK,gBAAgB;AACrB,WAAK,UAAU,MAAM;AACpBA,+DAAAA,EAAE,MAAM,MAAM,KAAK,gBAAgB,KAAK;AAAA,MAC5C,CAAI;AACD,UAAI,cAAc,KAAK,eAAe,MAAM,SAAS,OAAO;AAC5D,aAAO,YAAY;AACnB,gBAAU,YAAY;AACtB,UAAI,YAAYE,0DAAC,EAAC;AAClB,UAAI,KAAK;AAAmB,oBAAY;AACxC,WAAK,gBAAgB;AACrBF,6DAAC,EAAC,MAAM,MAAM;AACb,aAAK,eAAe;AACpB,aAAK,UAAU,MAAI;AAClB,WAAC,WAAW,KAAK,cAAc,YAAY,GAAG,MAAM,kBAAkB;AAAA,QAC3E,CAAK;AAAA,MACL,CAAI;AACD,UAAI,KAAK,aAAa;AACrB,aAAK,eAAe,CAAC;AACrB,aAAK,MAAM,sBAAsB,KAAK,YAAY;AAClD,YAAI,KAAK,iBAAiB,YAAY,KAAK,cAAcC,2DAAK,UAAU,SAAS,OAAO,KAAK,qBAAqB;AACjH,eAAK,gBAAgB,IAAI;AAAA,QACzB;AAAA,MACD;AACD,WAAK,qBAAqB;AAC1B,UAAI,SAAS;AACZ,YAAI,EAAE,KAAK,kBAAkB,SAAS,CAAC,KAAK,2BAA2B,KAAK,kBAAkBA,sDAAAA,KAAK,KAAK,SAAS;AAChH,eAAK,gBAAgBA,2DAAK,KAAK;AAAA,QAC/B;AACD,YAAI,SAAS;AAEZ,eAAK,uBAAuB;AAC5B,gBAAM,cAAc,KAAK;AACzB,gBAAM,gBAAgB,KAAK,cAAcA,2DAAK,UAAU,UAAU,KAAK,kBAAkB,KAAK;AAC9F,eAAK,sBAAsB,aAAa,eAAe,GAAG,SAAO;AAChED,mEAAC,EAAC,MAAM,MAAM;AACb,mBAAK,gBAAgB,KAAK,KAAK,qBAAqB,MAAM;AAAA,YAC1D,GAAE,CAAC;AAAA,UACV,CAAM;AAAA,QACN,OAAW;AAEN,cAAI,sBAAsB;AAM1BA,iEAAC,EAAC,MAAM,MAAM;AACb,iBAAK,mBAAmB,MAAM,KAAK,WAAW;AAC9C,iBAAK,iBAAiB,MAAM,KAAK,SAAS;AAAA,UAC1C,GAAE,mBAAmB;AAAA,QACtB;AACD,YAAI,KAAK,yBAAyB;AACjC,eAAK,0BAA0B;AAC/B,eAAK,SAAS,KAAK,gBAAgB,KAAK,KAAK,KAAK,SAAS,KAAK,QAAQ,IAAI;AAC5E,cAAI,KAAK,SAAS,KAAK,aAAa,GAAG;AACtC,iBAAK,eAAe;AAAA,UACpB;AAAA,QACD;AAAA,MACL,OAAU;AACN,aAAK,mBAAmB,MAAM,KAAK,WAAW;AAC9C,aAAK,iBAAiB,KAAK;AAC3B,aAAK,gBAAgBC,2DAAK,KAAK;AAC/B,aAAK,0BAA0B;AAC/B,YAAI,KAAK,gBAAgBA,2DAAK,YAAY,UAAU;AACnD,eAAK;AAAA,QACL;AAAA,MACD;AAAA,IACD;AAAA;AAAA,IAED,iBAAiB,QAAQ,QAAQ,aAAW,MAAM;AACjD,WAAK,CAAC,KAAK,gBAAgB,CAAC,KAAK,4BAA4B,KAAK,mBAAmB,CAAC,OAAO,UAAU,OAAO,QAAQ;AACrH;AAAA,MACA;AACD,WAAK,mCAAmC,MAAM;AAC9C,UAAG,CAAC,KAAK,cAAc,UAAU,CAAC,OAAO,QAAO;AAC/C,qBAAa;AAAA,MACb;AACD,WAAK,gBAAgB;AAErB,UAAI,YAAY;AACf,aAAK,MAAM,SAAS,MAAM;AAE1B,aAAK,MAAM,qBAAqB,MAAM;AAEtC,aAAK,MAAM,eAAe,MAAM;AAChC,aAAK,MAAM,cAAc,MAAM;AAC/B,aAAK,kBAAkB,MAAM;AAAA,MAC7B;AACD,WAAK,kBAAkB;AACvB,WAAK,2BAA2B;AAChC,WAAK,UAAU,MAAM;AACpBD,+DAAC,EAAC,MAAM,MAAI;AAEX,eAAK,mBAAmB,8BAA8B,EAAE,KAAK,SAAO;AACnE,mBAAO,KAAK,MAAM,wBAAwB,IAAI,CAAC,EAAE,MAAM;AAAA,UAC7D,CAAM;AAAA,QACN,GAAOE,0DAAAA,EAAE,aAAa,KAAK,QAAQ,IAAI,EAAE;AAAA,MAOzC,CAAI;AAAA,IACD;AAAA;AAAA,IAED,mBAAmB,QAAQ,QAAQ;AAClC,eAAS,CAAC,GAAG,MAAM;AAEnB,WAAK,uBAAuB,KAAK,cAAc,QAAQ,QAAQ;AAE/D,UAAI,KAAK,eAAe,KAAK,aAAa;AACzC,aAAK,YAAY;MACjB;AAED,UAAI,KAAK,iBAAiB,IAAI;AAE7B,YAAI,KAAK,iBAAiB,KAAM,KAAK,iBAAiB,KAAK,CAAC,OAAO,QAAS;AAC3E,eAAK,gBAAgBD,2DAAK,KAAK;AAAA,QAC/B;AAAA,MACL,OAAU;AAEN,YAAI,CAAC,OAAO,UAAW,OAAO,UAAU,OAAO,SAAS,KAAK,iBAAkB;AAC9E,eAAK,gBAAgBA,2DAAK,KAAK;AAAA,QAC/B;AAAA,MACD;AACD,UAAI,CAAC,KAAK,UAAU,QAAQ;AAO3B,aAAK,YAAY;AAAA,MACrB,OAAU;AACN,YAAI,KAAK,aAAa;AACrB,gBAAM,mBAAmB,KAAK;AAC9B,eAAK,YAAY,CAAC,GAAG,KAAK,WAAW,GAAG,MAAM;AAG9C,cAAI,CAAC,KAAK,SAAS,CAAC,KAAK,iBAAiB,CAAC,KAAK,iBAAiB,OAAO,QAAQ;AAC/E,iBAAK,uBAAuBD,yDAAE;AAC9B,iBAAK,UAAU,MAAM;AACpB,mBAAK,UAAU,gBAAgB;AAAA,YACtC,CAAO;AAAA,UACD;AAAA,QAEN,OAAW;AACN,eAAK,YAAY;AAAA,QACjB;AAAA,MACD;AACD,WAAK,gBAAgB;AAAA,IACrB;AAAA;AAAA,IAED,4BAA4B,QAAQ;AACnC,UAAI,CAAC,KAAK,2BAA2B,CAAC,KAAK,cAAc;AAAQ,eAAO,KAAK;AAC7E,UAAI,UAAU,GAAG;AAChB,aAAK,UAAU;AACf,aAAK,gBAAgB;AACrB,cAAM,gBAAgB,SAAS,KAAK;AACpC,aAAK,yBAAyB;AAE9B,YAAI,KAAK,iBAAiB,KAAK,yBAAyB;AACvD,eAAK,sBAAsB,KAAK,eAAe,eAAe,GAAG,SAAO;AACvE,iBAAK,SAAS,GAAG;AAAA,UACvB,CAAM;AAAA,QACN,OAAW;AAEN,eAAK,WAAW,KAAK,eAAe,eAAeC,2DAAK,UAAU,OAAO;AACzE,eAAK,mBAAmB,KAAK,eAAe,aAAa;AAAA,QACzD;AAAA,MACD;AACD,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,aAAK,qBAAqB,SAAS,EAAE,SAAS,OAAM;AAAA,MACxD,CAAI;AAAA,IACD;AAAA;AAAA,IAED,sBAAsB,QAAQ,UAAU,wBAAwB,UAAU;AACzE,eAAS,KAAK,IAAI,GAAG,MAAM;AAC3B,iBAAW,KAAK,IAAI,GAAG,QAAQ;AAC/B,YAAM,kBAAkB,CAAC,GAAG,KAAK,oBAAoB;AACrD,YAAM,eAAe,SAAS,KAAK;AACnC,YAAM,mBAAmB,KAAK,IAAI,gBAAgB,QAAQ,cAAc,QAAQ;AAChF,YAAM,mBAAmB,gBAAgB,OAAO,aAAa,mBAAmB,WAAW;AAC3FD,6DAAAA,EAAE,MAAM,MAAM,SAAS,gBAAgB,GAAG,sBAAsB;AAAA,IAChE;AAAA;AAAA,IAED,gBAAgB,MAAM;AACrBG,oBAAAA,MAAI,eAAe,KAAK,eAAe,IAAI;AAAA,IAC3C;AAAA;AAAA,IAED,uBAAuB;AACtB,WAAK,YAAYA,oBAAI,eAAe,KAAK,aAAa,KAAK;AAC3D,WAAK,qBAAqB;AAAA,IAC1B;AAAA;AAAA,IAED,kBAAkB,QAAQ;AACzB,UAAI,KAAK,iBAAiB,QAAQ;AACjC,cAAM,WAAWH,uDAAC,EAAC,UAAU,KAAK,OAAO;AACzC,YAAI,YAAY,SAAS,KAAK,gBAAgB,GAAG;AAChD,mBAAS,KAAK,gBAAgB,IAAI;AAAA,QAClC;AAAA,MACD;AAAA,IACD;AAAA;AAAA,IAED,mBAAmB,eAAe,GAAG,iBAAiB,GAAG;AACxD,UAAI,KAAK,mBAAmB;AAC3B,YAAI,KAAK,kBAAkB,IAAI;AAC9B,gBAAM,WAAWA,uDAAC,EAAC,UAAU,KAAK,OAAO;AACzC,cAAI,YAAY,SAAS,KAAK,iBAAiB,GAAG;AACjD,iBAAK,gBAAgB,SAAS,KAAK,iBAAiB;AAAA,UACpD;AAAA,QACD;AACD,YAAI,KAAK,kBAAkB,IAAI;AAC9B,2BAAiB,IAAI,KAAK,cAAc,cAAc,cAAc,IAAI,KAAK,cAAc,KAAK,QAAQ,KAAK,eAAe;AAAA,QAC5H;AAAA,MACD;AAAA,IACD;AAAA;AAAA,IAED,WAAW,QAAQ,UAAU,MAAK;AACjC,WAAK,YAAY;AACjB,WAAK,mBAAmBA,yDAAE;AAC1B,YAAM,CAAC,QAAQ,IAAI,KAAK,cAAc,MAAM,EAAE;AAC9C,UAAI,KAAK,OAAO;AACf,cAAM,cAAcI,6DAAW,YAAC,mBAAmB,EAAC,QAAQ,UAAU,MAAM,UAAU,YAAY,KAAI,GAAG,KAAK,WAAW;AACzH,cAAM,cAAc,KAAK,MAAM,WAAW;AAC1C,YAAI,CAACA,6DAAW,YAAC,mBAAmB,aAAa,MAAM,WAAW,GAAG;AACpEJ,iEAAC,EAAC,UAAU,WAAW,IAAI,YAAY,KAAK,SAAO;AAClD,iBAAK,SAAS,GAAG;AAAA,UACvB,CAAM,EAAE,MAAM,SAAO;AACf,iBAAK,SAAS,KAAK;AAAA,UACzB,CAAM,IAAI,KAAK,SAAS,WAAW;AAAA,QAC9B;AAAA,MACL,OAAU;AACN,aAAK,MAAM,SAAS,GAAGI,6DAAW,YAAC,aAAa,QAAQ,UAAU,MAAM,YAAY,IAAI,CAAC;AAAA,MACzF;AAAA,IACD;AAAA;AAAA,IAED,iBAAiB,SAAS,WAAW;AACpC,iBAAW,OAAO,KAAK,sBAAsB;AAC5C,cAAM,MAAM,KAAK,qBAAqB,GAAG;AACzC,YAAI,CAAC;AAAK;AACV,kBAAU,IAAI,QAAQ,EAAE,WAAW,QAAQ,KAAK,kBAAkBH,sDAAAA,KAAK,KAAK,OAAM,CAAE,IAAI,KAAK,qBAAqB,IAAI,OAAO,YAAY,GAAG,QAAQ;AAAA,MACpJ;AAAA,IACD;AAAA;AAAA,IAED,eAAe,MAAM,SAAS,SAAS;AACtC,YAAM,WAAW,OAAO,UAAU,SAAS,KAAK,IAAI;AACpD,UAAI,aAAa,oBAAoB;AACpC,kBAAU;AACV,eAAO,CAAA;AAAA,MACX,WAAc,aAAa,kBAAkB;AACzC,eAAO,CAAA;AACP,YAAI,aAAa,wBAAwB,aAAa,iBAAiB;AACtED,iEAAC,EAAC,WAAW,GAAG,UAAU,mBAAmB,UAAU,0BAA0B;AAAA,QACjF;AAAA,MACD;AACD,aAAO,EAAE,MAAM;IACf;AAAA,EACD;AACF;;"}