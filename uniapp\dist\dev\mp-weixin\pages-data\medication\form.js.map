{"version": 3, "file": "form.js", "sources": ["../../../../../src/pages-data/medication/form.vue", "../../../../../uniPage:/cGFnZXMtZGF0YVxtZWRpY2F0aW9uXGZvcm0udnVl"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n    <PageLayout>\r\n        <template #navbar>\r\n            <NavBar :title=\"mode === 'add' ? '新增' : '编辑'\" :showBack=\"true\" />\r\n        </template>\r\n\r\n        <scroll-view class=\"page-scroll-view\" scroll-y=\"true\">\r\n            <view class=\"form-container\">\r\n                <view class=\"form-header\">\r\n                    <text class=\"form-title\">用药情况</text>\r\n                </view>\r\n\r\n                <!-- 药物选择区域 - 只在新增模式下显示 -->\r\n                <view class=\"form-section\" v-if=\"mode === 'add'\">\r\n                    <view class=\"form-item\">\r\n                        <view class=\"form-item\">\r\n                            <view class=\"inline-form-item\">\r\n                                <!-- 药物名称搜索框 -->\r\n                                <view class=\"inline-form-label\">药物名称</view>\r\n                                <view class=\"search-wrapper\">\r\n                                    <input type=\"text\" v-model=\"searchKeyword\" @input=\"onSearchInput\"\r\n                                        placeholder=\"请输入药物名称搜索\" class=\"search-input\" />\r\n                                    <!-- 搜索结果下拉列表 -->\r\n                                    <view class=\"search-results\"\r\n                                        v-if=\"filteredMedications.length > 0 && showSearchResults\">\r\n                                        <view class=\"search-result-item\" v-for=\"(item, idx) in filteredMedications\"\r\n                                            :key=\"idx\" @tap=\"selectMedication(item)\">\r\n                                            <text>{{ item.specificName }}</text>\r\n                                            <text class=\"category-name\">({{ item.category }})</text>\r\n                                        </view>\r\n                                    </view>\r\n                                </view> <button class=\"add-btn\" @click=\"addMedication\"\r\n                                    :disabled=\"!canAddMedication\">添加</button>\r\n                            </view>\r\n                        </view>\r\n                    </view>\r\n                </view>\r\n\r\n                <!-- 已添加药物列表 -->\r\n                <view class=\"form-section\" v-if=\"formData.medications.length > 0\">\r\n                    <view class=\"medication-list-header\">\r\n                        <text class=\"medication-list-title\">已添加药物</text>\r\n                    </view>\r\n                    <view class=\"medication-item\" v-for=\"(med, index) in formData.medications\" :key=\"index\">\r\n                        <view class=\"medication-name\">\r\n                            {{ med.name }}\r\n                            <!-- <text v-if=\"med.specificName\" class=\"medication-other-name\">({{ med.specificName }})</text> -->\r\n\r\n                            <!-- 添加删除图标按钮 - 新增模式或编辑模式都显示 -->\r\n                            <view class=\"delete-report-btn\" @click=\"removeMedication(index, med.id)\"\r\n                                v-if=\"mode === 'add' || mode === 'view'\">\r\n                                <uni-icons type=\"trash\" size=\"18\" color=\"#FF0000\"></uni-icons>\r\n                            </view>\r\n                        </view>\r\n                        <view class=\"medication-details\">\r\n                            <view class=\"inline-form-item\">\r\n                                <view class=\"inline-form-label\">药物名称</view>\r\n                                <input type=\"text\" class=\"disabled-input\" disabled :value=\"med.specificName\"\r\n                                    placeholder=\"药物具体名称\" />\r\n                            </view>\r\n                            <view class=\"inline-form-item\">\r\n                                <view class=\"inline-form-label\">频率</view>\r\n                                <view class=\"frequency-input-wrapper\">\r\n                                    <picker :value=\"med.frequencyIndex || 0\" :range=\"frequencyOptions\"\r\n                                        @change=\"(e) => onFrequencyChange(e, index)\" class=\"frequency-picker\">\r\n                                        <view class=\"picker\">\r\n                                            <text :class=\"{ placeholder: !med.frequency }\">{{ med.frequency || '请选择'\r\n                                            }}</text>\r\n                                            <uni-icons type=\"bottom\" size=\"12\" color=\"#999999\" />\r\n                                        </view>\r\n                                    </picker>\r\n                                    <text class=\"unit-text\">次/天</text>\r\n                                </view>\r\n                            </view>\r\n                            <view class=\"inline-form-item\">\r\n                                <view class=\"inline-form-label\">剂量</view>\r\n                                <view class=\"dosage-input-wrapper\"> <input type=\"text\" class=\"dosage-input\"\r\n                                        placeholder=\"请输入剂量\" v-model=\"med.dosage\" />\r\n                                    <picker :value=\"med.dosageUnitIndex || 0\" :range=\"dosageUnitOptions\"\r\n                                        @change=\"(e) => onDosageUnitChange(e, index)\" class=\"unit-picker\">\r\n                                        <view class=\"unit-picker-inner\">\r\n                                            <text :class=\"{ placeholder: !med.dosageUnit }\">{{ med.dosageUnit || '单位'\r\n                                            }}</text>\r\n                                            <uni-icons type=\"bottom\" size=\"12\" color=\"#999999\" />\r\n                                        </view>\r\n                                    </picker>\r\n                                </view>\r\n                            </view>\r\n                        </view>\r\n                        <view class=\"delete-btn\" @click=\"removeMedication(index)\" v-if=\"mode === 'add'\">删除</view>\r\n                        <view class=\"delete-btn\" @click=\"removeMedication(index, med.id)\" v-if=\"mode === 'view'\">删除\r\n                        </view>\r\n                    </view>\r\n                </view> <!-- 提交按钮 -->\r\n                <view class=\"submit-section\">\r\n                    <button class=\"submit-btn\" @click=\"submitForm\">{{ mode === 'add' ? '提交' : '保存' }}</button>\r\n                </view>\r\n            </view>\r\n        </scroll-view>\r\n    </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { ref, computed, onMounted, watch } from 'vue'\r\nimport { useUserStore } from '@/store/user'\r\nimport { frequencyOptions, dosageUnitOptions, medicationOptions, specificMedicationOptions } from './medicationOptions'\r\n\r\nconst userStore = useUserStore()\r\n\r\ndefineOptions({\r\n    name: 'medicationForm',\r\n})\r\n// 存储待删除的药物ID列表\r\nconst deleteIds = ref<string[]>([])\r\n\r\n// 获取页面参数\r\nconst query = ref<any>({})\r\nonMounted(() => {\r\n    const pages = getCurrentPages()\r\n    const currentPage = pages[pages.length - 1]\r\n    query.value = (currentPage as any).options || {}\r\n\r\n    // 如果是查看模式，加载数据\r\n    if (query.value.mode === 'view' && query.value.idList) {\r\n        loadFormData(query.value.idList)\r\n    }\r\n\r\n    // 初始化药物搜索数据\r\n    initMedicationSearchData()\r\n\r\n    // 添加点击外部关闭搜索结果的事件\r\n    document.addEventListener('click', () => {\r\n        showSearchResults.value = false\r\n    })\r\n})\r\n\r\n// 表单模式：add 或 view\r\nconst mode = computed(() => query.value.mode || 'add')\r\nconst isViewMode = computed(() => mode.value === 'view')\r\n\r\n\r\n// 表单数据\r\nconst formData = ref({\r\n    date: '',\r\n    medications: [] as {\r\n        id: string; // 添加id字段，用于编辑模式\r\n        name: string;\r\n        frequency: string;\r\n        frequencyIndex?: number;\r\n        dosage: string;\r\n        dosageUnit: string;\r\n        dosageUnitIndex?: number;\r\n        otherName?: string;\r\n        specificName?: string;\r\n        specificMedicationIndex?: number\r\n    }[]\r\n})\r\n\r\n// 搜索相关变量\r\nconst searchKeyword = ref('')\r\nconst showSearchResults = ref(false)\r\nconst selectedSpecificName = ref('')\r\n\r\n// 选中的药物\r\nconst selectedMedicationIndex = ref(0)\r\nconst selectedMedication = ref('')\r\nconst otherMedicationName = ref('')\r\n\r\n// 将药物数据结构化为可搜索的格式\r\nconst allMedications = ref<{ category: string, specificName: string }[]>([])    // 初始化药物搜索数据\r\nconst initMedicationSearchData = () => {\r\n    const medications: { category: string, specificName: string }[] = [];\r\n\r\n    // 遍历所有药物类别和具体药物\r\n    Object.keys(specificMedicationOptions).forEach(category => {\r\n        specificMedicationOptions[category].forEach(specificName => {\r\n            medications.push({\r\n                category,\r\n                specificName\r\n            })\r\n        })\r\n    })\r\n\r\n    allMedications.value = medications\r\n}\r\n\r\n// 根据关键词过滤药物\r\nconst filteredMedications = computed(() => {\r\n    if (!searchKeyword.value) return []\r\n\r\n    return allMedications.value.filter(med =>\r\n        med.specificName.toLowerCase().includes(searchKeyword.value.toLowerCase())\r\n    ).slice(0, 10) // 限制最多显示10条结果\r\n})\r\n\r\n// 搜索输入事件\r\nconst onSearchInput = () => {\r\n    showSearchResults.value = true\r\n}\r\n\r\n// 选择药物\r\nconst selectMedication = (medication: { category: string, specificName: string }) => {\r\n    selectedMedication.value = medication.category\r\n    selectedSpecificName.value = medication.specificName\r\n\r\n    // 查找该药物在类别中的索引\r\n    const categoryIndex = medicationOptions.findIndex(item => item === medication.category)\r\n    if (categoryIndex !== -1) {\r\n        selectedMedicationIndex.value = categoryIndex\r\n    }\r\n\r\n    // 关闭搜索结果\r\n    showSearchResults.value = false\r\n    searchKeyword.value = medication.specificName\r\n}\r\n\r\n// 判断是否可以添加当前选择的药物\r\nconst canAddMedication = computed(() => {\r\n    // 如果搜索框为空，则不能添加\r\n    if (!searchKeyword.value) return false\r\n\r\n    // 允许添加任何药物，包括重复的药物和不在列表中的药物\r\n    return true\r\n})\r\n\r\n// 药物选择变化 - 保留原有方法，但现在主要使用搜索功能\r\nconst onMedicationChange = (e: any) => {\r\n    const index = e.detail.value\r\n    selectedMedicationIndex.value = index\r\n    selectedMedication.value = medicationOptions[index]\r\n    selectedSpecificName.value = '' // 重置具体药物\r\n}\r\n\r\n// 添加药物\r\nconst addMedication = () => {\r\n    if (!canAddMedication.value) return;\r\n\r\n    // 检查输入的药物是否在现有列表中\r\n    const matchedMedication = allMedications.value.find(med =>\r\n        med.specificName.toLowerCase() === searchKeyword.value.toLowerCase()\r\n    );\r\n\r\n    let medicationName = '';\r\n    let specificName = '';\r\n    let specificMedicationIndex = 0;\r\n\r\n    if (matchedMedication) {\r\n        // 如果在列表中找到匹配的药物\r\n        medicationName = matchedMedication.category;\r\n        specificName = matchedMedication.specificName;\r\n        specificMedicationIndex = getSpecificMedicationIndex(medicationName, specificName);\r\n    } else {\r\n        // 如果不在列表中，归类为\"其他\"\r\n        medicationName = '其他';\r\n        specificName = searchKeyword.value;\r\n        specificMedicationIndex = 0; // 默认索引\r\n    }\r\n\r\n    const newMedication = {\r\n        name: medicationName,\r\n        frequency: '',\r\n        frequencyIndex: 0,\r\n        dosage: '',\r\n        dosageUnit: '',\r\n        dosageUnitIndex: 0,\r\n        specificName: specificName,\r\n        specificMedicationIndex: specificMedicationIndex !== -1 ? specificMedicationIndex : 0\r\n    } as any\r\n\r\n    formData.value.medications.push(newMedication)\r\n\r\n    // 重置选择\r\n    selectedMedication.value = ''\r\n    selectedMedicationIndex.value = 0\r\n    selectedSpecificName.value = ''\r\n    searchKeyword.value = ''\r\n}\r\n\r\n// 获取具体药物在类别中的索引\r\nconst getSpecificMedicationIndex = (category: string, specificName: string) => {\r\n    if (!category || !specificName) return -1;\r\n    const options = specificMedicationOptions[category] || [];\r\n    return options.findIndex(name => name === specificName);\r\n}\r\n\r\n// 移除药物\r\nconst removeMedication = (index: number, id?: string) => {\r\n    // 如果是编辑模式且药物有ID，则将ID添加到待删除列表\r\n    if (mode.value === 'view' && id) {\r\n        deleteIds.value.push(id);\r\n    }\r\n    // 从数据列表中移除\r\n    formData.value.medications.splice(index, 1)\r\n}\r\n\r\n// 加载表单数据（根据idList获取数据）\r\nconst loadFormData = (idListStr: string) => {\r\n    // 显示加载中\r\n    uni.showLoading({\r\n        title: '加载中...'\r\n    })\r\n\r\n    // 将逗号分隔的字符串转换为数组\r\n    const idList = idListStr.split(',').filter(id => id)\r\n\r\n    if (idList.length === 0) {\r\n        uni.hideLoading()\r\n        uni.showToast({\r\n            title: '无法获取药物记录ID',\r\n            icon: 'none'\r\n        })\r\n        return\r\n    }\r\n\r\n    console.log('获取药物详情，ID列表:', idList)\r\n    // 调用接口获取数据\r\n    uni.request({\r\n        url: `${import.meta.env.VITE_SERVER_BASEURL}/patient/getMedicationList`,\r\n        method: 'POST',\r\n        data: {\r\n            idList: idList\r\n        },\r\n        success: (res: any) => {\r\n            console.log('获取药物详情数据:', res.data)\r\n\r\n            if (res.data && res.data.code === 200 && res.data.result) {\r\n                const medicationData = res.data.result.records || [];\r\n\r\n                // 清空现有数据\r\n                formData.value.medications = []\r\n\r\n                // 处理返回的药物数据\r\n                medicationData.forEach((item: any) => {\r\n                    // 找到药物所属的类别\r\n                    let category = '其他' // 默认分类为\"其他\"\r\n                    let specificName = item.specificName || ''\r\n\r\n                    // 查找药物类别\r\n                    for (const cat in specificMedicationOptions) {\r\n                        if (specificMedicationOptions[cat].includes(specificName)) {\r\n                            category = cat\r\n                            break\r\n                        }\r\n                    }\r\n\r\n                    // 找出频率和剂量单位的索引\r\n                    const frequencyIndex = frequencyOptions.findIndex(f => f === item.frequency)\r\n                    const dosageUnitIndex = dosageUnitOptions.findIndex(u => u === item.dosageUnit)                    // 添加到表单数据中\r\n                    formData.value.medications.push({\r\n                        id: item.id, // 保存药物记录的ID\r\n                        name: category,\r\n                        specificName: specificName,\r\n                        frequency: item.frequency || '',\r\n                        frequencyIndex: frequencyIndex !== -1 ? frequencyIndex : 0,\r\n                        dosage: item.dosage || '',\r\n                        dosageUnit: item.dosageUnit || '',\r\n                        dosageUnitIndex: dosageUnitIndex !== -1 ? dosageUnitIndex : 0,\r\n                        specificMedicationIndex: getSpecificMedicationIndex(category, specificName)\r\n                    })\r\n                })\r\n\r\n                console.log('处理后的表单数据:', formData.value)\r\n            } else {\r\n                uni.showToast({\r\n                    title: res.data?.msg || '获取数据失败',\r\n                    icon: 'none'\r\n                })\r\n            }\r\n        },\r\n        fail: (err: any) => {\r\n            console.error('获取数据失败:', err)\r\n            uni.showToast({\r\n                title: '网络异常，请稍后重试',\r\n                icon: 'none'\r\n            })\r\n        },\r\n        complete: () => {\r\n            uni.hideLoading()\r\n        }\r\n    })\r\n}\r\n\r\n// 提交表单\r\nconst submitForm = () => {\r\n    // 表单验证\r\n    if (formData.value.medications.length === 0) {\r\n        uni.showToast({\r\n            title: '请至少添加一种药物',\r\n            icon: 'none'\r\n        })\r\n        return\r\n    }\r\n\r\n    // 检查药物信息是否完整\r\n    for (const med of formData.value.medications) {\r\n        if (!med.specificName) {\r\n            uni.showToast({\r\n                title: '请确保药物的具体名称已填写',\r\n                icon: 'none'\r\n            })\r\n            return\r\n        }\r\n    }\r\n\r\n    for (const med of formData.value.medications) {\r\n        if (!med.frequency) {\r\n            uni.showToast({\r\n                title: '请选择药物的频率',\r\n                icon: 'none'\r\n            })\r\n            return\r\n        }\r\n    }\r\n\r\n    for (const med of formData.value.medications) {\r\n        if (!med.dosage) {\r\n            uni.showToast({\r\n                title: '请输入药物的剂量',\r\n                icon: 'none'\r\n            })\r\n            return\r\n        }\r\n    }\r\n\r\n    for (const med of formData.value.medications) {\r\n        if (!med.dosageUnit) {\r\n            uni.showToast({\r\n                title: '请选择药物的剂量单位',\r\n                icon: 'none'\r\n            })\r\n            return\r\n        }\r\n    }\r\n\r\n    // 显示加载中\r\n    uni.showLoading({\r\n        title: '提交中...'\r\n    });\r\n\r\n    const requestData: any = {\r\n        medications: formData.value.medications,\r\n        userId: userStore.userInfo.userid\r\n    }\r\n\r\n    // 如果是编辑模式，添加updateUserId\r\n    if (mode.value === 'view') {\r\n        requestData.updateUserId = userStore.userInfo.userid;\r\n    }\r\n\r\n    // 调用保存接口\r\n    uni.request({\r\n        url: `${import.meta.env.VITE_SERVER_BASEURL}/patient/savemedications`,\r\n        method: 'POST',\r\n        data: requestData,\r\n        success: (res) => {\r\n            // 如果是编辑模式且有待删除的药物ID，则调用删除接口\r\n            if (mode.value === 'view' && deleteIds.value.length > 0) {\r\n                deleteMedications();\r\n            } else {\r\n                uni.hideLoading();\r\n\r\n                if (res.data?.success) {\r\n                    uni.showModal({\r\n                        title: mode.value === 'add' ? '提交成功' : '保存成功',\r\n                        showCancel: false,\r\n                        success: () => {\r\n                            uni.navigateBack();\r\n                        }\r\n                    });\r\n                } else {\r\n                    const errorMsg = res.data?.message || '提交失败，未知错误';\r\n                    uni.showModal({\r\n                        title: mode.value === 'add' ? '提交失败' : '保存失败',\r\n                        content: errorMsg,\r\n                        showCancel: false\r\n                    });\r\n                }\r\n            }\r\n        },\r\n        fail: (err) => {\r\n            uni.hideLoading();\r\n            const errorMsg = err.errMsg || '网络错误，请稍后重试';\r\n            uni.showModal({\r\n                title: '提交失败',\r\n                content: errorMsg,\r\n                showCancel: false\r\n            });\r\n        }\r\n    })\r\n}\r\n\r\n// 删除选中的药物\r\nconst deleteMedications = () => {\r\n    // 如果没有待删除的药物ID，直接返回\r\n    if (deleteIds.value.length === 0) {\r\n        uni.hideLoading();\r\n        uni.showModal({\r\n            title: '保存成功',\r\n            showCancel: false,\r\n            success: () => {\r\n                uni.navigateBack();\r\n            }\r\n        });\r\n        return;\r\n    }\r\n\r\n    // 调用删除接口\r\n    uni.request({\r\n        url: `${import.meta.env.VITE_SERVER_BASEURL}/patient/deletemedication`,\r\n        method: 'POST',\r\n        data: {\r\n            deleteId: deleteIds.value,\r\n        },\r\n        success: (res) => {\r\n            uni.hideLoading();\r\n\r\n            if (res.data?.success) {\r\n                uni.showModal({\r\n                    title: '保存成功',\r\n                    showCancel: false,\r\n                    success: () => {\r\n                        uni.navigateBack();\r\n                    }\r\n                });\r\n            } else {\r\n                const errorMsg = res.data?.message || '删除药物失败，未知错误';\r\n                uni.showModal({\r\n                    title: '保存部分成功',\r\n                    content: '新药物已保存，但删除部分药物失败：' + errorMsg,\r\n                    showCancel: false,\r\n                    success: () => {\r\n                        uni.navigateBack();\r\n                    }\r\n                });\r\n            }\r\n        },\r\n        fail: (err) => {\r\n            uni.hideLoading();\r\n            const errorMsg = err.errMsg || '网络错误，请稍后重试';\r\n            uni.showModal({\r\n                title: '保存部分成功',\r\n                content: '新药物已保存，但删除部分药物失败：' + errorMsg,\r\n                showCancel: false,\r\n                success: () => {\r\n                    uni.navigateBack();\r\n                }\r\n            });\r\n        }\r\n    });\r\n}\r\n\r\n// 获取具体药物选项 - 通过导入的 specificMedicationOptions 使用\r\nconst getSpecificMedicationOptions = (medicationType: string) => {\r\n    return specificMedicationOptions[medicationType] || []\r\n}\r\n\r\n// 具体药物选择变化\r\nconst onSpecificMedicationChange = (e: any, medicationIndex: number) => {\r\n    const index = e.detail.value\r\n    const medication = formData.value.medications[medicationIndex]\r\n\r\n    medication.specificMedicationIndex = index\r\n    const options = getSpecificMedicationOptions(medication.name)\r\n    if (options && options.length > 0) {\r\n        medication.specificName = options[index]\r\n    }\r\n}\r\n\r\n// 频率选择变化\r\nconst onFrequencyChange = (e: any, medicationIndex: number) => {\r\n    const index = e.detail.value\r\n    const medication = formData.value.medications[medicationIndex]\r\n\r\n    medication.frequencyIndex = index\r\n    medication.frequency = frequencyOptions[index]\r\n}\r\n\r\n// 剂量单位选择变化\r\nconst onDosageUnitChange = (e: any, medicationIndex: number) => {\r\n    const index = e.detail.value\r\n    const medication = formData.value.medications[medicationIndex]\r\n\r\n    medication.dosageUnitIndex = index\r\n    medication.dosageUnit = dosageUnitOptions[index]\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-scroll-view {\r\n    height: calc(100vh - 44px);\r\n    /* 减去导航栏高度 */\r\n    width: 100%;\r\n}\r\n\r\n.form-container {\r\n    padding: 20rpx;\r\n    padding-bottom: 120rpx;\r\n    /* 增加底部内边距，防止内容被遮挡 */\r\n\r\n    .form-header {\r\n        margin-bottom: 20rpx;\r\n        text-align: center;\r\n        /* 标题居中 */\r\n\r\n        .form-title {\r\n            font-size: 32rpx;\r\n            font-weight: bold;\r\n            color: #333;\r\n        }\r\n    }\r\n\r\n    .form-section {\r\n        background-color: #FFFFFF;\r\n        border-radius: 12rpx;\r\n        padding: 20rpx;\r\n        margin-bottom: 20rpx;\r\n\r\n        .form-item {\r\n            margin-bottom: 30rpx;\r\n\r\n            .form-label {\r\n                display: block;\r\n                font-size: 28rpx;\r\n                color: #333;\r\n                margin-bottom: 20rpx;\r\n            }\r\n\r\n            .inline-form-item {\r\n                display: flex;\r\n                align-items: center;\r\n                margin-bottom: 20rpx;\r\n\r\n                .inline-form-label {\r\n                    font-size: 28rpx;\r\n                    color: #333;\r\n                    min-width: 160rpx;\r\n                }\r\n\r\n                .inline-form-input {\r\n                    flex: 1;\r\n                    background-color: #F7F7F7;\r\n                    border-radius: 8rpx;\r\n                    padding: 20rpx;\r\n                    font-size: 28rpx;\r\n                    color: #333;\r\n                    box-sizing: border-box;\r\n                    min-height: 80rpx;\r\n\r\n                    &:disabled {\r\n                        background-color: #F5F5F5;\r\n                        color: #666;\r\n                    }\r\n                }\r\n            }\r\n\r\n            .date-picker {\r\n                display: flex;\r\n                justify-content: space-between;\r\n                align-items: center;\r\n                background-color: #F7F7F7;\r\n                border-radius: 8rpx;\r\n                padding: 20rpx;\r\n                font-size: 28rpx;\r\n                color: #333;\r\n                box-sizing: border-box;\r\n                min-height: 80rpx;\r\n\r\n                .placeholder {\r\n                    color: #999;\r\n                }\r\n            }\r\n\r\n            .form-input,\r\n            .form-textarea {\r\n                width: 100%;\r\n                background-color: #F7F7F7;\r\n                border-radius: 8rpx;\r\n                padding: 20rpx;\r\n                font-size: 28rpx;\r\n                color: #333;\r\n                box-sizing: border-box;\r\n                min-height: 80rpx;\r\n\r\n                &:disabled {\r\n                    background-color: #F5F5F5;\r\n                    color: #666;\r\n                }\r\n            }\r\n\r\n            .form-textarea {\r\n                height: 180rpx;\r\n            }\r\n\r\n            .radio-group {\r\n                display: flex;\r\n                flex-direction: column;\r\n                gap: 30rpx;\r\n                /* 增加间距 */\r\n\r\n                .radio-row {\r\n                    display: flex;\r\n                    gap: 150rpx;\r\n                    /* 增加间距 */\r\n                    margin-left: 20rpx;\r\n                    /* 增加左边距，实现对齐 */\r\n                    justify-content: flex-start;\r\n                    /* 添加左对齐 */\r\n                }\r\n\r\n                .radio-item {\r\n                    display: flex;\r\n                    align-items: center;\r\n                    min-width: 180rpx;\r\n                    width: 240rpx;\r\n                    /* 确保宽度一致，增大宽度值以容纳更长的文本 */\r\n\r\n                    .radio-btn {\r\n                        width: 36rpx;\r\n                        height: 36rpx;\r\n                        border-radius: 50%;\r\n                        border: 2rpx solid #CCCCCC;\r\n                        margin-right: 10rpx;\r\n                        position: relative;\r\n\r\n                        &.checked {\r\n                            border-color: #07C160;\r\n\r\n                            &:after {\r\n                                content: '';\r\n                                position: absolute;\r\n                                width: 24rpx;\r\n                                height: 24rpx;\r\n                                background-color: #07C160;\r\n                                border-radius: 50%;\r\n                                top: 50%;\r\n                                left: 50%;\r\n                                transform: translate(-50%, -50%);\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    text {\r\n                        font-size: 28rpx;\r\n                        color: #333;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    // 行内选择器样式\r\n    .inline-picker-wrapper {\r\n        flex: 1;\r\n        width: 100%;\r\n    }\r\n\r\n    .required {\r\n        color: #FF0000;\r\n        margin-right: 4rpx;\r\n    }\r\n\r\n    .submit-section {\r\n        margin-top: 40rpx;\r\n        margin-bottom: 60rpx;\r\n        /* 增加底部间距 */\r\n\r\n        .submit-btn {\r\n            width: 100%;\r\n            background-color: #07C160;\r\n            color: #FFFFFF;\r\n            border-radius: 8rpx;\r\n            font-size: 32rpx;\r\n            padding: 20rpx 0;\r\n        }\r\n    }\r\n}\r\n\r\n.checkbox-group {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 30rpx;\r\n\r\n    .checkbox-row {\r\n        display: flex;\r\n        gap: 150rpx;\r\n        margin-left: 20rpx;\r\n        justify-content: flex-start;\r\n    }\r\n\r\n    .checkbox-item {\r\n        display: flex;\r\n        align-items: center;\r\n        min-width: 180rpx;\r\n        width: 240rpx;\r\n\r\n        .checkbox-btn {\r\n            width: 36rpx;\r\n            height: 36rpx;\r\n            border-radius: 4rpx;\r\n            border: 2rpx solid #CCCCCC;\r\n            margin-right: 10rpx;\r\n            position: relative;\r\n\r\n            &.checked {\r\n                border-color: #07C160;\r\n                background-color: #07C160;\r\n\r\n                &:after {\r\n                    content: '';\r\n                    position: absolute;\r\n                    width: 20rpx;\r\n                    height: 10rpx;\r\n                    border-left: 4rpx solid #FFFFFF;\r\n                    border-bottom: 4rpx solid #FFFFFF;\r\n                    top: 45%;\r\n                    left: 50%;\r\n                    transform: translate(-50%, -50%) rotate(-45deg);\r\n                }\r\n            }\r\n        }\r\n\r\n        text {\r\n            font-size: 28rpx;\r\n            color: #333;\r\n        }\r\n    }\r\n\r\n    .sub-form-item {\r\n        margin-left: 50rpx;\r\n        margin-top: -10rpx;\r\n        margin-bottom: 20rpx;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .sub-form-label {\r\n            font-size: 26rpx;\r\n            color: #666;\r\n            min-width: 180rpx;\r\n        }\r\n\r\n        .sub-form-input {\r\n            flex: 1;\r\n            background-color: #F7F7F7;\r\n            border-radius: 8rpx;\r\n            padding: 15rpx;\r\n            font-size: 26rpx;\r\n            color: #333;\r\n            min-height: 70rpx;\r\n            width: 100%;\r\n            box-sizing: border-box;\r\n        }\r\n\r\n        .picker {\r\n            flex: 1;\r\n            display: flex;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n            background-color: #F7F7F7;\r\n            border-radius: 8rpx;\r\n            padding: 15rpx;\r\n            font-size: 26rpx;\r\n            color: #333;\r\n            min-height: 70rpx;\r\n            width: 100%;\r\n            box-sizing: border-box;\r\n        }\r\n    }\r\n\r\n    .sub-form-items-row {\r\n        display: flex;\r\n        margin-left: 50rpx;\r\n        gap: 20rpx;\r\n        margin-top: -10rpx;\r\n        margin-bottom: 20rpx;\r\n\r\n        .sub-form-item {\r\n            flex: 1;\r\n            width: 50%;\r\n            min-width: calc(50% - 10rpx);\r\n            margin: 0;\r\n            display: flex;\r\n            align-items: center;\r\n\r\n            .picker-container {\r\n                width: 100%;\r\n                flex: 1;\r\n                display: block;\r\n            }\r\n\r\n            .temp-picker {\r\n                width: 100%;\r\n                flex: 1;\r\n                display: block;\r\n            }\r\n\r\n            .sub-form-input {\r\n                flex: 1;\r\n                background-color: #F7F7F7;\r\n                border-radius: 8rpx;\r\n                padding: 15rpx;\r\n                font-size: 26rpx;\r\n                color: #333;\r\n                min-height: 70rpx;\r\n                width: 100%;\r\n                box-sizing: border-box;\r\n            }\r\n\r\n            .picker {\r\n                flex: 1;\r\n                display: flex;\r\n                justify-content: space-between;\r\n                align-items: center;\r\n                background-color: #F7F7F7;\r\n                border-radius: 8rpx;\r\n                padding: 15rpx;\r\n                font-size: 26rpx;\r\n                color: #333;\r\n                min-height: 70rpx;\r\n                width: 100%;\r\n                box-sizing: border-box;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n/* 添加药物选择相关样式 */\r\n.add-btn {\r\n    background-color: #07C160;\r\n    color: #FFFFFF;\r\n    font-size: 28rpx;\r\n    padding: 15rpx 25rpx;\r\n    border-radius: 8rpx;\r\n    margin-left: 20rpx;\r\n\r\n    &:disabled {\r\n        background-color: #cccccc;\r\n    }\r\n}\r\n\r\n.search-wrapper {\r\n    position: relative;\r\n    flex: 1;\r\n\r\n    .search-input {\r\n        width: 100%;\r\n        background-color: #F7F7F7;\r\n        border-radius: 8rpx;\r\n        padding: 20rpx;\r\n        font-size: 28rpx;\r\n        color: #333;\r\n        box-sizing: border-box;\r\n        min-height: 80rpx;\r\n    }\r\n\r\n    .search-results {\r\n        position: absolute;\r\n        top: 100%;\r\n        left: 0;\r\n        right: 0;\r\n        background-color: #FFFFFF;\r\n        border-radius: 8rpx;\r\n        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n        max-height: 400rpx;\r\n        overflow-y: auto;\r\n        z-index: 100;\r\n        margin-top: 5rpx;\r\n\r\n        .search-result-item {\r\n            padding: 20rpx;\r\n            font-size: 28rpx;\r\n            color: #333;\r\n            border-bottom: 1rpx solid #F0F0F0;\r\n\r\n            &:hover,\r\n            &:active {\r\n                background-color: #F7F7F7;\r\n            }\r\n\r\n            .category-name {\r\n                color: #999;\r\n                font-size: 26rpx;\r\n                margin-left: 10rpx;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.picker {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    background-color: #F7F7F7;\r\n    border-radius: 8rpx;\r\n    padding: 15rpx 20rpx;\r\n    font-size: 28rpx;\r\n    color: #333;\r\n    box-sizing: border-box;\r\n    min-height: 80rpx;\r\n\r\n    .placeholder {\r\n        color: #999;\r\n    }\r\n}\r\n\r\n.medication-list-header {\r\n    margin-bottom: 20rpx;\r\n\r\n    .medication-list-title {\r\n        font-size: 30rpx;\r\n        font-weight: bold;\r\n        color: #333;\r\n    }\r\n}\r\n\r\n.medication-item {\r\n    background-color: #F7F7F7;\r\n    border-radius: 12rpx;\r\n    padding: 20rpx;\r\n    margin-bottom: 30rpx;\r\n    position: relative;\r\n    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\r\n\r\n    .medication-name {\r\n        display: flex;\r\n        align-items: center;\r\n        font-size: 32rpx;\r\n        font-weight: bold;\r\n        color: #333;\r\n        margin-bottom: 20rpx;\r\n        padding-bottom: 15rpx;\r\n        border-bottom: 1rpx solid #eaeaea;\r\n\r\n        .medication-other-name {\r\n            font-weight: normal;\r\n            font-size: 28rpx;\r\n            color: #666;\r\n            margin-left: 10rpx;\r\n        }\r\n\r\n        .delete-report-btn {\r\n            margin-left: auto;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            padding: 5rpx;\r\n        }\r\n    }\r\n\r\n    .medication-details {\r\n        display: flex;\r\n        flex-direction: column;\r\n        background-color: #fff;\r\n        border-radius: 8rpx;\r\n        padding: 15rpx;\r\n\r\n        .inline-form-item {\r\n            margin-bottom: 25rpx;\r\n\r\n            &:last-child {\r\n                margin-bottom: 0;\r\n            }\r\n\r\n            .inline-form-label {\r\n                margin-bottom: 12rpx;\r\n                font-weight: 500;\r\n                color: #2C2C2C;\r\n            }\r\n\r\n            .inline-form-input {\r\n                width: 100%;\r\n            }\r\n\r\n            .frequency-input-wrapper,\r\n            .dosage-input-wrapper {\r\n                display: flex;\r\n                align-items: center;\r\n                width: 100%;\r\n\r\n                .picker {\r\n                    flex: 1;\r\n                    min-width: 200rpx;\r\n                    /* 增加最小宽度 */\r\n                    flex-basis: 220rpx;\r\n                    /* 增加基础宽度，使剂量和频率输入框保持一致 */\r\n                    flex-grow: 1;\r\n                    /* 允许适当增长 */\r\n                }\r\n\r\n                .unit-text {\r\n                    margin-left: 10rpx;\r\n                    color: #666;\r\n                    font-size: 26rpx;\r\n                    flex-shrink: 0;\r\n                    width: 120rpx;\r\n                    text-align: center;\r\n                }\r\n\r\n                .dosage-input {\r\n                    flex: 1;\r\n                    flex-basis: 220rpx;\r\n                    /* 与频率选择器保持相同的基础宽度 */\r\n                    flex-grow: 1;\r\n                    /* 允许适当增长 */\r\n                    background-color: #F7F7F7;\r\n                    border-radius: 8rpx;\r\n                    padding: 15rpx 20rpx;\r\n                    font-size: 28rpx;\r\n                    color: #333;\r\n                    min-height: 80rpx;\r\n                    min-width: 200rpx;\r\n                    /* 增加最小宽度，确保与频率选择器保持一致 */\r\n                }\r\n\r\n                .dosage-input::placeholder {\r\n                    color: #999;\r\n                }\r\n\r\n                .unit-picker {\r\n                    width: 120rpx;\r\n                    margin-left: 10rpx;\r\n                    flex-shrink: 0;\r\n\r\n                    .unit-picker-inner {\r\n                        display: flex;\r\n                        align-items: center;\r\n                        justify-content: space-between;\r\n                        /* 改为space-between使文字和图标分开 */\r\n                        background-color: #F7F7F7;\r\n                        border-radius: 8rpx;\r\n                        padding: 15rpx 20rpx;\r\n                        font-size: 28rpx;\r\n                        color: #333;\r\n                        min-height: 80rpx;\r\n                        min-width: 120rpx;\r\n                        /* 确保内容有足够的宽度 */\r\n\r\n                        .placeholder {\r\n                            color: #999;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n\r\n            /* 频率选择器的特殊处理 */\r\n            .frequency-picker {\r\n                width: 100%;\r\n                min-width: 200rpx;\r\n                flex-basis: 220rpx;\r\n                flex-grow: 1;\r\n\r\n                .picker {\r\n                    width: 100%;\r\n                    min-width: 200rpx;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .delete-btn {\r\n        position: absolute;\r\n        top: 20rpx;\r\n        right: 20rpx;\r\n        color: #FF0000;\r\n        font-size: 28rpx;\r\n        background-color: rgba(255, 255, 255, 0.8);\r\n        padding: 5rpx 12rpx;\r\n        border-radius: 6rpx;\r\n        display: none;\r\n        /* 隐藏原删除按钮 */\r\n    }\r\n}\r\n\r\n/* 禁用输入框样式 */\r\n.disabled-input {\r\n    flex: 1;\r\n    background-color: #F5F5F5;\r\n    border-radius: 8rpx;\r\n    padding: 15rpx 20rpx;\r\n    font-size: 28rpx;\r\n    color: #666;\r\n    min-height: 80rpx;\r\n    width: 100%;\r\n    box-sizing: border-box;\r\n}\r\n</style>", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-data/medication/form.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "ref", "onMounted", "computed", "specificMedicationOptions", "medicationOptions", "uni", "frequencyOptions", "dosageUnitOptions"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+GA,UAAM,YAAYA,WAAAA,aAAa;AAMzB,UAAA,YAAYC,cAAc,IAAA,EAAE;AAG5B,UAAA,QAAQA,cAAS,IAAA,EAAE;AACzBC,kBAAAA,UAAU,MAAM;AACZ,YAAM,QAAQ,gBAAgB;AAC9B,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AACpC,YAAA,QAAS,YAAoB,WAAW,CAAC;AAG/C,UAAI,MAAM,MAAM,SAAS,UAAU,MAAM,MAAM,QAAQ;AACtC,qBAAA,MAAM,MAAM,MAAM;AAAA,MAAA;AAIV,+BAAA;AAGhB,eAAA,iBAAiB,SAAS,MAAM;AACrC,0BAAkB,QAAQ;AAAA,MAAA,CAC7B;AAAA,IAAA,CACJ;AAGD,UAAM,OAAOC,cAAAA,SAAS,MAAM,MAAM,MAAM,QAAQ,KAAK;AAClCA,2BAAS,MAAM,KAAK,UAAU,MAAM;AAIvD,UAAM,WAAWF,cAAAA,IAAI;AAAA,MACjB,MAAM;AAAA,MACN,aAAa,CAAA;AAAA,IAAC,CAYjB;AAGK,UAAA,gBAAgBA,kBAAI,EAAE;AACtB,UAAA,oBAAoBA,kBAAI,KAAK;AAC7B,UAAA,uBAAuBA,kBAAI,EAAE;AAG7B,UAAA,0BAA0BA,kBAAI,CAAC;AAC/B,UAAA,qBAAqBA,kBAAI,EAAE;AACLA,kBAAAA,IAAI,EAAE;AAG5B,UAAA,iBAAiBA,cAAkD,IAAA,EAAE;AAC3E,UAAM,2BAA2B,MAAM;AACnC,YAAM,cAA4D,CAAC;AAGnE,aAAO,KAAKG,uCAAAA,yBAAyB,EAAE,QAAQ,CAAY,aAAA;AAC7BA,+CAAAA,0BAAA,QAAQ,EAAE,QAAQ,CAAgB,iBAAA;AACxD,sBAAY,KAAK;AAAA,YACb;AAAA,YACA;AAAA,UAAA,CACH;AAAA,QAAA,CACJ;AAAA,MAAA,CACJ;AAED,qBAAe,QAAQ;AAAA,IAC3B;AAGM,UAAA,sBAAsBD,cAAAA,SAAS,MAAM;AACvC,UAAI,CAAC,cAAc;AAAO,eAAO,CAAC;AAElC,aAAO,eAAe,MAAM;AAAA,QAAO,CAAA,QAC/B,IAAI,aAAa,cAAc,SAAS,cAAc,MAAM,YAAa,CAAA;AAAA,MAAA,EAC3E,MAAM,GAAG,EAAE;AAAA,IAAA,CAChB;AAGD,UAAM,gBAAgB,MAAM;AACxB,wBAAkB,QAAQ;AAAA,IAC9B;AAGM,UAAA,mBAAmB,CAAC,eAA2D;AACjF,yBAAmB,QAAQ,WAAW;AACtC,2BAAqB,QAAQ,WAAW;AAGxC,YAAM,gBAAgBE,uCAAAA,kBAAkB,UAAU,CAAQ,SAAA,SAAS,WAAW,QAAQ;AACtF,UAAI,kBAAkB,IAAI;AACtB,gCAAwB,QAAQ;AAAA,MAAA;AAIpC,wBAAkB,QAAQ;AAC1B,oBAAc,QAAQ,WAAW;AAAA,IACrC;AAGM,UAAA,mBAAmBF,cAAAA,SAAS,MAAM;AAEpC,UAAI,CAAC,cAAc;AAAc,eAAA;AAG1B,aAAA;AAAA,IAAA,CACV;AAWD,UAAM,gBAAgB,MAAM;AACxB,UAAI,CAAC,iBAAiB;AAAO;AAGvB,YAAA,oBAAoB,eAAe,MAAM;AAAA,QAAK,SAChD,IAAI,aAAa,kBAAkB,cAAc,MAAM,YAAY;AAAA,MACvE;AAEA,UAAI,iBAAiB;AACrB,UAAI,eAAe;AACnB,UAAI,0BAA0B;AAE9B,UAAI,mBAAmB;AAEnB,yBAAiB,kBAAkB;AACnC,uBAAe,kBAAkB;AACP,kCAAA,2BAA2B,gBAAgB,YAAY;AAAA,MAAA,OAC9E;AAEc,yBAAA;AACjB,uBAAe,cAAc;AACH,kCAAA;AAAA,MAAA;AAG9B,YAAM,gBAAgB;AAAA,QAClB,MAAM;AAAA,QACN,WAAW;AAAA,QACX,gBAAgB;AAAA,QAChB,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,iBAAiB;AAAA,QACjB;AAAA,QACA,yBAAyB,4BAA4B,KAAK,0BAA0B;AAAA,MACxF;AAES,eAAA,MAAM,YAAY,KAAK,aAAa;AAG7C,yBAAmB,QAAQ;AAC3B,8BAAwB,QAAQ;AAChC,2BAAqB,QAAQ;AAC7B,oBAAc,QAAQ;AAAA,IAC1B;AAGM,UAAA,6BAA6B,CAAC,UAAkB,iBAAyB;AACvE,UAAA,CAAC,YAAY,CAAC;AAAqB,eAAA;AACvC,YAAM,UAAUC,uCAAAA,0BAA0B,QAAQ,KAAK,CAAC;AACxD,aAAO,QAAQ,UAAU,CAAQ,SAAA,SAAS,YAAY;AAAA,IAC1D;AAGM,UAAA,mBAAmB,CAAC,OAAe,OAAgB;AAEjD,UAAA,KAAK,UAAU,UAAU,IAAI;AACnB,kBAAA,MAAM,KAAK,EAAE;AAAA,MAAA;AAG3B,eAAS,MAAM,YAAY,OAAO,OAAO,CAAC;AAAA,IAC9C;AAGM,UAAA,eAAe,CAAC,cAAsB;AAExCE,oBAAAA,MAAI,YAAY;AAAA,QACZ,OAAO;AAAA,MAAA,CACV;AAGK,YAAA,SAAS,UAAU,MAAM,GAAG,EAAE,OAAO,QAAM,EAAE;AAE/C,UAAA,OAAO,WAAW,GAAG;AACrBA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACT;AACD;AAAA,MAAA;AAGI,cAAA,IAAI,gBAAgB,MAAM;AAElCA,oBAAAA,MAAI,QAAQ;AAAA,QACR,KAAK,GAAG,6BAAmC;AAAA,QAC3C,QAAQ;AAAA,QACR,MAAM;AAAA,UACF;AAAA,QACJ;AAAA,QACA,SAAS,CAAC,QAAa;;AACX,kBAAA,IAAI,aAAa,IAAI,IAAI;AAE7B,cAAA,IAAI,QAAQ,IAAI,KAAK,SAAS,OAAO,IAAI,KAAK,QAAQ;AACtD,kBAAM,iBAAiB,IAAI,KAAK,OAAO,WAAW,CAAC;AAG1C,qBAAA,MAAM,cAAc,CAAC;AAGf,2BAAA,QAAQ,CAAC,SAAc;AAElC,kBAAI,WAAW;AACX,kBAAA,eAAe,KAAK,gBAAgB;AAGxC,yBAAW,OAAOF,kEAA2B;AACzC,oBAAIA,uCAA0B,0BAAA,GAAG,EAAE,SAAS,YAAY,GAAG;AAC5C,6BAAA;AACX;AAAA,gBAAA;AAAA,cACJ;AAIJ,oBAAM,iBAAiBG,uCAAAA,iBAAiB,UAAU,CAAK,MAAA,MAAM,KAAK,SAAS;AAC3E,oBAAM,kBAAkBC,uCAAAA,kBAAkB,UAAU,CAAK,MAAA,MAAM,KAAK,UAAU;AACrE,uBAAA,MAAM,YAAY,KAAK;AAAA,gBAC5B,IAAI,KAAK;AAAA;AAAA,gBACT,MAAM;AAAA,gBACN;AAAA,gBACA,WAAW,KAAK,aAAa;AAAA,gBAC7B,gBAAgB,mBAAmB,KAAK,iBAAiB;AAAA,gBACzD,QAAQ,KAAK,UAAU;AAAA,gBACvB,YAAY,KAAK,cAAc;AAAA,gBAC/B,iBAAiB,oBAAoB,KAAK,kBAAkB;AAAA,gBAC5D,yBAAyB,2BAA2B,UAAU,YAAY;AAAA,cAAA,CAC7E;AAAA,YAAA,CACJ;AAEO,oBAAA,IAAI,aAAa,SAAS,KAAK;AAAA,UAAA,OACpC;AACHF,0BAAAA,MAAI,UAAU;AAAA,cACV,SAAO,SAAI,SAAJ,mBAAU,QAAO;AAAA,cACxB,MAAM;AAAA,YAAA,CACT;AAAA,UAAA;AAAA,QAET;AAAA,QACA,MAAM,CAAC,QAAa;AACR,kBAAA,MAAM,WAAW,GAAG;AAC5BA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACT;AAAA,QACL;AAAA,QACA,UAAU,MAAM;AACZA,wBAAAA,MAAI,YAAY;AAAA,QAAA;AAAA,MACpB,CACH;AAAA,IACL;AAGA,UAAM,aAAa,MAAM;AAErB,UAAI,SAAS,MAAM,YAAY,WAAW,GAAG;AACzCA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACT;AACD;AAAA,MAAA;AAIO,iBAAA,OAAO,SAAS,MAAM,aAAa;AACtC,YAAA,CAAC,IAAI,cAAc;AACnBA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACT;AACD;AAAA,QAAA;AAAA,MACJ;AAGO,iBAAA,OAAO,SAAS,MAAM,aAAa;AACtC,YAAA,CAAC,IAAI,WAAW;AAChBA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACT;AACD;AAAA,QAAA;AAAA,MACJ;AAGO,iBAAA,OAAO,SAAS,MAAM,aAAa;AACtC,YAAA,CAAC,IAAI,QAAQ;AACbA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACT;AACD;AAAA,QAAA;AAAA,MACJ;AAGO,iBAAA,OAAO,SAAS,MAAM,aAAa;AACtC,YAAA,CAAC,IAAI,YAAY;AACjBA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACT;AACD;AAAA,QAAA;AAAA,MACJ;AAIJA,oBAAAA,MAAI,YAAY;AAAA,QACZ,OAAO;AAAA,MAAA,CACV;AAED,YAAM,cAAmB;AAAA,QACrB,aAAa,SAAS,MAAM;AAAA,QAC5B,QAAQ,UAAU,SAAS;AAAA,MAC/B;AAGI,UAAA,KAAK,UAAU,QAAQ;AACX,oBAAA,eAAe,UAAU,SAAS;AAAA,MAAA;AAIlDA,oBAAAA,MAAI,QAAQ;AAAA,QACR,KAAK,GAAG,6BAAmC;AAAA,QAC3C,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,SAAS,CAAC,QAAQ;;AAEd,cAAI,KAAK,UAAU,UAAU,UAAU,MAAM,SAAS,GAAG;AACnC,8BAAA;AAAA,UAAA,OACf;AACHA,0BAAAA,MAAI,YAAY;AAEZ,iBAAA,SAAI,SAAJ,mBAAU,SAAS;AACnBA,4BAAAA,MAAI,UAAU;AAAA,gBACV,OAAO,KAAK,UAAU,QAAQ,SAAS;AAAA,gBACvC,YAAY;AAAA,gBACZ,SAAS,MAAM;AACXA,gCAAAA,MAAI,aAAa;AAAA,gBAAA;AAAA,cACrB,CACH;AAAA,YAAA,OACE;AACG,oBAAA,aAAW,SAAI,SAAJ,mBAAU,YAAW;AACtCA,4BAAAA,MAAI,UAAU;AAAA,gBACV,OAAO,KAAK,UAAU,QAAQ,SAAS;AAAA,gBACvC,SAAS;AAAA,gBACT,YAAY;AAAA,cAAA,CACf;AAAA,YAAA;AAAA,UACL;AAAA,QAER;AAAA,QACA,MAAM,CAAC,QAAQ;AACXA,wBAAAA,MAAI,YAAY;AACV,gBAAA,WAAW,IAAI,UAAU;AAC/BA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,UAAA,CACf;AAAA,QAAA;AAAA,MACL,CACH;AAAA,IACL;AAGA,UAAM,oBAAoB,MAAM;AAExB,UAAA,UAAU,MAAM,WAAW,GAAG;AAC9BA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,SAAS,MAAM;AACXA,0BAAAA,MAAI,aAAa;AAAA,UAAA;AAAA,QACrB,CACH;AACD;AAAA,MAAA;AAIJA,oBAAAA,MAAI,QAAQ;AAAA,QACR,KAAK,GAAG,6BAAmC;AAAA,QAC3C,QAAQ;AAAA,QACR,MAAM;AAAA,UACF,UAAU,UAAU;AAAA,QACxB;AAAA,QACA,SAAS,CAAC,QAAQ;;AACdA,wBAAAA,MAAI,YAAY;AAEZ,eAAA,SAAI,SAAJ,mBAAU,SAAS;AACnBA,0BAAAA,MAAI,UAAU;AAAA,cACV,OAAO;AAAA,cACP,YAAY;AAAA,cACZ,SAAS,MAAM;AACXA,8BAAAA,MAAI,aAAa;AAAA,cAAA;AAAA,YACrB,CACH;AAAA,UAAA,OACE;AACG,kBAAA,aAAW,SAAI,SAAJ,mBAAU,YAAW;AACtCA,0BAAAA,MAAI,UAAU;AAAA,cACV,OAAO;AAAA,cACP,SAAS,sBAAsB;AAAA,cAC/B,YAAY;AAAA,cACZ,SAAS,MAAM;AACXA,8BAAAA,MAAI,aAAa;AAAA,cAAA;AAAA,YACrB,CACH;AAAA,UAAA;AAAA,QAET;AAAA,QACA,MAAM,CAAC,QAAQ;AACXA,wBAAAA,MAAI,YAAY;AACV,gBAAA,WAAW,IAAI,UAAU;AAC/BA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,SAAS,sBAAsB;AAAA,YAC/B,YAAY;AAAA,YACZ,SAAS,MAAM;AACXA,4BAAAA,MAAI,aAAa;AAAA,YAAA;AAAA,UACrB,CACH;AAAA,QAAA;AAAA,MACL,CACH;AAAA,IACL;AAoBM,UAAA,oBAAoB,CAAC,GAAQ,oBAA4B;AACrD,YAAA,QAAQ,EAAE,OAAO;AACvB,YAAM,aAAa,SAAS,MAAM,YAAY,eAAe;AAE7D,iBAAW,iBAAiB;AACjB,iBAAA,YAAYC,wDAAiB,KAAK;AAAA,IACjD;AAGM,UAAA,qBAAqB,CAAC,GAAQ,oBAA4B;AACtD,YAAA,QAAQ,EAAE,OAAO;AACvB,YAAM,aAAa,SAAS,MAAM,YAAY,eAAe;AAE7D,iBAAW,kBAAkB;AAClB,iBAAA,aAAaC,yDAAkB,KAAK;AAAA,IACnD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3kBA,GAAG,WAAW,eAAe;"}