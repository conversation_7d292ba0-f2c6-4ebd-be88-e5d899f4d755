{"version": 3, "file": "tree.js", "sources": ["../../../../../src/pages/demo/tree.vue", "../../../../../uniPage:/cGFnZXMvZGVtby90cmVlLnZ1ZQ"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n  <PageLayout navTitle=\"树示例\" backRouteName=\"demo\">\r\n    <view class=\"btnArea bg-white\">\r\n      <view class=\"group\">\r\n        <wd-button @click=\"doCheckedTree(['2'], true)\">全选</wd-button>\r\n        <wd-button @click=\"doCheckedTree(['2'], false)\">取消全选</wd-button>\r\n      </view>\r\n      <view class=\"group\">\r\n        <wd-button @click=\"doExpandTree('all', true)\">展开全部节点</wd-button>\r\n        <wd-button @click=\"doExpandTree('all', false)\">收起全部节点</wd-button>\r\n      </view>\r\n      <view class=\"group\">\r\n        <wd-button @click=\"doExpandTree(['22', '23'], true)\">展开节点</wd-button>\r\n        <wd-button @click=\"doExpandTree(['22', '23'], false)\">收起节点</wd-button>\r\n      </view>\r\n      <view class=\"group\">\r\n        <wd-button @click=\"doCheckedTree(['211', '222'], true)\">选中指定节点</wd-button>\r\n        <wd-button @click=\"doCheckedTree(['211', '222'], false)\">取消选中指定节点</wd-button>\r\n      </view>\r\n    </view>\r\n    <scroll-view class=\"bg-gray-1\" scroll-y>\r\n      <view class=\"content p-2 mt-14px\">\r\n        <view class=\"title mt-5\">多选</view>\r\n        <DaTree\r\n          ref=\"DaTreeRef\"\r\n          :data=\"roomTreeData\"\r\n          labelField=\"name\"\r\n          valueField=\"id\"\r\n          defaultExpandAll\r\n          showCheckbox\r\n          :defaultCheckedKeys=\"defaultCheckedKeysValue\"\r\n          @change=\"handleTreeChange\"\r\n          @expand=\"handleExpandChange\"\r\n        ></DaTree>\r\n      </view>\r\n      <view class=\"content p-2 mt-14px\">\r\n        <view class=\"title mt-5\">单选</view>\r\n        <DaTree\r\n          :data=\"roomTreeData\"\r\n          labelField=\"name\"\r\n          valueField=\"id\"\r\n          defaultExpandAll\r\n          :defaultCheckedKeys=\"defaultCheckedKeysValue2\"\r\n          @change=\"handleTreeChange\"\r\n          @expand=\"handleExpandChange\"\r\n        ></DaTree>\r\n      </view>\r\n      <view class=\"content p-2 mt-14px\">\r\n        <view class=\"title mt-5\">默认展开指定节点</view>\r\n        <DaTree\r\n          :data=\"roomTreeData\"\r\n          labelField=\"name\"\r\n          valueField=\"id\"\r\n          showCheckbox\r\n          :defaultExpandedKeys=\"defaultExpandKeysValue3\"\r\n          @change=\"handleTreeChange\"\r\n          @expand=\"handleExpandChange\"\r\n        ></DaTree>\r\n      </view>\r\n      <view class=\"content p-2 mt-14px mb-14px\">\r\n        <view class=\"title mt-5\">异步加载数据</view>\r\n        <DaTree\r\n          :data=\"roomTreeData\"\r\n          labelField=\"name\"\r\n          valueField=\"id\"\r\n          showCheckbox\r\n          loadMode\r\n          :loadApi=\"GetApiData\"\r\n          defaultExpandAll\r\n          @change=\"handleTreeChange\"\r\n          @expand=\"handleExpandChange\"\r\n        ></DaTree>\r\n      </view>\r\n    </scroll-view>\r\n  </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\n//\r\nimport { defineComponent, ref } from 'vue'\r\n\r\n/**\r\n * 模拟创建一个接口数据\r\n */\r\nfunction GetApiData(currentNode) {\r\n  const { key } = currentNode\r\n\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      // 模拟返回空数据\r\n      if (key.indexOf('-') > -1) {\r\n        return resolve(null)\r\n        // return resolve([])\r\n      }\r\n\r\n      return resolve([\r\n        {\r\n          id: `${key}-1`,\r\n          name: `行政部X${key}-1`,\r\n        },\r\n        {\r\n          id: `${key}-2`,\r\n          name: `财务部X${key}-2`,\r\n          append: '定义了末项数据',\r\n          leaf: true,\r\n        },\r\n        {\r\n          id: `${key}-3`,\r\n          name: `资源部X${key}-3`,\r\n        },\r\n        {\r\n          id: `${key}-4`,\r\n          name: `资源部X${key}-3`,\r\n          append: '被禁用，无展开图标',\r\n          disabled: true,\r\n        },\r\n      ])\r\n    }, 2000)\r\n  })\r\n}\r\n\r\nimport DaTree from '@/uni_modules/da-tree/index.vue'\r\nconst DaTreeRef = ref()\r\n// key的类型必须对应树数据key的类型\r\nconst defaultCheckedKeysValue = ref(['211', '222'])\r\nconst defaultCheckedKeysValue2 = ref('222')\r\nconst defaultExpandKeysValue3 = ref(['212', '231'])\r\nconst roomTreeData = ref([\r\n  {\r\n    id: '2',\r\n    name: '行政中心',\r\n    children: [\r\n      {\r\n        id: '21',\r\n        name: '行政部',\r\n        children: [\r\n          {\r\n            id: '211',\r\n            name: '行政一部',\r\n            children: null,\r\n          },\r\n          {\r\n            id: '212',\r\n            name: '行政二部',\r\n            children: [],\r\n            disabled: true,\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        id: '22',\r\n        name: '财务部',\r\n        children: [\r\n          {\r\n            id: '221',\r\n            name: '财务一部',\r\n            children: [],\r\n            disabled: true,\r\n          },\r\n          {\r\n            id: '222',\r\n            name: '财务二部',\r\n            children: [],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        id: '23',\r\n        name: '人力资源部',\r\n        children: [\r\n          {\r\n            id: '231',\r\n            name: '人力一部',\r\n            children: [],\r\n          },\r\n          {\r\n            id: '232',\r\n            name: '人力二部',\r\n            // append: '更多示例，请下载示例项目查看',\r\n          },\r\n        ],\r\n      },\r\n    ],\r\n  },\r\n])\r\nfunction doExpandTree(keys, expand) {\r\n  DaTreeRef.value?.setExpandedKeys(keys, expand)\r\n\r\n  const gek = DaTreeRef.value?.getExpandedKeys()\r\n  console.log('当前已展开的KEY ==>', gek)\r\n}\r\nfunction doCheckedTree(keys, checked) {\r\n  DaTreeRef.value?.setCheckedKeys(keys, checked)\r\n\r\n  const gek = DaTreeRef.value?.getCheckedKeys()\r\n  console.log('当前已选中的KEY ==>', gek)\r\n}\r\nfunction handleTreeChange(allSelectedKeys, currentItem) {\r\n  console.log('handleTreeChange ==>', allSelectedKeys, currentItem)\r\n}\r\nfunction handleExpandChange(expand, currentItem) {\r\n  console.log('handleExpandChange ==>', expand, currentItem)\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n//\r\n.btnArea {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding-top: 10px;\r\n  .group {\r\n    margin-bottom: 10px;\r\n  }\r\n  :deep(.wd-button) {\r\n    margin: 0 5px;\r\n  }\r\n}\r\n.content {\r\n  background-color: #fff;\r\n  .title {\r\n    font-size: 15px;\r\n  }\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages/demo/tree.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref"], "mappings": ";;;;;;;;;;;;;AA6HA,MAAA,SAAmB,MAAA;;;;AArCnB,aAAS,WAAW,aAAa;AACzB,YAAA,EAAE,QAAQ;AAET,aAAA,IAAI,QAAQ,CAAC,YAAY;AAC9B,mBAAW,MAAM;AAEf,cAAI,IAAI,QAAQ,GAAG,IAAI,IAAI;AACzB,mBAAO,QAAQ,IAAI;AAAA,UAAA;AAIrB,iBAAO,QAAQ;AAAA,YACb;AAAA,cACE,IAAI,GAAG,GAAG;AAAA,cACV,MAAM,OAAO,GAAG;AAAA,YAClB;AAAA,YACA;AAAA,cACE,IAAI,GAAG,GAAG;AAAA,cACV,MAAM,OAAO,GAAG;AAAA,cAChB,QAAQ;AAAA,cACR,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,IAAI,GAAG,GAAG;AAAA,cACV,MAAM,OAAO,GAAG;AAAA,YAClB;AAAA,YACA;AAAA,cACE,IAAI,GAAG,GAAG;AAAA,cACV,MAAM,OAAO,GAAG;AAAA,cAChB,QAAQ;AAAA,cACR,UAAU;AAAA,YAAA;AAAA,UACZ,CACD;AAAA,WACA,GAAI;AAAA,MAAA,CACR;AAAA,IAAA;AAIH,UAAM,YAAYA,cAAAA,IAAI;AAEtB,UAAM,0BAA0BA,cAAA,IAAI,CAAC,OAAO,KAAK,CAAC;AAC5C,UAAA,2BAA2BA,kBAAI,KAAK;AAC1C,UAAM,0BAA0BA,cAAA,IAAI,CAAC,OAAO,KAAK,CAAC;AAClD,UAAM,eAAeA,cAAAA,IAAI;AAAA,MACvB;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,UAAU;AAAA,UACR;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,UAAU;AAAA,cACR;AAAA,gBACE,IAAI;AAAA,gBACJ,MAAM;AAAA,gBACN,UAAU;AAAA,cACZ;AAAA,cACA;AAAA,gBACE,IAAI;AAAA,gBACJ,MAAM;AAAA,gBACN,UAAU,CAAC;AAAA,gBACX,UAAU;AAAA,cAAA;AAAA,YACZ;AAAA,UAEJ;AAAA,UACA;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,UAAU;AAAA,cACR;AAAA,gBACE,IAAI;AAAA,gBACJ,MAAM;AAAA,gBACN,UAAU,CAAC;AAAA,gBACX,UAAU;AAAA,cACZ;AAAA,cACA;AAAA,gBACE,IAAI;AAAA,gBACJ,MAAM;AAAA,gBACN,UAAU,CAAA;AAAA,cAAC;AAAA,YACb;AAAA,UAEJ;AAAA,UACA;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,UAAU;AAAA,cACR;AAAA,gBACE,IAAI;AAAA,gBACJ,MAAM;AAAA,gBACN,UAAU,CAAA;AAAA,cACZ;AAAA,cACA;AAAA,gBACE,IAAI;AAAA,gBACJ,MAAM;AAAA;AAAA,cAAA;AAAA,YAER;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,CACD;AACQ,aAAA,aAAa,MAAM,QAAQ;;AACxB,sBAAA,UAAA,mBAAO,gBAAgB,MAAM;AAEjC,YAAA,OAAM,eAAU,UAAV,mBAAiB;AACrB,cAAA,IAAI,iBAAiB,GAAG;AAAA,IAAA;AAEzB,aAAA,cAAc,MAAM,SAAS;;AAC1B,sBAAA,UAAA,mBAAO,eAAe,MAAM;AAEhC,YAAA,OAAM,eAAU,UAAV,mBAAiB;AACrB,cAAA,IAAI,iBAAiB,GAAG;AAAA,IAAA;AAEzB,aAAA,iBAAiB,iBAAiB,aAAa;AAC9C,cAAA,IAAI,wBAAwB,iBAAiB,WAAW;AAAA,IAAA;AAEzD,aAAA,mBAAmB,QAAQ,aAAa;AACvC,cAAA,IAAI,0BAA0B,QAAQ,WAAW;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5M3D,GAAG,WAAW,eAAe;"}