"use strict";
const plugin_uniMiniRouter_index = require("../plugin/uni-mini-router/index.js");
const common_uitls = require("../common/uitls.js");
const common_vendor = require("../common/vendor.js");
const store_user = require("../store/user.js");
console.log("pagesJson::", common_uitls.e);
const routes = common_vendor.e(common_uitls.e);
setRouteName(routes);
const router = plugin_uniMiniRouter_index.createRouter({
  routes: [...routes]
  // 路由表信息
});
const whiteList = ["/pages/login/login"];
const loginPage = "/pages/login/login";
const beforEach = (to, from, next) => {
  const userStore = store_user.useUserStore();
  if (userStore.isLogined) {
    next(true);
  } else {
    if (whiteList.includes(to.path)) {
      next();
    } else {
      next({ path: loginPage });
    }
  }
};
router.beforeEach(beforEach);
function setRouteName(routes2) {
  routes2.forEach((item) => {
    if (item.path) {
      const name = item.path.split("/").pop();
      item.name = name;
    }
  });
}
exports.beforEach = beforEach;
exports.router = router;
//# sourceMappingURL=index.js.map
