"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../common/vendor.js");
const utils_index = require("../../../utils/index.js");
require("../../../store/index.js");
const common_uitls = require("../../../common/uitls.js");
const utils_is = require("../../../utils/is.js");
const store_user = require("../../../store/user.js");
if (!Array) {
  const _easycom_wd_upload2 = common_vendor.resolveComponent("wd-upload");
  _easycom_wd_upload2();
}
const _easycom_wd_upload = () => "../../../node-modules/wot-design-uni/components/wd-upload/wd-upload.js";
if (!Math) {
  _easycom_wd_upload();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "online-image",
  props: {
    title: {
      type: String,
      default: "",
      required: false
    },
    value: {
      type: String,
      required: false
    },
    disabled: {
      type: Boolean,
      default: false,
      required: false
    },
    name: {
      type: String,
      default: "",
      required: false
    },
    uploadFileType: {
      type: String,
      default: "image",
      required: false
    }
  },
  emits: ["change", "update:value"],
  setup(__props, { emit: __emit }) {
    const toast = common_vendor.useToast();
    const VITE_UPLOAD_BASEURL = `${utils_index.getEnvBaseUploadUrl()}`;
    const props = __props;
    const emit = __emit;
    const fileList = common_vendor.ref([]);
    const customUpload = (file, formData, options) => {
      const userStore = store_user.useUserStore();
      const uploadTask = common_vendor.index.uploadFile({
        url: VITE_UPLOAD_BASEURL,
        header: __spreadValues({
          "X-Access-Token": userStore.userInfo.token,
          "X-Tenant-Id": userStore.userInfo.tenantId
        }, options.header),
        name: options.name,
        fileName: options.name,
        fileType: options.fileType,
        formData,
        filePath: file.url,
        success(res) {
          if (res.statusCode === options.statusCode) {
            let data = res.data;
            if (data && utils_is.isString(data)) {
              data = JSON.parse(data);
            }
            if (data && data.success) {
              const file2 = {
                id: (/* @__PURE__ */ new Date()).getTime(),
                name: options.name,
                path: data.message,
                url: common_uitls.getFileAccessHttpUrl(data.message)
              };
              fileList.value.unshift(file2);
              changeOnlineFormValue();
            }
          } else {
            options.onError(__spreadProps(__spreadValues({}, res), { errMsg: res.errMsg || "" }), file, formData);
          }
        },
        fail(err) {
          console.info("upload fail", err);
          options.onError(err, file, formData);
        }
      });
      uploadTask.onProgressUpdate((res) => {
        options.onProgress(res, file);
      });
    };
    const changeOnlineFormValue = () => {
      console.log("changeOnlineFormValue fileList.value", fileList);
      const arr = fileList.value.map((item) => item["path"]);
      const str = arr.join(",");
      emit("change", str);
      emit("update:value", str);
    };
    const delFile = ({ file, fileList: fileList2, resolve }) => {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要删除吗？",
        cancelText: "取消",
        confirmText: "确定",
        success: (res) => {
          if (res.confirm) {
            console.log("当前删除文件", file);
            changeOnlineFormValue();
            toast.success("删除成功");
            resolve(true);
          }
        },
        fail: (err) => {
          console.log(err);
          resolve(false);
        }
      });
    };
    const loadFile = () => {
      if (!props.value || props.value.length === 0) {
        return;
      }
      const pathArr = props.value.split(",");
      const fileArray = [];
      pathArr.forEach((path) => {
        const seg = path.lastIndexOf("/");
        fileArray.push({
          name: path.substr(seg < 0 ? 0 : seg),
          path,
          url: common_uitls.getFileAccessHttpUrl(path)
        });
      });
      console.log("当前图片回显数据", fileArray);
      fileList.value = [...fileArray];
    };
    common_vendor.watch(
      () => props.value,
      () => {
        loadFile();
      },
      { immediate: true }
    );
    common_vendor.onMounted(() => {
      loadFile();
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(($event) => common_vendor.isRef(fileList) ? fileList.value = $event : null),
        b: common_vendor.p({
          accept: __props.uploadFileType,
          ["upload-method"]: customUpload,
          disabled: __props.disabled,
          ["before-remove"]: delFile,
          ["file-list"]: common_vendor.unref(fileList)
        })
      };
    };
  }
});
wx.createComponent(_sfc_main);
//# sourceMappingURL=online-image.js.map
