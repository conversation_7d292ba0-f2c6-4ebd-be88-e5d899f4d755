{"version": 3, "file": "pcaUtils.js", "sources": ["../../../../../src/common/areaData/pcaUtils.ts"], "sourcesContent": ["import {areaList} from '@vant/area-data'\r\n\r\n// 扁平化的省市区数据\r\nexport const pcaa = freezeDeep(usePlatPcaaData())\r\n\r\n/**\r\n * 获取扁平化的省市区数据\r\n */\r\nfunction usePlatPcaaData() {\r\n  const {city_list: city, county_list: county, province_list: province} = areaList;\r\n  const dataMap = new Map<string, Recordable>()\r\n  const flatData: Recordable = {'86': province}\r\n  // 省\r\n  Object.keys(province).forEach((code) => {\r\n    flatData[code] = {}\r\n    dataMap.set(code.slice(0, 2), flatData[code])\r\n  })\r\n  // 市区\r\n  Object.keys(city).forEach((code) => {\r\n    flatData[code] = {}\r\n    dataMap.set(code.slice(0, 4), flatData[code])\r\n    // 填充上一级\r\n    const getProvince = dataMap.get(code.slice(0, 2))\r\n    if (getProvince) {\r\n      getProvince[code] = city[code]\r\n    }\r\n  });\r\n  // 县\r\n  Object.keys(county).forEach((code) => {\r\n    // 填充上一级\r\n    const getCity = dataMap.get(code.slice(0, 4))\r\n    if (getCity) {\r\n      getCity[code] = county[code]\r\n    }\r\n  });\r\n  return flatData\r\n}\r\n\r\n\r\n/**\r\n *\r\n * 深度冻结对象\r\n * @param obj Object or Array\r\n */\r\nexport function freezeDeep(obj: Recordable | Recordable[]) {\r\n  if (obj != null) {\r\n    if (Array.isArray(obj)) {\r\n      obj.forEach(item => freezeDeep(item))\r\n    } else if (typeof obj === 'object') {\r\n      Object.values(obj).forEach(value => {\r\n        freezeDeep(value)\r\n      })\r\n    }\r\n    Object.freeze(obj)\r\n  }\r\n  return obj\r\n}\r\n"], "names": ["areaList"], "mappings": ";;AAGa,MAAA,OAAO,WAAW,gBAAiB,CAAA;AAKhD,SAAS,kBAAkB;AACzB,QAAM,EAAC,WAAW,MAAM,aAAa,QAAQ,eAAe,aAAYA,cAAA;AAClE,QAAA,8BAAc,IAAwB;AACtC,QAAA,WAAuB,EAAC,MAAM,SAAQ;AAE5C,SAAO,KAAK,QAAQ,EAAE,QAAQ,CAAC,SAAS;AAC7B,aAAA,IAAI,IAAI,CAAC;AACV,YAAA,IAAI,KAAK,MAAM,GAAG,CAAC,GAAG,SAAS,IAAI,CAAC;AAAA,EAAA,CAC7C;AAED,SAAO,KAAK,IAAI,EAAE,QAAQ,CAAC,SAAS;AACzB,aAAA,IAAI,IAAI,CAAC;AACV,YAAA,IAAI,KAAK,MAAM,GAAG,CAAC,GAAG,SAAS,IAAI,CAAC;AAE5C,UAAM,cAAc,QAAQ,IAAI,KAAK,MAAM,GAAG,CAAC,CAAC;AAChD,QAAI,aAAa;AACH,kBAAA,IAAI,IAAI,KAAK,IAAI;AAAA,IAAA;AAAA,EAC/B,CACD;AAED,SAAO,KAAK,MAAM,EAAE,QAAQ,CAAC,SAAS;AAEpC,UAAM,UAAU,QAAQ,IAAI,KAAK,MAAM,GAAG,CAAC,CAAC;AAC5C,QAAI,SAAS;AACH,cAAA,IAAI,IAAI,OAAO,IAAI;AAAA,IAAA;AAAA,EAC7B,CACD;AACM,SAAA;AACT;AAQO,SAAS,WAAW,KAAgC;AACzD,MAAI,OAAO,MAAM;AACX,QAAA,MAAM,QAAQ,GAAG,GAAG;AACtB,UAAI,QAAQ,CAAA,SAAQ,WAAW,IAAI,CAAC;AAAA,IAAA,WAC3B,OAAO,QAAQ,UAAU;AAClC,aAAO,OAAO,GAAG,EAAE,QAAQ,CAAS,UAAA;AAClC,mBAAW,KAAK;AAAA,MAAA,CACjB;AAAA,IAAA;AAEH,WAAO,OAAO,GAAG;AAAA,EAAA;AAEZ,SAAA;AACT;;"}