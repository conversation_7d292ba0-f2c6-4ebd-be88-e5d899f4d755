{"version": 3, "file": "wd-navbar.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-navbar/wd-navbar.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1uYXZiYXIvd2QtbmF2YmFyLnZ1ZQ"], "sourcesContent": ["<template>\n  <view :style=\"{ height: addUnit(height) }\">\n    <view :class=\"`wd-navbar ${customClass} ${fixed ? 'is-fixed' : ''} ${bordered ? 'is-border' : ''}`\" :style=\"rootStyle\">\n      <view class=\"wd-navbar__content\">\n        <view class=\"wd-navbar__capsule\" v-if=\"$slots.capsule\">\n          <slot name=\"capsule\" />\n        </view>\n\n        <view :class=\"`wd-navbar__left ${leftDisabled ? 'is-disabled' : ''}`\" @click=\"handleClickLeft\" v-else-if=\"!$slots.left\">\n          <wd-icon v-if=\"leftArrow\" name=\"arrow-left\" custom-class=\"wd-navbar__arrow\" />\n          <view v-if=\"leftText\" class=\"wd-navbar__text\">{{ leftText }}</view>\n        </view>\n\n        <view v-else :class=\"`wd-navbar__left ${leftDisabled ? 'is-disabled' : ''}`\" @click=\"handleClickLeft\">\n          <slot name=\"left\" />\n        </view>\n\n        <view class=\"wd-navbar__title\">\n          <slot name=\"title\" />\n          <template v-if=\"!$slots.title && title\">{{ title }}</template>\n        </view>\n        <view :class=\"`wd-navbar__right ${rightDisabled ? 'is-disabled' : ''}`\" @click=\"handleClickRight\" v-if=\"$slots.right || rightText\">\n          <slot name=\"right\" />\n\n          <view v-if=\"!$slots.right && rightText\" class=\"wd-navbar__text\" hover-class=\"wd-navbar__text--hover\" :hover-stay-time=\"70\">\n            {{ rightText }}\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n<script lang=\"ts\">\nexport default {\n  name: 'wd-navbar',\n  options: {\n    virtualHost: true,\n    addGlobalClass: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport wdIcon from '../wd-icon/wd-icon.vue'\nimport { type CSSProperties, computed, getCurrentInstance, nextTick, onMounted, ref, watch } from 'vue'\nimport { getRect, addUnit, isDef, objToStyle } from '../common/util'\nimport { navbarProps } from './types'\n\nconst props = defineProps(navbarProps)\nconst emit = defineEmits(['click-left', 'click-right'])\n\nconst height = ref<number | ''>('') // 占位高度\n\nconst { statusBarHeight } = uni.getSystemInfoSync()\n\nwatch(\n  [() => props.fixed, () => props.placeholder],\n  () => {\n    setPlaceholderHeight()\n  },\n  { deep: true, immediate: false }\n)\n\nconst rootStyle = computed(() => {\n  const style: CSSProperties = {}\n  if (props.fixed && isDef(props.zIndex)) {\n    style['z-index'] = props.zIndex\n  }\n  if (props.safeAreaInsetTop) {\n    style['padding-top'] = addUnit(statusBarHeight || 0)\n  }\n  return `${objToStyle(style)}${props.customStyle}`\n})\n\nonMounted(() => {\n  if (props.fixed && props.placeholder) {\n    nextTick(() => {\n      setPlaceholderHeight()\n    })\n  }\n})\n\nfunction handleClickLeft() {\n  if (!props.leftDisabled) {\n    emit('click-left')\n  }\n}\n\nfunction handleClickRight() {\n  if (!props.rightDisabled) {\n    emit('click-right')\n  }\n}\n\nconst { proxy } = getCurrentInstance() as any\n\nfunction setPlaceholderHeight() {\n  if (!props.fixed || !props.placeholder) {\n    return\n  }\n\n  getRect('.wd-navbar', false, proxy).then((res) => {\n    height.value = res.height as number\n  })\n}\n</script>\n\n<style lang=\"scss\">\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-navbar/wd-navbar.vue'\nwx.createComponent(Component)"], "names": ["ref", "uni", "watch", "computed", "isDef", "addUnit", "objToStyle", "onMounted", "nextTick", "getCurrentInstance", "getRect", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AA4CA,MAAA,SAAmB,MAAA;AAXnB,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,EAAA;AAEpB;;;;;AASA,UAAM,QAAQ;AACd,UAAM,OAAO;AAEP,UAAA,SAASA,kBAAiB,EAAE;AAElC,UAAM,EAAE,gBAAA,IAAoBC,cAAA,MAAI,kBAAkB;AAElDC,kBAAA;AAAA,MACE,CAAC,MAAM,MAAM,OAAO,MAAM,MAAM,WAAW;AAAA,MAC3C,MAAM;AACiB,6BAAA;AAAA,MACvB;AAAA,MACA,EAAE,MAAM,MAAM,WAAW,MAAM;AAAA,IACjC;AAEM,UAAA,YAAYC,cAAAA,SAAS,MAAM;AAC/B,YAAM,QAAuB,CAAC;AAC9B,UAAI,MAAM,SAASC,cAAM,MAAA,MAAM,MAAM,GAAG;AAChC,cAAA,SAAS,IAAI,MAAM;AAAA,MAAA;AAE3B,UAAI,MAAM,kBAAkB;AAC1B,cAAM,aAAa,IAAIC,sBAAQ,mBAAmB,CAAC;AAAA,MAAA;AAErD,aAAO,GAAGC,cAAAA,WAAW,KAAK,CAAC,GAAG,MAAM,WAAW;AAAA,IAAA,CAChD;AAEDC,kBAAAA,UAAU,MAAM;AACV,UAAA,MAAM,SAAS,MAAM,aAAa;AACpCC,sBAAAA,WAAS,MAAM;AACQ,+BAAA;AAAA,QAAA,CACtB;AAAA,MAAA;AAAA,IACH,CACD;AAED,aAAS,kBAAkB;AACrB,UAAA,CAAC,MAAM,cAAc;AACvB,aAAK,YAAY;AAAA,MAAA;AAAA,IACnB;AAGF,aAAS,mBAAmB;AACtB,UAAA,CAAC,MAAM,eAAe;AACxB,aAAK,aAAa;AAAA,MAAA;AAAA,IACpB;AAGI,UAAA,EAAE,MAAM,IAAIC,iCAAmB;AAErC,aAAS,uBAAuB;AAC9B,UAAI,CAAC,MAAM,SAAS,CAAC,MAAM,aAAa;AACtC;AAAA,MAAA;AAGFC,oBAAA,QAAQ,cAAc,OAAO,KAAK,EAAE,KAAK,CAAC,QAAQ;AAChD,eAAO,QAAQ,IAAI;AAAA,MAAA,CACpB;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvGH,GAAG,gBAAgBC,SAAS;", "x_google_ignoreList": [0]}