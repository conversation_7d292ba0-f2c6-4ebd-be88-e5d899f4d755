{"version": 3, "file": "wd-button.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-button/wd-button.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1idXR0b24vd2QtYnV0dG9uLnZ1ZQ"], "sourcesContent": ["<template>\n  <button\n    :id=\"buttonId\"\n    :hover-class=\"`${disabled || loading ? '' : 'wd-button--active'}`\"\n    :style=\"customStyle\"\n    :class=\"[\n      'wd-button',\n      'is-' + type,\n      'is-' + size,\n      round ? 'is-round' : '',\n      hairline ? 'is-hairline' : '',\n      plain ? 'is-plain' : '',\n      disabled ? 'is-disabled' : '',\n      block ? 'is-block' : '',\n      loading ? 'is-loading' : '',\n      customClass\n    ]\"\n    :hover-start-time=\"hoverStartTime\"\n    :hover-stay-time=\"hoverStayTime\"\n    :open-type=\"disabled || loading ? undefined : openType\"\n    :send-message-title=\"sendMessageTitle\"\n    :send-message-path=\"sendMessagePath\"\n    :send-message-img=\"sendMessageImg\"\n    :app-parameter=\"appParameter\"\n    :show-message-card=\"showMessageCard\"\n    :session-from=\"sessionFrom\"\n    :lang=\"lang\"\n    :hover-stop-propagation=\"hoverStopPropagation\"\n    :scope=\"scope\"\n    @click=\"handleClick\"\n    @getAuthorize=\"handleGetAuthorize\"\n    @getuserinfo=\"handleGetuserinfo\"\n    @contact=\"handleConcat\"\n    @getphonenumber=\"handleGetphonenumber\"\n    @error=\"handleError\"\n    @launchapp=\"handleLaunchapp\"\n    @opensetting=\"handleOpensetting\"\n    @chooseavatar=\"handleChooseavatar\"\n    @agreeprivacyauthorization=\"handleAgreePrivacyAuthorization\"\n  >\n    <view class=\"wd-button__content\">\n      <view v-if=\"loading\" class=\"wd-button__loading\">\n        <view class=\"wd-button__loading-svg\" :style=\"loadingStyle\"></view>\n      </view>\n      <wd-icon v-else-if=\"icon\" custom-class=\"wd-button__icon\" :name=\"icon\" :classPrefix=\"classPrefix\"></wd-icon>\n      <view class=\"wd-button__text\"><slot /></view>\n    </view>\n  </button>\n</template>\n\n<script lang=\"ts\">\nexport default {\n  name: 'wd-button',\n  options: {\n    addGlobalClass: true,\n    virtualHost: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport wdIcon from '../wd-icon/wd-icon.vue'\nimport { computed, watch } from 'vue'\nimport { ref } from 'vue'\nimport base64 from '../common/base64'\nimport { buttonProps } from './types'\n\nconst loadingIcon = (color = '#4D80F0', reverse = true) => {\n  return `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 42 42\"><defs><linearGradient x1=\"100%\" y1=\"0%\" x2=\"0%\" y2=\"0%\" id=\"a\"><stop stop-color=\"${\n    reverse ? color : '#fff'\n  }\" offset=\"0%\" stop-opacity=\"0\"/><stop stop-color=\"${\n    reverse ? color : '#fff'\n  }\" offset=\"100%\"/></linearGradient></defs><g fill=\"none\" fill-rule=\"evenodd\"><path d=\"M21 1c11.046 0 20 8.954 20 20s-8.954 20-20 20S1 32.046 1 21 9.954 1 21 1zm0 7C13.82 8 8 13.82 8 21s5.82 13 13 13 13-5.82 13-13S28.18 8 21 8z\" fill=\"${\n    reverse ? '#fff' : color\n  }\"/><path d=\"M4.599 21c0 9.044 7.332 16.376 16.376 16.376 9.045 0 16.376-7.332 16.376-16.376\" stroke=\"url(#a)\" stroke-width=\"3.5\" stroke-linecap=\"round\"/></g></svg>`\n}\nconst props = defineProps(buttonProps)\nconst emit = defineEmits([\n  'click',\n  'getuserinfo',\n  'contact',\n  'getphonenumber',\n  'error',\n  'launchapp',\n  'opensetting',\n  'chooseavatar',\n  'agreeprivacyauthorization'\n])\n\nconst hoverStartTime = ref<number>(20)\nconst hoverStayTime = ref<number>(70)\nconst loadingIconSvg = ref<string>('')\n\nconst loadingStyle = computed(() => {\n  return `background-image: url(${loadingIconSvg.value});`\n})\n\nwatch(\n  () => props.loading,\n  () => {\n    buildLoadingSvg()\n  },\n  { deep: true, immediate: true }\n)\n\nfunction handleClick(event: any) {\n  if (!props.disabled && !props.loading) {\n    emit('click', event)\n  }\n}\n\n/**\n * 支付宝小程序授权\n * @param event\n */\nfunction handleGetAuthorize(event: any) {\n  if (props.scope === 'phoneNumber') {\n    handleGetphonenumber(event)\n  } else if (props.scope === 'userInfo') {\n    handleGetuserinfo(event)\n  }\n}\n\nfunction handleGetuserinfo(event: any) {\n  emit('getuserinfo', event.detail)\n}\n\nfunction handleConcat(event: any) {\n  emit('contact', event.detail)\n}\n\nfunction handleGetphonenumber(event: any) {\n  emit('getphonenumber', event.detail)\n}\n\nfunction handleError(event: any) {\n  emit('error', event.detail)\n}\n\nfunction handleLaunchapp(event: any) {\n  emit('launchapp', event.detail)\n}\n\nfunction handleOpensetting(event: any) {\n  emit('opensetting', event.detail)\n}\n\nfunction handleChooseavatar(event: any) {\n  emit('chooseavatar', event.detail)\n}\n\nfunction handleAgreePrivacyAuthorization(event: any) {\n  emit('agreeprivacyauthorization', event.detail)\n}\nfunction buildLoadingSvg() {\n  const { loadingColor, type, plain } = props\n  let color = loadingColor\n  if (!color) {\n    switch (type) {\n      case 'primary':\n        color = '#4D80F0'\n        break\n      case 'success':\n        color = '#34d19d'\n        break\n      case 'info':\n        color = '#333'\n        break\n      case 'warning':\n        color = '#f0883a'\n        break\n      case 'error':\n        color = '#fa4350'\n        break\n      case 'default':\n        color = '#333'\n        break\n    }\n  }\n\n  const svg = loadingIcon(color, !plain)\n  loadingIconSvg.value = `\"data:image/svg+xml;base64,${base64(svg)}\"`\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-button/wd-button.vue'\nwx.createComponent(Component)"], "names": ["ref", "computed", "watch", "base64"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AA8DA,MAAA,SAAmB,MAAA;AAXnB,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,gBAAgB;AAAA,EAAA;AAEpB;;;;;;;;;;;;;;;AAUA,UAAM,cAAc,CAAC,QAAQ,WAAW,UAAU,SAAS;AAClD,aAAA,gJACL,UAAU,QAAQ,MACpB,qDACE,UAAU,QAAQ,MACpB,4OACE,UAAU,SAAS,KACrB;AAAA,IACF;AACA,UAAM,QAAQ;AACd,UAAM,OAAO;AAYP,UAAA,iBAAiBA,kBAAY,EAAE;AAC/B,UAAA,gBAAgBA,kBAAY,EAAE;AAC9B,UAAA,iBAAiBA,kBAAY,EAAE;AAE/B,UAAA,eAAeC,cAAAA,SAAS,MAAM;AAC3B,aAAA,yBAAyB,eAAe,KAAK;AAAA,IAAA,CACrD;AAEDC,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,MAAM;AACY,wBAAA;AAAA,MAClB;AAAA,MACA,EAAE,MAAM,MAAM,WAAW,KAAK;AAAA,IAChC;AAEA,aAAS,YAAY,OAAY;AAC/B,UAAI,CAAC,MAAM,YAAY,CAAC,MAAM,SAAS;AACrC,aAAK,SAAS,KAAK;AAAA,MAAA;AAAA,IACrB;AAOF,aAAS,mBAAmB,OAAY;AAClC,UAAA,MAAM,UAAU,eAAe;AACjC,6BAAqB,KAAK;AAAA,MAAA,WACjB,MAAM,UAAU,YAAY;AACrC,0BAAkB,KAAK;AAAA,MAAA;AAAA,IACzB;AAGF,aAAS,kBAAkB,OAAY;AAChC,WAAA,eAAe,MAAM,MAAM;AAAA,IAAA;AAGlC,aAAS,aAAa,OAAY;AAC3B,WAAA,WAAW,MAAM,MAAM;AAAA,IAAA;AAG9B,aAAS,qBAAqB,OAAY;AACnC,WAAA,kBAAkB,MAAM,MAAM;AAAA,IAAA;AAGrC,aAAS,YAAY,OAAY;AAC1B,WAAA,SAAS,MAAM,MAAM;AAAA,IAAA;AAG5B,aAAS,gBAAgB,OAAY;AAC9B,WAAA,aAAa,MAAM,MAAM;AAAA,IAAA;AAGhC,aAAS,kBAAkB,OAAY;AAChC,WAAA,eAAe,MAAM,MAAM;AAAA,IAAA;AAGlC,aAAS,mBAAmB,OAAY;AACjC,WAAA,gBAAgB,MAAM,MAAM;AAAA,IAAA;AAGnC,aAAS,gCAAgC,OAAY;AAC9C,WAAA,6BAA6B,MAAM,MAAM;AAAA,IAAA;AAEhD,aAAS,kBAAkB;AACzB,YAAM,EAAE,cAAc,MAAM,MAAU,IAAA;AACtC,UAAI,QAAQ;AACZ,UAAI,CAAC,OAAO;AACV,gBAAQ,MAAM;AAAA,UACZ,KAAK;AACK,oBAAA;AACR;AAAA,UACF,KAAK;AACK,oBAAA;AACR;AAAA,UACF,KAAK;AACK,oBAAA;AACR;AAAA,UACF,KAAK;AACK,oBAAA;AACR;AAAA,UACF,KAAK;AACK,oBAAA;AACR;AAAA,UACF,KAAK;AACK,oBAAA;AACR;AAAA,QAAA;AAAA,MACJ;AAGF,YAAM,MAAM,YAAY,OAAO,CAAC,KAAK;AACrC,qBAAe,QAAQ,8BAA8BC,cAAAA,OAAO,GAAG,CAAC;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrLlE,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}