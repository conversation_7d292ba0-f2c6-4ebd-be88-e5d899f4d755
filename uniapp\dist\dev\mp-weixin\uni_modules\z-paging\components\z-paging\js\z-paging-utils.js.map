{"version": 3, "file": "z-paging-utils.js", "sources": ["../../../../../../../../src/uni_modules/z-paging/components/z-paging/js/z-paging-utils.js"], "sourcesContent": ["// [z-paging]工具类\r\n\r\nimport zLocalConfig from '../config/index'\r\nimport c from './z-paging-constant'\r\n\r\nconst storageKey = 'Z-PAGING-REFRESHER-TIME-STORAGE-KEY';\r\nlet config = null;\r\nlet configLoaded = false;\r\nconst timeoutMap = {};\r\n\r\n// #ifdef APP-HARMONY\r\nlet screenWidth = 0;\r\n// #endif\r\n\r\n// 获取默认配置信息\r\nfunction gc(key, defaultValue) {\r\n\t// 这里return一个函数以解决在vue3+appvue中，props默认配置读取在main.js之前执行导致uni.$zp全局配置无效的问题。相当于props的default中传入一个带有返回值的函数\r\n\treturn () => {\r\n\t\t// 处理z-paging全局配置\r\n\t\t_handleDefaultConfig();\r\n\t\t// 如果全局配置不存在，则返回默认值\r\n\t\tif (!config) return defaultValue;\r\n\t\tconst value = config[key];\r\n\t\t// 如果全局配置存在但对应的配置项不存在，则返回默认值；反之返回配置项\r\n\t\treturn value === undefined ? defaultValue : value;\r\n\t};\r\n}\r\n\r\n// 获取最终的touch位置\r\nfunction getTouch(e) {\r\n\tlet touch = null;\r\n\tif (e.touches && e.touches.length) {\r\n\t\ttouch = e.touches[0];\r\n\t} else if (e.changedTouches && e.changedTouches.length) {\r\n\t\ttouch = e.changedTouches[0];\r\n\t} else if (e.datail && e.datail != {}) {\r\n\t\ttouch = e.datail;\r\n\t} else {\r\n\t\treturn { touchX: 0, touchY: 0 }\r\n\t}\r\n\treturn {\r\n\t\ttouchX: touch.clientX,\r\n\t\ttouchY: touch.clientY\r\n\t};\r\n}\r\n\r\n// 判断当前手势是否在z-paging内触发\r\nfunction getTouchFromZPaging(target) {\r\n\tif (target && target.tagName && target.tagName !== 'BODY' && target.tagName !== 'UNI-PAGE-BODY') {\r\n\t\tconst classList = target.classList;\r\n\t\tif (classList && classList.contains('z-paging-content')) {\r\n\t\t\t// 此处额外记录当前z-paging是否是页面滚动、是否滚动到了顶部、是否是聊天记录模式以传给renderjs。避免不同z-paging组件renderjs内部判断数据互相影响导致的各种问题\r\n\t\t\treturn {\r\n\t\t\t\tisFromZp: true,\r\n\t\t\t\tisPageScroll: classList.contains('z-paging-content-page'),\r\n\t\t\t\tisReachedTop: classList.contains('z-paging-reached-top'),\r\n\t\t\t\tisUseChatRecordMode: classList.contains('z-paging-use-chat-record-mode')\r\n\t\t\t};\r\n\t\t} else {\r\n\t\t\treturn getTouchFromZPaging(target.parentNode);\r\n\t\t}\r\n\t} else {\r\n\t\treturn { isFromZp: false };\r\n\t}\r\n}\r\n\r\n// 递归获取z-paging所在的parent，如果查找不到则返回null\r\nfunction getParent(parent) {\r\n\tif (!parent) return null;\r\n\tif (parent.$refs.paging) return parent;\r\n\treturn getParent(parent.$parent);\r\n}\r\n\r\n// 打印错误信息\r\nfunction consoleErr(err) {\r\n\tconsole.error(`[z-paging]${err}`);\r\n}\r\n\r\n// 延时操作，如果key存在，调用时清除对应key之前的延时操作\r\nfunction delay(callback, ms = c.delayTime, key) {\r\n\tconst timeout = setTimeout(callback, ms);;\r\n\tif (!!key) {\r\n\t\ttimeoutMap[key] && clearTimeout(timeoutMap[key]);\r\n\t\ttimeoutMap[key] = timeout;\r\n\t}\r\n\treturn timeout;\r\n}\r\n\r\n// 设置下拉刷新时间\r\nfunction setRefesrherTime(time, key) {\r\n\tconst datas = getRefesrherTime() || {};\r\n\tdatas[key] = time;\r\n\tuni.setStorageSync(storageKey, datas);\r\n}\r\n\r\n// 获取下拉刷新时间\r\nfunction getRefesrherTime() {\r\n\treturn uni.getStorageSync(storageKey);\r\n}\r\n\r\n// 通过下拉刷新标识key获取下拉刷新时间\r\nfunction getRefesrherTimeByKey(key) {\r\n\tconst datas = getRefesrherTime();\r\n\treturn datas && datas[key] ? datas[key] : null;\r\n}\r\n\r\n// 通过下拉刷新标识key获取下拉刷新时间(格式化之后)\r\nfunction getRefesrherFormatTimeByKey(key, textMap) {\r\n\tconst time = getRefesrherTimeByKey(key);\r\n\tconst timeText = time ? _timeFormat(time, textMap) : textMap.none;\r\n\treturn `${textMap.title}${timeText}`;\r\n}\r\n\r\n// 将文本的px或者rpx转为px的值\r\nfunction convertToPx(text) {\r\n\tconst dataType = Object.prototype.toString.call(text);\r\n\tif (dataType === '[object Number]') return text;\r\n\tlet isRpx = false;\r\n\tif (text.indexOf('rpx') !== -1 || text.indexOf('upx') !== -1) {\r\n\t\ttext = text.replace('rpx', '').replace('upx', '');\r\n\t\tisRpx = true;\r\n\t} else if (text.indexOf('px') !== -1) {\r\n\t\ttext = text.replace('px', '');\r\n\t}\r\n\tif (!isNaN(text)) {\r\n\t\tif (isRpx) return Number(rpx2px(text));\r\n\t\treturn Number(text);\r\n\t}\r\n\treturn 0;\r\n}\r\n\r\n// rpx => px，兼容鸿蒙\r\nfunction rpx2px(rpx) {\r\n\t// #ifdef APP-HARMONY\r\n\tif (!screenWidth) {\r\n\t\tscreenWidth = uni.getSystemInfoSync().screenWidth;\r\n\t}\r\n\treturn (screenWidth * Number.parseFloat(rpx)) / 750;\r\n\t// #endif\r\n\t// #ifndef APP-HARMONY\r\n\treturn uni.upx2px(rpx);\r\n\t// #endif\r\n}\r\n\r\n// 获取当前时间\r\nfunction getTime() {\r\n\treturn (new Date()).getTime();\r\n}\r\n\r\n// 获取z-paging实例id，随机生成10位数字+字母\r\nfunction getInstanceId() {\r\n    const s = [];\r\n    const hexDigits = \"0123456789abcdef\";\r\n    for (let i = 0; i < 10; i++) {\r\n        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);\r\n    }\r\n    return s.join('') + getTime();\r\n}\r\n\r\n// 等待一段时间\r\nfunction wait(ms) {\r\n\treturn new Promise(resolve => {\r\n\t\tsetTimeout(resolve, ms);\r\n\t});\r\n}\r\n\r\n// 是否是promise\r\nfunction isPromise(func) {\r\n\treturn Object.prototype.toString.call(func) === '[object Promise]';\r\n}\r\n\r\n// 添加单位\r\nfunction addUnit(value, unit) {\r\n\tif (Object.prototype.toString.call(value) === '[object String]') {\r\n\t\tlet tempValue = value;\r\n\t\ttempValue = tempValue.replace('rpx', '').replace('upx', '').replace('px', '');\r\n\t\tif (value.indexOf('rpx') === -1 && value.indexOf('upx') === -1 && value.indexOf('px') !== -1) {\r\n\t\t\ttempValue = parseFloat(tempValue) * 2;\r\n\t\t}\r\n\t\tvalue = tempValue;\r\n\t}\r\n\treturn unit === 'rpx' ? value + 'rpx' : (value / 2) + 'px';\r\n}\r\n\r\n// 深拷贝\r\nfunction deepCopy(obj) {\r\n\tif (typeof obj !== 'object' || obj === null) return obj;\r\n\tlet newObj = Array.isArray(obj) ? [] : {};\r\n\tfor (let key in obj) {\r\n\t\tif (obj.hasOwnProperty(key)) {\r\n\t\t\tnewObj[key] = deepCopy(obj[key]);\r\n\t\t}\r\n\t}\r\n\treturn newObj;\r\n}\r\n\r\n// ------------------ 私有方法 ------------------------\r\n// 处理全局配置\r\nfunction _handleDefaultConfig() {\r\n\t// 确保只加载一次全局配置\r\n\tif (configLoaded) return;\r\n\t// 优先从config.js中读取\r\n\tif (zLocalConfig && Object.keys(zLocalConfig).length) {\r\n\t\tconfig = zLocalConfig;\r\n\t}\r\n\t// 如果在config.js中读取不到，则尝试到uni.$zp读取\r\n\tif (!config && uni.$zp) {\r\n\t\tconfig = uni.$zp.config;\r\n\t}\r\n\t// 将config中的短横线写法全部转为驼峰写法，使得读取配置时可以直接通过key去匹配，而非读取每个配置时候再去转，减少不必要的性能开支\r\n\tconfig = config ? Object.keys(config).reduce((result, key) => {\r\n\t    result[_toCamelCase(key)] = config[key];\r\n\t    return result;\r\n\t}, {}) : null;\r\n\tconfigLoaded = true;\r\n}\r\n\r\n// 时间格式化\r\nfunction _timeFormat(time, textMap) {\r\n\tconst date = new Date(time);\r\n\tconst currentDate = new Date();\r\n\t// 设置time对应的天，去除时分秒，使得可以直接比较日期\r\n\tconst dateDay = new Date(time).setHours(0, 0, 0, 0);\r\n\t// 设置当前的天，去除时分秒，使得可以直接比较日期\r\n\tconst currentDateDay = new Date().setHours(0, 0, 0, 0);\r\n\tconst disTime = dateDay - currentDateDay;\r\n\tlet dayStr = '';\r\n\tconst timeStr = _dateTimeFormat(date);\r\n\tif (disTime === 0) {\r\n\t\tdayStr = textMap.today;\r\n\t} else if (disTime === -86400000) {\r\n\t\tdayStr = textMap.yesterday;\r\n\t} else {\r\n\t\tdayStr = _dateDayFormat(date, date.getFullYear() !== currentDate.getFullYear());\r\n\t}\r\n\treturn `${dayStr} ${timeStr}`;\r\n}\r\n\r\n// date格式化为年月日\r\nfunction _dateDayFormat(date, showYear = true) {\r\n\tconst year = date.getFullYear();\r\n\tconst month = date.getMonth() + 1;\r\n\tconst day = date.getDate();\r\n\treturn showYear ? `${year}-${_fullZeroToTwo(month)}-${_fullZeroToTwo(day)}` : `${_fullZeroToTwo(month)}-${_fullZeroToTwo(day)}`;\r\n}\r\n\r\n// data格式化为时分\r\nfunction _dateTimeFormat(date) {\r\n\tconst hour = date.getHours();\r\n\tconst minute = date.getMinutes();\r\n\treturn `${_fullZeroToTwo(hour)}:${_fullZeroToTwo(minute)}`;\r\n}\r\n\r\n// 不满2位在前面填充0\r\nfunction _fullZeroToTwo(str) {\r\n\tstr = str.toString();\r\n\treturn str.length === 1 ? '0' + str : str;\r\n}\r\n\r\n// 驼峰转短横线\r\nfunction _toKebab(value) {\r\n\treturn value.replace(/([A-Z])/g, \"-$1\").toLowerCase();\r\n}\r\n\r\n// 短横线转驼峰\r\nfunction _toCamelCase(value) {\r\n    return value.replace(/-([a-z])/g, (_, group1) => group1.toUpperCase());\r\n}\r\n\r\n\r\nexport default {\r\n\tgc,\r\n\tsetRefesrherTime,\r\n\tgetRefesrherFormatTimeByKey,\r\n\tgetTouch,\r\n\tgetTouchFromZPaging,\r\n\tgetParent,\r\n\tconvertToPx,\r\n\tgetTime,\r\n\tgetInstanceId,\r\n\tconsoleErr,\r\n\tdelay,\r\n\twait,\r\n\tisPromise,\r\n\taddUnit,\r\n\tdeepCopy,\r\n\trpx2px\r\n};\r\n"], "names": ["c", "uni", "zLocalConfig"], "mappings": ";;;;AAKA,MAAM,aAAa;AACnB,IAAI,SAAS;AACb,IAAI,eAAe;AACnB,MAAM,aAAa,CAAA;AAOnB,SAAS,GAAG,KAAK,cAAc;AAE9B,SAAO,MAAM;AAEZ;AAEA,QAAI,CAAC;AAAQ,aAAO;AACpB,UAAM,QAAQ,OAAO,GAAG;AAExB,WAAO,UAAU,SAAY,eAAe;AAAA,EAC9C;AACA;AAGA,SAAS,SAAS,GAAG;AACpB,MAAI,QAAQ;AACZ,MAAI,EAAE,WAAW,EAAE,QAAQ,QAAQ;AAClC,YAAQ,EAAE,QAAQ,CAAC;AAAA,EACnB,WAAU,EAAE,kBAAkB,EAAE,eAAe,QAAQ;AACvD,YAAQ,EAAE,eAAe,CAAC;AAAA,EAC1B,WAAU,EAAE,UAAU,EAAE,UAAU,CAAA,GAAI;AACtC,YAAQ,EAAE;AAAA,EACZ,OAAQ;AACN,WAAO,EAAE,QAAQ,GAAG,QAAQ,EAAG;AAAA,EAC/B;AACD,SAAO;AAAA,IACN,QAAQ,MAAM;AAAA,IACd,QAAQ,MAAM;AAAA,EAChB;AACA;AAGA,SAAS,oBAAoB,QAAQ;AACpC,MAAI,UAAU,OAAO,WAAW,OAAO,YAAY,UAAU,OAAO,YAAY,iBAAiB;AAChG,UAAM,YAAY,OAAO;AACzB,QAAI,aAAa,UAAU,SAAS,kBAAkB,GAAG;AAExD,aAAO;AAAA,QACN,UAAU;AAAA,QACV,cAAc,UAAU,SAAS,uBAAuB;AAAA,QACxD,cAAc,UAAU,SAAS,sBAAsB;AAAA,QACvD,qBAAqB,UAAU,SAAS,+BAA+B;AAAA,MAC3E;AAAA,IACA,OAAS;AACN,aAAO,oBAAoB,OAAO,UAAU;AAAA,IAC5C;AAAA,EACH,OAAQ;AACN,WAAO,EAAE,UAAU;EACnB;AACF;AAGA,SAAS,UAAU,QAAQ;AAC1B,MAAI,CAAC;AAAQ,WAAO;AACpB,MAAI,OAAO,MAAM;AAAQ,WAAO;AAChC,SAAO,UAAU,OAAO,OAAO;AAChC;AAGA,SAAS,WAAW,KAAK;AACxB,UAAQ,MAAM,aAAa,GAAG,EAAE;AACjC;AAGA,SAAS,MAAM,UAAU,KAAKA,0DAAAA,EAAE,WAAW,KAAK;AAC/C,QAAM,UAAU,WAAW,UAAU,EAAE;AACvC,MAAI,CAAC,CAAC,KAAK;AACV,eAAW,GAAG,KAAK,aAAa,WAAW,GAAG,CAAC;AAC/C,eAAW,GAAG,IAAI;AAAA,EAClB;AACD,SAAO;AACR;AAGA,SAAS,iBAAiB,MAAM,KAAK;AACpC,QAAM,QAAQ,iBAAkB,KAAI;AACpC,QAAM,GAAG,IAAI;AACbC,gBAAAA,MAAI,eAAe,YAAY,KAAK;AACrC;AAGA,SAAS,mBAAmB;AAC3B,SAAOA,cAAG,MAAC,eAAe,UAAU;AACrC;AAGA,SAAS,sBAAsB,KAAK;AACnC,QAAM,QAAQ;AACd,SAAO,SAAS,MAAM,GAAG,IAAI,MAAM,GAAG,IAAI;AAC3C;AAGA,SAAS,4BAA4B,KAAK,SAAS;AAClD,QAAM,OAAO,sBAAsB,GAAG;AACtC,QAAM,WAAW,OAAO,YAAY,MAAM,OAAO,IAAI,QAAQ;AAC7D,SAAO,GAAG,QAAQ,KAAK,GAAG,QAAQ;AACnC;AAGA,SAAS,YAAY,MAAM;AAC1B,QAAM,WAAW,OAAO,UAAU,SAAS,KAAK,IAAI;AACpD,MAAI,aAAa;AAAmB,WAAO;AAC3C,MAAI,QAAQ;AACZ,MAAI,KAAK,QAAQ,KAAK,MAAM,MAAM,KAAK,QAAQ,KAAK,MAAM,IAAI;AAC7D,WAAO,KAAK,QAAQ,OAAO,EAAE,EAAE,QAAQ,OAAO,EAAE;AAChD,YAAQ;AAAA,EACR,WAAU,KAAK,QAAQ,IAAI,MAAM,IAAI;AACrC,WAAO,KAAK,QAAQ,MAAM,EAAE;AAAA,EAC5B;AACD,MAAI,CAAC,MAAM,IAAI,GAAG;AACjB,QAAI;AAAO,aAAO,OAAO,OAAO,IAAI,CAAC;AACrC,WAAO,OAAO,IAAI;AAAA,EAClB;AACD,SAAO;AACR;AAGA,SAAS,OAAO,KAAK;AAQpB,SAAOA,cAAG,MAAC,OAAO,GAAG;AAEtB;AAGA,SAAS,UAAU;AAClB,UAAQ,oBAAI,QAAQ;AACrB;AAGA,SAAS,gBAAgB;AACrB,QAAM,IAAI,CAAA;AACV,QAAM,YAAY;AAClB,WAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACzB,MAAE,CAAC,IAAI,UAAU,OAAO,KAAK,MAAM,KAAK,OAAQ,IAAG,EAAI,GAAG,CAAC;AAAA,EAC9D;AACD,SAAO,EAAE,KAAK,EAAE,IAAI,QAAO;AAC/B;AAGA,SAAS,KAAK,IAAI;AACjB,SAAO,IAAI,QAAQ,aAAW;AAC7B,eAAW,SAAS,EAAE;AAAA,EACxB,CAAE;AACF;AAGA,SAAS,UAAU,MAAM;AACxB,SAAO,OAAO,UAAU,SAAS,KAAK,IAAI,MAAM;AACjD;AAGA,SAAS,QAAQ,OAAO,MAAM;AAC7B,MAAI,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM,mBAAmB;AAChE,QAAI,YAAY;AAChB,gBAAY,UAAU,QAAQ,OAAO,EAAE,EAAE,QAAQ,OAAO,EAAE,EAAE,QAAQ,MAAM,EAAE;AAC5E,QAAI,MAAM,QAAQ,KAAK,MAAM,MAAM,MAAM,QAAQ,KAAK,MAAM,MAAM,MAAM,QAAQ,IAAI,MAAM,IAAI;AAC7F,kBAAY,WAAW,SAAS,IAAI;AAAA,IACpC;AACD,YAAQ;AAAA,EACR;AACD,SAAO,SAAS,QAAQ,QAAQ,QAAS,QAAQ,IAAK;AACvD;AAGA,SAAS,SAAS,KAAK;AACtB,MAAI,OAAO,QAAQ,YAAY,QAAQ;AAAM,WAAO;AACpD,MAAI,SAAS,MAAM,QAAQ,GAAG,IAAI,CAAE,IAAG;AACvC,WAAS,OAAO,KAAK;AACpB,QAAI,IAAI,eAAe,GAAG,GAAG;AAC5B,aAAO,GAAG,IAAI,SAAS,IAAI,GAAG,CAAC;AAAA,IAC/B;AAAA,EACD;AACD,SAAO;AACR;AAIA,SAAS,uBAAuB;AAE/B,MAAI;AAAc;AAElB,MAAIC,oDAAY,gBAAI,OAAO,KAAKA,oDAAY,YAAA,EAAE,QAAQ;AACrD,aAASA,oDAAAA;AAAAA,EACT;AAED,MAAI,CAAC,UAAUD,cAAG,MAAC,KAAK;AACvB,aAASA,cAAAA,MAAI,IAAI;AAAA,EACjB;AAED,WAAS,SAAS,OAAO,KAAK,MAAM,EAAE,OAAO,CAAC,QAAQ,QAAQ;AAC1D,WAAO,aAAa,GAAG,CAAC,IAAI,OAAO,GAAG;AACtC,WAAO;AAAA,EACZ,GAAI,CAAA,CAAE,IAAI;AACT,iBAAe;AAChB;AAGA,SAAS,YAAY,MAAM,SAAS;AACnC,QAAM,OAAO,IAAI,KAAK,IAAI;AAC1B,QAAM,cAAc,oBAAI;AAExB,QAAM,UAAU,IAAI,KAAK,IAAI,EAAE,SAAS,GAAG,GAAG,GAAG,CAAC;AAElD,QAAM,kBAAiB,oBAAI,QAAO,SAAS,GAAG,GAAG,GAAG,CAAC;AACrD,QAAM,UAAU,UAAU;AAC1B,MAAI,SAAS;AACb,QAAM,UAAU,gBAAgB,IAAI;AACpC,MAAI,YAAY,GAAG;AAClB,aAAS,QAAQ;AAAA,EACnB,WAAY,YAAY,QAAW;AACjC,aAAS,QAAQ;AAAA,EACnB,OAAQ;AACN,aAAS,eAAe,MAAM,KAAK,YAAW,MAAO,YAAY,YAAW,CAAE;AAAA,EAC9E;AACD,SAAO,GAAG,MAAM,IAAI,OAAO;AAC5B;AAGA,SAAS,eAAe,MAAM,WAAW,MAAM;AAC9C,QAAM,OAAO,KAAK;AAClB,QAAM,QAAQ,KAAK,SAAQ,IAAK;AAChC,QAAM,MAAM,KAAK;AACjB,SAAO,WAAW,GAAG,IAAI,IAAI,eAAe,KAAK,CAAC,IAAI,eAAe,GAAG,CAAC,KAAK,GAAG,eAAe,KAAK,CAAC,IAAI,eAAe,GAAG,CAAC;AAC9H;AAGA,SAAS,gBAAgB,MAAM;AAC9B,QAAM,OAAO,KAAK;AAClB,QAAM,SAAS,KAAK;AACpB,SAAO,GAAG,eAAe,IAAI,CAAC,IAAI,eAAe,MAAM,CAAC;AACzD;AAGA,SAAS,eAAe,KAAK;AAC5B,QAAM,IAAI;AACV,SAAO,IAAI,WAAW,IAAI,MAAM,MAAM;AACvC;AAQA,SAAS,aAAa,OAAO;AACzB,SAAO,MAAM,QAAQ,aAAa,CAAC,GAAG,WAAW,OAAO,YAAW,CAAE;AACzE;AAGA,MAAe,IAAA;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;;"}