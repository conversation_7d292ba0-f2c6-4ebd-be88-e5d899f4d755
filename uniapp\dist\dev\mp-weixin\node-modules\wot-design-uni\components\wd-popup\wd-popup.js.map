{"version": 3, "file": "wd-popup.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-popup/wd-popup.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1wb3B1cC93ZC1wb3B1cC52dWU"], "sourcesContent": ["<template>\n  <view class=\"wd-popup-wrapper\">\n    <wd-overlay\n      v-if=\"modal\"\n      :show=\"modelValue\"\n      :z-index=\"zIndex\"\n      :lock-scroll=\"lockScroll\"\n      :duration=\"duration\"\n      :custom-style=\"modalStyle\"\n      @click=\"handleClickModal\"\n      @touchmove=\"noop\"\n    />\n    <wd-transition\n      :lazy-render=\"lazyRender\"\n      :custom-class=\"rootClass\"\n      :custom-style=\"style\"\n      :duration=\"duration\"\n      :show=\"modelValue\"\n      :name=\"transitionName\"\n      :destroy=\"hideWhenClose\"\n      @before-enter=\"emit('before-enter')\"\n      @enter=\"emit('enter')\"\n      @after-enter=\"emit('after-enter')\"\n      @before-leave=\"emit('before-leave')\"\n      @leave=\"emit('leave')\"\n      @after-leave=\"emit('after-leave')\"\n    >\n      <slot />\n      <wd-icon v-if=\"closable\" custom-class=\"wd-popup__close\" name=\"add\" @click=\"close\" />\n    </wd-transition>\n  </view>\n</template>\n\n<script lang=\"ts\">\nexport default {\n  name: 'wd-popup',\n  options: {\n    virtualHost: true,\n    addGlobalClass: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport wdIcon from '../wd-icon/wd-icon.vue'\nimport wdOverlay from '../wd-overlay/wd-overlay.vue'\nimport wdTransition from '../wd-transition/wd-transition.vue'\nimport { computed, onBeforeMount, ref } from 'vue'\nimport { popupProps } from './types'\nimport type { TransitionName } from '../wd-transition/types'\n\nconst props = defineProps(popupProps)\nconst emit = defineEmits([\n  'update:modelValue',\n  'before-enter',\n  'enter',\n  'before-leave',\n  'leave',\n  'after-leave',\n  'after-enter',\n  'click-modal',\n  'close'\n])\n\n/**\n * 弹出位置\n */\nconst transitionName = computed<TransitionName | TransitionName[]>(() => {\n  if (props.transition) {\n    return props.transition\n  }\n  if (props.position === 'center') {\n    return ['zoom-in', 'fade']\n  }\n  if (props.position === 'left') {\n    return 'slide-left'\n  }\n  if (props.position === 'right') {\n    return 'slide-right'\n  }\n  if (props.position === 'bottom') {\n    return 'slide-up'\n  }\n  if (props.position === 'top') {\n    return 'slide-down'\n  }\n  return 'slide-up'\n})\n\nconst safeBottom = ref<number>(0)\n\nconst style = computed(() => {\n  return `z-index:${props.zIndex}; padding-bottom: ${safeBottom.value}px;${props.customStyle}`\n})\n\nconst rootClass = computed(() => {\n  return `wd-popup wd-popup--${props.position} ${!props.transition && props.position === 'center' ? 'is-deep' : ''} ${props.customClass || ''}`\n})\n\nonBeforeMount(() => {\n  if (props.safeAreaInsetBottom) {\n    const { safeArea, screenHeight, safeAreaInsets } = uni.getSystemInfoSync()\n\n    if (safeArea) {\n\n      safeBottom.value = screenHeight - (safeArea!.bottom || 0)\n\n\n\n\n    } else {\n      safeBottom.value = 0\n    }\n  }\n})\n\nfunction handleClickModal() {\n  emit('click-modal')\n  if (props.closeOnClickModal) {\n    close()\n  }\n}\n\nfunction close() {\n  emit('close')\n  emit('update:modelValue', false)\n}\nfunction noop() {}\n</script>\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-popup/wd-popup.vue'\nwx.createComponent(Component)"], "names": ["computed", "ref", "onBeforeMount", "uni"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AA6CA,MAAA,SAAmB,MAAA;AACnB,MAAA,YAAsB,MAAA;AACtB,MAAA,eAAyB,MAAA;AAbzB,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,EAAA;AAEpB;;;;;;;;;;;;;;;AAWA,UAAM,QAAQ;AACd,UAAM,OAAO;AAeP,UAAA,iBAAiBA,cAAAA,SAA4C,MAAM;AACvE,UAAI,MAAM,YAAY;AACpB,eAAO,MAAM;AAAA,MAAA;AAEX,UAAA,MAAM,aAAa,UAAU;AACxB,eAAA,CAAC,WAAW,MAAM;AAAA,MAAA;AAEvB,UAAA,MAAM,aAAa,QAAQ;AACtB,eAAA;AAAA,MAAA;AAEL,UAAA,MAAM,aAAa,SAAS;AACvB,eAAA;AAAA,MAAA;AAEL,UAAA,MAAM,aAAa,UAAU;AACxB,eAAA;AAAA,MAAA;AAEL,UAAA,MAAM,aAAa,OAAO;AACrB,eAAA;AAAA,MAAA;AAEF,aAAA;AAAA,IAAA,CACR;AAEK,UAAA,aAAaC,kBAAY,CAAC;AAE1B,UAAA,QAAQD,cAAAA,SAAS,MAAM;AACpB,aAAA,WAAW,MAAM,MAAM,qBAAqB,WAAW,KAAK,MAAM,MAAM,WAAW;AAAA,IAAA,CAC3F;AAEK,UAAA,YAAYA,cAAAA,SAAS,MAAM;AAC/B,aAAO,sBAAsB,MAAM,QAAQ,IAAI,CAAC,MAAM,cAAc,MAAM,aAAa,WAAW,YAAY,EAAE,IAAI,MAAM,eAAe,EAAE;AAAA,IAAA,CAC5I;AAEDE,kBAAAA,cAAc,MAAM;AAClB,UAAI,MAAM,qBAAqB;AAC7B,cAAM,EAAE,UAAU,cAAc,eAAe,IAAIC,cAAAA,MAAI,kBAAkB;AAEzE,YAAI,UAAU;AAED,qBAAA,QAAQ,gBAAgB,SAAU,UAAU;AAAA,QAAA,OAKlD;AACL,qBAAW,QAAQ;AAAA,QAAA;AAAA,MACrB;AAAA,IACF,CACD;AAED,aAAS,mBAAmB;AAC1B,WAAK,aAAa;AAClB,UAAI,MAAM,mBAAmB;AACrB,cAAA;AAAA,MAAA;AAAA,IACR;AAGF,aAAS,QAAQ;AACf,WAAK,OAAO;AACZ,WAAK,qBAAqB,KAAK;AAAA,IAAA;AAEjC,aAAS,OAAO;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/HhB,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}