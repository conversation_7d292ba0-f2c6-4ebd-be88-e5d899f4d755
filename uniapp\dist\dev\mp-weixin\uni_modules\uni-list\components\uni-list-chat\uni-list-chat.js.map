{"version": 3, "file": "uni-list-chat.js", "sources": ["../../../../../../../src/uni_modules/uni-list/components/uni-list-chat/uni-list-chat.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvdW5pX21vZHVsZXMvdW5pLWxpc3QvY29tcG9uZW50cy91bmktbGlzdC1jaGF0L3VuaS1saXN0LWNoYXQudnVl"], "sourcesContent": ["<template>\r\n\t<!-- #ifdef APP-NVUE -->\r\n\t<cell>\r\n\t\t<!-- #endif -->\r\n\t\t<view :hover-class=\"!clickable && !link ? '' : 'uni-list-chat--hover'\" class=\"uni-list-chat\" @click.stop=\"onClick\">\r\n\t\t\t<view :class=\"{ 'uni-list--border': border, 'uni-list-chat--first': isFirstChild }\"></view>\r\n\t\t\t<view class=\"uni-list-chat__container\">\r\n\t\t\t\t<view class=\"uni-list-chat__header-warp\">\r\n\t\t\t\t\t<view v-if=\"avatarCircle || avatarList.length === 0\" class=\"uni-list-chat__header\" :class=\"{ 'header--circle': avatarCircle }\">\r\n\t\t\t\t\t\t<image class=\"uni-list-chat__header-image\" :class=\"{ 'header--circle': avatarCircle }\" :src=\"avatarUrl\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- 头像组 -->\r\n\t\t\t\t\t<view v-else class=\"uni-list-chat__header\">\r\n\t\t\t\t\t\t<view v-for=\"(item, index) in avatarList\" :key=\"index\" class=\"uni-list-chat__header-box\" :class=\"computedAvatar\"\r\n\t\t\t\t\t\t :style=\"{ width: imageWidth + 'px', height: imageWidth + 'px' }\">\r\n\t\t\t\t\t\t\t<image class=\"uni-list-chat__header-image\" :style=\"{ width: imageWidth + 'px', height: imageWidth + 'px' }\" :src=\"item.url\"\r\n\t\t\t\t\t\t\t mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- #ifndef APP -->\r\n\t\t\t\t<view class=\"slot-header\">\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<slot name=\"header\"></slot>\r\n\t\t\t\t<!-- #ifndef APP -->\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<view v-if=\"badgeText && badgePositon === 'left'\" class=\"uni-list-chat__badge uni-list-chat__badge-pos\" :class=\"[isSingle]\">\r\n\t\t\t\t\t<text class=\"uni-list-chat__badge-text\">{{ badgeText === 'dot' ? '' : badgeText }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uni-list-chat__content\">\r\n\t\t\t\t\t<view class=\"uni-list-chat__content-main\">\r\n\t\t\t\t\t\t<text class=\"uni-list-chat__content-title uni-ellipsis\">{{ title }}</text>\r\n\t\t\t\t\t\t<view style=\"flex-direction: row;\">\r\n\t\t\t\t\t\t\t<text class=\"draft\" v-if=\"isDraft\">[草稿]</text>\r\n\t\t\t\t\t\t\t<text class=\"uni-list-chat__content-note uni-ellipsis\">{{isDraft?note.slice(14):note}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"uni-list-chat__content-extra\">\r\n\t\t\t\t\t\t<slot>\r\n\t\t\t\t\t\t\t<text class=\"uni-list-chat__content-extra-text\">{{ time }}</text>\r\n\t\t\t\t\t\t\t<view v-if=\"badgeText && badgePositon === 'right'\" class=\"uni-list-chat__badge\" :class=\"[isSingle, badgePositon === 'right' ? 'uni-list-chat--right' : '']\">\r\n\t\t\t\t\t\t\t\t<text class=\"uni-list-chat__badge-text\">{{ badgeText === 'dot' ? '' : badgeText }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</slot>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- #ifdef APP-NVUE -->\r\n\t</cell>\r\n\t<!-- #endif -->\r\n</template>\r\n\r\n<script>\r\n\t// 头像大小\r\n\tconst avatarWidth = 45;\r\n\r\n\t/**\r\n\t * ListChat 聊天列表\r\n\t * @description 聊天列表,用于创建聊天类列表\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=24\r\n\t * @property {String} \ttitle \t\t\t\t\t\t\t标题\r\n\t * @property {String} \tnote \t\t\t\t\t\t\t描述\r\n\t * @property {Boolean} \tclickable = [true|false] \t\t是否开启点击反馈，默认为false\r\n\t * @property {String} \tbadgeText\t\t\t\t\t\t数字角标内容\r\n\t * @property {String}  \tbadgePositon = [left|right]\t\t角标位置，默认为 right\r\n\t * @property {String} \tlink = [false｜navigateTo|redirectTo|reLaunch|switchTab] 是否展示右侧箭头并开启点击反馈，默认为false\r\n\t *  @value false\t \t不开启\r\n\t *  @value navigateTo \t同 uni.navigateTo()\r\n\t * \t@value redirectTo \t同 uni.redirectTo()\r\n\t * \t@value reLaunch   \t同 uni.reLaunch()\r\n\t * \t@value switchTab  \t同 uni.switchTab()\r\n\t * @property {String | PageURIString} \tto  \t\t\t跳转目标页面\r\n\t * @property {String} \ttime\t\t\t\t\t\t\t右侧时间显示\r\n\t * @property {Boolean} \tavatarCircle = [true|false]\t\t是否显示圆形头像，默认为false\r\n\t * @property {String} \tavatar\t\t\t\t\t\t\t头像地址，avatarCircle 不填时生效\r\n\t * @property {Array} \tavatarList \t\t\t\t\t\t头像组，格式为 [{url:''}]\r\n\t * @event {Function} \tclick \t\t\t\t\t\t\t点击 uniListChat 触发事件\r\n\t */\r\n\texport default {\r\n\t\tname: 'UniListChat',\r\n\t\temits:['click'],\r\n\t\tprops: {\r\n\t\t\ttitle: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tnote: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tclickable: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tlink: {\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tto: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tbadgeText: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tbadgePositon: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'right'\r\n\t\t\t},\r\n\t\t\ttime: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tavatarCircle: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tavatar: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tavatarList: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn [];\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t// inject: ['list'],\r\n\t\tcomputed: {\r\n\t\t\tisDraft(){\r\n\t\t\t\treturn this.note.slice(0,14) == '[uni-im-draft]'\r\n\t\t\t},\r\n\t\t\tisSingle() {\r\n\t\t\t\tif (this.badgeText === 'dot') {\r\n\t\t\t\t\treturn 'uni-badge--dot';\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconst badgeText = this.badgeText.toString();\r\n\t\t\t\t\tif (badgeText.length > 1) {\r\n\t\t\t\t\t\treturn 'uni-badge--complex';\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn 'uni-badge--single';\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tcomputedAvatar() {\r\n\t\t\t\tif (this.avatarList.length > 4) {\r\n\t\t\t\t\tthis.imageWidth = avatarWidth * 0.31;\r\n\t\t\t\t\treturn 'avatarItem--3';\r\n\t\t\t\t} else if (this.avatarList.length > 1) {\r\n\t\t\t\t\tthis.imageWidth = avatarWidth * 0.47;\r\n\t\t\t\t\treturn 'avatarItem--2';\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.imageWidth = avatarWidth;\r\n\t\t\t\t\treturn 'avatarItem--1';\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tavatar:{\r\n\t\t\t\thandler(avatar) {\r\n\t\t\t\t\tif(avatar.substr(0,8) == 'cloud://'){\r\n\t\t\t\t\t\tuniCloud.getTempFileURL({\r\n\t\t\t\t\t\t\tfileList: [avatar]\r\n\t\t\t\t\t\t}).then(res=>{\r\n\t\t\t\t\t\t\t// console.log(res);\r\n\t\t\t\t\t\t\t// 兼容uniCloud私有化部署\r\n\t\t\t\t\t\t\tlet fileList = res.fileList || res.result.fileList\r\n\t\t\t\t\t\t\tthis.avatarUrl = fileList[0].tempFileURL\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthis.avatarUrl = avatar\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\timmediate: true\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tisFirstChild: false,\r\n\t\t\t\tborder: true,\r\n\t\t\t\t// avatarList: 3,\r\n\t\t\t\timageWidth: 50,\r\n\t\t\t\tavatarUrl:''\r\n\t\t\t};\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.list = this.getForm()\r\n\t\t\tif (this.list) {\r\n\t\t\t\tif (!this.list.firstChildAppend) {\r\n\t\t\t\t\tthis.list.firstChildAppend = true;\r\n\t\t\t\t\tthis.isFirstChild = true;\r\n\t\t\t\t}\r\n\t\t\t\tthis.border = this.list.border;\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t/**\r\n\t\t\t * 获取父元素实例\r\n\t\t\t */\r\n\t\t\tgetForm(name = 'uniList') {\r\n\t\t\t\tlet parent = this.$parent;\r\n\t\t\t\tlet parentName = parent.$options.name;\r\n\t\t\t\twhile (parentName !== name) {\r\n\t\t\t\t\tparent = parent.$parent;\r\n\t\t\t\t\tif (!parent) return false\r\n\t\t\t\t\tparentName = parent.$options.name;\r\n\t\t\t\t}\r\n\t\t\t\treturn parent;\r\n\t\t\t},\r\n\t\t\tonClick() {\r\n\t\t\t\tif (this.to !== '') {\r\n\t\t\t\t\tthis.openPage();\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (this.clickable || this.link) {\r\n\t\t\t\t\tthis.$emit('click', {\r\n\t\t\t\t\t\tdata: {}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\topenPage() {\r\n\t\t\t\tif (['navigateTo', 'redirectTo', 'reLaunch', 'switchTab'].indexOf(this.link) !== -1) {\r\n\t\t\t\t\tthis.pageApi(this.link);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.pageApi('navigateTo');\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tpageApi(api) {\r\n\t\t\t\tlet callback = {\r\n\t\t\t\t\turl: this.to,\r\n\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\tthis.$emit('click', {\r\n\t\t\t\t\t\t\tdata: res\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: err => {\r\n\t\t\t\t\t\tthis.$emit('click', {\r\n\t\t\t\t\t\t\tdata: err\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tswitch (api) {\r\n\t\t\t\t\tcase 'navigateTo':\r\n\t\t\t\t\t\tuni.navigateTo(callback)\r\n\t\t\t\t\t\tbreak\r\n\t\t\t\t\tcase 'redirectTo':\r\n\t\t\t\t\t\tuni.redirectTo(callback)\r\n\t\t\t\t\t\tbreak\r\n\t\t\t\t\tcase 'reLaunch':\r\n\t\t\t\t\t\tuni.reLaunch(callback)\r\n\t\t\t\t\t\tbreak\r\n\t\t\t\t\tcase 'switchTab':\r\n\t\t\t\t\t\tuni.switchTab(callback)\r\n\t\t\t\t\t\tbreak\r\n\t\t\t\t\tdefault:\r\n\t\t\t\t\tuni.navigateTo(callback)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\" >\r\n\t$uni-font-size-lg:16px;\r\n\t$uni-spacing-row-sm: 5px;\r\n\t$uni-spacing-row-base: 10px;\r\n\t$uni-spacing-row-lg: 15px;\r\n\t$background-color: #fff;\r\n\t$divide-line-color: #e5e5e5;\r\n\t$avatar-width: 45px;\r\n\t$avatar-border-radius: 5px;\r\n\t$avatar-border-color: #eee;\r\n\t$avatar-border-width: 1px;\r\n\t$title-size: 16px;\r\n\t$title-color: #3b4144;\r\n\t$title-weight: normal;\r\n\t$note-size: 12px;\r\n\t$note-color: #999;\r\n\t$note-weight: normal;\r\n\t$right-text-size: 12px;\r\n\t$right-text-color: #999;\r\n\t$right-text-weight: normal;\r\n\t$badge-left: 0px;\r\n\t$badge-top: 0px;\r\n\t$dot-width: 10px;\r\n\t$dot-height: 10px;\r\n\t$badge-size: 18px;\r\n\t$badge-font: 12px;\r\n\t$badge-color: #fff;\r\n\t$badge-background-color: #ff5a5f;\r\n\t$badge-space: 6px;\r\n\t$hover: #f5f5f5;\r\n\r\n\t.uni-list-chat {\r\n\t\tfont-size: $uni-font-size-lg;\r\n\t\tposition: relative;\r\n\t\tflex-direction: column;\r\n\t\tjustify-content: space-between;\r\n\t\tbackground-color: $background-color;\r\n\t}\r\n\r\n\t// .uni-list-chat--disabled {\r\n\t// \topacity: 0.3;\r\n\t// }\r\n\r\n\t.uni-list-chat--hover {\r\n\t\tbackground-color: $hover;\r\n\t}\r\n\r\n\t.uni-list--border {\r\n\t\tposition: relative;\r\n\t\tmargin-left: $uni-spacing-row-lg;\r\n\t\t/* #ifdef APP-PLUS */\r\n\t\tborder-top-color: $divide-line-color;\r\n\t\tborder-top-style: solid;\r\n\t\tborder-top-width: 0.5px;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t/* #ifndef APP-NVUE */\r\n\t.uni-list--border:after {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tright: 0;\r\n\t\tleft: 0;\r\n\t\theight: 1px;\r\n\t\tcontent: '';\r\n\t\t-webkit-transform: scaleY(0.5);\r\n\t\ttransform: scaleY(0.5);\r\n\t\tbackground-color: $divide-line-color;\r\n\t}\r\n\r\n\t.uni-list-item--first:after {\r\n\t\theight: 0px;\r\n\t}\r\n\r\n\t/* #endif */\r\n\r\n\t.uni-list-chat--first {\r\n\t\tborder-top-width: 0px;\r\n\t}\r\n\r\n\t.uni-ellipsis {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\toverflow: hidden;\r\n\t\twhite-space: nowrap;\r\n\t\ttext-overflow: ellipsis;\r\n\t\t/* #endif */\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\tlines: 1;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-ellipsis-2 {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-line-clamp: 2;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\t/* #endif */\r\n\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\tlines: 2;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-list-chat__container {\r\n\t\tposition: relative;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t\tflex: 1;\r\n\t\tpadding: $uni-spacing-row-base $uni-spacing-row-lg;\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.uni-list-chat__header-warp {\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.uni-list-chat__header {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\talign-content: center;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tflex-wrap: wrap-reverse;\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\twidth: 50px;\r\n\t\theight: 50px;\r\n\t\t/* #endif */\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\twidth: $avatar-width;\r\n\t\theight: $avatar-width;\r\n\t\t/* #endif */\r\n\r\n\t\tborder-radius: $avatar-border-radius;\r\n\t\tborder-color: $avatar-border-color;\r\n\t\tborder-width: $avatar-border-width;\r\n\t\tborder-style: solid;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.uni-list-chat__header-box {\r\n\t\t/* #ifndef APP-PLUS */\r\n\t\tbox-sizing: border-box;\r\n\t\tdisplay: flex;\r\n\t\twidth: $avatar-width;\r\n\t\theight: $avatar-width;\r\n\t\t/* #endif */\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\twidth: 50px;\r\n\t\theight: 50px;\r\n\t\t/* #endif */\r\n\t\toverflow: hidden;\r\n\t\tborder-radius: 2px;\r\n\t}\r\n\r\n\t.uni-list-chat__header-image {\r\n\t\tmargin: 1px;\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\twidth: 50px;\r\n\t\theight: 50px;\r\n\t\t/* #endif */\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\twidth: $avatar-width;\r\n\t\theight: $avatar-width;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t/* #ifndef APP-NVUE */\r\n\t.uni-list-chat__header-image {\r\n\t\tdisplay: block;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.avatarItem--1 {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.avatarItem--2 {\r\n\t\twidth: 47%;\r\n\t\theight: 47%;\r\n\t}\r\n\r\n\t.avatarItem--3 {\r\n\t\twidth: 32%;\r\n\t\theight: 32%;\r\n\t}\r\n\r\n\t/* #endif */\r\n\t.header--circle {\r\n\t\tborder-radius: 50%;\r\n\t}\r\n\r\n\t.uni-list-chat__content {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t\tflex: 1;\r\n\t\toverflow: hidden;\r\n\t\tpadding: 2px 0;\r\n\t}\r\n\r\n\t.uni-list-chat__content-main {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: column;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding-left: $uni-spacing-row-base;\r\n\t\tflex: 1;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.uni-list-chat__content-title {\r\n\t\tfont-size: $title-size;\r\n\t\tcolor: $title-color;\r\n\t\tfont-weight: $title-weight;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.draft ,.uni-list-chat__content-note {\r\n\t\tmargin-top: 3px;\r\n\t\tcolor: $note-color;\r\n\t\tfont-size: $note-size;\r\n\t\tfont-weight: $title-weight;\r\n\t\toverflow: hidden;\r\n\t}\r\n\t.draft{\r\n\t\tcolor: #eb3a41;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tflex-shrink: 0;\r\n\t\t/* #endif */\r\n\t\tpadding-right: 3px;\r\n\t}\r\n\r\n\t.uni-list-chat__content-extra {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tflex-shrink: 0;\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: column;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: flex-end;\r\n\t\tmargin-left: 5px;\r\n\t}\r\n\r\n\t.uni-list-chat__content-extra-text {\r\n\t\tcolor: $right-text-color;\r\n\t\tfont-size: $right-text-size;\r\n\t\tfont-weight: $right-text-weight;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.uni-list-chat__badge-pos {\r\n\t\tposition: absolute;\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\tleft: 55px;\r\n\t\ttop: 3px;\r\n\t\t/* #endif */\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tleft: calc(#{$avatar-width} + 10px - #{$badge-space} + #{$badge-left});\r\n\t\ttop: calc(#{$uni-spacing-row-base}/ 2 + 1px + #{$badge-top});\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-list-chat__badge {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tborder-radius: 100px;\r\n\t\tbackground-color: $badge-background-color;\r\n\t}\r\n\r\n\t.uni-list-chat__badge-text {\r\n\t\tcolor: $badge-color;\r\n\t\tfont-size: $badge-font;\r\n\t}\r\n\r\n\t.uni-badge--single {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\t// left: calc(#{$avatar-width} + 7px + #{$badge-left});\r\n\t\t/* #endif */\r\n\t\twidth: $badge-size;\r\n\t\theight: $badge-size;\r\n\t}\r\n\r\n\t.uni-badge--complex {\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\tleft: 50px;\r\n\t\t/* #endif */\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\twidth: auto;\r\n\t\t/* #endif */\r\n\t\theight: $badge-size;\r\n\t\tpadding: 0 $badge-space;\r\n\t}\r\n\r\n\t.uni-badge--dot {\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\tleft: 60px;\r\n\t\ttop: 6px;\r\n\t\t/* #endif */\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tleft: calc(#{$avatar-width} + 15px - #{$dot-width}/ 2 + 1px + #{$badge-left});\r\n\t\t/* #endif */\r\n\t\twidth: $dot-width;\r\n\t\theight: $dot-height;\r\n\t\tpadding: 0;\r\n\t}\r\n\r\n\t.uni-list-chat--right {\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\tleft: 0;\r\n\t\t/* #endif */\r\n\t}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/uni_modules/uni-list/components/uni-list-chat/uni-list-chat.vue'\nwx.createComponent(Component)"], "names": ["uniCloud", "uni"], "mappings": ";;AAwDC,MAAM,cAAc;AAwBpB,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,OAAM,CAAC,OAAO;AAAA,EACd,OAAO;AAAA,IACN,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,MAAM;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,WAAW;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,MAAM;AAAA,MACL,MAAM,CAAC,SAAS,MAAM;AAAA,MACtB,SAAS;AAAA,IACT;AAAA,IACD,IAAI;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,WAAW;AAAA,MACV,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACT;AAAA,IACD,cAAc;AAAA,MACb,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,MAAM;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,cAAc;AAAA,MACb,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,QAAQ;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,YAAY;AAAA,MACX,MAAM;AAAA,MACN,UAAW;AACV,eAAO;MACR;AAAA,IACD;AAAA,EACA;AAAA;AAAA,EAED,UAAU;AAAA,IACT,UAAS;AACR,aAAO,KAAK,KAAK,MAAM,GAAE,EAAE,KAAK;AAAA,IAChC;AAAA,IACD,WAAW;AACV,UAAI,KAAK,cAAc,OAAO;AAC7B,eAAO;AAAA,aACD;AACN,cAAM,YAAY,KAAK,UAAU,SAAQ;AACzC,YAAI,UAAU,SAAS,GAAG;AACzB,iBAAO;AAAA,eACD;AACN,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACA;AAAA,IACD,iBAAiB;AAChB,UAAI,KAAK,WAAW,SAAS,GAAG;AAC/B,aAAK,aAAa,cAAc;AAChC,eAAO;AAAA,MACR,WAAW,KAAK,WAAW,SAAS,GAAG;AACtC,aAAK,aAAa,cAAc;AAChC,eAAO;AAAA,aACD;AACN,aAAK,aAAa;AAClB,eAAO;AAAA,MACR;AAAA,IACD;AAAA,EACA;AAAA,EACD,OAAO;AAAA,IACN,QAAO;AAAA,MACN,QAAQ,QAAQ;AACf,YAAG,OAAO,OAAO,GAAE,CAAC,KAAK,YAAW;AACnCA,wBAAAA,GAAS,eAAe;AAAA,YACvB,UAAU,CAAC,MAAM;AAAA,WACjB,EAAE,KAAK,SAAK;AAGZ,gBAAI,WAAW,IAAI,YAAY,IAAI,OAAO;AAC1C,iBAAK,YAAY,SAAS,CAAC,EAAE;AAAA,WAC7B;AAAA,eACG;AACJ,eAAK,YAAY;AAAA,QAClB;AAAA,MACA;AAAA,MACD,WAAW;AAAA,IACZ;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,cAAc;AAAA,MACd,QAAQ;AAAA;AAAA,MAER,YAAY;AAAA,MACZ,WAAU;AAAA;EAEX;AAAA,EACD,UAAU;AACT,SAAK,OAAO,KAAK,QAAQ;AACzB,QAAI,KAAK,MAAM;AACd,UAAI,CAAC,KAAK,KAAK,kBAAkB;AAChC,aAAK,KAAK,mBAAmB;AAC7B,aAAK,eAAe;AAAA,MACrB;AACA,WAAK,SAAS,KAAK,KAAK;AAAA,IACzB;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA;AAAA;AAAA,IAIR,QAAQ,OAAO,WAAW;AACzB,UAAI,SAAS,KAAK;AAClB,UAAI,aAAa,OAAO,SAAS;AACjC,aAAO,eAAe,MAAM;AAC3B,iBAAS,OAAO;AAChB,YAAI,CAAC;AAAQ,iBAAO;AACpB,qBAAa,OAAO,SAAS;AAAA,MAC9B;AACA,aAAO;AAAA,IACP;AAAA,IACD,UAAU;AACT,UAAI,KAAK,OAAO,IAAI;AACnB,aAAK,SAAQ;AACb;AAAA,MACD;AAEA,UAAI,KAAK,aAAa,KAAK,MAAM;AAChC,aAAK,MAAM,SAAS;AAAA,UACnB,MAAM,CAAC;AAAA,QACR,CAAC;AAAA,MACF;AAAA,IACA;AAAA,IACD,WAAW;AACV,UAAI,CAAC,cAAc,cAAc,YAAY,WAAW,EAAE,QAAQ,KAAK,IAAI,MAAM,IAAI;AACpF,aAAK,QAAQ,KAAK,IAAI;AAAA,aAChB;AACN,aAAK,QAAQ,YAAY;AAAA,MAC1B;AAAA,IACA;AAAA,IACD,QAAQ,KAAK;AACZ,UAAI,WAAW;AAAA,QACd,KAAK,KAAK;AAAA,QACV,SAAS,SAAO;AACf,eAAK,MAAM,SAAS;AAAA,YACnB,MAAM;AAAA,UACP,CAAC;AAAA,QACD;AAAA,QACD,MAAM,SAAO;AACZ,eAAK,MAAM,SAAS;AAAA,YACnB,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACD;AACA,cAAQ,KAAG;AAAA,QACV,KAAK;AACJC,wBAAG,MAAC,WAAW,QAAQ;AACvB;AAAA,QACD,KAAK;AACJA,wBAAG,MAAC,WAAW,QAAQ;AACvB;AAAA,QACD,KAAK;AACJA,wBAAG,MAAC,SAAS,QAAQ;AACrB;AAAA,QACD,KAAK;AACJA,wBAAG,MAAC,UAAU,QAAQ;AACtB;AAAA,QACD;AACAA,wBAAG,MAAC,WAAW,QAAQ;AAAA,MACxB;AAAA,IACD;AAAA,EACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtQF,GAAG,gBAAgB,SAAS;"}