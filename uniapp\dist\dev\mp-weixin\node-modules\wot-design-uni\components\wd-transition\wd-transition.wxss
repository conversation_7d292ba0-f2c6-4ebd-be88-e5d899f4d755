/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.wd-transition.data-v-2c25887e {
  transition-timing-function: ease;
}
.wd-fade-enter.data-v-2c25887e,
.wd-fade-leave-to.data-v-2c25887e {
  opacity: 0;
}
.wd-fade-enter-active.data-v-2c25887e,
.wd-fade-leave-active.data-v-2c25887e {
  transition-property: opacity;
}
.wd-fade-up-enter.data-v-2c25887e,
.wd-fade-up-leave-to.data-v-2c25887e {
  transform: translate3d(0, 100%, 0);
  opacity: 0;
}
.wd-fade-down-enter.data-v-2c25887e,
.wd-fade-down-leave-to.data-v-2c25887e {
  transform: translate3d(0, -100%, 0);
  opacity: 0;
}
.wd-fade-left-enter.data-v-2c25887e,
.wd-fade-left-leave-to.data-v-2c25887e {
  transform: translate3d(-100%, 0, 0);
  opacity: 0;
}
.wd-fade-right-enter.data-v-2c25887e,
.wd-fade-right-leave-to.data-v-2c25887e {
  transform: translate3d(100%, 0, 0);
  opacity: 0;
}
.wd-slide-up-enter.data-v-2c25887e,
.wd-slide-up-leave-to.data-v-2c25887e {
  transform: translate3d(0, 100%, 0);
}
.wd-slide-down-enter.data-v-2c25887e,
.wd-slide-down-leave-to.data-v-2c25887e {
  transform: translate3d(0, -100%, 0);
}
.wd-slide-left-enter.data-v-2c25887e,
.wd-slide-left-leave-to.data-v-2c25887e {
  transform: translate3d(-100%, 0, 0);
}
.wd-slide-right-enter.data-v-2c25887e,
.wd-slide-right-leave-to.data-v-2c25887e {
  transform: translate3d(100%, 0, 0);
}
.wd-zoom-in-enter.data-v-2c25887e,
.wd-zoom-in-leave-to.data-v-2c25887e {
  opacity: 0;
  transform: scale(0.8);
}
.wd-zoom-out-enter.data-v-2c25887e,
.wd-zoom-out-leave-to.data-v-2c25887e {
  transform: scale(1.2);
  opacity: 0;
}
.wd-zoom-in-enter-active.data-v-2c25887e,
.wd-zoom-in-leave-active.data-v-2c25887e,
.wd-zoom-out-enter-active.data-v-2c25887e,
.wd-zoom-out-leave-active.data-v-2c25887e,
.wd-fade-up-enter-active.data-v-2c25887e,
.wd-fade-up-leave-active.data-v-2c25887e,
.wd-fade-down-enter-active.data-v-2c25887e,
.wd-fade-down-leave-active.data-v-2c25887e,
.wd-fade-left-enter-active.data-v-2c25887e,
.wd-fade-left-leave-active.data-v-2c25887e,
.wd-fade-right-enter-active.data-v-2c25887e,
.wd-fade-right-leave-active.data-v-2c25887e {
  transition-property: opacity, transform;
}
.wd-slide-up-enter-active.data-v-2c25887e,
.wd-slide-up-leave-active.data-v-2c25887e,
.wd-slide-down-enter-active.data-v-2c25887e,
.wd-slide-down-leave-active.data-v-2c25887e,
.wd-slide-left-enter-active.data-v-2c25887e,
.wd-slide-left-leave-active.data-v-2c25887e,
.wd-slide-right-enter-active.data-v-2c25887e,
.wd-slide-right-leave-active.data-v-2c25887e {
  transition-property: transform;
}