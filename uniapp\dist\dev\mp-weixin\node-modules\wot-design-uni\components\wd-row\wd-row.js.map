{"version": 3, "file": "wd-row.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-row/wd-row.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1yb3cvd2Qtcm93LnZ1ZQ"], "sourcesContent": ["<template>\n  <view :class=\"`wd-row ${customClass}`\" :style=\"rowStyle\">\n    <!-- 每一行 -->\n    <slot />\n  </view>\n</template>\n<script lang=\"ts\">\nexport default {\n  name: 'wd-row',\n  options: {\n    virtualHost: true,\n    addGlobalClass: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n<script lang=\"ts\" setup>\nimport { computed, type CSSProperties } from 'vue'\nimport { useChildren } from '../composables/useChildren'\nimport { ROW_KEY, rowProps } from './types'\nimport { addUnit, objToStyle } from '../common/util'\n\nconst props = defineProps(rowProps)\nconst { linkChildren } = useChildren(ROW_KEY)\n\nlinkChildren({ props })\n\nconst rowStyle = computed(() => {\n  const style: CSSProperties = {}\n  const { gutter } = props\n  if (gutter < 0) {\n    console.error('[wot design] warning(wd-row): attribute gutter must be greater than or equal to 0')\n  } else if (gutter) {\n    style.marginLeft = addUnit(gutter / 2)\n    style.marginRight = addUnit(gutter / 2)\n  }\n  return `${objToStyle(style)}${props.customStyle}`\n})\n</script>\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-row/wd-row.vue'\nwx.createComponent(Component)"], "names": ["useChildren", "ROW_KEY", "computed", "addUnit", "objToStyle"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAOA,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,EAAA;AAEpB;;;;AAQA,UAAM,QAAQ;AACd,UAAM,EAAE,aAAA,IAAiBA,cAAA,YAAYC,qBAAO;AAE/B,iBAAA,EAAE,OAAO;AAEhB,UAAA,WAAWC,cAAAA,SAAS,MAAM;AAC9B,YAAM,QAAuB,CAAC;AACxB,YAAA,EAAE,WAAW;AACnB,UAAI,SAAS,GAAG;AACd,gBAAQ,MAAM,mFAAmF;AAAA,iBACxF,QAAQ;AACX,cAAA,aAAaC,sBAAQ,SAAS,CAAC;AAC/B,cAAA,cAAcA,sBAAQ,SAAS,CAAC;AAAA,MAAA;AAExC,aAAO,GAAGC,cAAAA,WAAW,KAAK,CAAC,GAAG,MAAM,WAAW;AAAA,IAAA,CAChD;;;;;;;;;;ACpCD,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}