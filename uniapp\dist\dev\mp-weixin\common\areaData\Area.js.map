{"version": 3, "file": "Area.js", "sources": ["../../../../../src/common/areaData/Area.ts"], "sourcesContent": ["import {pcaa as REGION_DATA} from \"./pcaUtils\";\r\n\r\n/**\r\n * Area 属性all的类型\r\n */\r\ninterface PlainPca {\r\n  id: string;\r\n  text: string;\r\n  pid: string;\r\n  index: Number;\r\n}\r\n\r\n/**\r\n * 省市区工具类 -解决列表省市区组件的翻译问题\r\n */\r\nclass Area {\r\n  all: PlainPca[];\r\n\r\n  /**\r\n   * 构造器\r\n   * @param pcaa\r\n   */\r\n  constructor(pcaa?) {\r\n    if (!pcaa) {\r\n      pcaa = REGION_DATA;\r\n    }\r\n    let arr: PlainPca[] = [];\r\n    const province = pcaa['86'];\r\n    Object.keys(province).map((key) => {\r\n      arr.push({ id: key, text: province[key], pid: '86', index: 1 });\r\n      const city = pcaa[key];\r\n      Object.keys(city).map((key2) => {\r\n        arr.push({ id: key2, text: city[key2], pid: key, index: 2 });\r\n        const qu = pcaa[key2];\r\n        if (qu) {\r\n          Object.keys(qu).map((key3) => {\r\n            arr.push({ id: key3, text: qu[key3], pid: key2, index: 3 });\r\n          });\r\n        }\r\n      });\r\n    });\r\n    this.all = arr;\r\n  }\r\n\r\n  get pca() {\r\n    return this.all;\r\n  }\r\n\r\n  getCode(text) {\r\n    if (!text || text.length == 0) {\r\n      return '';\r\n    }\r\n    for (let item of this.all) {\r\n      if (item.text === text) {\r\n        return item.id;\r\n      }\r\n    }\r\n  }\r\n\r\n//update-begin-author:liusq---date:20230404--for: [issue/382]省市区组件JAreaLinkage数据不回显---\r\n  getText(code,index=3) {\r\n    if (!code || code.length == 0) {\r\n      return '';\r\n    }\r\n    let arr = [];\r\n    this.getAreaBycode(code, arr, index);\r\n    return arr.join('/');\r\n  }\r\n//update-end-author:liusq---date:20230404--for: [issue/382]省市区组件JAreaLinkage数据不回显---\r\n\r\n  getRealCode(code) {\r\n    let arr = [];\r\n    this.getPcode(code, arr, 3);\r\n    return arr;\r\n  }\r\n\r\n  getPcode(id, arr, index) {\r\n    for (let item of this.all) {\r\n      if (item.id === id && item.index == index) {\r\n        arr.unshift(id);\r\n        if (item.pid != '86') {\r\n          this.getPcode(item.pid, arr, --index);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  getAreaBycode(code, arr, index) {\r\n    for (let item of this.all) {\r\n      if (item.id === code && item.index == index) {\r\n        arr.unshift(item.text);\r\n        if (item.pid != '86') {\r\n          this.getAreaBycode(item.pid, arr, --index);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\nconst jeecgAreaData = new Area();\r\n\r\n// 根据code找文本\r\nconst getAreaTextByCode = function (code) {\r\n  let index = 3;\r\n  //update-begin-author:liusq---date:20220531--for: 判断code是否是多code逗号分割的字符串，是的话，获取最后一位的code ---\r\n  if (code && code.includes(',')) {\r\n    index = code.split(\",\").length;\r\n    code = code.substr(code.lastIndexOf(',') + 1);\r\n  }\r\n  //update-end-author:liusq---date:20220531--for: 判断code是否是多code逗号分割的字符串，是的话，获取最后一位的code ---\r\n  return jeecgAreaData.getText(code,index);\r\n};\r\n// 根据code找文本\r\nconst getAreaArrByCode = function (code) {\r\n  return jeecgAreaData.getRealCode(code);\r\n};\r\n\r\nexport { getAreaTextByCode,getAreaArrByCode };\r\n"], "names": ["REGION_DATA"], "mappings": ";;AAeA,MAAM,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,EAOT,YAAY,MAAO;AACjB,QAAI,CAAC,MAAM;AACF,aAAAA,yBAAA;AAAA,IAAA;AAET,QAAI,MAAkB,CAAC;AACjB,UAAA,WAAW,KAAK,IAAI;AAC1B,WAAO,KAAK,QAAQ,EAAE,IAAI,CAAC,QAAQ;AACjC,UAAI,KAAK,EAAE,IAAI,KAAK,MAAM,SAAS,GAAG,GAAG,KAAK,MAAM,OAAO,EAAA,CAAG;AACxD,YAAA,OAAO,KAAK,GAAG;AACrB,aAAO,KAAK,IAAI,EAAE,IAAI,CAAC,SAAS;AAC9B,YAAI,KAAK,EAAE,IAAI,MAAM,MAAM,KAAK,IAAI,GAAG,KAAK,KAAK,OAAO,EAAA,CAAG;AACrD,cAAA,KAAK,KAAK,IAAI;AACpB,YAAI,IAAI;AACN,iBAAO,KAAK,EAAE,EAAE,IAAI,CAAC,SAAS;AAC5B,gBAAI,KAAK,EAAE,IAAI,MAAM,MAAM,GAAG,IAAI,GAAG,KAAK,MAAM,OAAO,EAAA,CAAG;AAAA,UAAA,CAC3D;AAAA,QAAA;AAAA,MACH,CACD;AAAA,IAAA,CACF;AACD,SAAK,MAAM;AAAA,EAAA;AAAA,EAGb,IAAI,MAAM;AACR,WAAO,KAAK;AAAA,EAAA;AAAA,EAGd,QAAQ,MAAM;AACZ,QAAI,CAAC,QAAQ,KAAK,UAAU,GAAG;AACtB,aAAA;AAAA,IAAA;AAEA,aAAA,QAAQ,KAAK,KAAK;AACrB,UAAA,KAAK,SAAS,MAAM;AACtB,eAAO,KAAK;AAAA,MAAA;AAAA,IACd;AAAA,EACF;AAAA;AAAA,EAIF,QAAQ,MAAK,QAAM,GAAG;AACpB,QAAI,CAAC,QAAQ,KAAK,UAAU,GAAG;AACtB,aAAA;AAAA,IAAA;AAET,QAAI,MAAM,CAAC;AACN,SAAA,cAAc,MAAM,KAAK,KAAK;AAC5B,WAAA,IAAI,KAAK,GAAG;AAAA,EAAA;AAAA;AAAA,EAIrB,YAAY,MAAM;AAChB,QAAI,MAAM,CAAC;AACN,SAAA,SAAS,MAAM,KAAK,CAAC;AACnB,WAAA;AAAA,EAAA;AAAA,EAGT,SAAS,IAAI,KAAK,OAAO;AACd,aAAA,QAAQ,KAAK,KAAK;AACzB,UAAI,KAAK,OAAO,MAAM,KAAK,SAAS,OAAO;AACzC,YAAI,QAAQ,EAAE;AACV,YAAA,KAAK,OAAO,MAAM;AACpB,eAAK,SAAS,KAAK,KAAK,KAAK,EAAE,KAAK;AAAA,QAAA;AAAA,MACtC;AAAA,IACF;AAAA,EACF;AAAA,EAGF,cAAc,MAAM,KAAK,OAAO;AACrB,aAAA,QAAQ,KAAK,KAAK;AACzB,UAAI,KAAK,OAAO,QAAQ,KAAK,SAAS,OAAO;AACvC,YAAA,QAAQ,KAAK,IAAI;AACjB,YAAA,KAAK,OAAO,MAAM;AACpB,eAAK,cAAc,KAAK,KAAK,KAAK,EAAE,KAAK;AAAA,QAAA;AAAA,MAC3C;AAAA,IACF;AAAA,EACF;AAEJ;AACA,MAAM,gBAAgB,IAAI,KAAK;AAGzB,MAAA,oBAAoB,SAAU,MAAM;AACxC,MAAI,QAAQ;AAEZ,MAAI,QAAQ,KAAK,SAAS,GAAG,GAAG;AACtB,YAAA,KAAK,MAAM,GAAG,EAAE;AACxB,WAAO,KAAK,OAAO,KAAK,YAAY,GAAG,IAAI,CAAC;AAAA,EAAA;AAGvC,SAAA,cAAc,QAAQ,MAAK,KAAK;AACzC;AAEM,MAAA,mBAAmB,SAAU,MAAM;AAChC,SAAA,cAAc,YAAY,IAAI;AACvC;;;"}