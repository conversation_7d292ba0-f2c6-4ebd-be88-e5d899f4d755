{"version": 3, "file": "calendar.js", "sources": ["../../../../../../../src/uni_modules/uni-calendar/components/uni-calendar/calendar.js"], "sourcesContent": ["/**\r\n* @1900-2100区间内的公历、农历互转\r\n* @charset UTF-8\r\n* @github  https://github.com/jjonline/calendar.js\r\n* <AUTHOR>\r\n* @Time    2014-7-21\r\n* @Time    2016-8-13 Fixed 2033hex、Attribution Annals\r\n* @Time    2016-9-25 Fixed lunar LeapMonth Param Bug\r\n* @Time    2017-7-24 Fixed use getTerm Func Param Error.use solar year,NOT lunar year\r\n* @Version 1.0.3\r\n* @公历转农历：calendar.solar2lunar(1987,11,01); //[you can ignore params of prefix 0]\r\n* @农历转公历：calendar.lunar2solar(1987,09,10); //[you can ignore params of prefix 0]\r\n*/\r\n/* eslint-disable */\r\nvar calendar = {\r\n\r\n  /**\r\n      * 农历1900-2100的润大小信息表\r\n      * @Array Of Property\r\n      * @return Hex\r\n      */\r\n  lunarInfo: [0x04bd8, 0x04ae0, 0x0a570, 0x054d5, 0x0d260, 0x0d950, 0x16554, 0x056a0, 0x09ad0, 0x055d2, // 1900-1909\r\n    0x04ae0, 0x0a5b6, 0x0a4d0, 0x0d250, 0x1d255, 0x0b540, 0x0d6a0, 0x0ada2, 0x095b0, 0x14977, // 1910-1919\r\n    0x04970, 0x0a4b0, 0x0b4b5, 0x06a50, 0x06d40, 0x1ab54, 0x02b60, 0x09570, 0x052f2, 0x04970, // 1920-1929\r\n    0x06566, 0x0d4a0, 0x0ea50, 0x06e95, 0x05ad0, 0x02b60, 0x186e3, 0x092e0, 0x1c8d7, 0x0c950, // 1930-1939\r\n    0x0d4a0, 0x1d8a6, 0x0b550, 0x056a0, 0x1a5b4, 0x025d0, 0x092d0, 0x0d2b2, 0x0a950, 0x0b557, // 1940-1949\r\n    0x06ca0, 0x0b550, 0x15355, 0x04da0, 0x0a5b0, 0x14573, 0x052b0, 0x0a9a8, 0x0e950, 0x06aa0, // 1950-1959\r\n    0x0aea6, 0x0ab50, 0x04b60, 0x0aae4, 0x0a570, 0x05260, 0x0f263, 0x0d950, 0x05b57, 0x056a0, // 1960-1969\r\n    0x096d0, 0x04dd5, 0x04ad0, 0x0a4d0, 0x0d4d4, 0x0d250, 0x0d558, 0x0b540, 0x0b6a0, 0x195a6, // 1970-1979\r\n    0x095b0, 0x049b0, 0x0a974, 0x0a4b0, 0x0b27a, 0x06a50, 0x06d40, 0x0af46, 0x0ab60, 0x09570, // 1980-1989\r\n    0x04af5, 0x04970, 0x064b0, 0x074a3, 0x0ea50, 0x06b58, 0x05ac0, 0x0ab60, 0x096d5, 0x092e0, // 1990-1999\r\n    0x0c960, 0x0d954, 0x0d4a0, 0x0da50, 0x07552, 0x056a0, 0x0abb7, 0x025d0, 0x092d0, 0x0cab5, // 2000-2009\r\n    0x0a950, 0x0b4a0, 0x0baa4, 0x0ad50, 0x055d9, 0x04ba0, 0x0a5b0, 0x15176, 0x052b0, 0x0a930, // 2010-2019\r\n    0x07954, 0x06aa0, 0x0ad50, 0x05b52, 0x04b60, 0x0a6e6, 0x0a4e0, 0x0d260, 0x0ea65, 0x0d530, // 2020-2029\r\n    0x05aa0, 0x076a3, 0x096d0, 0x04afb, 0x04ad0, 0x0a4d0, 0x1d0b6, 0x0d250, 0x0d520, 0x0dd45, // 2030-2039\r\n    0x0b5a0, 0x056d0, 0x055b2, 0x049b0, 0x0a577, 0x0a4b0, 0x0aa50, 0x1b255, 0x06d20, 0x0ada0, // 2040-2049\r\n    /** <NAME_EMAIL>**/\r\n    0x14b63, 0x09370, 0x049f8, 0x04970, 0x064b0, 0x168a6, 0x0ea50, 0x06b20, 0x1a6c4, 0x0aae0, // 2050-2059\r\n    0x0a2e0, 0x0d2e3, 0x0c960, 0x0d557, 0x0d4a0, 0x0da50, 0x05d55, 0x056a0, 0x0a6d0, 0x055d4, // 2060-2069\r\n    0x052d0, 0x0a9b8, 0x0a950, 0x0b4a0, 0x0b6a6, 0x0ad50, 0x055a0, 0x0aba4, 0x0a5b0, 0x052b0, // 2070-2079\r\n    0x0b273, 0x06930, 0x07337, 0x06aa0, 0x0ad50, 0x14b55, 0x04b60, 0x0a570, 0x054e4, 0x0d160, // 2080-2089\r\n    0x0e968, 0x0d520, 0x0daa0, 0x16aa6, 0x056d0, 0x04ae0, 0x0a9d4, 0x0a2d0, 0x0d150, 0x0f252, // 2090-2099\r\n    0x0d520], // 2100\r\n\r\n  /**\r\n      * 公历每个月份的天数普通表\r\n      * @Array Of Property\r\n      * @return Number\r\n      */\r\n  solarMonth: [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31],\r\n\r\n  /**\r\n      * 天干地支之天干速查表\r\n      * @Array Of Property trans[\"甲\",\"乙\",\"丙\",\"丁\",\"戊\",\"己\",\"庚\",\"辛\",\"壬\",\"癸\"]\r\n      * @return Cn string\r\n      */\r\n  Gan: ['\\u7532', '\\u4e59', '\\u4e19', '\\u4e01', '\\u620a', '\\u5df1', '\\u5e9a', '\\u8f9b', '\\u58ec', '\\u7678'],\r\n\r\n  /**\r\n      * 天干地支之地支速查表\r\n      * @Array Of Property\r\n      * @trans[\"子\",\"丑\",\"寅\",\"卯\",\"辰\",\"巳\",\"午\",\"未\",\"申\",\"酉\",\"戌\",\"亥\"]\r\n      * @return Cn string\r\n      */\r\n  Zhi: ['\\u5b50', '\\u4e11', '\\u5bc5', '\\u536f', '\\u8fb0', '\\u5df3', '\\u5348', '\\u672a', '\\u7533', '\\u9149', '\\u620c', '\\u4ea5'],\r\n\r\n  /**\r\n      * 天干地支之地支速查表<=>生肖\r\n      * @Array Of Property\r\n      * @trans[\"鼠\",\"牛\",\"虎\",\"兔\",\"龙\",\"蛇\",\"马\",\"羊\",\"猴\",\"鸡\",\"狗\",\"猪\"]\r\n      * @return Cn string\r\n      */\r\n  Animals: ['\\u9f20', '\\u725b', '\\u864e', '\\u5154', '\\u9f99', '\\u86c7', '\\u9a6c', '\\u7f8a', '\\u7334', '\\u9e21', '\\u72d7', '\\u732a'],\r\n\r\n  /**\r\n      * 24节气速查表\r\n      * @Array Of Property\r\n      * @trans[\"小寒\",\"大寒\",\"立春\",\"雨水\",\"惊蛰\",\"春分\",\"清明\",\"谷雨\",\"立夏\",\"小满\",\"芒种\",\"夏至\",\"小暑\",\"大暑\",\"立秋\",\"处暑\",\"白露\",\"秋分\",\"寒露\",\"霜降\",\"立冬\",\"小雪\",\"大雪\",\"冬至\"]\r\n      * @return Cn string\r\n      */\r\n  solarTerm: ['\\u5c0f\\u5bd2', '\\u5927\\u5bd2', '\\u7acb\\u6625', '\\u96e8\\u6c34', '\\u60ca\\u86f0', '\\u6625\\u5206', '\\u6e05\\u660e', '\\u8c37\\u96e8', '\\u7acb\\u590f', '\\u5c0f\\u6ee1', '\\u8292\\u79cd', '\\u590f\\u81f3', '\\u5c0f\\u6691', '\\u5927\\u6691', '\\u7acb\\u79cb', '\\u5904\\u6691', '\\u767d\\u9732', '\\u79cb\\u5206', '\\u5bd2\\u9732', '\\u971c\\u964d', '\\u7acb\\u51ac', '\\u5c0f\\u96ea', '\\u5927\\u96ea', '\\u51ac\\u81f3'],\r\n\r\n  /**\r\n      * 1900-2100各年的24节气日期速查表\r\n      * @Array Of Property\r\n      * @return 0x string For splice\r\n      */\r\n  sTermInfo: ['9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e', '97bcf97c3598082c95f8c965cc920f',\r\n    '97bd0b06bdb0722c965ce1cfcc920f', 'b027097bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e',\r\n    '97bcf97c359801ec95f8c965cc920f', '97bd0b06bdb0722c965ce1cfcc920f', 'b027097bd097c36b0b6fc9274c91aa',\r\n    '97b6b97bd19801ec9210c965cc920e', '97bcf97c359801ec95f8c965cc920f', '97bd0b06bdb0722c965ce1cfcc920f',\r\n    'b027097bd097c36b0b6fc9274c91aa', '9778397bd19801ec9210c965cc920e', '97b6b97bd19801ec95f8c965cc920f',\r\n    '97bd09801d98082c95f8e1cfcc920f', '97bd097bd097c36b0b6fc9210c8dc2', '9778397bd197c36c9210c9274c91aa',\r\n    '97b6b97bd19801ec95f8c965cc920e', '97bd09801d98082c95f8e1cfcc920f', '97bd097bd097c36b0b6fc9210c8dc2',\r\n    '9778397bd097c36c9210c9274c91aa', '97b6b97bd19801ec95f8c965cc920e', '97bcf97c3598082c95f8e1cfcc920f',\r\n    '97bd097bd097c36b0b6fc9210c8dc2', '9778397bd097c36c9210c9274c91aa', '97b6b97bd19801ec9210c965cc920e',\r\n    '97bcf97c3598082c95f8c965cc920f', '97bd097bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa',\r\n    '97b6b97bd19801ec9210c965cc920e', '97bcf97c3598082c95f8c965cc920f', '97bd097bd097c35b0b6fc920fb0722',\r\n    '9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e', '97bcf97c359801ec95f8c965cc920f',\r\n    '97bd097bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e',\r\n    '97bcf97c359801ec95f8c965cc920f', '97bd097bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa',\r\n    '97b6b97bd19801ec9210c965cc920e', '97bcf97c359801ec95f8c965cc920f', '97bd097bd07f595b0b6fc920fb0722',\r\n    '9778397bd097c36b0b6fc9210c8dc2', '9778397bd19801ec9210c9274c920e', '97b6b97bd19801ec95f8c965cc920f',\r\n    '97bd07f5307f595b0b0bc920fb0722', '7f0e397bd097c36b0b6fc9210c8dc2', '9778397bd097c36c9210c9274c920e',\r\n    '97b6b97bd19801ec95f8c965cc920f', '97bd07f5307f595b0b0bc920fb0722', '7f0e397bd097c36b0b6fc9210c8dc2',\r\n    '9778397bd097c36c9210c9274c91aa', '97b6b97bd19801ec9210c965cc920e', '97bd07f1487f595b0b0bc920fb0722',\r\n    '7f0e397bd097c36b0b6fc9210c8dc2', '9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e',\r\n    '97bcf7f1487f595b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa',\r\n    '97b6b97bd19801ec9210c965cc920e', '97bcf7f1487f595b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722',\r\n    '9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e', '97bcf7f1487f531b0b0bb0b6fb0722',\r\n    '7f0e397bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e',\r\n    '97bcf7f1487f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa',\r\n    '97b6b97bd19801ec9210c9274c920e', '97bcf7f0e47f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722',\r\n    '9778397bd097c36b0b6fc9210c91aa', '97b6b97bd197c36c9210c9274c920e', '97bcf7f0e47f531b0b0bb0b6fb0722',\r\n    '7f0e397bd07f595b0b0bc920fb0722', '9778397bd097c36b0b6fc9210c8dc2', '9778397bd097c36c9210c9274c920e',\r\n    '97b6b7f0e47f531b0723b0b6fb0722', '7f0e37f5307f595b0b0bc920fb0722', '7f0e397bd097c36b0b6fc9210c8dc2',\r\n    '9778397bd097c36b0b70c9274c91aa', '97b6b7f0e47f531b0723b0b6fb0721', '7f0e37f1487f595b0b0bb0b6fb0722',\r\n    '7f0e397bd097c35b0b6fc9210c8dc2', '9778397bd097c36b0b6fc9274c91aa', '97b6b7f0e47f531b0723b0b6fb0721',\r\n    '7f0e27f1487f595b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa',\r\n    '97b6b7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722',\r\n    '9778397bd097c36b0b6fc9274c91aa', '97b6b7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722',\r\n    '7f0e397bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa', '97b6b7f0e47f531b0723b0b6fb0721',\r\n    '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722', '9778397bd097c36b0b6fc9274c91aa',\r\n    '97b6b7f0e47f531b0723b0787b0721', '7f0e27f0e47f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722',\r\n    '9778397bd097c36b0b6fc9210c91aa', '97b6b7f0e47f149b0723b0787b0721', '7f0e27f0e47f531b0723b0b6fb0722',\r\n    '7f0e397bd07f595b0b0bc920fb0722', '9778397bd097c36b0b6fc9210c8dc2', '977837f0e37f149b0723b0787b0721',\r\n    '7f07e7f0e47f531b0723b0b6fb0722', '7f0e37f5307f595b0b0bc920fb0722', '7f0e397bd097c35b0b6fc9210c8dc2',\r\n    '977837f0e37f14998082b0787b0721', '7f07e7f0e47f531b0723b0b6fb0721', '7f0e37f1487f595b0b0bb0b6fb0722',\r\n    '7f0e397bd097c35b0b6fc9210c8dc2', '977837f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721',\r\n    '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722', '977837f0e37f14998082b0787b06bd',\r\n    '7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722',\r\n    '977837f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722',\r\n    '7f0e397bd07f595b0b0bc920fb0722', '977837f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721',\r\n    '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722', '977837f0e37f14998082b0787b06bd',\r\n    '7f07e7f0e47f149b0723b0787b0721', '7f0e27f0e47f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722',\r\n    '977837f0e37f14998082b0723b06bd', '7f07e7f0e37f149b0723b0787b0721', '7f0e27f0e47f531b0723b0b6fb0722',\r\n    '7f0e397bd07f595b0b0bc920fb0722', '977837f0e37f14898082b0723b02d5', '7ec967f0e37f14998082b0787b0721',\r\n    '7f07e7f0e47f531b0723b0b6fb0722', '7f0e37f1487f595b0b0bb0b6fb0722', '7f0e37f0e37f14898082b0723b02d5',\r\n    '7ec967f0e37f14998082b0787b0721', '7f07e7f0e47f531b0723b0b6fb0722', '7f0e37f1487f531b0b0bb0b6fb0722',\r\n    '7f0e37f0e37f14898082b0723b02d5', '7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721',\r\n    '7f0e37f1487f531b0b0bb0b6fb0722', '7f0e37f0e37f14898082b072297c35', '7ec967f0e37f14998082b0787b06bd',\r\n    '7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e37f0e37f14898082b072297c35',\r\n    '7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722',\r\n    '7f0e37f0e366aa89801eb072297c35', '7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f149b0723b0787b0721',\r\n    '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e37f0e366aa89801eb072297c35', '7ec967f0e37f14998082b0723b06bd',\r\n    '7f07e7f0e47f149b0723b0787b0721', '7f0e27f0e47f531b0723b0b6fb0722', '7f0e37f0e366aa89801eb072297c35',\r\n    '7ec967f0e37f14998082b0723b06bd', '7f07e7f0e37f14998083b0787b0721', '7f0e27f0e47f531b0723b0b6fb0722',\r\n    '7f0e37f0e366aa89801eb072297c35', '7ec967f0e37f14898082b0723b02d5', '7f07e7f0e37f14998082b0787b0721',\r\n    '7f07e7f0e47f531b0723b0b6fb0722', '7f0e36665b66aa89801e9808297c35', '665f67f0e37f14898082b0723b02d5',\r\n    '7ec967f0e37f14998082b0787b0721', '7f07e7f0e47f531b0723b0b6fb0722', '7f0e36665b66a449801e9808297c35',\r\n    '665f67f0e37f14898082b0723b02d5', '7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721',\r\n    '7f0e36665b66a449801e9808297c35', '665f67f0e37f14898082b072297c35', '7ec967f0e37f14998082b0787b06bd',\r\n    '7f07e7f0e47f531b0723b0b6fb0721', '7f0e26665b66a449801e9808297c35', '665f67f0e37f1489801eb072297c35',\r\n    '7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722'],\r\n\r\n  /**\r\n      * 数字转中文速查表\r\n      * @Array Of Property\r\n      * @trans ['日','一','二','三','四','五','六','七','八','九','十']\r\n      * @return Cn string\r\n      */\r\n  nStr1: ['\\u65e5', '\\u4e00', '\\u4e8c', '\\u4e09', '\\u56db', '\\u4e94', '\\u516d', '\\u4e03', '\\u516b', '\\u4e5d', '\\u5341'],\r\n\r\n  /**\r\n      * 日期转农历称呼速查表\r\n      * @Array Of Property\r\n      * @trans ['初','十','廿','卅']\r\n      * @return Cn string\r\n      */\r\n  nStr2: ['\\u521d', '\\u5341', '\\u5eff', '\\u5345'],\r\n\r\n  /**\r\n      * 月份转农历称呼速查表\r\n      * @Array Of Property\r\n      * @trans ['正','一','二','三','四','五','六','七','八','九','十','冬','腊']\r\n      * @return Cn string\r\n      */\r\n  nStr3: ['\\u6b63', '\\u4e8c', '\\u4e09', '\\u56db', '\\u4e94', '\\u516d', '\\u4e03', '\\u516b', '\\u4e5d', '\\u5341', '\\u51ac', '\\u814a'],\r\n\r\n  /**\r\n      * 返回农历y年一整年的总天数\r\n      * @param lunar Year\r\n      * @return Number\r\n      * @eg:var count = calendar.lYearDays(1987) ;//count=387\r\n      */\r\n  lYearDays: function (y) {\r\n    var i; var sum = 348\r\n    for (i = 0x8000; i > 0x8; i >>= 1) { sum += (this.lunarInfo[y - 1900] & i) ? 1 : 0 }\r\n    return (sum + this.leapDays(y))\r\n  },\r\n\r\n  /**\r\n      * 返回农历y年闰月是哪个月；若y年没有闰月 则返回0\r\n      * @param lunar Year\r\n      * @return Number (0-12)\r\n      * @eg:var leapMonth = calendar.leapMonth(1987) ;//leapMonth=6\r\n      */\r\n  leapMonth: function (y) { // 闰字编码 \\u95f0\r\n    return (this.lunarInfo[y - 1900] & 0xf)\r\n  },\r\n\r\n  /**\r\n      * 返回农历y年闰月的天数 若该年没有闰月则返回0\r\n      * @param lunar Year\r\n      * @return Number (0、29、30)\r\n      * @eg:var leapMonthDay = calendar.leapDays(1987) ;//leapMonthDay=29\r\n      */\r\n  leapDays: function (y) {\r\n    if (this.leapMonth(y)) {\r\n      return ((this.lunarInfo[y - 1900] & 0x10000) ? 30 : 29)\r\n    }\r\n    return (0)\r\n  },\r\n\r\n  /**\r\n      * 返回农历y年m月（非闰月）的总天数，计算m为闰月时的天数请使用leapDays方法\r\n      * @param lunar Year\r\n      * @return Number (-1、29、30)\r\n      * @eg:var MonthDay = calendar.monthDays(1987,9) ;//MonthDay=29\r\n      */\r\n  monthDays: function (y, m) {\r\n    if (m > 12 || m < 1) { return -1 }// 月份参数从1至12，参数错误返回-1\r\n    return ((this.lunarInfo[y - 1900] & (0x10000 >> m)) ? 30 : 29)\r\n  },\r\n\r\n  /**\r\n      * 返回公历(!)y年m月的天数\r\n      * @param solar Year\r\n      * @return Number (-1、28、29、30、31)\r\n      * @eg:var solarMonthDay = calendar.leapDays(1987) ;//solarMonthDay=30\r\n      */\r\n  solarDays: function (y, m) {\r\n    if (m > 12 || m < 1) { return -1 } // 若参数错误 返回-1\r\n    var ms = m - 1\r\n    if (ms == 1) { // 2月份的闰平规律测算后确认返回28或29\r\n      return (((y % 4 == 0) && (y % 100 != 0) || (y % 400 == 0)) ? 29 : 28)\r\n    } else {\r\n      return (this.solarMonth[ms])\r\n    }\r\n  },\r\n\r\n  /**\r\n     * 农历年份转换为干支纪年\r\n     * @param  lYear 农历年的年份数\r\n     * @return Cn string\r\n     */\r\n  toGanZhiYear: function (lYear) {\r\n    var ganKey = (lYear - 3) % 10\r\n    var zhiKey = (lYear - 3) % 12\r\n    if (ganKey == 0) ganKey = 10// 如果余数为0则为最后一个天干\r\n    if (zhiKey == 0) zhiKey = 12// 如果余数为0则为最后一个地支\r\n    return this.Gan[ganKey - 1] + this.Zhi[zhiKey - 1]\r\n  },\r\n\r\n  /**\r\n     * 公历月、日判断所属星座\r\n     * @param  cMonth [description]\r\n     * @param  cDay [description]\r\n     * @return Cn string\r\n     */\r\n  toAstro: function (cMonth, cDay) {\r\n    var s = '\\u9b54\\u7faf\\u6c34\\u74f6\\u53cc\\u9c7c\\u767d\\u7f8a\\u91d1\\u725b\\u53cc\\u5b50\\u5de8\\u87f9\\u72ee\\u5b50\\u5904\\u5973\\u5929\\u79e4\\u5929\\u874e\\u5c04\\u624b\\u9b54\\u7faf'\r\n    var arr = [20, 19, 21, 21, 21, 22, 23, 23, 23, 23, 22, 22]\r\n    return s.substr(cMonth * 2 - (cDay < arr[cMonth - 1] ? 2 : 0), 2) + '\\u5ea7'// 座\r\n  },\r\n\r\n  /**\r\n      * 传入offset偏移量返回干支\r\n      * @param offset 相对甲子的偏移量\r\n      * @return Cn string\r\n      */\r\n  toGanZhi: function (offset) {\r\n    return this.Gan[offset % 10] + this.Zhi[offset % 12]\r\n  },\r\n\r\n  /**\r\n      * 传入公历(!)y年获得该年第n个节气的公历日期\r\n      * @param y公历年(1900-2100)；n二十四节气中的第几个节气(1~24)；从n=1(小寒)算起\r\n      * @return day Number\r\n      * @eg:var _24 = calendar.getTerm(1987,3) ;//_24=4;意即1987年2月4日立春\r\n      */\r\n  getTerm: function (y, n) {\r\n    if (y < 1900 || y > 2100) { return -1 }\r\n    if (n < 1 || n > 24) { return -1 }\r\n    var _table = this.sTermInfo[y - 1900]\r\n    var _info = [\r\n      parseInt('0x' + _table.substr(0, 5)).toString(),\r\n      parseInt('0x' + _table.substr(5, 5)).toString(),\r\n      parseInt('0x' + _table.substr(10, 5)).toString(),\r\n      parseInt('0x' + _table.substr(15, 5)).toString(),\r\n      parseInt('0x' + _table.substr(20, 5)).toString(),\r\n      parseInt('0x' + _table.substr(25, 5)).toString()\r\n    ]\r\n    var _calday = [\r\n      _info[0].substr(0, 1),\r\n      _info[0].substr(1, 2),\r\n      _info[0].substr(3, 1),\r\n      _info[0].substr(4, 2),\r\n\r\n      _info[1].substr(0, 1),\r\n      _info[1].substr(1, 2),\r\n      _info[1].substr(3, 1),\r\n      _info[1].substr(4, 2),\r\n\r\n      _info[2].substr(0, 1),\r\n      _info[2].substr(1, 2),\r\n      _info[2].substr(3, 1),\r\n      _info[2].substr(4, 2),\r\n\r\n      _info[3].substr(0, 1),\r\n      _info[3].substr(1, 2),\r\n      _info[3].substr(3, 1),\r\n      _info[3].substr(4, 2),\r\n\r\n      _info[4].substr(0, 1),\r\n      _info[4].substr(1, 2),\r\n      _info[4].substr(3, 1),\r\n      _info[4].substr(4, 2),\r\n\r\n      _info[5].substr(0, 1),\r\n      _info[5].substr(1, 2),\r\n      _info[5].substr(3, 1),\r\n      _info[5].substr(4, 2)\r\n    ]\r\n    return parseInt(_calday[n - 1])\r\n  },\r\n\r\n  /**\r\n      * 传入农历数字月份返回汉语通俗表示法\r\n      * @param lunar month\r\n      * @return Cn string\r\n      * @eg:var cnMonth = calendar.toChinaMonth(12) ;//cnMonth='腊月'\r\n      */\r\n  toChinaMonth: function (m) { // 月 => \\u6708\r\n    if (m > 12 || m < 1) { return -1 } // 若参数错误 返回-1\r\n    var s = this.nStr3[m - 1]\r\n    s += '\\u6708'// 加上月字\r\n    return s\r\n  },\r\n\r\n  /**\r\n      * 传入农历日期数字返回汉字表示法\r\n      * @param lunar day\r\n      * @return Cn string\r\n      * @eg:var cnDay = calendar.toChinaDay(21) ;//cnMonth='廿一'\r\n      */\r\n  toChinaDay: function (d) { // 日 => \\u65e5\r\n    var s\r\n    switch (d) {\r\n      case 10:\r\n        s = '\\u521d\\u5341'; break\r\n      case 20:\r\n        s = '\\u4e8c\\u5341'; break\r\n      case 30:\r\n        s = '\\u4e09\\u5341'; break\r\n      default :\r\n        s = this.nStr2[Math.floor(d / 10)]\r\n        s += this.nStr1[d % 10]\r\n    }\r\n    return (s)\r\n  },\r\n\r\n  /**\r\n      * 年份转生肖[!仅能大致转换] => 精确划分生肖分界线是“立春”\r\n      * @param y year\r\n      * @return Cn string\r\n      * @eg:var animal = calendar.getAnimal(1987) ;//animal='兔'\r\n      */\r\n  getAnimal: function (y) {\r\n    return this.Animals[(y - 4) % 12]\r\n  },\r\n\r\n  /**\r\n      * 传入阳历年月日获得详细的公历、农历object信息 <=>JSON\r\n      * @param y  solar year\r\n      * @param m  solar month\r\n      * @param d  solar day\r\n      * @return JSON object\r\n      * @eg:console.log(calendar.solar2lunar(1987,11,01));\r\n      */\r\n  solar2lunar: function (y, m, d) { // 参数区间1900.1.31~2100.12.31\r\n    // 年份限定、上限\r\n    if (y < 1900 || y > 2100) {\r\n      return -1// undefined转换为数字变为NaN\r\n    }\r\n    // 公历传参最下限\r\n    if (y == 1900 && m == 1 && d < 31) {\r\n      return -1\r\n    }\r\n    // 未传参  获得当天\r\n    if (!y) {\r\n      var objDate = new Date()\r\n    } else {\r\n      var objDate = new Date(y, parseInt(m) - 1, d)\r\n    }\r\n    var i; var leap = 0; var temp = 0\r\n    // 修正ymd参数\r\n    var y = objDate.getFullYear()\r\n    var m = objDate.getMonth() + 1\r\n    var d = objDate.getDate()\r\n    var offset = (Date.UTC(objDate.getFullYear(), objDate.getMonth(), objDate.getDate()) - Date.UTC(1900, 0, 31)) / 86400000\r\n    for (i = 1900; i < 2101 && offset > 0; i++) {\r\n      temp = this.lYearDays(i)\r\n      offset -= temp\r\n    }\r\n    if (offset < 0) {\r\n      offset += temp; i--\r\n    }\r\n\r\n    // 是否今天\r\n    var isTodayObj = new Date()\r\n    var isToday = false\r\n    if (isTodayObj.getFullYear() == y && isTodayObj.getMonth() + 1 == m && isTodayObj.getDate() == d) {\r\n      isToday = true\r\n    }\r\n    // 星期几\r\n    var nWeek = objDate.getDay()\r\n    var cWeek = this.nStr1[nWeek]\r\n    // 数字表示周几顺应天朝周一开始的惯例\r\n    if (nWeek == 0) {\r\n      nWeek = 7\r\n    }\r\n    // 农历年\r\n    var year = i\r\n    var leap = this.leapMonth(i) // 闰哪个月\r\n    var isLeap = false\r\n\r\n    // 效验闰月\r\n    for (i = 1; i < 13 && offset > 0; i++) {\r\n      // 闰月\r\n      if (leap > 0 && i == (leap + 1) && isLeap == false) {\r\n        --i\r\n        isLeap = true; temp = this.leapDays(year) // 计算农历闰月天数\r\n      } else {\r\n        temp = this.monthDays(year, i)// 计算农历普通月天数\r\n      }\r\n      // 解除闰月\r\n      if (isLeap == true && i == (leap + 1)) { isLeap = false }\r\n      offset -= temp\r\n    }\r\n    // 闰月导致数组下标重叠取反\r\n    if (offset == 0 && leap > 0 && i == leap + 1) {\r\n      if (isLeap) {\r\n        isLeap = false\r\n      } else {\r\n        isLeap = true; --i\r\n      }\r\n    }\r\n    if (offset < 0) {\r\n      offset += temp; --i\r\n    }\r\n    // 农历月\r\n    var month = i\r\n    // 农历日\r\n    var day = offset + 1\r\n    // 天干地支处理\r\n    var sm = m - 1\r\n    var gzY = this.toGanZhiYear(year)\r\n\r\n    // 当月的两个节气\r\n    // bugfix-2017-7-24 11:03:38 use lunar Year Param `y` Not `year`\r\n    var firstNode = this.getTerm(y, (m * 2 - 1))// 返回当月「节」为几日开始\r\n    var secondNode = this.getTerm(y, (m * 2))// 返回当月「节」为几日开始\r\n\r\n    // 依据12节气修正干支月\r\n    var gzM = this.toGanZhi((y - 1900) * 12 + m + 11)\r\n    if (d >= firstNode) {\r\n      gzM = this.toGanZhi((y - 1900) * 12 + m + 12)\r\n    }\r\n\r\n    // 传入的日期的节气与否\r\n    var isTerm = false\r\n    var Term = null\r\n    if (firstNode == d) {\r\n      isTerm = true\r\n      Term = this.solarTerm[m * 2 - 2]\r\n    }\r\n    if (secondNode == d) {\r\n      isTerm = true\r\n      Term = this.solarTerm[m * 2 - 1]\r\n    }\r\n    // 日柱 当月一日与 1900/1/1 相差天数\r\n    var dayCyclical = Date.UTC(y, sm, 1, 0, 0, 0, 0) / 86400000 + 25567 + 10\r\n    var gzD = this.toGanZhi(dayCyclical + d - 1)\r\n    // 该日期所属的星座\r\n    var astro = this.toAstro(m, d)\r\n\r\n    return { 'lYear': year, 'lMonth': month, 'lDay': day, 'Animal': this.getAnimal(year), 'IMonthCn': (isLeap ? '\\u95f0' : '') + this.toChinaMonth(month), 'IDayCn': this.toChinaDay(day), 'cYear': y, 'cMonth': m, 'cDay': d, 'gzYear': gzY, 'gzMonth': gzM, 'gzDay': gzD, 'isToday': isToday, 'isLeap': isLeap, 'nWeek': nWeek, 'ncWeek': '\\u661f\\u671f' + cWeek, 'isTerm': isTerm, 'Term': Term, 'astro': astro }\r\n  },\r\n\r\n  /**\r\n      * 传入农历年月日以及传入的月份是否闰月获得详细的公历、农历object信息 <=>JSON\r\n      * @param y  lunar year\r\n      * @param m  lunar month\r\n      * @param d  lunar day\r\n      * @param isLeapMonth  lunar month is leap or not.[如果是农历闰月第四个参数赋值true即可]\r\n      * @return JSON object\r\n      * @eg:console.log(calendar.lunar2solar(1987,9,10));\r\n      */\r\n  lunar2solar: function (y, m, d, isLeapMonth) { // 参数区间1900.1.31~2100.12.1\r\n    var isLeapMonth = !!isLeapMonth\r\n    var leapOffset = 0\r\n    var leapMonth = this.leapMonth(y)\r\n    var leapDay = this.leapDays(y)\r\n    if (isLeapMonth && (leapMonth != m)) { return -1 }// 传参要求计算该闰月公历 但该年得出的闰月与传参的月份并不同\r\n    if (y == 2100 && m == 12 && d > 1 || y == 1900 && m == 1 && d < 31) { return -1 }// 超出了最大极限值\r\n    var day = this.monthDays(y, m)\r\n    var _day = day\r\n    // bugFix 2016-9-25\r\n    // if month is leap, _day use leapDays method\r\n    if (isLeapMonth) {\r\n      _day = this.leapDays(y, m)\r\n    }\r\n    if (y < 1900 || y > 2100 || d > _day) { return -1 }// 参数合法性效验\r\n\r\n    // 计算农历的时间差\r\n    var offset = 0\r\n    for (var i = 1900; i < y; i++) {\r\n      offset += this.lYearDays(i)\r\n    }\r\n    var leap = 0; var isAdd = false\r\n    for (var i = 1; i < m; i++) {\r\n      leap = this.leapMonth(y)\r\n      if (!isAdd) { // 处理闰月\r\n        if (leap <= i && leap > 0) {\r\n          offset += this.leapDays(y); isAdd = true\r\n        }\r\n      }\r\n      offset += this.monthDays(y, i)\r\n    }\r\n    // 转换闰月农历 需补充该年闰月的前一个月的时差\r\n    if (isLeapMonth) { offset += day }\r\n    // 1900年农历正月一日的公历时间为1900年1月30日0时0分0秒(该时间也是本农历的最开始起始点)\r\n    var stmap = Date.UTC(1900, 1, 30, 0, 0, 0)\r\n    var calObj = new Date((offset + d - 31) * 86400000 + stmap)\r\n    var cY = calObj.getUTCFullYear()\r\n    var cM = calObj.getUTCMonth() + 1\r\n    var cD = calObj.getUTCDate()\r\n\r\n    return this.solar2lunar(cY, cM, cD)\r\n  }\r\n}\r\n\r\nexport default calendar\r\n"], "names": [], "mappings": ";AAcG,IAAC,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOb,WAAW;AAAA,IAAC;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA;AAAA,IAC3F;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA;AAAA,IACjF;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA;AAAA,IACjF;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA;AAAA,IACjF;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA;AAAA,IACjF;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA;AAAA,IACjF;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA;AAAA,IACjF;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA;AAAA,IACjF;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA;AAAA,IACjF;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA;AAAA,IACjF;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA;AAAA,IACjF;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA;AAAA,IACjF;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA;AAAA,IACjF;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA;AAAA,IACjF;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA;AAAA;AAAA,IAEjF;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA;AAAA,IACjF;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA;AAAA,IACjF;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA;AAAA,IACjF;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA;AAAA,IACjF;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA;AAAA,IACjF;AAAA,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOT,YAAY,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO3D,KAAK,CAAC,KAAU,KAAU,KAAU,KAAU,KAAU,KAAU,KAAU,KAAU,KAAU,GAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQxG,KAAK,CAAC,KAAU,KAAU,KAAU,KAAU,KAAU,KAAU,KAAU,KAAU,KAAU,KAAU,KAAU,GAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ5H,SAAS,CAAC,KAAU,KAAU,KAAU,KAAU,KAAU,KAAU,KAAU,KAAU,KAAU,KAAU,KAAU,GAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQhI,WAAW,CAAC,MAAgB,MAAgB,MAAgB,MAAgB,MAAgB,MAAgB,MAAgB,MAAgB,MAAgB,MAAgB,MAAgB,MAAgB,MAAgB,MAAgB,MAAgB,MAAgB,MAAgB,MAAgB,MAAgB,MAAgB,MAAgB,MAAgB,MAAgB,IAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO1Y,WAAW;AAAA,IAAC;AAAA,IAAkC;AAAA,IAAkC;AAAA,IAC9E;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,IACpE;AAAA,IAAkC;AAAA,IAAkC;AAAA,EAAgC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQtG,OAAO,CAAC,KAAU,KAAU,KAAU,KAAU,KAAU,KAAU,KAAU,KAAU,KAAU,KAAU,GAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQpH,OAAO,CAAC,KAAU,KAAU,KAAU,GAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ9C,OAAO,CAAC,KAAU,KAAU,KAAU,KAAU,KAAU,KAAU,KAAU,KAAU,KAAU,KAAU,KAAU,GAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ9H,WAAW,SAAU,GAAG;AACtB,QAAI;AAAG,QAAI,MAAM;AACjB,SAAK,IAAI,OAAQ,IAAI,GAAK,MAAM,GAAG;AAAE,aAAQ,KAAK,UAAU,IAAI,IAAI,IAAI,IAAK,IAAI;AAAA,IAAG;AACpF,WAAQ,MAAM,KAAK,SAAS,CAAC;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,WAAW,SAAU,GAAG;AACtB,WAAQ,KAAK,UAAU,IAAI,IAAI,IAAI;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,UAAU,SAAU,GAAG;AACrB,QAAI,KAAK,UAAU,CAAC,GAAG;AACrB,aAAS,KAAK,UAAU,IAAI,IAAI,IAAI,QAAW,KAAK;AAAA,IACrD;AACD,WAAQ;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,WAAW,SAAU,GAAG,GAAG;AACzB,QAAI,IAAI,MAAM,IAAI,GAAG;AAAE,aAAO;AAAA,IAAI;AAClC,WAAS,KAAK,UAAU,IAAI,IAAI,IAAK,SAAW,IAAM,KAAK;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,WAAW,SAAU,GAAG,GAAG;AACzB,QAAI,IAAI,MAAM,IAAI,GAAG;AAAE,aAAO;AAAA,IAAI;AAClC,QAAI,KAAK,IAAI;AACb,QAAI,MAAM,GAAG;AACX,aAAU,IAAI,KAAK,KAAO,IAAI,OAAO,KAAO,IAAI,OAAO,IAAM,KAAK;AAAA,IACxE,OAAW;AACL,aAAQ,KAAK,WAAW,EAAE;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,cAAc,SAAU,OAAO;AAC7B,QAAI,UAAU,QAAQ,KAAK;AAC3B,QAAI,UAAU,QAAQ,KAAK;AAC3B,QAAI,UAAU;AAAG,eAAS;AAC1B,QAAI,UAAU;AAAG,eAAS;AAC1B,WAAO,KAAK,IAAI,SAAS,CAAC,IAAI,KAAK,IAAI,SAAS,CAAC;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,SAAS,SAAU,QAAQ,MAAM;AAC/B,QAAI,IAAI;AACR,QAAI,MAAM,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AACzD,WAAO,EAAE,OAAO,SAAS,KAAK,OAAO,IAAI,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,UAAU,SAAU,QAAQ;AAC1B,WAAO,KAAK,IAAI,SAAS,EAAE,IAAI,KAAK,IAAI,SAAS,EAAE;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,SAAS,SAAU,GAAG,GAAG;AACvB,QAAI,IAAI,QAAQ,IAAI,MAAM;AAAE,aAAO;AAAA,IAAI;AACvC,QAAI,IAAI,KAAK,IAAI,IAAI;AAAE,aAAO;AAAA,IAAI;AAClC,QAAI,SAAS,KAAK,UAAU,IAAI,IAAI;AACpC,QAAI,QAAQ;AAAA,MACV,SAAS,OAAO,OAAO,OAAO,GAAG,CAAC,CAAC,EAAE,SAAU;AAAA,MAC/C,SAAS,OAAO,OAAO,OAAO,GAAG,CAAC,CAAC,EAAE,SAAU;AAAA,MAC/C,SAAS,OAAO,OAAO,OAAO,IAAI,CAAC,CAAC,EAAE,SAAU;AAAA,MAChD,SAAS,OAAO,OAAO,OAAO,IAAI,CAAC,CAAC,EAAE,SAAU;AAAA,MAChD,SAAS,OAAO,OAAO,OAAO,IAAI,CAAC,CAAC,EAAE,SAAU;AAAA,MAChD,SAAS,OAAO,OAAO,OAAO,IAAI,CAAC,CAAC,EAAE,SAAU;AAAA,IACjD;AACD,QAAI,UAAU;AAAA,MACZ,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC;AAAA,MACpB,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC;AAAA,MACpB,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC;AAAA,MACpB,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC;AAAA,MAEpB,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC;AAAA,MACpB,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC;AAAA,MACpB,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC;AAAA,MACpB,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC;AAAA,MAEpB,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC;AAAA,MACpB,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC;AAAA,MACpB,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC;AAAA,MACpB,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC;AAAA,MAEpB,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC;AAAA,MACpB,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC;AAAA,MACpB,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC;AAAA,MACpB,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC;AAAA,MAEpB,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC;AAAA,MACpB,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC;AAAA,MACpB,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC;AAAA,MACpB,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC;AAAA,MAEpB,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC;AAAA,MACpB,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC;AAAA,MACpB,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC;AAAA,MACpB,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC;AAAA,IACrB;AACD,WAAO,SAAS,QAAQ,IAAI,CAAC,CAAC;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,cAAc,SAAU,GAAG;AACzB,QAAI,IAAI,MAAM,IAAI,GAAG;AAAE,aAAO;AAAA,IAAI;AAClC,QAAI,IAAI,KAAK,MAAM,IAAI,CAAC;AACxB,SAAK;AACL,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,YAAY,SAAU,GAAG;AACvB,QAAI;AACJ,YAAQ,GAAC;AAAA,MACP,KAAK;AACH,YAAI;AAAgB;AAAA,MACtB,KAAK;AACH,YAAI;AAAgB;AAAA,MACtB,KAAK;AACH,YAAI;AAAgB;AAAA,MACtB;AACE,YAAI,KAAK,MAAM,KAAK,MAAM,IAAI,EAAE,CAAC;AACjC,aAAK,KAAK,MAAM,IAAI,EAAE;AAAA,IACzB;AACD,WAAQ;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,WAAW,SAAU,GAAG;AACtB,WAAO,KAAK,SAAS,IAAI,KAAK,EAAE;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUD,aAAa,SAAU,GAAG,GAAG,GAAG;AAE9B,QAAI,IAAI,QAAQ,IAAI,MAAM;AACxB,aAAO;AAAA,IACR;AAED,QAAI,KAAK,QAAQ,KAAK,KAAK,IAAI,IAAI;AACjC,aAAO;AAAA,IACR;AAED,QAAI,CAAC,GAAG;AACN,UAAI,UAAU,oBAAI,KAAM;AAAA,IAC9B,OAAW;AACL,UAAI,UAAU,IAAI,KAAK,GAAG,SAAS,CAAC,IAAI,GAAG,CAAC;AAAA,IAC7C;AACD,QAAI;AAAG,QAAI,OAAO;AAAG,QAAI,OAAO;AAEhC,QAAI,IAAI,QAAQ,YAAa;AAC7B,QAAI,IAAI,QAAQ,SAAQ,IAAK;AAC7B,QAAI,IAAI,QAAQ,QAAS;AACzB,QAAI,UAAU,KAAK,IAAI,QAAQ,YAAW,GAAI,QAAQ,SAAQ,GAAI,QAAQ,QAAO,CAAE,IAAI,KAAK,IAAI,MAAM,GAAG,EAAE,KAAK;AAChH,SAAK,IAAI,MAAM,IAAI,QAAQ,SAAS,GAAG,KAAK;AAC1C,aAAO,KAAK,UAAU,CAAC;AACvB,gBAAU;AAAA,IACX;AACD,QAAI,SAAS,GAAG;AACd,gBAAU;AAAM;AAAA,IACjB;AAGD,QAAI,aAAa,oBAAI,KAAM;AAC3B,QAAI,UAAU;AACd,QAAI,WAAW,iBAAiB,KAAK,WAAW,aAAa,KAAK,KAAK,WAAW,QAAO,KAAM,GAAG;AAChG,gBAAU;AAAA,IACX;AAED,QAAI,QAAQ,QAAQ,OAAQ;AAC5B,QAAI,QAAQ,KAAK,MAAM,KAAK;AAE5B,QAAI,SAAS,GAAG;AACd,cAAQ;AAAA,IACT;AAED,QAAI,OAAO;AACX,QAAI,OAAO,KAAK,UAAU,CAAC;AAC3B,QAAI,SAAS;AAGb,SAAK,IAAI,GAAG,IAAI,MAAM,SAAS,GAAG,KAAK;AAErC,UAAI,OAAO,KAAK,KAAM,OAAO,KAAM,UAAU,OAAO;AAClD,UAAE;AACF,iBAAS;AAAM,eAAO,KAAK,SAAS,IAAI;AAAA,MAChD,OAAa;AACL,eAAO,KAAK,UAAU,MAAM,CAAC;AAAA,MAC9B;AAED,UAAI,UAAU,QAAQ,KAAM,OAAO,GAAI;AAAE,iBAAS;AAAA,MAAO;AACzD,gBAAU;AAAA,IACX;AAED,QAAI,UAAU,KAAK,OAAO,KAAK,KAAK,OAAO,GAAG;AAC5C,UAAI,QAAQ;AACV,iBAAS;AAAA,MACjB,OAAa;AACL,iBAAS;AAAM,UAAE;AAAA,MAClB;AAAA,IACF;AACD,QAAI,SAAS,GAAG;AACd,gBAAU;AAAM,QAAE;AAAA,IACnB;AAED,QAAI,QAAQ;AAEZ,QAAI,MAAM,SAAS;AAEnB,QAAI,KAAK,IAAI;AACb,QAAI,MAAM,KAAK,aAAa,IAAI;AAIhC,QAAI,YAAY,KAAK,QAAQ,GAAI,IAAI,IAAI,CAAG;AAC5C,QAAI,aAAa,KAAK,QAAQ,GAAI,IAAI,CAAG;AAGzC,QAAI,MAAM,KAAK,UAAU,IAAI,QAAQ,KAAK,IAAI,EAAE;AAChD,QAAI,KAAK,WAAW;AAClB,YAAM,KAAK,UAAU,IAAI,QAAQ,KAAK,IAAI,EAAE;AAAA,IAC7C;AAGD,QAAI,SAAS;AACb,QAAI,OAAO;AACX,QAAI,aAAa,GAAG;AAClB,eAAS;AACT,aAAO,KAAK,UAAU,IAAI,IAAI,CAAC;AAAA,IAChC;AACD,QAAI,cAAc,GAAG;AACnB,eAAS;AACT,aAAO,KAAK,UAAU,IAAI,IAAI,CAAC;AAAA,IAChC;AAED,QAAI,cAAc,KAAK,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC,IAAI,QAAW,QAAQ;AACtE,QAAI,MAAM,KAAK,SAAS,cAAc,IAAI,CAAC;AAE3C,QAAI,QAAQ,KAAK,QAAQ,GAAG,CAAC;AAE7B,WAAO,EAAE,SAAS,MAAM,UAAU,OAAO,QAAQ,KAAK,UAAU,KAAK,UAAU,IAAI,GAAG,aAAa,SAAS,MAAW,MAAM,KAAK,aAAa,KAAK,GAAG,UAAU,KAAK,WAAW,GAAG,GAAG,SAAS,GAAG,UAAU,GAAG,QAAQ,GAAG,UAAU,KAAK,WAAW,KAAK,SAAS,KAAK,WAAW,SAAS,UAAU,QAAQ,SAAS,OAAO,UAAU,OAAiB,OAAO,UAAU,QAAQ,QAAQ,MAAM,SAAS,MAAO;AAAA,EACjZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWD,aAAa,SAAU,GAAG,GAAG,GAAG,aAAa;AAC3C,QAAI,cAAc,CAAC,CAAC;AAEpB,QAAI,YAAY,KAAK,UAAU,CAAC;AAClB,SAAK,SAAS,CAAC;AAC7B,QAAI,eAAgB,aAAa,GAAI;AAAE,aAAO;AAAA,IAAI;AAClD,QAAI,KAAK,QAAQ,KAAK,MAAM,IAAI,KAAK,KAAK,QAAQ,KAAK,KAAK,IAAI,IAAI;AAAE,aAAO;AAAA,IAAI;AACjF,QAAI,MAAM,KAAK,UAAU,GAAG,CAAC;AAC7B,QAAI,OAAO;AAGX,QAAI,aAAa;AACf,aAAO,KAAK,SAAS,GAAG,CAAC;AAAA,IAC1B;AACD,QAAI,IAAI,QAAQ,IAAI,QAAQ,IAAI,MAAM;AAAE,aAAO;AAAA,IAAI;AAGnD,QAAI,SAAS;AACb,aAAS,IAAI,MAAM,IAAI,GAAG,KAAK;AAC7B,gBAAU,KAAK,UAAU,CAAC;AAAA,IAC3B;AACD,QAAI,OAAO;AAAG,QAAI,QAAQ;AAC1B,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,aAAO,KAAK,UAAU,CAAC;AACvB,UAAI,CAAC,OAAO;AACV,YAAI,QAAQ,KAAK,OAAO,GAAG;AACzB,oBAAU,KAAK,SAAS,CAAC;AAAG,kBAAQ;AAAA,QACrC;AAAA,MACF;AACD,gBAAU,KAAK,UAAU,GAAG,CAAC;AAAA,IAC9B;AAED,QAAI,aAAa;AAAE,gBAAU;AAAA,IAAK;AAElC,QAAI,QAAQ,KAAK,IAAI,MAAM,GAAG,IAAI,GAAG,GAAG,CAAC;AACzC,QAAI,SAAS,IAAI,MAAM,SAAS,IAAI,MAAM,QAAW,KAAK;AAC1D,QAAI,KAAK,OAAO,eAAgB;AAChC,QAAI,KAAK,OAAO,YAAW,IAAK;AAChC,QAAI,KAAK,OAAO,WAAY;AAE5B,WAAO,KAAK,YAAY,IAAI,IAAI,EAAE;AAAA,EACnC;AACH;;"}