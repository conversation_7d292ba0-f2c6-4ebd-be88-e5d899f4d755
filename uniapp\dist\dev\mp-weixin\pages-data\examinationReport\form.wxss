/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-scroll-view.data-v-552db1d6 {
  height: calc(100vh - 44px);
  /* 减去导航栏高度 */
  width: 100%;
}
.form-container.data-v-552db1d6 {
  padding: 20rpx;
  padding-bottom: 120rpx;
  /* 增加底部内边距，防止内容被遮挡 */
}
.form-container .form-header.data-v-552db1d6 {
  margin-bottom: 20rpx;
  text-align: center;
  /* 标题居中 */
}
.form-container .form-header .form-title.data-v-552db1d6 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.form-container .form-section.data-v-552db1d6 {
  background-color: #FFFFFF;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}
.form-container .form-section .form-item.data-v-552db1d6 {
  margin-bottom: 30rpx;
}
.form-container .form-section .form-item .form-label.data-v-552db1d6 {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}
.form-container .form-section .form-item .inline-form-item.data-v-552db1d6 {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.form-container .form-section .form-item .inline-form-item .inline-form-label.data-v-552db1d6 {
  font-size: 28rpx;
  color: #333;
  min-width: 160rpx;
}
.form-container .form-section .form-item .inline-form-item .inline-form-input.data-v-552db1d6 {
  flex: 1;
  background-color: #F5F5F5;
  /* 灰色背景 */
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  min-height: 80rpx;
}
.form-container .form-section .form-item .inline-form-item .inline-form-input.data-v-552db1d6:disabled {
  background-color: #F5F5F5;
  color: #666;
}
.form-container .form-section .form-item .date-picker-wrapper.data-v-552db1d6 {
  width: 100%;
}
.form-container .form-section .form-item .date-picker.data-v-552db1d6 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #F5F5F5;
  /* 灰色背景 */
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  min-height: 80rpx;
  width: 100%;
  border: 1px solid #E0E7F1;
  /* 添加边框 */
}
.form-container .image-upload-area.data-v-552db1d6 {
  width: 100%;
}
.form-container .image-upload-area .image-list.data-v-552db1d6 {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 10rpx;
}
.form-container .image-upload-area .image-list .image-item.data-v-552db1d6 {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  overflow: hidden;
  position: relative;
}
.form-container .image-upload-area .image-list .image-item image.data-v-552db1d6 {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.form-container .image-upload-area .image-list .image-item .delete-btn.data-v-552db1d6 {
  position: absolute;
  right: 0;
  top: 0;
  width: 36rpx;
  height: 36rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 0 0 0 8rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.form-container .image-upload-area .image-list .upload-btn.data-v-552db1d6 {
  width: 160rpx;
  height: 160rpx;
  background-color: #F7F7F7;
  border-radius: 8rpx;
  border: 1rpx dashed #CCCCCC;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.form-container .image-upload-area .image-list .upload-btn text.data-v-552db1d6 {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}
.form-container .image-upload-area .tips.data-v-552db1d6 {
  font-size: 24rpx;
  color: #999;
}
.form-container .inline-picker-wrapper.data-v-552db1d6 {
  flex: 1;
  width: 100%;
}
.form-container .required.data-v-552db1d6 {
  color: #FF0000;
  margin-right: 4rpx;
}
.form-container .submit-section.data-v-552db1d6 {
  margin-top: 40rpx;
  margin-bottom: 60rpx;
  /* 增加底部间距 */
}
.form-container .submit-section .submit-btn.data-v-552db1d6 {
  width: 100%;
  background-color: #07C160;
  color: #FFFFFF;
  border-radius: 8rpx;
  font-size: 32rpx;
  padding: 20rpx 0;
}

/* 添加药物选择相关样式 */
.add-btn.data-v-552db1d6 {
  background-color: #07C160;
  color: #FFFFFF;
  font-size: 28rpx;
  padding: 15rpx 25rpx;
  border-radius: 8rpx;
  margin-left: 20rpx;
}
.add-btn.data-v-552db1d6:disabled {
  background-color: #cccccc;
}
.picker.data-v-552db1d6 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #F5F5F5;
  /* 灰色背景 */
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #999;
  box-sizing: border-box;
  min-height: 80rpx;
  border: 1px solid #E0E7F1;
  /* 添加边框 */
}
.report-list-header.data-v-552db1d6 {
  margin-bottom: 20rpx;
}
.report-list-header .report-list-title.data-v-552db1d6 {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}
.report-item.data-v-552db1d6 {
  background-color: #F7F7F7;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  position: relative;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.report-item .report-name.data-v-552db1d6 {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #eaeaea;
}
.report-item .report-name .report-type-picker.data-v-552db1d6 {
  flex: 0 0 auto;
}
.report-item .report-name .report-type-picker-inner.data-v-552db1d6 {
  background-color: #f5f5f5;
  padding: 10rpx 20rpx;
  min-width: 180rpx;
  border: 1px solid #E0E7F1;
}
.report-item .report-name .report-other-name.data-v-552db1d6 {
  font-weight: normal;
  font-size: 28rpx;
  color: #666;
  margin-left: 10rpx;
}
.report-item .report-name .date-picker-wrapper.data-v-552db1d6 {
  margin-left: auto;
  width: 280rpx;
}
.report-item .report-name .date-picker.data-v-552db1d6 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #F5F5F5;
  /* 灰色背景 */
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  min-height: 60rpx;
  width: 100%;
  border: 1px solid #eaeaea;
}
.report-item .report-name .date-picker .placeholder.data-v-552db1d6 {
  color: #999;
}
.report-item .report-name .delete-report-btn.data-v-552db1d6 {
  margin-left: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5rpx;
}
.report-item .report-details.data-v-552db1d6 {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 8rpx;
  padding: 15rpx;
}
.report-item .report-details .inline-form-item.data-v-552db1d6 {
  margin-bottom: 25rpx;
}
.report-item .report-details .inline-form-item.data-v-552db1d6:last-child {
  margin-bottom: 0;
}
.report-item .report-details .inline-form-item .inline-form-label.data-v-552db1d6 {
  margin-bottom: 12rpx;
  font-weight: 500;
  color: #2C2C2C;
}
.kidney-function-fields.data-v-552db1d6 {
  margin-bottom: 20rpx;
  /* 最外层样式结束 */
}
.kidney-function-fields .value-unit-input.data-v-552db1d6 {
  display: flex;
  flex-direction: column;
  margin-bottom: 20rpx;
}
.kidney-function-fields .value-unit-input .input-with-picker.data-v-552db1d6 {
  display: flex;
  align-items: center;
  width: 100%;
}
.kidney-function-fields .value-unit-input .form-input.data-v-552db1d6 {
  flex: 1;
  margin-right: 10rpx;
  background-color: #F5F5F5;
  border: 1px solid #E0E7F1;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  min-height: 80rpx;
}
.kidney-function-fields .value-unit-input .input-with-fixed-unit.data-v-552db1d6 {
  display: flex;
  align-items: center;
  width: 100%;
}
.kidney-function-fields .value-unit-input .input-with-fixed-unit .form-input.data-v-552db1d6 {
  flex: 1;
  background-color: #F5F5F5;
  border: 1px solid #E0E7F1;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  min-height: 80rpx;
  border-right: none;
}
.kidney-function-fields .value-unit-input .input-with-fixed-unit .fixed-unit.data-v-552db1d6 {
  width: 180rpx;
  /* 与 unit-picker 相同的宽度 */
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F5F5F5;
  border: 1px solid #E0E7F1;
  border-left: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;
  padding: 0 20rpx;
  box-sizing: border-box;
}
.kidney-function-fields .value-unit-input .unit-picker.data-v-552db1d6 {
  width: 180rpx;
}
.kidney-function-fields .value-unit-input .picker-view.data-v-552db1d6 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #F5F5F5;
  border: 1px solid #E0E7F1;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  min-height: 80rpx;
}
.kidney-function-fields .value-unit-input .unit-selector.data-v-552db1d6 {
  display: flex;
  flex-wrap: wrap;
}
.kidney-function-fields .value-unit-input .unit-selector radio-group.data-v-552db1d6 {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}
.kidney-function-fields .value-unit-input .unit-selector label.data-v-552db1d6 {
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  font-size: 28rpx;
  margin-bottom: 10rpx;
}
.kidney-function-fields .value-unit-input .unit-selector label radio.data-v-552db1d6 {
  margin-right: 4rpx;
  transform: scale(0.8);
}
.kidney-function-fields .radio-group.data-v-552db1d6 {
  display: flex;
  flex-direction: column;
  margin-top: 10rpx;
}
.kidney-function-fields .radio-group .radio-row.data-v-552db1d6 {
  display: flex;
  gap: 50rpx;
  margin-left: 20rpx;
  justify-content: flex-start;
}
.kidney-function-fields .radio-group .radio-row .radio-item.data-v-552db1d6 {
  display: flex;
  align-items: center;
  min-width: 100rpx;
}
.kidney-function-fields .radio-group .radio-row .radio-item .radio-btn.data-v-552db1d6 {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 2rpx solid #CCCCCC;
  margin-right: 10rpx;
  position: relative;
}
.kidney-function-fields .radio-group .radio-row .radio-item .radio-btn.checked.data-v-552db1d6 {
  border-color: #07C160;
}
.kidney-function-fields .radio-group .radio-row .radio-item .radio-btn.checked.data-v-552db1d6:after {
  content: "";
  position: absolute;
  width: 24rpx;
  height: 24rpx;
  background-color: #07C160;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.kidney-function-fields .radio-group .radio-row .radio-item text.data-v-552db1d6 {
  font-size: 28rpx;
  color: #333;
}
.kidney-function-fields .checkbox-group.data-v-552db1d6 {
  display: flex;
  flex-direction: column;
  margin-top: 10rpx;
}
.kidney-function-fields .checkbox-group .checkbox-row.data-v-552db1d6 {
  display: flex;
  flex-wrap: wrap;
  gap: 30rpx;
  margin-left: 20rpx;
  justify-content: flex-start;
}
.kidney-function-fields .checkbox-group .checkbox-row .checkbox-item.data-v-552db1d6 {
  display: flex;
  align-items: center;
  min-width: 180rpx;
  margin-bottom: 20rpx;
}
.kidney-function-fields .checkbox-group .checkbox-row .checkbox-item .checkbox-btn.data-v-552db1d6 {
  width: 36rpx;
  height: 36rpx;
  border-radius: 4rpx;
  border: 2rpx solid #CCCCCC;
  margin-right: 10rpx;
  position: relative;
}
.kidney-function-fields .checkbox-group .checkbox-row .checkbox-item .checkbox-btn.checked.data-v-552db1d6 {
  border-color: #07C160;
  background-color: #07C160;
}
.kidney-function-fields .checkbox-group .checkbox-row .checkbox-item .checkbox-btn.checked.data-v-552db1d6:after {
  content: "";
  position: absolute;
  width: 20rpx;
  height: 10rpx;
  border-left: 4rpx solid #FFFFFF;
  border-bottom: 4rpx solid #FFFFFF;
  top: 45%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
}
.kidney-function-fields .checkbox-group .checkbox-row .checkbox-item text.data-v-552db1d6 {
  font-size: 28rpx;
  color: #333;
}
.kidney-function-fields .rhythm-container.data-v-552db1d6 {
  flex-direction: column;
  align-items: flex-start;
}
.kidney-function-fields .rhythm-container .inline-form-label.data-v-552db1d6 {
  margin-bottom: 15rpx;
}