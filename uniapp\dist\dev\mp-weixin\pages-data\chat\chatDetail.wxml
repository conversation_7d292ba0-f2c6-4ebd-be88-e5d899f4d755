<layout-default-uni class="data-v-53009239" u-s="{{['d']}}" u-i="53009239-0" bind:__l="__l"><view class="chat-detail-page data-v-53009239"><page-layout wx:if="{{o}}" class="data-v-53009239" u-s="{{['d']}}" u-i="53009239-1,53009239-0" bind:__l="__l" u-p="{{o}}"><view class="page-wrapper data-v-53009239"><scroll-view class="page-scroll-view data-v-53009239" scroll-y="true"><view class="patient-question-card data-v-53009239"><view class="patient-header data-v-53009239"><image class="patient-avatar data-v-53009239" src="{{a}}" alt="头像" binderror="{{b}}"/><view class="patient-info data-v-53009239"><text class="patient-name data-v-53009239">{{c}}</text><text class="question-time data-v-53009239">{{d}}</text></view></view><view wx:if="{{e}}" class="title-section data-v-53009239"><text class="title-text data-v-53009239">{{f}}</text></view><view class="content-section data-v-53009239"><view class="content-header data-v-53009239"><text class="content-label data-v-53009239">问题描述</text></view><text class="content-text data-v-53009239">{{g}}</text></view><view wx:if="{{h}}" class="images-section data-v-53009239"><view class="images-header data-v-53009239"><text class="images-label data-v-53009239">相关图片 ({{i}})</text></view><view class="images-grid data-v-53009239"><view wx:for="{{j}}" wx:for-item="img" wx:key="d" class="image-item data-v-53009239" bindtap="{{img.e}}"><image class="question-image data-v-53009239" src="{{img.a}}" mode="aspectFill" binderror="{{img.b}}"/><view class="image-debug data-v-53009239"><text class="data-v-53009239">{{img.c}}</text></view></view></view></view><view wx:if="{{k}}" class="mentioned-section data-v-53009239"><view class="mentioned-doctors-list data-v-53009239"><text wx:for="{{l}}" wx:for-item="doctor" wx:key="b" class="doctor-tag data-v-53009239"> @{{doctor.a}}</text></view></view></view><view class="message-container data-v-53009239"><text class="message-title data-v-53009239">回复</text><view wx:if="{{m}}" class="data-v-53009239"><view wx:for="{{n}}" wx:for-item="rep" wx:key="o" class="reply data-v-53009239" bindtap="{{rep.p}}"><image class="reply-avatar data-v-53009239" src="{{rep.a}}" alt="头像" binderror="{{rep.b}}"/><view class="reply-part data-v-53009239"><view class="reply-name-container data-v-53009239"><text wx:if="{{rep.c}}" class="reply-name data-v-53009239">{{rep.d}}医生</text><text wx:else class="reply-name data-v-53009239">{{rep.e}}</text></view><view wx:if="{{rep.f}}" class="reply-mention data-v-53009239"><text class="mention-text data-v-53009239">@{{rep.g}}：{{rep.h}}</text></view><text class="reply-content data-v-53009239">{{rep.i}}</text><view wx:if="{{rep.j}}" class="reply-images data-v-53009239"><view wx:for="{{rep.k}}" wx:for-item="img" wx:key="d" class="reply-image-container data-v-53009239"><image class="reply-image data-v-53009239" src="{{img.a}}" mode="aspectFill" catchtap="{{img.b}}" binderror="{{img.c}}"/></view></view></view><view wx:if="{{rep.l}}" class="delete-button data-v-53009239" catchtap="{{rep.m}}"><view class="trash-icon data-v-53009239"><view class="trash-lid data-v-53009239"></view><view class="trash-body data-v-53009239"><view class="trash-line data-v-53009239"></view><view class="trash-line data-v-53009239"></view><view class="trash-line data-v-53009239"></view></view></view></view><text class="chat-time data-v-53009239">{{rep.n}}</text></view></view><view wx:else class="empty-reply-container data-v-53009239"><view class="empty-icon data-v-53009239">💬</view><text class="message-empty data-v-53009239">暂无回复</text><text class="empty-subtitle data-v-53009239">成为第一个回复的人吧</text></view></view></scroll-view></view></page-layout><view class="reply-window-fixed data-v-53009239"><view wx:if="{{p}}" class="reply-tip-above-input data-v-53009239"><label class="reply-content-text data-v-53009239">@{{q}}：{{r}}</label><label class="cancel-reply data-v-53009239" bindtap="{{s}}">取消</label></view><view class="reply-input-row data-v-53009239"><image class="reply-avatar data-v-53009239" src="{{t}}" alt="头像" binderror="{{v}}"/><view class="reply-input-area data-v-53009239"><block wx:if="{{r0}}"><textarea ref="replyTextarea" class="reply-textarea data-v-53009239" placeholder="请输入回复内容" focus="{{w}}" auto-height="{{true}}" fixed="{{true}}" show-confirm-bar="{{false}}" value="{{x}}" bindinput="{{y}}"></textarea></block></view><image src="https://www.mograine.cn/images/upload_picture.jpg" class="upload-button data-v-53009239" bindtap="{{z}}"/><button class="{{['sub-reply', 'data-v-53009239', A && 'disabled']}}" bindtap="{{B}}" disabled="{{C}}">提交</button></view><view wx:if="{{D}}" class="reply-input-images data-v-53009239"><view wx:for="{{E}}" wx:for-item="img" wx:key="c" class="reply-input-image data-v-53009239" bindtap="{{img.d}}"><image class="input-image-preview data-v-53009239" src="{{img.a}}" mode="aspectFill"/><view class="remove-image data-v-53009239" catchtap="{{img.b}}">×</view></view></view></view></view></layout-default-uni>