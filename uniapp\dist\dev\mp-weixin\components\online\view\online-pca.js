"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../../common/vendor.js");
const components_online_areaPickerData = require("../area-picker-data.js");
const common_areaData_Area = require("../../../common/areaData/Area.js");
const utils_is = require("../../../utils/is.js");
if (!Array) {
  const _easycom_wd_picker2 = common_vendor.resolveComponent("wd-picker");
  _easycom_wd_picker2();
}
const _easycom_wd_picker = () => "../../../node-modules/wot-design-uni/components/wd-picker/wd-picker.js";
if (!Math) {
  _easycom_wd_picker();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "online-pca",
  props: {
    label: {
      type: String,
      default: "",
      required: false
    },
    labelWidth: {
      type: String,
      default: "80px",
      required: false
    },
    value: {
      type: [String, Array],
      required: false
    },
    placeholder: {
      type: String,
      required: false,
      default: "请选择省市区"
    },
    disabled: {
      type: Boolean,
      default: false,
      required: false
    },
    required: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  emits: ["input", "change", "update:value"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emits = __emit;
    const selected = common_vendor.ref([]);
    const district = __spreadValues({}, components_online_areaPickerData.areaData);
    const columns = common_vendor.ref([
      district[0],
      district[district[0][0].value],
      district[district[district[0][0].value][0].value]
    ]);
    const onChangeDistrict = (pickerView, value, columnIndex, resolve) => {
      const item = value[columnIndex];
      if (columnIndex === 0) {
        pickerView.setColumnData(1, district[item.value]);
        pickerView.setColumnData(2, district[district[item.value][0].value]);
      } else if (columnIndex === 1) {
        pickerView.setColumnData(2, district[item.value]);
      }
      resolve();
    };
    const handleConfirm = ({ value }) => {
      emits("update:value", value);
    };
    common_vendor.watch(
      () => props.value,
      (val) => __async(this, null, function* () {
        if (props.value && utils_is.isString(props.value)) {
          let arr = common_areaData_Area.getAreaArrByCode(props.value);
          selected.value = arr;
          yield initColumnData(arr);
        }
      }),
      { immediate: true }
    );
    function initColumnData(val) {
      if (val && val.length) {
        let first = district[0];
        let second = district[selected.value[0]];
        let third = district[selected.value[1]];
        columns.value = [first, second, third];
      }
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(handleConfirm),
        b: common_vendor.o(($event) => common_vendor.isRef(selected) ? selected.value = $event : null),
        c: common_vendor.p({
          columns: common_vendor.unref(columns),
          ["label-width"]: __props.labelWidth,
          label: __props.label,
          required: __props.required,
          ["column-change"]: onChangeDistrict,
          modelValue: common_vendor.unref(selected)
        })
      };
    };
  }
});
wx.createComponent(_sfc_main);
//# sourceMappingURL=online-pca.js.map
