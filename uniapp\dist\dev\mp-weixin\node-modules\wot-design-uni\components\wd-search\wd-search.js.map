{"version": 3, "file": "wd-search.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-search/wd-search.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1zZWFyY2gvd2Qtc2VhcmNoLnZ1ZQ"], "sourcesContent": ["<template>\n  <view :class=\"rootClass\" :style=\"customStyle\">\n    <view class=\"wd-search__block\">\n      <slot name=\"prefix\"></slot>\n      <view class=\"wd-search__field\">\n        <view v-if=\"!placeholderLeft\" :style=\"coverStyle\" class=\"wd-search__cover\" @click=\"closeCover\">\n          <wd-icon name=\"search\" custom-class=\"wd-search__search-icon\"></wd-icon>\n          <text :class=\"`wd-search__placeholder-txt ${placeholderClass}`\">{{ placeholder || translate('search') }}</text>\n        </view>\n        <wd-icon v-if=\"showInput || str || placeholderLeft\" name=\"search\" custom-class=\"wd-search__search-left-icon\"></wd-icon>\n        <input\n          v-if=\"showInput || str || placeholderLeft\"\n          :placeholder=\"placeholder || translate('search')\"\n          :placeholder-class=\"`wd-search__placeholder-txt ${placeholderClass}`\"\n          :placeholder-style=\"placeholderStyle\"\n          confirm-type=\"search\"\n          v-model=\"str\"\n          :class=\"['wd-search__input', customInputClass]\"\n          @focus=\"searchFocus\"\n          @input=\"inputValue\"\n          @blur=\"searchBlur\"\n          @confirm=\"search\"\n          :disabled=\"disabled\"\n          :maxlength=\"maxlength\"\n          :focus=\"isFocused\"\n        />\n        <wd-icon v-if=\"str\" custom-class=\"wd-search__clear wd-search__clear-icon\" name=\"error-fill\" @click=\"clearSearch\" />\n      </view>\n    </view>\n\n    <slot v-if=\"!hideCancel\" name=\"suffix\">\n      <view class=\"wd-search__cancel\" @click=\"handleCancel\">\n        {{ cancelTxt || translate('cancel') }}\n      </view>\n    </slot>\n  </view>\n</template>\n\n<script lang=\"ts\">\nexport default {\n  name: 'wd-search',\n  options: {\n    virtualHost: true,\n    addGlobalClass: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport wdIcon from '../wd-icon/wd-icon.vue'\nimport { type CSSProperties, computed, onMounted, ref, watch } from 'vue'\nimport { objToStyle, pause } from '../common/util'\nimport { useTranslate } from '../composables/useTranslate'\nimport { searchProps } from './types'\n\nconst props = defineProps(searchProps)\nconst emit = defineEmits(['update:modelValue', 'change', 'clear', 'search', 'focus', 'blur', 'cancel'])\n\nconst { translate } = useTranslate('search')\n\nconst isFocused = ref<boolean>(false) // 是否聚焦中\nconst showInput = ref<boolean>(false) // 是否显示输入框 用于实现聚焦的hack\nconst str = ref('')\nconst showPlaceHolder = ref<boolean>(true)\nconst clearing = ref<boolean>(false)\n\nwatch(\n  () => props.modelValue,\n  (newValue) => {\n    str.value = newValue\n    if (newValue) {\n      showInput.value = true\n    }\n  },\n  { immediate: true }\n)\n\nwatch(\n  () => props.focus,\n  (newValue) => {\n    if (newValue) {\n      if (props.disabled) return\n      closeCover()\n    }\n  }\n)\n\nonMounted(() => {\n  if (props.focus) {\n    closeCover()\n  }\n})\n\nconst rootClass = computed(() => {\n  return `wd-search  ${props.light ? 'is-light' : ''}  ${props.hideCancel ? 'is-without-cancel' : ''} ${props.customClass}`\n})\n\nconst coverStyle = computed(() => {\n  const coverStyle: CSSProperties = {\n    display: str.value === '' && showPlaceHolder.value ? 'flex' : 'none'\n  }\n\n  return objToStyle(coverStyle)\n})\n\nasync function hackFocus(focus: boolean) {\n  showInput.value = focus\n  await pause()\n  isFocused.value = focus\n}\n\nasync function closeCover() {\n  if (props.disabled) return\n  await pause(100)\n  showPlaceHolder.value = false\n  hackFocus(true)\n}\n/**\n * @description input的input事件handle\n * @param value\n */\nfunction inputValue(event: any) {\n  str.value = event.detail.value\n  emit('update:modelValue', event.detail.value)\n  emit('change', {\n    value: event.detail.value\n  })\n}\n/**\n * @description 点击清空icon的handle\n */\nasync function clearSearch() {\n  str.value = ''\n  clearing.value = true\n  if (props.focusWhenClear) {\n    isFocused.value = false\n  }\n  await pause(100)\n  if (props.focusWhenClear) {\n    showPlaceHolder.value = false\n    hackFocus(true)\n  } else {\n    showPlaceHolder.value = true\n    hackFocus(false)\n  }\n  emit('change', {\n    value: ''\n  })\n  emit('update:modelValue', '')\n  emit('clear')\n}\n/**\n * @description 点击搜索按钮时的handle\n * @param value\n */\nfunction search({ detail: { value } }: any) {\n  // 组件触发search事件\n  emit('search', {\n    value\n  })\n}\n/**\n * @description 输入框聚焦时的handle\n */\nfunction searchFocus() {\n  if (clearing.value) {\n    clearing.value = false\n    return\n  }\n  showPlaceHolder.value = false\n  emit('focus', {\n    value: str.value\n  })\n}\n/**\n * @description 输入框失焦的handle\n */\nfunction searchBlur() {\n  if (clearing.value) return\n  // 组件触发blur事件\n  showPlaceHolder.value = !str.value\n  showInput.value = !showPlaceHolder.value\n  isFocused.value = false\n  emit('blur', {\n    value: str.value\n  })\n}\n/**\n * @description 点击取消搜索按钮的handle\n */\nfunction handleCancel() {\n  // 组件触发cancel事件\n  emit('cancel', {\n    value: str.value\n  })\n}\n</script>\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-search/wd-search.vue'\nwx.createComponent(Component)"], "names": ["useTranslate", "ref", "watch", "onMounted", "computed", "coverStyle", "objToStyle", "pause"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkDA,MAAA,SAAmB,MAAA;AAXnB,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,EAAA;AAEpB;;;;;AAUA,UAAM,QAAQ;AACd,UAAM,OAAO;AAEb,UAAM,EAAE,UAAA,IAAcA,cAAA,aAAa,QAAQ;AAErC,UAAA,YAAYC,kBAAa,KAAK;AAC9B,UAAA,YAAYA,kBAAa,KAAK;AAC9B,UAAA,MAAMA,kBAAI,EAAE;AACZ,UAAA,kBAAkBA,kBAAa,IAAI;AACnC,UAAA,WAAWA,kBAAa,KAAK;AAEnCC,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,aAAa;AACZ,YAAI,QAAQ;AACZ,YAAI,UAAU;AACZ,oBAAU,QAAQ;AAAA,QAAA;AAAA,MAEtB;AAAA,MACA,EAAE,WAAW,KAAK;AAAA,IACpB;AAEAA,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,aAAa;AACZ,YAAI,UAAU;AACZ,cAAI,MAAM;AAAU;AACT,qBAAA;AAAA,QAAA;AAAA,MACb;AAAA,IAEJ;AAEAC,kBAAAA,UAAU,MAAM;AACd,UAAI,MAAM,OAAO;AACJ,mBAAA;AAAA,MAAA;AAAA,IACb,CACD;AAEK,UAAA,YAAYC,cAAAA,SAAS,MAAM;AAC/B,aAAO,cAAc,MAAM,QAAQ,aAAa,EAAE,KAAK,MAAM,aAAa,sBAAsB,EAAE,IAAI,MAAM,WAAW;AAAA,IAAA,CACxH;AAEK,UAAA,aAAaA,cAAAA,SAAS,MAAM;AAChC,YAAMC,cAA4B;AAAA,QAChC,SAAS,IAAI,UAAU,MAAM,gBAAgB,QAAQ,SAAS;AAAA,MAChE;AAEA,aAAOC,cAAAA,WAAWD,WAAU;AAAA,IAAA,CAC7B;AAED,aAAe,UAAU,OAAgB;AAAA;AACvC,kBAAU,QAAQ;AAClB,cAAME,oBAAM;AACZ,kBAAU,QAAQ;AAAA,MAAA;AAAA;AAGpB,aAAe,aAAa;AAAA;AAC1B,YAAI,MAAM;AAAU;AACpB,cAAMA,cAAAA,MAAM,GAAG;AACf,wBAAgB,QAAQ;AACxB,kBAAU,IAAI;AAAA,MAAA;AAAA;AAMhB,aAAS,WAAW,OAAY;AAC1B,UAAA,QAAQ,MAAM,OAAO;AACpB,WAAA,qBAAqB,MAAM,OAAO,KAAK;AAC5C,WAAK,UAAU;AAAA,QACb,OAAO,MAAM,OAAO;AAAA,MAAA,CACrB;AAAA,IAAA;AAKH,aAAe,cAAc;AAAA;AAC3B,YAAI,QAAQ;AACZ,iBAAS,QAAQ;AACjB,YAAI,MAAM,gBAAgB;AACxB,oBAAU,QAAQ;AAAA,QAAA;AAEpB,cAAMA,cAAAA,MAAM,GAAG;AACf,YAAI,MAAM,gBAAgB;AACxB,0BAAgB,QAAQ;AACxB,oBAAU,IAAI;AAAA,QAAA,OACT;AACL,0BAAgB,QAAQ;AACxB,oBAAU,KAAK;AAAA,QAAA;AAEjB,aAAK,UAAU;AAAA,UACb,OAAO;AAAA,QAAA,CACR;AACD,aAAK,qBAAqB,EAAE;AAC5B,aAAK,OAAO;AAAA,MAAA;AAAA;AAMd,aAAS,OAAO,EAAE,QAAQ,EAAE,WAAgB;AAE1C,WAAK,UAAU;AAAA,QACb;AAAA,MAAA,CACD;AAAA,IAAA;AAKH,aAAS,cAAc;AACrB,UAAI,SAAS,OAAO;AAClB,iBAAS,QAAQ;AACjB;AAAA,MAAA;AAEF,sBAAgB,QAAQ;AACxB,WAAK,SAAS;AAAA,QACZ,OAAO,IAAI;AAAA,MAAA,CACZ;AAAA,IAAA;AAKH,aAAS,aAAa;AACpB,UAAI,SAAS;AAAO;AAEJ,sBAAA,QAAQ,CAAC,IAAI;AACnB,gBAAA,QAAQ,CAAC,gBAAgB;AACnC,gBAAU,QAAQ;AAClB,WAAK,QAAQ;AAAA,QACX,OAAO,IAAI;AAAA,MAAA,CACZ;AAAA,IAAA;AAKH,aAAS,eAAe;AAEtB,WAAK,UAAU;AAAA,QACb,OAAO,IAAI;AAAA,MAAA,CACZ;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClMH,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}