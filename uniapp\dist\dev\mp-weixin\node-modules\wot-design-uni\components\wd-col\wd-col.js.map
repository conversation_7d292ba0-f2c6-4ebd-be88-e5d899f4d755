{"version": 3, "file": "wd-col.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-col/wd-col.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1jb2wvd2QtY29sLnZ1ZQ"], "sourcesContent": ["<template>\n  <view :class=\"['wd-col', span && 'wd-col__' + span, offset && 'wd-col__offset-' + offset, customClass]\" :style=\"rootStyle\">\n    <!-- 每一列 -->\n    <slot />\n  </view>\n</template>\n<script lang=\"ts\">\nexport default {\n  name: 'wd-col',\n  options: {\n    addGlobalClass: true,\n    virtualHost: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport { computed, watch } from 'vue'\nimport { useParent } from '../composables/useParent'\nimport { ROW_KEY } from '../wd-row/types'\nimport { colProps } from './types'\nimport { isDef } from '../common/util'\n\nconst props = defineProps(colProps)\nconst { parent: row } = useParent(ROW_KEY)\n\nconst rootStyle = computed(() => {\n  const gutter = isDef(row) ? row.props.gutter || 0 : 0\n  const padding = `${gutter / 2}px`\n  const style = gutter > 0 ? `padding-left: ${padding}; padding-right: ${padding};background-clip: content-box;` : ''\n  return `${style}${props.customStyle}`\n})\n\nwatch([() => props.span, () => props.offset], () => {\n  check()\n})\n\nfunction check() {\n  const { span, offset } = props\n  if (span < 0 || offset < 0) {\n    console.error('[wot-design] warning(wd-col): attribute span/offset must be greater than or equal to 0')\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-col/wd-col.vue'\nwx.createComponent(Component)"], "names": ["useParent", "ROW_KEY", "computed", "isDef", "watch"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAOA,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,gBAAgB;AAAA,EAAA;AAEpB;;;;AAUA,UAAM,QAAQ;AACd,UAAM,EAAE,QAAQ,QAAQA,cAAAA,UAAUC,cAAAA,OAAO;AAEnC,UAAA,YAAYC,cAAAA,SAAS,MAAM;AAC/B,YAAM,SAASC,cAAM,MAAA,GAAG,IAAI,IAAI,MAAM,UAAU,IAAI;AAC9C,YAAA,UAAU,GAAG,SAAS,CAAC;AAC7B,YAAM,QAAQ,SAAS,IAAI,iBAAiB,OAAO,oBAAoB,OAAO,mCAAmC;AACjH,aAAO,GAAG,KAAK,GAAG,MAAM,WAAW;AAAA,IAAA,CACpC;AAEKC,kBAAA,MAAA,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,GAAG,MAAM;AAC5C,YAAA;AAAA,IAAA,CACP;AAED,aAAS,QAAQ;AACT,YAAA,EAAE,MAAM,OAAA,IAAW;AACrB,UAAA,OAAO,KAAK,SAAS,GAAG;AAC1B,gBAAQ,MAAM,wFAAwF;AAAA,MAAA;AAAA,IACxG;;;;;;;;;;;;ACzCF,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}