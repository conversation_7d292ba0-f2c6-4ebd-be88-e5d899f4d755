{"version": 3, "file": "wd-overlay.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-overlay/wd-overlay.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1vdmVybGF5L3dkLW92ZXJsYXkudnVl"], "sourcesContent": ["<template>\n  <wd-transition\n    :show=\"show\"\n    name=\"fade\"\n    custom-class=\"wd-overlay\"\n    :duration=\"duration\"\n    :custom-style=\"`z-index: ${zIndex}; ${customStyle}`\"\n    @click=\"handleClick\"\n    @touchmove.stop.prevent=\"lockScroll ? noop : ''\"\n  >\n    <slot></slot>\n  </wd-transition>\n</template>\n<script lang=\"ts\">\nexport default {\n  name: 'wd-overlay',\n  options: {\n    virtualHost: true,\n    addGlobalClass: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport wdTransition from '../wd-transition/wd-transition.vue'\nimport { overlayProps } from './types'\n\n\n\n\nconst props = defineProps(overlayProps)\n\nconst emit = defineEmits(['click'])\n\nfunction handleClick() {\n  emit('click')\n}\n\nfunction noop() {}\n\n\n\n\n</script>\n\n<style lang=\"scss\">\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-overlay/wd-overlay.vue'\nwx.createComponent(Component)"], "names": ["Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAyBA,MAAA,eAAyB,MAAA;AAXzB,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,EAAA;AAEpB;;;;;AAYA,UAAM,OAAO;AAEb,aAAS,cAAc;AACrB,WAAK,OAAO;AAAA,IAAA;AAGd,aAAS,OAAO;AAAA,IAAA;;;;;;;;;;;;;;;;ACtChB,GAAG,gBAAgBA,SAAS;", "x_google_ignoreList": [0]}