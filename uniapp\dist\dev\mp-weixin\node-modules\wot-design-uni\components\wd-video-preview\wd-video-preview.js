"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../../common/vendor.js");
if (!Math) {
  wdIcon();
}
const wdIcon = () => "../wd-icon/wd-icon.js";
const __default__ = {
  name: "wd-video-preview",
  options: {
    addGlobalClass: true,
    virtualHost: true,
    styleIsolation: "shared"
  }
};
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, __default__), {
  props: common_vendor.videoPreviewProps,
  setup(__props, { expose: __expose }) {
    const showPopup = common_vendor.ref(false);
    const previdewVideo = common_vendor.reactive({ url: "", poster: "", title: "" });
    function open(video) {
      showPopup.value = true;
      previdewVideo.url = video.url;
      previdewVideo.poster = video.poster;
      previdewVideo.title = video.title;
    }
    function close() {
      showPopup.value = false;
      common_vendor.nextTick$1(() => {
        handleClosed();
      });
    }
    function handleClosed() {
      previdewVideo.url = "";
      previdewVideo.poster = "";
      previdewVideo.title = "";
    }
    __expose({
      open,
      close
    });
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: showPopup.value
      }, showPopup.value ? common_vendor.e$1({
        b: previdewVideo.url
      }, previdewVideo.url ? {
        c: previdewVideo.poster,
        d: previdewVideo.title,
        e: previdewVideo.url
      } : {}, {
        f: common_vendor.o(() => {
        }),
        g: common_vendor.o(close),
        h: common_vendor.p({
          name: "close",
          ["custom-class"]: `wd-video-preview__close`
        }),
        i: common_vendor.n(`wd-video-preview ${_ctx.customClass}`),
        j: common_vendor.s(_ctx.customStyle),
        k: common_vendor.o(close)
      }) : {});
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-81c5af26"]]);
wx.createComponent(Component);
//# sourceMappingURL=wd-video-preview.js.map
