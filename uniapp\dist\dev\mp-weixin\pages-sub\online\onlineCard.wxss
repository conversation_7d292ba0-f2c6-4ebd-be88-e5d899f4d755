/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.wrap.data-v-75097847 {
  height: 100%;
}
.data-v-75097847 .wd-swipe-action {
  margin-top: 10px;
  background-color: #fff;
}
.list.data-v-75097847 {
  padding: 10px 10px;
  width: 100%;
  text-align: left;
  display: flex;
  justify-content: space-between;
}
.list .box.data-v-75097847 {
  width: 33%;
}
.list .box .field.data-v-75097847 {
  margin-bottom: 10px;
  line-height: 20px;
}
.action.data-v-75097847 {
  width: 60px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action .button.data-v-75097847 {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 100%;
  color: #fff;
}
.action .button.data-v-75097847:first-child {
  background-color: #fa4350;
}
.add.data-v-75097847,
.goTable.data-v-75097847 {
  height: 70rpx;
  width: 70rpx;
  text-align: center;
  line-height: 70rpx;
  background-color: #fff;
  border-radius: 50%;
  position: fixed;
  bottom: 80rpx;
  right: 30rpx;
  box-shadow: 0 0 5px 2px rgba(0, 0, 0, 0.1);
  color: #666;
}
.goTable.data-v-75097847 {
  bottom: 180rpx;
}