{"version": 3, "file": "index.js", "sources": ["../../../../../../src/plugin/uni-mini-router/utils/index.ts"], "sourcesContent": ["/*\r\n * @Author: weish<PERSON>\r\n * @Date: 2023-04-23 13:19:59\r\n * @LastEditTime: 2023-04-27 12:45:37\r\n * @LastEditors: weisheng\r\n * @Description:url工具\r\n * @FilePath: \\uni-mini-router\\src\\utils\\index.ts\r\n * 记得注释\r\n */\r\n/**\r\n * 获取url中的参数\r\n * @param path 完整路径\r\n * @returns\r\n */\r\nexport function getUrlParams(path: string) {\r\n  const params: Record<string, string> = {}\r\n  const pathArray: string[] = path.split('?') // 路径根据？拆分为2部分\r\n  let paramString: string = '' // 参数字符串\r\n  let paramArrary: string[] = [] // 参数数组\r\n  if (pathArray.length > 1) {\r\n    paramString = pathArray[1]\r\n  }\r\n  paramArrary = paramString.split('&')\r\n  for (let index = 0; index < paramArrary.length; index++) {\r\n    if (paramArrary[index].split('=').length === 2) {\r\n      params[paramArrary[index].split('=')[0]] = paramArrary[index].split('=')[1]\r\n    }\r\n  }\r\n  return params\r\n}\r\n\r\n/**\r\n * 设置参数\r\n * @param path 路径（无参数）\r\n * @param params （参数）\r\n * @returns\r\n */\r\nexport function setUrlParams(path: string, params: Record<string, string>) {\r\n  for (const key in params) {\r\n    if (path.indexOf('?') > -1) {\r\n      path = path + `&${key}=${params[key]}`\r\n    } else {\r\n      path = path + `?${key}=${params[key]}`\r\n    }\r\n  }\r\n  return path\r\n}\r\n\r\n/**\r\n * 全量替换url中的字符\r\n * @param str 原始字符串\r\n * @param find 要查找的字符串\r\n * @param replace 要替换的字符串\r\n * @returns\r\n */\r\nfunction replaceAll(str: string, find: string, replace: string) {\r\n  return str.replace(new RegExp(find, 'g'), replace)\r\n}\r\n\r\n/**\r\n * 去除拼接url产生的多余的/\r\n * @param url 目标路径\r\n */\r\nexport function beautifyUrl(url: string) {\r\n  url = replaceAll(url, '//', '/') // 先替换所有'//'为'/'\r\n  url = replaceAll(url, 'https:/', 'https://') // 再将https补全'//'\r\n  url = replaceAll(url, 'http:/', 'http://') // 再将http补全'//'\r\n  return url\r\n}\r\n/**\r\n * url查询参数序列化\r\n * @param query url查询参数\r\n * @returns\r\n */\r\nexport function queryStringify(query: Record<string, string>) {\r\n  const result: Record<string, string> = {}\r\n  if (query) {\r\n    for (const key in query) {\r\n      let value: any = query[key]\r\n      if (value === undefined) {\r\n        value = ''\r\n      }\r\n      result[key] = value\r\n    }\r\n  }\r\n  return result\r\n}\r\n\r\n/**\r\n * 判断query或params是否为空或者undefined\r\n * @param obj 待判断对象\r\n * @returns\r\n */\r\nexport function isEmptyObject(obj: undefined | null | Record<string, any>): boolean {\r\n  return obj === undefined || obj === null || Object.keys(obj).length === 0\r\n}\r\n"], "names": [], "mappings": ";AAcO,SAAS,aAAa,MAAc;AACzC,QAAM,SAAiC,CAAC;AAClC,QAAA,YAAsB,KAAK,MAAM,GAAG;AAC1C,MAAI,cAAsB;AAC1B,MAAI,cAAwB,CAAC;AACzB,MAAA,UAAU,SAAS,GAAG;AACxB,kBAAc,UAAU,CAAC;AAAA,EAAA;AAEb,gBAAA,YAAY,MAAM,GAAG;AACnC,WAAS,QAAQ,GAAG,QAAQ,YAAY,QAAQ,SAAS;AACvD,QAAI,YAAY,KAAK,EAAE,MAAM,GAAG,EAAE,WAAW,GAAG;AAC9C,aAAO,YAAY,KAAK,EAAE,MAAM,GAAG,EAAE,CAAC,CAAC,IAAI,YAAY,KAAK,EAAE,MAAM,GAAG,EAAE,CAAC;AAAA,IAAA;AAAA,EAC5E;AAEK,SAAA;AACT;AAQgB,SAAA,aAAa,MAAc,QAAgC;AACzE,aAAW,OAAO,QAAQ;AACxB,QAAI,KAAK,QAAQ,GAAG,IAAI,IAAI;AAC1B,aAAO,OAAO,IAAI,GAAG,IAAI,OAAO,GAAG,CAAC;AAAA,IAAA,OAC/B;AACL,aAAO,OAAO,IAAI,GAAG,IAAI,OAAO,GAAG,CAAC;AAAA,IAAA;AAAA,EACtC;AAEK,SAAA;AACT;AASA,SAAS,WAAW,KAAa,MAAc,SAAiB;AAC9D,SAAO,IAAI,QAAQ,IAAI,OAAO,MAAM,GAAG,GAAG,OAAO;AACnD;AAMO,SAAS,YAAY,KAAa;AACjC,QAAA,WAAW,KAAK,MAAM,GAAG;AACzB,QAAA,WAAW,KAAK,WAAW,UAAU;AACrC,QAAA,WAAW,KAAK,UAAU,SAAS;AAClC,SAAA;AACT;AAMO,SAAS,eAAe,OAA+B;AAC5D,QAAM,SAAiC,CAAC;AACxC,MAAI,OAAO;AACT,eAAW,OAAO,OAAO;AACnB,UAAA,QAAa,MAAM,GAAG;AAC1B,UAAI,UAAU,QAAW;AACf,gBAAA;AAAA,MAAA;AAEV,aAAO,GAAG,IAAI;AAAA,IAAA;AAAA,EAChB;AAEK,SAAA;AACT;AAOO,SAAS,cAAc,KAAsD;AAC3E,SAAA,QAAQ,UAAa,QAAQ,QAAQ,OAAO,KAAK,GAAG,EAAE,WAAW;AAC1E;;;;;;"}