{"version": 3, "file": "index.js", "sources": ["../../../../../../../../src/pages-work/components/echarts/map/JHeatMap/index.vue", "../../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvcGFnZXMtd29yay9jb21wb25lbnRzL2VjaGFydHMvbWFwL0pIZWF0TWFwL2luZGV4LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"content\">\r\n    <statusTip v-if=\"pageTips.show\" :status=\"pageTips.status\"></statusTip>\r\n    <!-- #ifdef APP-PLUS || H5 -->\r\n<!--    <EchartsMap-->\r\n<!--      v-else-->\r\n<!--      v-model:option=\"option\"-->\r\n<!--      v-model:map=\"mapObject\"-->\r\n<!--      v-model:echartId=\"echartId\"-->\r\n<!--    />-->\r\n    <!-- #endif -->\r\n    <!-- #ifdef APP-PLUS || H5 || MP-WEIXIN -->\r\n    <echartsUniapp v-else :option=\"option\" :mapName=\"mapName\" :mapData=\"mapDataJson\"></echartsUniapp>\r\n    <!-- #endif -->\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted } from 'vue'\r\nimport { echartProps } from '@/pages-work/components/echarts/props'\r\nimport EchartsMap from '../index.vue'\r\nimport echartsUniapp from \"../../index.vue\";\r\nimport { merge } from 'lodash-es';\r\nimport {\r\n  deepMerge,\r\n  handleTotalAndUnit,\r\n  disposeGridLayout,\r\n  getGeoCoordMap,\r\n} from '../../../common/echartUtil'\r\nimport useChartHook from '@/pages-work/components/hooks/useEchartMap'\r\n// 定义 props\r\nconst props = defineProps({\r\n  ...echartProps,\r\n})\r\n// 定义响应式数据\r\nconst option = ref({})\r\nconst chartOption = ref({\r\n  geo: {\r\n    map: '',\r\n    itemStyle: {},\r\n  },\r\n  tooltip: {\r\n    textStyle: {\r\n      color: '#fff',\r\n    },\r\n    padding: 5,\r\n    formatter: null,\r\n  },\r\n})\r\nlet [\r\n  { dataSource, reload, pageTips, config, mapDataJson,mapName, getAreaCode, city_point },\r\n  {\r\n    queryData,\r\n    registerMap,\r\n    setGeoAreaColor,\r\n    handleTotalAndUnitMap,\r\n    handleCommonOpt,\r\n    queryCityCenter,\r\n    getHeatMapData,\r\n  },\r\n] = useChartHook(props, initOption)\r\nconst echartId = ref('')\r\n// 计算属性\r\nconst mapObject = computed(() => ({ code: getAreaCode.value, data: mapDataJson.value }))\r\n// 初始化配置选项\r\nasync function initOption(data) {\r\n  let chartData = dataSource.value\r\n  let mapName = await registerMap()\r\n  try {\r\n    // 使用 registerMap 注册的地图名称\r\n    //地图配置\r\n    chartOption.value.tooltip = {\r\n      enterable: true,\r\n      transitionDuration: 1,\r\n      textStyle: {\r\n        color: '#000',\r\n        decoration: 'none',\r\n      },\r\n      trigger: 'item',\r\n      formatter: (params) => {\r\n        let value = params.value || 0\r\n        return `${params.name || '空'}:${value}`\r\n      },\r\n    }\r\n    //使用 registerMap 注册的地图名称\r\n    chartOption.value.geo.map = mapName;\r\n    //配置项修改\r\n    chartOption.value.series = [\r\n      {\r\n        name: '地图',\r\n        type: 'map',\r\n        map: mapName,\r\n        geoIndex: 0,\r\n        aspectScale: 0.75, //长宽比\r\n        showLegendSymbol: false, // 存在legend时显示\r\n        label: {\r\n          show: true,\r\n          color: '#000',\r\n        },\r\n        emphasis: {\r\n          show: true,\r\n          color: '#000',\r\n          itemStyle: {\r\n            areaColor: '#2B91B7',\r\n          },\r\n        },\r\n        roam: true,\r\n        itemStyle: {\r\n          areaColor: '#3B5077',\r\n          borderColor: '#3B5077',\r\n        },\r\n        animation: true,\r\n        data: chartData,\r\n        zlevel: 1,\r\n      },\r\n      {\r\n        name: '数据',\r\n        type: 'heatmap',\r\n        coordinateSystem: 'geo',\r\n        blurSize: config.commonOption?.heat?.blurSize || 20,\r\n        pointSize: config.commonOption?.heat?.pointSize || 15,\r\n        maxOpacity: config.commonOption?.heat?.maxOpacity || 1,\r\n        data: getHeatMapData(chartData),\r\n      },\r\n    ]\r\n    // 合并配置\r\n    if (props.config && props.config.option) {\r\n      merge(chartOption.value, props.config.option)\r\n      chartOption.value = setGeoAreaColor(chartOption.value, props.config)\r\n      chartOption.value = handleTotalAndUnitMap(\r\n        props.compName,\r\n        chartOption.value,\r\n        props.config,\r\n        chartData,\r\n      )\r\n      chartOption.value = handleCommonOpt(chartOption.value)\r\n    }\r\n\r\n    //如果视觉映射未设置最大值，就计算数据的最大值并赋值\r\n    if(chartOption.value?.visualMap?.max === 0 && chartData.length>0){\r\n      let maxValue = chartData.reduce((max, data) => Math.max(max, data.value), chartData[0].value);\r\n      chartOption.value.visualMap.max = maxValue;\r\n    }\r\n    if (chartOption.value?.visualMap?.top) {\r\n      chartOption.value.visualMap.top = 'auto';\r\n      chartOption.value.visualMap.bottom = '1%';\r\n    }\r\n    setTimeout(() => {\r\n      option.value = { ...chartOption.value }\r\n      pageTips.show = false\r\n      echartId.value = props.i\r\n    }, 300)\r\n    if (dataSource.value && dataSource.value.length === 0) {\r\n      pageTips.status = 1\r\n      pageTips.show = true\r\n    }\r\n  } catch (e) {\r\n    console.log('热力地图报错', e)\r\n  }\r\n}\r\n\r\n// 挂载时查询数据\r\nonMounted(async () => {\r\n  await queryCityCenter()\r\n  await queryData()\r\n})\r\n</script>\r\n\r\n<style>\r\n.content {\r\n  margin: 5px;\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-work/components/echarts/map/JHeatMap/index.vue'\nwx.createComponent(Component)"], "names": ["ref", "useChartHook", "computed", "mapName", "merge", "data", "onMounted", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBA,MAAM,gBAAgB,MAAW;;;;;AAUjC,UAAM,QAAQ;AAId,UAAM,SAASA,cAAG,IAAC,EAAE;AACrB,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB,KAAK;AAAA,QACH,KAAK;AAAA,QACL,WAAW,CAAE;AAAA,MACd;AAAA,MACD,SAAS;AAAA,QACP,WAAW;AAAA,UACT,OAAO;AAAA,QACR;AAAA,QACD,SAAS;AAAA,QACT,WAAW;AAAA,MACZ;AAAA,IACH,CAAC;AACD,QAAI;AAAA,MACF,EAAE,YAAY,QAAQ,UAAU,QAAQ,aAAY,SAAS,aAAa,WAAY;AAAA,MACtF;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,IACH,IAAIC,wCAAY,aAAC,OAAO,UAAU;AAClC,UAAM,WAAWD,cAAG,IAAC,EAAE;AAELE,kBAAAA,SAAS,OAAO,EAAE,MAAM,YAAY,OAAO,MAAM,YAAY,MAAK,EAAG;AAEvF,aAAe,WAAW,MAAM;AAAA;;AAC9B,YAAI,YAAY,WAAW;AAC3B,YAAIC,WAAU,MAAM,YAAa;AACjC,YAAI;AAGF,sBAAY,MAAM,UAAU;AAAA,YAC1B,WAAW;AAAA,YACX,oBAAoB;AAAA,YACpB,WAAW;AAAA,cACT,OAAO;AAAA,cACP,YAAY;AAAA,YACb;AAAA,YACD,SAAS;AAAA,YACT,WAAW,CAAC,WAAW;AACrB,kBAAI,QAAQ,OAAO,SAAS;AAC5B,qBAAO,GAAG,OAAO,QAAQ,GAAG,IAAI,KAAK;AAAA,YACtC;AAAA,UACF;AAED,sBAAY,MAAM,IAAI,MAAMA;AAE5B,sBAAY,MAAM,SAAS;AAAA,YACzB;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,KAAKA;AAAA,cACL,UAAU;AAAA,cACV,aAAa;AAAA;AAAA,cACb,kBAAkB;AAAA;AAAA,cAClB,OAAO;AAAA,gBACL,MAAM;AAAA,gBACN,OAAO;AAAA,cACR;AAAA,cACD,UAAU;AAAA,gBACR,MAAM;AAAA,gBACN,OAAO;AAAA,gBACP,WAAW;AAAA,kBACT,WAAW;AAAA,gBACZ;AAAA,cACF;AAAA,cACD,MAAM;AAAA,cACN,WAAW;AAAA,gBACT,WAAW;AAAA,gBACX,aAAa;AAAA,cACd;AAAA,cACD,WAAW;AAAA,cACX,MAAM;AAAA,cACN,QAAQ;AAAA,YACT;AAAA,YACD;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,kBAAkB;AAAA,cAClB,YAAU,kBAAO,iBAAP,mBAAqB,SAArB,mBAA2B,aAAY;AAAA,cACjD,aAAW,kBAAO,iBAAP,mBAAqB,SAArB,mBAA2B,cAAa;AAAA,cACnD,cAAY,kBAAO,iBAAP,mBAAqB,SAArB,mBAA2B,eAAc;AAAA,cACrD,MAAM,eAAe,SAAS;AAAA,YAC/B;AAAA,UACF;AAED,cAAI,MAAM,UAAU,MAAM,OAAO,QAAQ;AACvCC,0BAAAA,MAAM,YAAY,OAAO,MAAM,OAAO,MAAM;AAC5C,wBAAY,QAAQ,gBAAgB,YAAY,OAAO,MAAM,MAAM;AACnE,wBAAY,QAAQ;AAAA,cAClB,MAAM;AAAA,cACN,YAAY;AAAA,cACZ,MAAM;AAAA,cACN;AAAA,YACD;AACD,wBAAY,QAAQ,gBAAgB,YAAY,KAAK;AAAA,UACtD;AAGD,gBAAG,uBAAY,UAAZ,mBAAmB,cAAnB,mBAA8B,SAAQ,KAAK,UAAU,SAAO,GAAE;AAC/D,gBAAI,WAAW,UAAU,OAAO,CAAC,KAAKC,UAAS,KAAK,IAAI,KAAKA,MAAK,KAAK,GAAG,UAAU,CAAC,EAAE,KAAK;AAC5F,wBAAY,MAAM,UAAU,MAAM;AAAA,UACnC;AACD,eAAI,uBAAY,UAAZ,mBAAmB,cAAnB,mBAA8B,KAAK;AACrC,wBAAY,MAAM,UAAU,MAAM;AAClC,wBAAY,MAAM,UAAU,SAAS;AAAA,UACtC;AACD,qBAAW,MAAM;AACf,mBAAO,QAAQ,mBAAK,YAAY;AAChC,qBAAS,OAAO;AAChB,qBAAS,QAAQ,MAAM;AAAA,UACxB,GAAE,GAAG;AACN,cAAI,WAAW,SAAS,WAAW,MAAM,WAAW,GAAG;AACrD,qBAAS,SAAS;AAClB,qBAAS,OAAO;AAAA,UACjB;AAAA,QACF,SAAQ,GAAG;AACV,kBAAQ,IAAI,UAAU,CAAC;AAAA,QACxB;AAAA,MACH;AAAA;AAGAC,kBAAAA,UAAU,MAAY;AACpB,YAAM,gBAAiB;AACvB,YAAM,UAAW;AAAA,IACnB,EAAC;;;;;;;;;;;;;;;;;;ACpKD,GAAG,gBAAgBC,SAAS;"}