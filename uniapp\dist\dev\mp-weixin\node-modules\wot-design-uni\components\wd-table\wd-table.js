"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../../common/vendor.js");
if (!Math) {
  (wdSortButton + wdTableCol)();
}
const wdTableCol = () => "../wd-table-col/wd-table-col.js";
const wdSortButton = () => "../wd-sort-button/wd-sort-button.js";
const __default__ = {
  name: "wd-table",
  options: {
    addGlobalClass: true,
    virtualHost: true,
    styleIsolation: "shared"
  }
};
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, __default__), {
  props: common_vendor.tableProps,
  emits: ["sort-method", "row-click"],
  setup(__props, { emit: __emit }) {
    const { translate } = common_vendor.useTranslate("tableCol");
    const props = __props;
    const emit = __emit;
    const state = common_vendor.reactive({
      scrollLeft: 0
    });
    const { linkChildren, children } = common_vendor.useChildren(common_vendor.TABLE_KEY);
    linkChildren({ props, state, rowClick, getIsLastFixed, getFixedStyle });
    const indexUUID = common_vendor.uuid();
    const indexColumn = common_vendor.ref(__spreadValues({
      prop: indexUUID,
      label: translate("indexLabel"),
      width: "100rpx",
      sortable: false,
      fixed: false,
      align: "left"
    }, common_vendor.isObj(props.index) ? props.index : {}));
    const scroll = common_vendor.debounce(handleScroll, 100, { leading: false });
    const tableStyle = common_vendor.computed(() => {
      const style = {};
      if (common_vendor.isDef(props.height)) {
        style["max-height"] = common_vendor.addUnit(props.height);
      }
      return `${common_vendor.objToStyle(style)}${props.customStyle}`;
    });
    const realWidthStyle = common_vendor.computed(() => {
      const style = {
        display: "flex"
      };
      let width = "";
      children.forEach((child) => {
        width = width ? `${width} + ${common_vendor.addUnit(child.width)}` : common_vendor.addUnit(child.width);
      });
      style["width"] = `calc(${width})`;
      return common_vendor.objToStyle(style);
    });
    const bodyStyle = common_vendor.computed(() => {
      const style = {};
      if (common_vendor.isDef(props.height)) {
        style["height"] = common_vendor.isDef(props.rowHeight) ? `calc(${props.data.length} * ${common_vendor.addUnit(props.rowHeight)})` : `calc(${props.data.length} * 50px)`;
      }
      return `${common_vendor.objToStyle(style)}`;
    });
    function getIsLastFixed(column) {
      let isLastFixed = false;
      if (column.fixed && common_vendor.isDef(children)) {
        const columns = children.filter((child) => {
          return child.fixed;
        });
        if (columns.length && columns[columns.length - 1].prop === column.prop) {
          isLastFixed = true;
        }
      }
      return isLastFixed;
    }
    function getCellStyle(columnIndex) {
      let style = {};
      if (common_vendor.isDef(children[columnIndex].width)) {
        style["width"] = common_vendor.addUnit(children[columnIndex].width);
      }
      if (children[columnIndex].fixed) {
        style = getFixedStyle(columnIndex, style);
      }
      return common_vendor.objToStyle(style);
    }
    function getFixedStyle(columnIndex, style) {
      if (columnIndex > 0) {
        let left = "";
        children.forEach((column, index) => {
          if (index < columnIndex) {
            left = left ? `${left} + ${common_vendor.addUnit(column.width)}` : common_vendor.addUnit(column.width);
          }
        });
        style["left"] = `calc(${left})`;
      } else {
        style["left"] = 0;
      }
      return style;
    }
    function handleSortChange(value, index) {
      children[index].$.exposed.sortDirection.value = value;
      children.forEach((col, i) => {
        if (index != i) {
          col.$.exposed.sortDirection.value = 0;
        }
      });
      const column = {
        // 列对应字段
        prop: children[index].prop,
        // 列对应字段标题
        label: children[index].label,
        // 列宽度
        width: children[index].width,
        // 是否开启列排序
        sortable: children[index].sortable,
        // 列的对齐方式，可选值left,center,right
        align: children[index].align,
        // 列的排序方向
        sortDirection: value,
        // 是否i固定列
        fixed: children[index].fixed
      };
      emit("sort-method", column);
    }
    function handleScroll(event) {
      state.scrollLeft = event.detail.scrollLeft;
    }
    function rowClick(index) {
      emit("row-click", { rowIndex: index });
    }
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: _ctx.fixedHeader
      }, _ctx.fixedHeader ? common_vendor.e$1({
        b: _ctx.showHeader
      }, _ctx.showHeader ? {
        c: common_vendor.f(common_vendor.unref(children), (column, index, i0) => {
          return common_vendor.e$1({
            a: column.sortable
          }, column.sortable ? {
            b: common_vendor.o(({
              value
            }) => handleSortChange(value, index), index),
            c: "d423803e-0-" + i0,
            d: common_vendor.o(($event) => column.$.exposed.sortDirection.value = $event, index),
            e: common_vendor.p({
              ["allow-reset"]: true,
              line: false,
              title: column.label,
              modelValue: column.$.exposed.sortDirection.value
            })
          } : {
            f: common_vendor.t(column.label),
            g: common_vendor.n(`wd-table__value ${_ctx.ellipsis ? "is-ellipsis" : ""}`)
          }, {
            h: common_vendor.n(`wd-table__cell ${_ctx.border ? "is-border" : ""} ${column.fixed ? "is-fixed" : ""} ${_ctx.stripe ? "is-stripe" : ""} is-${column.align} ${getIsLastFixed(column) && state.scrollLeft ? "is-shadow" : ""}`),
            i: common_vendor.s(getCellStyle(index)),
            j: index
          });
        }),
        d: common_vendor.s(realWidthStyle.value),
        e: state.scrollLeft,
        f: common_vendor.o(
          //@ts-ignore
          (...args) => common_vendor.unref(scroll) && common_vendor.unref(scroll)(...args)
        )
      } : {}, {
        g: _ctx.index !== false
      }, _ctx.index !== false ? {
        h: common_vendor.w(({
          index
        }, s0, i0) => {
          return {
            a: common_vendor.t(index + 1),
            b: i0,
            c: s0
          };
        }, {
          name: "value",
          path: "h",
          vueId: "d423803e-1"
        }),
        i: common_vendor.p({
          prop: indexColumn.value.prop,
          label: indexColumn.value.label,
          width: indexColumn.value.width,
          sortable: indexColumn.value.sortable,
          fixed: indexColumn.value.fixed,
          align: indexColumn.value.align
        })
      } : {}, {
        j: common_vendor.s(realWidthStyle.value),
        k: common_vendor.s(bodyStyle.value),
        l: common_vendor.o(
          //@ts-ignore
          (...args) => common_vendor.unref(scroll) && common_vendor.unref(scroll)(...args)
        ),
        m: state.scrollLeft
      }) : common_vendor.e$1({
        n: _ctx.showHeader
      }, _ctx.showHeader ? {
        o: common_vendor.f(common_vendor.unref(children), (column, index, i0) => {
          return common_vendor.e$1({
            a: column.sortable
          }, column.sortable ? {
            b: common_vendor.o(({
              value
            }) => handleSortChange(value, index), index),
            c: "d423803e-2-" + i0,
            d: common_vendor.o(($event) => column.$.exposed.sortDirection.value = $event, index),
            e: common_vendor.p({
              ["allow-reset"]: true,
              line: false,
              title: column.label,
              modelValue: column.$.exposed.sortDirection.value
            })
          } : {
            f: common_vendor.t(column.label),
            g: common_vendor.n(`wd-table__value ${_ctx.ellipsis ? "is-ellipsis" : ""}`)
          }, {
            h: index,
            i: common_vendor.n(`wd-table__cell ${_ctx.border ? "is-border" : ""} ${column.fixed ? "is-fixed" : ""} ${_ctx.stripe ? "is-stripe" : ""} is-${column.align} ${getIsLastFixed(column) && state.scrollLeft ? "is-shadow" : ""}`),
            j: common_vendor.s(getCellStyle(index))
          });
        })
      } : {}, {
        p: _ctx.index !== false
      }, _ctx.index !== false ? {
        q: common_vendor.w(({
          index
        }, s0, i0) => {
          return {
            a: common_vendor.t(index + 1),
            b: i0,
            c: s0
          };
        }, {
          name: "value",
          path: "q",
          vueId: "d423803e-3"
        }),
        r: common_vendor.p({
          prop: indexColumn.value.prop,
          label: indexColumn.value.label,
          width: indexColumn.value.width,
          sortable: indexColumn.value.sortable,
          fixed: indexColumn.value.fixed,
          align: indexColumn.value.align
        })
      } : {}, {
        s: common_vendor.s(bodyStyle.value),
        t: common_vendor.s(realWidthStyle.value),
        v: common_vendor.o(
          //@ts-ignore
          (...args) => common_vendor.unref(scroll) && common_vendor.unref(scroll)(...args)
        ),
        w: state.scrollLeft
      }), {
        x: common_vendor.n(`wd-table ${_ctx.border ? "is-border" : ""} ${_ctx.customClass}`),
        y: common_vendor.s(tableStyle.value)
      });
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d423803e"]]);
wx.createComponent(Component);
//# sourceMappingURL=wd-table.js.map
