{"version": 3, "file": "personPage.js", "sources": ["../../../../../src/pages-message/personPage/personPage.vue", "../../../../../uniPage:/cGFnZXMtbWVzc2FnZVxwZXJzb25QYWdlXHBlcnNvblBhZ2UudnVl"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n  <PageLayout\r\n    :backRouteName=\"backRouteName\"\r\n    navLeftText=\"\"\r\n    :navBgTransparent=\"true\"\r\n    :navFixed=\"true\"\r\n  >\r\n    <view class=\"wrap\">\r\n      <view class=\"topArea\"></view>\r\n      <view class=\"middleArea bg-white\">\r\n        <wd-img\r\n          custom-class=\"avatar\"\r\n          width=\"75px\"\r\n          height=\"75px\"\r\n          radius=\"50%\"\r\n          :src=\"data.avatar\"\r\n        ></wd-img>\r\n        <wd-text custom-class=\"realname center\" :text=\"data.realname\"></wd-text>\r\n        <wd-button custom-class=\"\" @click=\"handleGo\">发消息</wd-button>\r\n      </view>\r\n      <view class=\"bottomArea bg-white\">\r\n        <view class=\"list\">\r\n          <view class=\"iconBox\">\r\n            <view class=\"cuIcon-mobile text-gray-4\"></view>\r\n          </view>\r\n          <view class=\"content\">\r\n            <view class=\"label text-gray-4\">手机</view>\r\n            <view class=\"value text-blue-5\">{{ data.phone || '未填写' }}</view>\r\n          </view>\r\n        </view>\r\n        <view class=\"list\">\r\n          <view class=\"iconBox\">\r\n            <view class=\"cuIcon-mail text-gray-4\"></view>\r\n          </view>\r\n          <view class=\"content\">\r\n            <view class=\"label text-gray-4\">手机</view>\r\n            <view class=\"value text-blue-5\">{{ data.email || '未填写' }}</view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { ref } from 'vue'\r\nimport { useUserStore } from '@/store/user'\r\nimport { useParamsStore } from '@/store/page-params'\r\nimport { useRouter } from '@/plugin/uni-mini-router'\r\n//\r\nconst userStore = useUserStore()\r\nconst paramsStore = useParamsStore()\r\nconst router = useRouter()\r\nconst params = paramsStore.getPageParams('personPage')\r\nconst backRouteName = ref(params.backRouteName) ?? {}\r\nlet data = params.data ?? {}\r\n\r\n\r\nconst handleGo = () => {\r\n  var parmas = {\r\n    fromAvatar: data.avatar,\r\n    fromUserName: data.realname || data.username,\r\n    msgFrom: userStore.userInfo.userid,\r\n    msgTo: data.id,\r\n    type: 'friend',\r\n  }\r\n  paramsStore.setPageParams('chat', { back: 'personPage', data: parmas })\r\n  router.push({ name: 'chat' })\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n//\r\n.topArea {\r\n  background: linear-gradient(45deg, #0081ff, #1cbbb4);\r\n  min-height: 170px;\r\n}\r\n.middleArea {\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding-bottom: 30px;\r\n  .avatar {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 50%;\r\n    transform: translate(-50%, -50%);\r\n  }\r\n  .realname {\r\n    padding-top: 50px;\r\n    font-size: 18px;\r\n    font-weight: 700;\r\n    color: #333;\r\n    margin-bottom: 20px;\r\n  }\r\n}\r\n.bottomArea {\r\n  .list {\r\n    border-top: 1px solid #f1f1f1;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 10px;\r\n    .iconBox {\r\n      font-size: 28px;\r\n      margin-right: 10px;\r\n    }\r\n    .label {\r\n      font-size: 15px;\r\n      margin-bottom: 4px;\r\n    }\r\n  }\r\n  .value {\r\n    color: #3665cb;\r\n  }\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-message/personPage/personPage.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "useParamsStore", "useRouter", "ref"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAsDA,UAAM,YAAYA,WAAAA,aAAa;AAC/B,UAAM,cAAcC,iBAAAA,eAAe;AACnC,UAAM,SAASC,gCAAAA,UAAU;AACnB,UAAA,SAAS,YAAY,cAAc,YAAY;AACrD,UAAM,iBAAgBC,mBAAA,IAAI,OAAO,aAAa,MAAxBA,YAA6B,CAAC;AAChD,QAAA,QAAO,YAAO,SAAP,YAAe,CAAC;AAG3B,UAAM,WAAW,MAAM;AACrB,UAAI,SAAS;AAAA,QACX,YAAY,KAAK;AAAA,QACjB,cAAc,KAAK,YAAY,KAAK;AAAA,QACpC,SAAS,UAAU,SAAS;AAAA,QAC5B,OAAO,KAAK;AAAA,QACZ,MAAM;AAAA,MACR;AACA,kBAAY,cAAc,QAAQ,EAAE,MAAM,cAAc,MAAM,QAAQ;AACtE,aAAO,KAAK,EAAE,MAAM,OAAA,CAAQ;AAAA,IAC9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvEA,GAAG,WAAW,eAAe;"}