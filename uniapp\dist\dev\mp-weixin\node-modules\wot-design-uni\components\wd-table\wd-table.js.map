{"version": 3, "file": "wd-table.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-table/wd-table.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC10YWJsZS93ZC10YWJsZS52dWU"], "sourcesContent": ["<template>\n  <view :class=\"`wd-table ${border ? 'is-border' : ''} ${customClass}`\" :style=\"tableStyle\">\n    <template v-if=\"fixedHeader\">\n      <scroll-view\n        :enable-flex=\"true\"\n        :throttle=\"false\"\n        :scrollLeft=\"state.scrollLeft\"\n        :scroll-x=\"true\"\n        class=\"wd-table__header\"\n        @scroll=\"scroll\"\n        v-if=\"showHeader\"\n      >\n        <view id=\"table-header\" class=\"wd-table__content wd-table__content--header\" :style=\"realWidthStyle\">\n          <view\n            :class=\"`wd-table__cell ${border ? 'is-border' : ''} ${column.fixed ? 'is-fixed' : ''} ${stripe ? 'is-stripe' : ''} is-${column.align} ${\n              getIsLastFixed(column) && state.scrollLeft ? 'is-shadow' : ''\n            }`\"\n            :style=\"getCellStyle(index)\"\n            v-for=\"(column, index) in children\"\n            :key=\"index\"\n          >\n            <wd-sort-button\n              v-model=\"column.$.exposed!.sortDirection.value\"\n              allow-reset\n              :line=\"false\"\n              :title=\"column.label\"\n              @change=\"({ value }) => handleSortChange(value, index)\"\n              v-if=\"column.sortable\"\n            />\n            <text v-else :class=\"`wd-table__value ${ellipsis ? 'is-ellipsis' : ''}`\">{{ column.label }}</text>\n          </view>\n        </view>\n      </scroll-view>\n      <scroll-view\n        class=\"wd-table__body\"\n        :style=\"bodyStyle\"\n        :enable-flex=\"true\"\n        :throttle=\"false\"\n        :scroll-x=\"true\"\n        @scroll=\"scroll\"\n        :scrollLeft=\"state.scrollLeft\"\n      >\n        <view id=\"table-body\" class=\"wd-table__content\" :style=\"realWidthStyle\">\n          <wd-table-col\n            v-if=\"index !== false\"\n            :prop=\"indexColumn.prop\"\n            :label=\"indexColumn.label\"\n            :width=\"indexColumn.width\"\n            :sortable=\"indexColumn.sortable\"\n            :fixed=\"indexColumn.fixed\"\n            :align=\"indexColumn.align\"\n          >\n            <template #value=\"{ index }\">\n              <text>{{ index + 1 }}</text>\n            </template>\n          </wd-table-col>\n          <slot></slot>\n        </view>\n      </scroll-view>\n    </template>\n    <!-- 非固定表头时使用单个scroll-view -->\n    <template v-else>\n      <scroll-view class=\"wd-table__wrapper\" :enable-flex=\"true\" :throttle=\"false\" :scroll-x=\"true\" @scroll=\"scroll\" :scrollLeft=\"state.scrollLeft\">\n        <view class=\"wd-table__inner\" :style=\"realWidthStyle\">\n          <!-- 表头部分 -->\n          <view v-if=\"showHeader\" class=\"wd-table__header-row\">\n            <view\n              v-for=\"(column, index) in children\"\n              :key=\"index\"\n              :class=\"`wd-table__cell ${border ? 'is-border' : ''} ${column.fixed ? 'is-fixed' : ''} ${stripe ? 'is-stripe' : ''} is-${\n                column.align\n              } ${getIsLastFixed(column) && state.scrollLeft ? 'is-shadow' : ''}`\"\n              :style=\"getCellStyle(index)\"\n            >\n              <wd-sort-button\n                v-if=\"column.sortable\"\n                v-model=\"column.$.exposed!.sortDirection.value\"\n                allow-reset\n                :line=\"false\"\n                :title=\"column.label\"\n                @change=\"({ value }) => handleSortChange(value, index)\"\n              />\n              <text v-else :class=\"`wd-table__value ${ellipsis ? 'is-ellipsis' : ''}`\">{{ column.label }}</text>\n            </view>\n          </view>\n\n          <!-- 表格内容部分 -->\n          <view class=\"wd-table__content\" :style=\"bodyStyle\">\n            <wd-table-col\n              v-if=\"index !== false\"\n              :prop=\"indexColumn.prop\"\n              :label=\"indexColumn.label\"\n              :width=\"indexColumn.width\"\n              :sortable=\"indexColumn.sortable\"\n              :fixed=\"indexColumn.fixed\"\n              :align=\"indexColumn.align\"\n            >\n              <template #value=\"{ index }\">\n                <text>{{ index + 1 }}</text>\n              </template>\n            </wd-table-col>\n            <slot></slot>\n          </view>\n        </view>\n      </scroll-view>\n    </template>\n  </view>\n</template>\n\n<script lang=\"ts\">\nexport default {\n  name: 'wd-table',\n  options: {\n    addGlobalClass: true,\n    virtualHost: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport wdTableCol from '../wd-table-col/wd-table-col.vue'\nimport wdSortButton from '../wd-sort-button/wd-sort-button.vue'\nimport { type CSSProperties, computed, reactive, ref } from 'vue'\nimport { addUnit, debounce, isDef, isObj, objToStyle, uuid } from '../common/util'\nimport type { SortDirection, TableColumn, TableColumnInstance, TableColumnProps } from '../wd-table-col/types'\nimport { TABLE_KEY, tableProps, type TableProvide } from './types'\nimport WdTableCol from '../wd-table-col/wd-table-col.vue'\nimport { useTranslate } from '../composables/useTranslate'\nimport { useChildren } from '../composables/useChildren'\n\nconst { translate } = useTranslate('tableCol')\n\nconst props = defineProps(tableProps)\nconst emit = defineEmits(['sort-method', 'row-click'])\n\nconst state = reactive({\n  scrollLeft: 0\n})\n\nconst { linkChildren, children } = useChildren<TableColumnInstance, TableProvide>(TABLE_KEY)\n\nlinkChildren({ props, state, rowClick, getIsLastFixed, getFixedStyle })\n\nconst indexUUID = uuid()\nconst indexColumn = ref<TableColumnProps>({\n  prop: indexUUID,\n  label: translate('indexLabel'),\n  width: '100rpx',\n  sortable: false,\n  fixed: false,\n  align: 'left',\n  ...(isObj(props.index) ? props.index : {})\n})\n\nconst scroll = debounce(handleScroll, 100, { leading: false }) // 滚动事件\n\n/**\n * 容器样式\n */\nconst tableStyle = computed(() => {\n  const style: CSSProperties = {}\n  if (isDef(props.height)) {\n    style['max-height'] = addUnit(props.height)\n  }\n  return `${objToStyle(style)}${props.customStyle}`\n})\n\nconst realWidthStyle = computed(() => {\n  const style: CSSProperties = {\n    display: 'flex'\n  }\n  let width: string | number = ''\n  children.forEach((child) => {\n    width = width ? `${width} + ${addUnit(child.width)}` : addUnit(child.width)\n  })\n  style['width'] = `calc(${width})`\n  return objToStyle(style)\n})\n\nconst bodyStyle = computed(() => {\n  const style: CSSProperties = {}\n  if (isDef(props.height)) {\n    style['height'] = isDef(props.rowHeight) ? `calc(${props.data.length} * ${addUnit(props.rowHeight)})` : `calc(${props.data.length} * 50px)`\n  }\n  return `${objToStyle(style)}`\n})\n\n/**\n * 是否最后一个固定元素\n * @param column 列数据\n */\nfunction getIsLastFixed(column: { fixed: boolean; prop: string }) {\n  let isLastFixed: boolean = false\n  if (column.fixed && isDef(children)) {\n    const columns = children.filter((child) => {\n      return child.fixed\n    })\n    if (columns.length && columns[columns.length - 1].prop === column.prop) {\n      isLastFixed = true\n    }\n  }\n  return isLastFixed\n}\n\n/**\n * 表头单元格样式\n */\nfunction getCellStyle(columnIndex: number) {\n  let style: CSSProperties = {}\n  if (isDef(children[columnIndex].width)) {\n    style['width'] = addUnit(children[columnIndex].width)\n  }\n  if (children[columnIndex].fixed) {\n    style = getFixedStyle(columnIndex, style)\n  }\n  return objToStyle(style)\n}\n\n/**\n * 获取固定列样式\n * @param columnIndex\n */\nfunction getFixedStyle(columnIndex: number, style: CSSProperties) {\n  if (columnIndex > 0) {\n    let left: string | number = ''\n    children.forEach((column, index) => {\n      if (index < columnIndex) {\n        left = left ? `${left} + ${addUnit(column.width)}` : addUnit(column.width)\n      }\n    })\n    style['left'] = `calc(${left})`\n  } else {\n    style['left'] = 0\n  }\n  return style\n}\n\n/**\n * 排序\n * @param value\n * @param index\n */\nfunction handleSortChange(value: SortDirection, index: number) {\n  children[index].$.exposed!.sortDirection.value = value\n  children.forEach((col, i) => {\n    if (index != i) {\n      col.$.exposed!.sortDirection.value = 0\n    }\n  })\n  const column: TableColumn = {\n    // 列对应字段\n    prop: children[index].prop,\n    // 列对应字段标题\n    label: children[index].label,\n    // 列宽度\n    width: children[index].width,\n    // 是否开启列排序\n    sortable: children[index].sortable,\n    // 列的对齐方式，可选值left,center,right\n    align: children[index].align,\n    // 列的排序方向\n    sortDirection: value,\n    // 是否i固定列\n    fixed: children[index].fixed\n  }\n  emit('sort-method', column)\n}\n\n/**\n * 滚动事件\n */\nfunction handleScroll(event: any) {\n  state.scrollLeft = event.detail.scrollLeft\n}\n\nfunction rowClick(index: number) {\n  emit('row-click', { rowIndex: index })\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-table/wd-table.vue'\nwx.createComponent(Component)"], "names": ["useTranslate", "reactive", "useChildren", "TABLE_KEY", "uuid", "ref", "isObj", "debounce", "computed", "isDef", "addUnit", "objToStyle"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAyHA,MAAA,aAAuB,MAAA;AACvB,MAAA,eAAyB,MAAA;AAZzB,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,gBAAgB;AAAA,EAAA;AAEpB;;;;;AAcA,UAAM,EAAE,UAAA,IAAcA,cAAA,aAAa,UAAU;AAE7C,UAAM,QAAQ;AACd,UAAM,OAAO;AAEb,UAAM,QAAQC,cAAAA,SAAS;AAAA,MACrB,YAAY;AAAA,IAAA,CACb;AAED,UAAM,EAAE,cAAc,aAAaC,cAAAA,YAA+CC,cAAAA,SAAS;AAE3F,iBAAa,EAAE,OAAO,OAAO,UAAU,gBAAgB,eAAe;AAEtE,UAAM,YAAYC,cAAAA,KAAK;AACvB,UAAM,cAAcC,cAAAA,IAAsB;AAAA,MACxC,MAAM;AAAA,MACN,OAAO,UAAU,YAAY;AAAA,MAC7B,OAAO;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,MACP,OAAO;AAAA,OACHC,cAAM,MAAA,MAAM,KAAK,IAAI,MAAM,QAAQ,CAAA,EACxC;AAED,UAAM,SAASC,cAAAA,SAAS,cAAc,KAAK,EAAE,SAAS,OAAO;AAKvD,UAAA,aAAaC,cAAAA,SAAS,MAAM;AAChC,YAAM,QAAuB,CAAC;AAC1B,UAAAC,cAAA,MAAM,MAAM,MAAM,GAAG;AACvB,cAAM,YAAY,IAAIC,sBAAQ,MAAM,MAAM;AAAA,MAAA;AAE5C,aAAO,GAAGC,cAAAA,WAAW,KAAK,CAAC,GAAG,MAAM,WAAW;AAAA,IAAA,CAChD;AAEK,UAAA,iBAAiBH,cAAAA,SAAS,MAAM;AACpC,YAAM,QAAuB;AAAA,QAC3B,SAAS;AAAA,MACX;AACA,UAAI,QAAyB;AACpB,eAAA,QAAQ,CAAC,UAAU;AAClB,gBAAA,QAAQ,GAAG,KAAK,MAAME,cAAA,QAAQ,MAAM,KAAK,CAAC,KAAKA,sBAAQ,MAAM,KAAK;AAAA,MAAA,CAC3E;AACK,YAAA,OAAO,IAAI,QAAQ,KAAK;AAC9B,aAAOC,cAAAA,WAAW,KAAK;AAAA,IAAA,CACxB;AAEK,UAAA,YAAYH,cAAAA,SAAS,MAAM;AAC/B,YAAM,QAAuB,CAAC;AAC1B,UAAAC,cAAA,MAAM,MAAM,MAAM,GAAG;AACvB,cAAM,QAAQ,IAAIA,cAAAA,MAAM,MAAM,SAAS,IAAI,QAAQ,MAAM,KAAK,MAAM,MAAMC,cAAAA,QAAQ,MAAM,SAAS,CAAC,MAAM,QAAQ,MAAM,KAAK,MAAM;AAAA,MAAA;AAE5H,aAAA,GAAGC,cAAAA,WAAW,KAAK,CAAC;AAAA,IAAA,CAC5B;AAMD,aAAS,eAAe,QAA0C;AAChE,UAAI,cAAuB;AAC3B,UAAI,OAAO,SAASF,cAAM,MAAA,QAAQ,GAAG;AACnC,cAAM,UAAU,SAAS,OAAO,CAAC,UAAU;AACzC,iBAAO,MAAM;AAAA,QAAA,CACd;AACG,YAAA,QAAQ,UAAU,QAAQ,QAAQ,SAAS,CAAC,EAAE,SAAS,OAAO,MAAM;AACxD,wBAAA;AAAA,QAAA;AAAA,MAChB;AAEK,aAAA;AAAA,IAAA;AAMT,aAAS,aAAa,aAAqB;AACzC,UAAI,QAAuB,CAAC;AAC5B,UAAIA,cAAM,MAAA,SAAS,WAAW,EAAE,KAAK,GAAG;AACtC,cAAM,OAAO,IAAIC,cAAAA,QAAQ,SAAS,WAAW,EAAE,KAAK;AAAA,MAAA;AAElD,UAAA,SAAS,WAAW,EAAE,OAAO;AACvB,gBAAA,cAAc,aAAa,KAAK;AAAA,MAAA;AAE1C,aAAOC,cAAAA,WAAW,KAAK;AAAA,IAAA;AAOhB,aAAA,cAAc,aAAqB,OAAsB;AAChE,UAAI,cAAc,GAAG;AACnB,YAAI,OAAwB;AACnB,iBAAA,QAAQ,CAAC,QAAQ,UAAU;AAClC,cAAI,QAAQ,aAAa;AAChB,mBAAA,OAAO,GAAG,IAAI,MAAMD,cAAA,QAAQ,OAAO,KAAK,CAAC,KAAKA,sBAAQ,OAAO,KAAK;AAAA,UAAA;AAAA,QAC3E,CACD;AACK,cAAA,MAAM,IAAI,QAAQ,IAAI;AAAA,MAAA,OACvB;AACL,cAAM,MAAM,IAAI;AAAA,MAAA;AAEX,aAAA;AAAA,IAAA;AAQA,aAAA,iBAAiB,OAAsB,OAAe;AAC7D,eAAS,KAAK,EAAE,EAAE,QAAS,cAAc,QAAQ;AACxC,eAAA,QAAQ,CAAC,KAAK,MAAM;AAC3B,YAAI,SAAS,GAAG;AACV,cAAA,EAAE,QAAS,cAAc,QAAQ;AAAA,QAAA;AAAA,MACvC,CACD;AACD,YAAM,SAAsB;AAAA;AAAA,QAE1B,MAAM,SAAS,KAAK,EAAE;AAAA;AAAA,QAEtB,OAAO,SAAS,KAAK,EAAE;AAAA;AAAA,QAEvB,OAAO,SAAS,KAAK,EAAE;AAAA;AAAA,QAEvB,UAAU,SAAS,KAAK,EAAE;AAAA;AAAA,QAE1B,OAAO,SAAS,KAAK,EAAE;AAAA;AAAA,QAEvB,eAAe;AAAA;AAAA,QAEf,OAAO,SAAS,KAAK,EAAE;AAAA,MACzB;AACA,WAAK,eAAe,MAAM;AAAA,IAAA;AAM5B,aAAS,aAAa,OAAY;AAC1B,YAAA,aAAa,MAAM,OAAO;AAAA,IAAA;AAGlC,aAAS,SAAS,OAAe;AAC/B,WAAK,aAAa,EAAE,UAAU,MAAA,CAAO;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpRvC,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}