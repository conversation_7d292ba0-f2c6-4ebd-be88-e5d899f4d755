"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
const utils_platform = require("../../utils/platform.js");
if (!Array) {
  const _easycom_wd_tab2 = common_vendor.resolveComponent("wd-tab");
  const _easycom_wd_tabs2 = common_vendor.resolveComponent("wd-tabs");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_tab2 + _easycom_wd_tabs2 + _easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_wd_tab = () => "../../node-modules/wot-design-uni/components/wd-tab/wd-tab.js";
const _easycom_wd_tabs = () => "../../node-modules/wot-design-uni/components/wd-tabs/wd-tabs.js";
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  (chatList + addressBookList + _easycom_wd_tab + _easycom_wd_tabs + _easycom_PageLayout)();
}
const chatList = () => "./components/chatList.js";
const addressBookList = () => "./components/addressBookList.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "message",
  options: {
    // apply-shared‌：当前页面样式会影响到子组件样式.(小程序)
    // shared‌：当前页面样式影响到子组件，子组件样式也会影响到当前页面.(小程序)
    styleIsolation: "‌shared‌"
  }
}), {
  __name: "message",
  setup(__props) {
    const globalData = getApp().globalData;
    const { systemInfo, navHeight } = globalData;
    const { statusBarHeight } = systemInfo;
    console.log("systemInfo:::", systemInfo);
    const tabList = common_vendor.ref([
      { key: "1", title: "消息" },
      { key: "2", title: "通讯录" }
    ]);
    const tabActive = common_vendor.ref("1");
    const getClass = () => {
      return `${utils_platform.platform} ${utils_platform.isMp ? "mp" : ""}`;
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.f(tabList.value, (item, index, i0) => {
          return common_vendor.e$1({
            a: item.key === "1"
          }, item.key === "1" ? {
            b: "3556fc04-4-" + i0 + "," + ("3556fc04-3-" + i0)
          } : {}, {
            c: item.key === "2"
          }, item.key === "2" ? {
            d: "3556fc04-5-" + i0 + "," + ("3556fc04-3-" + i0)
          } : {}, {
            e: "3556fc04-3-" + i0 + ",3556fc04-2",
            f: common_vendor.p({
              title: item.title,
              name: item.key
            }),
            g: index
          });
        }),
        b: common_vendor.o(($event) => tabActive.value = $event),
        c: common_vendor.p({
          customClass: getClass(),
          modelValue: tabActive.value
        }),
        d: `${common_vendor.unref(statusBarHeight) + common_vendor.unref(navHeight)}px`,
        e: `${common_vendor.unref(statusBarHeight)}px`,
        f: common_vendor.p({
          navbarShow: false
        })
      };
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-3556fc04"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=message.js.map
