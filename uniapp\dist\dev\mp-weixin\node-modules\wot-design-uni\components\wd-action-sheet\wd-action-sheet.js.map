{"version": 3, "file": "wd-action-sheet.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-action-sheet/wd-action-sheet.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1hY3Rpb24tc2hlZXQvd2QtYWN0aW9uLXNoZWV0LnZ1ZQ"], "sourcesContent": ["<template>\n  <view>\n    <wd-popup\n      custom-class=\"wd-action-sheet__popup\"\n      :custom-style=\"`${(actions && actions.length) || (panels && panels.length) ? 'background: transparent;' : ''}`\"\n      v-model=\"showPopup\"\n      :duration=\"duration\"\n      position=\"bottom\"\n      :close-on-click-modal=\"closeOnClickModal\"\n      :safe-area-inset-bottom=\"safeAreaInsetBottom\"\n      :lazy-render=\"lazyRender\"\n      @enter=\"handleOpen\"\n      @close=\"close\"\n      @after-enter=\"handleOpened\"\n      @after-leave=\"handleClosed\"\n      @click-modal=\"handleClickModal\"\n      :z-index=\"zIndex\"\n    >\n      <view\n        :class=\"`wd-action-sheet ${customClass}`\"\n        :style=\"`${\n          (actions && actions.length) || (panels && panels.length)\n            ? 'margin: 0 10px calc(var(--window-bottom) + 10px) 10px; border-radius: 16px;'\n            : 'margin-bottom: var(--window-bottom);'\n        } ${customStyle}`\"\n      >\n        <view v-if=\"title\" :class=\"`wd-action-sheet__header ${customHeaderClass}`\">\n          {{ title }}\n          <wd-icon custom-class=\"wd-action-sheet__close\" name=\"add\" @click=\"close\" />\n        </view>\n        <view class=\"wd-action-sheet__actions\" v-if=\"actions && actions.length\">\n          <button\n            v-for=\"(action, rowIndex) in actions\"\n            :key=\"rowIndex\"\n            :class=\"`wd-action-sheet__action ${action.disabled ? 'wd-action-sheet__action--disabled' : ''}  ${\n              action.loading ? 'wd-action-sheet__action--loading' : ''\n            }`\"\n            :style=\"`color: ${action.color}`\"\n            @click=\"select(rowIndex, 'action')\"\n          >\n            <wd-loading custom-class=\"`wd-action-sheet__action-loading\" v-if=\"action.loading\" />\n            <view v-else class=\"wd-action-sheet__name\">{{ action.name }}</view>\n            <view v-if=\"!action.loading && action.subname\" class=\"wd-action-sheet__subname\">{{ action.subname }}</view>\n          </button>\n        </view>\n        <view v-if=\"formatPanels && formatPanels.length\">\n          <view v-for=\"(panel, rowIndex) in formatPanels\" :key=\"rowIndex\" class=\"wd-action-sheet__panels\">\n            <view class=\"wd-action-sheet__panels-content\">\n              <view v-for=\"(col, colIndex) in panel\" :key=\"colIndex\" class=\"wd-action-sheet__panel\" @click=\"select(rowIndex, 'panels', colIndex)\">\n                <image class=\"wd-action-sheet__panel-img\" :src=\"(col as any).iconUrl\" />\n                <view class=\"wd-action-sheet__panel-title\">{{ (col as any).title }}</view>\n              </view>\n            </view>\n          </view>\n        </view>\n        <slot />\n        <button v-if=\"cancelText\" class=\"wd-action-sheet__cancel\" @click=\"handleCancel\">{{ cancelText }}</button>\n      </view>\n    </wd-popup>\n  </view>\n</template>\n<script lang=\"ts\">\nexport default {\n  name: 'wd-action-sheet',\n  options: {\n    addGlobalClass: true,\n    virtualHost: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport wdPopup from '../wd-popup/wd-popup.vue'\nimport wdIcon from '../wd-icon/wd-icon.vue'\nimport wdLoading from '../wd-loading/wd-loading.vue'\nimport { watch, ref } from 'vue'\nimport { actionSheetProps, type Panel } from './types'\nimport { isArray } from '../common/util'\n\nconst props = defineProps(actionSheetProps)\nconst emit = defineEmits(['select', 'click-modal', 'cancel', 'closed', 'close', 'open', 'opened', 'update:modelValue'])\n\nconst formatPanels = ref<Array<Panel> | Array<Panel[]>>([])\n\nconst showPopup = ref<boolean>(false)\n\nwatch(() => props.panels, computedValue, { deep: true, immediate: true })\n\nwatch(\n  () => props.modelValue,\n  (newValue) => {\n    showPopup.value = newValue\n  },\n  { deep: true, immediate: true }\n)\n\nfunction isPanelArray() {\n  return props.panels.length && !isArray(props.panels[0])\n}\nfunction computedValue() {\n  formatPanels.value = isPanelArray() ? [props.panels as Panel[]] : (props.panels as Panel[][])\n}\n\nfunction select(rowIndex: number, type: 'action' | 'panels', colIndex?: number) {\n  if (type === 'action') {\n    if (props.actions[rowIndex].disabled || props.actions[rowIndex].loading) {\n      return\n    }\n    emit('select', {\n      item: props.actions[rowIndex],\n      index: rowIndex\n    })\n  } else if (isPanelArray()) {\n    emit('select', {\n      item: props.panels[Number(colIndex)],\n      index: colIndex\n    })\n  } else {\n    emit('select', {\n      item: (props.panels as Panel[][])[rowIndex][Number(colIndex)],\n      rowIndex,\n      colIndex\n    })\n  }\n  if (props.closeOnClickAction) {\n    close()\n  }\n}\nfunction handleClickModal() {\n  emit('click-modal')\n}\nfunction handleCancel() {\n  emit('cancel')\n  close()\n}\nfunction close() {\n  emit('update:modelValue', false)\n  emit('close')\n}\nfunction handleOpen() {\n  emit('open')\n}\nfunction handleOpened() {\n  emit('opened')\n}\nfunction handleClosed() {\n  emit('closed')\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-action-sheet/wd-action-sheet.vue'\nwx.createComponent(Component)"], "names": ["ref", "watch", "isArray"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAyEA,MAAA,UAAoB,MAAA;AACpB,MAAA,SAAmB,MAAA;AACnB,MAAA,YAAsB,MAAA;AAbtB,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,gBAAgB;AAAA,EAAA;AAEpB;;;;;AAWA,UAAM,QAAQ;AACd,UAAM,OAAO;AAEP,UAAA,eAAeA,cAAmC,IAAA,EAAE;AAEpD,UAAA,YAAYA,kBAAa,KAAK;AAE9BC,wBAAA,MAAM,MAAM,QAAQ,eAAe,EAAE,MAAM,MAAM,WAAW,MAAM;AAExEA,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,aAAa;AACZ,kBAAU,QAAQ;AAAA,MACpB;AAAA,MACA,EAAE,MAAM,MAAM,WAAW,KAAK;AAAA,IAChC;AAEA,aAAS,eAAe;AACf,aAAA,MAAM,OAAO,UAAU,CAACC,sBAAQ,MAAM,OAAO,CAAC,CAAC;AAAA,IAAA;AAExD,aAAS,gBAAgB;AACvB,mBAAa,QAAQ,aAAa,IAAI,CAAC,MAAM,MAAiB,IAAK,MAAM;AAAA,IAAA;AAGlE,aAAA,OAAO,UAAkB,MAA2B,UAAmB;AAC9E,UAAI,SAAS,UAAU;AACjB,YAAA,MAAM,QAAQ,QAAQ,EAAE,YAAY,MAAM,QAAQ,QAAQ,EAAE,SAAS;AACvE;AAAA,QAAA;AAEF,aAAK,UAAU;AAAA,UACb,MAAM,MAAM,QAAQ,QAAQ;AAAA,UAC5B,OAAO;AAAA,QAAA,CACR;AAAA,MAAA,WACQ,gBAAgB;AACzB,aAAK,UAAU;AAAA,UACb,MAAM,MAAM,OAAO,OAAO,QAAQ,CAAC;AAAA,UACnC,OAAO;AAAA,QAAA,CACR;AAAA,MAAA,OACI;AACL,aAAK,UAAU;AAAA,UACb,MAAO,MAAM,OAAqB,QAAQ,EAAE,OAAO,QAAQ,CAAC;AAAA,UAC5D;AAAA,UACA;AAAA,QAAA,CACD;AAAA,MAAA;AAEH,UAAI,MAAM,oBAAoB;AACtB,cAAA;AAAA,MAAA;AAAA,IACR;AAEF,aAAS,mBAAmB;AAC1B,WAAK,aAAa;AAAA,IAAA;AAEpB,aAAS,eAAe;AACtB,WAAK,QAAQ;AACP,YAAA;AAAA,IAAA;AAER,aAAS,QAAQ;AACf,WAAK,qBAAqB,KAAK;AAC/B,WAAK,OAAO;AAAA,IAAA;AAEd,aAAS,aAAa;AACpB,WAAK,MAAM;AAAA,IAAA;AAEb,aAAS,eAAe;AACtB,WAAK,QAAQ;AAAA,IAAA;AAEf,aAAS,eAAe;AACtB,WAAK,QAAQ;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClJf,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}