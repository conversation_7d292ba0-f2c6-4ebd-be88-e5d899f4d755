"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_http = require("../../utils/http.js");
if (!Array) {
  const _easycom_wd_text2 = common_vendor.resolveComponent("wd-text");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_text2 + _easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_wd_text = () => "../../node-modules/wot-design-uni/components/wd-text/wd-text.js";
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  (_easycom_wd_text + _easycom_PageLayout)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "annotationDetail",
  setup(__props) {
    const annotation = common_vendor.reactive({
      id: "",
      titile: "",
      startTime: "",
      sender: "",
      msgContent: "",
      anntId: "",
      sendTime: ""
    });
    const goodNumber = common_vendor.ref(null);
    const flg = common_vendor.ref(true);
    const init = (option) => {
      const annItem = JSON.parse(decodeURIComponent(option.item));
      console.log("ann", annItem);
      Object.assign(annotation, annItem);
      readOk();
    };
    const readOk = () => {
      let param = { anntId: annotation.anntId };
      utils_http.http.put("/sys/sysAnnouncementSend/editByAnntIdAndUserId", param);
    };
    const numberPlus = () => {
      if (flg.value) {
        goodNumber.value++;
        flg.value = false;
      } else {
        goodNumber.value--;
        if (goodNumber.value == 0) {
          goodNumber.value = null;
        }
        flg.value = true;
      }
    };
    common_vendor.onLoad((option) => {
      init(option);
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          ["custom-class"]: "title font-size-20px",
          text: annotation.titile
        }),
        b: common_vendor.p({
          ["custom-class"]: "sender mr-8px",
          text: annotation.sender
        }),
        c: common_vendor.p({
          text: annotation.sendTime
        }),
        d: annotation.msgContent,
        e: common_vendor.o(numberPlus),
        f: common_vendor.p({
          ["custom-class"]: "cIcon cuIcon-attentionfill mr-10px",
          text: "10"
        }),
        g: common_vendor.o(numberPlus),
        h: common_vendor.p({
          text: "20"
        }),
        i: common_vendor.p({
          navTitle: "详情",
          backRouteName: "annotationList"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-7d1ab4e9"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=annotationDetail.js.map
