"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../../../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "wd-index-bar",
  props: common_vendor.indexBarProps,
  setup(__props) {
    const props = __props;
    const indexBarId = common_vendor.ref(`indexBar${common_vendor.uuid()}`);
    const { proxy } = common_vendor.getCurrentInstance();
    const state = common_vendor.reactive({
      activeIndex: null
    });
    const { linkChildren, children } = common_vendor.useChildren(common_vendor.indexBarInjectionKey);
    linkChildren({ props, anchorState: state });
    common_vendor.watch(
      () => children,
      (newValue) => {
        if (!newValue.length) {
          state.activeIndex = null;
          return;
        }
        if (!common_vendor.isDef(state.activeIndex) || !newValue.find((item) => item.index === state.activeIndex)) {
          state.activeIndex = newValue[0].index;
        }
      },
      { deep: true, immediate: true }
    );
    const scrollState = common_vendor.reactive({
      scrollTop: 0,
      // 即将滚动到的位置
      prevScrollTop: 0,
      // 上次记录的位置
      // 滚动距离
      touching: false
    });
    let offsetTop = 0;
    let sidebarInfo = {
      // 侧边栏距离顶部的高度
      offsetTop: 0,
      // 高度固定24px
      indexHeight: 24
    };
    function init() {
      setTimeout(() => {
        Promise.all([
          common_vendor.getRect(`#${indexBarId.value}`, false, proxy),
          common_vendor.getRect(".wd-index-bar__sidebar", false, proxy),
          common_vendor.getRect(".wd-index-bar__index", false, proxy)
        ]).then(([bar, sidebar, index]) => {
          offsetTop = bar.top;
          sidebarInfo.offsetTop = sidebar.top;
          sidebarInfo.indexHeight = index.height;
        });
      }, 100);
    }
    common_vendor.onMounted(() => {
      init();
    });
    function hanleScroll(scrollEvent) {
      if (scrollState.touching) {
        return;
      }
      const { detail } = scrollEvent;
      const scrolltop = Math.floor(detail.scrollTop);
      const anchor = children.find((item, index) => {
        if (!common_vendor.isDef(children[index + 1]))
          return true;
        if (item.$.exposed.top.value - offsetTop <= scrolltop && children[index + 1].$.exposed.top.value - offsetTop > scrolltop)
          return true;
        return false;
      });
      if (common_vendor.isDef(anchor) && state.activeIndex !== anchor.index) {
        state.activeIndex = anchor.index;
      }
      scrollState.prevScrollTop = scrolltop;
    }
    function getAnchorByPageY(pageY) {
      const y = pageY - sidebarInfo.offsetTop;
      let idx = Math.floor(y / sidebarInfo.indexHeight);
      if (idx < 0)
        idx = 0;
      else if (idx > children.length - 1)
        idx = children.length - 1;
      return children[idx];
    }
    function handleTouchStart() {
      scrollState.touching = true;
    }
    function handleTouchMove(e) {
      const clientY = e.touches[0].pageY;
      if (state.activeIndex === getAnchorByPageY(clientY).index) {
        return;
      }
      state.activeIndex = getAnchorByPageY(clientY).index;
      setScrollTop(getAnchorByPageY(clientY).$.exposed.top.value - offsetTop);
    }
    function handleTouchEnd(e) {
      return __async(this, null, function* () {
        const clientY = e.changedTouches[0].pageY;
        state.activeIndex = getAnchorByPageY(clientY).index;
        setScrollTop(getAnchorByPageY(clientY).$.exposed.top.value - offsetTop);
        yield common_vendor.pause();
        scrollState.touching = false;
      });
    }
    function setScrollTop(top) {
      if (scrollState.scrollTop === top) {
        scrollState.scrollTop = scrollState.prevScrollTop;
        common_vendor.nextTick$1(() => {
          scrollState.scrollTop = top;
        });
      } else {
        scrollState.scrollTop = top;
      }
    }
    return (_ctx, _cache) => {
      return {
        a: scrollState.scrollTop,
        b: common_vendor.o(hanleScroll),
        c: common_vendor.f(common_vendor.unref(children), (item, k0, i0) => {
          return {
            a: common_vendor.t(item.index),
            b: item.index === state.activeIndex ? 1 : "",
            c: item.index
          };
        }),
        d: common_vendor.o(handleTouchStart),
        e: common_vendor.o(handleTouchMove),
        f: common_vendor.o(handleTouchEnd),
        g: common_vendor.o(handleTouchEnd),
        h: indexBarId.value
      };
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-2955bcb3"]]);
wx.createComponent(Component);
//# sourceMappingURL=wd-index-bar.js.map
