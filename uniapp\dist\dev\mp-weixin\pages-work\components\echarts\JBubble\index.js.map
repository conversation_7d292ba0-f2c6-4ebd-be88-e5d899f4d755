{"version": 3, "file": "index.js", "sources": ["../../../../../../../src/pages-work/components/echarts/JBubble/index.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvcGFnZXMtd29yay9jb21wb25lbnRzL2VjaGFydHMvSkJ1YmJsZS9pbmRleC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"content\">\r\n    <statusTip v-if=\"pageTips.show\" :status=\"pageTips.status\"></statusTip>\r\n\t<echartsUniapp v-else :option=\"option\"></echartsUniapp>\r\n  </view>\r\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { echartProps } from '@/pages-work/components/echarts/props';\r\nimport {deepMerge, handleTotalAndUnit, disposeGridLayout, getRandomColor} from '../../common/echartUtil';\r\nimport {isNumber, isObject} from '@/utils/is';\r\nimport useChartHook from '@/pages-work/components/hooks/useEchart';\r\nimport { deepClone } from '@/uni_modules/da-tree/utils';\r\nimport echartsUniapp from '@/pages-work/components/echarts/index.vue';\r\nimport statusTip from '@/pages-work/components/statusTip.vue';\r\nimport {merge} from \"lodash-es\";\r\n//组件传参\r\nconst props = defineProps({\r\n\t...echartProps\r\n})\r\n\r\n//最终图表配置项\r\nconst option = ref({});\r\nlet chartOption = {\r\n    title: {\r\n        show: true,\r\n    },\r\n    legend: {\r\n        show: true,\r\n        data: [],\r\n        bottom: '5%',\r\n    },\r\n    xAxis: {\r\n        type: 'category',\r\n    },\r\n    yAxis: {\r\n        type: 'value',\r\n    },\r\n    series: []\r\n}\r\n//图表数据查询\r\nlet [{ dataSource, reload, pageTips, config }, { queryData }] = useChartHook(\r\n  props,\r\n  initOption\r\n)\r\n\r\n\r\n//初始化图表配置项\r\nfunction initOption(data) {\r\n  let chartData: any = dataSource.value\r\n  if (typeof chartData === 'string') {\r\n    chartData = JSON.parse(chartData)\r\n  }\r\n  if (chartData && chartData.length > 0) {\r\n      //1.将{name，type，value}转换成dataset\r\n      let configOption = config.option;\r\n      let dataset = getDataset(chartData);\r\n      chartOption.series = [];\r\n      let series = configOption.series&&configOption.series.length>0?JSON.parse(JSON.stringify(configOption.series[0])):{};\r\n      dataset.dimensions.forEach((name, index) => {\r\n          let hasCustom = configOption.customColor && configOption.customColor.length>0 && configOption.customColor[index];\r\n          let color = hasCustom ? configOption.customColor[index].color : getRandomColor(index);\r\n          let edgeColor = isObject(color) ? `rgb(${color.r},${color.g},${color.b})` : color;\r\n          chartOption.series.push({\r\n              type: 'scatter',\r\n              name,\r\n              data:dataset.source[index],\r\n              symbolSize: 30,\r\n              ...series,\r\n              itemStyle: {\r\n                  shadowBlur: 10,\r\n                  shadowColor: 'rgba(25, 100, 150, 0.5)',\r\n                  shadowOffsetY: 5,\r\n                  color: {\r\n                      type: 'radial',\r\n                      x: 0.4,\r\n                      y: 0.3,\r\n                      r: 1,\r\n                      colorStops: [\r\n                          {\r\n                              offset: 0,\r\n                              color: 'rgb(255, 255, 255)', // 0% 处的颜色\r\n                          },\r\n                          {\r\n                              offset: 1,\r\n                              color: edgeColor, // 100% 处的颜色\r\n                          },\r\n                      ],\r\n                  },\r\n              },\r\n          });\r\n      });\r\n\r\n      chartOption.legend.data = chartOption.series.map((item) => item.name);\r\n\r\n      if(config.option.xAxis && config.option.xAxis.type){\r\n          chartOption.yAxis['type'] = config.option.xAxis['type'] =='value'?'category':'value';\r\n      }\r\n    // 合并配置\r\n    if (props.config && config.option) {\r\n      merge(chartOption, config.option)\r\n      chartOption = handleTotalAndUnit(props.compName, chartOption, config, chartData)\r\n      chartOption = disposeGridLayout(props.compName, chartOption, config, chartData)\r\n\t\t  option.value = deepClone(chartOption)\r\n\t\t  pageTips.show = false\r\n    }\r\n  } else {\r\n    pageTips.status = 1\r\n    pageTips.show = true\r\n  }\r\n}\r\n\r\nfunction getDataset(chartData) {\r\n    let dataObj = { dimensions: [], source: [] };\r\n    let dataList = [];\r\n    //获取系列\r\n    let typeArr = new Set(chartData.map((item) => item['type']));\r\n    let dimensions = [ ...typeArr];\r\n    typeArr.forEach(type=>{\r\n        let arr = chartData.filter((item) => item.type == type).map(item=>{\r\n            return [item.name,item.value]\r\n        });\r\n        dataList.push(arr)\r\n    })\r\n    dataObj.dimensions = dimensions;\r\n    dataObj.source = dataList;\r\n    return dataObj;\r\n}\r\nonMounted(()=>{\r\n\tqueryData();\r\n})\r\n\r\n</script>\r\n<style>\r\n.content {\r\n  padding: 10px;\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-work/components/echarts/JBubble/index.vue'\nwx.createComponent(Component)"], "names": ["ref", "useChartHook", "getRandomColor", "isObject", "merge", "handleTotalAndUnit", "disposeGridLayout", "deepClone", "onMounted", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaA,MAAA,gBAA0B,MAAA;AAC1B,MAAA,YAAsB,MAAA;;;;;AAGtB,UAAM,QAAQ;AAKR,UAAA,SAASA,cAAI,IAAA,EAAE;AACrB,QAAI,cAAc;AAAA,MACd,OAAO;AAAA,QACH,MAAM;AAAA,MACV;AAAA,MACA,QAAQ;AAAA,QACJ,MAAM;AAAA,QACN,MAAM,CAAC;AAAA,QACP,QAAQ;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACH,MAAM;AAAA,MACV;AAAA,MACA,OAAO;AAAA,QACH,MAAM;AAAA,MACV;AAAA,MACA,QAAQ,CAAA;AAAA,IACZ;AAEI,QAAA,CAAC,EAAE,YAAY,QAAQ,UAAU,UAAU,EAAE,UAAW,CAAA,IAAIC,qCAAA;AAAA,MAC9D;AAAA,MACA;AAAA,IACF;AAIA,aAAS,WAAW,MAAM;AACxB,UAAI,YAAiB,WAAW;AAC5B,UAAA,OAAO,cAAc,UAAU;AACrB,oBAAA,KAAK,MAAM,SAAS;AAAA,MAAA;AAE9B,UAAA,aAAa,UAAU,SAAS,GAAG;AAEnC,YAAI,eAAe,OAAO;AACtB,YAAA,UAAU,WAAW,SAAS;AAClC,oBAAY,SAAS,CAAC;AACtB,YAAI,SAAS,aAAa,UAAQ,aAAa,OAAO,SAAO,IAAE,KAAK,MAAM,KAAK,UAAU,aAAa,OAAO,CAAC,CAAC,CAAC,IAAE,CAAC;AACnH,gBAAQ,WAAW,QAAQ,CAAC,MAAM,UAAU;AACpC,cAAA,YAAY,aAAa,eAAe,aAAa,YAAY,SAAO,KAAK,aAAa,YAAY,KAAK;AAC3G,cAAA,QAAQ,YAAY,aAAa,YAAY,KAAK,EAAE,QAAQC,sDAAe,KAAK;AACpF,cAAI,YAAYC,SAAAA,SAAS,KAAK,IAAI,OAAO,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM;AAC5E,sBAAY,OAAO,KAAK;AAAA,YACpB,MAAM;AAAA,YACN;AAAA,YACA,MAAK,QAAQ,OAAO,KAAK;AAAA,YACzB,YAAY;AAAA,aACT,SALiB;AAAA,YAMpB,WAAW;AAAA,cACP,YAAY;AAAA,cACZ,aAAa;AAAA,cACb,eAAe;AAAA,cACf,OAAO;AAAA,gBACH,MAAM;AAAA,gBACN,GAAG;AAAA,gBACH,GAAG;AAAA,gBACH,GAAG;AAAA,gBACH,YAAY;AAAA,kBACR;AAAA,oBACI,QAAQ;AAAA,oBACR,OAAO;AAAA;AAAA,kBACX;AAAA,kBACA;AAAA,oBACI,QAAQ;AAAA,oBACR,OAAO;AAAA;AAAA,kBAAA;AAAA,gBACX;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ,EACH;AAAA,QAAA,CACJ;AAEW,oBAAA,OAAO,OAAO,YAAY,OAAO,IAAI,CAAC,SAAS,KAAK,IAAI;AAEpE,YAAG,OAAO,OAAO,SAAS,OAAO,OAAO,MAAM,MAAK;AACnC,sBAAA,MAAM,MAAM,IAAI,OAAO,OAAO,MAAM,MAAM,KAAI,UAAQ,aAAW;AAAA,QAAA;AAG/E,YAAA,MAAM,UAAU,OAAO,QAAQ;AAC3BC,8BAAA,aAAa,OAAO,MAAM;AAChC,wBAAcC,uCAAmB,mBAAA,MAAM,UAAU,aAAa,QAAQ,SAAS;AAC/E,wBAAcC,uCAAkB,kBAAA,MAAM,UAAU,WAA8B;AACzE,iBAAA,QAAQC,mCAAU,WAAW;AACpC,mBAAS,OAAO;AAAA,QAAA;AAAA,MAChB,OACK;AACL,iBAAS,SAAS;AAClB,iBAAS,OAAO;AAAA,MAAA;AAAA,IAClB;AAGF,aAAS,WAAW,WAAW;AAC3B,UAAI,UAAU,EAAE,YAAY,CAAA,GAAI,QAAQ,CAAA,EAAG;AAC3C,UAAI,WAAW,CAAC;AAEZ,UAAA,UAAU,IAAI,IAAI,UAAU,IAAI,CAAC,SAAS,KAAK,MAAM,CAAC,CAAC;AACvD,UAAA,aAAa,CAAE,GAAG,OAAO;AAC7B,cAAQ,QAAQ,CAAM,SAAA;AACd,YAAA,MAAM,UAAU,OAAO,CAAC,SAAS,KAAK,QAAQ,IAAI,EAAE,IAAI,CAAM,SAAA;AAC9D,iBAAO,CAAC,KAAK,MAAK,KAAK,KAAK;AAAA,QAAA,CAC/B;AACD,iBAAS,KAAK,GAAG;AAAA,MAAA,CACpB;AACD,cAAQ,aAAa;AACrB,cAAQ,SAAS;AACV,aAAA;AAAA,IAAA;AAEXC,kBAAAA,UAAU,MAAI;AACH,gBAAA;AAAA,IAAA,CACV;;;;;;;;;;;;;;;;ACjID,GAAG,gBAAgBC,SAAS;"}