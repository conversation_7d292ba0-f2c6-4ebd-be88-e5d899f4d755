"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../../../../common/vendor.js");
const pagesWork_components_echarts_props = require("../../props.js");
const pagesWork_components_common_echartUtil = require("../../../common/echartUtil.js");
const pagesWork_components_hooks_useEchartMap = require("../../../hooks/useEchartMap.js");
if (!Array) {
  const _component_statusTip = common_vendor.resolveComponent("statusTip");
  _component_statusTip();
}
if (!Math) {
  echartsUniapp();
}
const echartsUniapp = () => "../../index.js";
const _sfc_main = {
  __name: "index",
  props: __spreadValues({}, pagesWork_components_echarts_props.echartProps),
  setup(__props) {
    const props = __props;
    const option = common_vendor.ref({});
    const chartOption = common_vendor.ref({
      geo: {
        map: "",
        itemStyle: {}
      },
      tooltip: {
        textStyle: {
          color: "#fff"
        },
        padding: 5,
        formatter: null
      }
    });
    let [
      { dataSource, reload, pageTips, config, mapDataJson, mapName, getAreaCode, city_point },
      { queryData, registerMap, setGeoAreaColor, handleTotalAndUnitMap, handleCommonOpt, queryCityCenter }
    ] = pagesWork_components_hooks_useEchartMap.useChartHook(props, initOption);
    const echartId = common_vendor.ref("");
    common_vendor.computed(() => ({ code: getAreaCode.value, data: mapDataJson.value }));
    let geoCoordMap = {};
    function initOption(data) {
      return __async(this, null, function* () {
        var _a, _b, _c;
        let chartData = dataSource.value;
        let mapName2 = yield registerMap();
        try {
          geoCoordMap = pagesWork_components_common_echartUtil.getGeoCoordMap(mapDataJson.value);
          chartOption.value.geo.map = mapName2;
          let barSize = ((_a = config == null ? void 0 : config.commonOption) == null ? void 0 : _a.barSize) || 17;
          let barColor = ((_b = config == null ? void 0 : config.commonOption) == null ? void 0 : _b.barColor) || "#F8E71C";
          let barColor2 = ((_c = config == null ? void 0 : config.commonOption) == null ? void 0 : _c.barColor2) || "#F8E71C";
          chartOption.value.series = [
            {
              geoIndex: 0,
              // coordinateSystem: 'geo',
              showLegendSymbol: false,
              type: "map",
              roam: true,
              label: {
                show: false,
                color: "#ffffff"
              },
              emphasis: {
                color: "white",
                show: false,
                itemStyle: {
                  areaColor: "#FA8C16",
                  borderWidth: 0,
                  color: "green"
                }
              },
              itemStyle: {
                borderColor: "#2980b9",
                borderWidth: 1,
                areaColor: "#12235c"
              },
              map: mapName2,
              // 使用
              animation: true,
              data: chartData
            },
            {
              type: "lines",
              zlevel: 5,
              geoIndex: 0,
              effect: {
                show: false,
                symbolSize: 5
                // 图标大小
              },
              lineStyle: {
                width: barSize,
                // 尾迹线条宽度
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 1,
                  y2: 0,
                  colorStops: [
                    {
                      offset: 0,
                      color: barColor
                      // 0% 处的颜色
                    },
                    {
                      offset: 0.5,
                      color: barColor
                      // 0% 处的颜色
                    },
                    {
                      offset: 0.5,
                      color: barColor2
                      // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: barColor2
                      // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: barColor
                      // 100% 处的颜色
                    }
                  ],
                  global: false
                  // 缺省为 false
                },
                opacity: 1,
                // 尾迹线条透明度
                curveness: 0
                // 尾迹线条曲直度
              },
              label: {
                show: 0,
                position: "end",
                formatter: "245"
              },
              silent: true,
              data: lineData(chartData)
            },
            {
              type: "scatter",
              coordinateSystem: "geo",
              geoIndex: 0,
              zlevel: 2,
              label: {
                show: false,
                position: "bottom",
                formatter: (params) => {
                  var _a2;
                  return (_a2 = data[params.dataIndex]) == null ? void 0 : _a2.value;
                },
                padding: [4, 8],
                backgroundColor: "#003F5E",
                borderRadius: 5,
                borderColor: "#67F0EF",
                borderWidth: 1,
                color: "#67F0EF"
              },
              symbol: "diamond",
              symbolSize: [barSize - 1, 8],
              itemStyle: {
                color: barColor,
                opacity: 1
              },
              silent: true,
              data: scatterData(chartData)
            },
            {
              type: "scatter",
              coordinateSystem: "geo",
              geoIndex: 0,
              zlevel: 1,
              label: {
                formatter: "{b}",
                position: "bottom",
                color: "#fff",
                fontSize: 11,
                distance: 10,
                show: false
              },
              symbol: "diamond",
              symbolSize: [barSize - 1, 8],
              itemStyle: {
                // color: '#F7AF21',
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 1,
                  y2: 0,
                  colorStops: [
                    {
                      offset: 0,
                      color: barColor
                      // 0% 处的颜色
                    },
                    {
                      offset: 0.5,
                      color: barColor
                      // 0% 处的颜色
                    },
                    {
                      offset: 0.5,
                      color: barColor2
                      // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: barColor2
                      // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: barColor
                      // 100% 处的颜色
                    }
                  ],
                  global: false
                  // 缺省为 false
                },
                opacity: 1
                // shadowColor: '#fff',
                // shadowBlur: 5,
                // shadowOffsetY: 2
              },
              silent: true,
              data: scatterData2(chartData)
            }
          ];
          if (props.config && props.config.option) {
            common_vendor.merge(chartOption.value, props.config.option);
            chartOption.value = setGeoAreaColor(chartOption.value, props.config);
            chartOption.value = handleTotalAndUnitMap(props.compName, chartOption.value, props.config, chartData);
            chartOption.value = handleCommonOpt(chartOption.value);
            setTimeout(() => {
              option.value = __spreadValues({}, chartOption.value);
              console.log("柱形地图option.value", option.value);
              pageTips.show = false;
              echartId.value = props.i;
            }, 300);
          }
          if (dataSource.value && dataSource.value.length === 0) {
            pageTips.status = 1;
            pageTips.show = true;
          }
        } catch (e) {
          console.log("柱形地图报错", e);
        }
      });
    }
    function lineMaxHeight(chartData) {
      const maxValue = Math.max(...chartData.map((item) => item.value));
      return maxValue < 10 ? maxValue : 1 / maxValue;
    }
    function lineData(chartData) {
      let lineData2 = [];
      chartData.forEach((item) => {
        let geoCoord = city_point.value[item.name];
        if (geoCoord) {
          let coords = [geoCoord, [geoCoord[0], geoCoord[1] + item.value * lineMaxHeight(chartData)]];
          lineData2.push({
            coords
          });
        }
      });
      return lineData2;
    }
    function scatterData(chartData) {
      let scatterData3 = [];
      chartData.forEach((item) => {
        let geoCoord = city_point.value[item.name];
        if (geoCoord) {
          scatterData3.push([geoCoord[0], geoCoord[1] + item.value * lineMaxHeight(chartData)]);
        }
      });
      return scatterData3;
    }
    function scatterData2(chartData) {
      let scatterData22 = [];
      chartData.forEach((item) => {
        let geoCoord = city_point.value[item.name];
        if (geoCoord) {
          scatterData22.push({
            name: item.name,
            value: geoCoord
          });
        }
      });
      return scatterData22;
    }
    common_vendor.onMounted(() => __async(this, null, function* () {
      yield queryCityCenter();
      yield queryData();
    }));
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.unref(pageTips).show
      }, common_vendor.unref(pageTips).show ? {
        b: common_vendor.p({
          status: common_vendor.unref(pageTips).status
        })
      } : {
        c: common_vendor.p({
          option: option.value,
          mapName: common_vendor.unref(mapName),
          mapData: common_vendor.unref(mapDataJson)
        })
      });
    };
  }
};
wx.createComponent(_sfc_main);
//# sourceMappingURL=index.js.map
