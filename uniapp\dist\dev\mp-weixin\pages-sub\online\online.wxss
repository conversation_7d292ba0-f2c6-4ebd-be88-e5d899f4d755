/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.data-v-b3a2b4cd .wd-search {
  border-bottom: 1px solid #f4f2f2;
}
.wd-swipe-action.data-v-b3a2b4cd:first-child {
  margin-top: 10px;
}
.list.data-v-b3a2b4cd {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  border-bottom: 1px solid #eee;
  padding: 16px 12px;
  line-height: 20px;
  margin-bottom: 10px;
}
.list .cIcon.data-v-b3a2b4cd {
  text-align: center;
  line-height: 24px;
  color: #fff;
  margin-right: 8px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
}
.list .cIcon .u-iconfont.data-v-b3a2b4cd {
  font-size: 14px;
}
.list .tableTxt.data-v-b3a2b4cd {
  flex: 1;
  margin-right: 40px;
}
.list .createTime.data-v-b3a2b4cd {
  text-align: right;
  width: 75px;
  font-size: 12px;
  color: #919191;
}
.action.data-v-b3a2b4cd {
  width: 100px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action .button.data-v-b3a2b4cd {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 100%;
  color: #fff;
}
.action .button.data-v-b3a2b4cd:first-child {
  background-color: #fa4350;
}
.action .button.data-v-b3a2b4cd:last-child {
  background-color: #f0883a;
}
.wrap.data-v-b3a2b4cd {
  height: 100%;
}