{"version": 3, "file": "Popup.js", "sources": ["../../../../../src/components/Popup/Popup.vue", "../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvY29tcG9uZW50cy9Qb3B1cC9Qb3B1cC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"Popup\">\r\n    <view @click=\"handleClick\">\r\n      <wd-input\r\n        :placeholder=\"`请选择${$attrs.label}`\"\r\n        type=\"text\"\r\n        readonly\r\n        v-model=\"showText\"\r\n        clearable\r\n        v-bind=\"$attrs\"\r\n      />\r\n    </view>\r\n    <popupReportModal\r\n      v-if=\"reportModal.show\"\r\n      :code=\"code\"\r\n      :showFiled=\"reportModal.showFiled\"\r\n      :multi=\"multi\"\r\n      @close=\"handleClose\"\r\n      @change=\"handleChange\"\r\n    ></popupReportModal>\r\n  </view>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, watch, useAttrs } from 'vue'\r\nimport { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'\r\nimport { http } from '@/utils/http'\r\nimport popupReportModal from './components/popupReportModal.vue'\r\ndefineOptions({\r\n  name: 'Popup',\r\n  options: {\r\n    styleIsolation: 'shared'\r\n  }\r\n})\r\nconst props = defineProps({\r\n  code: {\r\n    type: String,\r\n    required: true,\r\n    default: '',\r\n  },\r\n  fieldConfig: {\r\n    type: Array,\r\n    required: true,\r\n    default: () => [],\r\n  },\r\n  setFieldsValue: {\r\n    type: Function,\r\n    required: true,\r\n    default: () => {},\r\n  },\r\n  modelValue: {\r\n    type: String,\r\n    default: '',\r\n  },\r\n  multi: {\r\n    type: Boolean,\r\n    default: false,\r\n  },\r\n  spliter: {\r\n    type: String,\r\n    default: ',',\r\n  },\r\n})\r\nconst emit = defineEmits(['change', 'update:modelValue'])\r\n\r\nconst toast = useToast()\r\nconst showText = ref('')\r\nconst attrs: any = useAttrs()\r\nconst reportModal = reactive({\r\n  show: false,\r\n  showFiled: props.fieldConfig.map((item) => item['target']).join(','),\r\n})\r\nif (!props.code || props.fieldConfig.length == 0) {\r\n  toast.error('popup参数未正确配置!')\r\n}\r\n\r\n/**\r\n * 监听value数值\r\n */\r\nwatch(\r\n  () => props.modelValue,\r\n  (val) => {\r\n    showText.value = val && val.length > 0 ? val.split(props.spliter).join(',') : ''\r\n  },\r\n  { immediate: true },\r\n)\r\nfunction callBack(rows) {\r\n  let fieldConfig: any = props.fieldConfig\r\n  //匹配popup设置的回调值\r\n  let values = {}\r\n  let labels = []\r\n  for (let item of fieldConfig) {\r\n    let val = rows.map((row) => row[item.source])\r\n    val = val.length == 1 ? val[0] : val.join(',')\r\n    item.target.split(',').forEach((target) => {\r\n      values[target] = val\r\n    })\r\n  }\r\n  showText.value = labels.join(',')\r\n  props.setFieldsValue(values)\r\n  emit('change', values)\r\n  // emit('update:modelValue', values)\r\n}\r\nconst handleClick = () => {\r\n  if (!attrs.disabled) {\r\n    reportModal.show = true\r\n  }\r\n}\r\nconst handleClose = () => {\r\n  reportModal.show = false\r\n}\r\nconst handleChange = (data) => {\r\n  console.log('选中的值：', data)\r\n  callBack(data)\r\n}\r\n</script>\r\n\r\n<style scoped></style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/components/Popup/Popup.vue'\nwx.createComponent(Component)"], "names": ["useToast", "ref", "useAttrs", "reactive", "watch", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,MAAA,mBAA6B,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7B,UAAM,QAAQ;AA6Bd,UAAM,OAAO;AAEb,UAAM,QAAQA,cAAAA,SAAS;AACjB,UAAA,WAAWC,kBAAI,EAAE;AACvB,UAAM,QAAaC,cAAAA,SAAS;AAC5B,UAAM,cAAcC,cAAAA,SAAS;AAAA,MAC3B,MAAM;AAAA,MACN,WAAW,MAAM,YAAY,IAAI,CAAC,SAAS,KAAK,QAAQ,CAAC,EAAE,KAAK,GAAG;AAAA,IAAA,CACpE;AACD,QAAI,CAAC,MAAM,QAAQ,MAAM,YAAY,UAAU,GAAG;AAChD,YAAM,MAAM,eAAe;AAAA,IAAA;AAM7BC,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,QAAQ;AACP,iBAAS,QAAQ,OAAO,IAAI,SAAS,IAAI,IAAI,MAAM,MAAM,OAAO,EAAE,KAAK,GAAG,IAAI;AAAA,MAChF;AAAA,MACA,EAAE,WAAW,KAAK;AAAA,IACpB;AACA,aAAS,SAAS,MAAM;AACtB,UAAI,cAAmB,MAAM;AAE7B,UAAI,SAAS,CAAC;AACd,UAAI,SAAS,CAAC;AACd,eAAS,QAAQ,aAAa;AACxB,YAAA,MAAM,KAAK,IAAI,CAAC,QAAQ,IAAI,KAAK,MAAM,CAAC;AACtC,cAAA,IAAI,UAAU,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK,GAAG;AAC7C,aAAK,OAAO,MAAM,GAAG,EAAE,QAAQ,CAAC,WAAW;AACzC,iBAAO,MAAM,IAAI;AAAA,QAAA,CAClB;AAAA,MAAA;AAEM,eAAA,QAAQ,OAAO,KAAK,GAAG;AAChC,YAAM,eAAe,MAAM;AAC3B,WAAK,UAAU,MAAM;AAAA,IAAA;AAGvB,UAAM,cAAc,MAAM;AACpB,UAAA,CAAC,MAAM,UAAU;AACnB,oBAAY,OAAO;AAAA,MAAA;AAAA,IAEvB;AACA,UAAM,cAAc,MAAM;AACxB,kBAAY,OAAO;AAAA,IACrB;AACM,UAAA,eAAe,CAAC,SAAS;AACrB,cAAA,IAAI,SAAS,IAAI;AACzB,eAAS,IAAI;AAAA,IACf;;;;;;;;;;;;;;;;;;;;;;;;;;ACjHA,GAAG,gBAAgBC,SAAS;"}