/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.z-paging-content.data-v-0aee2493 {
  background-color: #f1f1f1;
}
.data-v-0aee2493 .wd-row {
  display: flex;
  flex-wrap: wrap;
  list-style-type: none;
  background-color: #ffffff;
}
.data-v-0aee2493 .wd-row .wd-col {
  padding: 10px;
  min-width: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.data-v-0aee2493 .wd-row .wd-col .li-img {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80rpx;
  height: 80rpx;
  background-color: #00bcd4;
  border-radius: 100%;
  color: #fff;
}
.data-v-0aee2493 .wd-row .wd-col .li-text {
  color: #000;
  margin-top: 6px;
  text-align: center;
  font-size: 12px;
  width: 120rpx; /* 设置元素宽度 */
  white-space: nowrap; /* 禁止文本换行 */
  overflow: hidden; /* 隐藏超出部分 */
  text-overflow: ellipsis; /* 超出部分显示为省略号 */
}
.list-ul.data-v-0aee2493 {
  display: flex;
  flex-wrap: wrap;
  list-style-type: none;
  background-color: #ffffff;
}
.list-ul .list-li.data-v-0aee2493 {
  padding: 10px;
  min-width: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.list-ul .list-li .li-img.data-v-0aee2493 {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80rpx;
  height: 80rpx;
  background-color: #00bcd4;
  border-radius: 100%;
  color: #fff;
}
.list-ul .list-li .li-text.data-v-0aee2493 {
  color: #000;
  margin-top: 6px;
  text-align: center;
  font-size: 12px;
  width: 120rpx; /* 设置元素宽度 */
  white-space: nowrap; /* 禁止文本换行 */
  overflow: hidden; /* 隐藏超出部分 */
  text-overflow: ellipsis; /* 超出部分显示为省略号 */
}