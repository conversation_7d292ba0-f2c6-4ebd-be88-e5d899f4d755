{"version": 3, "file": "uni-list.js", "sources": ["../../../../../../../src/uni_modules/uni-list/components/uni-list/uni-list.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvdW5pX21vZHVsZXMvdW5pLWxpc3QvY29tcG9uZW50cy91bmktbGlzdC91bmktbGlzdC52dWU"], "sourcesContent": ["<template>\r\n\t<!-- #ifndef APP-NVUE -->\r\n\t<view class=\"uni-list uni-border-top-bottom\">\r\n\t\t<view v-if=\"border\" class=\"uni-list--border-top\"></view>\r\n\t\t<slot />\r\n\t\t<view v-if=\"border\" class=\"uni-list--border-bottom\"></view>\r\n\t</view>\r\n\t<!-- #endif -->\r\n\t<!-- #ifdef APP-NVUE -->\r\n\t<list :bounce=\"false\" :scrollable=\"true\" show-scrollbar :render-reverse=\"renderReverse\" @scroll=\"scroll\" class=\"uni-list\" :class=\"{ 'uni-list--border': border }\" :enableBackToTop=\"enableBackToTop\"\r\n\t\tloadmoreoffset=\"15\">\r\n\t\t<slot />\r\n\t</list>\r\n\t<!-- #endif -->\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * List 列表\r\n\t * @description 列表组件\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=24\r\n\t * @property {String} \tborder = [true|false] \t\t标题\r\n\t */\r\n\texport default {\r\n\t\tname: 'uniList',\r\n\t\t'mp-weixin': {\r\n\t\t\toptions: {\r\n\t\t\t\tmultipleSlots: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tstackFromEnd:{\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault:false\r\n\t\t\t},\r\n\t\t\tenableBackToTop: {\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tscrollY: {\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tborder: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\trenderReverse:{\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t}\r\n\t\t},\r\n\t\t// provide() {\r\n\t\t// \treturn {\r\n\t\t// \t\tlist: this\r\n\t\t// \t};\r\n\t\t// },\r\n\t\tcreated() {\r\n\t\t\tthis.firstChildAppend = false;\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tloadMore(e) {\r\n\t\t\t\tthis.$emit('scrolltolower');\r\n\t\t\t},\r\n\t\t\tscroll(e) {\r\n\t\t\t\tthis.$emit('scroll', e);\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n<style lang=\"scss\">\r\n\t$uni-bg-color:#ffffff;\r\n\t$uni-border-color:#e5e5e5;\r\n\r\n\t.uni-list {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tbackground-color: $uni-bg-color;\r\n\t\tposition: relative;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.uni-list--border {\r\n\t\tposition: relative;\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\tborder-top-color: $uni-border-color;\r\n\t\tborder-top-style: solid;\r\n\t\tborder-top-width: 0.5px;\r\n\t\tborder-bottom-color: $uni-border-color;\r\n\t\tborder-bottom-style: solid;\r\n\t\tborder-bottom-width: 0.5px;\r\n\t\t/* #endif */\r\n\t\tz-index: -1;\r\n\t}\r\n\r\n\t/* #ifndef APP-NVUE */\r\n\r\n\t.uni-list--border-top {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tright: 0;\r\n\t\tleft: 0;\r\n\t\theight: 1px;\r\n\t\t-webkit-transform: scaleY(0.5);\r\n\t\ttransform: scaleY(0.5);\r\n\t\tbackground-color: $uni-border-color;\r\n\t\tz-index: 1;\r\n\t}\r\n\r\n\t.uni-list--border-bottom {\r\n\t\tposition: absolute;\r\n\t\tbottom: 0;\r\n\t\tright: 0;\r\n\t\tleft: 0;\r\n\t\theight: 1px;\r\n\t\t-webkit-transform: scaleY(0.5);\r\n\t\ttransform: scaleY(0.5);\r\n\t\tbackground-color: $uni-border-color;\r\n\t}\r\n\r\n\t/* #endif */\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/uni_modules/uni-list/components/uni-list/uni-list.vue'\nwx.createComponent(Component)"], "names": [], "mappings": ";;AAuBC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,aAAa;AAAA,IACZ,SAAS;AAAA,MACR,eAAe;AAAA,IAChB;AAAA,EACA;AAAA,EACD,OAAO;AAAA,IACN,cAAa;AAAA,MACZ,MAAM;AAAA,MACN,SAAQ;AAAA,IACR;AAAA,IACD,iBAAiB;AAAA,MAChB,MAAM,CAAC,SAAS,MAAM;AAAA,MACtB,SAAS;AAAA,IACT;AAAA,IACD,SAAS;AAAA,MACR,MAAM,CAAC,SAAS,MAAM;AAAA,MACtB,SAAS;AAAA,IACT;AAAA,IACD,QAAQ;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,eAAc;AAAA,MACb,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,UAAU;AACT,SAAK,mBAAmB;AAAA,EACxB;AAAA,EACD,SAAS;AAAA,IACR,SAAS,GAAG;AACX,WAAK,MAAM,eAAe;AAAA,IAC1B;AAAA,IACD,OAAO,GAAG;AACT,WAAK,MAAM,UAAU,CAAC;AAAA,IACvB;AAAA,EACD;;;;;;;;;;AClEF,GAAG,gBAAgB,SAAS;"}