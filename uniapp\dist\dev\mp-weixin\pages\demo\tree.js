"use strict";
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_button2 + _easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  (_easycom_wd_button + DaTree + _easycom_PageLayout)();
}
const DaTree = () => "../../uni_modules/da-tree/index.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "tree",
  setup(__props) {
    function GetApiData(currentNode) {
      const { key } = currentNode;
      return new Promise((resolve) => {
        setTimeout(() => {
          if (key.indexOf("-") > -1) {
            return resolve(null);
          }
          return resolve([
            {
              id: `${key}-1`,
              name: `行政部X${key}-1`
            },
            {
              id: `${key}-2`,
              name: `财务部X${key}-2`,
              append: "定义了末项数据",
              leaf: true
            },
            {
              id: `${key}-3`,
              name: `资源部X${key}-3`
            },
            {
              id: `${key}-4`,
              name: `资源部X${key}-3`,
              append: "被禁用，无展开图标",
              disabled: true
            }
          ]);
        }, 2e3);
      });
    }
    const DaTreeRef = common_vendor.ref();
    const defaultCheckedKeysValue = common_vendor.ref(["211", "222"]);
    const defaultCheckedKeysValue2 = common_vendor.ref("222");
    const defaultExpandKeysValue3 = common_vendor.ref(["212", "231"]);
    const roomTreeData = common_vendor.ref([
      {
        id: "2",
        name: "行政中心",
        children: [
          {
            id: "21",
            name: "行政部",
            children: [
              {
                id: "211",
                name: "行政一部",
                children: null
              },
              {
                id: "212",
                name: "行政二部",
                children: [],
                disabled: true
              }
            ]
          },
          {
            id: "22",
            name: "财务部",
            children: [
              {
                id: "221",
                name: "财务一部",
                children: [],
                disabled: true
              },
              {
                id: "222",
                name: "财务二部",
                children: []
              }
            ]
          },
          {
            id: "23",
            name: "人力资源部",
            children: [
              {
                id: "231",
                name: "人力一部",
                children: []
              },
              {
                id: "232",
                name: "人力二部"
                // append: '更多示例，请下载示例项目查看',
              }
            ]
          }
        ]
      }
    ]);
    function doExpandTree(keys, expand) {
      var _a, _b;
      (_a = DaTreeRef.value) == null ? void 0 : _a.setExpandedKeys(keys, expand);
      const gek = (_b = DaTreeRef.value) == null ? void 0 : _b.getExpandedKeys();
      console.log("当前已展开的KEY ==>", gek);
    }
    function doCheckedTree(keys, checked) {
      var _a, _b;
      (_a = DaTreeRef.value) == null ? void 0 : _a.setCheckedKeys(keys, checked);
      const gek = (_b = DaTreeRef.value) == null ? void 0 : _b.getCheckedKeys();
      console.log("当前已选中的KEY ==>", gek);
    }
    function handleTreeChange(allSelectedKeys, currentItem) {
      console.log("handleTreeChange ==>", allSelectedKeys, currentItem);
    }
    function handleExpandChange(expand, currentItem) {
      console.log("handleExpandChange ==>", expand, currentItem);
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(($event) => doCheckedTree(["2"], true)),
        b: common_vendor.o(($event) => doCheckedTree(["2"], false)),
        c: common_vendor.o(($event) => doExpandTree("all", true)),
        d: common_vendor.o(($event) => doExpandTree("all", false)),
        e: common_vendor.o(($event) => doExpandTree(["22", "23"], true)),
        f: common_vendor.o(($event) => doExpandTree(["22", "23"], false)),
        g: common_vendor.o(($event) => doCheckedTree(["211", "222"], true)),
        h: common_vendor.o(($event) => doCheckedTree(["211", "222"], false)),
        i: common_vendor.sr(DaTreeRef, "fe99460d-10,fe99460d-1", {
          "k": "DaTreeRef"
        }),
        j: common_vendor.o(handleTreeChange),
        k: common_vendor.o(handleExpandChange),
        l: common_vendor.p({
          data: roomTreeData.value,
          labelField: "name",
          valueField: "id",
          defaultExpandAll: true,
          showCheckbox: true,
          defaultCheckedKeys: defaultCheckedKeysValue.value
        }),
        m: common_vendor.o(handleTreeChange),
        n: common_vendor.o(handleExpandChange),
        o: common_vendor.p({
          data: roomTreeData.value,
          labelField: "name",
          valueField: "id",
          defaultExpandAll: true,
          defaultCheckedKeys: defaultCheckedKeysValue2.value
        }),
        p: common_vendor.o(handleTreeChange),
        q: common_vendor.o(handleExpandChange),
        r: common_vendor.p({
          data: roomTreeData.value,
          labelField: "name",
          valueField: "id",
          showCheckbox: true,
          defaultExpandedKeys: defaultExpandKeysValue3.value
        }),
        s: common_vendor.o(handleTreeChange),
        t: common_vendor.o(handleExpandChange),
        v: common_vendor.p({
          data: roomTreeData.value,
          labelField: "name",
          valueField: "id",
          showCheckbox: true,
          loadMode: true,
          loadApi: GetApiData,
          defaultExpandAll: true
        }),
        w: common_vendor.p({
          navTitle: "树示例",
          backRouteName: "demo"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-fe99460d"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=tree.js.map
