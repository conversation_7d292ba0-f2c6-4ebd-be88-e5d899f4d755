{"version": 3, "file": "foo.js", "sources": ["../../../../../src/service/index/foo.ts"], "sourcesContent": ["import { http } from '@/utils/http'\r\nexport interface IFooItem {\r\n  id: string\r\n  name: string\r\n}\r\n\r\n/** GET 请求 */\r\nexport const getFooAPI = (name: string) => {\r\n  return http.get<IFooItem>('/foo', { name })\r\n}\r\n\r\n/** POST 请求 */\r\nexport const postFooAPI = (name: string) => {\r\n  return http.post<IFooItem>('/foo', { name }, { name })\r\n}\r\n"], "names": ["http"], "mappings": ";;AAOa,MAAA,YAAY,CAAC,SAAiB;AACzC,SAAOA,WAAK,KAAA,IAAc,QAAQ,EAAE,MAAM;AAC5C;;"}