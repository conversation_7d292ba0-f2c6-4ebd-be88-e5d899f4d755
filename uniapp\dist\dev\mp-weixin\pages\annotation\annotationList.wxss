/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.wrap.data-v-b4db58dd {
  height: 100%;
}
.wd-swipe-action.data-v-b4db58dd:first-child {
  margin-top: 10px;
}
.list.data-v-b4db58dd {
  padding: 14px 14px;
  background-color: #fff;
  border-bottom: 1px solid #eee;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}
.list .cIcon.data-v-b4db58dd {
  flex: none;
  text-align: center;
  line-height: 33px;
  width: 33px;
  height: 33px;
  background-color: #f37b1d;
  color: #fff;
  border-radius: 4px;
  margin-right: 10px;
}
.list .content.data-v-b4db58dd {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-right: 20px;
  overflow: hidden;
}
.list .content .title.data-v-b4db58dd {
  font-size: 15px;
  margin-bottom: 2px;
}
.list .content .desc.data-v-b4db58dd {
  font-size: 13px;
  color: rgb(153, 153, 153);
}
.list .operate.data-v-b4db58dd {
  text-align: right;
  width: 70px;
}
.list .operate .u-iconfont.data-v-b4db58dd {
  font-size: 20px;
  margin-bottom: 8px;
}
.list .operate .time.data-v-b4db58dd {
  font-size: 12px;
  color: #aaa;
}
.action.data-v-b4db58dd {
  width: 70px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action .button.data-v-b4db58dd {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 100%;
  color: #fff;
}
.action .button.data-v-b4db58dd:first-child {
  background-color: #fa4350;
}