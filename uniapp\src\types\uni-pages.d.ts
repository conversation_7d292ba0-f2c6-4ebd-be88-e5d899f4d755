/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-pages

interface NavigateToOptions {
  url: "/pages/index/index" |
       "/pages/about/about" |
       "/pages/annotation/annotationDetail" |
       "/pages/annotation/annotationList" |
       "/pages/data/detail" |
       "/pages/data/index" |
       "/pages/demo/demo" |
       "/pages/demo/form" |
       "/pages/demo/indexBar" |
       "/pages/demo/selectPicker" |
       "/pages/demo/tree" |
       "/pages/index/data" |
       "/pages/index/postDetail" |
       "/pages/login/login" |
       "/pages/message/message" |
       "/pages/more/more" |
       "/pages/user/people" |
       "/pages/workHome/index" |
       "/pages-home/home/<USER>" |
       "/pages-message/chat/chat" |
       "/pages-message/contacts/contacts" |
       "/pages-message/personPage/personPage" |
       "/pages-message/tenant/tenant" |
       "/pages-user/location/location" |
       "/pages-user/userEdit/userEdit" |
       "/pages-work/dragPage/index" |
       "/pages-work/onlinePage/onlineAdd" |
       "/pages-work/onlinePage/onlineDetail" |
       "/pages-work/onlinePage/onlineEdit" |
       "/pages-sub/online/online" |
       "/pages-sub/online/onlineCard" |
       "/pages-sub/online/onlineTable" |
       "/pages-data/medication/list" |
       "/pages-data/medication/form" |
       "/pages-data/vital-signs/list" |
       "/pages-data/vital-signs/form" |
       "/pages-data/examinationReport/list" |
       "/pages-data/examinationReport/form" |
       "/pages-data/monitor/list" |
       "/pages-data/monitor/form" |
       "/pages-data/psychology/list" |
       "/pages-data/psychology/form" |
       "/pages-data/dailyLife/list" |
       "/pages-data/dailyLife/form" |
       "/pages-data/registration/form" |
       "/pages-data/registration/consent" |
       "/pages-data/doctor/form" |
       "/pages-data/social/form" |
       "/pages-data/chat/chat" |
       "/pages-data/chat/chatDetail" |
       "/pages-data/chat/addChat";
}
interface RedirectToOptions extends NavigateToOptions {}

interface SwitchTabOptions {
  url: "/pages/index/index" | "/pages/data/index" | "/pages/user/people"
}

type ReLaunchOptions = NavigateToOptions | SwitchTabOptions;

declare interface Uni {
  navigateTo(options: UniNamespace.NavigateToOptions & NavigateToOptions): void;
  redirectTo(options: UniNamespace.RedirectToOptions & RedirectToOptions): void;
  switchTab(options: UniNamespace.SwitchTabOptions & SwitchTabOptions): void;
  reLaunch(options: UniNamespace.ReLaunchOptions & ReLaunchOptions): void;
}
