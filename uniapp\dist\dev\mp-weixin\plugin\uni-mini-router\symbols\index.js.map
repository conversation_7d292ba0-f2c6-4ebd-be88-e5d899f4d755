{"version": 3, "file": "index.js", "sources": ["../../../../../../src/plugin/uni-mini-router/symbols/index.ts"], "sourcesContent": ["/*\r\n * @Author: weish<PERSON>\r\n * @Date: 2023-03-13 17:01:30\r\n * @LastEditTime: 2023-04-26 21:51:51\r\n * @LastEditors: weisheng\r\n * @Description:\r\n * @FilePath: \\uni-mini-router\\src\\symbols\\index.ts\r\n * 记得注释\r\n */\r\nimport type { InjectionKey, Ref } from 'vue'\r\nimport type { Route, Router } from '../interfaces'\r\n\r\n/**\r\n * useRouter 用到的key\r\n *\r\n * @internal\r\n */\r\nexport const routerKey = Symbol('__ROUTER__') as InjectionKey<Router>\r\n\r\n/**\r\n * useRoute 用到的key\r\n *\r\n * @internal\r\n */\r\nexport const routeKey = Symbol('__ROUTE__') as InjectionKey<Ref<Route>>\r\n"], "names": [], "mappings": ";AAiBa,MAAA,YAAY,OAAO,YAAY;AAO/B,MAAA,WAAW,OAAO,WAAW;;;"}