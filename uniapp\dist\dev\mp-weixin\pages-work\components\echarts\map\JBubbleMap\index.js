"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../../../../common/vendor.js");
const pagesWork_components_echarts_props = require("../../props.js");
require("../../../common/echartUtil.js");
const pagesWork_components_hooks_useEchartMap = require("../../../hooks/useEchartMap.js");
const utils_is = require("../../../../../utils/is.js");
if (!Array) {
  const _component_statusTip = common_vendor.resolveComponent("statusTip");
  _component_statusTip();
}
if (!Math) {
  echartsUniapp();
}
const echartsUniapp = () => "../../index.js";
const _sfc_main = {
  __name: "index",
  props: __spreadValues({}, pagesWork_components_echarts_props.echartProps),
  setup(__props) {
    const props = __props;
    const option = common_vendor.ref({});
    const chartOption = common_vendor.ref({
      geo: {
        map: "",
        itemStyle: {}
      },
      tooltip: {
        textStyle: {
          color: "#fff"
        },
        padding: 5,
        formatter: null
      }
    });
    const mapName = common_vendor.ref("");
    let [
      { dataSource, reload, pageTips, config, mapDataJson, getAreaCode },
      { queryData, registerMap, setGeoAreaColor, handleTotalAndUnitMap, handleCommonOpt, getConvertData }
    ] = pagesWork_components_hooks_useEchartMap.useChartHook(props, initOption);
    const echartId = common_vendor.ref("");
    common_vendor.computed(() => ({ code: getAreaCode.value, data: mapDataJson.value }));
    function initOption(data) {
      return __async(this, null, function* () {
        var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o, _p, _q, _r, _s, _t, _u;
        let chartData = dataSource.value;
        mapName.value = yield registerMap();
        try {
          chartOption.value.tooltip = {
            enterable: true,
            transitionDuration: 1,
            textStyle: {
              color: "#000",
              decoration: "none"
            },
            trigger: "item",
            formatter: (params) => {
              let value = (params == null ? void 0 : params.value) ? utils_is.isArray(params.value) ? params.value[2] : params.value : 0;
              return `${params.seriesName} <br/> ${params.name}:   ${value}`;
            }
          };
          chartOption.value.geo.map = mapName.value;
          common_vendor.merge(chartOption.value.geo, props.config.option.geo);
          chartOption.value.series = [
            {
              name: "数据",
              type: (_b = (_a = props.config.option) == null ? void 0 : _a.area) == null ? void 0 : _b.markerType,
              coordinateSystem: "geo",
              data: chartData && chartData.length > 0 ? getConvertData(
                chartData.sort(function(a, b) {
                  return b.value - a.value;
                }).slice(0, (((_d = (_c = config.option) == null ? void 0 : _c.area) == null ? void 0 : _d.markerCount) || 0) > 0 ? (_e = config.option) == null ? void 0 : _e.area.markerCount : chartData.length)
              ) : [],
              symbol: ((_g = (_f = config.option) == null ? void 0 : _f.area) == null ? void 0 : _g.markerShape) || "circle",
              symbolSize: function(val) {
                var _a2, _b2, _c2, _d2, _e2, _f2;
                return ((_b2 = (_a2 = config.option) == null ? void 0 : _a2.area) == null ? void 0 : _b2.markerSize) && ((_d2 = (_c2 = config.option) == null ? void 0 : _c2.area) == null ? void 0 : _d2.markerSize) > 20 ? (_f2 = (_e2 = config.option) == null ? void 0 : _e2.area) == null ? void 0 : _f2.markerSize : 20;
              },
              showEffectOn: "render",
              //涟漪配置
              rippleEffect: {
                brushType: "stroke"
              },
              label: {
                position: ((_i = (_h = config.option) == null ? void 0 : _h.area) == null ? void 0 : _i.scatterLabelPosition) || "top",
                show: ((_k = (_j = config.option) == null ? void 0 : _j.area) == null ? void 0 : _k.scatterLabelShow) || false,
                color: ((_m = (_l = config.option) == null ? void 0 : _l.area) == null ? void 0 : _m.scatterLabelColor) || "#ffffff",
                fontSize: ((_o = (_n = config.option) == null ? void 0 : _n.area) == null ? void 0 : _o.scatterFontSize) || 12,
                formatter: (params) => {
                  if (utils_is.isArray(params.value)) {
                    return `${params.name || ""}:${params.value[2]}`;
                  }
                  return `${params.name || "空"}:${params.value}`;
                }
              },
              emphasis: {
                show: false
              },
              animation: true,
              itemStyle: {
                color: ((_q = (_p = config == null ? void 0 : config.option) == null ? void 0 : _p.area) == null ? void 0 : _q.markerColor) || "auto",
                shadowColor: ((_s = (_r = config == null ? void 0 : config.option) == null ? void 0 : _r.area) == null ? void 0 : _s.shadowColor) || "auto",
                opacity: (_u = (_t = config == null ? void 0 : config.option) == null ? void 0 : _t.area) == null ? void 0 : _u.markerOpacity
              }
            },
            {
              name: "地图",
              type: "map",
              map: mapName.value,
              geoIndex: 0,
              aspectScale: 0.75,
              //长宽比
              showLegendSymbol: false,
              // 存在legend时显示
              label: {
                show: true,
                color: "#000"
              },
              emphasis: {
                show: true,
                color: "#000",
                itemStyle: {
                  areaColor: "#2B91B7"
                }
              },
              roam: true,
              itemStyle: {
                areaColor: "#3B5077",
                borderColor: "#3B5077"
              },
              animation: true,
              data: chartData && chartData.length > 0 ? chartData : [],
              zlevel: 1
            }
          ];
          if (props.config && props.config.option) {
            common_vendor.merge(chartOption.value, props.config.option);
            chartOption.value = setGeoAreaColor(chartOption.value, props.config);
            chartOption.value = handleTotalAndUnitMap(props.compName, chartOption.value, props.config, chartData);
            chartOption.value = handleCommonOpt(chartOption.value);
            setTimeout(() => {
              option.value = __spreadValues({}, chartOption.value);
              console.log("散点地图最终的option.value", option.value);
              pageTips.show = false;
              echartId.value = props.i;
            }, 300);
          }
          if (dataSource.value && dataSource.value.length === 0) {
            pageTips.status = 1;
            pageTips.show = true;
          }
        } catch (e) {
          console.log("散点地图报错", e);
        }
      });
    }
    common_vendor.onMounted(() => {
      queryData();
    });
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.unref(pageTips).show
      }, common_vendor.unref(pageTips).show ? {
        b: common_vendor.p({
          status: common_vendor.unref(pageTips).status
        })
      } : {
        c: common_vendor.p({
          option: option.value,
          mapName: mapName.value,
          mapData: common_vendor.unref(mapDataJson)
        })
      });
    };
  }
};
wx.createComponent(_sfc_main);
//# sourceMappingURL=index.js.map
