{"version": 3, "file": "wd-radio-group.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-radio-group/wd-radio-group.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1yYWRpby1ncm91cC93ZC1yYWRpby1ncm91cC52dWU"], "sourcesContent": ["<template>\n  <view :class=\"`wd-radio-group  ${customClass} ${cell && shape === 'button' ? 'is-button' : ''}`\" :style=\"customStyle\">\n    <slot />\n  </view>\n</template>\n<script lang=\"ts\">\nexport default {\n  name: 'wd-radio-group',\n  options: {\n    virtualHost: true,\n    addGlobalClass: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport { watch } from 'vue'\nimport { useChildren } from '../composables/useChildren'\nimport { RADIO_GROUP_KEY, radioGroupProps } from './types'\n\nconst props = defineProps(radioGroupProps)\nconst emit = defineEmits(['change', 'update:modelValue'])\n\nconst { linkChildren, children } = useChildren(RADIO_GROUP_KEY)\n\nlinkChildren({ props, updateValue })\n\nwatch(\n  () => props.shape,\n  (newValue) => {\n    // type: 'dot', 'button', 'check'\n    const type = ['check', 'dot', 'button']\n    if (type.indexOf(newValue) === -1) console.error(`shape must be one of ${type.toString()}`)\n  },\n  { deep: true, immediate: true }\n)\n\n/**\n * @description 处理radio子节点通知\n */\nfunction updateValue(value: string | number | boolean) {\n  emit('update:modelValue', value)\n  emit('change', {\n    value\n  })\n}\n</script>\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-radio-group/wd-radio-group.vue'\nwx.createComponent(Component)"], "names": ["useChildren", "RADIO_GROUP_KEY", "watch"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAMA,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,EAAA;AAEpB;;;;;AAQA,UAAM,QAAQ;AACd,UAAM,OAAO;AAEb,UAAM,EAAE,aAAuB,IAAIA,0BAAYC,6BAAe;AAEjD,iBAAA,EAAE,OAAO,aAAa;AAEnCC,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,aAAa;AAEZ,cAAM,OAAO,CAAC,SAAS,OAAO,QAAQ;AAClC,YAAA,KAAK,QAAQ,QAAQ,MAAM;AAAI,kBAAQ,MAAM,wBAAwB,KAAK,SAAU,CAAA,EAAE;AAAA,MAC5F;AAAA,MACA,EAAE,MAAM,MAAM,WAAW,KAAK;AAAA,IAChC;AAKA,aAAS,YAAY,OAAkC;AACrD,WAAK,qBAAqB,KAAK;AAC/B,WAAK,UAAU;AAAA,QACb;AAAA,MAAA,CACD;AAAA,IAAA;;;;;;;;;;AC5CH,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}