"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
const utils_http = require("../../utils/http.js");
require("../../plugin/uni-mini-router/router/index.js");
const plugin_uniMiniRouter_core_index = require("../../plugin/uni-mini-router/core/index.js");
if (!Array) {
  const _easycom_wd_swipe_action2 = common_vendor.resolveComponent("wd-swipe-action");
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_swipe_action2 + _easycom_z_paging2 + _easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_wd_swipe_action = () => "../../node-modules/wot-design-uni/components/wd-swipe-action/wd-swipe-action.js";
const _easycom_z_paging = () => "../../uni_modules/z-paging/components/z-paging/z-paging.js";
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  (_easycom_wd_swipe_action + _easycom_z_paging + rightConditionFilter + _easycom_PageLayout)();
}
const rightConditionFilter = () => "./components/rightConditionFilter.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "annotationList",
  options: {
    // apply-shared‌：当前页面样式会影响到子组件样式.(小程序)
    // shared‌：当前页面样式影响到子组件，子组件样式也会影响到当前页面.(小程序)
    styleIsolation: "‌apply-shared‌"
  }
}), {
  __name: "annotationList",
  setup(__props) {
    const toast = common_vendor.useToast();
    plugin_uniMiniRouter_core_index.useRouter();
    const paging = common_vendor.ref(null);
    const dataList = common_vendor.ref([]);
    const starFlag = common_vendor.ref("");
    const conditionFilter = common_vendor.reactive({ show: false });
    const backRouteName = common_vendor.ref("index");
    const conditionStartDate = common_vendor.ref(null);
    const conditionEndDate = common_vendor.ref(null);
    const getParams = ({ pageNo, pageSize }) => {
      let result = {
        pageNo,
        pageSize,
        starFlag: starFlag.value,
        rangeDateKey: "zdy"
      };
      if (conditionStartDate.value) {
        result.beginDate = common_vendor.dayjs$1(conditionStartDate.value).format("YYYY-MM-DD") + " 00:00:00";
      } else {
        result.beginDate = "";
      }
      if (conditionEndDate.value) {
        result.endDate = common_vendor.dayjs$1(conditionEndDate.value).format("YYYY-MM-DD") + " 23:59:59";
      } else {
        result.endDate = "";
      }
      return result;
    };
    const queryList = (pageNo, pageSize) => {
      const params = getParams({ pageNo, pageSize });
      utils_http.http.get("/sys/annountCement/vue3List", __spreadValues({}, params)).then((res) => {
        if (res.success) {
          paging.value.complete(res.result);
        } else {
          paging.value.complete(false);
        }
      }).catch((res) => {
        paging.value.complete(false);
      });
    };
    const showDetail = (record) => {
      if (record.busType == "email") {
        goEmailDetailPage(record);
      } else if (record.busType == "bpm") {
        toast.warning("暂未实现~");
      } else if (record.busType == "bpm_task") {
        toast.warning("暂未实现~");
      } else {
        common_vendor.index.navigateTo({
          url: "/pages/annotation/annotationDetail?item=" + encodeURIComponent(JSON.stringify(record))
        });
      }
    };
    const goEmailDetailPage = (item) => {
      if (item.readFlag == "0") {
        paging.value.reload();
        let readUrl = "/sys/sysAnnouncementSend/editByAnntIdAndUserId";
        utils_http.http.put(readUrl, { anntId: item.anntId });
      }
      common_vendor.index.navigateTo({
        url: "/pages/mail/mailDetail?id=" + item.busId
      });
    };
    const handleFilterChange = ([flag, startTime, endTime]) => {
      starFlag.value = flag;
      startTime && (conditionStartDate.value = startTime);
      endTime && (conditionEndDate.value = endTime);
      paging.value.reload();
    };
    const changeStarFlag = (item) => {
      const url = "/sys/sysAnnouncementSend/edit";
      let starFlag2 = "1";
      if (item.starFlag == starFlag2) {
        starFlag2 = "0";
      }
      const params = {
        starFlag: starFlag2,
        id: item.sendId
      };
      utils_http.http.put(url, params).then((res) => {
        if (res.success) {
          item.starFlag = starFlag2;
        } else {
          toast.warning(res.message);
        }
      });
    };
    const handleAction = (flag, item) => {
      utils_http.http.delete("/sys/sysAnnouncementSend/delete", { id: item.sendId }).then((res) => {
        if (res.success) {
          paging.value.reload();
        }
      }).catch((e) => {
        console.log("al delUrl请求错误2", e);
      });
    };
    const getDesc = (item) => {
      if (item.busType == "email") {
        return "您收到一封新的邮件，请及时处理。";
      } else if (item.busType == "bpm") {
        return "您收到一条流程催办，请及时处理。";
      } else if (item.busType == "bpm_task") {
        return "您收到一条流程任务，请及时处理。";
      } else if (item.msgCategory == "2") {
        return "您收到一条系统消息，请及时处理。";
      } else if (item.msgCategory == "1") {
        return "您收到一条通知公告，请及查看。";
      }
    };
    common_vendor.onLoad((options) => {
      if (options == null ? void 0 : options.backRouteName) {
        backRouteName.value = options.backRouteName;
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.f(common_vendor.unref(dataList), (item, index, i0) => {
          var _a;
          return common_vendor.e$1({
            a: ["email"].includes(item.busType)
          }, ["email"].includes(item.busType) ? {} : ["bpm_task", "bpm"].includes(item.busType) ? {} : ["msgCategory"].includes(item.busType) ? {} : {}, {
            b: ["bpm_task", "bpm"].includes(item.busType),
            c: ["msgCategory"].includes(item.busType),
            d: common_vendor.t(item.titile),
            e: common_vendor.t(getDesc(item)),
            f: item.starFlag == "1"
          }, item.starFlag == "1" ? {} : {}, {
            g: common_vendor.o(($event) => changeStarFlag(item)),
            h: common_vendor.t((_a = item.sendTime) == null ? void 0 : _a.substring(0, 10)),
            i: common_vendor.o(($event) => showDetail(item)),
            j: common_vendor.o(($event) => handleAction("del", item)),
            k: "b4db58dd-3-" + i0 + ",b4db58dd-2"
          });
        }),
        b: common_vendor.sr(paging, "b4db58dd-2,b4db58dd-1", {
          "k": "paging"
        }),
        c: common_vendor.o(queryList),
        d: common_vendor.o(($event) => common_vendor.isRef(dataList) ? dataList.value = $event : null),
        e: common_vendor.p({
          fixed: false,
          modelValue: common_vendor.unref(dataList)
        }),
        f: common_vendor.o(() => common_vendor.unref(conditionFilter).show = true),
        g: common_vendor.unref(conditionFilter).show
      }, common_vendor.unref(conditionFilter).show ? {
        h: common_vendor.o(() => common_vendor.unref(conditionFilter).show = false),
        i: common_vendor.o(handleFilterChange),
        j: common_vendor.p({
          starFlag: common_vendor.unref(starFlag),
          conditionStartDate: common_vendor.unref(conditionStartDate),
          conditionEndDate: common_vendor.unref(conditionEndDate)
        })
      } : {}, {
        k: common_vendor.p({
          navTitle: "我的消息",
          backRouteName: common_vendor.unref(backRouteName),
          routeMethod: "pushTab"
        })
      });
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-b4db58dd"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=annotationList.js.map
