"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../../common/vendor.js");
const __default__ = {
  name: "wd-cell-group",
  options: {
    addGlobalClass: true,
    virtualHost: true,
    styleIsolation: "shared"
  }
};
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, __default__), {
  props: common_vendor.cellGroupProps,
  setup(__props) {
    const props = __props;
    const { linkChildren } = common_vendor.useChildren(common_vendor.CELL_GROUP_KEY);
    linkChildren({ props });
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: _ctx.title || _ctx.value || _ctx.useSlot
      }, _ctx.title || _ctx.value || _ctx.useSlot ? common_vendor.e$1({
        b: !_ctx.$slots.title
      }, !_ctx.$slots.title ? {
        c: common_vendor.t(_ctx.title)
      } : {}, {
        d: !_ctx.$slots.value
      }, !_ctx.$slots.value ? {
        e: common_vendor.t(_ctx.value)
      } : {}) : {}, {
        f: common_vendor.n(_ctx.border ? "is-border" : ""),
        g: common_vendor.n(_ctx.customClass),
        h: common_vendor.s(_ctx.customStyle)
      });
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-0cbdf28c"]]);
wx.createComponent(Component);
//# sourceMappingURL=wd-cell-group.js.map
