{"version": 3, "file": "BottomOperate.js", "sources": ["../../../../../src/components/BottomOperate/BottomOperate.vue", "../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvY29tcG9uZW50cy9Cb3R0b21PcGVyYXRlL0JvdHRvbU9wZXJhdGUudnVl"], "sourcesContent": ["<template>\r\n  <wd-popup v-model=\"show\" position=\"bottom\" @close=\"handleClose\">\r\n    <view class=\"contetn\">\r\n      <wd-text v-if=\"title\" :text=\"title\"></wd-text>\r\n      <wd-cell-group border>\r\n        <wd-cell\r\n          v-for=\"(item, index) in options\"\r\n          :icon=\"item.icon\"\r\n          :label=\"item.label\"\r\n          :custom-class=\"item.color\"\r\n          clickable\r\n          @click=\"handleClick(item)\"\r\n        ></wd-cell>\r\n      </wd-cell-group>\r\n    </view>\r\n  </wd-popup>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref } from 'vue'\r\ndefineOptions({\r\n  name: 'BottomOperate',\r\n  options: {\r\n    styleIsolation: 'shared',\r\n  },\r\n})\r\nconst eimt = defineEmits(['change', 'close'])\r\nconst show = ref(true)\r\nconst props = defineProps(['title', 'data', 'options'])\r\nconst handleClose = () => {\r\n  show.value = false\r\n  setTimeout(() => {\r\n    eimt('close')\r\n  }, 300)\r\n}\r\nconst handleClick = (item) => {\r\n  eimt('change', { option: item, data: props.data })\r\n  handleClose()\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.contetn {\r\n  padding: 10px;\r\n  .wd-text.is-default {\r\n    font-size: 14px;\r\n    color: #666;\r\n  }\r\n  :deep(.wd-cell) {\r\n    padding-left: 0;\r\n    --wot-cell-label-color: #444;\r\n    --wot-cell-label-fs: 14px;\r\n    &.red {\r\n      color: red;\r\n      --wot-cell-label-color: red;\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/components/BottomOperate/BottomOperate.vue'\nwx.createComponent(Component)"], "names": ["ref"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,UAAM,OAAO;AACP,UAAA,OAAOA,kBAAI,IAAI;AACrB,UAAM,QAAQ;AACd,UAAM,cAAc,MAAM;AACxB,WAAK,QAAQ;AACb,iBAAW,MAAM;AACf,aAAK,OAAO;AAAA,SACX,GAAG;AAAA,IACR;AACM,UAAA,cAAc,CAAC,SAAS;AAC5B,WAAK,UAAU,EAAE,QAAQ,MAAM,MAAM,MAAM,MAAM;AACrC,kBAAA;AAAA,IACd;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrCA,GAAG,gBAAgB,SAAS;"}