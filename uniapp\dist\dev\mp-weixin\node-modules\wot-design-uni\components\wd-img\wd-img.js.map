{"version": 3, "file": "wd-img.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-img/wd-img.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1pbWcvd2QtaW1nLnZ1ZQ"], "sourcesContent": ["<!--\n * @Author: 810505339\n * @Date: 2024-09-25 11:30:46\n * @LastEditors: 810505339\n * @LastEditTime: 2025-01-09 11:37:45\n * @FilePath: \\wot-design-uni\\src\\uni_modules\\wot-design-uni\\components\\wd-img\\wd-img.vue\n * 记得注释\n-->\n<template>\n  <view :class=\"rootClass\" @click=\"handleClick\" :style=\"rootStyle\">\n    <image\n      :class=\"`wd-img__image ${customImage}`\"\n      :style=\"status !== 'success' ? 'width: 0;height: 0;' : ''\"\n      :src=\"src\"\n      :mode=\"mode\"\n      :show-menu-by-longpress=\"showMenuByLongpress\"\n      :lazy-load=\"lazyLoad\"\n      @load=\"handleLoad\"\n      @error=\"handleError\"\n    />\n    <slot v-if=\"status === 'loading'\" name=\"loading\"></slot>\n    <slot v-if=\"status === 'error'\" name=\"error\"></slot>\n  </view>\n</template>\n<script lang=\"ts\">\nexport default {\n  name: 'wd-img',\n  options: {\n    virtualHost: true,\n    addGlobalClass: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport { computed, ref } from 'vue'\nimport { addUnit, isDef, objToStyle } from '../common/util'\nimport { imgProps } from './types'\n\nconst props = defineProps(imgProps)\nconst emit = defineEmits<{\n  (e: 'error', event: Event): void\n  (e: 'click', event: MouseEvent): void\n  (e: 'load', event: Event): void\n}>()\n\nconst rootStyle = computed(() => {\n  const style: Record<string, string | number> = {}\n  if (isDef(props.height)) {\n    style['height'] = addUnit(props.height)\n  }\n  if (isDef(props.width)) {\n    style['width'] = addUnit(props.width)\n  }\n  if (isDef(props.radius)) {\n    style['border-radius'] = addUnit(props.radius)\n    style['overflow'] = 'hidden'\n  }\n  return `${objToStyle(style)}${props.customStyle}`\n})\n\nconst rootClass = computed(() => {\n  return `wd-img  ${props.round ? 'is-round' : ''} ${props.customClass}`\n})\n\nconst status = ref<'loading' | 'error' | 'success'>('loading')\n\nfunction handleError(event: any) {\n  status.value = 'error'\n  emit('error', event)\n}\nfunction handleClick(event: MouseEvent) {\n  if (props.enablePreview && props.src && status.value == 'success') {\n    uni.previewImage({\n      urls: [props.previewSrc || props.src]\n    })\n  }\n  emit('click', event)\n}\nfunction handleLoad(event: any) {\n  status.value = 'success'\n  emit('load', event)\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-img/wd-img.vue'\nwx.createComponent(Component)"], "names": ["computed", "isDef", "addUnit", "objToStyle", "ref", "uni"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAyBA,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,EAAA;AAEpB;;;;;AAQA,UAAM,QAAQ;AACd,UAAM,OAAO;AAMP,UAAA,YAAYA,cAAAA,SAAS,MAAM;AAC/B,YAAM,QAAyC,CAAC;AAC5C,UAAAC,cAAA,MAAM,MAAM,MAAM,GAAG;AACvB,cAAM,QAAQ,IAAIC,sBAAQ,MAAM,MAAM;AAAA,MAAA;AAEpC,UAAAD,cAAA,MAAM,MAAM,KAAK,GAAG;AACtB,cAAM,OAAO,IAAIC,sBAAQ,MAAM,KAAK;AAAA,MAAA;AAElC,UAAAD,cAAA,MAAM,MAAM,MAAM,GAAG;AACvB,cAAM,eAAe,IAAIC,sBAAQ,MAAM,MAAM;AAC7C,cAAM,UAAU,IAAI;AAAA,MAAA;AAEtB,aAAO,GAAGC,cAAAA,WAAW,KAAK,CAAC,GAAG,MAAM,WAAW;AAAA,IAAA,CAChD;AAEK,UAAA,YAAYH,cAAAA,SAAS,MAAM;AAC/B,aAAO,WAAW,MAAM,QAAQ,aAAa,EAAE,IAAI,MAAM,WAAW;AAAA,IAAA,CACrE;AAEK,UAAA,SAASI,kBAAqC,SAAS;AAE7D,aAAS,YAAY,OAAY;AAC/B,aAAO,QAAQ;AACf,WAAK,SAAS,KAAK;AAAA,IAAA;AAErB,aAAS,YAAY,OAAmB;AACtC,UAAI,MAAM,iBAAiB,MAAM,OAAO,OAAO,SAAS,WAAW;AACjEC,sBAAAA,MAAI,aAAa;AAAA,UACf,MAAM,CAAC,MAAM,cAAc,MAAM,GAAG;AAAA,QAAA,CACrC;AAAA,MAAA;AAEH,WAAK,SAAS,KAAK;AAAA,IAAA;AAErB,aAAS,WAAW,OAAY;AAC9B,aAAO,QAAQ;AACf,WAAK,QAAQ,KAAK;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;ACjFpB,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}