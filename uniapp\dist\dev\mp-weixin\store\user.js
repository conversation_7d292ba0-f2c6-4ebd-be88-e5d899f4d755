"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
const common_vendor = require("../common/vendor.js");
const initState = {
  token: "",
  userid: "",
  username: "",
  realname: "",
  welcome: "",
  avatar: "",
  tenantId: 0,
  phone: "",
  email: "",
  sex: 1,
  userCategory: "",
  // 用户类型：0医生 1患者
  roleList: [],
  caseNumber: "",
  // 病案号，仅患者端使用
  // 本地存储时间
  localStorageTime: 0
};
const useUserStore = common_vendor.defineStore(
  "user",
  () => {
    const userInfo = common_vendor.ref(__spreadValues({}, initState));
    const setUserInfo = (val) => {
      userInfo.value = val;
    };
    const clearUserInfo = () => {
      userInfo.value = __spreadValues({}, initState);
    };
    const editUserInfo = (options) => {
      userInfo.value = __spreadValues(__spreadValues({}, userInfo.value), options);
    };
    const reset = () => {
      userInfo.value = __spreadValues({}, initState);
    };
    const isLogined = common_vendor.computed(() => !!userInfo.value.token);
    return {
      userInfo,
      setUserInfo,
      clearUserInfo,
      isLogined,
      editUserInfo,
      reset
    };
  },
  {
    // 如果需要持久化就写 true, 不需要持久化就写 false（或者去掉这个配置项）
    persist: true
  }
);
exports.useUserStore = useUserStore;
//# sourceMappingURL=user.js.map
