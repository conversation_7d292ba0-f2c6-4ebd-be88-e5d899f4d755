{"version": 3, "file": "wd-cell-group.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-cell-group/wd-cell-group.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1jZWxsLWdyb3VwL3dkLWNlbGwtZ3JvdXAudnVl"], "sourcesContent": ["<template>\n  <view :class=\"['wd-cell-group', border ? 'is-border' : '', customClass]\" :style=\"customStyle\">\n    <view v-if=\"title || value || useSlot\" class=\"wd-cell-group__title\">\n      <!--左侧标题-->\n      <view class=\"wd-cell-group__left\">\n        <text v-if=\"!$slots.title\">{{ title }}</text>\n        <slot v-else name=\"title\"></slot>\n      </view>\n      <!--右侧标题-->\n      <view class=\"wd-cell-group__right\">\n        <text v-if=\"!$slots.value\">{{ value }}</text>\n        <slot v-else name=\"value\"></slot>\n      </view>\n    </view>\n    <view class=\"wd-cell-group__body\">\n      <slot></slot>\n    </view>\n  </view>\n</template>\n\n<script lang=\"ts\">\nexport default {\n  name: 'wd-cell-group',\n  options: {\n    addGlobalClass: true,\n    virtualHost: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport { useChildren } from '../composables/useChildren'\nimport { CELL_GROUP_KEY, cellGroupProps } from './types'\n\nconst props = defineProps(cellGroupProps)\n\nconst { linkChildren } = useChildren(CELL_GROUP_KEY)\n\nlinkChildren({ props })\n</script>\n\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-cell-group/wd-cell-group.vue'\nwx.createComponent(Component)"], "names": ["useChildren", "CELL_GROUP_KEY"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAqBA,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,gBAAgB;AAAA,EAAA;AAEpB;;;;AAOA,UAAM,QAAQ;AAEd,UAAM,EAAE,aAAA,IAAiBA,cAAA,YAAYC,4BAAc;AAEtC,iBAAA,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;ACtCtB,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}