{"version": 3, "file": "form.js", "sources": ["../../../../../src/pages-data/monitor/form.vue", "../../../../../uniPage:/cGFnZXMtZGF0YVxtb25pdG9yXGZvcm0udnVl"], "sourcesContent": ["\r\n<template>\n<layout-default-uni >\r\n\t<PageLayout>\r\n\t\t<template #navbar>\r\n\t\t\t<NavBar :title=\"mode === 'add' ? '新增监测表' : '查看监测表'\" :showBack=\"true\" />\r\n\t\t</template>\r\n\r\n\t\t<scroll-view class=\"page-scroll-view\" scroll-y=\"true\">\r\n\t\t\t<view class=\"form-container\">\r\n\t\t\t\t<view class=\"form-header\">\r\n\t\t\t\t\t<text class=\"form-title\">慢性心衰患者监测表</text>\r\n\t\t\t\t</view>\r\n\r\n\r\n\r\n\t\t\t\t<view class=\"table1\">\r\n\t\t\t\t\t<view class=\"form-section\">\r\n\t\t\t\t\t\t<view class=\"head0\">\r\n\t\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"form-label\">尊敬的患者：</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"form-label indent-text\">我们邀请您参加 “院内慢性心力衰竭患者院外管理中应用研究”\r\n\t\t\t\t\t\t\t\t\t\t课题研究。本研究将在武汉亚心总医院医院开展。本研究已经得到伦理委员会的审查和批准。本研究的目的是探索在现有的慢性心力衰竭随访计划中增加线上远程随访的模式和效果，探索线上远程随访能否改善慢性心衰患者的身体机能、心理状况、生活质量、自我管理行为以及死亡率和再住院率，以推动慢性心衰患者线上远程随访模式的建立和发展，推动慢性心衰患者院外健康管理的智能化发展，为慢性心衰患者的院外健康管理提供进一步的支持。\r\n\t\t\t\t\t\t\t\t\t\t请您按监测表 1 - 5 项监测项目，请您真实填写，谢谢配合！\r\n\t\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"form-header\">\r\n\t\t\t\t\t\t\t<text class=\"form-title\">表1：心衰患者每日回访表</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>1.患者姓名</text>\r\n\t\t\t\t\t\t\t<view class=\"inline-form-item\">\r\n\t\t\t\t\t\t\t\t<input class=\"inline-form-input\" type=\"text\" v-model=\"formData.userName\" disabled />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>2.您的地理位置</text>\r\n\t\t\t\t\t\t\t<view class=\"inline-form-item\">\r\n\t\t\t\t\t\t\t\t<view class=\"location-box\" @click=\"getLocation\">\r\n\t\t\t\t\t\t\t\t\t<text v-if=\"!formData.location\">点击获取位置</text>\r\n\t\t\t\t\t\t\t\t\t<text v-else>{{ formData.location }}</text>\r\n\t\t\t\t\t\t\t\t\t<view class=\"location-icon cuIcon-locationfill\"></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>3.体重</text>\r\n\t\t\t\t\t\t\t<view class=\"inline-form-item\">\r\n\t\t\t\t\t\t\t\t<input class=\"inline-form-input\" type=\"number\" v-model=\"formData.weight\"\r\n\t\t\t\t\t\t\t\t\t placeholder=\"请输入患者体重（kg）\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>4.血压</text>\r\n\t\t\t\t\t\t\t<view class=\"inline-form-item\">\r\n\t\t\t\t\t\t\t\t<input class=\"inline-form-input\" type=\"number\" v-model=\"formData.bloodPressure\"\r\n\t\t\t\t\t\t\t\t\t placeholder=\"请输入患者血压（mmHg）\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>5.心率</text>\r\n\t\t\t\t\t\t\t<view class=\"inline-form-item\">\r\n\t\t\t\t\t\t\t\t<input type=\"number\" class=\"inline-form-input\" v-model=\"formData.heartRate\"\r\n\t\t\t\t\t\t\t\t\t placeholder=\"请输入患者心率（bpm）\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>6.是否吸烟</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.smock = '是')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.smock === '是' }\"></view>\r\n\t\t\t\t\t\t\t\t\t\t<text>是</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.smock = '否')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.smock === '否' }\"></view>\r\n\t\t\t\t\t\t\t\t\t\t<text>否</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>7.是否按时服药</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.medicineOnTime = '是')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.medicineOnTime === '是' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>是</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.medicineOnTime = '否')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.medicineOnTime === '否' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>否</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>8.是否漏服药</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.missingMedicine = '是')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.missingMedicine === '是' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>是</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.missingMedicine = '否')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.missingMedicine === '否' }\"></view>\r\n\t\t\t\t\t\t\t\t\t\t<text>否</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>9.是否擅自减药</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.reduceMedicine = '是')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.reduceMedicine === '是' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>是</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.reduceMedicine = '否')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.reduceMedicine === '否' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>否</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>10.现在正在遭受的不适【多选题】</text>\r\n\t\t\t\t\t\t\t<view class=\"checkbox-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"checkbox-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"checkbox-item\" @click=\"toggleUncomfortable('comfort')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"checkbox-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.discomfort && formData.discomfort.comfort }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>没有不适</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"checkbox-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"checkbox-item\" @click=\"toggleUncomfortable('fatigue')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"checkbox-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.discomfort && formData.discomfort.fatigue }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>乏力</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"checkbox-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"checkbox-item\" @click=\"toggleUncomfortable('syncope')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"checkbox-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.discomfort && formData.discomfort.syncope }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>晕厥</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"checkbox-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"checkbox-item\" @click=\"toggleUncomfortable('dyspnea')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"checkbox-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.discomfort && formData.discomfort.dyspnea }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>气促，呼吸困难</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"checkbox-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"checkbox-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"toggleUncomfortable('anginaPectoris')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"checkbox-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.discomfort && formData.discomfort.anginaPectoris }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>胸闷胸痛</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"checkbox-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"checkbox-item\" @click=\"toggleUncomfortable('else')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"checkbox-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.discomfort && formData.discomfort.else }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>其他</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>11.自理情况</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.selfCareStatus = '能够自理')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.selfCareStatus === '能够自理' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>能够自理</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.selfCareStatus = '穿衣，洗澡部分困难')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.selfCareStatus === '穿衣、洗澡部分困难' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>穿衣、洗澡部分困难</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.selfCareStatus = '不能自行穿衣，洗澡')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.selfCareStatus === '不能自行穿衣、洗澡' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>不能自行穿衣、洗澡</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"form-item heart-function-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>12.心功能分级</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.heartFunction = 'Ⅰ级（日常活动不受限）')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.heartFunction === 'Ⅰ级（日常活动不受限）' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>Ⅰ级（日常活动不受限）</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.heartFunction = 'Ⅱ级（轻度受限）')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.heartFunction === 'Ⅱ级（轻度受限）' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>Ⅱ级（轻度受限）</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.heartFunction = 'Ⅲ级（明显受限）')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.heartFunction === 'Ⅲ级（明显受限）' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>Ⅲ级（明显受限）</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.heartFunction = 'Ⅳ级（不能从事任何体力活动）')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.heartFunction === 'Ⅳ级（不能从事任何体力活动）' }\"></view>\r\n\t\t\t\t\t\t\t\t\t\t<text>Ⅳ级（不能从事任何体力活动）</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>13.自我评定</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.selfAppraisal = '自我监测好')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.selfAppraisal === '自我监测好' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>自我监测好</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.selfAppraisal = '自我监测一般')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.selfAppraisal === '自我监测一般' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>自我监测一般</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.selfAppraisal = '自我监测不好')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.selfAppraisal === '自我监测不好' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>自我监测不好</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"table2\">\r\n\r\n\t\t\t\t\t<view class=\"form-section\">\r\n\t\t\t\t\t\t<view class=\"form-header\">\r\n\t\t\t\t\t\t\t<text class=\"form-title\">表2：明尼苏达心力衰竭生活治疗调查表</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>请填写您的心力衰竭对您每天生活的影响</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>14.1.您的踝关节或腿出现肿胀？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.swelling = '无')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.swelling === '无' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>无</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.swelling = '很轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.swelling === '很轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.swelling = '轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.swelling === '轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.swelling = '中')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.swelling === '中' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>中</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.swelling = '重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.swelling === '重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.swelling = '很重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.swelling === '很重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>14.2.使您在被迫白天坐下或躺下休息？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.forcedRest = '无')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.forcedRest === '无' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>无</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.forcedRest = '很轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.forcedRest === '很轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.forcedRest = '轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.forcedRest === '轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.forcedRest = '中')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.forcedRest === '中' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>中</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.forcedRest = '重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.forcedRest == '重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.forcedRest = '很重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.forcedRest === '很重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>14.3.使您在步行或上楼梯困难？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.dysbasia = '无')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.dysbasia === '无' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>无</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.dysbasia = '很轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.dysbasia === '很轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.dysbasia = '轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.dysbasia === '轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.dysbasia = '中')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.dysbasia === '中' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>中</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.dysbasia = '重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.dysbasia == '重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.dysbasia = '很重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.dysbasia === '很重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>14.4.使您在家中或院子里工作困难？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.workWithDifficulty = '无')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.workWithDifficulty === '无' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>无</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.workWithDifficulty = '很轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.workWithDifficulty === '很轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.workWithDifficulty = '轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.workWithDifficulty === '轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.workWithDifficulty = '中')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.workWithDifficulty === '中' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>中</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.workWithDifficulty = '重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.workWithDifficulty == '重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.workWithDifficulty = '很重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.workWithDifficulty === '很重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>14.5.使您离开家出门困难？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.goOutWithDifficulty = '无')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.goOutWithDifficulty === '无' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>无</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.goOutWithDifficulty = '很轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.goOutWithDifficulty === '很轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.goOutWithDifficulty = '轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.goOutWithDifficulty === '轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.goOutWithDifficulty = '中')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.goOutWithDifficulty === '中' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>中</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.goOutWithDifficulty = '重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.goOutWithDifficulty == '重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.goOutWithDifficulty = '很重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.goOutWithDifficulty === '很重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>14.6.使您晚上睡眠状况困难？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.sleepWithDifficulty = '无')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.sleepWithDifficulty === '无' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>无</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.sleepWithDifficulty = '很轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.sleepWithDifficulty === '很轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.sleepWithDifficulty = '轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.sleepWithDifficulty === '轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.sleepWithDifficulty = '中')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.sleepWithDifficulty === '中' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>中</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.sleepWithDifficulty = '重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.sleepWithDifficulty == '重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.sleepWithDifficulty = '很重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.sleepWithDifficulty === '很重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>14.7.使您和您的朋友或家人或家人一起做事困难？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.doingWithDifficulty = '无')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.doingWithDifficulty === '无' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>无</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.doingWithDifficulty = '很轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.doingWithDifficulty === '很轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.doingWithDifficulty = '轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.doingWithDifficulty === '轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.doingWithDifficulty = '中')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.doingWithDifficulty === '中' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>中</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.doingWithDifficulty = '重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.doingWithDifficulty == '重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.doingWithDifficulty = '很重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.doingWithDifficulty === '很重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>14.8.使您做获得收入的工作困难？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.incomeWithDifficulty = '无')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.incomeWithDifficulty === '无' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>无</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.incomeWithDifficulty = '很轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.incomeWithDifficulty === '很轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.incomeWithDifficulty = '轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.incomeWithDifficulty === '轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.incomeWithDifficulty = '中')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.incomeWithDifficulty === '中' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>中</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.incomeWithDifficulty = '重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.incomeWithDifficulty == '重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.incomeWithDifficulty = '很重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.incomeWithDifficulty === '很重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>14.9.使您的做娱乐、体育活动或喜好的事情困难？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.entertainmentWithDifficulty = '无')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.entertainmentWithDifficulty === '无' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>无</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.entertainmentWithDifficulty = '很轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.entertainmentWithDifficulty === '很轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.entertainmentWithDifficulty = '轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.entertainmentWithDifficulty === '轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.entertainmentWithDifficulty = '中')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.entertainmentWithDifficulty === '中' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>中</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.entertainmentWithDifficulty = '重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.entertainmentWithDifficulty == '重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.entertainmentWithDifficulty = '很重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.entertainmentWithDifficulty === '很重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>14.10.使您的性生活困难？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.sexWithDifficulty = '无')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.sexWithDifficulty === '无' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>无</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.sexWithDifficulty = '很轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.sexWithDifficulty === '很轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.sexWithDifficulty = '轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.sexWithDifficulty === '轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.sexWithDifficulty = '中')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.sexWithDifficulty === '中' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>中</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.sexWithDifficulty = '重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.sexWithDifficulty == '重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.sexWithDifficulty = '很重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.sexWithDifficulty === '很重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>14.11.使您对您喜欢的事物也吃的很少？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.eatLess = '无')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.eatLess === '无' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>无</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.eatLess = '很轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.eatLess === '很轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.eatLess = '轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.eatLess === '轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.eatLess = '中')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.eatLess === '中' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>中</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.eatLess = '重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.eatLess == '重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.eatLess = '很重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.eatLess === '很重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>14.12.使您有呼吸困难？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.breathWithDifficulty = '无')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.breathWithDifficulty === '无' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>无</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.breathWithDifficulty = '很轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.breathWithDifficulty === '很轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.breathWithDifficulty = '轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.breathWithDifficulty === '轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.breathWithDifficulty = '中')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.breathWithDifficulty === '中' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>中</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.breathWithDifficulty = '重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.breathWithDifficulty == '重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.breathWithDifficulty = '很重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.breathWithDifficulty === '很重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>14.13.使您疲劳、乏力、或没有精力？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.tired = '无')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.tired === '无' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>无</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.tired = '很轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.tired === '很轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.tired = '轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.tired === '轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.tired = '中')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.tired === '中' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>中</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.tired = '重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.tired == '重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.tired = '很重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.tired === '很重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>14.14.使您在医院住院？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.inHospital = '无')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.inHospital === '无' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>无</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.inHospital = '很轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.inHospital === '很轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.inHospital = '轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.inHospital === '轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.inHospital = '中')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.inHospital === '中' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>中</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.inHospital = '重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.inHospital == '重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.inHospital = '很重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.inHospital === '很重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>14.15.使您因就医花钱？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.spendMoney = '无')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.spendMoney === '无' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>无</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.spendMoney = '很轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.spendMoney === '很轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.spendMoney = '轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.spendMoney === '轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.spendMoney = '中')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.spendMoney === '中' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>中</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.spendMoney = '重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.spendMoney == '重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.spendMoney = '很重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.spendMoney === '很重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>14.16.使您因为治疗出现了副作用？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.sideEffect = '无')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.sideEffect === '无' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>无</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.sideEffect = '很轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.sideEffect === '很轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.sideEffect = '轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.sideEffect === '轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.sideEffect = '中')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.sideEffect === '中' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>中</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.sideEffect = '重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.sideEffect == '重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.sideEffect = '很重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.sideEffect === '很重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>14.17.使您觉得自己是家人或朋友的负担？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.beBburden = '无')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.beBburden === '无' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>无</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.beBburden = '很轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.beBburden === '很轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.beBburden = '轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.beBburden === '轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.beBburden = '中')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.beBburden === '中' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>中</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.beBburden = '重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.beBburden == '重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.beBburden = '很重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.beBburden === '很重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>14.18.使您觉得不能控制自己的生活？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.uncontrolled = '无')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.uncontrolled === '无' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>无</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.uncontrolled = '很轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.uncontrolled === '很轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.uncontrolled = '轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.uncontrolled === '轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.uncontrolled = '中')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.uncontrolled === '中' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>中</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.uncontrolled = '重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.uncontrolled == '重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.uncontrolled = '很重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.uncontrolled === '很重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>14.19.使得您焦虑？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.anxiety = '无')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.anxiety === '无' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>无</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.anxiety = '很轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.anxiety === '很轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.anxiety = '轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.anxiety === '轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.anxiety = '中')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.anxiety === '中' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>中</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.anxiety = '重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.anxiety == '重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.anxiety = '很重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.anxiety === '很重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>14.20.使您不能集中注意力或记忆力下降？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.decreasedMemory = '无')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.decreasedMemory === '无' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>无</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.decreasedMemory = '很轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.decreasedMemory === '很轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.decreasedMemory = '轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.decreasedMemory === '轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.decreasedMemory = '中')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.decreasedMemory === '中' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>中</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.decreasedMemory = '重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.decreasedMemory == '重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.decreasedMemory = '很重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.decreasedMemory === '很重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>14.21.使您情绪低落？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.depression = '无')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.depression === '无' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>无</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.depression = '很轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.depression === '很轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.depression = '轻')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.depression === '轻' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>轻</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.depression = '中')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.depression === '中' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>中</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.depression = '重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.depression == '重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.depression = '很重')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.depression === '很重' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>很重</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"table3\">\r\n\t\t\t\t\t<view class=\"form-section\">\r\n\t\t\t\t\t\t<view class=\"form-header\">\r\n\t\t\t\t\t\t\t<text class=\"form-title\">表3：心力衰竭患者自我护理指数</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"form-label\">第一部分：</text>\r\n\t\t\t\t\t\t\t\t<text class=\"form-label\">以下是心衰患者常常采取的自我照护行为。您通常多久做一次下列事情？</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>15.尽量避免生病（例如勤洗手）？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.preventIllness = '从不')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.preventIllness === '从不' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>从不</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.preventIllness = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.preventIllness === '偶尔' }\"></view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.preventIllness = '有时')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.preventIllness === '有时' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>有时</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.preventIllness = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.preventIllness === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.preventIllness = '总是')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.preventIllness === '总是' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>总是</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>16.锻炼身体（例如快步走、爬楼梯）？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.exercise = '从不')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.exercise === '从不' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>从不</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.exercise = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.exercise === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.exercise = '有时')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.exercise === '有时' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>有时</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.exercise = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.exercise === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.exercise = '总是')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.exercise === '总是' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>总是</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>17.低盐饮食？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.lowSalt = '从不')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.lowSalt === '从不' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>从不</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.lowSalt = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.lowSalt === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.lowSalt = '有时')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.lowSalt === '有时' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>有时</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.lowSalt = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.lowSalt === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.lowSalt = '总是')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.lowSalt === '总是' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>总是</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>18.定期看医生？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.seeDoctor = '从不')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.seeDoctor === '从不' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>从不</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.seeDoctor = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.seeDoctor === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.seeDoctor = '有时')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.seeDoctor === '有时' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>有时</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.seeDoctor = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.seeDoctor === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.seeDoctor = '总是')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.seeDoctor === '总是' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>总是</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>19.吃医生开的药，不漏吃少吃？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.doctorMedicine = '从不')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.doctorMedicine === '从不' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>从不</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.doctorMedicine = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.doctorMedicine === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.doctorMedicine = '有时')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.doctorMedicine === '有时' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>有时</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.doctorMedicine = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.doctorMedicine === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.doctorMedicine = '总是')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.doctorMedicine === '总是' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>总是</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>20.外出就餐选择低盐饮食？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.lowSaltGoOut = '从不')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.lowSaltGoOut === '从不' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>从不</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.lowSaltGoOut = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.lowSaltGoOut === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.lowSaltGoOut = '有时')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.lowSaltGoOut === '有时' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>有时</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.lowSaltGoOut = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.lowSaltGoOut === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.lowSaltGoOut = '总是')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.lowSaltGoOut === '总是' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>总是</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>21.确保每年接种疫苗?</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.vaccinated = '从不')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.vaccinated === '从不' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>从不</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.vaccinated = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.vaccinated === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.vaccinated = '有时')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.vaccinated === '有时' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>有时</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.vaccinated = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.vaccinated === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.vaccinated = '总是')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.vaccinated === '总是' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>总是</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>22.拜访亲朋好友时要求吃低盐食品?</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.visitFriends = '从不')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.visitFriends === '从不' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>从不</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.visitFriends = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.visitFriends === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.visitFriends = '有时')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.visitFriends === '有时' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>有时</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.visitFriends = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.visitFriends === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.visitFriends = '总是')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.visitFriends === '总是' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>总是</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>23.采取一些措施提醒自己吃药（如药盒、闹钟等）？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.takeMedicine = '从不')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.takeMedicine === '从不' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>从不</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.takeMedicine = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.takeMedicine === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.takeMedicine = '有时')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.takeMedicine === '有时' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>有时</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.takeMedicine = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.takeMedicine === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.takeMedicine = '总是')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.takeMedicine === '总是' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>总是</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>24.向医护人员咨询自己吃的药</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.consultMedicine = '从不')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.consultMedicine === '从不' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>从不</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.consultMedicine = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.consultMedicine === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.consultMedicine = '有时')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.consultMedicine === '有时' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>有时</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.consultMedicine = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.consultMedicine === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.consultMedicine = '总是')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.consultMedicine === '总是' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>总是</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"form-label\">第二部分：</text>\r\n\t\t\t\t\t\t\t\t<text class=\"form-label\">以下是心衰患者常常自我监测的变化。您通常多久做一次下列事情？</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>25.每天称体重？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.weighOneself = '从不')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.weighOneself === '从不' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>从不</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.weighOneself = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.weighOneself === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.weighOneself = '有时')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.weighOneself === '有时' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>有时</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.weighOneself = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.weighOneself === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.weighOneself = '总是')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.weighOneself === '总是' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>总是</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>26.关注自身感觉的变化？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.focusOneself = '从不')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.focusOneself === '从不' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>从不</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.focusOneself = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.focusOneself === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.focusOneself = '有时')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.focusOneself === '有时' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>有时</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.focusOneself = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.focusOneself === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.focusOneself = '总是')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.focusOneself === '总是' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>总是</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>27.查询药物的副作用？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.checkTheSideEffects = '从不')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.checkTheSideEffects === '从不' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>从不</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.checkTheSideEffects = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.checkTheSideEffects === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.checkTheSideEffects = '有时')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.checkTheSideEffects === '有时' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>有时</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.checkTheSideEffects = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.checkTheSideEffects === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.checkTheSideEffects = '总是')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.checkTheSideEffects === '总是' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>总是</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>28.从事日常活动，关注自己是否比平常更容易疲惫？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.beProneToFatigue = '从不')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.beProneToFatigue === '从不' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>从不</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.beProneToFatigue = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.beProneToFatigue === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.beProneToFatigue = '有时')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.beProneToFatigue === '有时' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>有时</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.beProneToFatigue = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.beProneToFatigue === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.beProneToFatigue = '总是')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.beProneToFatigue === '总是' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>总是</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>29.向医护人员咨询自己的身体状况？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.consultWorkers = '从不')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.consultWorkers === '从不' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>从不</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.consultWorkers = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.consultWorkers === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.consultWorkers = '有时')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.consultWorkers === '有时' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>有时</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.consultWorkers = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.consultWorkers === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.consultWorkers = '总是')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.consultWorkers === '总是' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>总是</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>30.密切监测有没有症状出现？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.monitorSymptoms = '从不')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.monitorSymptoms === '从不' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>从不</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.monitorSymptoms = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.monitorSymptoms === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.monitorSymptoms = '有时')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.monitorSymptoms === '有时' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>有时</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.monitorSymptoms = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.monitorSymptoms === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.monitorSymptoms = '总是')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.monitorSymptoms === '总是' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>总是</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>31.检查脚踝有没有水肿？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.ankleEdema = '从不')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.ankleEdema === '从不' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>从不</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.ankleEdema = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.ankleEdema === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.ankleEdema = '有时')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.ankleEdema === '有时' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>有时</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.ankleEdema = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.ankleEdema === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.ankleEdema = '总是')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.ankleEdema === '总是' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>总是</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text\r\n\t\t\t\t\t\t\t\t\tclass=\"required\">*</text>32.在进行洗澡、穿衣等日常活动时检查是否有气短、喘不上气的情况？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.shortnessOfBreath = '从不')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.shortnessOfBreath === '从不' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>从不</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.shortnessOfBreath = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.shortnessOfBreath === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.shortnessOfBreath = '有时')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.shortnessOfBreath === '有时' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>有时</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.shortnessOfBreath = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.shortnessOfBreath === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.shortnessOfBreath = '总是')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.shortnessOfBreath === '总是' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>总是</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>33.记录自己的症状？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.checkOwnSymptoms = '从不')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.checkOwnSymptoms === '从不' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>从不</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.checkOwnSymptoms = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.checkOwnSymptoms === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.checkOwnSymptoms = '有时')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.checkOwnSymptoms === '有时' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>有时</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.checkOwnSymptoms = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.checkOwnSymptoms === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.checkOwnSymptoms = '总是')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.checkOwnSymptoms === '总是' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>总是</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"form-label\">请回想最近一次您出现心衰症状的情形，回答下列问题（在符合您情况的选项上填入。）</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>34.您多快意识到自己出现了症状？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.timelyAwareness = '还没出现过')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.timelyAwareness === '还没出现过' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>还没出现过</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.timelyAwareness = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.timelyAwareness === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.timelyAwareness = '不快')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.timelyAwareness === '不快' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>不快</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.timelyAwareness = '稍微快一点')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.timelyAwareness === '稍微快一点' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>稍微快一点</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.timelyAwareness = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.timelyAwareness === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.timelyAwareness = '非常迅速')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.timelyAwareness === '非常迅速' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>非常迅速</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.timelyAwareness = '没有识别出症状')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.timelyAwareness === '没有识别出症状' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>没有识别出症状</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>35.您多快知道这个症状是由心衰引起的？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.knowInTime = '还没出现过')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.knowInTime === '还没出现过' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>还没出现过</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.knowInTime = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.knowInTime === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.knowInTime = '不快')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.knowInTime === '不快' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>不快</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.knowInTime = '稍微快一点')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.knowInTime === '稍微快一点' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>稍微快一点</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.knowInTime = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.knowInTime === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.knowInTime = '非常迅速')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.knowInTime === '非常迅速' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>非常迅速</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.knowInTime = '没有识别出症状')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.knowInTime === '没有识别出症状' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>没有识别出症状</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"form-label\">第三部分：</text>\r\n\t\t\t\t\t\t\t\t<text class=\"form-label\">以下是心衰患者用来控制症状的行为。当出现症状时，您有多大可能采取下列措施？（请选择在每一题符合您情况的选项）</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>36.进一步减少当天饮食中的盐？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.reduceSalt = '不会')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.reduceSalt === '不会' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>不会</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.reduceSalt = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.reduceSalt === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.reduceSalt = '可能会')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.reduceSalt === '可能会' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>可能会</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.reduceSalt = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.reduceSalt === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.reduceSalt = '肯定会')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.reduceSalt === '肯定会' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>肯定会</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>37.减少液体（水、汤等）的摄入量？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.reduceFluids = '不会')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.reduceFluids === '不会' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>不会</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.reduceFluids = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.reduceFluids === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.reduceFluids = '可能会')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.reduceFluids === '可能会' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>可能会</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.reduceFluids = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.reduceFluids === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.reduceFluids = '肯定会')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.reduceFluids === '肯定会' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>肯定会</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>38.吃药？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.takeMedicine2 = '不会')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.takeMedicine2 === '不会' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>不会</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.takeMedicine2 = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.takeMedicine2 === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.takeMedicine2 = '可能会')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.takeMedicine2 === '可能会' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>可能会</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.takeMedicine2 = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.takeMedicine2 === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.takeMedicine2 = '肯定会')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.takeMedicine2 === '肯定会' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>肯定会</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>39.电话咨询医护人员获取指导？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.consultByPhone = '不会')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.consultByPhone === '不会' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>不会</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.consultByPhone = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.consultByPhone === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.consultByPhone = '可能会')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.consultByPhone === '可能会' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>可能会</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.consultByPhone = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.consultByPhone === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.consultByPhone = '肯定会')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.consultByPhone === '肯定会' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>肯定会</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>40.电话咨询寻求家人或朋友的帮助？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.seekHelp = '不会')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.seekHelp === '不会' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>不会</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.seekHelp = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.seekHelp === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.seekHelp = '可能会')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.seekHelp === '可能会' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>可能会</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.seekHelp = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.seekHelp === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.seekHelp = '肯定会')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.seekHelp === '肯定会' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>肯定会</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>41.试着找出自己出现症状的原因？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.tryToFind = '不会')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.tryToFind === '不会' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>不会</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.tryToFind = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.tryToFind === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.tryToFind = '可能会')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.tryToFind === '可能会' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>可能会</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.tryToFind = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.tryToFind === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.tryToFind = '肯定会')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.tryToFind === '肯定会' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>肯定会</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>42.减少活动直到感觉好一些？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.reduceActivity = '不会')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.reduceActivity === '不会' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>不会</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.reduceActivity = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.reduceActivity === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.reduceActivity = '可能会')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.reduceActivity === '可能会' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>可能会</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.reduceActivity = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.reduceActivity === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.reduceActivity = '肯定会')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.reduceActivity === '肯定会' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>肯定会</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>43.您采取的措施有让您感觉好一些吗？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.feelBetter = '不会')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.feelBetter === '不会' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>不会</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.feelBetter = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.feelBetter === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.feelBetter = '可能会')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.feelBetter === '可能会' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>可能会</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.feelBetter = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.feelBetter === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.feelBetter = '肯定会')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.feelBetter === '肯定会' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>肯定会</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"form-label\">第四部分：</text>\r\n\t\t\t\t\t\t\t\t<text class=\"form-label\">一般来说，您有多大信心能完成以下项目:（在每一题符合您情况的选项）</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>44.保持身体状况稳定且没有症状？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.keepStable = '没有信心')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.keepStable === '没有信心' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>没有信心</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.keepStable = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.keepStable === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.keepStable = '有点信心')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.keepStable === '有点信心' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>有点信心</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.keepStable = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.keepStable === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.keepStable = '非常有信心')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.keepStable === '非常有信心' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>非常有信心</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>45.遵从医护人员为您制订的治疗方案？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.followPlan = '没有信心')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.followPlan === '没有信心' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>没有信心</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.followPlan = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.followPlan === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.followPlan = '有点信心')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.followPlan === '有点信心' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>有点信心</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.followPlan = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.followPlan === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.followPlan = '非常有信心')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.followPlan === '非常有信心' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>非常有信心</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>46.在有困难时依然坚持遵从治疗方案？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.followPlanWithDifficluty = '没有信心')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.followPlanWithDifficluty === '没有信心' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>没有信心</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.followPlanWithDifficluty = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.followPlanWithDifficluty === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.followPlanWithDifficluty = '有点信心')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.followPlanWithDifficluty === '有点信心' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>有点信心</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.followPlanWithDifficluty = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.followPlanWithDifficluty === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.followPlanWithDifficluty = '非常有信心')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.followPlanWithDifficluty === '非常有信心' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>非常有信心</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>47.定期监测自己的身体状况？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.regularMonitor = '没有信心')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.regularMonitor === '没有信心' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>没有信心</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.regularMonitor = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.regularMonitor === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.regularMonitor = '有点信心')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.regularMonitor === '有点信心' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>有点信心</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.regularMonitor = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.regularMonitor === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.regularMonitor = '非常有信心')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.regularMonitor === '非常有信心' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>非常有信心</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>48.在有困难时依然坚持监测自己的身体状况？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.monitorWithDifficulty = '没有信心')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.monitorWithDifficulty === '没有信心' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>没有信心</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.monitorWithDifficulty = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.monitorWithDifficulty === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.monitorWithDifficulty = '有点信心')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.monitorWithDifficulty === '有点信心' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>有点信心</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.monitorWithDifficulty = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.monitorWithDifficulty === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.monitorWithDifficulty = '非常有信心')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.monitorWithDifficulty === '非常有信心' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>非常有信心</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>49.意识到自己身体健康状况出现变化？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.realizeChange = '没有信心')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.realizeChange === '没有信心' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>没有信心</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.realizeChange = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.realizeChange === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.realizeChange = '有点信心')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.realizeChange === '有点信心' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>有点信心</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.realizeChange = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.realizeChange === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.realizeChange = '非常有信心')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.realizeChange === '非常有信心' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>非常有信心</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>50.评估自己的症状是否重要或是否需要引起重视？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.assessSymptom = '没有信心')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.assessSymptom === '没有信心' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>没有信心</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.assessSymptom = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.assessSymptom === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.assessSymptom = '有点信心')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.assessSymptom === '有点信心' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>有点信心</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.assessSymptom = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'checked': formData.assessSymptom === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.assessSymptom = '非常有信心')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.assessSymptom === '非常有信心' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>非常有信心</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>51.采取一些措施来缓解自己的症状？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.relieveSymptom = '没有信心')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.relieveSymptom === '没有信心' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>没有信心</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.relieveSymptom = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.relieveSymptom === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.relieveSymptom = '有点信心')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.relieveSymptom === '有点信心' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>有点信心</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.relieveSymptom = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.relieveSymptom === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.relieveSymptom = '非常有信心')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.relieveSymptom === '非常有信心' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>非常有信心</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text\r\n\t\t\t\t\t\t\t\t\tclass=\"required\">*</text>52.在有困难时依然坚持寻找一个能够缓解自己症状的治疗方法？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.relieveWithDifficulty = '没有信心')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.relieveWithDifficulty === '没有信心' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>没有信心</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.relieveWithDifficulty = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.relieveWithDifficulty === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.relieveWithDifficulty = '有点信心')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.relieveWithDifficulty === '有点信心' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>有点信心</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.relieveWithDifficulty = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.relieveWithDifficulty === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.relieveWithDifficulty = '非常有信心')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.relieveWithDifficulty === '非常有信心' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>非常有信心</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\"><text class=\"required\">*</text>53.判断所采取的缓解症状的方法是否有效？</text>\r\n\t\t\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.judgmentMethod = '没有信心')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.judgmentMethod === '没有信心' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>没有信心</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.judgmentMethod = '偶尔')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.judgmentMethod === '偶尔' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>偶尔</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.judgmentMethod = '有点信心')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.judgmentMethod === '有点信心' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>有点信心</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"(formData.judgmentMethod = '经常')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.judgmentMethod === '经常' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>经常</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio-row\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-item\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"(formData.judgmentMethod = '非常有信心')\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"radio-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'checked': formData.judgmentMethod === '非常有信心' }\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text>非常有信心</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"form-label\">再次感谢您的支持！如果您符合我们条件，我们会尽快与您取得联系，期待您的加入！</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 提交按钮 -->\r\n                <view class=\"submit-section\">\r\n                    <button class=\"submit-btn\" @click=\"submitForm\">{{ mode === 'add' ? '提交' : '保存' }}</button>\r\n                </view>\r\n\t\t\t</view>\r\n\t\t</scroll-view>\r\n\t</PageLayout>\r\n\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { ref, computed, onMounted, watch } from 'vue'\r\nimport { fastLerp } from 'zrender/lib/tool/color'\r\nimport { useUserStore } from '@/store/user'\r\n\r\nconst userStore = useUserStore()\r\n\r\ndefineOptions({\r\n\tname: 'monitorForm',\r\n})\r\n// 获取页面参数\r\nconst query = ref<any>({})\r\nonMounted(() => {\r\n    const pages = getCurrentPages()\r\n    const currentPage = pages[pages.length - 1]\r\n    query.value = (currentPage as any).options || {}\r\n\t // 如果是查看模式，加载数据\r\n    if (query.value.mode === 'view' && query.value.id) {\r\n        loadFormData(query.value.id)\r\n    }\r\n\r\n    // 自动填充患者姓名\r\n    if (userStore.userInfo.realname) {\r\n        formData.value.userName = userStore.userInfo.realname\r\n    }\r\n\r\n})\r\n\r\n// 表单模式：add 或 view\r\nconst mode = computed(() => query.value.mode || 'add')\r\n\r\n// 表单数据\r\nconst formData = ref({\r\n\tuserName: '',\r\n\tlocation: '',\r\n\tweight: '',\r\n\tbloodPressure: '',\r\n\theartRate: '',\r\n\tsmock: '',\r\n\tmedicineOnTime: '',\r\n\tmissingMedicine: '',\r\n\treduceMedicine: '',\r\n\tdiscomfort: {\r\n\t\tcomfort: false,\r\n\t\tfatigue: false,\r\n\t\tsyncope: false,\r\n\t\tdyspnea: false,\r\n\t\tanginaPectoris: false,\r\n\t\telse: false,\r\n\t},\r\n\tselfCareStatus: '',\r\n\theartFunction: '',\r\n\tselfAppraisal: '',\r\n\r\n\tswelling: '',\r\n\tforcedRest: '',\r\n\tdysbasia: '',\r\n\tworkWithDifficulty: '',\r\n\tgoOutWithDifficulty: '',\r\n\tsleepWithDifficulty: '',\r\n\tdoingWithDifficulty: '',\r\n\tincomeWithDifficulty: '',\r\n\tentertainmentWithDifficulty: '',\r\n\tsexWithDifficulty: '',\r\n\teatLess: '',\r\n\tbreathWithDifficulty: '',\r\n\ttired: '',\r\n\tinHospital: '',\r\n\tspendMoney: '',\r\n\tsideEffect: '',\r\n\tbeBburden: '',\r\n\tuncontrolled: '',\r\n\tanxiety:'',\r\n\tdecreasedMemory: '',\r\n\tdepression: '',\r\n\r\n\tpreventIllness: '',\r\n\texercise: '',\r\n\tlowSalt: '',\r\n\tseeDoctor: '',\r\n\tdoctorMedicine: '',\r\n\tlowSaltGoOut: '',\r\n\tvaccinated: '',\r\n\tvisitFriends: '',\r\n\ttakeMedicine: '',\r\n\tconsultMedicine: '',\r\n\r\n\tweighOneself: '',\r\n\tfocusOneself: '',\r\n\tcheckTheSideEffects: '',\r\n\tbeProneToFatigue: '',\r\n\tconsultWorkers: '',\r\n\tmonitorSymptoms: '',\r\n\tankleEdema: '',\r\n\tshortnessOfBreath: '',\r\n\tcheckOwnSymptoms: '',\r\n\r\n\ttimelyAwareness: '',\r\n\tknowInTime: '',\r\n\r\n\treduceSalt: '',\r\n\treduceFluids: '',\r\n\ttakeMedicine2: '',\r\n\tconsultByPhone: '',\r\n\tseekHelp: '',\r\n\ttryToFind: '',\r\n\treduceActivity: '',\r\n\tfeelBetter: '',\r\n\r\n\tkeepStable: '',\r\n\tfollowPlan: '',\r\n\tfollowPlanWithDifficluty: '',\r\n\tregularMonitor: '',\r\n\tmonitorWithDifficulty: '',\r\n\trealizeChange: '',\r\n\tassessSymptom: '',\r\n\trelieveSymptom: '',\r\n\trelieveWithDifficulty: '',\r\n\tjudgmentMethod: '',\r\n})\r\n// 加载表单数据（根据ID获取数据）\r\nconst loadFormData = (id: string) => {\r\n\t// 这里应该是从API获取数据的逻辑\r\n\t// 显示加载提示\r\n\tuni.showLoading({\r\n\t    title: '加载中...'\r\n\t})\r\n\t\r\n\t// 根据您提供的API路径格式构建URL\r\n\tconst url = `${import.meta.env.VITE_SERVER_BASEURL}/patient/${id}/monitoring`\r\n\tconsole.log('请求详情URL:', url)\r\n\t\r\n\tuni.request({\r\n\t    url,\r\n\t    method: 'GET',\r\n\t    success: (res: any) => {\r\n\t        const response = res.data\r\n\t        console.log('详情接口返回数据:', response.result.details.doingWithDifficulty)\r\n\t\r\n\t        if (response && response.code === 200) {\r\n\t            // 解析返回的详细数据并填充表单\r\n\t            if (response.result) {\r\n\t                formData.value = {\r\n\t                    bloodPressure: response.result.details.bloodPressure || '',\r\n\t                    heartRate: response.result.details.heartRate || '',\r\n\t                    weight: response.result.details.weight || '',\r\n\t\t\t\t\t\tuserName:response.result.userName || '',\r\n\t\t\t\t\t\tlocation:response.result.details.location ||'',\r\n\t\t\t\t\t\tsmock:response.result.details.smock ||'',\r\n\t\t\t\t\t\tmedicineOnTime:response.result.details.medicineOnTime ||'',\r\n\t\t\t\t\t\tmissingMedicine:response.result.details.missingMedicine ||'',\r\n\t\t\t\t\t\treduceMedicine:response.result.details.reduceMedicine ||'',\r\n\t\t\t\t\t\tdiscomfort:response.result.details.discomfort ||'',\r\n\t\t\t\t\t\tselfCareStatus:response.result.details.selfCareStatus ||'',\r\n\t\t\t\t\t\theartFunction:response.result.details.heartFunction ||'',\r\n\t\t\t\t\t\tselfAppraisal:response.result.details.selfAppraisal ||'',\r\n\t\t\t\t\t\tswelling:response.result.details.swelling ||'',\r\n\t\t\t\t\t\tforcedRest:response.result.details.forcedRest ||'',\r\n\t\t\t\t\t\tdysbasia:response.result.details.dysbasia ||'',\r\n\t\t\t\t\t\tworkWithDifficulty:response.result.details.workWithDifficulty ||'',\r\n\t\t\t\t\t\tgoOutWithDifficulty:response.result.details.goOutWithDifficulty ||'',\r\n\t\t\t\t\t\tsleepWithDifficulty:response.result.details.sleepWithDifficulty ||'',\r\n\t\t\t\t\t\tdoingWithDifficulty:response.result.details.doingWithDifficulty ||'',\r\n\t\t\t\t\t\tincomeWithDifficulty:response.result.details.incomeWithDifficulty ||'',\r\n\t\t\t\t\t\tentertainmentWithDifficulty:response.result.details.entertainmentWithDifficulty ||'',\r\n\t\t\t\t\t\tsexWithDifficulty:response.result.details.sexWithDifficulty ||'',\r\n\t\t\t\t\t\teatLess:response.result.details.eatLess ||'',\r\n\t\t\t\t\t\tbreathWithDifficulty:response.result.details.breathWithDifficulty ||'',\r\n\t\t\t\t\t\ttired:response.result.details.tired ||'',\r\n\t\t\t\t\t\tinHospital:response.result.details.inHospital ||'',\r\n\t\t\t\t\t\tspendMoney:response.result.details.spendMoney ||'',\r\n\t\t\t\t\t\tsideEffect:response.result.details.sideEffect ||'',\r\n\t\t\t\t\t\tbeBburden:response.result.details.beBburden ||'',\r\n\t\t\t\t\t\tuncontrolled:response.result.details.uncontrolled ||'',\r\n\t\t\t\t\t\tanxiety:response.result.details.anxiety ||'',\r\n\t\t\t\t\t\tdecreasedMemory:response.result.details.decreasedMemory ||'',\r\n\t\t\t\t\t\tdepression:response.result.details.depression ||'',\r\n\t\t\t\t\t\tpreventIllness:response.result.details.preventIllness ||'',\r\n\t\t\t\t\t\texercise:response.result.details.exercise ||'',\r\n\t\t\t\t\t\tlowSalt:response.result.details.lowSalt ||'',\r\n\t\t\t\t\t\tseeDoctor:response.result.details.seeDoctor ||'',\r\n\t\t\t\t\t\tdoctorMedicine:response.result.details.doctorMedicine ||'',\r\n\t\t\t\t\t\tlowSaltGoOut:response.result.details.lowSaltGoOut ||'',\r\n\t\t\t\t\t\tvaccinated:response.result.details.vaccinated ||'',\r\n\t\t\t\t\t\tvisitFriends:response.result.details.visitFriends ||'',\r\n\t\t\t\t\t\ttakeMedicine:response.result.details.takeMedicine ||'',\r\n\t\t\t\t\t\tconsultMedicine:response.result.details.consultMedicine ||'',\r\n\t\t\t\t\t\tweighOneself:response.result.details.weighOneself ||'',\r\n\t\t\t\t\t\tfocusOneself:response.result.details.focusOneself ||'',\r\n\t\t\t\t\t\tcheckTheSideEffects:response.result.details.checkTheSideEffects ||'',\r\n\t\t\t\t\t\tbeProneToFatigue:response.result.details.beProneToFatigue ||'',\r\n\t\t\t\t\t\tconsultWorkers:response.result.details.consultWorkers ||'',\r\n\t\t\t\t\t\tmonitorSymptoms:response.result.details.monitorSymptoms ||'',\r\n\t\t\t\t\t\tankleEdema:response.result.details.ankleEdema ||'',\r\n\t\t\t\t\t\tshortnessOfBreath:response.result.details.shortnessOfBreath ||'',\r\n\t\t\t\t\t\tcheckOwnSymptoms:response.result.details.checkOwnSymptoms ||'',\r\n\t\t\t\t\t\ttimelyAwareness:response.result.details.timelyAwareness ||'',\r\n\t\t\t\t\t\tknowInTime:response.result.details.knowInTime ||'',\r\n\t\t\t\t\t\treduceSalt:response.result.details.reduceSalt ||'',\r\n\t\t\t\t\t\treduceFluids:response.result.details.reduceFluids ||'',\r\n\t\t\t\t\t\ttakeMedicine2:response.result.details.takeMedicine2 ||'',\r\n\t\t\t\t\t\tconsultByPhone:response.result.details.consultByPhone ||'',\r\n\t\t\t\t\t\tseekHelp:response.result.details.seekHelp ||'',\r\n\t\t\t\t\t\ttryToFind:response.result.details.tryToFind ||'',\r\n\t\t\t\t\t\treduceActivity:response.result.details.reduceActivity ||'',\r\n\t\t\t\t\t\tfeelBetter:response.result.details.feelBetter ||'',\r\n\t\t\t\t\t\tkeepStable:response.result.details.keepStable ||'',\r\n\t\t\t\t\t\tfollowPlan:response.result.details.followPlan ||'',\r\n\t\t\t\t\t\tfollowPlanWithDifficluty:response.result.details.followPlanWithDifficluty ||'',\r\n\t\t\t\t\t\tregularMonitor:response.result.details.regularMonitor ||'',\r\n\t\t\t\t\t\tmonitorWithDifficulty:response.result.details.monitorWithDifficulty ||'',\r\n\t\t\t\t\t\trealizeChange:response.result.details.realizeChange ||'',\r\n\t\t\t\t\t\tassessSymptom:response.result.details.assessSymptom ||'',\r\n\t\t\t\t\t\trelieveSymptom:response.result.details.relieveSymptom ||'',\r\n\t\t\t\t\t\trelieveWithDifficulty:response.result.details.relieveWithDifficulty ||'',\r\n\t\t\t\t\t\tjudgmentMethod:response.result.details.judgmentMethod ||''\r\n\t                }\r\n\t                console.log('解析后的详情数据:', formData.value)\r\n\t            } else {\r\n\t                console.log('没有找到详情数据')\r\n\t                uni.showToast({\r\n\t                    title: '未找到记录详情',\r\n\t                    icon: 'none'\r\n\t                })\r\n\t            }\r\n\t        } else {\r\n\t            uni.showToast({\r\n\t                title: response.msg || '获取详情失败',\r\n\t                icon: 'none'\r\n\t            })\r\n\t        }\r\n\t    },\r\n\t    fail: (err: any) => {\r\n\t        console.error('获取体征详情失败:', err)\r\n\t        uni.showToast({\r\n\t            title: '网络异常，请稍后重试',\r\n\t            icon: 'none'\r\n\t        })\r\n\t    },\r\n\t    complete: () => {\r\n\t        uni.hideLoading()\r\n\t    }\r\n\t})\r\n}\r\n// 处理多选题\r\nconst toggleUncomfortable = (discomfort: string) => {\r\n\tif (!formData.value.discomfort) {\r\n\t\tformData.value.discomfort = {\r\n\t\t\tcomfort: false,\r\n\t\t\tfatigue: false,\r\n\t\t\tsyncope: false,\r\n\t\t\tdyspnea: false,\r\n\t\t\tanginaPectoris: false,\r\n\t\t\telse: false,\r\n\t\t}\r\n\t}\r\n\r\n\t// 更新选中状态（切换）\r\n\tconst newStatus = !formData.value.discomfort[discomfort]\r\n\tformData.value.discomfort = {\r\n\t\t...formData.value.discomfort,\r\n\t\t[discomfort]: newStatus\r\n\t}\r\n\r\n}\r\n// 获取地理位置\r\nconst getLocation = () => {\r\n\t// 使用腾讯位置选择器\r\n\tuni.chooseLocation({\r\n\t\tsuccess: function (res) {\r\n\t\t\tconsole.log('位置名称：' + res.name);\r\n\t\t\tconsole.log('详细地址：' + res.address);\r\n\t\t\tconsole.log('纬度：' + res.latitude);\r\n\t\t\tconsole.log('经度：' + res.longitude);\r\n\r\n\t\t\t// 更新表单数据\r\n\t\t\tformData.value.location = res.address;\r\n\t\t},\r\n\t\tfail: function (err) {\r\n\t\t\tconsole.error('选择位置失败', err);\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '选择位置失败，请检查定位权限',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t});\r\n\t\t}\r\n\t});\r\n}\r\n\r\n// 提交表单\r\nconst submitForm = () => {\r\n\r\n\tconsole.log('提交表单数据:', formData.value)\r\n\r\n\t// 表单验证\r\n\tif (!formData.value.location) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请获取您的地理位置',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\r\n\tif (!formData.value.weight) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请输入体重',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\r\n\tif (!formData.value.bloodPressure) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请输入血压',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.heartRate) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请输入心率',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.smock) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否吸烟',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.medicineOnTime) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否按时服药',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.missingMedicine) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否漏服药',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.reduceMedicine) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否擅自减药',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\t\r\n\tif (!formData.value.discomfort || \r\n\t\t(!formData.value.discomfort.comfort && \r\n\t\t!formData.value.discomfort.fatigue && \r\n\t\t!formData.value.discomfort.syncope && \r\n\t\t!formData.value.discomfort.dyspnea && \r\n\t\t!formData.value.discomfort.anginaPectoris && \r\n\t\t!formData.value.discomfort.else)) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择现在正在遭受的不适',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.selfCareStatus) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择自理情况',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.heartFunction) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择心功能分级',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.selfAppraisal) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择自我评定',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\r\n\tif (!formData.value.swelling) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否您的踝关节或腿出现肿胀',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.forcedRest) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否使您在白天被迫坐下或躺下休息',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.dysbasia) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否使您在步行或上楼梯困难',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.workWithDifficulty) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否使您在家中或院子里工作困难',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.goOutWithDifficulty) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否使您离开家出门困难',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.sleepWithDifficulty) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否使您晚上睡眠状况困难',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.doingWithDifficulty) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否使您和您的朋友或家人一起做事困难',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.incomeWithDifficulty) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否使您做获得收入的工作困难',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.entertainmentWithDifficulty) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否使您的做娱乐、体育活动或喜好的事情困难',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.sexWithDifficulty) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否使您的性生活困难',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.eatLess) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否使您对您喜欢的事物也吃得很少',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.breathWithDifficulty) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否使您有呼吸困难',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.tired) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否使您疲劳、乏力、或没有精力',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.inHospital) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否使您在医院住院',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.spendMoney) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否使您因就医花钱',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.sideEffect) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否使您因为治疗出现了副作用',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.beBburden) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否使您觉得自己是家人或朋友的负担',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.uncontrolled) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否使您觉得不能控制自己的生活',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.anxiety) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否使得您焦虑',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.decreasedMemory) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否使您不能集中注意力或记忆力下降',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.depression) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否使您情绪低落',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\r\n\tif (!formData.value.preventIllness) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否尽量避免生病',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.exercise) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否锻炼身体',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.lowSalt) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否低盐饮食',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\r\n\tif (!formData.value.seeDoctor) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否定期看医生',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\r\n\tif (!formData.value.doctorMedicine) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否吃医生开的药，不漏吃少吃',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.lowSaltGoOut) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否外出就餐选择低盐饮食',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.vaccinated) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否确保每年接种疫苗',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.visitFriends) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否拜访亲朋好友时要求吃低盐食品',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.takeMedicine) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否采取措施提醒自己吃药',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.consultMedicine) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否向医护人员咨询自己吃的药',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\r\n\tif (!formData.value.weighOneself) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否每天称体重',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.focusOneself) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否关注自身感觉的变化',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.checkTheSideEffects) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否查询药物的副作用',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.beProneToFatigue) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择从事日常活动，关注自己是否比平常更容易疲惫',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.consultWorkers) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否向医护人员咨询自己的身体状况',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.monitorSymptoms) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否密切监测有没有症状出现',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.ankleEdema) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否检查脚踝有没有水肿',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.shortnessOfBreath) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择在进行洗澡、穿衣等日常活动时检查是否有气短、喘不上气的情况',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.checkOwnSymptoms) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择记录自己的症状',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\r\n\tif (!formData.value.timelyAwareness) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择多快意识到自己出现了症状',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.knowInTime) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择多快知道这个症状是由心衰引起的',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\r\n\tif (!formData.value.reduceSalt) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否进一步减少当天饮食中的盐',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.reduceFluids) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否减少液体的摄入量',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.takeMedicine2) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否吃药',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.consultByPhone) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否电话咨询医护人员获取指导',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.seekHelp) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否电话咨询寻求家人或朋友的帮助',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.tryToFind) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否试着找出自己出现症状的原因',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.reduceActivity) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否减少活动直到感觉好一些',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.feelBetter) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否您采取的措施有让您感觉好一些吗',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\r\n\tif (!formData.value.keepStable) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否保持身体状况稳定且没有症状',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.followPlan) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否遵从医护人员为您制订的治疗方案',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.followPlanWithDifficluty) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否在有困难时依然坚持遵从治疗方案',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.regularMonitor) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否定期监测自己的身体状况',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.monitorWithDifficulty) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否在有困难时依然坚持监测自己的身体状况',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.realizeChange) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否意识到自己身体健康状况出现变化',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.assessSymptom) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否评估自己的症状是否重要或是否需要引起重视',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.relieveSymptom) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否采取一些措施来缓解自己的症状',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.relieveWithDifficulty) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择是否在困难时以然坚持寻找一个能够缓解自己症状的治疗方法',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!formData.value.judgmentMethod) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '判断所采取的缓解症状的方法是否有效',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\r\n\t// 显示加载提示\r\n\tuni.showLoading({\r\n\t    title: mode.value === 'add' ? '提交中...' : '保存中...'\r\n\t})\r\n\tconst requestData = {\r\n\t\tuserName: formData.value.userName,\r\n\t\tlocation: formData.value.location,\r\n\t\tweight: formData.value.weight,\r\n\t\tbloodPressure: formData.value.bloodPressure,\r\n\t\theartRate: formData.value.heartRate,\r\n\t\tsmock: formData.value.smock,\r\n\t\tmedicineOnTime: formData.value.medicineOnTime,\r\n\t\tmissingMedicine: formData.value.missingMedicine,\r\n\t\treduceMedicine: formData.value.reduceMedicine,\r\n\t\tdiscomfort: formData.value.discomfort,\r\n\t\tselfCareStatus: formData.value.selfCareStatus,\r\n\t\theartFunction: formData.value.heartFunction,\r\n\t\tselfAppraisal: formData.value.selfAppraisal,\r\n\r\n\t\tswelling: formData.value.swelling,\r\n\t\tforcedRest: formData.value.forcedRest,\r\n\t\tdysbasia: formData.value.dysbasia,\r\n\t\tworkWithDifficulty: formData.value.workWithDifficulty,\r\n\t\tgoOutWithDifficulty: formData.value.goOutWithDifficulty,\r\n\t\tsleepWithDifficulty: formData.value.sleepWithDifficulty,\r\n\t\tdoingWithDifficulty: formData.value.doingWithDifficulty,\r\n\t\tincomeWithDifficulty: formData.value.incomeWithDifficulty,\r\n\t\tentertainmentWithDifficulty: formData.value.entertainmentWithDifficulty,\r\n\t\tsexWithDifficulty: formData.value.sexWithDifficulty,\r\n\t\teatLess: formData.value.eatLess,\r\n\t\tbreathWithDifficulty: formData.value.breathWithDifficulty,\r\n\t\ttired: formData.value.tired,\r\n\t\tinHospital: formData.value.inHospital,\r\n\t\tspendMoney: formData.value.spendMoney,\r\n\t\tsideEffect: formData.value.sideEffect,\r\n\t\tbeBburden: formData.value.beBburden,\r\n\t\tuncontrolled: formData.value.uncontrolled,\r\n\t\tanxiety: formData.value.anxiety,\r\n\t\tdecreasedMemory: formData.value.decreasedMemory,\r\n\t\tdepression: formData.value.depression,\r\n\r\n\t\tpreventIllness: formData.value.preventIllness,\r\n\t\texercise: formData.value.exercise,\r\n\t\tlowSalt: formData.value.lowSalt,\r\n\t\tseeDoctor: formData.value.seeDoctor,\r\n\t\tdoctorMedicine: formData.value.doctorMedicine,\r\n\t\tlowSaltGoOut: formData.value.lowSaltGoOut,\r\n\t\tvaccinated: formData.value.vaccinated,\r\n\t\tvisitFriends: formData.value.visitFriends,\r\n\t\ttakeMedicine: formData.value.takeMedicine,\r\n\t\tconsultMedicine: formData.value.consultMedicine,\r\n\r\n\t\ttimelyAwareness: formData.value.timelyAwareness,\r\n\t\tknowInTime: formData.value.knowInTime,\r\n\r\n\t\tweighOneself: formData.value.weighOneself,\r\n\t\tfocusOneself: formData.value.focusOneself,\r\n\t\tcheckTheSideEffects: formData.value.checkTheSideEffects,\r\n\t\tbeProneToFatigue: formData.value.beProneToFatigue,\r\n\t\tconsultWorkers: formData.value.consultWorkers,\r\n\t\tmonitorSymptoms: formData.value.monitorSymptoms,\r\n\t\tankleEdema: formData.value.ankleEdema,\r\n\t\tshortnessOfBreath: formData.value.shortnessOfBreath,\r\n\t\tcheckOwnSymptoms: formData.value.checkOwnSymptoms,\r\n\r\n\t\treduceSalt: formData.value.reduceSalt,\r\n\t\treduceFluids: formData.value.reduceFluids,\r\n\t\ttakeMedicine2: formData.value.takeMedicine2,\r\n\t\tconsultByPhone: formData.value.consultByPhone,\r\n\t\tseekHelp: formData.value.seekHelp,\r\n\t\ttryToFind: formData.value.tryToFind,\r\n\t\treduceActivity: formData.value.reduceActivity,\r\n\t\tfeelBetter: formData.value.feelBetter,\r\n\r\n\t\tkeepStable: formData.value.keepStable,\r\n\t\tfollowPlan: formData.value.followPlan,\r\n\t\tfollowPlanWithDifficluty: formData.value.followPlanWithDifficluty,\r\n\t\tregularMonitor: formData.value.regularMonitor,\r\n\t\tmonitorWithDifficulty: formData.value.monitorWithDifficulty,\r\n\t\trealizeChange: formData.value.realizeChange,\r\n\t\tassessSymptom: formData.value.assessSymptom,\r\n\t\trelieveSymptom: formData.value.relieveSymptom,\r\n\t\trelieveWithDifficulty: formData.value.relieveWithDifficulty,\r\n\t\tjudgmentMethod: formData.value.judgmentMethod,\r\n\t\t\r\n\t\tuserId: userStore.userInfo.userid\r\n\t}\r\n\r\n\t// 如果是查看模式，并且有id，则添加id到请求数据中\r\n\t    if (mode.value === 'view') {\r\n\t        requestData.id = query.value.id;\r\n\t        requestData.updateUserId = userStore.userInfo.userid\r\n\t    }\r\n\t\r\n\t    console.log('formData为：', formData.value)\r\n\t    console.log('requestData为', requestData)\r\n\t\r\n\t    // 调用保存接口\r\n\t    uni.request({\r\n\t        url: `${import.meta.env.VITE_SERVER_BASEURL}/patient/savemonitoring`,\r\n\t        method: 'POST',\r\n\t        data: requestData,\r\n\t        success: (res) => {\r\n\t            uni.hideLoading(); if (res.data?.success) {\r\n\t                uni.showModal({\r\n\t                    title: mode.value === 'add' ? '提交成功' : '保存成功',\r\n\t                    showCancel: false,\r\n\t                    success: () => {\r\n\t                        uni.navigateBack();\r\n\t                    }\r\n\t                });\r\n\t            } else {\r\n\t                const errorMsg = res.data?.message || '提交失败，未知错误';\r\n\t                uni.showModal({\r\n\t                    title: mode.value === 'add' ? '提交失败' : '保存失败',\r\n\t                    content: errorMsg,\r\n\t                    showCancel: false\r\n\t                });\r\n\t            }\r\n\t        },\r\n\t        fail: (err) => {\r\n\t            uni.hideLoading();\r\n\t            const errorMsg = err.errMsg || '网络错误，请稍后重试';\r\n\t            uni.showModal({\r\n\t                title: '提交失败',\r\n\t                content: errorMsg,\r\n\t                showCancel: false\r\n\t            });\r\n\t        }\r\n\t    })\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.head0 {\r\n\tposition: relative;\r\n\tpadding-bottom: 20px;\r\n}\r\n\r\n.head0::after {\r\n\tcontent: '';\r\n\tposition: absolute;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\theight: 1px;\r\n\tbackground: linear-gradient(90deg, transparent, #e5e7eb, transparent);\r\n}\r\n\r\n.page-scroll-view {\r\n\theight: calc(100vh - 44px);\r\n\t/* 减去导航栏高度 */\r\n\twidth: 100%;\r\n}\r\n\r\n.form-container {\r\n\tpadding: 20rpx;\r\n\tpadding-bottom: 120rpx;\r\n\t/* 增加底部内边距，防止内容被遮挡 */\r\n\r\n\t.form-header {\r\n\t\tmargin-bottom: 20rpx;\r\n\t\ttext-align: center;\r\n\t\t/* 标题居中 */\r\n\r\n\t\t.form-title {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tcolor: #333;\r\n\t\t}\r\n\t}\r\n\r\n\t.form-section {\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tborder-radius: 12rpx;\r\n\t\tpadding: 20rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\r\n\t\t.form-item {\r\n\t\t\tmargin-bottom: 30rpx;\r\n\r\n\t\t\t.form-label {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.indent-text {\r\n\t\t\t\ttext-indent: 2em;\r\n\t\t\t}\r\n\r\n\t\t\t.inline-form-item {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\r\n\t\t\t\t.inline-form-label {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\tmin-width: 160rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.inline-form-input {\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\tbackground-color: #F7F7F7;\r\n\t\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\tpadding: 20rpx;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\t\tmin-height: 80rpx;\r\n\r\n\t\t\t\t\t&:disabled {\r\n\t\t\t\t\t\tbackground-color: #F5F5F5;\r\n\t\t\t\t\t\tcolor: #666;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.date-picker {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tbackground-color: #F7F7F7;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\tpadding: 20rpx;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\tmin-height: 80rpx;\r\n\r\n\t\t\t\t.placeholder {\r\n\t\t\t\t\tcolor: #999;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.form-input,\r\n\t\t\t.form-textarea {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tbackground-color: #F7F7F7;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\tpadding: 20rpx;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\tmin-height: 80rpx;\r\n\r\n\t\t\t\t&:disabled {\r\n\t\t\t\t\tbackground-color: #F5F5F5;\r\n\t\t\t\t\tcolor: #666;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.form-textarea {\r\n\t\t\t\theight: 180rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.radio-group {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tgap: 30rpx;\r\n\r\n\t\t\t\t/* 增加间距 */\r\n\t\t\t\t.radio-row {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tgap: 150rpx;\r\n\t\t\t\t\t/* 增加间距 */\r\n\t\t\t\t\tmargin-left: 20rpx;\r\n\t\t\t\t\t/* 增加左边距，实现对齐 */\r\n\t\t\t\t\tjustify-content: flex-start;\r\n\t\t\t\t\t/* 添加左对齐 */\r\n\t\t\t\t}\r\n\r\n\t\t\t\t/* 心功能分级选项特殊样式 */\r\n\t\t\t\t.heart-function-item .radio-row {\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t/* 确保是块级元素，占据整行 */\r\n\t\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\t\t/* 增加行间距 */\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t/* 确保宽度100% */\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.heart-function-item .radio-item {\r\n\t\t\t\t\twidth: 100% !important;\r\n\t\t\t\t\t/* 强制宽度为100%，确保文本不会换行 */\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.heart-function-item .radio-item text {\r\n\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t\t/* 防止文本换行 */\r\n\t\t\t\t\toverflow: visible;\r\n\t\t\t\t\t/* 确保文本完全显示 */\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.radio-item {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tmin-width: 180rpx;\r\n\t\t\t\t\twidth: auto;\r\n\t\t\t\t\t/* 允许宽度自适应内容 */\r\n\t\t\t\t\tmax-width: 100%;\r\n\t\t\t\t\t/* 确保不超出容器 */\r\n\t\t\t\t\t/* 确保宽度一致，增大宽度值以容纳更长的文本 */\r\n\r\n\t\t\t\t\t.radio-btn {\r\n\t\t\t\t\t\twidth: 36rpx;\r\n\t\t\t\t\t\theight: 36rpx;\r\n\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\tborder: 2rpx solid #CCCCCC;\r\n\t\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t\t\t&.checked {\r\n\t\t\t\t\t\t\tborder-color: #07C160;\r\n\r\n\t\t\t\t\t\t\t&:after {\r\n\t\t\t\t\t\t\t\tcontent: '';\r\n\t\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\t\twidth: 24rpx;\r\n\t\t\t\t\t\t\t\theight: 24rpx;\r\n\t\t\t\t\t\t\t\tbackground-color: #07C160;\r\n\t\t\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\t\t\ttop: 50%;\r\n\t\t\t\t\t\t\t\tleft: 50%;\r\n\t\t\t\t\t\t\t\ttransform: translate(-50%, -50%);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\ttext {\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// 添加省市区选择器样式\r\n\t.area-picker-container {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tmargin-bottom: 20rpx;\r\n\r\n\t\t.inline-form-label {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #333;\r\n\t\t\tmargin-bottom: 10rpx;\r\n\t\t\tdisplay: block;\r\n\t\t}\r\n\r\n\t\t.area-picker-wrapper {\r\n\t\t\twidth: 100%;\r\n\t\t\tbackground-color: #F7F7F7;\r\n\t\t\tborder-radius: 8rpx;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\tmin-height: 80rpx;\r\n\r\n\t\t\t:deep(.wd-picker__value) {\r\n\t\t\t\ttext-align: left;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t}\r\n\r\n\t\t\t:deep(.wd-picker__action) {\r\n\t\t\t\tdisplay: none;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// 行内选择器样式\r\n\t.inline-picker-wrapper {\r\n\t\tflex: 1;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.required {\r\n\t\tcolor: #FF0000;\r\n\t\tmargin-right: 4rpx;\r\n\t}\r\n\r\n\t.submit-section {\r\n\t\tmargin-top: 40rpx;\r\n\t\tmargin-bottom: 60rpx;\r\n\t\t/* 增加底部间距 */\r\n\r\n\t\t.submit-btn {\r\n\t\t\twidth: 100%;\r\n\t\t\tbackground-color: #07C160;\r\n\t\t\tcolor: #FFFFFF;\r\n\t\t\tborder-radius: 8rpx;\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tpadding: 20rpx 0;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.checkbox-group {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tgap: 30rpx;\r\n\r\n\t.checkbox-row {\r\n\t\tdisplay: flex;\r\n\t\tgap: 150rpx;\r\n\t\tmargin-left: 20rpx;\r\n\t\tjustify-content: flex-start;\r\n\t}\r\n\r\n\t.checkbox-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmin-width: 180rpx;\r\n\t\twidth: 240rpx;\r\n\r\n\t\t.checkbox-btn {\r\n\t\t\twidth: 36rpx;\r\n\t\t\theight: 36rpx;\r\n\t\t\tborder-radius: 4rpx;\r\n\t\t\tborder: 2rpx solid #CCCCCC;\r\n\t\t\tmargin-right: 10rpx;\r\n\t\t\tposition: relative;\r\n\r\n\t\t\t&.checked {\r\n\t\t\t\tborder-color: #07C160;\r\n\t\t\t\tbackground-color: #07C160;\r\n\r\n\t\t\t\t&:after {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\twidth: 20rpx;\r\n\t\t\t\t\theight: 10rpx;\r\n\t\t\t\t\tborder-left: 4rpx solid #FFFFFF;\r\n\t\t\t\t\tborder-bottom: 4rpx solid #FFFFFF;\r\n\t\t\t\t\ttop: 45%;\r\n\t\t\t\t\tleft: 50%;\r\n\t\t\t\t\ttransform: translate(-50%, -50%) rotate(-45deg);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\ttext {\r\n\t\t\tfont-size: 28rpx; //biaoji\r\n\t\t\tcolor: #333;\r\n\t\t}\r\n\t}\r\n\r\n\t.sub-form-item {\r\n\t\tmargin-left: 50rpx;\r\n\t\tmargin-top: -10rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\r\n\t\t.sub-form-label {\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: #666;\r\n\t\t\tmin-width: 180rpx;\r\n\t\t}\r\n\r\n\t\t.sub-form-input {\r\n\t\t\tflex: 1;\r\n\t\t\tbackground-color: #F7F7F7;\r\n\t\t\tborder-radius: 8rpx;\r\n\t\t\tpadding: 15rpx;\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: #333;\r\n\t\t\tmin-height: 70rpx;\r\n\t\t\twidth: 100%;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t}\r\n\r\n\t\t.picker {\r\n\t\t\tflex: 1;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\tbackground-color: #F7F7F7;\r\n\t\t\tborder-radius: 8rpx;\r\n\t\t\tpadding: 15rpx;\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: #333;\r\n\t\t\tmin-height: 70rpx;\r\n\t\t\twidth: 100%;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t}\r\n\t}\r\n\r\n\t/* 添加新的样式以支持两列布局 */\r\n\t.sub-form-items-row {\r\n\t\tdisplay: flex;\r\n\t\tmargin-left: 50rpx;\r\n\t\tgap: 20rpx;\r\n\t\tmargin-top: -10rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\r\n\t\t.sub-form-item {\r\n\t\t\tflex: 1;\r\n\t\t\twidth: 50%;\r\n\t\t\tmin-width: calc(50% - 10rpx);\r\n\t\t\t/* 确保即使只有一个元素也占据一半宽度 */\r\n\t\t\tmargin: 0;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\r\n\t\t\t.picker-container {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t}\r\n\r\n\t\t\t.temp-picker {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t}\r\n\r\n\t\t\t.sub-form-input {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tbackground-color: #F7F7F7;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\tpadding: 15rpx;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tmin-height: 70rpx;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t}\r\n\r\n\t\t\t.picker {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tbackground-color: #F7F7F7;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\tpadding: 15rpx;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tmin-height: 70rpx;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.location-box {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\twidth: 100%;\r\n\tbackground-color: #F7F7F7;\r\n\tborder-radius: 8rpx;\r\n\tpadding: 20rpx;\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tbox-sizing: border-box;\r\n\tmin-height: 80rpx;\r\n}\r\n\r\n.location-icon {\r\n\tcolor: #07C160;\r\n\tfont-size: 36rpx;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-data/monitor/form.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "ref", "onMounted", "computed", "uni"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwyFA,UAAM,YAAYA,WAAAA,aAAa;AAMzB,UAAA,QAAQC,cAAS,IAAA,EAAE;AACzBC,kBAAAA,UAAU,MAAM;AACZ,YAAM,QAAQ,gBAAgB;AAC9B,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AACpC,YAAA,QAAS,YAAoB,WAAW,CAAC;AAE/C,UAAI,MAAM,MAAM,SAAS,UAAU,MAAM,MAAM,IAAI;AAClC,qBAAA,MAAM,MAAM,EAAE;AAAA,MAAA;AAI3B,UAAA,UAAU,SAAS,UAAU;AACpB,iBAAA,MAAM,WAAW,UAAU,SAAS;AAAA,MAAA;AAAA,IACjD,CAEH;AAGD,UAAM,OAAOC,cAAAA,SAAS,MAAM,MAAM,MAAM,QAAQ,KAAK;AAGrD,UAAM,WAAWF,cAAAA,IAAI;AAAA,MACpB,UAAU;AAAA,MACV,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,eAAe;AAAA,MACf,WAAW;AAAA,MACX,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,YAAY;AAAA,QACX,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,QACT,gBAAgB;AAAA,QAChB,MAAM;AAAA,MACP;AAAA,MACA,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,eAAe;AAAA,MAEf,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,oBAAoB;AAAA,MACpB,qBAAqB;AAAA,MACrB,qBAAqB;AAAA,MACrB,qBAAqB;AAAA,MACrB,sBAAsB;AAAA,MACtB,6BAA6B;AAAA,MAC7B,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,sBAAsB;AAAA,MACtB,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,SAAQ;AAAA,MACR,iBAAiB;AAAA,MACjB,YAAY;AAAA,MAEZ,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,SAAS;AAAA,MACT,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,cAAc;AAAA,MACd,iBAAiB;AAAA,MAEjB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,qBAAqB;AAAA,MACrB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,YAAY;AAAA,MACZ,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,MAElB,iBAAiB;AAAA,MACjB,YAAY;AAAA,MAEZ,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,YAAY;AAAA,MAEZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,uBAAuB;AAAA,MACvB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,uBAAuB;AAAA,MACvB,gBAAgB;AAAA,IAAA,CAChB;AAEK,UAAA,eAAe,CAAC,OAAe;AAGpCG,oBAAAA,MAAI,YAAY;AAAA,QACZ,OAAO;AAAA,MAAA,CACV;AAGD,YAAM,MAAM,GAAG,6BAAmC,YAAY,EAAE;AACxD,cAAA,IAAI,YAAY,GAAG;AAE3BA,oBAAAA,MAAI,QAAQ;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,QACR,SAAS,CAAC,QAAa;AACnB,gBAAM,WAAW,IAAI;AACrB,kBAAQ,IAAI,aAAa,SAAS,OAAO,QAAQ,mBAAmB;AAEhE,cAAA,YAAY,SAAS,SAAS,KAAK;AAEnC,gBAAI,SAAS,QAAQ;AACjB,uBAAS,QAAQ;AAAA,gBACb,eAAe,SAAS,OAAO,QAAQ,iBAAiB;AAAA,gBACxD,WAAW,SAAS,OAAO,QAAQ,aAAa;AAAA,gBAChD,QAAQ,SAAS,OAAO,QAAQ,UAAU;AAAA,gBACzD,UAAS,SAAS,OAAO,YAAY;AAAA,gBACrC,UAAS,SAAS,OAAO,QAAQ,YAAW;AAAA,gBAC5C,OAAM,SAAS,OAAO,QAAQ,SAAQ;AAAA,gBACtC,gBAAe,SAAS,OAAO,QAAQ,kBAAiB;AAAA,gBACxD,iBAAgB,SAAS,OAAO,QAAQ,mBAAkB;AAAA,gBAC1D,gBAAe,SAAS,OAAO,QAAQ,kBAAiB;AAAA,gBACxD,YAAW,SAAS,OAAO,QAAQ,cAAa;AAAA,gBAChD,gBAAe,SAAS,OAAO,QAAQ,kBAAiB;AAAA,gBACxD,eAAc,SAAS,OAAO,QAAQ,iBAAgB;AAAA,gBACtD,eAAc,SAAS,OAAO,QAAQ,iBAAgB;AAAA,gBACtD,UAAS,SAAS,OAAO,QAAQ,YAAW;AAAA,gBAC5C,YAAW,SAAS,OAAO,QAAQ,cAAa;AAAA,gBAChD,UAAS,SAAS,OAAO,QAAQ,YAAW;AAAA,gBAC5C,oBAAmB,SAAS,OAAO,QAAQ,sBAAqB;AAAA,gBAChE,qBAAoB,SAAS,OAAO,QAAQ,uBAAsB;AAAA,gBAClE,qBAAoB,SAAS,OAAO,QAAQ,uBAAsB;AAAA,gBAClE,qBAAoB,SAAS,OAAO,QAAQ,uBAAsB;AAAA,gBAClE,sBAAqB,SAAS,OAAO,QAAQ,wBAAuB;AAAA,gBACpE,6BAA4B,SAAS,OAAO,QAAQ,+BAA8B;AAAA,gBAClF,mBAAkB,SAAS,OAAO,QAAQ,qBAAoB;AAAA,gBAC9D,SAAQ,SAAS,OAAO,QAAQ,WAAU;AAAA,gBAC1C,sBAAqB,SAAS,OAAO,QAAQ,wBAAuB;AAAA,gBACpE,OAAM,SAAS,OAAO,QAAQ,SAAQ;AAAA,gBACtC,YAAW,SAAS,OAAO,QAAQ,cAAa;AAAA,gBAChD,YAAW,SAAS,OAAO,QAAQ,cAAa;AAAA,gBAChD,YAAW,SAAS,OAAO,QAAQ,cAAa;AAAA,gBAChD,WAAU,SAAS,OAAO,QAAQ,aAAY;AAAA,gBAC9C,cAAa,SAAS,OAAO,QAAQ,gBAAe;AAAA,gBACpD,SAAQ,SAAS,OAAO,QAAQ,WAAU;AAAA,gBAC1C,iBAAgB,SAAS,OAAO,QAAQ,mBAAkB;AAAA,gBAC1D,YAAW,SAAS,OAAO,QAAQ,cAAa;AAAA,gBAChD,gBAAe,SAAS,OAAO,QAAQ,kBAAiB;AAAA,gBACxD,UAAS,SAAS,OAAO,QAAQ,YAAW;AAAA,gBAC5C,SAAQ,SAAS,OAAO,QAAQ,WAAU;AAAA,gBAC1C,WAAU,SAAS,OAAO,QAAQ,aAAY;AAAA,gBAC9C,gBAAe,SAAS,OAAO,QAAQ,kBAAiB;AAAA,gBACxD,cAAa,SAAS,OAAO,QAAQ,gBAAe;AAAA,gBACpD,YAAW,SAAS,OAAO,QAAQ,cAAa;AAAA,gBAChD,cAAa,SAAS,OAAO,QAAQ,gBAAe;AAAA,gBACpD,cAAa,SAAS,OAAO,QAAQ,gBAAe;AAAA,gBACpD,iBAAgB,SAAS,OAAO,QAAQ,mBAAkB;AAAA,gBAC1D,cAAa,SAAS,OAAO,QAAQ,gBAAe;AAAA,gBACpD,cAAa,SAAS,OAAO,QAAQ,gBAAe;AAAA,gBACpD,qBAAoB,SAAS,OAAO,QAAQ,uBAAsB;AAAA,gBAClE,kBAAiB,SAAS,OAAO,QAAQ,oBAAmB;AAAA,gBAC5D,gBAAe,SAAS,OAAO,QAAQ,kBAAiB;AAAA,gBACxD,iBAAgB,SAAS,OAAO,QAAQ,mBAAkB;AAAA,gBAC1D,YAAW,SAAS,OAAO,QAAQ,cAAa;AAAA,gBAChD,mBAAkB,SAAS,OAAO,QAAQ,qBAAoB;AAAA,gBAC9D,kBAAiB,SAAS,OAAO,QAAQ,oBAAmB;AAAA,gBAC5D,iBAAgB,SAAS,OAAO,QAAQ,mBAAkB;AAAA,gBAC1D,YAAW,SAAS,OAAO,QAAQ,cAAa;AAAA,gBAChD,YAAW,SAAS,OAAO,QAAQ,cAAa;AAAA,gBAChD,cAAa,SAAS,OAAO,QAAQ,gBAAe;AAAA,gBACpD,eAAc,SAAS,OAAO,QAAQ,iBAAgB;AAAA,gBACtD,gBAAe,SAAS,OAAO,QAAQ,kBAAiB;AAAA,gBACxD,UAAS,SAAS,OAAO,QAAQ,YAAW;AAAA,gBAC5C,WAAU,SAAS,OAAO,QAAQ,aAAY;AAAA,gBAC9C,gBAAe,SAAS,OAAO,QAAQ,kBAAiB;AAAA,gBACxD,YAAW,SAAS,OAAO,QAAQ,cAAa;AAAA,gBAChD,YAAW,SAAS,OAAO,QAAQ,cAAa;AAAA,gBAChD,YAAW,SAAS,OAAO,QAAQ,cAAa;AAAA,gBAChD,0BAAyB,SAAS,OAAO,QAAQ,4BAA2B;AAAA,gBAC5E,gBAAe,SAAS,OAAO,QAAQ,kBAAiB;AAAA,gBACxD,uBAAsB,SAAS,OAAO,QAAQ,yBAAwB;AAAA,gBACtE,eAAc,SAAS,OAAO,QAAQ,iBAAgB;AAAA,gBACtD,eAAc,SAAS,OAAO,QAAQ,iBAAgB;AAAA,gBACtD,gBAAe,SAAS,OAAO,QAAQ,kBAAiB;AAAA,gBACxD,uBAAsB,SAAS,OAAO,QAAQ,yBAAwB;AAAA,gBACtE,gBAAe,SAAS,OAAO,QAAQ,kBAAiB;AAAA,cAC7C;AACQ,sBAAA,IAAI,aAAa,SAAS,KAAK;AAAA,YAAA,OACpC;AACH,sBAAQ,IAAI,UAAU;AACtBA,4BAAAA,MAAI,UAAU;AAAA,gBACV,OAAO;AAAA,gBACP,MAAM;AAAA,cAAA,CACT;AAAA,YAAA;AAAA,UACL,OACG;AACHA,0BAAAA,MAAI,UAAU;AAAA,cACV,OAAO,SAAS,OAAO;AAAA,cACvB,MAAM;AAAA,YAAA,CACT;AAAA,UAAA;AAAA,QAET;AAAA,QACA,MAAM,CAAC,QAAa;AACR,kBAAA,MAAM,aAAa,GAAG;AAC9BA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACT;AAAA,QACL;AAAA,QACA,UAAU,MAAM;AACZA,wBAAAA,MAAI,YAAY;AAAA,QAAA;AAAA,MACpB,CACH;AAAA,IACF;AAEM,UAAA,sBAAsB,CAAC,eAAuB;AAC/C,UAAA,CAAC,SAAS,MAAM,YAAY;AAC/B,iBAAS,MAAM,aAAa;AAAA,UAC3B,SAAS;AAAA,UACT,SAAS;AAAA,UACT,SAAS;AAAA,UACT,SAAS;AAAA,UACT,gBAAgB;AAAA,UAChB,MAAM;AAAA,QACP;AAAA,MAAA;AAID,YAAM,YAAY,CAAC,SAAS,MAAM,WAAW,UAAU;AACvD,eAAS,MAAM,aAAa,iCACxB,SAAS,MAAM,aADS;AAAA,QAE3B,CAAC,UAAU,GAAG;AAAA,MACf;AAAA,IAED;AAEA,UAAM,cAAc,MAAM;AAEzBA,oBAAAA,MAAI,eAAe;AAAA,QAClB,SAAS,SAAU,KAAK;AACf,kBAAA,IAAI,UAAU,IAAI,IAAI;AACtB,kBAAA,IAAI,UAAU,IAAI,OAAO;AACzB,kBAAA,IAAI,QAAQ,IAAI,QAAQ;AACxB,kBAAA,IAAI,QAAQ,IAAI,SAAS;AAGxB,mBAAA,MAAM,WAAW,IAAI;AAAA,QAC/B;AAAA,QACA,MAAM,SAAU,KAAK;AACZ,kBAAA,MAAM,UAAU,GAAG;AAC3BA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACN;AAAA,QAAA;AAAA,MACF,CACA;AAAA,IACF;AAGA,UAAM,aAAa,MAAM;AAEhB,cAAA,IAAI,WAAW,SAAS,KAAK;AAGjC,UAAA,CAAC,SAAS,MAAM,UAAU;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAGG,UAAA,CAAC,SAAS,MAAM,QAAQ;AAC3BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAGG,UAAA,CAAC,SAAS,MAAM,eAAe;AAClCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,WAAW;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,OAAO;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,gBAAgB;AACnCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,iBAAiB;AACpCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,gBAAgB;AACnCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAED,UAAI,CAAC,SAAS,MAAM,cAClB,CAAC,SAAS,MAAM,WAAW,WAC5B,CAAC,SAAS,MAAM,WAAW,WAC3B,CAAC,SAAS,MAAM,WAAW,WAC3B,CAAC,SAAS,MAAM,WAAW,WAC3B,CAAC,SAAS,MAAM,WAAW,kBAC3B,CAAC,SAAS,MAAM,WAAW,MAAO;AAClCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,gBAAgB;AACnCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,eAAe;AAClCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,eAAe;AAClCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAGG,UAAA,CAAC,SAAS,MAAM,UAAU;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,YAAY;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,UAAU;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,oBAAoB;AACvCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,qBAAqB;AACxCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,qBAAqB;AACxCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,qBAAqB;AACxCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,sBAAsB;AACzCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,6BAA6B;AAChDA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,mBAAmB;AACtCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,SAAS;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,sBAAsB;AACzCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,OAAO;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,YAAY;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,YAAY;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,YAAY;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,WAAW;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,cAAc;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,SAAS;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,iBAAiB;AACpCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,YAAY;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAGG,UAAA,CAAC,SAAS,MAAM,gBAAgB;AACnCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,UAAU;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,SAAS;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAGG,UAAA,CAAC,SAAS,MAAM,WAAW;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAGG,UAAA,CAAC,SAAS,MAAM,gBAAgB;AACnCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,cAAc;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,YAAY;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,cAAc;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,cAAc;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,iBAAiB;AACpCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAGG,UAAA,CAAC,SAAS,MAAM,cAAc;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,cAAc;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,qBAAqB;AACxCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,kBAAkB;AACrCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,gBAAgB;AACnCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,iBAAiB;AACpCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,YAAY;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,mBAAmB;AACtCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,kBAAkB;AACrCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAGG,UAAA,CAAC,SAAS,MAAM,iBAAiB;AACpCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,YAAY;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAGG,UAAA,CAAC,SAAS,MAAM,YAAY;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,cAAc;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,eAAe;AAClCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,gBAAgB;AACnCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,UAAU;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,WAAW;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,gBAAgB;AACnCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,YAAY;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAGG,UAAA,CAAC,SAAS,MAAM,YAAY;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,YAAY;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,0BAA0B;AAC7CA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,gBAAgB;AACnCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,uBAAuB;AAC1CA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,eAAe;AAClCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,eAAe;AAClCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,gBAAgB;AACnCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,uBAAuB;AAC1CA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAEG,UAAA,CAAC,SAAS,MAAM,gBAAgB;AACnCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MAAA;AAIDA,oBAAAA,MAAI,YAAY;AAAA,QACZ,OAAO,KAAK,UAAU,QAAQ,WAAW;AAAA,MAAA,CAC5C;AACD,YAAM,cAAc;AAAA,QACnB,UAAU,SAAS,MAAM;AAAA,QACzB,UAAU,SAAS,MAAM;AAAA,QACzB,QAAQ,SAAS,MAAM;AAAA,QACvB,eAAe,SAAS,MAAM;AAAA,QAC9B,WAAW,SAAS,MAAM;AAAA,QAC1B,OAAO,SAAS,MAAM;AAAA,QACtB,gBAAgB,SAAS,MAAM;AAAA,QAC/B,iBAAiB,SAAS,MAAM;AAAA,QAChC,gBAAgB,SAAS,MAAM;AAAA,QAC/B,YAAY,SAAS,MAAM;AAAA,QAC3B,gBAAgB,SAAS,MAAM;AAAA,QAC/B,eAAe,SAAS,MAAM;AAAA,QAC9B,eAAe,SAAS,MAAM;AAAA,QAE9B,UAAU,SAAS,MAAM;AAAA,QACzB,YAAY,SAAS,MAAM;AAAA,QAC3B,UAAU,SAAS,MAAM;AAAA,QACzB,oBAAoB,SAAS,MAAM;AAAA,QACnC,qBAAqB,SAAS,MAAM;AAAA,QACpC,qBAAqB,SAAS,MAAM;AAAA,QACpC,qBAAqB,SAAS,MAAM;AAAA,QACpC,sBAAsB,SAAS,MAAM;AAAA,QACrC,6BAA6B,SAAS,MAAM;AAAA,QAC5C,mBAAmB,SAAS,MAAM;AAAA,QAClC,SAAS,SAAS,MAAM;AAAA,QACxB,sBAAsB,SAAS,MAAM;AAAA,QACrC,OAAO,SAAS,MAAM;AAAA,QACtB,YAAY,SAAS,MAAM;AAAA,QAC3B,YAAY,SAAS,MAAM;AAAA,QAC3B,YAAY,SAAS,MAAM;AAAA,QAC3B,WAAW,SAAS,MAAM;AAAA,QAC1B,cAAc,SAAS,MAAM;AAAA,QAC7B,SAAS,SAAS,MAAM;AAAA,QACxB,iBAAiB,SAAS,MAAM;AAAA,QAChC,YAAY,SAAS,MAAM;AAAA,QAE3B,gBAAgB,SAAS,MAAM;AAAA,QAC/B,UAAU,SAAS,MAAM;AAAA,QACzB,SAAS,SAAS,MAAM;AAAA,QACxB,WAAW,SAAS,MAAM;AAAA,QAC1B,gBAAgB,SAAS,MAAM;AAAA,QAC/B,cAAc,SAAS,MAAM;AAAA,QAC7B,YAAY,SAAS,MAAM;AAAA,QAC3B,cAAc,SAAS,MAAM;AAAA,QAC7B,cAAc,SAAS,MAAM;AAAA,QAC7B,iBAAiB,SAAS,MAAM;AAAA,QAEhC,iBAAiB,SAAS,MAAM;AAAA,QAChC,YAAY,SAAS,MAAM;AAAA,QAE3B,cAAc,SAAS,MAAM;AAAA,QAC7B,cAAc,SAAS,MAAM;AAAA,QAC7B,qBAAqB,SAAS,MAAM;AAAA,QACpC,kBAAkB,SAAS,MAAM;AAAA,QACjC,gBAAgB,SAAS,MAAM;AAAA,QAC/B,iBAAiB,SAAS,MAAM;AAAA,QAChC,YAAY,SAAS,MAAM;AAAA,QAC3B,mBAAmB,SAAS,MAAM;AAAA,QAClC,kBAAkB,SAAS,MAAM;AAAA,QAEjC,YAAY,SAAS,MAAM;AAAA,QAC3B,cAAc,SAAS,MAAM;AAAA,QAC7B,eAAe,SAAS,MAAM;AAAA,QAC9B,gBAAgB,SAAS,MAAM;AAAA,QAC/B,UAAU,SAAS,MAAM;AAAA,QACzB,WAAW,SAAS,MAAM;AAAA,QAC1B,gBAAgB,SAAS,MAAM;AAAA,QAC/B,YAAY,SAAS,MAAM;AAAA,QAE3B,YAAY,SAAS,MAAM;AAAA,QAC3B,YAAY,SAAS,MAAM;AAAA,QAC3B,0BAA0B,SAAS,MAAM;AAAA,QACzC,gBAAgB,SAAS,MAAM;AAAA,QAC/B,uBAAuB,SAAS,MAAM;AAAA,QACtC,eAAe,SAAS,MAAM;AAAA,QAC9B,eAAe,SAAS,MAAM;AAAA,QAC9B,gBAAgB,SAAS,MAAM;AAAA,QAC/B,uBAAuB,SAAS,MAAM;AAAA,QACtC,gBAAgB,SAAS,MAAM;AAAA,QAE/B,QAAQ,UAAU,SAAS;AAAA,MAC5B;AAGQ,UAAA,KAAK,UAAU,QAAQ;AACX,oBAAA,KAAK,MAAM,MAAM;AACjB,oBAAA,eAAe,UAAU,SAAS;AAAA,MAAA;AAG1C,cAAA,IAAI,cAAc,SAAS,KAAK;AAChC,cAAA,IAAI,gBAAgB,WAAW;AAGvCA,oBAAAA,MAAI,QAAQ;AAAA,QACR,KAAK,GAAG,6BAAmC;AAAA,QAC3C,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,SAAS,CAAC,QAAQ;;AACdA,wBAAAA,MAAI,YAAY;AAAO,eAAA,SAAI,SAAJ,mBAAU,SAAS;AACtCA,0BAAAA,MAAI,UAAU;AAAA,cACV,OAAO,KAAK,UAAU,QAAQ,SAAS;AAAA,cACvC,YAAY;AAAA,cACZ,SAAS,MAAM;AACXA,8BAAAA,MAAI,aAAa;AAAA,cAAA;AAAA,YACrB,CACH;AAAA,UAAA,OACE;AACG,kBAAA,aAAW,SAAI,SAAJ,mBAAU,YAAW;AACtCA,0BAAAA,MAAI,UAAU;AAAA,cACV,OAAO,KAAK,UAAU,QAAQ,SAAS;AAAA,cACvC,SAAS;AAAA,cACT,YAAY;AAAA,YAAA,CACf;AAAA,UAAA;AAAA,QAET;AAAA,QACA,MAAM,CAAC,QAAQ;AACXA,wBAAAA,MAAI,YAAY;AACV,gBAAA,WAAW,IAAI,UAAU;AAC/BA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,UAAA,CACf;AAAA,QAAA;AAAA,MACL,CACH;AAAA,IACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACntHD,GAAG,WAAW,eAAe;"}