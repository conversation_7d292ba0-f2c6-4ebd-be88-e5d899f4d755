{"version": 3, "file": "form.js", "sources": ["../../../../../src/pages-data/social/form.vue", "../../../../../uniPage:/cGFnZXMtZGF0YVxzb2NpYWxcZm9ybS52dWU"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n    <PageLayout>\r\n        <template #navbar>\r\n            <NavBar title=\"社工/社区医生注册\" :showBack=\"true\" />\r\n        </template> <scroll-view class=\"page-scroll-view\" scroll-y=\"true\">\r\n            <view class=\"form-container\">\r\n                <view class=\"form-section\">\r\n                    <view class=\"form-item\">\r\n                        <view class=\"inline-form-item\">\r\n                            <text class=\"inline-form-label\"><text class=\"required\">*</text>姓名：</text>\r\n                            <input class=\"inline-form-input\" v-model=\"formData.name\" placeholder=\"请输入姓名\"\r\n                                maxlength=\"20\" />\r\n                        </view>\r\n                    </view>\r\n                    <view class=\"form-item\">\r\n                        <view class=\"inline-form-item\">\r\n                            <text class=\"inline-form-label\"><text class=\"required\">*</text>社区：</text>\r\n                            <input class=\"inline-form-input\" v-model=\"formData.community\" placeholder=\"请输入社区\"\r\n                                maxlength=\"20\" />\r\n                        </view>\r\n                    </view>\r\n                </view>\r\n\r\n                <view class=\"btn-container\">\r\n                    <button class=\"submit-btn\" @click=\"handleSubmit\" :disabled=\"submitting\">\r\n                        {{ submitting ? '提交中...' : '提交' }}\r\n                    </button>\r\n                </view>\r\n            </view>\r\n        </scroll-view>\r\n    </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { ref, onMounted } from 'vue'\r\nimport { useUserStore } from '@/store/user'\r\nimport { useToast } from 'wot-design-uni'\r\nimport { http } from '@/utils/http'\r\n\r\ndefineOptions({\r\n    name: 'SoicalRegistration',\r\n    options: {\r\n        styleIsolation: 'shared',\r\n    },\r\n})\r\n\r\nconst userStore = useUserStore()\r\nconst toast = useToast()\r\n\r\nconst submitting = ref(false)\r\n\r\n// 表单数据\r\nconst formData = ref({\r\n    name: '',\r\n    community: ''\r\n})\r\n\r\n// 表单验证\r\nconst validateForm = () => {\r\n    if (!formData.value.name.trim()) {\r\n        toast.error('请输入姓名')\r\n        return false\r\n    }\r\n\r\n    if (!formData.value.community.trim()) {\r\n        toast.error('请输入社区')\r\n        return false\r\n    }\r\n\r\n    return true\r\n}\r\n\r\n// 提交表单\r\nconst handleSubmit = async () => {\r\n    if (!validateForm()) {\r\n        return\r\n    }\r\n\r\n    submitting.value = true\r\n\r\n    try {\r\n        // 提交社工/社区医生注册数据\r\n        const response = await uni.request({\r\n            url: `${import.meta.env.VITE_SERVER_BASEURL}/sys/user/2/addApply`,\r\n            method: 'POST',\r\n            header: {\r\n                'X-Access-Token': userStore.userInfo.token\r\n            },\r\n            data: {\r\n                userId: userStore.userInfo.userid,\r\n                userName: formData.value.name,\r\n                community: formData.value.community,\r\n                phone: userStore.userInfo.phone\r\n            }\r\n        })\r\n\r\n\r\n        if ((response.data as any)?.success) {\r\n            uni.showModal({\r\n                title: '提交注册申请成功',\r\n                content: '提交注册申请成功！',\r\n                showCancel: false,\r\n                success: () => {\r\n                    uni.navigateBack()\r\n                }\r\n            })\r\n        } else {\r\n            const errorMsg = (response.data as any)?.message || '提交注册申请失败，请稍后重试'\r\n            toast.error(errorMsg)\r\n        }\r\n    } catch (error) {\r\n        console.error('提交注册申请失败:', error)\r\n        toast.error('网络错误，请稍后重试')\r\n    } finally {\r\n        submitting.value = false\r\n    }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-scroll-view {\r\n    height: 100vh;\r\n    background-color: #f5f5f5;\r\n}\r\n\r\n.form-container {\r\n    min-height: 100vh;\r\n    padding: 20rpx;\r\n}\r\n\r\n.form-header {\r\n    background-color: #fff;\r\n    border-radius: 16rpx;\r\n    padding: 40rpx 30rpx;\r\n    margin-bottom: 20rpx;\r\n    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);\r\n\r\n    .form-title {\r\n        font-size: 36rpx;\r\n        font-weight: 600;\r\n        color: #333;\r\n        text-align: center;\r\n    }\r\n}\r\n\r\n.form-section {\r\n    background-color: #fff;\r\n    border-radius: 16rpx;\r\n    padding: 30rpx;\r\n    margin-bottom: 30rpx;\r\n    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.form-item {\r\n    margin-bottom: 30rpx;\r\n\r\n    &:last-child {\r\n        margin-bottom: 0;\r\n    }\r\n}\r\n\r\n.form-label {\r\n    font-size: 32rpx;\r\n    font-weight: 600;\r\n    color: #333;\r\n    margin-bottom: 20rpx;\r\n    display: block;\r\n}\r\n\r\n.inline-form-item {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-bottom: 20rpx;\r\n\r\n    &:last-child {\r\n        margin-bottom: 0;\r\n    }\r\n}\r\n\r\n.inline-form-label {\r\n    font-size: 30rpx;\r\n    color: #333;\r\n    width: 120rpx;\r\n    flex-shrink: 0;\r\n\r\n    .required {\r\n        color: #ff4d4f;\r\n        margin-right: 5rpx;\r\n    }\r\n}\r\n\r\n.inline-form-input {\r\n    flex: 1;\r\n    height: 80rpx;\r\n    padding: 0 20rpx;\r\n    border: 2rpx solid #e8e8e8;\r\n    border-radius: 8rpx;\r\n    font-size: 28rpx;\r\n    color: #333;\r\n    background-color: #fff;\r\n\r\n    &:focus {\r\n        border-color: #07C160;\r\n    }\r\n\r\n    &:disabled {\r\n        background-color: #f5f5f5;\r\n        color: #999;\r\n    }\r\n}\r\n\r\n.btn-container {\r\n    padding: 0 30rpx 40rpx;\r\n}\r\n\r\n.submit-btn {\r\n    width: 100%;\r\n    background-color: #07C160;\r\n    color: #FFFFFF;\r\n    border-radius: 8rpx;\r\n    font-size: 32rpx;\r\n    padding: 20rpx 0;\r\n\r\n    &:disabled {\r\n        background: #cccccc;\r\n    }\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-data/social/form.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "useToast", "ref", "uni"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkDA,UAAM,YAAYA,WAAAA,aAAa;AAC/B,UAAM,QAAQC,cAAAA,SAAS;AAEjB,UAAA,aAAaC,kBAAI,KAAK;AAG5B,UAAM,WAAWA,cAAAA,IAAI;AAAA,MACjB,MAAM;AAAA,MACN,WAAW;AAAA,IAAA,CACd;AAGD,UAAM,eAAe,MAAM;AACvB,UAAI,CAAC,SAAS,MAAM,KAAK,QAAQ;AAC7B,cAAM,MAAM,OAAO;AACZ,eAAA;AAAA,MAAA;AAGX,UAAI,CAAC,SAAS,MAAM,UAAU,QAAQ;AAClC,cAAM,MAAM,OAAO;AACZ,eAAA;AAAA,MAAA;AAGJ,aAAA;AAAA,IACX;AAGA,UAAM,eAAe,MAAY;;AACzB,UAAA,CAAC,gBAAgB;AACjB;AAAA,MAAA;AAGJ,iBAAW,QAAQ;AAEf,UAAA;AAEM,cAAA,WAAW,MAAMC,cAAA,MAAI,QAAQ;AAAA,UAC/B,KAAK,GAAG,6BAAmC;AAAA,UAC3C,QAAQ;AAAA,UACR,QAAQ;AAAA,YACJ,kBAAkB,UAAU,SAAS;AAAA,UACzC;AAAA,UACA,MAAM;AAAA,YACF,QAAQ,UAAU,SAAS;AAAA,YAC3B,UAAU,SAAS,MAAM;AAAA,YACzB,WAAW,SAAS,MAAM;AAAA,YAC1B,OAAO,UAAU,SAAS;AAAA,UAAA;AAAA,QAC9B,CACH;AAGI,aAAA,cAAS,SAAT,mBAAuB,SAAS;AACjCA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,SAAS,MAAM;AACXA,4BAAAA,MAAI,aAAa;AAAA,YAAA;AAAA,UACrB,CACH;AAAA,QAAA,OACE;AACG,gBAAA,aAAY,cAAS,SAAT,mBAAuB,YAAW;AACpD,gBAAM,MAAM,QAAQ;AAAA,QAAA;AAAA,eAEnB,OAAO;AACJ,gBAAA,MAAM,aAAa,KAAK;AAChC,cAAM,MAAM,YAAY;AAAA,MAAA,UAC1B;AACE,mBAAW,QAAQ;AAAA,MAAA;AAAA,IAE3B;;;;;;;;;;;;;;;;;;;ACvHA,GAAG,WAAW,eAAe;"}