{"version": 3, "file": "wd-checkbox-group.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-checkbox-group/wd-checkbox-group.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1jaGVja2JveC1ncm91cC93ZC1jaGVja2JveC1ncm91cC52dWU"], "sourcesContent": ["<template>\n  <view :class=\"`wd-checkbox-group ${shape === 'button' && cell ? 'is-button' : ''} ${customClass}`\" :style=\"customStyle\">\n    <slot />\n  </view>\n</template>\n<script lang=\"ts\">\nexport default {\n  name: 'wd-checkbox-group',\n  options: {\n    addGlobalClass: true,\n    virtualHost: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport { watch } from 'vue'\nimport { checkNumRange, deepClone } from '../common/util'\nimport { useChildren } from '../composables/useChildren'\nimport { CHECKBOX_GROUP_KEY, checkboxGroupProps } from './types'\n\nconst props = defineProps(checkboxGroupProps)\nconst emit = defineEmits(['change', 'update:modelValue'])\n\nconst { linkChildren } = useChildren(CHECKBOX_GROUP_KEY)\n\nlinkChildren({ props, changeSelectState })\n\nwatch(\n  () => props.modelValue,\n  (newValue) => {\n    // 传入的value数组中包括重复的元素，这种情况非法。\n    if (new Set(newValue).size !== newValue.length) {\n      // eslint-disable-next-line quotes\n      console.error(\"checkboxGroup's bound value includes same value\")\n    }\n    if (newValue.length < props.min) {\n      // eslint-disable-next-line quotes\n      console.error(\"checkboxGroup's bound value's length can't be less than min\")\n    }\n    if (props.max !== 0 && newValue.length > props.max) {\n      // eslint-disable-next-line quotes\n      console.error(\"checkboxGroup's bound value's length can't be large than max\")\n    }\n    // 每次value变化都会触发重新匹配选中项\n  },\n  { deep: true, immediate: true }\n)\n\nwatch(\n  () => props.shape,\n  (newValue) => {\n    const type = ['circle', 'square', 'button']\n    if (type.indexOf(newValue) === -1) console.error(`shape must be one of ${type.toString()}`)\n  },\n  { deep: true, immediate: true }\n)\n\nwatch(\n  () => props.min,\n  (newValue) => {\n    checkNumRange(newValue, 'min')\n  },\n  { deep: true, immediate: true }\n)\n\nwatch(\n  () => props.max,\n  (newValue) => {\n    checkNumRange(newValue, 'max')\n  },\n  { deep: true, immediate: true }\n)\n\n/**\n * @description 子节点通知父节点修改子节点选中状态\n * @param {any} value 子组件的标识符\n */\nfunction changeSelectState(value: string | number | boolean) {\n  const temp: (string | number | boolean)[] = deepClone(props.modelValue)\n  const index = temp.indexOf(value)\n  if (index > -1) {\n    // 已经选中，则从 value 列表中删除子节点的标识符。\n    temp.splice(index, 1)\n  } else {\n    // 之前未选中，则现在把加子节点的标识符加到 value 列表中。\n    temp.push(value)\n  }\n  emit('update:modelValue', temp)\n\n  emit('change', {\n    value: temp\n  })\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-checkbox-group/wd-checkbox-group.vue'\nwx.createComponent(Component)"], "names": ["useChildren", "CHECKBOX_GROUP_KEY", "watch", "checkNumRange", "deepClone"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAMA,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,gBAAgB;AAAA,EAAA;AAEpB;;;;;AASA,UAAM,QAAQ;AACd,UAAM,OAAO;AAEb,UAAM,EAAE,aAAA,IAAiBA,cAAA,YAAYC,gCAAkB;AAE1C,iBAAA,EAAE,OAAO,mBAAmB;AAEzCC,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,aAAa;AAEZ,YAAI,IAAI,IAAI,QAAQ,EAAE,SAAS,SAAS,QAAQ;AAE9C,kBAAQ,MAAM,iDAAiD;AAAA,QAAA;AAE7D,YAAA,SAAS,SAAS,MAAM,KAAK;AAE/B,kBAAQ,MAAM,6DAA6D;AAAA,QAAA;AAE7E,YAAI,MAAM,QAAQ,KAAK,SAAS,SAAS,MAAM,KAAK;AAElD,kBAAQ,MAAM,8DAA8D;AAAA,QAAA;AAAA,MAGhF;AAAA,MACA,EAAE,MAAM,MAAM,WAAW,KAAK;AAAA,IAChC;AAEAA,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,aAAa;AACZ,cAAM,OAAO,CAAC,UAAU,UAAU,QAAQ;AACtC,YAAA,KAAK,QAAQ,QAAQ,MAAM;AAAI,kBAAQ,MAAM,wBAAwB,KAAK,SAAU,CAAA,EAAE;AAAA,MAC5F;AAAA,MACA,EAAE,MAAM,MAAM,WAAW,KAAK;AAAA,IAChC;AAEAA,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,aAAa;AACZC,sBAAA,cAAc,UAAU,KAAK;AAAA,MAC/B;AAAA,MACA,EAAE,MAAM,MAAM,WAAW,KAAK;AAAA,IAChC;AAEAD,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,aAAa;AACZC,sBAAA,cAAc,UAAU,KAAK;AAAA,MAC/B;AAAA,MACA,EAAE,MAAM,MAAM,WAAW,KAAK;AAAA,IAChC;AAMA,aAAS,kBAAkB,OAAkC;AACrD,YAAA,OAAsCC,cAAAA,UAAU,MAAM,UAAU;AAChE,YAAA,QAAQ,KAAK,QAAQ,KAAK;AAChC,UAAI,QAAQ,IAAI;AAET,aAAA,OAAO,OAAO,CAAC;AAAA,MAAA,OACf;AAEL,aAAK,KAAK,KAAK;AAAA,MAAA;AAEjB,WAAK,qBAAqB,IAAI;AAE9B,WAAK,UAAU;AAAA,QACb,OAAO;AAAA,MAAA,CACR;AAAA,IAAA;;;;;;;;;;AC5FH,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}