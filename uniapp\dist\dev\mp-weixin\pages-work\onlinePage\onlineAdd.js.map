{"version": 3, "file": "onlineAdd.js", "sources": ["../../../../../src/pages-work/onlinePage/onlineAdd.vue", "../../../../../uniPage:/cGFnZXMtd29ya1xvbmxpbmVQYWdlXG9ubGluZUFkZC52dWU"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n  <PageLayout :navTitle=\"navTitle\" :backRouteName=\"backRouteName\">\r\n    <scroll-view scroll-y>\r\n      <online-loader\r\n        ref=\"online\"\r\n        :table=\"tableName\"\r\n        :title=\"navTitle\"\r\n        show-footer\r\n        @success=\"handleSuccess\"\r\n        @back=\"backRoute\"\r\n      ></online-loader>\r\n    </scroll-view>\r\n  </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport OnlineLoader from '@/components/online/online-loader.vue'\r\nimport router from '@/router'\r\nimport { http } from '@/utils/http'\r\nimport { useToast } from 'wot-design-uni'\r\nimport { onLoad } from '@dcloudio/uni-app'\r\nconst toast = useToast()\r\n// 定义响应式数据\r\nconst tableName = ref('')\r\nconst navTitle = ref('')\r\nconst flow_code_pre = ref('onl_')\r\nconst process_url = ref('/act/process/extActProcess/startMutilProcess')\r\nconst backRouteName = ref('onlineTable')\r\n\r\n// 引用组件\r\nconst online = ref(null)\r\n\r\n// 定义 initForm 方法\r\nconst initForm = (item) => {\r\n  // 表名\r\n  tableName.value = item.desformCode\r\n  // 表描述\r\n  navTitle.value = `表单【${item.desformName}】发起申请`\r\n  // 返回上一页面\r\n  item.backRouteName && (backRouteName.value = item.backRouteName)\r\n  nextTick(() => {\r\n    online.value.loadByTableName(tableName.value)\r\n  })\r\n}\r\n\r\nconst backRoute = () => {\r\n  router.back()\r\n}\r\n\r\n// 开启流程\r\nconst startProcess = (id) => {\r\n  const param = {\r\n    flowCode: flow_code_pre.value + tableName.value,\r\n    id: id,\r\n    formUrl: 'modules/bpm/task/form/OnlineFormDetail',\r\n    formUrlMobile: 'check/onlineForm/detail',\r\n  }\r\n  console.log('提交流程参数', param)\r\n  http.post(process_url.value, param).then((res: any) => {\r\n    toast.info(res.message)\r\n    if (res.success) {\r\n      router.back()\r\n    }\r\n  })\r\n}\r\n\r\n// 定义 handleSuccess 方法\r\nconst handleSuccess = (id) => {\r\n  callPrevPageMethod()\r\n}\r\n\r\n// 定义一个方法来调用上一页的方法\r\nconst callPrevPageMethod = () => {\r\n  uni.$emit('refreshList')\r\n  router.back()\r\n}\r\n// onLoad 生命周期钩子\r\nonLoad((option) => {\r\n  initForm(option)\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n//\r\n</style>\r\n", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-work/onlinePage/onlineAdd.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useToast", "ref", "nextTick", "router", "uni", "onLoad"], "mappings": ";;;;;;;;;;;;;AAoBA,MAAA,eAAyB,MAAA;;;;AAKXA,kBAAS,SAAA;AAEjB,UAAA,YAAYC,kBAAI,EAAE;AAClB,UAAA,WAAWA,kBAAI,EAAE;AACDA,kBAAAA,IAAI,MAAM;AACZA,kBAAAA,IAAI,8CAA8C;AAChE,UAAA,gBAAgBA,kBAAI,aAAa;AAGjC,UAAA,SAASA,kBAAI,IAAI;AAGjB,UAAA,WAAW,CAAC,SAAS;AAEzB,gBAAU,QAAQ,KAAK;AAEd,eAAA,QAAQ,MAAM,KAAK,WAAW;AAElC,WAAA,kBAAkB,cAAc,QAAQ,KAAK;AAClDC,oBAAAA,WAAS,MAAM;AACN,eAAA,MAAM,gBAAgB,UAAU,KAAK;AAAA,MAAA,CAC7C;AAAA,IACH;AAEA,UAAM,YAAY,MAAM;AACtBC,mBAAAA,OAAO,KAAK;AAAA,IACd;AAoBM,UAAA,gBAAgB,CAAC,OAAO;AACT,yBAAA;AAAA,IACrB;AAGA,UAAM,qBAAqB,MAAM;AAC/BC,oBAAA,MAAI,MAAM,aAAa;AACvBD,mBAAAA,OAAO,KAAK;AAAA,IACd;AAEAE,kBAAA,OAAO,CAAC,WAAW;AACjB,eAAS,MAAM;AAAA,IAAA,CAChB;;;;;;;;;;;;;;;;;;;;;;AClFD,GAAG,WAAW,eAAe;"}