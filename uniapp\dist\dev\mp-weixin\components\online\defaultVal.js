"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const utils_index = require("../../utils/index.js");
const utils_http = require("../../utils/http.js");
require("../../store/index.js");
const store_user = require("../../store/user.js");
const CustomExpression = {};
const ceKeys = Object.keys(CustomExpression);
ceKeys.join(",");
ceKeys.map((key) => CustomExpression[key]);
const normalRegExp = /#{([^}]+)?}/g;
const customRegExp = /{{([^}]+)?}}/g;
const fillRuleRegExp = /\${([^}]+)?}/g;
const ACTION_TYPES = { ADD: "add" };
function loadOneFieldDefVal(defVal, type, callback) {
  return __async(this, null, function* () {
    if (hasEffectiveValue(defVal)) {
      let value = yield handleDefaultValue(defVal, ACTION_TYPES.ADD, {});
      if ("number" === type && value) {
        value = Number.parseFloat(value);
      }
      callback(value);
    }
  });
}
function hasEffectiveValue(val) {
  if (val || val === 0) {
    return true;
  }
  return false;
}
function handleDefaultValue(defVal, action, getFormData) {
  return __async(this, null, function* () {
    if (defVal != null) {
      if (checkExpressionType(defVal)) {
        let value = yield getDefaultValue(defVal, action, getFormData);
        if (value != null) {
          return value;
        }
      }
    }
    return defVal;
  });
}
function checkExpressionType(defVal) {
  let normalCount = 0, customCount = 0, fillRuleCount = 0;
  defVal.replace(fillRuleRegExp, () => fillRuleCount++);
  if (fillRuleCount > 1) {
    logWarn(`表达式[${defVal}]不合法：只能同时填写一个填值规则表达式！`);
    return false;
  }
  defVal.replace(normalRegExp, () => normalCount++);
  defVal.replace(customRegExp, () => customCount++);
  let fillRuleOtherCount = normalCount + customCount;
  if (fillRuleCount > 0 && fillRuleOtherCount > 0) {
    logWarn(`表达式[${defVal}]不合法：填值规则表达式不能和其他表达式混用！`);
    return false;
  }
  return true;
}
function getRegExpMap(text, exp) {
  let map = /* @__PURE__ */ new Map();
  if (text && text.length > 0) {
    text.replace(exp, function(match, param, offset, string) {
      map.set(match, param.trim());
      return match;
    });
  }
  return map;
}
function getDefaultValue(defVal, action, getFormData) {
  return __async(this, null, function* () {
    {
      if (fillRuleRegExp.test(defVal)) {
        return yield executeRegExp(defVal, fillRuleRegExp, executeFillRuleExpression, [getFormData]);
      }
    }
    {
      defVal = yield executeRegExp(defVal, normalRegExp, executeNormalExpression);
      defVal = yield executeRegExp(defVal, customRegExp, executeCustomExpression);
      return defVal;
    }
  });
}
function executeRegExp(_0, _1, _2) {
  return __async(this, arguments, function* (defVal, regExp, execFun, otherParams = []) {
    let map = getRegExpMap(defVal, regExp);
    for (let origin of map.keys()) {
      let exp = map.get(origin);
      let result = yield execFun.apply(null, [exp, origin, ...otherParams]);
      if (origin === defVal) {
        return result;
      }
      defVal = replaceAll(defVal, origin, result);
    }
    return defVal;
  });
}
function executeNormalExpression(expression, origin) {
  return __async(this, null, function* () {
    let temp = /* @__PURE__ */ new Date();
    switch (expression) {
      case "date":
        return utils_index.formatDate(temp, "yyyy-MM-dd");
      case "time":
        return utils_index.formatDate(temp, "HH:mm:ss");
      case "datetime":
        return utils_index.formatDate(temp, "yyyy-MM-dd HH:mm:ss");
      default:
        let result = getUserInfoByExpression(expression);
        if (result != null) {
          return result;
        }
        return origin;
    }
  });
}
function getUserInfoByExpression(expression) {
  let userInfo = store_user.useUserStore().userInfo;
  if (userInfo) {
    switch (expression) {
      case "sysUserId":
        return userInfo.id;
      case "sysUserCode":
        return userInfo.username;
      case "sysUserName":
        return userInfo.realname;
      case "sysOrgCode":
        return userInfo.orgCode;
    }
  }
  return null;
}
function executeCustomExpression(expression, origin) {
  return __async(this, null, function* () {
    return expression;
  });
}
function executeFillRuleExpression(expression, origin, getFormData) {
  return __async(this, null, function* () {
    let url = `/sys/fillRule/executeRuleByCode/${expression}`;
    let formData = {};
    if (typeof getFormData === "function") {
      formData = getFormData();
    }
    let res = yield utils_http.http.put(url, formData);
    let { success, message, result } = res;
    console.log(success, message, result);
    if (success) {
      return result;
    } else {
      logError(`填值规则（${expression}）执行失败：${message}`);
      return origin;
    }
  });
}
function logWarn(message) {
  console.warn("[loadFieldDefVal]:", message);
}
function logError(message) {
  console.error("[loadFieldDefVal]:", message);
}
function replaceAll(text, checker, replacer) {
  let lastText = text;
  text = text.replace(checker, replacer);
  if (lastText !== text) {
    return replaceAll(text, checker, replacer);
  }
  return text;
}
exports.loadOneFieldDefVal = loadOneFieldDefVal;
//# sourceMappingURL=defaultVal.js.map
