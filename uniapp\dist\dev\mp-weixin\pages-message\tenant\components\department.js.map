{"version": 3, "file": "department.js", "sources": ["../../../../../../src/pages-message/tenant/components/department.vue", "../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvcGFnZXMtbWVzc2FnZS90ZW5hbnQvY29tcG9uZW50cy9kZXBhcnRtZW50LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"wrap\">\r\n    <view class=\"content\">\r\n      <scroll-view class=\"scrollArea\" scroll-y>\r\n        <template v-if=\"dataSource.length\">\r\n          <template v-for=\"(item, index) in dataSource\">\r\n            <wd-cell border clickable @click=\"handleClick(item)\">\r\n              <template #icon>\r\n                <wd-img\r\n                  customClass=\"mr2\"\r\n                  :radius=\"getRadius(item)\"\r\n                  :width=\"30\"\r\n                  :height=\"30\"\r\n                  :src=\"getImg(item)\"\r\n                ></wd-img>\r\n              </template>\r\n              <template #title>\r\n                <view class=\"content text-gray-4\">\r\n                  <text>{{ getName(item) }}</text>\r\n                </view>\r\n              </template>\r\n            </wd-cell>\r\n          </template>\r\n        </template>\r\n        <template v-else>\r\n          <wd-status-tip image=\"content\" tip=\"暂无内容\" />\r\n        </template>\r\n      </scroll-view>\r\n    </view>\r\n  </view>\r\n  <wd-toast />\r\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { onLaunch, onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'\r\nimport { nextTick, onMounted, ref } from 'vue'\r\nimport { useUserStore } from '@/store/user'\r\nimport { http } from '@/utils/http'\r\nimport { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'\r\nimport { useRouter } from '@/plugin/uni-mini-router'\r\nimport { useParamsStore } from '@/store/page-params'\r\nimport defaultAvatar from '@/static/default-avatar.png'\r\nimport folderImg from '@/static/folder.png'\r\n\r\nconst router = useRouter()\r\nconst userStore = useUserStore()\r\nconst toast = useToast()\r\nconst props = defineProps(['tenantId'])\r\nconst dataSource: any = ref([])\r\nconst paramsStore = useParamsStore()\r\nconst api = {\r\n  depTree: '/sys/sysDepart/queryBookDepTreeSync',\r\n  list: '/sys/user/list',\r\n}\r\nconst getImg = (item) => {\r\n  if (item.parentId != null) {\r\n    return folderImg\r\n  } else {\r\n    return item.avatar\r\n  }\r\n}\r\nconst getName = (item) => {\r\n  if (item.parentId != null) {\r\n    return item.departName\r\n  } else {\r\n    return item.realname\r\n  }\r\n}\r\nconst getRadius = (item) => {\r\n  return item.parentId != null ? null : '50%'\r\n}\r\n\r\nconst handleClick = (item) => {\r\n  if (item.parentId != null) {\r\n    query({ id: item.id })\r\n  } else {\r\n    paramsStore.setPageParams('personPage', { data: item })\r\n    router.push({ name: 'personPage' })\r\n  }\r\n}\r\nconst query = (params: any = {}) => {\r\n  const pararms = { pid: params.id ?? '', departId: params.id, tenantId: props.tenantId }\r\n  Promise.all([\r\n    http.get(api.depTree, pararms),\r\n    pararms.pid\r\n      ? http.get(api.list, pararms)\r\n      : Promise.resolve({ success: true, result: { records: [] } }),\r\n  ])\r\n    .then((res: any) => {\r\n      if (res[0].success == true && res[1].success == true) {\r\n        const result = res[0]?.result ?? []\r\n        const records = res[1]?.result?.records ?? []\r\n        const data = [...result, ...records]\r\n        if (params.id) {\r\n          // 证明是点击\r\n          if (data.length) {\r\n            dataSource.value = data\r\n          } else {\r\n            toast.warning('下一级无数据~')\r\n          }\r\n        } else {\r\n          dataSource.value = data\r\n        }\r\n      }\r\n    })\r\n    .catch((res) => {})\r\n}\r\nquery()\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n:deep(.wd-cell) {\r\n  .wd-cell__right {\r\n    display: none;\r\n  }\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-message/tenant/components/department.vue'\nwx.createComponent(Component)"], "names": ["useRouter", "useUserStore", "useToast", "ref", "useParamsStore", "folderImg", "http", "_a"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA,UAAM,SAASA,gCAAAA,UAAU;AACPC,eAAa,aAAA;AAC/B,UAAM,QAAQC,cAAAA,SAAS;AACvB,UAAM,QAAQ;AACR,UAAA,aAAkBC,cAAI,IAAA,EAAE;AAC9B,UAAM,cAAcC,iBAAAA,eAAe;AACnC,UAAM,MAAM;AAAA,MACV,SAAS;AAAA,MACT,MAAM;AAAA,IACR;AACM,UAAA,SAAS,CAAC,SAAS;AACnB,UAAA,KAAK,YAAY,MAAM;AAClB,eAAAC,cAAA;AAAA,MAAA,OACF;AACL,eAAO,KAAK;AAAA,MAAA;AAAA,IAEhB;AACM,UAAA,UAAU,CAAC,SAAS;AACpB,UAAA,KAAK,YAAY,MAAM;AACzB,eAAO,KAAK;AAAA,MAAA,OACP;AACL,eAAO,KAAK;AAAA,MAAA;AAAA,IAEhB;AACM,UAAA,YAAY,CAAC,SAAS;AACnB,aAAA,KAAK,YAAY,OAAO,OAAO;AAAA,IACxC;AAEM,UAAA,cAAc,CAAC,SAAS;AACxB,UAAA,KAAK,YAAY,MAAM;AACzB,cAAM,EAAE,IAAI,KAAK,GAAA,CAAI;AAAA,MAAA,OAChB;AACL,oBAAY,cAAc,cAAc,EAAE,MAAM,MAAM;AACtD,eAAO,KAAK,EAAE,MAAM,aAAA,CAAc;AAAA,MAAA;AAAA,IAEtC;AACA,UAAM,QAAQ,CAAC,SAAc,OAAO;;AAC5B,YAAA,UAAU,EAAE,MAAK,YAAO,OAAP,YAAa,IAAI,UAAU,OAAO,IAAI,UAAU,MAAM,SAAS;AACtF,cAAQ,IAAI;AAAA,QACVC,WAAAA,KAAK,IAAI,IAAI,SAAS,OAAO;AAAA,QAC7B,QAAQ,MACJA,WAAA,KAAK,IAAI,IAAI,MAAM,OAAO,IAC1B,QAAQ,QAAQ,EAAE,SAAS,MAAM,QAAQ,EAAE,SAAS,CAAA,IAAM,CAAA;AAAA,MAAA,CAC/D,EACE,KAAK,CAAC,QAAa;;AACd,YAAA,IAAI,CAAC,EAAE,WAAW,QAAQ,IAAI,CAAC,EAAE,WAAW,MAAM;AACpD,gBAAM,UAAS,MAAAC,MAAA,IAAI,CAAC,MAAL,gBAAAA,IAAQ,WAAR,YAAkB,CAAC;AAClC,gBAAM,WAAU,qBAAI,CAAC,MAAL,mBAAQ,WAAR,mBAAgB,YAAhB,YAA2B,CAAC;AAC5C,gBAAM,OAAO,CAAC,GAAG,QAAQ,GAAG,OAAO;AACnC,cAAI,OAAO,IAAI;AAEb,gBAAI,KAAK,QAAQ;AACf,yBAAW,QAAQ;AAAA,YAAA,OACd;AACL,oBAAM,QAAQ,SAAS;AAAA,YAAA;AAAA,UACzB,OACK;AACL,uBAAW,QAAQ;AAAA,UAAA;AAAA,QACrB;AAAA,MACF,CACD,EACA,MAAM,CAAC,QAAQ;AAAA,MAAA,CAAE;AAAA,IACtB;AACM,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1GN,GAAG,gBAAgB,SAAS;"}