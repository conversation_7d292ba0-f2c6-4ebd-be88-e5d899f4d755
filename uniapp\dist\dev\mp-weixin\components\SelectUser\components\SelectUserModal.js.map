{"version": 3, "file": "SelectUserModal.js", "sources": ["../../../../../../src/components/SelectUser/components/SelectUserModal.vue", "../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvY29tcG9uZW50cy9TZWxlY3RVc2VyL2NvbXBvbmVudHMvU2VsZWN0VXNlck1vZGFsLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <wd-popup position=\"bottom\" v-model=\"show\">\r\n    <PageLayout\r\n      :navTitle=\"modalTitle\"\r\n      type=\"popup\"\r\n      navRightText=\"确定\"\r\n      @navRight=\"handleConfirm\"\r\n      @navBack=\"handleCancel\"\r\n    >\r\n      <view class=\"wrap\">\r\n        <z-paging\r\n          ref=\"paging\"\r\n          :fixed=\"false\"\r\n          v-model=\"dataList\"\r\n          @query=\"queryList\"\r\n          :default-page-size=\"15\"\r\n        >\r\n          <template #top>\r\n            <wd-search\r\n              hide-cancel\r\n              :placeholder=\"search.placeholder\"\r\n              v-model=\"search.keyword\"\r\n              @search=\"handleSearch\"\r\n              @clear=\"handleClear\"\r\n            />\r\n          </template>\r\n          <template v-if=\"multi\">\r\n            <wd-checkbox-group shape=\"square\" v-model=\"checkedValue\">\r\n              <template v-for=\"(item, index) in dataList\" :key=\"index\">\r\n                <view class=\"list\" @click=\"hanldeCheck(index, item.username)\">\r\n                  <view class=\"left text-gray-5\">\r\n                    <wd-img\r\n                      custom-class=\"avatar\"\r\n                      radius=\"50%\"\r\n                      height=\"40\"\r\n                      width=\"40\"\r\n                      :src=\"getAvatar(item.avatar)\"\r\n                    ></wd-img>\r\n                    <view class=\"subContent\">\r\n                      <text>账号：{{ item.username }}</text>\r\n                      <text>姓名：{{ item.realname }}</text>\r\n                    </view>\r\n                  </view>\r\n                  <view class=\"right\" @click.stop>\r\n                    <wd-checkbox ref=\"checkboxRef\" :modelValue=\"item.username\"></wd-checkbox>\r\n                  </view>\r\n                </view>\r\n              </template>\r\n            </wd-checkbox-group>\r\n          </template>\r\n          <template v-else>\r\n            <wd-radio-group shape=\"dot\" v-model=\"checkedValue\">\r\n              <template v-for=\"(item, index) in dataList\" :key=\"index\">\r\n                <wd-cell>\r\n                  <view class=\"list\" @click=\"hanldeCheck(index, item.username)\">\r\n                    <view class=\"left text-gray-5\">\r\n                      <wd-img\r\n                        custom-class=\"avatar\"\r\n                        radius=\"50%\"\r\n                        height=\"40\"\r\n                        width=\"40\"\r\n                        :src=\"getAvatar(item.avatar)\"\r\n                      ></wd-img>\r\n                      <view class=\"subContent\">\r\n                        <text>账号：{{ item.username }}</text>\r\n                        <text>姓名：{{ item.realname }}</text>\r\n                      </view>\r\n                    </view>\r\n                    <view class=\"right\" @click.stop>\r\n                      <wd-radio :value=\"item.username\"></wd-radio>\r\n                    </view>\r\n                  </view>\r\n                </wd-cell>\r\n              </template>\r\n            </wd-radio-group>\r\n          </template>\r\n        </z-paging>\r\n      </view>\r\n    </PageLayout>\r\n  </wd-popup>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, reactive, nextTick } from 'vue'\r\nimport { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'\r\nimport { http } from '@/utils/http'\r\nimport { isArray, isString } from '@/utils/is'\r\nimport { cache, getFileAccessHttpUrl } from '@/common/uitls'\r\nimport defaultAvatar from '@/static/default-avatar.png'\r\ndefineOptions({\r\n  name: 'SelectUserModal',\r\n  options: {\r\n    styleIsolation: 'shared',\r\n  },\r\n})\r\nconst props = defineProps({\r\n  multi: {\r\n    type: Boolean,\r\n    default: true,\r\n  },\r\n  modalTitle: {\r\n    type: String,\r\n    default: '选择用户',\r\n  },\r\n  maxSelectCount: {\r\n    type: Number,\r\n  },\r\n  selected: {\r\n    type: [Array, String],\r\n    default: '',\r\n  },\r\n})\r\nconst emit = defineEmits(['change', 'close'])\r\nconst toast = useToast()\r\nconst show = ref(true)\r\nconst api = {\r\n  selectUserList: '/sys/user/selectUserList',\r\n  userlist: '/sys/user/list',\r\n}\r\nconst paging = ref(null)\r\nconst dataList = ref([])\r\nconst checkedValue: any = ref(props.multi ? [] : '')\r\nconst checkboxRef = ref(null)\r\nconst search = reactive({\r\n  keyword: '',\r\n  placeholder: '输入姓名可搜索',\r\n  field: 'realname',\r\n})\r\n\r\nconst handleClose = () => {\r\n  setTimeout(() => {\r\n    emit('close')\r\n  }, 400)\r\n}\r\nconst handleConfirm = () => {\r\n  if (checkedValue.value.length == 0) {\r\n    toast.warning('还没选择~')\r\n    return\r\n  }\r\n  const result = []\r\n  let value = checkedValue.value\r\n  if (!Array.isArray(checkedValue.value)) {\r\n    value = [checkedValue.value]\r\n  }\r\n  value.forEach((username, index) => {\r\n    const findIndex = dataList.value.findIndex((item) => item['username'] === username)\r\n    result.push(dataList.value[findIndex])\r\n  })\r\n  show.value = false\r\n  emit('change', result)\r\n  handleClose()\r\n}\r\nconst handleCancel = () => {\r\n  show.value = false\r\n  handleClose()\r\n  console.log('取消了~')\r\n}\r\n// 搜索\r\nfunction handleSearch() {\r\n  paging.value.reload()\r\n}\r\n// 清除搜索条件\r\nfunction handleClear() {\r\n  search.keyword = ''\r\n  handleSearch()\r\n}\r\nconst hanldeCheck = (index, username) => {\r\n  if (props.multi) {\r\n    if (Array.isArray(checkboxRef.value)) {\r\n      checkboxRef.value[index].toggle()\r\n      nextTick(() => {\r\n        if (props.maxSelectCount) {\r\n          if (checkedValue.value.length > props.maxSelectCount) {\r\n            toast.warning(`最多可选择${props.maxSelectCount}个用户`)\r\n            // 超过个数取消\r\n            checkboxRef.value[index].toggle()\r\n          }\r\n        }\r\n      })\r\n    }\r\n  } else {\r\n    checkedValue.value = username\r\n  }\r\n}\r\n\r\nconst getAvatar = (url) => {\r\n  let result = getFileAccessHttpUrl(url)\r\n  if (result.length) {\r\n    return result\r\n  } else {\r\n    return defaultAvatar\r\n  }\r\n}\r\n\r\nconst queryList = (pageNo, pageSize) => {\r\n  const pararms = { pageNo, pageSize, column: 'createTime', order: 'desc' }\r\n  if (search.keyword) {\r\n    pararms[search.field] = `*${search.keyword}*`\r\n  }\r\n  http\r\n    .get(`${api.userlist}`, pararms)\r\n    .then((res: any) => {\r\n      if (res.success && res.result.records) {\r\n        paging.value.complete(res.result.records ?? [])\r\n      } else {\r\n        paging.value.complete(false)\r\n      }\r\n    })\r\n    .catch((err) => {})\r\n}\r\nconst init = () => {\r\n  if (props.selected.length) {\r\n    if (props.multi) {\r\n      if (isArray(props.selected)) {\r\n        checkedValue.value = props.selected\r\n      } else if (isString(props.selected)) {\r\n        checkedValue.value = props.selected.split(',')\r\n      }\r\n    } else {\r\n      if (isString(props.selected)) {\r\n        checkedValue.value = props.selected\r\n      } else if (isArray(props.selected)) {\r\n        checkedValue.value = props.selected.join(',')\r\n      }\r\n    }\r\n  }\r\n}\r\ninit()\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n:deep(.wd-cell) {\r\n  --wot-color-white: tranparent;\r\n  --wot-cell-padding: 0;\r\n  .wd-cell__wrapper {\r\n    --wot-cell-wrapper-padding: 0;\r\n  }\r\n  .wd-cell__left {\r\n    display: none;\r\n  }\r\n}\r\n:deep(.wd-checkbox-group) {\r\n  --wot-checkbox-bg: tranparent;\r\n}\r\n:deep(.wd-radio-group) {\r\n  --wot-radio-bg: tranparent;\r\n}\r\n.list {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  background: #fff;\r\n  padding: 16px;\r\n  margin-top: 16px;\r\n  .left {\r\n    display: flex;\r\n    align-items: center;\r\n    text-align: left;\r\n    .avatar {\r\n      margin-right: 8px;\r\n      background-color: #e9e9e9;\r\n    }\r\n    .subContent {\r\n      display: flex;\r\n      flex-direction: column;\r\n    }\r\n  }\r\n  .right {\r\n    :deep(.wd-checkbox) {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n.wrap {\r\n  height: 100%;\r\n}\r\n:deep(.wd-popup-wrapper) {\r\n  .wd-popup {\r\n    top: 100px;\r\n  }\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/components/SelectUser/components/SelectUserModal.vue'\nwx.createComponent(Component)"], "names": ["useToast", "ref", "reactive", "nextTick", "getFileAccessHttpUrl", "defaultAvatar", "http", "isArray", "isString"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+FA,UAAM,QAAQ;AAiBd,UAAM,OAAO;AACb,UAAM,QAAQA,cAAAA,SAAS;AACjB,UAAA,OAAOC,kBAAI,IAAI;AACrB,UAAM,MAAM;AAAA,MAEV,UAAU;AAAA,IACZ;AACM,UAAA,SAASA,kBAAI,IAAI;AACjB,UAAA,WAAWA,cAAI,IAAA,EAAE;AACvB,UAAM,eAAoBA,cAAAA,IAAI,MAAM,QAAQ,CAAA,IAAK,EAAE;AAC7C,UAAA,cAAcA,kBAAI,IAAI;AAC5B,UAAM,SAASC,cAAAA,SAAS;AAAA,MACtB,SAAS;AAAA,MACT,aAAa;AAAA,MACb,OAAO;AAAA,IAAA,CACR;AAED,UAAM,cAAc,MAAM;AACxB,iBAAW,MAAM;AACf,aAAK,OAAO;AAAA,SACX,GAAG;AAAA,IACR;AACA,UAAM,gBAAgB,MAAM;AACtB,UAAA,aAAa,MAAM,UAAU,GAAG;AAClC,cAAM,QAAQ,OAAO;AACrB;AAAA,MAAA;AAEF,YAAM,SAAS,CAAC;AAChB,UAAI,QAAQ,aAAa;AACzB,UAAI,CAAC,MAAM,QAAQ,aAAa,KAAK,GAAG;AAC9B,gBAAA,CAAC,aAAa,KAAK;AAAA,MAAA;AAEvB,YAAA,QAAQ,CAAC,UAAU,UAAU;AAC3B,cAAA,YAAY,SAAS,MAAM,UAAU,CAAC,SAAS,KAAK,UAAU,MAAM,QAAQ;AAClF,eAAO,KAAK,SAAS,MAAM,SAAS,CAAC;AAAA,MAAA,CACtC;AACD,WAAK,QAAQ;AACb,WAAK,UAAU,MAAM;AACT,kBAAA;AAAA,IACd;AACA,UAAM,eAAe,MAAM;AACzB,WAAK,QAAQ;AACD,kBAAA;AACZ,cAAQ,IAAI,MAAM;AAAA,IACpB;AAEA,aAAS,eAAe;AACtB,aAAO,MAAM,OAAO;AAAA,IAAA;AAGtB,aAAS,cAAc;AACrB,aAAO,UAAU;AACJ,mBAAA;AAAA,IAAA;AAET,UAAA,cAAc,CAAC,OAAO,aAAa;AACvC,UAAI,MAAM,OAAO;AACf,YAAI,MAAM,QAAQ,YAAY,KAAK,GAAG;AACxB,sBAAA,MAAM,KAAK,EAAE,OAAO;AAChCC,wBAAAA,WAAS,MAAM;AACb,gBAAI,MAAM,gBAAgB;AACxB,kBAAI,aAAa,MAAM,SAAS,MAAM,gBAAgB;AACpD,sBAAM,QAAQ,QAAQ,MAAM,cAAc,KAAK;AAEnC,4BAAA,MAAM,KAAK,EAAE,OAAO;AAAA,cAAA;AAAA,YAClC;AAAA,UACF,CACD;AAAA,QAAA;AAAA,MACH,OACK;AACL,qBAAa,QAAQ;AAAA,MAAA;AAAA,IAEzB;AAEM,UAAA,YAAY,CAAC,QAAQ;AACrB,UAAA,SAASC,kCAAqB,GAAG;AACrC,UAAI,OAAO,QAAQ;AACV,eAAA;AAAA,MAAA,OACF;AACE,eAAAC,cAAA;AAAA,MAAA;AAAA,IAEX;AAEM,UAAA,YAAY,CAAC,QAAQ,aAAa;AACtC,YAAM,UAAU,EAAE,QAAQ,UAAU,QAAQ,cAAc,OAAO,OAAO;AACxE,UAAI,OAAO,SAAS;AAClB,gBAAQ,OAAO,KAAK,IAAI,IAAI,OAAO,OAAO;AAAA,MAAA;AAGzCC,sBAAA,IAAI,GAAG,IAAI,QAAQ,IAAI,OAAO,EAC9B,KAAK,CAAC,QAAa;;AAClB,YAAI,IAAI,WAAW,IAAI,OAAO,SAAS;AACrC,iBAAO,MAAM,UAAS,SAAI,OAAO,YAAX,YAAsB,EAAE;AAAA,QAAA,OACzC;AACE,iBAAA,MAAM,SAAS,KAAK;AAAA,QAAA;AAAA,MAC7B,CACD,EACA,MAAM,CAAC,QAAQ;AAAA,MAAA,CAAE;AAAA,IACtB;AACA,UAAM,OAAO,MAAM;AACb,UAAA,MAAM,SAAS,QAAQ;AACzB,YAAI,MAAM,OAAO;AACX,cAAAC,SAAA,QAAQ,MAAM,QAAQ,GAAG;AAC3B,yBAAa,QAAQ,MAAM;AAAA,UAClB,WAAAC,SAAA,SAAS,MAAM,QAAQ,GAAG;AACnC,yBAAa,QAAQ,MAAM,SAAS,MAAM,GAAG;AAAA,UAAA;AAAA,QAC/C,OACK;AACD,cAAAA,SAAA,SAAS,MAAM,QAAQ,GAAG;AAC5B,yBAAa,QAAQ,MAAM;AAAA,UAClB,WAAAD,SAAA,QAAQ,MAAM,QAAQ,GAAG;AAClC,yBAAa,QAAQ,MAAM,SAAS,KAAK,GAAG;AAAA,UAAA;AAAA,QAC9C;AAAA,MACF;AAAA,IAEJ;AACK,SAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClOL,GAAG,gBAAgB,SAAS;"}