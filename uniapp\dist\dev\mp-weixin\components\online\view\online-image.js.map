{"version": 3, "file": "online-image.js", "sources": ["../../../../../../src/components/online/view/online-image.vue", "../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvY29tcG9uZW50cy9vbmxpbmUvdmlldy9vbmxpbmUtaW1hZ2UudnVl"], "sourcesContent": ["<template>\r\n    <wd-upload\r\n      v-model:file-list=\"fileList\"\r\n      :accept=\"uploadFileType\"\r\n      :upload-method=\"customUpload\"\r\n      :disabled=\"disabled\"\r\n      :before-remove=\"delFile\"\r\n    ></wd-upload>\r\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport type { UploadMethod } from '@/uni_modules/wot-design-uni/components/wd-upload/types'\r\nimport { getEnvBaseUploadUrl } from '@/utils'\r\nimport { useUserStore } from '@/store'\r\nimport { getFileAccessHttpUrl } from '@/common/uitls'\r\nimport {isString} from \"@/utils/is\";\r\nimport {useToast} from \"wot-design-uni\";\r\nconst toast = useToast()\r\nconst VITE_UPLOAD_BASEURL = `${getEnvBaseUploadUrl()}`\r\n// 接收 props\r\nconst props = defineProps({\r\n  title: {\r\n    type: String,\r\n    default: '',\r\n    required: false,\r\n  },\r\n  value: {\r\n    type: String,\r\n    required: false,\r\n  },\r\n  disabled: {\r\n    type: Boolean,\r\n    default: false,\r\n    required: false,\r\n  },\r\n  name: {\r\n    type: String,\r\n    default: '',\r\n    required: false,\r\n  },\r\n  uploadFileType: {\r\n    type: String,\r\n    default: 'image',\r\n    required: false,\r\n  },\r\n})\r\n\r\n// 定义 emits\r\nconst emit = defineEmits(['change', 'update:value'])\r\n// 定义响应式数据\r\nconst fileList = ref([])\r\n/**\r\n * 自定义上传方法\r\n * @param file\r\n * @param formData\r\n * @param options\r\n */\r\nconst customUpload: UploadMethod = (file, formData, options) => {\r\n  const userStore = useUserStore()\r\n  const uploadTask = uni.uploadFile({\r\n    url: VITE_UPLOAD_BASEURL,\r\n    header: {\r\n      'X-Access-Token': userStore.userInfo.token,\r\n      'X-Tenant-Id': userStore.userInfo.tenantId,\r\n      ...options.header,\r\n    },\r\n    name: options.name,\r\n    fileName: options.name,\r\n    fileType: options.fileType,\r\n    formData,\r\n    filePath: file.url,\r\n    success(res: any) {\r\n      if (res.statusCode === options.statusCode) {\r\n        let data = res.data;\r\n        if (data && isString(data)) {\r\n            data = JSON.parse(data)\r\n        }\r\n        // 设置上传成功\r\n        if (data && data.success) {\r\n            const file = {\r\n                id: new Date().getTime(),\r\n                name: options.name,\r\n                path: data.message,\r\n                url: getFileAccessHttpUrl(data.message),\r\n            }\r\n            fileList.value.unshift(file)\r\n            changeOnlineFormValue()\r\n        }\r\n      } else {\r\n        // 设置上传失败\r\n        options.onError({ ...res, errMsg: res.errMsg || '' }, file, formData)\r\n      }\r\n    },\r\n    fail(err) {\r\n      console.info('upload fail', err)\r\n      // 设置上传失败\r\n      options.onError(err, file, formData)\r\n    },\r\n  })\r\n  // 设置当前文件加载的百分比\r\n  uploadTask.onProgressUpdate((res) => {\r\n    options.onProgress(res, file)\r\n  })\r\n}\r\n\r\nconst changeOnlineFormValue = () => {\r\n    console.log('changeOnlineFormValue fileList.value', fileList)\r\n  const arr = fileList.value.map((item) => item['path'])\r\n  const str = arr.join(',')\r\n  emit('change', str)\r\n  emit('update:value', str)\r\n}\r\n\r\nconst delFile = ({ file, fileList, resolve }) => {\r\n  uni.showModal({\r\n    title: '提示',\r\n    content: '确定要删除吗？',\r\n    cancelText: '取消',\r\n    confirmText: '确定',\r\n    success: (res) => {\r\n      if (res.confirm) {\r\n        console.log('当前删除文件', file)\r\n        changeOnlineFormValue()\r\n        toast.success('删除成功')\r\n        resolve(true)\r\n      }\r\n    },\r\n    fail: (err) => {\r\n      console.log(err)\r\n      resolve(false)\r\n    },\r\n  })\r\n}\r\nconst loadFile = () => {\r\n  if (!props.value || props.value.length === 0) {\r\n    return\r\n  }\r\n  const pathArr = props.value.split(',')\r\n  const fileArray = []\r\n  pathArr.forEach((path) => {\r\n    const seg = path.lastIndexOf('/')\r\n    fileArray.push({\r\n      name: path.substr(seg < 0 ? 0 : seg),\r\n      path: path,\r\n      url: getFileAccessHttpUrl(path),\r\n    })\r\n  })\r\n  console.log('当前图片回显数据', fileArray)\r\n  fileList.value = [...fileArray]\r\n}\r\n\r\n// 监听 value 的变化\r\nwatch(\r\n  () => props.value,\r\n  () => {\r\n    loadFile()\r\n  },\r\n  { immediate: true },\r\n)\r\n\r\n// 组件挂载时加载文件\r\nonMounted(() => {\r\n  loadFile()\r\n})\r\n</script>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/components/online/view/online-image.vue'\nwx.createComponent(Component)"], "names": ["useToast", "getEnvBaseUploadUrl", "ref", "useUserStore", "uni", "isString", "file", "getFileAccessHttpUrl", "fileList", "watch", "onMounted", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBA,UAAM,QAAQA,cAAAA,SAAS;AACjB,UAAA,sBAAsB,GAAGC,YAAA,oBAAA,CAAqB;AAEpD,UAAM,QAAQ;AA4Bd,UAAM,OAAO;AAEP,UAAA,WAAWC,cAAI,IAAA,EAAE;AAOvB,UAAM,eAA6B,CAAC,MAAM,UAAU,YAAY;AAC9D,YAAM,YAAYC,WAAAA,aAAa;AACzB,YAAA,aAAaC,oBAAI,WAAW;AAAA,QAChC,KAAK;AAAA,QACL,QAAQ;AAAA,UACN,kBAAkB,UAAU,SAAS;AAAA,UACrC,eAAe,UAAU,SAAS;AAAA,WAC/B,QAAQ;AAAA,QAEb,MAAM,QAAQ;AAAA,QACd,UAAU,QAAQ;AAAA,QAClB,UAAU,QAAQ;AAAA,QAClB;AAAA,QACA,UAAU,KAAK;AAAA,QACf,QAAQ,KAAU;AACZ,cAAA,IAAI,eAAe,QAAQ,YAAY;AACzC,gBAAI,OAAO,IAAI;AACX,gBAAA,QAAQC,kBAAS,IAAI,GAAG;AACjB,qBAAA,KAAK,MAAM,IAAI;AAAA,YAAA;AAGtB,gBAAA,QAAQ,KAAK,SAAS;AACtB,oBAAMC,QAAO;AAAA,gBACT,KAAI,oBAAI,KAAK,GAAE,QAAQ;AAAA,gBACvB,MAAM,QAAQ;AAAA,gBACd,MAAM,KAAK;AAAA,gBACX,KAAKC,aAAAA,qBAAqB,KAAK,OAAO;AAAA,cAC1C;AACS,uBAAA,MAAM,QAAQD,KAAI;AACL,oCAAA;AAAA,YAAA;AAAA,UAC1B,OACK;AAEG,oBAAA,QAAQ,iCAAK,MAAL,EAAU,QAAQ,IAAI,UAAU,GAAA,IAAM,MAAM,QAAQ;AAAA,UAAA;AAAA,QAExE;AAAA,QACA,KAAK,KAAK;AACA,kBAAA,KAAK,eAAe,GAAG;AAEvB,kBAAA,QAAQ,KAAK,MAAM,QAAQ;AAAA,QAAA;AAAA,MACrC,CACD;AAEU,iBAAA,iBAAiB,CAAC,QAAQ;AAC3B,gBAAA,WAAW,KAAK,IAAI;AAAA,MAAA,CAC7B;AAAA,IACH;AAEA,UAAM,wBAAwB,MAAM;AACxB,cAAA,IAAI,wCAAwC,QAAQ;AACxD,YAAA,MAAM,SAAS,MAAM,IAAI,CAAC,SAAS,KAAK,MAAM,CAAC;AAC/C,YAAA,MAAM,IAAI,KAAK,GAAG;AACxB,WAAK,UAAU,GAAG;AAClB,WAAK,gBAAgB,GAAG;AAAA,IAC1B;AAEA,UAAM,UAAU,CAAC,EAAE,MAAM,UAAAE,WAAU,cAAc;AAC/CJ,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACP,oBAAA,IAAI,UAAU,IAAI;AACJ,kCAAA;AACtB,kBAAM,QAAQ,MAAM;AACpB,oBAAQ,IAAI;AAAA,UAAA;AAAA,QAEhB;AAAA,QACA,MAAM,CAAC,QAAQ;AACb,kBAAQ,IAAI,GAAG;AACf,kBAAQ,KAAK;AAAA,QAAA;AAAA,MACf,CACD;AAAA,IACH;AACA,UAAM,WAAW,MAAM;AACrB,UAAI,CAAC,MAAM,SAAS,MAAM,MAAM,WAAW,GAAG;AAC5C;AAAA,MAAA;AAEF,YAAM,UAAU,MAAM,MAAM,MAAM,GAAG;AACrC,YAAM,YAAY,CAAC;AACX,cAAA,QAAQ,CAAC,SAAS;AAClB,cAAA,MAAM,KAAK,YAAY,GAAG;AAChC,kBAAU,KAAK;AAAA,UACb,MAAM,KAAK,OAAO,MAAM,IAAI,IAAI,GAAG;AAAA,UACnC;AAAA,UACA,KAAKG,kCAAqB,IAAI;AAAA,QAAA,CAC/B;AAAA,MAAA,CACF;AACO,cAAA,IAAI,YAAY,SAAS;AACxB,eAAA,QAAQ,CAAC,GAAG,SAAS;AAAA,IAChC;AAGAE,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,MAAM;AACK,iBAAA;AAAA,MACX;AAAA,MACA,EAAE,WAAW,KAAK;AAAA,IACpB;AAGAC,kBAAAA,UAAU,MAAM;AACL,eAAA;AAAA,IAAA,CACV;;;;;;;;;;;;;;;AClKD,GAAG,gBAAgBC,SAAS;"}