{"version": 3, "file": "refresher.js", "sources": ["../../../../../../../../../src/uni_modules/z-paging/components/z-paging/js/modules/refresher.js"], "sourcesContent": ["// [z-paging]下拉刷新view模块\r\nimport u from '.././z-paging-utils'\r\nimport c from '.././z-paging-constant'\r\nimport Enum from '.././z-paging-enum'\r\n\r\n// #ifdef APP-NVUE\r\nconst weexAnimation = weex.requireModule('animation');\r\n// #endif\r\nexport default {\r\n\tprops: {\r\n\t\t// 下拉刷新的主题样式，支持black，white，默认black\r\n\t\trefresherThemeStyle: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('refresherThemeStyle', '')\r\n\t\t},\r\n\t\t// 自定义下拉刷新中左侧图标的样式\r\n\t\trefresherImgStyle: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault: u.gc('refresherImgStyle', {})\r\n\t\t},\r\n\t\t// 自定义下拉刷新中右侧状态描述文字的样式\r\n\t\trefresherTitleStyle: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault: u.gc('refresherTitleStyle', {})\r\n\t\t},\r\n\t\t// 自定义下拉刷新中右侧最后更新时间文字的样式(show-refresher-update-time为true时有效)\r\n\t\trefresherUpdateTimeStyle: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault: u.gc('refresherUpdateTimeStyle', {})\r\n\t\t},\r\n\t\t// 在微信小程序和QQ小程序中，是否实时监听下拉刷新中进度，默认为否\r\n\t\twatchRefresherTouchmove: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('watchRefresherTouchmove', false)\r\n\t\t},\r\n\t\t// 底部加载更多的主题样式，支持black，white，默认black\r\n\t\tloadingMoreThemeStyle: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('loadingMoreThemeStyle', '')\r\n\t\t},\r\n\t\t// 是否只使用下拉刷新，设置为true后将关闭mounted自动请求数据、关闭滚动到底部加载更多，强制隐藏空数据图。默认为否\r\n\t\trefresherOnly: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('refresherOnly', false)\r\n\t\t},\r\n\t\t// 自定义下拉刷新默认状态下回弹动画时间，单位为毫秒，默认为100毫秒，nvue无效\r\n\t\trefresherDefaultDuration: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: u.gc('refresherDefaultDuration', 100)\r\n\t\t},\r\n\t\t// 自定义下拉刷新结束以后延迟回弹的时间，单位为毫秒，默认为0\r\n\t\trefresherCompleteDelay: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: u.gc('refresherCompleteDelay', 0)\r\n\t\t},\r\n\t\t// 自定义下拉刷新结束回弹动画时间，单位为毫秒，默认为300毫秒(refresherEndBounceEnabled为false时，refresherCompleteDuration为设定值的1/3)，nvue无效\r\n\t\trefresherCompleteDuration: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: u.gc('refresherCompleteDuration', 300)\r\n\t\t},\r\n\t\t// 自定义下拉刷新中是否允许列表滚动，默认为是\r\n\t\trefresherRefreshingScrollable: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('refresherRefreshingScrollable', true)\r\n\t\t},\r\n\t\t// 自定义下拉刷新结束状态下是否允许列表滚动，默认为否\r\n\t\trefresherCompleteScrollable: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('refresherCompleteScrollable', false)\r\n\t\t},\r\n\t\t// 是否使用自定义的下拉刷新，默认为是，即使用z-paging的下拉刷新。设置为false即代表使用uni scroll-view自带的下拉刷新，h5、App、微信小程序以外的平台不支持uni scroll-view自带的下拉刷新\r\n\t\tuseCustomRefresher: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('useCustomRefresher', true)\r\n\t\t},\r\n\t\t// 自定义下拉刷新下拉帧率，默认为40，过高可能会出现抖动问题\r\n\t\trefresherFps: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: u.gc('refresherFps', 40)\r\n\t\t},\r\n\t\t// 自定义下拉刷新允许触发的最大下拉角度，默认为40度，当下拉角度小于设定值时，自定义下拉刷新动画不会被触发\r\n\t\trefresherMaxAngle: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: u.gc('refresherMaxAngle', 40)\r\n\t\t},\r\n\t\t// 自定义下拉刷新的角度由未达到最大角度变到达到最大角度时，是否继续下拉刷新手势，默认为否\r\n\t\trefresherAngleEnableChangeContinued: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('refresherAngleEnableChangeContinued', false)\r\n\t\t},\r\n\t\t// 自定义下拉刷新默认状态下的文字\r\n\t\trefresherDefaultText: {\r\n\t\t\ttype: [String, Object],\r\n\t\t\tdefault: u.gc('refresherDefaultText', null)\r\n\t\t},\r\n\t\t// 自定义下拉刷新松手立即刷新状态下的文字\r\n\t\trefresherPullingText: {\r\n\t\t\ttype: [String, Object],\r\n\t\t\tdefault: u.gc('refresherPullingText', null)\r\n\t\t},\r\n\t\t// 自定义下拉刷新刷新中状态下的文字\r\n\t\trefresherRefreshingText: {\r\n\t\t\ttype: [String, Object],\r\n\t\t\tdefault: u.gc('refresherRefreshingText', null)\r\n\t\t},\r\n\t\t// 自定义下拉刷新刷新结束状态下的文字\r\n\t\trefresherCompleteText: {\r\n\t\t\ttype: [String, Object],\r\n\t\t\tdefault: u.gc('refresherCompleteText', null)\r\n\t\t},\r\n\t\t// 自定义继续下拉进入二楼文字\r\n\t\trefresherGoF2Text: {\r\n\t\t\ttype: [String, Object],\r\n\t\t\tdefault: u.gc('refresherGoF2Text', null)\r\n\t\t},\r\n\t\t// 自定义下拉刷新默认状态下的图片\r\n\t\trefresherDefaultImg: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('refresherDefaultImg', null)\r\n\t\t},\r\n\t\t// 自定义下拉刷新松手立即刷新状态下的图片，默认与refresherDefaultImg一致\r\n\t\trefresherPullingImg: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('refresherPullingImg', null)\r\n\t\t},\r\n\t\t// 自定义下拉刷新刷新中状态下的图片\r\n\t\trefresherRefreshingImg: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('refresherRefreshingImg', null)\r\n\t\t},\r\n\t\t// 自定义下拉刷新刷新结束状态下的图片\r\n\t\trefresherCompleteImg: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('refresherCompleteImg', null)\r\n\t\t},\r\n\t\t// 自定义下拉刷新刷新中状态下是否展示旋转动画\r\n\t\trefresherRefreshingAnimated: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('refresherRefreshingAnimated', true)\r\n\t\t},\r\n\t\t// 是否开启自定义下拉刷新刷新结束回弹效果，默认为是\r\n\t\trefresherEndBounceEnabled: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('refresherEndBounceEnabled', true)\r\n\t\t},\r\n\t\t// 是否开启自定义下拉刷新，默认为是\r\n\t\trefresherEnabled: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('refresherEnabled', true)\r\n\t\t},\r\n\t\t// 设置自定义下拉刷新阈值，默认为80rpx\r\n\t\trefresherThreshold: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: u.gc('refresherThreshold', '80rpx')\r\n\t\t},\r\n\t\t// 设置系统下拉刷新默认样式，支持设置 black，white，none，none 表示不使用默认样式，默认为black\r\n\t\trefresherDefaultStyle: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('refresherDefaultStyle', 'black')\r\n\t\t},\r\n\t\t// 设置自定义下拉刷新区域背景\r\n\t\trefresherBackground: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('refresherBackground', 'transparent')\r\n\t\t},\r\n\t\t// 设置固定的自定义下拉刷新区域背景\r\n\t\trefresherFixedBackground: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('refresherFixedBackground', 'transparent')\r\n\t\t},\r\n\t\t// 设置固定的自定义下拉刷新区域高度，默认为0\r\n\t\trefresherFixedBacHeight: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: u.gc('refresherFixedBacHeight', 0)\r\n\t\t},\r\n\t\t// 设置自定义下拉刷新下拉超出阈值后继续下拉位移衰减的比例，范围0-1，值越大代表衰减越多。默认为0.65(nvue无效)\r\n\t\trefresherOutRate: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: u.gc('refresherOutRate', 0.65)\r\n\t\t},\r\n\t\t// 是否开启下拉进入二楼功能，默认为否\r\n\t\trefresherF2Enabled: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('refresherF2Enabled', false)\r\n\t\t},\r\n\t\t// 下拉进入二楼阈值，默认为200rpx\r\n\t\trefresherF2Threshold: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: u.gc('refresherF2Threshold', '200rpx')\r\n\t\t},\r\n\t\t// 下拉进入二楼动画时间，单位为毫秒，默认为200毫秒\r\n\t\trefresherF2Duration: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: u.gc('refresherF2Duration', 200)\r\n\t\t},\r\n\t\t// 下拉进入二楼状态松手后是否弹出二楼，默认为是\r\n\t\tshowRefresherF2: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('showRefresherF2', true)\r\n\t\t},\r\n\t\t// 设置自定义下拉刷新下拉时实际下拉位移与用户下拉距离的比值，默认为0.75，即代表若用户下拉10px，则实际位移为7.5px(nvue无效)\r\n\t\trefresherPullRate: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: u.gc('refresherPullRate', 0.75)\r\n\t\t},\r\n\t\t// 是否显示最后更新时间，默认为否\r\n\t\tshowRefresherUpdateTime: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('showRefresherUpdateTime', false)\r\n\t\t},\r\n\t\t// 如果需要区别不同页面的最后更新时间，请为不同页面的z-paging的`refresher-update-time-key`设置不同的字符串\r\n\t\trefresherUpdateTimeKey: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('refresherUpdateTimeKey', 'default')\r\n\t\t},\r\n\t\t// 下拉刷新时下拉到“松手立即刷新”或“松手进入二楼”状态时是否使手机短振动，默认为否（h5无效）\r\n\t\trefresherVibrate: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('refresherVibrate', false)\r\n\t\t},\r\n\t\t// 下拉刷新时是否禁止下拉刷新view跟随用户触摸竖直移动，默认为否。注意此属性只是禁止下拉刷新view移动，其他下拉刷新逻辑依然会正常触发\r\n\t\trefresherNoTransform: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('refresherNoTransform', false)\r\n\t\t},\r\n\t\t// 是否开启下拉刷新状态栏占位，适用于隐藏导航栏时，下拉刷新需要避开状态栏高度的情况，默认为否\r\n\t\tuseRefresherStatusBarPlaceholder: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('useRefresherStatusBarPlaceholder', false)\r\n\t\t},\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tR: Enum.Refresher,\r\n\t\t\t//下拉刷新状态\r\n\t\t\trefresherStatus: Enum.Refresher.Default,\r\n\t\t\trefresherTouchstartY: 0,\r\n\t\t\tlastRefresherTouchmove: null,\r\n\t\t\trefresherReachMaxAngle: true,\r\n\t\t\trefresherTransform: 'translateY(0px)',\r\n\t\t\trefresherTransition: '',\r\n\t\t\tfinalRefresherDefaultStyle: 'black',\r\n\t\t\trefresherRevealStackCount: 0,\r\n\t\t\trefresherCompleteTimeout: null,\r\n\t\t\trefresherCompleteSubTimeout: null,\r\n\t\t\trefresherEndTimeout: null,\r\n\t\t\tisTouchmovingTimeout: null,\r\n\t\t\trefresherTriggered: false,\r\n\t\t\tisTouchmoving: false,\r\n\t\t\tisTouchEnded: false,\r\n\t\t\tisUserPullDown: false,\r\n\t\t\tprivateRefresherEnabled: -1,\r\n\t\t\tprivateShowRefresherWhenReload: false,\r\n\t\t\tcustomRefresherHeight: -1,\r\n\t\t\tshowCustomRefresher: false,\r\n\t\t\tdoRefreshAnimateAfter: false,\r\n\t\t\tisRefresherInComplete: false,\r\n\t\t\tshowF2: false,\r\n\t\t\tf2Transform: '',\r\n\t\t\tpullDownTimeStamp: 0,\r\n\t\t\tmoveDis: 0,\r\n\t\t\toldMoveDis: 0,\r\n\t\t\tcurrentDis: 0,\r\n\t\t\toldCurrentMoveDis: 0,\r\n\t\t\toldRefresherTouchmoveY: 0,\r\n\t\t\toldTouchDirection: '',\r\n\t\t\toldEmitedTouchDirection: '',\r\n\t\t\toldPullingDistance: -1,\r\n\t\t\trefresherThresholdUpdateTag: 0\r\n\t\t}\r\n\t},\r\n\twatch: {\r\n\t\trefresherDefaultStyle: {\r\n\t\t\thandler(newVal) {\r\n\t\t\t\tif (newVal.length) {\r\n\t\t\t\t\tthis.finalRefresherDefaultStyle = newVal;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\timmediate: true\r\n\t\t},\r\n\t\trefresherStatus(newVal) {\r\n\t\t\tnewVal === Enum.Refresher.Loading && this._cleanRefresherEndTimeout();\r\n\t\t\tthis.refresherVibrate && (newVal === Enum.Refresher.ReleaseToRefresh || newVal === Enum.Refresher.GoF2) && this._doVibrateShort();\r\n\t\t\tthis.$emit('refresherStatusChange', newVal);\r\n\t\t\tthis.$emit('update:refresherStatus', newVal);\r\n\t\t},\r\n\t\t// 监听当前下拉刷新启用/禁用状态\r\n\t\trefresherEnabled(newVal) {\r\n\t\t\t// 当禁用下拉刷新时，强制收回正在展示的下拉刷新view\r\n\t\t\t!newVal && this.endRefresh();\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\tpullDownDisTimeStamp() {\r\n\t\t\treturn 1000 / this.refresherFps;\r\n\t\t},\r\n\t\trefresherThresholdUnitConverted() {\r\n\t\t\treturn u.addUnit(this.refresherThreshold, this.unit);\r\n\t\t},\r\n\t\tfinalRefresherEnabled() {\r\n\t\t\tif (this.useChatRecordMode) return false;\r\n\t\t\tif (this.privateRefresherEnabled === -1) return this.refresherEnabled;\r\n\t\t\treturn this.privateRefresherEnabled === 1;\r\n\t\t},\r\n\t\tfinalRefresherThreshold() {\r\n\t\t\tlet refresherThreshold = this.refresherThresholdUnitConverted;\r\n\t\t\tlet idDefault = false;\r\n\t\t\tif (refresherThreshold === u.addUnit(80, this.unit)) {\r\n\t\t\t\tidDefault = true;\r\n\t\t\t\tif (this.showRefresherUpdateTime) {\r\n\t\t\t\t\trefresherThreshold = u.addUnit(120, this.unit);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (idDefault && this.customRefresherHeight > 0) return this.customRefresherHeight + this.finalRefresherThresholdPlaceholder;\r\n\t\t\treturn u.convertToPx(refresherThreshold) + this.finalRefresherThresholdPlaceholder;\r\n\t\t},\r\n\t\tfinalRefresherF2Threshold() {\r\n\t\t\treturn u.convertToPx(u.addUnit(this.refresherF2Threshold, this.unit));\r\n\t\t},\r\n\t\tfinalRefresherThresholdPlaceholder() {\r\n\t\t\treturn this.useRefresherStatusBarPlaceholder ? this.statusBarHeight : 0;\r\n\t\t},\r\n\t\tfinalRefresherFixedBacHeight() {\r\n\t\t\treturn u.convertToPx(this.refresherFixedBacHeight);\r\n\t\t},\r\n\t\tfinalRefresherThemeStyle() {\r\n\t\t\treturn this.refresherThemeStyle.length ? this.refresherThemeStyle : this.defaultThemeStyle;\r\n\t\t},\r\n\t\tfinalRefresherOutRate() {\r\n\t\t\tlet rate = this.refresherOutRate;\r\n\t\t\trate = Math.max(0,rate);\r\n\t\t\trate = Math.min(1,rate);\r\n\t\t\treturn rate;\r\n\t\t},\r\n\t\tfinalRefresherPullRate() {\r\n\t\t\tlet rate = this.refresherPullRate;\r\n\t\t\trate = Math.max(0,rate);\r\n\t\t\treturn rate;\r\n\t\t},\r\n\t\tfinalRefresherTransform() {\r\n\t\t\tif (this.refresherNoTransform || this.refresherTransform === 'translateY(0px)') return 'none';\r\n\t\t\treturn this.refresherTransform;\r\n\t\t},\r\n\t\tfinalShowRefresherWhenReload() {\r\n\t\t\treturn this.showRefresherWhenReload || this.privateShowRefresherWhenReload;\r\n\t\t},\r\n\t\tfinalRefresherTriggered() {\r\n\t\t\tif (!(this.finalRefresherEnabled && !this.useCustomRefresher)) return false;\r\n\t\t\treturn this.refresherTriggered;\r\n\t\t},\r\n\t\tshowRefresher() {\r\n\t\t\tconst showRefresher = this.finalRefresherEnabled || this.useCustomRefresher && !this.useChatRecordMode;\r\n\t\t\t// #ifndef APP-NVUE\r\n\t\t\tthis.active && this.customRefresherHeight === -1 && showRefresher && this.updateCustomRefresherHeight();\r\n\t\t\t// #endif\r\n\t\t\treturn showRefresher;\r\n\t\t},\r\n\t\thasTouchmove() {\r\n\t\t\t// #ifdef VUE2\r\n\t\t\t// #ifdef APP-VUE || H5\r\n\t\t\tif (this.$listeners && !this.$listeners.refresherTouchmove) return false;\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef MP-WEIXIN || MP-QQ\r\n\t\t\treturn this.watchRefresherTouchmove;\r\n\t\t\t// #endif\r\n\t\t\treturn true;\r\n\t\t\t// #endif\r\n\t\t\treturn this.watchRefresherTouchmove;\r\n\t\t},\r\n\t},\r\n\tmethods: {\r\n\t\t// 终止下拉刷新状态\r\n\t\tendRefresh() {\r\n\t\t\tthis.totalData = this.realTotalData;\r\n\t\t\tthis._refresherEnd();\r\n\t\t\tthis._endSystemLoadingAndRefresh();\r\n\t\t\tthis._handleScrollViewBounce({ bounce: true });\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis.refresherTriggered = false;\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 手动更新自定义下拉刷新view高度\r\n\t\tupdateCustomRefresherHeight() {\r\n\t\t\tu.delay(() => this.$nextTick(this._updateCustomRefresherHeight));\r\n\t\t},\r\n\t\t// 关闭二楼\r\n\t\tcloseF2() {\r\n\t\t\tthis._handleCloseF2();\r\n\t\t},\r\n\t\t// 自定义下拉刷新被触发\r\n\t\t_onRefresh(fromScrollView = false, isUserPullDown = true) {\r\n\t\t\tif (fromScrollView && !(this.finalRefresherEnabled && !this.useCustomRefresher)) return;\r\n\t\t\tthis.$emit('onRefresh');\r\n\t\t\tthis.$emit('Refresh');\r\n\t\t\t// #ifdef APP-NVUE\r\n\t\t\tif (this.loading) {\r\n\t\t\t\tu.delay(this._nRefresherEnd, 500)\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t\tif (this.loading || this.isRefresherInComplete) return;\r\n\t\t\tthis.loadingType = Enum.LoadingType.Refresher;\r\n\t\t\tif (this.nShowRefresherReveal) return;\r\n\t\t\tthis.isUserPullDown = isUserPullDown;\r\n\t\t\tthis.isUserReload = !isUserPullDown;\r\n\t\t\tthis._startLoading(true);\r\n\t\t\tthis.refresherTriggered = true;\r\n\t\t\tif(this.reloadWhenRefresh && isUserPullDown){\r\n\t\t\t\tthis.useChatRecordMode ? this._onLoadingMore('click') : this._reload(false, false, isUserPullDown);\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 自定义下拉刷新被复位\r\n\t\t_onRestore() {\r\n\t\t\tthis.refresherTriggered = 'restore';\r\n\t\t\tthis.$emit('onRestore');\r\n\t\t\tthis.$emit('Restore');\r\n\t\t},\r\n\t\t// #ifndef APP-PLUS || MP-WEIXIN || MP-QQ || H5\r\n\t\t// touch开始\r\n\t\t_refresherTouchstart(e) {\r\n\t\t\tthis._handleListTouchstart();\r\n\t\t\tif (this._touchDisabled()) return;\r\n\t\t\tthis._handleRefresherTouchstart(u.getTouch(e));\r\n\t\t},\r\n\t\t// #endif\r\n\t\t// 进一步处理touch开始结果\r\n\t\t_handleRefresherTouchstart(touch) {\r\n\t\t\tif (!this.loading && this.isTouchEnded) {\r\n\t\t\t\tthis.isTouchmoving = false;\r\n\t\t\t}\r\n\t\t\tthis.loadingType = Enum.LoadingType.Refresher;\r\n\t\t\tthis.isTouchmovingTimeout && clearTimeout(this.isTouchmovingTimeout);\r\n\t\t\tthis.isTouchEnded = false;\r\n\t\t\tthis.refresherTransition = '';\r\n\t\t\tthis.refresherTouchstartY = touch.touchY;\r\n\t\t\tthis.$emit('refresherTouchstart', this.refresherTouchstartY);\r\n\t\t\tthis.lastRefresherTouchmove = touch;\r\n\t\t\tthis._cleanRefresherCompleteTimeout();\r\n\t\t\tthis._cleanRefresherEndTimeout();\r\n\t\t},\r\n\t\t\r\n\t\t// 非app-ios/android或微信小程序或QQ小程序或h5平台，使用js控制下拉刷新\r\n\t\t// #ifndef APP-IOS || APP-ANDROID || MP-WEIXIN || MP-QQ || H5\r\n\t\t// touch中\r\n\t\t_refresherTouchmove(e) {\r\n\t\t\tconst currentTimeStamp = u.getTime();\r\n\t\t\tlet touch = null;\r\n\t\t\tlet refresherTouchmoveY = 0;\r\n\t\t\tif (this.watchTouchDirectionChange) {\r\n\t\t\t\t// 检测下拉刷新方向改变\r\n\t\t\t\ttouch = u.getTouch(e);\r\n\t\t\t\trefresherTouchmoveY = touch.touchY;\r\n\t\t\t\tconst direction  = refresherTouchmoveY > this.oldRefresherTouchmoveY ? 'top' : 'bottom';\r\n\t\t\t\t// 只有在方向改变的时候才emit相关事件\r\n\t\t\t\tif (direction === this.oldTouchDirection && direction !== this.oldEmitedTouchDirection) {\r\n\t\t\t\t\tthis._handleTouchDirectionChange({ direction });\r\n\t\t\t\t\tthis.oldEmitedTouchDirection = direction;\r\n\t\t\t\t}\r\n\t\t\t\tthis.oldTouchDirection = direction;\r\n\t\t\t\tthis.oldRefresherTouchmoveY = refresherTouchmoveY;\r\n\t\t\t}\r\n\t\t\t// 节流处理，在pullDownDisTimeStamp时间内的下拉刷新中事件不进行处理\r\n\t\t\tif (this.pullDownTimeStamp && currentTimeStamp - this.pullDownTimeStamp <= this.pullDownDisTimeStamp) return;\r\n\t\t\t// 如果不允许下拉，则return\r\n\t\t\tif (this._touchDisabled()) return;\r\n\t\t\tthis.pullDownTimeStamp = Number(currentTimeStamp);\r\n\t\t\ttouch = u.getTouch(e);\r\n\t\t\trefresherTouchmoveY = touch.touchY;\r\n\t\t\t// 获取当前touch的y - 初始touch的y，计算它们的差\r\n\t\t\tlet moveDis = refresherTouchmoveY - this.refresherTouchstartY;\r\n\t\t\tif (moveDis < 0) return;\r\n\t\t\t// 对下拉刷新的角度进行限制\r\n\t\t\tif (this.refresherMaxAngle >= 0 && this.refresherMaxAngle <= 90 && this.lastRefresherTouchmove && this.lastRefresherTouchmove.touchY <= refresherTouchmoveY) {\r\n\t\t\t\tif (!moveDis && !this.refresherAngleEnableChangeContinued && this.moveDis < 1 && !this.refresherReachMaxAngle) return;\r\n\t\t\t\tconst x = Math.abs(touch.touchX - this.lastRefresherTouchmove.touchX);\r\n\t\t\t\tconst y = Math.abs(refresherTouchmoveY - this.lastRefresherTouchmove.touchY);\r\n\t\t\t\tconst z = Math.sqrt(Math.pow(x, 2) + Math.pow(y, 2));\r\n\t\t\t\tif ((x || y) && x > 1) {\r\n\t\t\t\t\t// 获取下拉刷新前后两次位移的角度\r\n\t\t\t\t\tconst angle = Math.asin(y / z) / Math.PI * 180;\r\n\t\t\t\t\t// 如果角度小于配置要求，则return\r\n\t\t\t\t\tif (angle < this.refresherMaxAngle) {\r\n\t\t\t\t\t\tthis.lastRefresherTouchmove = touch;\r\n\t\t\t\t\t\tthis.refresherReachMaxAngle = false;\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// 获取最终的moveDis\r\n\t\t\tmoveDis = this._getFinalRefresherMoveDis(moveDis);\r\n\t\t\t// 处理下拉刷新位移\r\n\t\t\tthis._handleRefresherTouchmove(moveDis, touch);\r\n\t\t\t// 下拉刷新时，禁止页面滚动以防止页面向下滚动和下拉刷新同时作用导致下拉刷新位置偏移超过预期\r\n\t\t\tif (!this.disabledBounce) {\r\n\t\t\t\t// #ifndef MP-LARK\r\n\t\t\t\tthis._handleScrollViewBounce({ bounce: false });\r\n\t\t\t\t// #endif\r\n\t\t\t\tthis.disabledBounce = true;\r\n\t\t\t}\r\n\t\t\tthis._emitTouchmove({ pullingDistance: moveDis, dy: this.moveDis - this.oldMoveDis });\r\n\t\t},\r\n\t\t// #endif\r\n\t\t// 进一步处理touch中结果\r\n\t\t_handleRefresherTouchmove(moveDis, touch) {\r\n\t\t\tthis.refresherReachMaxAngle = true;\r\n\t\t\tthis.isTouchmovingTimeout && clearTimeout(this.isTouchmovingTimeout);\r\n\t\t\tthis.isTouchmoving = true;\r\n\t\t\tthis.isTouchEnded = false;\r\n\t\t\t// 更新下拉刷新状态\r\n\t\t\t// 下拉刷新距离超过阈值\r\n\t\t\tif (moveDis >= this.finalRefresherThreshold) {\r\n\t\t\t\t// 如果开启了下拉进入二楼并且下拉刷新距离超过进入二楼阈值，则当前下拉刷新状态为松手进入二楼，否则为松手立即刷新\r\n\t\t\t\tthis.refresherStatus = this.refresherF2Enabled && moveDis >= this.finalRefresherF2Threshold ? Enum.Refresher.GoF2 : Enum.Refresher.ReleaseToRefresh;\r\n\t\t\t} else {\r\n\t\t\t\t// 下拉刷新距离未超过阈值，显示默认状态\r\n\t\t\t\tthis.refresherStatus = Enum.Refresher.Default;\r\n\t\t\t}\r\n\t\t\t// #ifndef APP-PLUS || MP-WEIXIN || MP-QQ  || H5\r\n\t\t\t// this.scrollEnable = false;\r\n\t\t\t// 通过transform控制下拉刷新view垂直偏移\r\n\t\t\tthis.refresherTransform = `translateY(${moveDis}px)`;\r\n\t\t\tthis.lastRefresherTouchmove = touch;\r\n\t\t\t// #endif\r\n\t\t\tthis.moveDis = moveDis;\r\n\t\t},\r\n\t\t// #ifndef APP-PLUS || MP-WEIXIN || MP-QQ || H5\r\n\t\t// touch结束\r\n\t\t_refresherTouchend(e) {\r\n\t\t\t// 下拉刷新用户手离开屏幕，允许列表滚动\r\n\t\t\tthis._handleScrollViewBounce({bounce: true});\r\n\t\t\tif (this._touchDisabled() || !this.isTouchmoving) return;\r\n\t\t\tconst touch = u.getTouch(e);\r\n\t\t\tlet refresherTouchendY = touch.touchY;\r\n\t\t\tlet moveDis = refresherTouchendY - this.refresherTouchstartY;\r\n\t\t\tmoveDis = this._getFinalRefresherMoveDis(moveDis);\r\n\t\t\tthis._handleRefresherTouchend(moveDis);\r\n\t\t\tthis.disabledBounce = false;\r\n\t\t},\r\n\t\t// #endif\r\n\t\t// 进一步处理touch结束结果\r\n\t\t_handleRefresherTouchend(moveDis) {\r\n\t\t\t// #ifndef APP-PLUS || H5 || MP-WEIXIN\r\n\t\t\tif (!this.isTouchmoving) return;\r\n\t\t\t// #endif\r\n\t\t\tthis.isTouchmovingTimeout && clearTimeout(this.isTouchmovingTimeout);\r\n\t\t\tthis.refresherReachMaxAngle = true;\r\n\t\t\tthis.isTouchEnded = true;\r\n\t\t\tconst refresherThreshold = this.finalRefresherThreshold;\r\n\t\t\tif (moveDis >= refresherThreshold && (this.refresherStatus === Enum.Refresher.ReleaseToRefresh || this.refresherStatus === Enum.Refresher.GoF2)) {\r\n\t\t\t\t// 如果是松手进入二楼状态，则触发进入二楼\r\n\t\t\t\tif (this.refresherStatus === Enum.Refresher.GoF2) {\r\n\t\t\t\t\tthis._handleGoF2();\r\n\t\t\t\t\tthis._refresherEnd();\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 如果是松手立即刷新状态，则触发下拉刷新\r\n\t\t\t\t\t// #ifndef APP-PLUS || MP-WEIXIN || MP-QQ || H5\r\n\t\t\t\t\tthis.refresherTransform = `translateY(${refresherThreshold}px)`;\r\n\t\t\t\t\tthis.refresherTransition = 'transform .1s linear';\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tu.delay(() => {\r\n\t\t\t\t\t\tthis._emitTouchmove({ pullingDistance: refresherThreshold, dy: this.moveDis - refresherThreshold });\r\n\t\t\t\t\t}, 0.1);\r\n\t\t\t\t\tthis.moveDis = refresherThreshold;\r\n\t\t\t\t\tthis.refresherStatus = Enum.Refresher.Loading;\r\n\t\t\t\t\tthis._doRefresherLoad();\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tthis._refresherEnd();\r\n\t\t\t\tthis.isTouchmovingTimeout = u.delay(() => {\r\n\t\t\t\t\tthis.isTouchmoving = false;\r\n\t\t\t\t}, this.refresherDefaultDuration);\r\n\t\t\t}\r\n\t\t\tthis.scrollEnable = true;\r\n\t\t\tthis.$emit('refresherTouchend', moveDis);\r\n\t\t},\r\n\t\t// 处理列表触摸开始事件\r\n\t\t_handleListTouchstart() {\r\n\t\t\tif (this.useChatRecordMode && this.autoHideKeyboardWhenChat) {\r\n\t\t\t\tuni.hideKeyboard();\r\n\t\t\t\tthis.$emit('hidedKeyboard');\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 处理scroll-view bounce是否生效\r\n\t\t_handleScrollViewBounce({ bounce }) {\r\n\t\t\tif (!this.usePageScroll && !this.scrollToTopBounceEnabled) {\r\n\t\t\t\tif (this.wxsScrollTop <= 5) {\r\n\t\t\t\t\t// #ifdef APP-PLUS || MP-WEIXIN || MP-QQ || H5\r\n\t\t\t\t\tthis.refresherTransition = '';\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tthis.scrollEnable = bounce;\r\n\t\t\t\t} else if (bounce) {\r\n\t\t\t\t\tthis.scrollEnable = bounce;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t// wxs正在下拉状态改变处理\r\n\t\t_handleWxsPullingDownStatusChange(onPullingDown) {\r\n\t\t\tthis.wxsOnPullingDown = onPullingDown;\r\n\t\t\tif (onPullingDown && !this.useChatRecordMode) {\r\n\t\t\t\tthis.renderPropScrollTop = 0;\r\n\t\t\t}\r\n\t\t},\r\n\t\t// wxs正在下拉处理\r\n\t\t_handleWxsPullingDown({ moveDis, diffDis }){\r\n\t\t\tthis._emitTouchmove({ pullingDistance: moveDis,dy: diffDis });\r\n\t\t},\r\n\t\t// wxs触摸方向改变\r\n\t\t_handleTouchDirectionChange({ direction }) {\r\n\t\t\tthis.$emit('touchDirectionChange',direction);\r\n\t\t},\r\n\t\t// wxs通知更新其props\r\n\t\t_handlePropUpdate(){\r\n\t\t\tthis.wxsPropType = u.getTime().toString();\r\n\t\t},\r\n\t\t// 下拉刷新结束\r\n\t\t_refresherEnd(shouldEndLoadingDelay = true, fromAddData = false, isUserPullDown = false, setLoading = true) {\r\n\t\t\tif (this.loadingType === Enum.LoadingType.Refresher) {\r\n\t\t\t\t// 计算当前下拉刷新结束需要延迟的时间\r\n\t\t\t\tconst refresherCompleteDelay = (fromAddData && (isUserPullDown || this.showRefresherWhenReload)) ? this.refresherCompleteDelay : 0;\r\n\t\t\t\t// 如果延迟时间大于0，则展示刷新结束状态，否则直接展示默认状态\r\n\t\t\t\tconst refresherStatus = refresherCompleteDelay > 0 ? Enum.Refresher.Complete : Enum.Refresher.Default;\r\n\t\t\t\tif (this.finalShowRefresherWhenReload) {\r\n\t\t\t\t\tconst stackCount = this.refresherRevealStackCount;\r\n\t\t\t\t\tthis.refresherRevealStackCount --;\r\n\t\t\t\t\tif (stackCount > 1) return;\r\n\t\t\t\t}\r\n\t\t\t\tthis._cleanRefresherEndTimeout();\r\n\t\t\t\tthis.refresherEndTimeout = u.delay(() => {\r\n\t\t\t\t\t// 更新下拉刷新状态\r\n\t\t\t\t\tthis.refresherStatus = refresherStatus;\r\n\t\t\t\t\t// 如果当前下拉刷新状态不是刷新结束，则认为其不在刷新结束状态\r\n\t\t\t\t\tif (refresherStatus !== Enum.Refresher.Complete) {\r\n\t\t\t\t\t\tthis.isRefresherInComplete = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t}, this.refresherStatus !== Enum.Refresher.Default && refresherStatus === Enum.Refresher.Default ? this.refresherCompleteDuration : 0);\r\n\t\t\t\t\r\n\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\tif (refresherCompleteDelay > 0) {\r\n\t\t\t\t\tthis.isRefresherInComplete = true;\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\tthis._cleanRefresherCompleteTimeout();\r\n\t\t\t\tthis.refresherCompleteTimeout = u.delay(() => {\r\n\t\t\t\t\tlet animateDuration = 1;\r\n\t\t\t\t\tconst animateType = this.refresherEndBounceEnabled && fromAddData ? 'cubic-bezier(0.19,1.64,0.42,0.72)' : 'linear';\r\n\t\t\t\t\tif (fromAddData) {\r\n\t\t\t\t\t\tanimateDuration = this.refresherEndBounceEnabled ? this.refresherCompleteDuration / 1000 : this.refresherCompleteDuration / 3000;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.refresherTransition = `transform ${fromAddData ? animateDuration : this.refresherDefaultDuration / 1000}s ${animateType}`;\r\n\t\t\t\t\t// #ifndef APP-PLUS || MP-WEIXIN || MP-QQ  || H5\r\n\t\t\t\t\tthis.refresherTransform = 'translateY(0px)';\r\n\t\t\t\t\tthis.currentDis = 0;\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifdef APP-PLUS || MP-WEIXIN || MP-QQ || H5\r\n\t\t\t\t\tthis.wxsPropType = this.refresherTransition + 'end' + u.getTime();\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\t\tthis._nRefresherEnd();\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tthis.moveDis = 0;\r\n\t\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\t\tif (refresherStatus === Enum.Refresher.Complete) {\r\n\t\t\t\t\t\tif (this.refresherCompleteSubTimeout) {\r\n\t\t\t\t\t\t\tclearTimeout(this.refresherCompleteSubTimeout);\r\n\t\t\t\t\t\t\tthis.refresherCompleteSubTimeout = null;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.refresherCompleteSubTimeout = u.delay(() => {\r\n\t\t\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\t\t\tthis.refresherStatus = Enum.Refresher.Default;\r\n\t\t\t\t\t\t\t\tthis.isRefresherInComplete = false;\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}, animateDuration * 800);\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tthis._emitTouchmove({ pullingDistance: 0, dy: this.moveDis });\r\n\t\t\t\t}, refresherCompleteDelay);\r\n\t\t\t}\r\n\t\t\tif (setLoading) {\r\n\t\t\t\tu.delay(() => this.loading = false, shouldEndLoadingDelay ? 10 : 0);\r\n\t\t\t\tisUserPullDown && this._onRestore();\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 处理进入二楼\r\n\t\t_handleGoF2() {\r\n\t\t\tif (this.showF2 || !this.refresherF2Enabled) return;\r\n\t\t\tthis.$emit('refresherF2Change', 'go');\r\n\t\t\t\r\n\t\t\tif (!this.showRefresherF2) return;\r\n\t\t\t// #ifndef APP-NVUE\r\n\t\t\tthis.f2Transform = `translateY(${-this.superContentHeight}px)`;\r\n\t\t\tthis.showF2 = true;\r\n\t\t\tu.delay(() => {\r\n\t\t\t\tthis.f2Transform = 'translateY(0px)';\r\n\t\t\t}, 100, 'f2ShowDelay')\r\n\t\t\t// #endif\r\n\t\t\t\r\n\t\t\t// #ifdef APP-NVUE\r\n\t\t\tthis.showF2 = true;\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tweexAnimation.transition(this.$refs['zp-n-f2'], {\r\n\t\t\t\t\tstyles: { transform: `translateY(${-this.superContentHeight}px)` },\r\n\t\t\t\t\tduration: 0,\r\n\t\t\t\t\ttimingFunction: 'linear',\r\n\t\t\t\t\tneedLayout: true,\r\n\t\t\t\t\tdelay: 0\r\n\t\t\t\t})\r\n\t\t\t\tthis.nF2Opacity = 1;\r\n\t\t\t})\r\n\t\t\tu.delay(() => {\r\n\t\t\t\tweexAnimation.transition(this.$refs['zp-n-f2'], {\r\n\t\t\t\t\tstyles: { transform: 'translateY(0px)' },\r\n\t\t\t\t\tduration: this.refresherF2Duration,\r\n\t\t\t\t\ttimingFunction: 'linear',\r\n\t\t\t\t\tneedLayout: true,\r\n\t\t\t\t\tdelay: 0\r\n\t\t\t\t})\r\n\t\t\t}, 10, 'f2GoDelay')\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\t// 处理退出二楼\r\n\t\t_handleCloseF2() {\r\n\t\t\tif (!this.showF2 || !this.refresherF2Enabled) return;\r\n\t\t\tthis.$emit('refresherF2Change', 'close');\r\n\t\t\t\r\n\t\t\tif (!this.showRefresherF2) return;\r\n\t\t\t// #ifndef APP-NVUE\r\n\t\t\tthis.f2Transform = `translateY(${-this.superContentHeight}px)`;\r\n\t\t\t// #endif\r\n\t\t\t\r\n\t\t\t// #ifdef APP-NVUE\r\n\t\t\tweexAnimation.transition(this.$refs['zp-n-f2'], {\r\n\t\t\t\tstyles: { transform: `translateY(${-this.superContentHeight}px)` },\r\n\t\t\t\tduration: this.refresherF2Duration,\r\n\t\t\t\ttimingFunction: 'linear',\r\n\t\t\t\tneedLayout: true,\r\n\t\t\t\tdelay: 0\r\n\t\t\t})\r\n\t\t\t// #endif\r\n\t\t\t\r\n\t\t\tu.delay(() => {\r\n\t\t\t\tthis.showF2 = false;\r\n\t\t\t\tthis.nF2Opacity = 0;\r\n\t\t\t}, this.refresherF2Duration, 'f2CloseDelay')\r\n\t\t},\r\n\t\t// 模拟用户手动触发下拉刷新\r\n\t\t_doRefresherRefreshAnimate() {\r\n\t\t\tthis._cleanRefresherCompleteTimeout();\r\n\t\t\t// 用户处理用户在短时间内多次调用reload的情况，此时下拉刷新view不需要重复显示，只需要保证最后一次reload对应的请求结束后收回下拉刷新view即可\r\n\t\t\t// #ifndef APP-NVUE\r\n\t\t\tconst doRefreshAnimateAfter = !this.doRefreshAnimateAfter && (this.finalShowRefresherWhenReload) && this\r\n\t\t\t\t.customRefresherHeight === -1 && this.refresherThreshold === u.addUnit(80, this.unit);\r\n\t\t\tif (doRefreshAnimateAfter) {\r\n\t\t\t\tthis.doRefreshAnimateAfter = true;\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t\tthis.refresherRevealStackCount ++;\r\n\t\t\t// #ifndef APP-PLUS || MP-WEIXIN || MP-QQ  || H5\r\n\t\t\tthis.refresherTransform = `translateY(${this.finalRefresherThreshold}px)`;\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef APP-PLUS || MP-WEIXIN || MP-QQ || H5\r\n\t\t\tthis.wxsPropType = 'begin' + u.getTime();\r\n\t\t\t// #endif\r\n\t\t\tthis.moveDis = this.finalRefresherThreshold;\r\n\t\t\tthis.refresherStatus = Enum.Refresher.Loading;\r\n\t\t\tthis.isTouchmoving = true;\r\n\t\t\tthis.isTouchmovingTimeout && clearTimeout(this.isTouchmovingTimeout);\r\n\t\t\tthis._doRefresherLoad(false);\r\n\t\t},\r\n\t\t// 触发下拉刷新\r\n\t\t_doRefresherLoad(isUserPullDown = true) {\r\n\t\t\tthis._onRefresh(false, isUserPullDown);\r\n\t\t\tthis.loading = true;\r\n\t\t},\r\n\t\t// #ifndef APP-PLUS || MP-WEIXIN || MP-QQ || H5\r\n\t\t// 获取处理后的moveDis\r\n\t\t_getFinalRefresherMoveDis(moveDis) {\r\n\t\t\tlet diffDis = moveDis - this.oldCurrentMoveDis;\r\n\t\t\tthis.oldCurrentMoveDis = moveDis;\r\n\t\t\tif (diffDis > 0) {\r\n\t\t\t\t// 根据配置的下拉刷新用户手势位移与实际需要的位移比率计算最终的diffDis\r\n\t\t\t\tdiffDis = diffDis * this.finalRefresherPullRate;\r\n\t\t\t\tif (this.currentDis > this.finalRefresherThreshold) {\r\n\t\t\t\t\tdiffDis = diffDis * (1 - this.finalRefresherOutRate);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// 控制diffDis过大的情况，比如进入页面突然猛然下拉，此时diffDis不应进行太大的偏移\r\n\t\t\tdiffDis = diffDis > 100 ? diffDis / 100 : diffDis;\r\n\t\t\tthis.currentDis += diffDis;\r\n\t\t\tthis.currentDis = Math.max(0, this.currentDis);\r\n\t\t\treturn this.currentDis;\r\n\t\t},\r\n\t\t// 判断touch手势是否要触发\r\n\t\t_touchDisabled() {\r\n\t\t\tconst checkOldScrollTop = this.oldScrollTop > 5;\r\n\t\t\treturn this.loading || this.isRefresherInComplete || this.useChatRecordMode || !this.refresherEnabled || !this.useCustomRefresher ||(this.usePageScroll && this.useCustomRefresher && this.pageScrollTop > 10) || (!(this.usePageScroll && this.useCustomRefresher) && checkOldScrollTop);\r\n\t\t},\r\n\t\t// #endif\r\n\t\t// 更新自定义下拉刷新view高度\r\n\t\t_updateCustomRefresherHeight() {\r\n\t\t\tthis._getNodeClientRect('.zp-custom-refresher-slot-view').then((res) => {\r\n\t\t\t\tthis.customRefresherHeight = res ? res[0].height : 0;\r\n\t\t\t\tthis.showCustomRefresher = this.customRefresherHeight > 0;\r\n\t\t\t\tif (this.doRefreshAnimateAfter) {\r\n\t\t\t\t\tthis.doRefreshAnimateAfter = false;\r\n\t\t\t\t\tthis._doRefresherRefreshAnimate();\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t// emit pullingDown事件\r\n\t\t_emitTouchmove(e) {\r\n\t\t\t// #ifndef APP-NVUE\r\n\t\t\te.viewHeight = this.finalRefresherThreshold;\r\n\t\t\t// #endif\r\n\t\t\te.rate = e.viewHeight > 0 ? e.pullingDistance / e.viewHeight : 0;\r\n\t\t\tthis.hasTouchmove && this.oldPullingDistance !== e.pullingDistance && this.$emit('refresherTouchmove', e);\r\n\t\t\tthis.oldPullingDistance = e.pullingDistance;\r\n\t\t},\r\n\t\t// 清除refresherCompleteTimeout\r\n\t\t_cleanRefresherCompleteTimeout() {\r\n\t\t\tthis.refresherCompleteTimeout = this._cleanTimeout(this.refresherCompleteTimeout);\r\n\t\t\t// #ifdef APP-NVUE\r\n\t\t\tthis._nRefresherEnd(false);\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\t// 清除refresherEndTimeout\r\n\t\t_cleanRefresherEndTimeout() {\r\n\t\t\tthis.refresherEndTimeout = this._cleanTimeout(this.refresherEndTimeout);\r\n\t\t},\r\n\t}\r\n}\r\n"], "names": ["u", "Enum", "uni"], "mappings": ";;;;AAQA,MAAe,kBAAA;AAAA,EACd,OAAO;AAAA;AAAA,IAEN,qBAAqB;AAAA,MACpB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,uBAAuB,EAAE;AAAA,IACvC;AAAA;AAAA,IAED,mBAAmB;AAAA,MAClB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,qBAAqB,CAAA,CAAE;AAAA,IACrC;AAAA;AAAA,IAED,qBAAqB;AAAA,MACpB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,uBAAuB,CAAA,CAAE;AAAA,IACvC;AAAA;AAAA,IAED,0BAA0B;AAAA,MACzB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,4BAA4B,CAAA,CAAE;AAAA,IAC5C;AAAA;AAAA,IAED,yBAAyB;AAAA,MACxB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,2BAA2B,KAAK;AAAA,IAC9C;AAAA;AAAA,IAED,uBAAuB;AAAA,MACtB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,yBAAyB,EAAE;AAAA,IACzC;AAAA;AAAA,IAED,eAAe;AAAA,MACd,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,iBAAiB,KAAK;AAAA,IACpC;AAAA;AAAA,IAED,0BAA0B;AAAA,MACzB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,4BAA4B,GAAG;AAAA,IAC7C;AAAA;AAAA,IAED,wBAAwB;AAAA,MACvB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,0BAA0B,CAAC;AAAA,IACzC;AAAA;AAAA,IAED,2BAA2B;AAAA,MAC1B,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,6BAA6B,GAAG;AAAA,IAC9C;AAAA;AAAA,IAED,+BAA+B;AAAA,MAC9B,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,iCAAiC,IAAI;AAAA,IACnD;AAAA;AAAA,IAED,6BAA6B;AAAA,MAC5B,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,+BAA+B,KAAK;AAAA,IAClD;AAAA;AAAA,IAED,oBAAoB;AAAA,MACnB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,sBAAsB,IAAI;AAAA,IACxC;AAAA;AAAA,IAED,cAAc;AAAA,MACb,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,gBAAgB,EAAE;AAAA,IAChC;AAAA;AAAA,IAED,mBAAmB;AAAA,MAClB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,qBAAqB,EAAE;AAAA,IACrC;AAAA;AAAA,IAED,qCAAqC;AAAA,MACpC,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,uCAAuC,KAAK;AAAA,IAC1D;AAAA;AAAA,IAED,sBAAsB;AAAA,MACrB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,wBAAwB,IAAI;AAAA,IAC1C;AAAA;AAAA,IAED,sBAAsB;AAAA,MACrB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,wBAAwB,IAAI;AAAA,IAC1C;AAAA;AAAA,IAED,yBAAyB;AAAA,MACxB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,2BAA2B,IAAI;AAAA,IAC7C;AAAA;AAAA,IAED,uBAAuB;AAAA,MACtB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,yBAAyB,IAAI;AAAA,IAC3C;AAAA;AAAA,IAED,mBAAmB;AAAA,MAClB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,qBAAqB,IAAI;AAAA,IACvC;AAAA;AAAA,IAED,qBAAqB;AAAA,MACpB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,uBAAuB,IAAI;AAAA,IACzC;AAAA;AAAA,IAED,qBAAqB;AAAA,MACpB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,uBAAuB,IAAI;AAAA,IACzC;AAAA;AAAA,IAED,wBAAwB;AAAA,MACvB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,0BAA0B,IAAI;AAAA,IAC5C;AAAA;AAAA,IAED,sBAAsB;AAAA,MACrB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,wBAAwB,IAAI;AAAA,IAC1C;AAAA;AAAA,IAED,6BAA6B;AAAA,MAC5B,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,+BAA+B,IAAI;AAAA,IACjD;AAAA;AAAA,IAED,2BAA2B;AAAA,MAC1B,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,6BAA6B,IAAI;AAAA,IAC/C;AAAA;AAAA,IAED,kBAAkB;AAAA,MACjB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,oBAAoB,IAAI;AAAA,IACtC;AAAA;AAAA,IAED,oBAAoB;AAAA,MACnB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,sBAAsB,OAAO;AAAA,IAC3C;AAAA;AAAA,IAED,uBAAuB;AAAA,MACtB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,yBAAyB,OAAO;AAAA,IAC9C;AAAA;AAAA,IAED,qBAAqB;AAAA,MACpB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,uBAAuB,aAAa;AAAA,IAClD;AAAA;AAAA,IAED,0BAA0B;AAAA,MACzB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,4BAA4B,aAAa;AAAA,IACvD;AAAA;AAAA,IAED,yBAAyB;AAAA,MACxB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,2BAA2B,CAAC;AAAA,IAC1C;AAAA;AAAA,IAED,kBAAkB;AAAA,MACjB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,oBAAoB,IAAI;AAAA,IACtC;AAAA;AAAA,IAED,oBAAoB;AAAA,MACnB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,sBAAsB,KAAK;AAAA,IACzC;AAAA;AAAA,IAED,sBAAsB;AAAA,MACrB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,wBAAwB,QAAQ;AAAA,IAC9C;AAAA;AAAA,IAED,qBAAqB;AAAA,MACpB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,uBAAuB,GAAG;AAAA,IACxC;AAAA;AAAA,IAED,iBAAiB;AAAA,MAChB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,mBAAmB,IAAI;AAAA,IACrC;AAAA;AAAA,IAED,mBAAmB;AAAA,MAClB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,qBAAqB,IAAI;AAAA,IACvC;AAAA;AAAA,IAED,yBAAyB;AAAA,MACxB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,2BAA2B,KAAK;AAAA,IAC9C;AAAA;AAAA,IAED,wBAAwB;AAAA,MACvB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,0BAA0B,SAAS;AAAA,IACjD;AAAA;AAAA,IAED,kBAAkB;AAAA,MACjB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,oBAAoB,KAAK;AAAA,IACvC;AAAA;AAAA,IAED,sBAAsB;AAAA,MACrB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,wBAAwB,KAAK;AAAA,IAC3C;AAAA;AAAA,IAED,kCAAkC;AAAA,MACjC,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,oCAAoC,KAAK;AAAA,IACvD;AAAA,EACD;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,GAAGC,sDAAI,KAAC;AAAA;AAAA,MAER,iBAAiBA,sDAAAA,KAAK,UAAU;AAAA,MAChC,sBAAsB;AAAA,MACtB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,oBAAoB;AAAA,MACpB,qBAAqB;AAAA,MACrB,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,0BAA0B;AAAA,MAC1B,6BAA6B;AAAA,MAC7B,qBAAqB;AAAA,MACrB,sBAAsB;AAAA,MACtB,oBAAoB;AAAA,MACpB,eAAe;AAAA,MACf,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,gCAAgC;AAAA,MAChC,uBAAuB;AAAA,MACvB,qBAAqB;AAAA,MACrB,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,mBAAmB;AAAA,MACnB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,yBAAyB;AAAA,MACzB,oBAAoB;AAAA,MACpB,6BAA6B;AAAA,IAC7B;AAAA,EACD;AAAA,EACD,OAAO;AAAA,IACN,uBAAuB;AAAA,MACtB,QAAQ,QAAQ;AACf,YAAI,OAAO,QAAQ;AAClB,eAAK,6BAA6B;AAAA,QAClC;AAAA,MACD;AAAA,MACD,WAAW;AAAA,IACX;AAAA,IACD,gBAAgB,QAAQ;AACvB,iBAAWA,sDAAI,KAAC,UAAU,WAAW,KAAK,0BAAyB;AACnE,WAAK,qBAAqB,WAAWA,sDAAI,KAAC,UAAU,oBAAoB,WAAWA,sDAAAA,KAAK,UAAU,SAAS,KAAK,gBAAe;AAC/H,WAAK,MAAM,yBAAyB,MAAM;AAC1C,WAAK,MAAM,0BAA0B,MAAM;AAAA,IAC3C;AAAA;AAAA,IAED,iBAAiB,QAAQ;AAExB,OAAC,UAAU,KAAK;IAChB;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACT,uBAAuB;AACtB,aAAO,MAAO,KAAK;AAAA,IACnB;AAAA,IACD,kCAAkC;AACjC,aAAOD,uDAAAA,EAAE,QAAQ,KAAK,oBAAoB,KAAK,IAAI;AAAA,IACnD;AAAA,IACD,wBAAwB;AACvB,UAAI,KAAK;AAAmB,eAAO;AACnC,UAAI,KAAK,4BAA4B;AAAI,eAAO,KAAK;AACrD,aAAO,KAAK,4BAA4B;AAAA,IACxC;AAAA,IACD,0BAA0B;AACzB,UAAI,qBAAqB,KAAK;AAC9B,UAAI,YAAY;AAChB,UAAI,uBAAuBA,uDAAAA,EAAE,QAAQ,IAAI,KAAK,IAAI,GAAG;AACpD,oBAAY;AACZ,YAAI,KAAK,yBAAyB;AACjC,+BAAqBA,uDAAC,EAAC,QAAQ,KAAK,KAAK,IAAI;AAAA,QAC7C;AAAA,MACD;AACD,UAAI,aAAa,KAAK,wBAAwB;AAAG,eAAO,KAAK,wBAAwB,KAAK;AAC1F,aAAOA,uDAAC,EAAC,YAAY,kBAAkB,IAAI,KAAK;AAAA,IAChD;AAAA,IACD,4BAA4B;AAC3B,aAAOA,uDAAC,EAAC,YAAYA,uDAAAA,EAAE,QAAQ,KAAK,sBAAsB,KAAK,IAAI,CAAC;AAAA,IACpE;AAAA,IACD,qCAAqC;AACpC,aAAO,KAAK,mCAAmC,KAAK,kBAAkB;AAAA,IACtE;AAAA,IACD,+BAA+B;AAC9B,aAAOA,yDAAE,YAAY,KAAK,uBAAuB;AAAA,IACjD;AAAA,IACD,2BAA2B;AAC1B,aAAO,KAAK,oBAAoB,SAAS,KAAK,sBAAsB,KAAK;AAAA,IACzE;AAAA,IACD,wBAAwB;AACvB,UAAI,OAAO,KAAK;AAChB,aAAO,KAAK,IAAI,GAAE,IAAI;AACtB,aAAO,KAAK,IAAI,GAAE,IAAI;AACtB,aAAO;AAAA,IACP;AAAA,IACD,yBAAyB;AACxB,UAAI,OAAO,KAAK;AAChB,aAAO,KAAK,IAAI,GAAE,IAAI;AACtB,aAAO;AAAA,IACP;AAAA,IACD,0BAA0B;AACzB,UAAI,KAAK,wBAAwB,KAAK,uBAAuB;AAAmB,eAAO;AACvF,aAAO,KAAK;AAAA,IACZ;AAAA,IACD,+BAA+B;AAC9B,aAAO,KAAK,2BAA2B,KAAK;AAAA,IAC5C;AAAA,IACD,0BAA0B;AACzB,UAAI,EAAE,KAAK,yBAAyB,CAAC,KAAK;AAAqB,eAAO;AACtE,aAAO,KAAK;AAAA,IACZ;AAAA,IACD,gBAAgB;AACf,YAAM,gBAAgB,KAAK,yBAAyB,KAAK,sBAAsB,CAAC,KAAK;AAErF,WAAK,UAAU,KAAK,0BAA0B,MAAM,iBAAiB,KAAK;AAE1E,aAAO;AAAA,IACP;AAAA,IACD,eAAe;AAUd,aAAO,KAAK;AAAA,IACZ;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAER,aAAa;AACZ,WAAK,YAAY,KAAK;AACtB,WAAK,cAAa;AAClB,WAAK,4BAA2B;AAChC,WAAK,wBAAwB,EAAE,QAAQ,KAAM,CAAA;AAC7C,WAAK,UAAU,MAAM;AACpB,aAAK,qBAAqB;AAAA,MAC9B,CAAI;AAAA,IACD;AAAA;AAAA,IAED,8BAA8B;AAC7BA,6DAAC,EAAC,MAAM,MAAM,KAAK,UAAU,KAAK,4BAA4B,CAAC;AAAA,IAC/D;AAAA;AAAA,IAED,UAAU;AACT,WAAK,eAAc;AAAA,IACnB;AAAA;AAAA,IAED,WAAW,iBAAiB,OAAO,iBAAiB,MAAM;AACzD,UAAI,kBAAkB,EAAE,KAAK,yBAAyB,CAAC,KAAK;AAAqB;AACjF,WAAK,MAAM,WAAW;AACtB,WAAK,MAAM,SAAS;AAOpB,UAAI,KAAK,WAAW,KAAK;AAAuB;AAChD,WAAK,cAAcC,2DAAK,YAAY;AACpC,UAAI,KAAK;AAAsB;AAC/B,WAAK,iBAAiB;AACtB,WAAK,eAAe,CAAC;AACrB,WAAK,cAAc,IAAI;AACvB,WAAK,qBAAqB;AAC1B,UAAG,KAAK,qBAAqB,gBAAe;AAC3C,aAAK,oBAAoB,KAAK,eAAe,OAAO,IAAI,KAAK,QAAQ,OAAO,OAAO,cAAc;AAAA,MACjG;AAAA,IACD;AAAA;AAAA,IAED,aAAa;AACZ,WAAK,qBAAqB;AAC1B,WAAK,MAAM,WAAW;AACtB,WAAK,MAAM,SAAS;AAAA,IACpB;AAAA;AAAA,IAUD,2BAA2B,OAAO;AACjC,UAAI,CAAC,KAAK,WAAW,KAAK,cAAc;AACvC,aAAK,gBAAgB;AAAA,MACrB;AACD,WAAK,cAAcA,2DAAK,YAAY;AACpC,WAAK,wBAAwB,aAAa,KAAK,oBAAoB;AACnE,WAAK,eAAe;AACpB,WAAK,sBAAsB;AAC3B,WAAK,uBAAuB,MAAM;AAClC,WAAK,MAAM,uBAAuB,KAAK,oBAAoB;AAC3D,WAAK,yBAAyB;AAC9B,WAAK,+BAA8B;AACnC,WAAK,0BAAyB;AAAA,IAC9B;AAAA;AAAA;AAAA,IAgED,0BAA0B,SAAS,OAAO;AACzC,WAAK,yBAAyB;AAC9B,WAAK,wBAAwB,aAAa,KAAK,oBAAoB;AACnE,WAAK,gBAAgB;AACrB,WAAK,eAAe;AAGpB,UAAI,WAAW,KAAK,yBAAyB;AAE5C,aAAK,kBAAkB,KAAK,sBAAsB,WAAW,KAAK,4BAA4BA,sDAAAA,KAAK,UAAU,OAAOA,sDAAAA,KAAK,UAAU;AAAA,MACvI,OAAU;AAEN,aAAK,kBAAkBA,2DAAK,UAAU;AAAA,MACtC;AAOD,WAAK,UAAU;AAAA,IACf;AAAA;AAAA,IAgBD,yBAAyB,SAAS;AAIjC,WAAK,wBAAwB,aAAa,KAAK,oBAAoB;AACnE,WAAK,yBAAyB;AAC9B,WAAK,eAAe;AACpB,YAAM,qBAAqB,KAAK;AAChC,UAAI,WAAW,uBAAuB,KAAK,oBAAoBA,sDAAAA,KAAK,UAAU,oBAAoB,KAAK,oBAAoBA,sDAAI,KAAC,UAAU,OAAO;AAEhJ,YAAI,KAAK,oBAAoBA,2DAAK,UAAU,MAAM;AACjD,eAAK,YAAW;AAChB,eAAK,cAAa;AAAA,QACvB,OAAW;AAMND,iEAAC,EAAC,MAAM,MAAM;AACb,iBAAK,eAAe,EAAE,iBAAiB,oBAAoB,IAAI,KAAK,UAAU,mBAAkB,CAAE;AAAA,UAClG,GAAE,GAAG;AACN,eAAK,UAAU;AACf,eAAK,kBAAkBC,2DAAK,UAAU;AACtC,eAAK,iBAAgB;AAAA,QACrB;AAAA,MACL,OAAU;AACN,aAAK,cAAa;AAClB,aAAK,uBAAuBD,yDAAE,MAAM,MAAM;AACzC,eAAK,gBAAgB;AAAA,QAC1B,GAAO,KAAK,wBAAwB;AAAA,MAChC;AACD,WAAK,eAAe;AACpB,WAAK,MAAM,qBAAqB,OAAO;AAAA,IACvC;AAAA;AAAA,IAED,wBAAwB;AACvB,UAAI,KAAK,qBAAqB,KAAK,0BAA0B;AAC5DE,sBAAG,MAAC,aAAY;AAChB,aAAK,MAAM,eAAe;AAAA,MAC1B;AAAA,IACD;AAAA;AAAA,IAED,wBAAwB,EAAE,UAAU;AACnC,UAAI,CAAC,KAAK,iBAAiB,CAAC,KAAK,0BAA0B;AAC1D,YAAI,KAAK,gBAAgB,GAAG;AAE3B,eAAK,sBAAsB;AAE3B,eAAK,eAAe;AAAA,QACpB,WAAU,QAAQ;AAClB,eAAK,eAAe;AAAA,QACpB;AAAA,MACD;AAAA,IACD;AAAA;AAAA,IAED,kCAAkC,eAAe;AAChD,WAAK,mBAAmB;AACxB,UAAI,iBAAiB,CAAC,KAAK,mBAAmB;AAC7C,aAAK,sBAAsB;AAAA,MAC3B;AAAA,IACD;AAAA;AAAA,IAED,sBAAsB,EAAE,SAAS,WAAU;AAC1C,WAAK,eAAe,EAAE,iBAAiB,SAAQ,IAAI,QAAO,CAAE;AAAA,IAC5D;AAAA;AAAA,IAED,4BAA4B,EAAE,aAAa;AAC1C,WAAK,MAAM,wBAAuB,SAAS;AAAA,IAC3C;AAAA;AAAA,IAED,oBAAmB;AAClB,WAAK,cAAcF,uDAAAA,EAAE,QAAS,EAAC,SAAQ;AAAA,IACvC;AAAA;AAAA,IAED,cAAc,wBAAwB,MAAM,cAAc,OAAO,iBAAiB,OAAO,aAAa,MAAM;AAC3G,UAAI,KAAK,gBAAgBC,2DAAK,YAAY,WAAW;AAEpD,cAAM,yBAA0B,gBAAgB,kBAAkB,KAAK,2BAA4B,KAAK,yBAAyB;AAEjI,cAAM,kBAAkB,yBAAyB,IAAIA,sDAAI,KAAC,UAAU,WAAWA,sDAAAA,KAAK,UAAU;AAC9F,YAAI,KAAK,8BAA8B;AACtC,gBAAM,aAAa,KAAK;AACxB,eAAK;AACL,cAAI,aAAa;AAAG;AAAA,QACpB;AACD,aAAK,0BAAyB;AAC9B,aAAK,sBAAsBD,yDAAE,MAAM,MAAM;AAExC,eAAK,kBAAkB;AAEvB,cAAI,oBAAoBC,sDAAAA,KAAK,UAAU,UAAU;AAChD,iBAAK,wBAAwB;AAAA,UAC7B;AAAA,QACD,GAAE,KAAK,oBAAoBA,2DAAK,UAAU,WAAW,oBAAoBA,sDAAI,KAAC,UAAU,UAAU,KAAK,4BAA4B,CAAC;AAGrI,YAAI,yBAAyB,GAAG;AAC/B,eAAK,wBAAwB;AAAA,QAC7B;AAED,aAAK,+BAA8B;AACnC,aAAK,2BAA2BD,yDAAE,MAAM,MAAM;AAC7C,cAAI,kBAAkB;AACtB,gBAAM,cAAc,KAAK,6BAA6B,cAAc,sCAAsC;AAC1G,cAAI,aAAa;AAChB,8BAAkB,KAAK,4BAA4B,KAAK,4BAA4B,MAAO,KAAK,4BAA4B;AAAA,UAC5H;AACD,eAAK,sBAAsB,aAAa,cAAc,kBAAkB,KAAK,2BAA2B,GAAI,KAAK,WAAW;AAM5H,eAAK,cAAc,KAAK,sBAAsB,QAAQA,uDAAAA,EAAE;AAKxD,eAAK,UAAU;AAEf,cAAI,oBAAoBC,sDAAAA,KAAK,UAAU,UAAU;AAChD,gBAAI,KAAK,6BAA6B;AACrC,2BAAa,KAAK,2BAA2B;AAC7C,mBAAK,8BAA8B;AAAA,YACnC;AACD,iBAAK,8BAA8BD,yDAAE,MAAM,MAAM;AAChD,mBAAK,UAAU,MAAM;AACpB,qBAAK,kBAAkBC,2DAAK,UAAU;AACtC,qBAAK,wBAAwB;AAAA,cACrC,CAAQ;AAAA,YACR,GAAS,kBAAkB,GAAG;AAAA,UACxB;AAED,eAAK,eAAe,EAAE,iBAAiB,GAAG,IAAI,KAAK,QAAO,CAAE;AAAA,QAC5D,GAAE,sBAAsB;AAAA,MACzB;AACD,UAAI,YAAY;AACfD,iEAAE,MAAM,MAAM,KAAK,UAAU,OAAO,wBAAwB,KAAK,CAAC;AAClE,0BAAkB,KAAK;MACvB;AAAA,IACD;AAAA;AAAA,IAED,cAAc;AACb,UAAI,KAAK,UAAU,CAAC,KAAK;AAAoB;AAC7C,WAAK,MAAM,qBAAqB,IAAI;AAEpC,UAAI,CAAC,KAAK;AAAiB;AAE3B,WAAK,cAAc,cAAc,CAAC,KAAK,kBAAkB;AACzD,WAAK,SAAS;AACdA,6DAAC,EAAC,MAAM,MAAM;AACb,aAAK,cAAc;AAAA,MACvB,GAAM,KAAK,aAAa;AAAA,IAyBrB;AAAA;AAAA,IAED,iBAAiB;AAChB,UAAI,CAAC,KAAK,UAAU,CAAC,KAAK;AAAoB;AAC9C,WAAK,MAAM,qBAAqB,OAAO;AAEvC,UAAI,CAAC,KAAK;AAAiB;AAE3B,WAAK,cAAc,cAAc,CAAC,KAAK,kBAAkB;AAazDA,6DAAC,EAAC,MAAM,MAAM;AACb,aAAK,SAAS;AACd,aAAK,aAAa;AAAA,MACtB,GAAM,KAAK,qBAAqB,cAAc;AAAA,IAC3C;AAAA;AAAA,IAED,6BAA6B;AAC5B,WAAK,+BAA8B;AAGnC,YAAM,wBAAwB,CAAC,KAAK,yBAA0B,KAAK,gCAAiC,KAClG,0BAA0B,MAAM,KAAK,uBAAuBA,uDAAAA,EAAE,QAAQ,IAAI,KAAK,IAAI;AACrF,UAAI,uBAAuB;AAC1B,aAAK,wBAAwB;AAC7B;AAAA,MACA;AAED,WAAK;AAKL,WAAK,cAAc,UAAUA,uDAAC,EAAC,QAAO;AAEtC,WAAK,UAAU,KAAK;AACpB,WAAK,kBAAkBC,2DAAK,UAAU;AACtC,WAAK,gBAAgB;AACrB,WAAK,wBAAwB,aAAa,KAAK,oBAAoB;AACnE,WAAK,iBAAiB,KAAK;AAAA,IAC3B;AAAA;AAAA,IAED,iBAAiB,iBAAiB,MAAM;AACvC,WAAK,WAAW,OAAO,cAAc;AACrC,WAAK,UAAU;AAAA,IACf;AAAA;AAAA,IA0BD,+BAA+B;AAC9B,WAAK,mBAAmB,gCAAgC,EAAE,KAAK,CAAC,QAAQ;AACvE,aAAK,wBAAwB,MAAM,IAAI,CAAC,EAAE,SAAS;AACnD,aAAK,sBAAsB,KAAK,wBAAwB;AACxD,YAAI,KAAK,uBAAuB;AAC/B,eAAK,wBAAwB;AAC7B,eAAK,2BAA0B;AAAA,QAC/B;AAAA,MACL,CAAI;AAAA,IACD;AAAA;AAAA,IAED,eAAe,GAAG;AAEjB,QAAE,aAAa,KAAK;AAEpB,QAAE,OAAO,EAAE,aAAa,IAAI,EAAE,kBAAkB,EAAE,aAAa;AAC/D,WAAK,gBAAgB,KAAK,uBAAuB,EAAE,mBAAmB,KAAK,MAAM,sBAAsB,CAAC;AACxG,WAAK,qBAAqB,EAAE;AAAA,IAC5B;AAAA;AAAA,IAED,iCAAiC;AAChC,WAAK,2BAA2B,KAAK,cAAc,KAAK,wBAAwB;AAAA,IAIhF;AAAA;AAAA,IAED,4BAA4B;AAC3B,WAAK,sBAAsB,KAAK,cAAc,KAAK,mBAAmB;AAAA,IACtE;AAAA,EACD;AACF;;"}