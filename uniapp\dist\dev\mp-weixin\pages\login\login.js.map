{"version": 3, "file": "login.js", "sources": ["../../../../../src/pages/login/login.vue", "../../../../../uniPage:/cGFnZXMvbG9naW4vbG9naW4udnVl"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n  <PageLayout :navbarShow=\"false\">\r\n    <view class=\"header\"></view>\r\n    <view class=\"page-container\">\r\n      <view class=\"logo-section\">\r\n        <image :src=\"compLogo\" mode=\"aspectFit\" class=\"logo\" style=\"width: 300upx; height: 300upx;\"></image>\r\n        <view class=\"title text-shadow\">{{ compTitle }}</view>\r\n      </view>\r\n      <view class=\"content-wrapper\">\r\n        <view class=\"enter-area\">\r\n          <view class=\"box user-category\">\r\n            <wd-text text=\"请选择登录类型：\"></wd-text>\r\n            <view class=\"user-category-options\">\r\n              <wd-button :class=\"['user-category-option', userCategory === 1 ? 'active' : '']\" custom-class=\"mt-20px\"\r\n                hairline open-type=\"getPhoneNumber\" @getphonenumber=\"(e) => handleCategoryLogin(e, 1)\"\r\n                @click=\"userCategory = 1\">\r\n                患者端\r\n              </wd-button>\r\n              <wd-button :class=\"['user-category-option', userCategory === 0 ? 'active' : '']\" custom-class=\"mt-20px\"\r\n                hairline open-type=\"getPhoneNumber\" @getphonenumber=\"(e) => handleCategoryLogin(e, 0)\"\r\n                @click=\"userCategory = 0\">\r\n                医生端\r\n              </wd-button>\r\n              <wd-button :class=\"['user-category-option', userCategory === 2 ? 'active' : '']\" custom-class=\"mt-20px\"\r\n                hairline open-type=\"getPhoneNumber\" @getphonenumber=\"(e) => handleCategoryLogin(e, 2)\"\r\n                @click=\"userCategory = 2\">\r\n                社工/社区医生端\r\n              </wd-button>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      <wd-notify />\r\n    </view>\r\n  </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { useNotify, useToast } from 'wot-design-uni'\r\nimport { ref } from 'vue'\r\nimport { useUserStore } from '@/store/user'\r\nimport { http } from '@/utils/http'\r\nimport {\r\n  ACCESS_TOKEN,\r\n  USER_NAME,\r\n  USER_INFO,\r\n  APP_ROUTE,\r\n  APP_CONFIG,\r\n  HOME_CONFIG_EXPIRED_TIME,\r\n  HOME_PAGE,\r\n} from '@/common/constants'\r\n\r\nimport { cache, getFileAccessHttpUrl } from '@/common/uitls'\r\nimport { useRouter } from '@/plugin/uni-mini-router'\r\nimport { useParamsStore } from '@/store/page-params'\r\n\r\ndefineOptions({\r\n  name: 'login',\r\n  options: {\r\n    // apply-shared‌：当前页面样式会影响到子组件样式.(小程序)\r\n    // shared‌：当前页面样式影响到子组件，子组件样式也会影响到当前页面.(小程序)\r\n    styleIsolation: 'shared',\r\n  },\r\n})\r\nconst router = useRouter()\r\nconst defLogo = 'https://www.mograine.cn/images/logo2.png'\r\n\r\nconst compLogo = ref(defLogo)\r\nconst compTitle = ref('心 衰 院 外 管 理')\r\nconst userCategory = ref(-1) // 用户类型：0医生 1患者 2社工，-1表示未选择\r\nconst paramsStore = useParamsStore()\r\nparamsStore.reset()\r\nlet isLocalConfig = true\r\n\r\nconst userStore = useUserStore()\r\n\r\n// 处理选择端并授权登录\r\nconst handleCategoryLogin = (e, category) => {\r\n  userCategory.value = category\r\n  mobileLogin(e)\r\n}\r\n\r\n// 手机号授权登录\r\nconst mobileLogin = (e) => {\r\n  console.log('手机号授权登录', e);\r\n  return new Promise(async (resolve, reject) => {\r\n    if (!e) {\r\n      console.error('e 未定义');\r\n      return resolve(false);\r\n    }\r\n\r\n    // 首先检查手机号授权是否通过\r\n    if (e.errMsg !== 'getPhoneNumber:ok') {\r\n      console.log('用户拒绝授权，不打印登录授权码和手机号授权码');\r\n      uni.showToast({\r\n        title: '您已拒绝授权手机号',\r\n        icon: 'none'\r\n      });\r\n      return resolve(false);\r\n    }\r\n\r\n    // 用户已授权手机号，显示确认对话框\r\n    let categoryText = '患者端';\r\n    if (userCategory.value === 0) {\r\n      categoryText = '医生端';\r\n    } else if (userCategory.value === 2) {\r\n      categoryText = '社工/社区医生端';\r\n    }\r\n\r\n    uni.showModal({\r\n      title: '登录确认',\r\n      content: `您确定要登录${categoryText}吗？`,\r\n      success: async function (res) {\r\n        if (res.confirm) {\r\n          // 用户点击确定，继续登录流程\r\n          const phoneCode = e.code; // 手机号授权码（根据此授权码在后端获取手机号）\r\n\r\n          // 调用微信登录接口获取微信登录授权码\r\n          try {\r\n            const codeResult = await wx.login();\r\n            if (codeResult.errMsg !== 'login:ok') {\r\n              return resolve(false);\r\n            }\r\n            const loginCode = codeResult.code; // 登录授权码（根据此授权码在后端获取openId）\r\n\r\n            // 打印登录授权码和手机号授权码\r\n            console.log('登录授权码:', loginCode);\r\n            console.log('手机号授权码:', phoneCode);\r\n\r\n            uni.request({\r\n              url: `${import.meta.env.VITE_SERVER_BASEURL}/wx/login`,\r\n              method: 'POST',\r\n              data: {\r\n                phoneCode,\r\n                loginCode,\r\n                userCategory: userCategory.value // 将用户类型传给后端\r\n              },\r\n              success: (res) => {\r\n                console.log(\"手机号授权成功返回参数\", res.data);\r\n                // 使用类型断言解决类型问题\r\n                const result = (res.data as any).result;\r\n                if (result) {\r\n                  userStore.setUserInfo({\r\n                    token: result.token,\r\n                    userid: result.userInfo.id,\r\n                    username: result.userInfo.username,\r\n                    realname: result.userInfo.realname,\r\n                    avatar: result.userInfo.avatar,\r\n                    phone: result.phoneNumber,\r\n                    tenantId: result.userInfo.loginTenantId,\r\n                    openid: result.openid,\r\n                    sex: result.userInfo.sex,\r\n                    userCategory: userCategory.value as unknown as String, // 使用类型断言\r\n                    roleList: result.userInfo.roleList,\r\n                    caseNumber: result.userInfo.caseNumber,\r\n                    localStorageTime: +new Date()\r\n                  })\r\n                  console.log(\"userStore.userInfo\", userStore.userInfo)\r\n                  wx.setStorageSync(ACCESS_TOKEN, result.accessToken);\r\n                  wx.setStorageSync('user', userStore.userInfo);\r\n                  uni.switchTab({\r\n                    url: '/pages/index/index'\r\n                  });\r\n                } else {\r\n                  uni.showToast({\r\n                    title: '登录失败，请重试',\r\n                    icon: 'none'\r\n                  });\r\n                }\r\n              },\r\n              fail: (err) => {\r\n                console.error('请求失败', err);\r\n                uni.showToast({\r\n                  title: '网络请求失败',\r\n                  icon: 'none'\r\n                });\r\n                resolve(false);\r\n              }\r\n            });\r\n          } catch (error) {\r\n            console.error('微信登录失败', error);\r\n            uni.showToast({\r\n              title: '登录失败',\r\n              icon: 'none'\r\n            });\r\n            resolve(false);\r\n          }\r\n        } else {\r\n          // 用户点击取消\r\n          console.log('用户取消登录确认');\r\n          return resolve(false);\r\n        }\r\n      }\r\n    });\r\n  });\r\n};\r\nconst loadConfig = () => {\r\n  http.get('/eoa/sysAppConfig/queryAppConfig').then((res: any) => {\r\n    if (res.success) {\r\n      let info = res.result\r\n      if (info) {\r\n        compLogo.value = getFileAccessHttpUrl(info.appLogo) || defLogo\r\n        compTitle.value = info.appTitle || 'JEECG-BOOT'\r\n      } else {\r\n        compLogo.value = defLogo\r\n      }\r\n    }\r\n  })\r\n}\r\nconst checkToken = () => {\r\n  const { userInfo, clearUserInfo } = userStore\r\n  if (userInfo.token) {\r\n    if (+new Date() - userInfo.localStorageTime > 2 * 3600000) {\r\n      // 超过2小时过期\r\n      clearUserInfo()\r\n    } else {\r\n      router.pushTab({ path: HOME_PAGE })\r\n    }\r\n  } else {\r\n    clearUserInfo()\r\n  }\r\n}\r\nconst checkAccount = () => { }\r\n\r\n\r\n\r\n\r\n// @ts-ignore\r\nif (isLocalConfig === false) {\r\n  loadConfig()\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.header {\r\n  //距离顶部 --status-bar-height 自动调整\r\n  margin-top: var(--status-bar-height);\r\n}\r\n\r\n.page-container {\r\n  padding: 0 20upx;\r\n  padding-top: 8vh;\r\n  position: relative;\r\n  font-size: 15px;\r\n  color: var(--UI-FG-0);\r\n  background-image: url('https://www.mograine.cn/images/background.png');\r\n  background-size: cover;\r\n  background-position: center;\r\n  background-repeat: no-repeat;\r\n  min-height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .logo-section {\r\n    text-align: center;\r\n    padding-top: 2vh;\r\n  }\r\n\r\n  .content-wrapper {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: flex-start;\r\n    align-items: center;\r\n    text-align: center;\r\n    padding-top: 6vh;\r\n  }\r\n\r\n  .logo {\r\n    width: 400upx;\r\n    height: 200upx;\r\n  }\r\n\r\n  .title {\r\n    font-size: 80upx;\r\n    color: white !important;\r\n  }\r\n\r\n  .enter-area {\r\n    padding-top: 8vh;\r\n    width: 87%;\r\n    margin: 0 auto;\r\n\r\n    .box {\r\n      display: flex;\r\n      align-items: center;\r\n      min-height: 100upx;\r\n      color: #000;\r\n      border: 1px solid #eee;\r\n      background-color: #fff;\r\n      padding: 0 20upx;\r\n      margin-bottom: 30upx;\r\n      box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);\r\n\r\n      :deep(.wd-text) {\r\n        margin: 0 10upx;\r\n        color: white !important;\r\n      }\r\n\r\n      :deep(.wd-input),\r\n      :deep(.uni-input) {\r\n        flex: 1;\r\n\r\n        &::after {\r\n          height: 0;\r\n        }\r\n      }\r\n\r\n      .uni-input {\r\n        text-align: left;\r\n        font-size: var(--wot-input-fs, var(--wot-cell-title-fs, 14px));\r\n        height: var(--wot-input-inner-height, 34px);\r\n        color: var(--wot-input-color, #262626);\r\n\r\n        .uni-input-placeholder {\r\n          color: var(--wot-input-placeholder-color, #bfbfbf);\r\n        }\r\n      }\r\n\r\n      &.user-category {\r\n        flex-direction: column;\r\n        background-color: transparent;\r\n        border: none;\r\n        box-shadow: none;\r\n\r\n        .user-category-options {\r\n          display: flex;\r\n          flex-direction: column;\r\n          width: 100%;\r\n          margin-top: 20px;\r\n          gap: 10px;\r\n\r\n          :deep(.wd-button) {\r\n            --wot-button-medium-height: 50px;\r\n            --wot-button-medium-fs: 16px;\r\n            width: 100%;\r\n            text-align: center;\r\n            border: 1px solid #e0e0e0;\r\n            border-radius: 8px;\r\n            font-size: 16px;\r\n            transition: all 0.3s ease;\r\n            background-color: white;\r\n            color: #333;\r\n\r\n            &.active {\r\n              background-color: #07C160 !important;\r\n              color: white !important;\r\n              border-color: #07C160 !important;\r\n              box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);\r\n              font-weight: 500;\r\n            }\r\n          }\r\n\r\n          .user-category-option {\r\n            &.active :deep(.wd-button) {\r\n              background-color: #07C160 !important;\r\n              color: white !important;\r\n              border-color: #07C160 !important;\r\n              box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);\r\n              font-weight: 500;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    :deep(.sendSMSBtn) {\r\n      margin-left: 20upx;\r\n    }\r\n\r\n    :deep(.wd-icon-view),\r\n    :deep(.wd-icon-eye-close) {\r\n      color: #555;\r\n    }\r\n  }\r\n}\r\n</style>", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages/login/login.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useRouter", "ref", "useParamsStore", "useUserStore", "uni", "wx", "res", "ACCESS_TOKEN"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqEA,MAAM,UAAU;;;;;;;;;;;AADDA,oCAAU,UAAA;AAGnB,UAAA,WAAWC,kBAAI,OAAO;AACtB,UAAA,YAAYA,kBAAI,aAAa;AAC7B,UAAA,eAAeA,kBAAI,EAAE;AAC3B,UAAM,cAAcC,iBAAAA,eAAe;AACnC,gBAAY,MAAM;AAGlB,UAAM,YAAYC,WAAAA,aAAa;AAGzB,UAAA,sBAAsB,CAAC,GAAG,aAAa;AAC3C,mBAAa,QAAQ;AACrB,kBAAY,CAAC;AAAA,IACf;AAGM,UAAA,cAAc,CAAC,MAAM;AACjB,cAAA,IAAI,WAAW,CAAC;AACxB,aAAO,IAAI,QAAQ,CAAO,SAAS,WAAW;AAC5C,YAAI,CAAC,GAAG;AACN,kBAAQ,MAAM,OAAO;AACrB,iBAAO,QAAQ,KAAK;AAAA,QAAA;AAIlB,YAAA,EAAE,WAAW,qBAAqB;AACpC,kBAAQ,IAAI,wBAAwB;AACpCC,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACP;AACD,iBAAO,QAAQ,KAAK;AAAA,QAAA;AAItB,YAAI,eAAe;AACf,YAAA,aAAa,UAAU,GAAG;AACb,yBAAA;AAAA,QAAA,WACN,aAAa,UAAU,GAAG;AACpB,yBAAA;AAAA,QAAA;AAGjBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS,SAAS,YAAY;AAAA,UAC9B,SAAS,SAAgB,KAAK;AAAA;AAC5B,kBAAI,IAAI,SAAS;AAEf,sBAAM,YAAY,EAAE;AAGhB,oBAAA;AACI,wBAAA,aAAa,MAAMC,cAAA,KAAG,MAAM;AAC9B,sBAAA,WAAW,WAAW,YAAY;AACpC,2BAAO,QAAQ,KAAK;AAAA,kBAAA;AAEtB,wBAAM,YAAY,WAAW;AAGrB,0BAAA,IAAI,UAAU,SAAS;AACvB,0BAAA,IAAI,WAAW,SAAS;AAEhCD,gCAAAA,MAAI,QAAQ;AAAA,oBACV,KAAK,GAAG,6BAAmC;AAAA,oBAC3C,QAAQ;AAAA,oBACR,MAAM;AAAA,sBACJ;AAAA,sBACA;AAAA,sBACA,cAAc,aAAa;AAAA;AAAA,oBAC7B;AAAA,oBACA,SAAS,CAACE,SAAQ;AACR,8BAAA,IAAI,eAAeA,KAAI,IAAI;AAE7B,4BAAA,SAAUA,KAAI,KAAa;AACjC,0BAAI,QAAQ;AACV,kCAAU,YAAY;AAAA,0BACpB,OAAO,OAAO;AAAA,0BACd,QAAQ,OAAO,SAAS;AAAA,0BACxB,UAAU,OAAO,SAAS;AAAA,0BAC1B,UAAU,OAAO,SAAS;AAAA,0BAC1B,QAAQ,OAAO,SAAS;AAAA,0BACxB,OAAO,OAAO;AAAA,0BACd,UAAU,OAAO,SAAS;AAAA,0BAC1B,QAAQ,OAAO;AAAA,0BACf,KAAK,OAAO,SAAS;AAAA,0BACrB,cAAc,aAAa;AAAA;AAAA,0BAC3B,UAAU,OAAO,SAAS;AAAA,0BAC1B,YAAY,OAAO,SAAS;AAAA,0BAC5B,kBAAkB,CAAC,oBAAI,KAAK;AAAA,wBAAA,CAC7B;AACO,gCAAA,IAAI,sBAAsB,UAAU,QAAQ;AACjDD,sCAAAA,KAAA,eAAeE,+BAAc,OAAO,WAAW;AAC/CF,sCAAAA,KAAA,eAAe,QAAQ,UAAU,QAAQ;AAC5CD,sCAAAA,MAAI,UAAU;AAAA,0BACZ,KAAK;AAAA,wBAAA,CACN;AAAA,sBAAA,OACI;AACLA,sCAAAA,MAAI,UAAU;AAAA,0BACZ,OAAO;AAAA,0BACP,MAAM;AAAA,wBAAA,CACP;AAAA,sBAAA;AAAA,oBAEL;AAAA,oBACA,MAAM,CAAC,QAAQ;AACL,8BAAA,MAAM,QAAQ,GAAG;AACzBA,oCAAAA,MAAI,UAAU;AAAA,wBACZ,OAAO;AAAA,wBACP,MAAM;AAAA,sBAAA,CACP;AACD,8BAAQ,KAAK;AAAA,oBAAA;AAAA,kBACf,CACD;AAAA,yBACM,OAAO;AACN,0BAAA,MAAM,UAAU,KAAK;AAC7BA,gCAAAA,MAAI,UAAU;AAAA,oBACZ,OAAO;AAAA,oBACP,MAAM;AAAA,kBAAA,CACP;AACD,0BAAQ,KAAK;AAAA,gBAAA;AAAA,cACf,OACK;AAEL,wBAAQ,IAAI,UAAU;AACtB,uBAAO,QAAQ,KAAK;AAAA,cAAA;AAAA,YACtB;AAAA;AAAA,QACF,CACD;AAAA,MAAA,EACF;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtMA,GAAG,WAAW,eAAe;"}