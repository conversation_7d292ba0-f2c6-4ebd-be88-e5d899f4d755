{"version": 3, "file": "wd-radio.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-radio/wd-radio.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1yYWRpby93ZC1yYWRpby52dWU"], "sourcesContent": ["<template>\n  <view\n    :class=\"`wd-radio ${cellValue ? 'is-cell-radio' : ''} ${cellValue && shapeValue == 'button' ? 'is-button-radio' : ''} ${\n      sizeValue ? 'is-' + sizeValue : ''\n    } ${inlineValue ? 'is-inline' : ''} ${isChecked ? 'is-checked' : ''} ${shapeValue !== 'check' ? 'is-' + shapeValue : ''} ${\n      disabledValue ? 'is-disabled' : ''\n    } icon-placement-${iconPlacement} ${customClass}`\"\n    :style=\"customStyle\"\n    @click=\"handleClick\"\n  >\n    <view\n      class=\"wd-radio__label\"\n      :style=\"`${maxWidth ? 'max-width:' + maxWidth : ''};  ${\n        isChecked && shapeValue === 'button' && !disabledValue ? 'color :' + checkedColorValue : ''\n      }`\"\n    >\n      <slot></slot>\n    </view>\n    <view class=\"wd-radio__shape\" :style=\"isChecked && !disabledValue ? 'color: ' + checkedColorValue : ''\">\n      <wd-icon v-if=\"shapeValue === 'check'\" :style=\"isChecked && !disabledValue ? 'color: ' + checkedColorValue : ''\" name=\"check\"></wd-icon>\n    </view>\n  </view>\n</template>\n<script lang=\"ts\">\nexport default {\n  name: 'wd-radio',\n  options: {\n    virtualHost: true,\n    addGlobalClass: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n<script lang=\"ts\" setup>\nimport wdIcon from '../wd-icon/wd-icon.vue'\nimport { computed, watch } from 'vue'\nimport { useParent } from '../composables/useParent'\nimport { RADIO_GROUP_KEY } from '../wd-radio-group/types'\nimport { radioProps, type RadioIconPlacement } from './types'\nimport { getPropByPath, isDef } from '../common/util'\n\nconst props = defineProps(radioProps)\n\nconst { parent: radioGroup } = useParent(RADIO_GROUP_KEY)\n\nconst isChecked = computed(() => {\n  if (radioGroup) {\n    return props.value === radioGroup.props.modelValue\n  } else {\n    return false\n  }\n})\n\nconst shapeValue = computed(() => {\n  return props.shape || getPropByPath(radioGroup, 'props.shape')\n})\n\nconst checkedColorValue = computed(() => {\n  return props.checkedColor || getPropByPath(radioGroup, 'props.checkedColor')\n})\n\nconst disabledValue = computed(() => {\n  if (isDef(props.disabled)) {\n    return props.disabled\n  } else {\n    return getPropByPath(radioGroup, 'props.disabled')\n  }\n})\n\nconst inlineValue = computed(() => {\n  if (isDef(props.inline)) {\n    return props.inline\n  } else {\n    return getPropByPath(radioGroup, 'props.inline')\n  }\n})\n\nconst sizeValue = computed(() => {\n  return props.size || getPropByPath(radioGroup, 'props.size')\n})\n\nconst cellValue = computed(() => {\n  if (isDef(props.cell)) {\n    return props.cell\n  } else {\n    return getPropByPath(radioGroup, 'props.cell')\n  }\n})\n\nconst iconPlacement = computed<RadioIconPlacement>(() => {\n  if (isDef(props.iconPlacement)) {\n    return props.iconPlacement\n  } else {\n    return getPropByPath(radioGroup, 'props.iconPlacement')\n  }\n})\n\nwatch(\n  () => props.shape,\n  (newValue) => {\n    const type = ['check', 'dot', 'button']\n    if (!newValue || type.indexOf(newValue) === -1) console.error(`shape must be one of ${type.toString()}`)\n  }\n)\n\n/**\n * 点击子元素，通知父元素触发change事件\n */\nfunction handleClick() {\n  const { value } = props\n  if (!disabledValue.value && radioGroup && isDef(value)) {\n    radioGroup.updateValue(value)\n  }\n}\n</script>\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-radio/wd-radio.vue'\nwx.createComponent(Component)"], "names": ["useParent", "RADIO_GROUP_KEY", "computed", "getPropByPath", "isDef", "watch"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAkCA,MAAA,SAAmB,MAAA;AAVnB,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,EAAA;AAEpB;;;;AAUA,UAAM,QAAQ;AAEd,UAAM,EAAE,QAAQ,eAAeA,cAAAA,UAAUC,cAAAA,eAAe;AAElD,UAAA,YAAYC,cAAAA,SAAS,MAAM;AAC/B,UAAI,YAAY;AACP,eAAA,MAAM,UAAU,WAAW,MAAM;AAAA,MAAA,OACnC;AACE,eAAA;AAAA,MAAA;AAAA,IACT,CACD;AAEK,UAAA,aAAaA,cAAAA,SAAS,MAAM;AAChC,aAAO,MAAM,SAASC,4BAAc,YAAY,aAAa;AAAA,IAAA,CAC9D;AAEK,UAAA,oBAAoBD,cAAAA,SAAS,MAAM;AACvC,aAAO,MAAM,gBAAgBC,4BAAc,YAAY,oBAAoB;AAAA,IAAA,CAC5E;AAEK,UAAA,gBAAgBD,cAAAA,SAAS,MAAM;AAC/B,UAAAE,cAAA,MAAM,MAAM,QAAQ,GAAG;AACzB,eAAO,MAAM;AAAA,MAAA,OACR;AACE,eAAAD,cAAA,cAAc,YAAY,gBAAgB;AAAA,MAAA;AAAA,IACnD,CACD;AAEK,UAAA,cAAcD,cAAAA,SAAS,MAAM;AAC7B,UAAAE,cAAA,MAAM,MAAM,MAAM,GAAG;AACvB,eAAO,MAAM;AAAA,MAAA,OACR;AACE,eAAAD,cAAA,cAAc,YAAY,cAAc;AAAA,MAAA;AAAA,IACjD,CACD;AAEK,UAAA,YAAYD,cAAAA,SAAS,MAAM;AAC/B,aAAO,MAAM,QAAQC,4BAAc,YAAY,YAAY;AAAA,IAAA,CAC5D;AAEK,UAAA,YAAYD,cAAAA,SAAS,MAAM;AAC3B,UAAAE,cAAA,MAAM,MAAM,IAAI,GAAG;AACrB,eAAO,MAAM;AAAA,MAAA,OACR;AACE,eAAAD,cAAA,cAAc,YAAY,YAAY;AAAA,MAAA;AAAA,IAC/C,CACD;AAEK,UAAA,gBAAgBD,cAAAA,SAA6B,MAAM;AACnD,UAAAE,cAAA,MAAM,MAAM,aAAa,GAAG;AAC9B,eAAO,MAAM;AAAA,MAAA,OACR;AACE,eAAAD,cAAA,cAAc,YAAY,qBAAqB;AAAA,MAAA;AAAA,IACxD,CACD;AAEDE,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,aAAa;AACZ,cAAM,OAAO,CAAC,SAAS,OAAO,QAAQ;AACtC,YAAI,CAAC,YAAY,KAAK,QAAQ,QAAQ,MAAM;AAAI,kBAAQ,MAAM,wBAAwB,KAAK,SAAU,CAAA,EAAE;AAAA,MAAA;AAAA,IAE3G;AAKA,aAAS,cAAc;AACf,YAAA,EAAE,UAAU;AAClB,UAAI,CAAC,cAAc,SAAS,cAAcD,cAAA,MAAM,KAAK,GAAG;AACtD,mBAAW,YAAY,KAAK;AAAA,MAAA;AAAA,IAC9B;;;;;;;;;;;;;;;;;;;;AC/GF,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}