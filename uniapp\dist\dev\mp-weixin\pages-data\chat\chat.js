"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const store_user = require("../../store/user.js");
const utils_http = require("../../utils/http.js");
if (!Array) {
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  const _easycom_wd_loading2 = common_vendor.resolveComponent("wd-loading");
  const _easycom_uni_calendar2 = common_vendor.resolveComponent("uni-calendar");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_uni_icons2 + _easycom_wd_loading2 + _easycom_uni_calendar2 + _easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
const _easycom_wd_loading = () => "../../node-modules/wot-design-uni/components/wd-loading/wd-loading.js";
const _easycom_uni_calendar = () => "../../uni_modules/uni-calendar/components/uni-calendar/uni-calendar.js";
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  (_easycom_uni_icons + _easycom_wd_loading + _easycom_uni_calendar + _easycom_PageLayout)();
}
const defAvatar = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNGNUY1RjUiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iIzk5OTk5OSIvPgo8cGF0aCBkPSJNMzAgMzJDMzAgMjYuNDc3MSAyNS41MjI5IDIyIDIwIDIyQzE0LjQ3NzEgMjIgMTAgMjYuNDc3MSAxMCAzMkgzMFoiIGZpbGw9IiM5OTk5OTkiLz4KPC9zdmc+";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "chat",
  setup(__props) {
    const userStore = store_user.useUserStore();
    const chatList = common_vendor.ref([]);
    const loadingChat = common_vendor.ref(false);
    const isRefreshing = common_vendor.ref(false);
    const startDate = common_vendor.ref("");
    const endDate = common_vendor.ref("");
    const datePickerType = common_vendor.ref("");
    const calendar = common_vendor.ref(null);
    function onStartDateChange(e) {
      startDate.value = e.detail.value;
    }
    function onEndDateChange(e) {
      endDate.value = e.detail.value;
    }
    const dateConfirm = (e) => {
      const selectedDate = e.fulldate;
      if (datePickerType.value === "start") {
        startDate.value = selectedDate;
      } else if (datePickerType.value === "end") {
        endDate.value = selectedDate;
      }
    };
    common_vendor.ref(false);
    const resetDateFilter = () => {
      if (isRefreshing.value) {
        return;
      }
      isRefreshing.value = true;
      common_vendor.index.showLoading({
        title: "刷新中...",
        mask: true
      });
      startDate.value = "";
      endDate.value = "";
      searchParams.value.patientName = "";
      searchParams.value.question = "";
      searchParams.value.patientAvatar = "";
      searchParams.value.createTime = "";
      chatList.value = [];
      loadingChat.value = false;
      fetchChatList().then(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "页面刷新成功",
          icon: "success",
          duration: 1500
        });
        setTimeout(() => {
          isRefreshing.value = false;
        }, 500);
      }).catch((error) => {
        common_vendor.index.hideLoading();
        console.error("❌ 页面刷新失败:", error);
        common_vendor.index.showToast({
          title: "刷新失败，请重试",
          icon: "none",
          duration: 2e3
        });
        setTimeout(() => {
          isRefreshing.value = false;
        }, 500);
      });
    };
    const hasSearchConditions = () => {
      const hasReplyStatusFilter = getSelectedReplyStatus() !== "all";
      if (Number(userStore.userInfo.userCategory) === 1) {
        return !!(searchParams.value.question && searchParams.value.question.trim() || startDate.value || endDate.value || hasReplyStatusFilter);
      }
      return !!(searchParams.value.patientName && searchParams.value.patientName.trim() || searchParams.value.question && searchParams.value.question.trim() || startDate.value || endDate.value || hasReplyStatusFilter || showOnlyMentioned.value);
    };
    const handleSearch = () => {
      fetchChatList();
    };
    console.log("userCategory:", userStore.userInfo.userCategory);
    const userAvatarCache = common_vendor.ref("");
    const fetchFileUrl = (objectName) => __async(this, null, function* () {
      var _a;
      try {
        const response = yield utils_http.http.get("/file/url", {
          objectName,
          token: userStore.userInfo.token
        });
        if ((response == null ? void 0 : response.success) && ((_a = response == null ? void 0 : response.result) == null ? void 0 : _a.fileUrl)) {
          return response.result.fileUrl;
        } else {
          return "";
        }
      } catch (error) {
        return "";
      }
    });
    const getAvatarUrl = (avatar) => {
      if (!avatar || avatar.trim() === "") {
        return defAvatar;
      }
      if (avatar.startsWith("http://") || avatar.startsWith("https://")) {
        return avatar;
      }
      if (avatar.startsWith("data:image/")) {
        return avatar;
      }
      if (avatar.startsWith("/")) {
        return "https://www.mograine.cn" + avatar;
      }
      if (userAvatarCache.value && avatar === userStore.userInfo.avatar) {
        return userAvatarCache.value;
      }
      return "https://www.mograine.cn/images/" + avatar;
    };
    const handleAvatarError = (event) => {
      event.target.src = defAvatar;
    };
    const searchParams = common_vendor.ref({
      patientName: "",
      // 患者姓名
      question: "",
      // 问题关键字
      patientAvatar: "",
      createTime: ""
    });
    const replyStatusOptions = common_vendor.ref([
      { value: "all", label: "全部", checked: true },
      { value: "pending", label: "待回复", checked: false },
      { value: "replied", label: "已回复", checked: false }
    ]);
    const showOnlyMentioned = common_vendor.ref(false);
    const getSelectedReplyStatus = () => {
      const selected = replyStatusOptions.value.find((option) => option.checked);
      return selected ? selected.value : "all";
    };
    const onReplyStatusChange = (selectedValue) => {
      replyStatusOptions.value.forEach((option) => {
        option.checked = option.value === selectedValue;
      });
    };
    const toggleMentionFilter = () => {
      showOnlyMentioned.value = !showOnlyMentioned.value;
      console.log("🔍 切换@我的问题筛选:", showOnlyMentioned.value ? "开启" : "关闭");
    };
    const fetchChatList = () => __async(this, null, function* () {
      return new Promise((resolve, reject) => {
        try {
          loadingChat.value = true;
          const params = {};
          if (userStore.userInfo.userid) {
            params.user_id = userStore.userInfo.userid;
          }
          if (userStore.userInfo.userCategory !== void 0 && userStore.userInfo.userCategory !== "") {
            params.category = Number(userStore.userInfo.userCategory);
          }
          if (Number(userStore.userInfo.userCategory) === 0 && searchParams.value.patientName && searchParams.value.patientName.trim()) {
            params.patientName = searchParams.value.patientName.trim();
          }
          if (searchParams.value.question && searchParams.value.question.trim()) {
            params.question = searchParams.value.question.trim();
          }
          if (startDate.value) {
            params.startDate = startDate.value;
          }
          if (endDate.value) {
            params.endDate = endDate.value;
          }
          if (!userStore.userInfo.token) {
            console.error("❌ Token未设置，可能需要重新登录");
            common_vendor.index.showToast({
              title: "登录状态已过期，请重新登录",
              icon: "none",
              duration: 3e3
            });
            common_vendor.index.reLaunch({
              url: "/pages/login/login"
            });
            reject(new Error("Token未设置"));
            return;
          }
          utils_http.http.get("/communication/list", params).then((res) => {
            if (res.success && res.result) {
              console.log("API返回的获取沟通记录详情:", res.result);
              const processedData = res.result.map((item) => {
                let replyToContent = null;
                if (item.replyToUserId) {
                  console.log("聊天列表检测到回复关系，replyToUserId:", item.replyToUserId);
                  if (item.replyToContent && typeof item.replyToContent === "string" && item.replyToContent.trim()) {
                    replyToContent = item.replyToContent.length > 15 ? item.replyToContent.substring(0, 15) + "..." : item.replyToContent;
                  } else {
                    replyToContent = "原评论";
                  }
                }
                if (item.doctors && item.doctors.length > 0) {
                  console.log("📋 检测到@医生信息:", {
                    问题ID: item.id,
                    医生数量: item.doctors.length,
                    医生列表: item.doctors.map((d) => d.realname || d.username || "未知医生")
                  });
                }
                return __spreadProps(__spreadValues({}, item), {
                  // 有 replyToUserId 就显示回复关系
                  replyToName: item.replyToUserId ? item.replyToUserName || "某用户" : null,
                  replyToContent
                });
              });
              let filteredData = processedData || [];
              const originalCount = filteredData.length;
              if (Number(userStore.userInfo.userCategory) === 1) {
                const currentUserId = userStore.userInfo.userid;
                filteredData = filteredData.filter((item) => {
                  const match = item.userId === currentUserId || item.patientId === currentUserId || item.user_id === currentUserId || item.patient_id === currentUserId;
                  return match;
                });
              }
              if (Number(userStore.userInfo.userCategory) === 0 && searchParams.value.patientName && searchParams.value.patientName.trim()) {
                const nameKeyword = searchParams.value.patientName.trim().toLowerCase();
                filteredData = filteredData.filter((item) => {
                  const patientName = (item.patientName || "").toLowerCase();
                  return patientName.includes(nameKeyword);
                });
              }
              if (searchParams.value.question && searchParams.value.question.trim()) {
                const questionKeyword = searchParams.value.question.trim().toLowerCase();
                filteredData = filteredData.filter((item) => {
                  const question = (item.question || "").toLowerCase();
                  return question.includes(questionKeyword);
                });
              }
              if (startDate.value || endDate.value) {
                filteredData = filteredData.filter((item) => {
                  const itemDate = item.createTime || item.updateTime;
                  if (!itemDate)
                    return false;
                  const itemDateStr = itemDate.substring(0, 10);
                  if (startDate.value && itemDateStr < startDate.value) {
                    return false;
                  }
                  if (endDate.value && itemDateStr > endDate.value) {
                    return false;
                  }
                  return true;
                });
              }
              const selectedStatus = getSelectedReplyStatus();
              if (selectedStatus !== "all") {
                filteredData = filteredData.filter((item) => {
                  if (selectedStatus === "pending") {
                    return item.status === 0;
                  } else if (selectedStatus === "replied") {
                    return item.status === 1;
                  }
                  return true;
                });
              }
              if (Number(userStore.userInfo.userCategory) === 0 && showOnlyMentioned.value) {
                const currentUserId = userStore.userInfo.userid;
                filteredData = filteredData.filter((item) => {
                  if (item.doctors && Array.isArray(item.doctors)) {
                    return item.doctors.some(
                      (doctor) => doctor.id === currentUserId || doctor.userId === currentUserId || doctor.user_id === currentUserId
                    );
                  }
                  return false;
                });
                console.log("🔍 @我的问题筛选结果:", {
                  当前医生ID: currentUserId,
                  筛选后数量: filteredData.length,
                  筛选前数量: (processedData == null ? void 0 : processedData.length) || 0
                });
              }
              chatList.value = filteredData;
              if (hasSearchConditions()) {
                common_vendor.index.showToast({
                  title: `找到 ${chatList.value.length} 条记录`,
                  icon: "none",
                  duration: 1500
                });
              }
              resolve(res);
            } else {
              const errorMessage = res.message || "获取沟通记录失败";
              console.error("❌ API返回错误:", {
                success: res.success,
                message: res.message,
                code: res.code,
                result: res.result
              });
              common_vendor.index.showToast({
                title: errorMessage,
                icon: "none",
                duration: 3e3
              });
              if (res.code === 500) {
                console.log("🔄 服务器错误，设置空数据列表");
                chatList.value = [];
                resolve(res);
              } else {
                reject(new Error(errorMessage));
              }
            }
          }).catch((error) => {
            console.error("❌ 网络请求失败:", error);
            let errorMessage = "网络连接失败，请检查网络设置";
            if (error.message) {
              errorMessage = error.message;
            }
            common_vendor.index.showToast({
              title: errorMessage,
              icon: "none",
              duration: 3e3
            });
            chatList.value = [];
            reject(error);
          });
        } catch (error) {
          console.error("❌ fetchChatList 异常:", error);
          common_vendor.index.showToast({
            title: "系统异常，请稍后重试",
            icon: "none",
            duration: 3e3
          });
          chatList.value = [];
          reject(error);
        } finally {
          loadingChat.value = false;
        }
      });
    });
    common_vendor.onMounted(() => __async(this, null, function* () {
      if (Number(userStore.userInfo.userCategory) === 1 && userStore.userInfo.avatar && !userStore.userInfo.avatar.startsWith("http")) {
        const realUrl = yield fetchFileUrl(userStore.userInfo.avatar);
        if (realUrl) {
          userAvatarCache.value = realUrl;
        }
      }
      fetchChatList();
    }));
    common_vendor.onShow(() => {
      fetchChatList();
    });
    function goHome() {
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    }
    function goToChatDetail(id) {
      console.log("跳转到聊天详情，ID:", id);
      common_vendor.index.navigateTo({
        url: `/pages-data/chat/chatDetail?communication_id=${id}`
      });
    }
    function getDisplayAvatar(item) {
      if (Number(userStore.userInfo.userCategory) === 1) {
        const userAvatar = userStore.userInfo.avatar;
        const patientAvatar = item.patientAvatar;
        if (userAvatar) {
          return userAvatar;
        }
        if (patientAvatar) {
          return patientAvatar;
        }
        return "";
      } else {
        return item.patientAvatar || "";
      }
    }
    function getDisplayName(item) {
      if (Number(userStore.userInfo.userCategory) === 1) {
        return userStore.userInfo.realname || userStore.userInfo.username || "我的提问";
      } else {
        return item.patientName || "-";
      }
    }
    function addChat() {
      console.log("发起提问");
      common_vendor.index.navigateTo({
        url: "/pages-data/chat/addChat"
      });
    }
    const isViewMode = common_vendor.ref(false);
    const onPatientNameInput = (e) => {
      const value = e.detail.value;
      if (value.length > 20) {
        searchParams.value.patientName = value.substring(0, 20);
        common_vendor.index.showToast({
          title: "患者姓名最多输入20个字符",
          icon: "none",
          duration: 1500
        });
      } else {
        searchParams.value.patientName = value;
      }
    };
    const onQuestionInput = (e) => {
      const value = e.detail.value;
      if (value.length > 50) {
        searchParams.value.question = value.substring(0, 50);
        common_vendor.index.showToast({
          title: "问题关键字最多输入50个字符",
          icon: "none",
          duration: 1500
        });
      } else {
        searchParams.value.question = value;
      }
    };
    const clearPatientName = () => {
      searchParams.value.patientName = "";
    };
    const clearQuestion = () => {
      searchParams.value.question = "";
    };
    const clearAllSearch = () => {
      startDate.value = "";
      endDate.value = "";
      if (Number(userStore.userInfo.userCategory) === 0) {
        searchParams.value.patientName = "";
      }
      searchParams.value.question = "";
      replyStatusOptions.value.forEach((option) => {
        option.checked = option.value === "all";
      });
      showOnlyMentioned.value = false;
      common_vendor.index.showToast({
        title: "已清空搜索条件",
        icon: "none",
        duration: 1e3
      });
      fetchChatList();
    };
    common_vendor.computed(() => {
      return searchParams.value.patientName.length > 5 ? searchParams.value.patientName.slice(0, 5) + "..." : searchParams.value.patientName;
    });
    const getReplyStatusClass = (status) => {
      switch (status) {
        case 0:
          return "status-pending";
        case 1:
          return "status-replied";
        default:
          return "status-pending";
      }
    };
    const getReplyStatusText = (status) => {
      switch (status) {
        case 0:
          return "待回复";
        case 1:
          return "已回复";
        default:
          return "待回复";
      }
    };
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: Number(common_vendor.unref(userStore).userInfo.userCategory) === 0
      }, Number(common_vendor.unref(userStore).userInfo.userCategory) === 0 ? common_vendor.e$1({
        b: common_vendor.t(startDate.value || "开始日期"),
        c: common_vendor.p({
          type: "calendar",
          size: "15",
          color: "#666666"
        }),
        d: startDate.value,
        e: common_vendor.o(onStartDateChange),
        f: common_vendor.t(endDate.value || "结束日期"),
        g: common_vendor.p({
          type: "calendar",
          size: "15",
          color: "#666666"
        }),
        h: endDate.value,
        i: common_vendor.o(onEndDateChange),
        j: common_vendor.p({
          type: "reload",
          size: "18",
          color: "#07C160"
        }),
        k: isRefreshing.value ? 1 : "",
        l: common_vendor.o(resetDateFilter),
        m: common_vendor.o([($event) => searchParams.value.patientName = $event.detail.value, onPatientNameInput]),
        n: searchParams.value.patientName,
        o: searchParams.value.patientName
      }, searchParams.value.patientName ? {
        p: common_vendor.o(clearPatientName)
      } : {}, {
        q: common_vendor.o([($event) => searchParams.value.question = $event.detail.value, onQuestionInput]),
        r: searchParams.value.question,
        s: searchParams.value.question
      }, searchParams.value.question ? {
        t: common_vendor.o(clearQuestion)
      } : {}, {
        v: common_vendor.f(replyStatusOptions.value, (option, k0, i0) => {
          return common_vendor.e$1({
            a: option.checked
          }, option.checked ? {} : {}, {
            b: option.checked ? 1 : "",
            c: common_vendor.t(option.label),
            d: option.value,
            e: common_vendor.o(($event) => onReplyStatusChange(option.value), option.value)
          });
        }),
        w: showOnlyMentioned.value
      }, showOnlyMentioned.value ? {
        x: common_vendor.p({
          type: "checkmarkempty",
          size: "12",
          color: "#FFFFFF"
        })
      } : {}, {
        y: showOnlyMentioned.value ? 1 : "",
        z: showOnlyMentioned.value ? 1 : "",
        A: common_vendor.o(toggleMentionFilter),
        B: common_vendor.p({
          type: "search",
          size: "16",
          color: "#FFFFFF"
        }),
        C: common_vendor.o(handleSearch),
        D: common_vendor.p({
          type: "clear",
          size: "16",
          color: "#666666"
        }),
        E: common_vendor.o(clearAllSearch)
      }) : {}, {
        F: Number(common_vendor.unref(userStore).userInfo.userCategory) === 0
      }, Number(common_vendor.unref(userStore).userInfo.userCategory) === 0 ? common_vendor.e$1({
        G: loadingChat.value
      }, loadingChat.value ? {} : {}, {
        H: chatList.value.length === 0 && !loadingChat.value
      }, chatList.value.length === 0 && !loadingChat.value ? {} : {}, {
        I: chatList.value.length > 0
      }, chatList.value.length > 0 ? {
        J: common_vendor.f(chatList.value, (item, index, i0) => {
          return common_vendor.e$1({
            a: getAvatarUrl(getDisplayAvatar(item)),
            b: common_vendor.o(handleAvatarError, index),
            c: common_vendor.t(getDisplayName(item)),
            d: item.replyToName
          }, item.replyToName ? {
            e: common_vendor.t(item.replyToName),
            f: common_vendor.t(item.replyToContent || "原消息")
          } : {}, {
            g: common_vendor.t(item.createTime || "-"),
            h: item.title
          }, item.title ? {
            i: common_vendor.t(item.title)
          } : {}, {
            j: item.doctors && item.doctors.length > 0
          }, item.doctors && item.doctors.length > 0 ? {
            k: common_vendor.f(item.doctors, (doctor, doctorIndex, i1) => {
              return {
                a: common_vendor.t(doctor.realname || doctor.username || "医生"),
                b: doctorIndex
              };
            })
          } : {}, {
            l: common_vendor.t(item.question || "-"),
            m: common_vendor.t(getReplyStatusText(item.status)),
            n: common_vendor.n(getReplyStatusClass(item.status)),
            o: index,
            p: common_vendor.o(($event) => goToChatDetail(item.id), index)
          });
        })
      } : {}) : {}, {
        K: Number(common_vendor.unref(userStore).userInfo.userCategory) === 1
      }, Number(common_vendor.unref(userStore).userInfo.userCategory) === 1 ? common_vendor.e$1({
        L: common_vendor.t(startDate.value || "开始日期"),
        M: common_vendor.p({
          type: "calendar",
          size: "15",
          color: "#666666"
        }),
        N: startDate.value,
        O: common_vendor.o(onStartDateChange),
        P: common_vendor.t(endDate.value || "结束日期"),
        Q: common_vendor.p({
          type: "calendar",
          size: "15",
          color: "#666666"
        }),
        R: endDate.value,
        S: common_vendor.o(onEndDateChange),
        T: common_vendor.p({
          type: "reload",
          size: "18",
          color: "#07C160"
        }),
        U: isRefreshing.value ? 1 : "",
        V: common_vendor.o(resetDateFilter),
        W: common_vendor.o([($event) => searchParams.value.question = $event.detail.value, onQuestionInput]),
        X: searchParams.value.question,
        Y: searchParams.value.question
      }, searchParams.value.question ? {
        Z: common_vendor.o(clearQuestion)
      } : {}, {
        aa: common_vendor.f(replyStatusOptions.value, (option, k0, i0) => {
          return common_vendor.e$1({
            a: option.checked
          }, option.checked ? {} : {}, {
            b: option.checked ? 1 : "",
            c: common_vendor.t(option.label),
            d: option.value,
            e: common_vendor.o(($event) => onReplyStatusChange(option.value), option.value)
          });
        }),
        ab: common_vendor.p({
          type: "search",
          size: "16",
          color: "#FFFFFF"
        }),
        ac: common_vendor.o(handleSearch),
        ad: common_vendor.p({
          type: "clear",
          size: "16",
          color: "#666666"
        }),
        ae: common_vendor.o(clearAllSearch),
        af: common_vendor.o(addChat),
        ag: isViewMode.value
      }) : {}, {
        ah: Number(common_vendor.unref(userStore).userInfo.userCategory) === 1
      }, Number(common_vendor.unref(userStore).userInfo.userCategory) === 1 ? common_vendor.e$1({
        ai: loadingChat.value
      }, loadingChat.value ? {} : {}, {
        aj: chatList.value.length === 0 && !loadingChat.value
      }, chatList.value.length === 0 && !loadingChat.value ? {} : {}, {
        ak: chatList.value.length > 0
      }, chatList.value.length > 0 ? {
        al: common_vendor.f(chatList.value, (item, index, i0) => {
          return common_vendor.e$1({
            a: getAvatarUrl(getDisplayAvatar(item)),
            b: common_vendor.o(handleAvatarError, index),
            c: common_vendor.t(getDisplayName(item)),
            d: item.replyToName
          }, item.replyToName ? {
            e: common_vendor.t(item.replyToName),
            f: common_vendor.t(item.replyToContent || "原消息")
          } : {}, {
            g: common_vendor.t(item.createTime || "-"),
            h: item.title
          }, item.title ? {
            i: common_vendor.t(item.title)
          } : {}, {
            j: item.doctors && item.doctors.length > 0
          }, item.doctors && item.doctors.length > 0 ? {
            k: common_vendor.f(item.doctors, (doctor, doctorIndex, i1) => {
              return {
                a: common_vendor.t(doctor.realname || doctor.username || "医生"),
                b: doctorIndex
              };
            })
          } : {}, {
            l: common_vendor.t(item.question || "-"),
            m: common_vendor.t(getReplyStatusText(item.status)),
            n: common_vendor.n(getReplyStatusClass(item.status)),
            o: index,
            p: common_vendor.o(($event) => goToChatDetail(item.id), index)
          });
        })
      } : {}, {
        am: Number(common_vendor.unref(userStore).userInfo.userCategory) === 1 ? 1 : "",
        an: common_vendor.o(goHome)
      }) : {}, {
        ao: common_vendor.sr(calendar, "d9776e99-15,d9776e99-1", {
          "k": "calendar"
        }),
        ap: common_vendor.o(dateConfirm),
        aq: common_vendor.p({
          insert: false,
          range: false
        }),
        ar: common_vendor.p({
          navLeftArrow: true,
          navLeftText: "返回"
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d9776e99"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=chat.js.map
