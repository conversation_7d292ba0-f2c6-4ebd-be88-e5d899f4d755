{"version": 3, "file": "index.js", "sources": ["../../../../../src/uni_modules/da-tree/index.vue", "../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvdW5pX21vZHVsZXMvZGEtdHJlZS9pbmRleC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"da-tree\" :style=\"{'--theme-color': themeColor}\">\r\n    <scroll-view class=\"da-tree-scroll\" :scroll-y=\"true\" :scroll-x=\"false\">\r\n      <view\r\n        class=\"da-tree-item\"\r\n        :class=\"{'is-show': item.show}\"\r\n        :style=\"{paddingLeft: item.level * indent + 'rpx'}\"\r\n        v-for=\"item in datalist\"\r\n        :key=\"item.key\">\r\n        <view\r\n          v-if=\"item.showArrow && !filterValue\"\r\n          class=\"da-tree-item__icon\"\r\n          @click=\"handleExpandedChange(item)\">\r\n          <view :class=\"['da-tree-item__icon--arr','is-loading']\" v-if=\"loadLoading && item.loading\"></view>\r\n          <view :class=\"['da-tree-item__icon--arr','is-expand', {'is-right':!item.expand}]\" v-else></view>\r\n        </view>\r\n        <view v-else class=\"da-tree-item__icon\"></view>\r\n        <view\r\n          class=\"da-tree-item__checkbox\"\r\n          :class=\"[`da-tree-item__checkbox--${checkboxPlacement}`,{'is--disabled': item.disabled}]\"\r\n          v-if=\"showCheckbox\"\r\n          @click=\"handleCheckChange(item)\">\r\n          <view class=\"da-tree-item__checkbox--icon da-tree-checkbox-checked\" v-if=\"item.checkedStatus === isCheckedStatus\"></view>\r\n          <view class=\"da-tree-item__checkbox--icon da-tree-checkbox-indeterminate\" v-else-if=\"item.checkedStatus === halfCheckedStatus\"></view>\r\n          <view class=\"da-tree-item__checkbox--icon da-tree-checkbox-outline\" v-else></view>\r\n        </view>\r\n        <view\r\n          class=\"da-tree-item__checkbox\"\r\n          :class=\"[`da-tree-item__checkbox--${checkboxPlacement}`,{'is--disabled': item.disabled}]\"\r\n          v-if=\"!showCheckbox && showRadioIcon\"\r\n          @click=\"handleRadioChange(item)\">\r\n          <view class=\"da-tree-item__checkbox--icon da-tree-radio-checked\" v-if=\"item.checkedStatus === isCheckedStatus\"></view>\r\n          <view class=\"da-tree-item__checkbox--icon da-tree-radio-indeterminate\" v-else-if=\"item.checkedStatus === halfCheckedStatus\"></view>\r\n          <view class=\"da-tree-item__checkbox--icon da-tree-radio-outline\" v-else></view>\r\n        </view>\r\n        <view class=\"da-tree-item__label\" :class=\"'da-tree-item__label--'+item.checkedStatus\" @click=\"handleLabelClick(item)\">{{ item.label }} <text class=\"da-tree-item__label--append\" v-if=\"item.append\">{{ item.append }}</text></view>\r\n      </view>\r\n    </scroll-view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { defineComponent, ref, unref, watch } from 'vue'\r\n\r\nimport {\r\n  unCheckedStatus,\r\n  halfCheckedStatus,\r\n  isCheckedStatus,\r\n  deepClone,\r\n  getAllNodeKeys,\r\n  getAllNodes,\r\n  logError,\r\n  isArray,\r\n  isString,\r\n  isNumber,\r\n  isFunction,\r\n} from './utils'\r\nimport basicProps from './props'\r\n\r\nexport default defineComponent({\r\n  name: 'DaTree',\r\n  props: basicProps,\r\n  emits: ['change', 'expand'],\r\n  setup(props, { emit }) {\r\n    /** 原始的树数据 */\r\n    const dataRef = ref([])\r\n    /** 处理后的一维树项数据 */\r\n    const datalist = ref([])\r\n    /** 处理后的以key为键值的树项数据 */\r\n    const datamap = ref({})\r\n    /** 默认的展开数据 */\r\n    const expandedKeys = ref([])\r\n    /** 默认的已选数据 */\r\n    const checkedKeys = ref(null)\r\n    /** 加载状态 */\r\n    const loadLoading = ref(false)\r\n    let fieldMap = {\r\n      value: 'value',\r\n      label: 'label',\r\n      children: 'children',\r\n      disabled: 'disabled',\r\n      append: 'append',\r\n      leaf: 'leaf',\r\n      sort: 'sort',\r\n    }\r\n\r\n    /**\r\n     * 初始化数据结构\r\n     */\r\n    function initData() {\r\n      fieldMap = {\r\n        value: props.field?.key || props.field?.value || props.valueField || 'value',\r\n        label: props.field?.label || props.labelField || 'label',\r\n        children: props.field?.children || props.childrenField || 'children',\r\n        disabled: props.field?.disabled || props.disabledField || 'disabled',\r\n        append: props.field?.append || props.appendField || 'append',\r\n        leaf: props.field?.leaf || props.leafField || 'leaf',\r\n        sort: props.field?.sort || props.sortField || 'sort',\r\n      }\r\n\r\n      const data = deepClone(dataRef.value)\r\n      datalist.value = []\r\n      datamap.value = {}\r\n\r\n      // clean tree\r\n      handleTreeData(data)\r\n      // flat tree\r\n      datalist.value = checkInitData(datalist.value)\r\n      // console.log('init datalist', datalist.value)\r\n      // console.log('init datamap', datamap.value)\r\n    }\r\n\r\n    /**\r\n     * 转换为节点数据\r\n     * @param data\r\n     * @param parent\r\n     * @param level\r\n     */\r\n    function handleTreeData(data = [], parent = null, level = 0, insertIndex = -1) {\r\n      return data.reduce((prev, cur, index) => {\r\n        const key = cur[fieldMap.value]\r\n        const children = cur[fieldMap.children] || null\r\n        const newItem = createNewItem(cur, index, parent, level)\r\n        if (insertIndex > -1) {\r\n          // 插入子项尾部\r\n          const index = (parent.childrenKeys?.length || 0) + insertIndex + 1\r\n          if (!parent?.childrenKeys?.includes(key)) {\r\n            datamap.value[key] = newItem\r\n            datalist.value.splice(index, 0, newItem)\r\n            parent.children.push(newItem)\r\n            if (newItem.parentKeys?.length) {\r\n              newItem.parentKeys.forEach(k => {\r\n                datamap.value[k].childrenKeys = [...datamap.value[k].childrenKeys, newItem.key]\r\n              })\r\n            }\r\n          }\r\n        } else {\r\n          datamap.value[key] = newItem\r\n          datalist.value.push(newItem)\r\n        }\r\n\r\n        const hasChildren = children && children.length > 0\r\n        if (hasChildren) {\r\n          const childrenData = handleTreeData(children, newItem, level + 1)\r\n          // childrenData.sort((a, b) => a.sort - b.sort)\r\n          newItem.children = childrenData\r\n          const childrenKeys = childrenData.reduce((p, k) => {\r\n            const keys = k.childrenKeys\r\n            p.push(...keys, k.key)\r\n            return p\r\n          }, [])\r\n          newItem.childrenKeys = childrenKeys\r\n        }\r\n        prev.push(newItem)\r\n        return prev\r\n      }, [])\r\n    }\r\n\r\n    /**\r\n     * 创建节点\r\n     * @param item\r\n     * @param index\r\n     * @param parent\r\n     * @param level\r\n     */\r\n    function createNewItem(item, index, parent, level) {\r\n      const key = item[fieldMap.value]\r\n      const label = item[fieldMap.label]\r\n      const sort = item[fieldMap.sort] || 0\r\n      const children = item[fieldMap.children] || null\r\n      const append = item[fieldMap.append] || null\r\n      let disabled = item[fieldMap.disabled] || false\r\n      // 优先继承父级禁用属性\r\n      disabled = parent?.disabled || disabled\r\n      let isLeaf = isFunction(props.isLeafFn) ? props.isLeafFn(item) : (item[fieldMap.leaf] || false)\r\n      // const hasChildren = children && children.length > 0\r\n      const isEmptyChildren = children && children.length === 0\r\n      let showArrow = true\r\n      // let isLeaf = !hasChildren\r\n      let expand = props.defaultExpandAll || false\r\n      // 是否异步加载模式\r\n      const isLoadMode = props.loadMode && isFunction(props.loadApi)\r\n\r\n      if (!children) {\r\n        expand = false\r\n        if (isLoadMode) {\r\n          showArrow = true\r\n        } else {\r\n          isLeaf = true\r\n          showArrow = false\r\n        }\r\n      }\r\n\r\n      if (isEmptyChildren) {\r\n        expand = false\r\n        if (isLoadMode) {\r\n          showArrow = true\r\n        } else {\r\n          isLeaf = true\r\n          showArrow = false\r\n        }\r\n      }\r\n\r\n      if (isLeaf) {\r\n        showArrow = false\r\n        expand = false\r\n      } else {\r\n        showArrow = true\r\n      }\r\n\r\n      // onlyRadioLeaf 单选只能选择末级节点\r\n      if (!props.showCheckbox) {\r\n        if (props.onlyRadioLeaf) {\r\n          if (!isLeaf) {\r\n            disabled = true\r\n          } else {\r\n            // 仍旧继承父类原始禁用状态\r\n            disabled = parent?.originItem?.disabled || false\r\n          }\r\n        }\r\n      }\r\n\r\n      if (disabled) {\r\n        if (isLeaf || !children || isEmptyChildren) {\r\n          expand = false\r\n          showArrow = false\r\n        }\r\n      }\r\n\r\n      const parentKey = parent ? parent.key : null\r\n      const show = props.defaultExpandAll || level === 0\r\n\r\n      const newItem = {\r\n        key,\r\n        parentKey,\r\n        label,\r\n        append,\r\n        isLeaf,\r\n        showArrow,\r\n        level,\r\n        expand,\r\n        show,\r\n        sort,\r\n        disabled,\r\n        loaded: false,\r\n        loading: false,\r\n        indexs: [index],\r\n        checkedStatus: unCheckedStatus,\r\n        parentKeys: [],\r\n        childrenKeys: [],\r\n        children: [],\r\n        originItem: item,\r\n      }\r\n\r\n      if (parent) {\r\n        newItem.parentKeys = [parent.key, ...parent.parentKeys]\r\n        newItem.indexs = [...parent.indexs, index]\r\n      }\r\n\r\n      return newItem\r\n    }\r\n\r\n    /**\r\n     * 处理初始化内容\r\n     * @param list\r\n     */\r\n    function checkInitData(list) {\r\n      let checkedKeyList = null\r\n      let expandedKeyList = []\r\n      if (props.showCheckbox) {\r\n        checkedKeyList = [...new Set(checkedKeys.value || [])]\r\n        expandedKeyList = props.expandChecked ? ([...(checkedKeys.value || []), ...(expandedKeys.value || [])]) : expandedKeys.value\r\n      } else {\r\n        checkedKeyList = checkedKeys.value || null\r\n        expandedKeyList = props.expandChecked && checkedKeys.value ? ([checkedKeys.value, ...(expandedKeys.value || [])]) : expandedKeys.value\r\n      }\r\n\r\n      handleCheckState(list, checkedKeyList, true)\r\n\r\n      // 处理初始展开\r\n      expandedKeyList = [...new Set(expandedKeyList)]\r\n      if (!props.defaultExpandAll) {\r\n        handleExpandState(list, expandedKeyList, true)\r\n      }\r\n\r\n      list.sort((a, b) => {\r\n        if (a.sort === 0 && b.sort === 0) {\r\n          return 0\r\n        }\r\n\r\n        if (a.parentKey === b.parentKey) {\r\n          if (a.sort - b.sort > 0) {\r\n            return 1\r\n          } else {\r\n            return -1\r\n          }\r\n        }\r\n\r\n        return 0\r\n      })\r\n\r\n      return list\r\n    }\r\n\r\n    /**\r\n     * 处理选中\r\n     * @param list\r\n     * @param checkedKeyList\r\n     */\r\n    function handleCheckState(list, checkedKeyList, checked = true) {\r\n      // 多选\r\n      if (props.showCheckbox) {\r\n        if (checkedKeyList?.length) {\r\n          checkedKeyList.forEach(k => {\r\n            const item = datamap.value[k]\r\n            if (item) {\r\n              checkTheChecked(item, checked)\r\n            }\r\n          })\r\n        }\r\n\r\n        return\r\n      }\r\n\r\n      // 单选\r\n      for (let i = 0; i < list.length; i++) {\r\n        const item = list[i]\r\n        if (item.key === checkedKeyList) {\r\n          checkTheRadio(item, checked)\r\n          break\r\n        }\r\n      }\r\n    }\r\n\r\n    /**\r\n     * 校验多选节点\r\n     * @param item\r\n     * @param checked\r\n     */\r\n    function checkTheChecked(item, checked = true) {\r\n      const { childrenKeys, parentKeys, disabled = false } = item\r\n      if (!props.checkedDisabled && disabled) return\r\n\r\n      // 当前\r\n      item.checkedStatus = checked ? isCheckedStatus : unCheckedStatus\r\n\r\n      if (!props.checkStrictly) {\r\n        // 子类\r\n        childrenKeys.forEach(k => {\r\n          const childrenItem = unref(datamap)[k]\r\n          childrenItem.checkedStatus = (!props.checkedDisabled && childrenItem.disabled) ? childrenItem.checkedStatus : item.checkedStatus\r\n        })\r\n\r\n        // 父类\r\n        parentKeys.forEach(k => {\r\n          const parentItem = datamap.value[k]\r\n          parentItem.checkedStatus = getParentCheckedStatus(parentItem)\r\n        })\r\n      }\r\n    }\r\n\r\n    /**\r\n     * 校验单选节点\r\n     * @param item\r\n     */\r\n    function checkTheRadio(item, checked) {\r\n      const { parentKeys, isLeaf, disabled = false } = item\r\n      if (!props.checkedDisabled && disabled) return\r\n\r\n      // 限制末节点选中，但当前非末节点\r\n      if (props.onlyRadioLeaf && !isLeaf) {\r\n        logError(`限制了末节点选中，当前[${item.label}]非末节点`)\r\n        return\r\n      }\r\n\r\n      if (datalist.value?.length) {\r\n        datalist.value.forEach(k => {\r\n          k.checkedStatus = unCheckedStatus\r\n        })\r\n      }\r\n\r\n      parentKeys.forEach(k => {\r\n        const parentItem = datamap.value[k]\r\n        parentItem.checkedStatus = checked ? getParentCheckedStatus(parentItem) : unCheckedStatus\r\n      })\r\n\r\n      // 当前\r\n      item.checkedStatus = checked ? isCheckedStatus : unCheckedStatus\r\n    }\r\n\r\n    /**\r\n     * 处理父节点展开\r\n     * @param item\r\n     * @param expand\r\n     */\r\n    // function handleExpandParentNode(item, expand = true) {\r\n    //   if (!expand) return\r\n\r\n    //   if (item?.parentKeys?.length) {\r\n    //     item.parentKeys.forEach(pk => {\r\n    //       if (!datamap.value[pk].expand) {\r\n    //         datamap.value[pk].expand = true\r\n    //       }\r\n    //     })\r\n    //   }\r\n    // }\r\n\r\n    /**\r\n     * 处理节点展开\r\n     * @param list\r\n     * @param expandedKeyList\r\n     * @param expand\r\n     */\r\n    function handleExpandState(list, expandedKeyList, expand = true) {\r\n      // 收起\r\n      if (expand === false) {\r\n        for (let i = 0; i < list.length; i++) {\r\n          const item = list[i]\r\n          if (expandedKeyList?.includes(item.key)) {\r\n            item.expand = false\r\n            if (item.childrenKeys?.length) {\r\n              item.childrenKeys.forEach(ck => {\r\n                datamap.value[ck].expand = false\r\n                datamap.value[ck].show = false\r\n              })\r\n            }\r\n          }\r\n        }\r\n        return\r\n      }\r\n      // 展开\r\n      for (let i = 0; i < list.length; i++) {\r\n        const item = list[i]\r\n        // 处理展开\r\n        if (expandedKeyList?.includes(item.key)) {\r\n          // 父子\r\n          item.expand = true\r\n          if (item.children?.length) {\r\n            item.children.forEach(k => {\r\n              const kItem = unref(datamap)[k.key]\r\n              kItem.show = true\r\n            })\r\n          }\r\n\r\n          // 族系\r\n          if (item.parentKeys?.length) {\r\n            item.parentKeys.forEach(k => {\r\n              const kItem = unref(datamap)[k]\r\n              kItem.expand = true\r\n              if (kItem.children?.length) {\r\n                kItem.children.forEach(k => {\r\n                  const skItem = unref(datamap)[k.key]\r\n                  skItem.show = true\r\n                })\r\n              }\r\n            })\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    /**\r\n     * 点击选框\r\n     * @param item\r\n     */\r\n    function handleCheckChange(item) {\r\n      const { childrenKeys, parentKeys, checkedStatus, isLeaf, disabled = false } = item\r\n      if (!props.showCheckbox) return\r\n      if (disabled) return\r\n\r\n      // 当前\r\n      item.checkedStatus = checkedStatus === isCheckedStatus ? unCheckedStatus : isCheckedStatus\r\n\r\n      // 子类\r\n      if (!props.checkStrictly) {\r\n        if (props.expandChecked) {\r\n          item.show = true\r\n          item.expand = childrenKeys?.length > 0 || isLeaf\r\n        }\r\n\r\n        childrenKeys.forEach(k => {\r\n          const childrenItem = unref(datamap)[k]\r\n          childrenItem.checkedStatus = childrenItem.disabled ? childrenItem.checkedStatus : item.checkedStatus\r\n\r\n          if (props.expandChecked) {\r\n            childrenItem.show = true\r\n            childrenItem.expand = childrenItem?.childrenKeys?.length > 0 || childrenItem.isLeaf\r\n          }\r\n        })\r\n      } else {\r\n        if (props.expandChecked) {\r\n          logError(`多选时，当 checkStrictly 为 true 时，不支持选择自动展开子节点属性(expandChecked)`)\r\n        }\r\n      }\r\n\r\n      // 父类\r\n      if (!props.checkStrictly) {\r\n        parentKeys.forEach(k => {\r\n          const parentItem = datamap.value[k]\r\n          parentItem.checkedStatus = getParentCheckedStatus(parentItem)\r\n        })\r\n      }\r\n\r\n      const hasCheckedKeys = []\r\n      for (let i = 0; i < datalist.value.length; i++) {\r\n        const k = datalist.value[i]\r\n        if (k.checkedStatus === isCheckedStatus) {\r\n          if ((props.packDisabledkey && k.disabled) || !k.disabled) {\r\n            hasCheckedKeys.push(k.key)\r\n          }\r\n        }\r\n      }\r\n\r\n      checkedKeys.value = [...hasCheckedKeys]\r\n\r\n      emit('change', hasCheckedKeys, item)\r\n    }\r\n\r\n    /**\r\n     * 点击单选\r\n     * @param item\r\n     */\r\n    function handleRadioChange(item) {\r\n      const { parentKeys, checkedStatus, key, disabled = false, isLeaf } = item\r\n      if (props.showCheckbox) return\r\n      if (props.onlyRadioLeaf && !isLeaf) handleExpandedChange(item)\r\n\r\n      if (disabled) return\r\n\r\n      // 重置所有选择\r\n      if (datalist.value?.length) {\r\n        for (let i = 0; i < datalist.value.length; i++) {\r\n          const k = datalist.value[i]\r\n          k.checkedStatus = unCheckedStatus\r\n        }\r\n      }\r\n\r\n      parentKeys.forEach(k => {\r\n        const parentItem = datamap.value[k]\r\n        parentItem.checkedStatus = getParentCheckedStatus(parentItem)\r\n      })\r\n\r\n      // 当前\r\n      item.checkedStatus = checkedStatus === isCheckedStatus ? unCheckedStatus : isCheckedStatus\r\n\r\n      checkedKeys.value = key\r\n      emit('change', key, item)\r\n    }\r\n\r\n    /**\r\n     * 点击标签\r\n     */\r\n    function handleLabelClick(item) {\r\n      if (props.showCheckbox) {\r\n        handleCheckChange(item)\r\n      } else {\r\n        handleRadioChange(item)\r\n      }\r\n    }\r\n\r\n    /**\r\n     * 点击展开收起\r\n     * @param item\r\n     */\r\n    async function handleExpandedChange(item) {\r\n      if (props.filterValue) return\r\n\r\n      const { expand, loading = false, disabled } = item\r\n      if (loadLoading.value && loading) return\r\n\r\n      checkExpandedChange(item)\r\n\r\n      // 异步\r\n      item.expand = !expand\r\n\r\n      let currentItem = null\r\n      if (!disabled) {\r\n        if (!props.showCheckbox && props.onlyRadioLeaf && props.loadMode) {\r\n          logError(`单选时，当 onlyRadioLeaf 为 true 时不支持动态数据`)\r\n        } else {\r\n          currentItem = await loadExpandNode(item)\r\n        }\r\n      }\r\n\r\n      emit('expand', !expand, currentItem || item || null)\r\n    }\r\n\r\n    /**\r\n     * 检查展开状态\r\n     * @param item\r\n     */\r\n    function checkExpandedChange(item) {\r\n      const { expand, childrenKeys, children = null } = item\r\n\r\n      if (expand) {\r\n        if (childrenKeys?.length) {\r\n          childrenKeys.forEach(k => {\r\n            if (unref(datamap)[k]) {\r\n              unref(datamap)[k].show = false\r\n              unref(datamap)[k].expand = false\r\n            }\r\n          })\r\n        }\r\n      } else {\r\n        if (children?.length) {\r\n          const childrenKeys = children.map(k => k.key)\r\n          childrenKeys.forEach(k => {\r\n            if (unref(datamap)[k]) {\r\n              unref(datamap)[k].show = true\r\n            }\r\n          })\r\n        }\r\n      }\r\n    }\r\n\r\n    /**\r\n     * 加载异步数据\r\n     * @param item\r\n     */\r\n    async function loadExpandNode(item) {\r\n      const { expand, key, loaded, children } = item\r\n      if (children?.length && !props.alwaysFirstLoad) {\r\n        return item\r\n      }\r\n\r\n      if (expand && props.loadMode && !loaded) {\r\n        if (isFunction(props.loadApi)) {\r\n          expandedKeys.value.push(key)\r\n          loadLoading.value = true\r\n          item.loading = true\r\n\r\n          const currentNode = deepClone(item)\r\n          const apiRes = await props.loadApi(currentNode)\r\n\r\n          // 新增子项\r\n          let newChildren = [...(item.originItem?.children || []), ...(apiRes || [])]\r\n          const newChildrenObj = {}\r\n          newChildren = newChildren.reduce((total, next) => {\r\n            newChildrenObj[next[fieldMap.value]] ? '' : newChildrenObj[next[fieldMap.value]] = true && total.push(next)\r\n            return total\r\n          }, [])\r\n\r\n          item.originItem.children = newChildren || null\r\n          if (apiRes?.length) {\r\n            const insertIndex = datalist.value.findIndex(k => k.key === item.key)\r\n            handleTreeData(apiRes, item, item.level + 1, insertIndex)\r\n            datalist.value = checkInitData(datalist.value)\r\n          } else {\r\n            // 加载后无数据就移除展开图标\r\n            item.expand = false\r\n            item.isLeaf = true\r\n            item.showArrow = false\r\n          }\r\n\r\n          loadLoading.value = false\r\n          item.loading = false\r\n          item.loaded = true\r\n        }\r\n      } else {\r\n        const eki = expandedKeys.value.findIndex(k => k === key)\r\n        if (eki >= 0) {\r\n          expandedKeys.value.splice(eki, 1)\r\n        }\r\n      }\r\n\r\n      return item\r\n    }\r\n\r\n    /**\r\n     * 获取父类的选中状态\r\n     * @param item\r\n     */\r\n    function getParentCheckedStatus(item) {\r\n      if (!item) {\r\n        return unCheckedStatus\r\n      }\r\n\r\n      if (!props.checkedDisabled && item.disabled) {\r\n        return item.checkedStatus || unCheckedStatus\r\n      }\r\n\r\n      // 单选时，父类永远为半选\r\n      if (!props.showCheckbox) {\r\n        return halfCheckedStatus\r\n      }\r\n\r\n      const { children } = item\r\n      // 子类全选中\r\n      const childrenCheckedAll = children.every(k => k.checkedStatus === isCheckedStatus)\r\n      if (childrenCheckedAll) {\r\n        return isCheckedStatus\r\n      }\r\n\r\n      // 子类全不选中\r\n      const childrenUncheckedAll = children.every(k => k.checkedStatus === unCheckedStatus)\r\n      if (childrenUncheckedAll) {\r\n        return unCheckedStatus\r\n      }\r\n\r\n      return halfCheckedStatus\r\n    }\r\n\r\n    function filterData() {\r\n      if (props.filterValue === '') {\r\n        datalist.value.forEach(k => {\r\n          k.show = true\r\n        })\r\n        return\r\n      }\r\n\r\n      datalist.value.forEach(k => {\r\n        if (k.label.indexOf(props.filterValue) > -1) {\r\n          k.show = true\r\n          k.parentKeys.forEach(k => {\r\n            datamap.value[k].show = true\r\n          })\r\n        } else {\r\n          k.show = false\r\n        }\r\n      })\r\n\r\n      datalist.value.forEach(k => {\r\n        if (k.show) {\r\n          k.parentKeys.forEach(k => {\r\n            datamap.value[k].show = true\r\n          })\r\n        }\r\n      })\r\n    }\r\n\r\n    /**\r\n     * 返回已选的 key\r\n     */\r\n    const getCheckedKeys = () => getAllNodeKeys(datalist.value, 'checkedStatus', isCheckedStatus, props.packDisabledkey)\r\n    /**\r\n     * 根据key设置已选\r\n     * @param keys 单选时为数字或者字符串，多选时为数组\r\n     * @param checked 多选时为key的数组，单选时为key\r\n     */\r\n    function setCheckedKeys(keys, checked = true) {\r\n      // 多选\r\n      if (props.showCheckbox) {\r\n        if (!isArray(keys)) {\r\n          logError(`setCheckedKeys 第一个参数非数组，传入的是[${keys}]`)\r\n          return\r\n        }\r\n\r\n        const list = datalist.value\r\n\r\n        // 取消选择\r\n        if (checked === false) {\r\n          let newCheckedKeys = []\r\n          for (let i = 0; i < checkedKeys.value.length; i++) {\r\n            const ck = checkedKeys.value[i]\r\n            if (!keys.includes(ck)) {\r\n              newCheckedKeys.push(ck)\r\n            }\r\n          }\r\n          newCheckedKeys = [...new Set(newCheckedKeys)]\r\n          checkedKeys.value = newCheckedKeys\r\n          handleCheckState(list, keys, false)\r\n\r\n          return\r\n        }\r\n\r\n        // 选择\r\n        const newCheckedKeys = [...checkedKeys.value, ...keys]\r\n        checkedKeys.value = [...new Set(newCheckedKeys)]\r\n        handleCheckState(list, checkedKeys.value, true)\r\n\r\n        if (props.expandChecked && checked) {\r\n          expandedKeys.value = [...new Set([...(checkedKeys.value || []), ...(keys || [])])]\r\n          handleExpandState(list, keys, true)\r\n        }\r\n        return\r\n      }\r\n\r\n      // 单选\r\n      // 如果为数组则拿第一个\r\n      if (isArray(keys)) {\r\n        keys = keys[0]\r\n      }\r\n\r\n      if (!isString(keys) && !isNumber(keys)) {\r\n        logError('setCheckedKeys 第一个参数字符串或数字，传入的是==>', keys)\r\n        return\r\n      }\r\n\r\n      const list = datalist.value\r\n      checkedKeys.value = checked ? keys : null\r\n\r\n      if (props.expandChecked && checked) {\r\n        handleExpandState(list, [keys], true)\r\n      }\r\n\r\n      handleCheckState(list, keys, !!checked)\r\n    }\r\n    /**\r\n     * 返回半选的 key\r\n     */\r\n    const getHalfCheckedKeys = () => getAllNodeKeys(datalist.value, 'checkedStatus', halfCheckedStatus, props.packDisabledkey)\r\n    /**\r\n     * 返回未选的 key\r\n     */\r\n    const getUncheckedKeys = () => getAllNodeKeys(datalist.value, 'checkedStatus', unCheckedStatus, props.packDisabledkey)\r\n    /**\r\n     * 返回已展开的 key\r\n     */\r\n    const getExpandedKeys = () => getAllNodeKeys(datalist.value, 'expand', true)\r\n    /**\r\n     * 返回未展开的 key\r\n     */\r\n    const getUnexpandedKeys = () => getAllNodeKeys(datalist.value, 'expand', false)\r\n    /**\r\n     * 根据key展开/收起\r\n     *\r\n     * @param keys 数组，或字符串 all\r\n     * @param expand true为展开/false为收起\r\n     */\r\n    function setExpandedKeys(keys, expand = true) {\r\n      if (!Array.isArray(keys) && keys !== 'all') {\r\n        logError('setExpandedKeys 第一个参数非数组，传入的是===>', keys)\r\n        return\r\n      }\r\n\r\n      const list = datalist.value\r\n\r\n      // 展开/收起全部\r\n      if (keys === 'all') {\r\n        list.forEach(k => {\r\n          k.expand = expand\r\n          if (k.level > 0) {\r\n            k.show = expand\r\n          }\r\n        })\r\n        return\r\n      }\r\n\r\n      // 收起\r\n      if (expand === false) {\r\n        const newExpandedKeys = []\r\n        for (let i = 0; i < expandedKeys.value.length; i++) {\r\n          const ek = expandedKeys.value[i]\r\n          if (!keys.includes(ek)) {\r\n            newExpandedKeys.push(ek)\r\n          }\r\n        }\r\n        expandedKeys.value = [...new Set(newExpandedKeys)]\r\n        handleExpandState(list, keys, false)\r\n\r\n        return\r\n      }\r\n\r\n      // 展开\r\n      const newExpandedKeys = []\r\n      for (let i = 0; i < list.length; i++) {\r\n        if (keys.includes(list[i].key)) {\r\n          newExpandedKeys.push(list[i].key)\r\n        }\r\n      }\r\n      expandedKeys.value = [...new Set(newExpandedKeys)]\r\n      handleExpandState(list, newExpandedKeys, true)\r\n    }\r\n    /**\r\n     * 返回已选的节点\r\n     */\r\n    const getCheckedNodes = () => getAllNodes(datalist.value, 'checkedStatus', isCheckedStatus, props.packDisabledkey)\r\n    /**\r\n     * 返回半选的节点\r\n     */\r\n    const getHalfCheckedNodes = () => getAllNodes(datalist.value, 'checkedStatus', halfCheckedStatus, props.packDisabledkey)\r\n    /**\r\n     * 返回未选的节点\r\n     */\r\n    const getUncheckedNodes = () => getAllNodes(datalist.value, 'checkedStatus', unCheckedStatus, props.packDisabledkey)\r\n    /**\r\n     * 返回已展开的节点\r\n     */\r\n    const getExpandedNodes = () => getAllNodes(datalist.value, 'expand', true)\r\n    /**\r\n     * 返回未展开的节点\r\n     */\r\n    const getUnexpandedNodes = () => getAllNodes(datalist.value, 'expand', false)\r\n\r\n    watch(\r\n      () => props.defaultExpandedKeys,\r\n      (v) => {\r\n        if (v?.length) {\r\n          expandedKeys.value = v\r\n        } else {\r\n          expandedKeys.value = []\r\n        }\r\n\r\n        // if (v) checkInitData(datalist.value)\r\n      },\r\n      { immediate: true }\r\n    )\r\n\r\n    watch(\r\n      () => props.defaultCheckedKeys,\r\n      (v) => {\r\n        if (props.showCheckbox) {\r\n          if (v?.length) {\r\n            checkedKeys.value = v\r\n          } else {\r\n            checkedKeys.value = []\r\n          }\r\n        } else {\r\n          if (v || v === 0) {\r\n            checkedKeys.value = v\r\n          } else {\r\n            checkedKeys.value = null\r\n          }\r\n        }\r\n        // checkInitData(datalist.value)\r\n      },\r\n      { immediate: true }\r\n    )\r\n\r\n    watch(\r\n      () => props.data,\r\n      (v) => {\r\n        dataRef.value = deepClone(v)\r\n        setTimeout(() => {\r\n          initData()\r\n        }, 36)\r\n      },\r\n      { immediate: true, deep: true }\r\n    )\r\n\r\n    watch(\r\n      () => props.filterValue,\r\n      () => {\r\n        filterData()\r\n      },\r\n    )\r\n\r\n    return {\r\n      datalist,\r\n      unCheckedStatus,\r\n      halfCheckedStatus,\r\n      isCheckedStatus,\r\n      handleCheckChange,\r\n      handleRadioChange,\r\n      handleLabelClick,\r\n      handleExpandedChange,\r\n      loadLoading,\r\n\r\n      // updateChildrenByKey: () => {},\r\n      // insertBeforeByKey: () => {},\r\n      // insertAfterByKey: () => {},\r\n      getCheckedKeys,\r\n      setCheckedKeys,\r\n      getHalfCheckedKeys,\r\n      getUncheckedKeys,\r\n      getExpandedKeys,\r\n      getUnexpandedKeys,\r\n      setExpandedKeys,\r\n      getCheckedNodes,\r\n      getHalfCheckedNodes,\r\n      getUncheckedNodes,\r\n      getExpandedNodes,\r\n      getUnexpandedNodes,\r\n    }\r\n  },\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@font-face {\r\n  font-family: 'da-tree-iconfont'; /* Project id  */\r\n  src: url('data:application/octet-stream;base64,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') format('truetype');\r\n}\r\n\r\n.da-tree {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  &-scroll {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n\r\n  &-item {\r\n    display: flex;\r\n    align-items: center;\r\n    height: 0;\r\n    padding: 0;\r\n    overflow: hidden;\r\n    font-size: 28rpx;\r\n    line-height: 1;\r\n    visibility: hidden;\r\n    opacity: 0;\r\n    transition: opacity 0.2s linear;\r\n\r\n    &.is-show {\r\n      height: auto;\r\n      padding: 12rpx 24rpx;\r\n      visibility: visible;\r\n      opacity: 1;\r\n    }\r\n\r\n    &__icon {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      width: 40rpx;\r\n      height: 40rpx;\r\n      overflow: hidden;\r\n\r\n      &--arr {\r\n        position: relative;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        width: 32rpx;\r\n        height: 32rpx;\r\n\r\n        &::after {\r\n          position: relative;\r\n          z-index: 1;\r\n          overflow: hidden;\r\n          /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */\r\n          font-family: 'da-tree-iconfont' !important;\r\n          font-size: 32rpx;\r\n          font-style: normal;\r\n          color: #999;\r\n          -webkit-font-smoothing: antialiased;\r\n          -moz-osx-font-smoothing: grayscale;\r\n        }\r\n\r\n        &.is-expand {\r\n          &::after {\r\n            content: '\\e604';\r\n          }\r\n        }\r\n\r\n        &.is-right {\r\n          transform: rotate(-90deg);\r\n        }\r\n\r\n        &.is-loading {\r\n          animation: IconLoading 1s linear 0s infinite;\r\n\r\n          &::after {\r\n            content: '\\e7f1';\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    &__checkbox {\r\n      width: 40rpx;\r\n      height: 40rpx;\r\n      overflow: hidden;\r\n\r\n      &--left {\r\n        order: 0;\r\n      }\r\n\r\n      &--right {\r\n        order: 1;\r\n      }\r\n\r\n      &--icon {\r\n        position: relative;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        width: 40rpx;\r\n        height: 40rpx;\r\n\r\n        &::after {\r\n          position: relative;\r\n          top: 0;\r\n          left: 0;\r\n          z-index: 1;\r\n          overflow: hidden;\r\n          /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */\r\n          font-family: 'da-tree-iconfont' !important;\r\n          font-size: 32rpx;\r\n          font-style: normal;\r\n          -webkit-font-smoothing: antialiased;\r\n          -moz-osx-font-smoothing: grayscale;\r\n        }\r\n\r\n        &.da-tree-checkbox-outline::after {\r\n          color: #bbb;\r\n          content: '\\ead5';\r\n        }\r\n\r\n        &.da-tree-checkbox-checked::after {\r\n          color: var(--theme-color,#007aff);\r\n          content: '\\ead4';\r\n        }\r\n\r\n        &.da-tree-checkbox-indeterminate::after {\r\n          color: var(--theme-color,#007aff);\r\n          content: '\\ebce';\r\n        }\r\n\r\n        &.da-tree-radio-outline::after {\r\n          color: #bbb;\r\n          content: '\\ecc5';\r\n        }\r\n\r\n        &.da-tree-radio-checked::after {\r\n          color: var(--theme-color,#007aff);\r\n          content: '\\ecc4';\r\n        }\r\n\r\n        &.da-tree-radio-indeterminate::after {\r\n          color: var(--theme-color,#007aff);\r\n          content: '\\ea4f';\r\n        }\r\n      }\r\n\r\n      &.is--disabled {\r\n        cursor: not-allowed;\r\n        opacity: 0.35;\r\n      }\r\n    }\r\n\r\n    &__label {\r\n      flex: 1;\r\n      margin-left: 4rpx;\r\n      color: #555;\r\n\r\n      &--2 {\r\n        color: var(--theme-color,#007aff);\r\n      }\r\n\r\n      &--append {\r\n        font-size: 60%;\r\n        opacity: 0.6;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes IconLoading {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/uni_modules/da-tree/index.vue'\nwx.createComponent(Component)"], "names": ["defineComponent", "basicProps", "ref", "deepClone", "index", "isFunction", "unCheckedStatus", "isCheckedStatus", "unref", "logError", "_a", "k", "children<PERSON>eys", "halfCheckedStatus", "getAllNodeKeys", "isArray", "list", "newCheckedKeys", "isString", "isNumber", "newExpandedKeys", "getAllNodes", "watch"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AA4DA,MAAK,YAAaA,cAAAA,gBAAa;AAAA,EAC7B,MAAM;AAAA,EACN,OAAOC,yBAAU;AAAA,EACjB,OAAO,CAAC,UAAU,QAAQ;AAAA,EAC1B,MAAM,OAAO,EAAE,QAAQ;AAErB,UAAM,UAAUC,cAAG,IAAC,EAAE;AAEtB,UAAM,WAAWA,cAAG,IAAC,EAAE;AAEvB,UAAM,UAAUA,cAAG,IAAC,EAAE;AAEtB,UAAM,eAAeA,cAAG,IAAC,EAAE;AAE3B,UAAM,cAAcA,cAAG,IAAC,IAAI;AAE5B,UAAM,cAAcA,cAAG,IAAC,KAAK;AAC7B,QAAI,WAAW;AAAA,MACb,OAAO;AAAA,MACP,OAAO;AAAA,MACP,UAAU;AAAA,MACV,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,MAAM;AAAA,IACR;AAKA,aAAS,WAAW;;AAClB,iBAAW;AAAA,QACT,SAAO,WAAM,UAAN,mBAAa,UAAO,WAAM,UAAN,mBAAa,UAAS,MAAM,cAAc;AAAA,QACrE,SAAO,WAAM,UAAN,mBAAa,UAAS,MAAM,cAAc;AAAA,QACjD,YAAU,WAAM,UAAN,mBAAa,aAAY,MAAM,iBAAiB;AAAA,QAC1D,YAAU,WAAM,UAAN,mBAAa,aAAY,MAAM,iBAAiB;AAAA,QAC1D,UAAQ,WAAM,UAAN,mBAAa,WAAU,MAAM,eAAe;AAAA,QACpD,QAAM,WAAM,UAAN,mBAAa,SAAQ,MAAM,aAAa;AAAA,QAC9C,QAAM,WAAM,UAAN,mBAAa,SAAQ,MAAM,aAAa;AAAA,MAChD;AAEA,YAAM,OAAOC,yBAAAA,UAAU,QAAQ,KAAK;AACpC,eAAS,QAAQ,CAAC;AAClB,cAAQ,QAAQ,CAAC;AAGjB,qBAAe,IAAI;AAEnB,eAAS,QAAQ,cAAc,SAAS,KAAK;AAAA,IAG/C;AAQA,aAAS,eAAe,OAAO,CAAA,GAAI,SAAS,MAAM,QAAQ,GAAG,cAAc,IAAI;AAC7E,aAAO,KAAK,OAAO,CAAC,MAAM,KAAK,UAAU;;AACvC,cAAM,MAAM,IAAI,SAAS,KAAK;AAC9B,cAAM,WAAW,IAAI,SAAS,QAAQ,KAAK;AAC3C,cAAM,UAAU,cAAc,KAAK,OAAO,QAAQ,KAAK;AACvD,YAAI,cAAc,IAAI;AAEpB,gBAAMC,YAAS,YAAO,iBAAP,mBAAqB,WAAU,KAAK,cAAc;AACjE,cAAI,GAAC,sCAAQ,iBAAR,mBAAsB,SAAS,OAAM;AACxC,oBAAQ,MAAM,GAAG,IAAI;AACrB,qBAAS,MAAM,OAAOA,QAAO,GAAG,OAAO;AACvC,mBAAO,SAAS,KAAK,OAAO;AAC5B,iBAAI,aAAQ,eAAR,mBAAoB,QAAQ;AAC9B,sBAAQ,WAAW,QAAQ,OAAK;AAC9B,wBAAQ,MAAM,CAAC,EAAE,eAAe,CAAC,GAAG,QAAQ,MAAM,CAAC,EAAE,cAAc,QAAQ,GAAG;AAAA,eAC/E;AAAA,YACH;AAAA,UACF;AAAA,eACK;AACL,kBAAQ,MAAM,GAAG,IAAI;AACrB,mBAAS,MAAM,KAAK,OAAO;AAAA,QAC7B;AAEA,cAAM,cAAc,YAAY,SAAS,SAAS;AAClD,YAAI,aAAa;AACf,gBAAM,eAAe,eAAe,UAAU,SAAS,QAAQ,CAAC;AAEhE,kBAAQ,WAAW;AACnB,gBAAM,eAAe,aAAa,OAAO,CAAC,GAAG,MAAM;AACjD,kBAAM,OAAO,EAAE;AACf,cAAE,KAAK,GAAG,MAAM,EAAE,GAAG;AACrB,mBAAO;AAAA,UACR,GAAE,EAAE;AACL,kBAAQ,eAAe;AAAA,QACzB;AACA,aAAK,KAAK,OAAO;AACjB,eAAO;AAAA,MACR,GAAE,EAAE;AAAA,IACP;AASA,aAAS,cAAc,MAAM,OAAO,QAAQ,OAAO;;AACjD,YAAM,MAAM,KAAK,SAAS,KAAK;AAC/B,YAAM,QAAQ,KAAK,SAAS,KAAK;AACjC,YAAM,OAAO,KAAK,SAAS,IAAI,KAAK;AACpC,YAAM,WAAW,KAAK,SAAS,QAAQ,KAAK;AAC5C,YAAM,SAAS,KAAK,SAAS,MAAM,KAAK;AACxC,UAAI,WAAW,KAAK,SAAS,QAAQ,KAAK;AAE1C,kBAAW,iCAAQ,aAAY;AAC/B,UAAI,SAASC,yBAAU,WAAC,MAAM,QAAQ,IAAI,MAAM,SAAS,IAAI,IAAK,KAAK,SAAS,IAAI,KAAK;AAEzF,YAAM,kBAAkB,YAAY,SAAS,WAAW;AACxD,UAAI,YAAY;AAEhB,UAAI,SAAS,MAAM,oBAAoB;AAEvC,YAAM,aAAa,MAAM,YAAYA,yBAAAA,WAAW,MAAM,OAAO;AAE7D,UAAI,CAAC,UAAU;AACb,iBAAS;AACT,YAAI,YAAY;AACd,sBAAY;AAAA,eACP;AACL,mBAAS;AACT,sBAAY;AAAA,QACd;AAAA,MACF;AAEA,UAAI,iBAAiB;AACnB,iBAAS;AACT,YAAI,YAAY;AACd,sBAAY;AAAA,eACP;AACL,mBAAS;AACT,sBAAY;AAAA,QACd;AAAA,MACF;AAEA,UAAI,QAAQ;AACV,oBAAY;AACZ,iBAAS;AAAA,aACJ;AACL,oBAAY;AAAA,MACd;AAGA,UAAI,CAAC,MAAM,cAAc;AACvB,YAAI,MAAM,eAAe;AACvB,cAAI,CAAC,QAAQ;AACX,uBAAW;AAAA,iBACN;AAEL,yBAAW,sCAAQ,eAAR,mBAAoB,aAAY;AAAA,UAC7C;AAAA,QACF;AAAA,MACF;AAEA,UAAI,UAAU;AACZ,YAAI,UAAU,CAAC,YAAY,iBAAiB;AAC1C,mBAAS;AACT,sBAAY;AAAA,QACd;AAAA,MACF;AAEA,YAAM,YAAY,SAAS,OAAO,MAAM;AACxC,YAAM,OAAO,MAAM,oBAAoB,UAAU;AAEjD,YAAM,UAAU;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ,CAAC,KAAK;AAAA,QACd,eAAeC,yBAAe;AAAA,QAC9B,YAAY,CAAE;AAAA,QACd,cAAc,CAAE;AAAA,QAChB,UAAU,CAAE;AAAA,QACZ,YAAY;AAAA,MACd;AAEA,UAAI,QAAQ;AACV,gBAAQ,aAAa,CAAC,OAAO,KAAK,GAAG,OAAO,UAAU;AACtD,gBAAQ,SAAS,CAAC,GAAG,OAAO,QAAQ,KAAK;AAAA,MAC3C;AAEA,aAAO;AAAA,IACT;AAMA,aAAS,cAAc,MAAM;AAC3B,UAAI,iBAAiB;AACrB,UAAI,kBAAkB,CAAC;AACvB,UAAI,MAAM,cAAc;AACtB,yBAAiB,CAAC,GAAG,IAAI,IAAI,YAAY,SAAS,CAAA,CAAE,CAAC;AACrD,0BAAkB,MAAM,gBAAiB,CAAC,GAAI,YAAY,SAAS,CAAA,GAAK,GAAI,aAAa,SAAS,CAAE,CAAC,IAAK,aAAa;AAAA,aAClH;AACL,yBAAiB,YAAY,SAAS;AACtC,0BAAkB,MAAM,iBAAiB,YAAY,QAAS,CAAC,YAAY,OAAO,GAAI,aAAa,SAAS,CAAE,CAAC,IAAK,aAAa;AAAA,MACnI;AAEA,uBAAiB,MAAM,gBAAgB,IAAI;AAG3C,wBAAkB,CAAC,GAAG,IAAI,IAAI,eAAe,CAAC;AAC9C,UAAI,CAAC,MAAM,kBAAkB;AAC3B,0BAAkB,MAAM,iBAAiB,IAAI;AAAA,MAC/C;AAEA,WAAK,KAAK,CAAC,GAAG,MAAM;AAClB,YAAI,EAAE,SAAS,KAAK,EAAE,SAAS,GAAG;AAChC,iBAAO;AAAA,QACT;AAEA,YAAI,EAAE,cAAc,EAAE,WAAW;AAC/B,cAAI,EAAE,OAAO,EAAE,OAAO,GAAG;AACvB,mBAAO;AAAA,iBACF;AACL,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,eAAO;AAAA,OACR;AAED,aAAO;AAAA,IACT;AAOA,aAAS,iBAAiB,MAAM,gBAAgB,UAAU,MAAM;AAE9D,UAAI,MAAM,cAAc;AACtB,YAAI,iDAAgB,QAAQ;AAC1B,yBAAe,QAAQ,OAAK;AAC1B,kBAAM,OAAO,QAAQ,MAAM,CAAC;AAC5B,gBAAI,MAAM;AACR,8BAAgB,MAAM,OAAO;AAAA,YAC/B;AAAA,WACD;AAAA,QACH;AAEA;AAAA,MACF;AAGA,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAM,OAAO,KAAK,CAAC;AACnB,YAAI,KAAK,QAAQ,gBAAgB;AAC/B,wBAAc,MAAM,OAAO;AAC3B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAOA,aAAS,gBAAgB,MAAM,UAAU,MAAM;AAC7C,YAAM,EAAE,cAAc,YAAY,WAAW,MAAQ,IAAE;AACvD,UAAI,CAAC,MAAM,mBAAmB;AAAU;AAGxC,WAAK,gBAAgB,UAAUC,yBAAAA,kBAAkBD,yBAAc;AAE/D,UAAI,CAAC,MAAM,eAAe;AAExB,qBAAa,QAAQ,OAAK;AACxB,gBAAM,eAAeE,cAAAA,MAAM,OAAO,EAAE,CAAC;AACrC,uBAAa,gBAAiB,CAAC,MAAM,mBAAmB,aAAa,WAAY,aAAa,gBAAgB,KAAK;AAAA,SACpH;AAGD,mBAAW,QAAQ,OAAK;AACtB,gBAAM,aAAa,QAAQ,MAAM,CAAC;AAClC,qBAAW,gBAAgB,uBAAuB,UAAU;AAAA,SAC7D;AAAA,MACH;AAAA,IACF;AAMA,aAAS,cAAc,MAAM,SAAS;;AACpC,YAAM,EAAE,YAAY,QAAQ,WAAW,UAAU;AACjD,UAAI,CAAC,MAAM,mBAAmB;AAAU;AAGxC,UAAI,MAAM,iBAAiB,CAAC,QAAQ;AAClCC,iCAAAA,SAAS,eAAe,KAAK,KAAK,OAAO;AACzC;AAAA,MACF;AAEA,WAAI,cAAS,UAAT,mBAAgB,QAAQ;AAC1B,iBAAS,MAAM,QAAQ,OAAK;AAC1B,YAAE,gBAAgBH,yBAAc;AAAA,SACjC;AAAA,MACH;AAEA,iBAAW,QAAQ,OAAK;AACtB,cAAM,aAAa,QAAQ,MAAM,CAAC;AAClC,mBAAW,gBAAgB,UAAU,uBAAuB,UAAU,IAAIA,yBAAc;AAAA,OACzF;AAGD,WAAK,gBAAgB,UAAUC,yBAAAA,kBAAkBD,yBAAc;AAAA,IACjE;AAyBA,aAAS,kBAAkB,MAAM,iBAAiB,SAAS,MAAM;;AAE/D,UAAI,WAAW,OAAO;AACpB,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,gBAAM,OAAO,KAAK,CAAC;AACnB,cAAI,mDAAiB,SAAS,KAAK,MAAM;AACvC,iBAAK,SAAS;AACd,iBAAI,UAAK,iBAAL,mBAAmB,QAAQ;AAC7B,mBAAK,aAAa,QAAQ,QAAM;AAC9B,wBAAQ,MAAM,EAAE,EAAE,SAAS;AAC3B,wBAAQ,MAAM,EAAE,EAAE,OAAO;AAAA,eAC1B;AAAA,YACH;AAAA,UACF;AAAA,QACF;AACA;AAAA,MACF;AAEA,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAM,OAAO,KAAK,CAAC;AAEnB,YAAI,mDAAiB,SAAS,KAAK,MAAM;AAEvC,eAAK,SAAS;AACd,eAAI,UAAK,aAAL,mBAAe,QAAQ;AACzB,iBAAK,SAAS,QAAQ,OAAK;AACzB,oBAAM,QAAQE,cAAK,MAAC,OAAO,EAAE,EAAE,GAAG;AAClC,oBAAM,OAAO;AAAA,aACd;AAAA,UACH;AAGA,eAAI,UAAK,eAAL,mBAAiB,QAAQ;AAC3B,iBAAK,WAAW,QAAQ,OAAK;;AAC3B,oBAAM,QAAQA,cAAAA,MAAM,OAAO,EAAE,CAAC;AAC9B,oBAAM,SAAS;AACf,mBAAIE,MAAA,MAAM,aAAN,gBAAAA,IAAgB,QAAQ;AAC1B,sBAAM,SAAS,QAAQ,CAAAC,OAAK;AAC1B,wBAAM,SAASH,cAAK,MAAC,OAAO,EAAEG,GAAE,GAAG;AACnC,yBAAO,OAAO;AAAA,iBACf;AAAA,cACH;AAAA,aACD;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAMA,aAAS,kBAAkB,MAAM;AAC/B,YAAM,EAAE,cAAc,YAAY,eAAe,QAAQ,WAAW,MAAM,IAAI;AAC9E,UAAI,CAAC,MAAM;AAAc;AACzB,UAAI;AAAU;AAGd,WAAK,gBAAgB,kBAAkBJ,yBAAc,kBAAID,yBAAc,kBAAIC,yBAAc;AAGzF,UAAI,CAAC,MAAM,eAAe;AACxB,YAAI,MAAM,eAAe;AACvB,eAAK,OAAO;AACZ,eAAK,UAAS,6CAAc,UAAS,KAAK;AAAA,QAC5C;AAEA,qBAAa,QAAQ,OAAK;;AACxB,gBAAM,eAAeC,cAAAA,MAAM,OAAO,EAAE,CAAC;AACrC,uBAAa,gBAAgB,aAAa,WAAW,aAAa,gBAAgB,KAAK;AAEvF,cAAI,MAAM,eAAe;AACvB,yBAAa,OAAO;AACpB,yBAAa,WAAS,kDAAc,iBAAd,mBAA4B,UAAS,KAAK,aAAa;AAAA,UAC/E;AAAA,SACD;AAAA,aACI;AACL,YAAI,MAAM,eAAe;AACvBC,mCAAQ,SAAC,4DAA4D;AAAA,QACvE;AAAA,MACF;AAGA,UAAI,CAAC,MAAM,eAAe;AACxB,mBAAW,QAAQ,OAAK;AACtB,gBAAM,aAAa,QAAQ,MAAM,CAAC;AAClC,qBAAW,gBAAgB,uBAAuB,UAAU;AAAA,SAC7D;AAAA,MACH;AAEA,YAAM,iBAAiB,CAAC;AACxB,eAAS,IAAI,GAAG,IAAI,SAAS,MAAM,QAAQ,KAAK;AAC9C,cAAM,IAAI,SAAS,MAAM,CAAC;AAC1B,YAAI,EAAE,kBAAkBF,0CAAiB;AACvC,cAAK,MAAM,mBAAmB,EAAE,YAAa,CAAC,EAAE,UAAU;AACxD,2BAAe,KAAK,EAAE,GAAG;AAAA,UAC3B;AAAA,QACF;AAAA,MACF;AAEA,kBAAY,QAAQ,CAAC,GAAG,cAAc;AAEtC,WAAK,UAAU,gBAAgB,IAAI;AAAA,IACrC;AAMA,aAAS,kBAAkB,MAAM;;AAC/B,YAAM,EAAE,YAAY,eAAe,KAAK,WAAW,OAAO,OAAK,IAAM;AACrE,UAAI,MAAM;AAAc;AACxB,UAAI,MAAM,iBAAiB,CAAC;AAAQ,6BAAqB,IAAI;AAE7D,UAAI;AAAU;AAGd,WAAI,cAAS,UAAT,mBAAgB,QAAQ;AAC1B,iBAAS,IAAI,GAAG,IAAI,SAAS,MAAM,QAAQ,KAAK;AAC9C,gBAAM,IAAI,SAAS,MAAM,CAAC;AAC1B,YAAE,gBAAgBD,yBAAc;AAAA,QAClC;AAAA,MACF;AAEA,iBAAW,QAAQ,OAAK;AACtB,cAAM,aAAa,QAAQ,MAAM,CAAC;AAClC,mBAAW,gBAAgB,uBAAuB,UAAU;AAAA,OAC7D;AAGD,WAAK,gBAAgB,kBAAkBC,yBAAc,kBAAID,yBAAc,kBAAIC,yBAAc;AAEzF,kBAAY,QAAQ;AACpB,WAAK,UAAU,KAAK,IAAI;AAAA,IAC1B;AAKA,aAAS,iBAAiB,MAAM;AAC9B,UAAI,MAAM,cAAc;AACtB,0BAAkB,IAAI;AAAA,aACjB;AACL,0BAAkB,IAAI;AAAA,MACxB;AAAA,IACF;AAMA,aAAe,qBAAqB,MAAM;AAAA;AACxC,YAAI,MAAM;AAAa;AAEvB,cAAM,EAAE,QAAQ,UAAU,OAAO,aAAa;AAC9C,YAAI,YAAY,SAAS;AAAS;AAElC,4BAAoB,IAAI;AAGxB,aAAK,SAAS,CAAC;AAEf,YAAI,cAAc;AAClB,YAAI,CAAC,UAAU;AACb,cAAI,CAAC,MAAM,gBAAgB,MAAM,iBAAiB,MAAM,UAAU;AAChEE,qCAAQ,SAAC,qCAAqC;AAAA,iBACzC;AACL,0BAAc,MAAM,eAAe,IAAI;AAAA,UACzC;AAAA,QACF;AAEA,aAAK,UAAU,CAAC,QAAQ,eAAe,QAAQ,IAAI;AAAA,MACrD;AAAA;AAMA,aAAS,oBAAoB,MAAM;AACjC,YAAM,EAAE,QAAQ,cAAc,WAAW,SAAS;AAElD,UAAI,QAAQ;AACV,YAAI,6CAAc,QAAQ;AACxB,uBAAa,QAAQ,OAAK;AACxB,gBAAID,oBAAM,OAAO,EAAE,CAAC,GAAG;AACrBA,4BAAAA,MAAM,OAAO,EAAE,CAAC,EAAE,OAAO;AACzBA,4BAAAA,MAAM,OAAO,EAAE,CAAC,EAAE,SAAS;AAAA,YAC7B;AAAA,WACD;AAAA,QACH;AAAA,aACK;AACL,YAAI,qCAAU,QAAQ;AACpB,gBAAMI,gBAAe,SAAS,IAAI,OAAK,EAAE,GAAG;AAC5C,UAAAA,cAAa,QAAQ,OAAK;AACxB,gBAAIJ,oBAAM,OAAO,EAAE,CAAC,GAAG;AACrBA,4BAAAA,MAAM,OAAO,EAAE,CAAC,EAAE,OAAO;AAAA,YAC3B;AAAA,WACD;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAMA,aAAe,eAAe,MAAM;AAAA;;AAClC,cAAM,EAAE,QAAQ,KAAK,QAAQ,SAAS,IAAI;AAC1C,aAAI,qCAAU,WAAU,CAAC,MAAM,iBAAiB;AAC9C,iBAAO;AAAA,QACT;AAEA,YAAI,UAAU,MAAM,YAAY,CAAC,QAAQ;AACvC,cAAIH,yBAAU,WAAC,MAAM,OAAO,GAAG;AAC7B,yBAAa,MAAM,KAAK,GAAG;AAC3B,wBAAY,QAAQ;AACpB,iBAAK,UAAU;AAEf,kBAAM,cAAcF,yBAAS,UAAC,IAAI;AAClC,kBAAM,SAAS,MAAM,MAAM,QAAQ,WAAW;AAG9C,gBAAI,cAAc,CAAC,KAAI,UAAK,eAAL,mBAAiB,aAAY,CAAA,GAAK,GAAI,UAAU,CAAA,CAAG;AAC1E,kBAAM,iBAAiB,CAAC;AACxB,0BAAc,YAAY,OAAO,CAAC,OAAO,SAAS;AAChD,6BAAe,KAAK,SAAS,KAAK,CAAC,IAAI,KAAK,eAAe,KAAK,SAAS,KAAK,CAAC,IAAY,MAAM,KAAK,IAAI;AAC1G,qBAAO;AAAA,YACR,GAAE,EAAE;AAEL,iBAAK,WAAW,WAAW,eAAe;AAC1C,gBAAI,iCAAQ,QAAQ;AAClB,oBAAM,cAAc,SAAS,MAAM,UAAU,OAAK,EAAE,QAAQ,KAAK,GAAG;AACpE,6BAAe,QAAQ,MAAM,KAAK,QAAQ,GAAG,WAAW;AACxD,uBAAS,QAAQ,cAAc,SAAS,KAAK;AAAA,mBACxC;AAEL,mBAAK,SAAS;AACd,mBAAK,SAAS;AACd,mBAAK,YAAY;AAAA,YACnB;AAEA,wBAAY,QAAQ;AACpB,iBAAK,UAAU;AACf,iBAAK,SAAS;AAAA,UAChB;AAAA,eACK;AACL,gBAAM,MAAM,aAAa,MAAM,UAAU,OAAK,MAAM,GAAG;AACvD,cAAI,OAAO,GAAG;AACZ,yBAAa,MAAM,OAAO,KAAK,CAAC;AAAA,UAClC;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAAA;AAMA,aAAS,uBAAuB,MAAM;AACpC,UAAI,CAAC,MAAM;AACT,eAAOG,yBAAc;AAAA,MACvB;AAEA,UAAI,CAAC,MAAM,mBAAmB,KAAK,UAAU;AAC3C,eAAO,KAAK,iBAAiBA,yBAAc;AAAA,MAC7C;AAGA,UAAI,CAAC,MAAM,cAAc;AACvB,eAAOO,yBAAgB;AAAA,MACzB;AAEA,YAAM,EAAE,SAAO,IAAM;AAErB,YAAM,qBAAqB,SAAS,MAAM,OAAK,EAAE,kBAAkBN,wCAAe;AAClF,UAAI,oBAAoB;AACtB,eAAOA,yBAAc;AAAA,MACvB;AAGA,YAAM,uBAAuB,SAAS,MAAM,OAAK,EAAE,kBAAkBD,wCAAe;AACpF,UAAI,sBAAsB;AACxB,eAAOA,yBAAc;AAAA,MACvB;AAEA,aAAOO,yBAAgB;AAAA,IACzB;AAEA,aAAS,aAAa;AACpB,UAAI,MAAM,gBAAgB,IAAI;AAC5B,iBAAS,MAAM,QAAQ,OAAK;AAC1B,YAAE,OAAO;AAAA,SACV;AACD;AAAA,MACF;AAEA,eAAS,MAAM,QAAQ,OAAK;AAC1B,YAAI,EAAE,MAAM,QAAQ,MAAM,WAAW,IAAI,IAAI;AAC3C,YAAE,OAAO;AACT,YAAE,WAAW,QAAQ,CAAAF,OAAK;AACxB,oBAAQ,MAAMA,EAAC,EAAE,OAAO;AAAA,WACzB;AAAA,eACI;AACL,YAAE,OAAO;AAAA,QACX;AAAA,OACD;AAED,eAAS,MAAM,QAAQ,OAAK;AAC1B,YAAI,EAAE,MAAM;AACV,YAAE,WAAW,QAAQ,CAAAA,OAAK;AACxB,oBAAQ,MAAMA,EAAC,EAAE,OAAO;AAAA,WACzB;AAAA,QACH;AAAA,OACD;AAAA,IACH;AAKA,UAAM,iBAAiB,MAAMG,yBAAAA,eAAe,SAAS,OAAO,iBAAiBP,yBAAAA,iBAAiB,MAAM,eAAe;AAMnH,aAAS,eAAe,MAAM,UAAU,MAAM;AAE5C,UAAI,MAAM,cAAc;AACtB,YAAI,CAACQ,yBAAAA,QAAQ,IAAI,GAAG;AAClBN,mCAAAA,SAAS,gCAAgC,IAAI,GAAG;AAChD;AAAA,QACF;AAEA,cAAMO,QAAO,SAAS;AAGtB,YAAI,YAAY,OAAO;AACrB,cAAIC,kBAAiB,CAAC;AACtB,mBAAS,IAAI,GAAG,IAAI,YAAY,MAAM,QAAQ,KAAK;AACjD,kBAAM,KAAK,YAAY,MAAM,CAAC;AAC9B,gBAAI,CAAC,KAAK,SAAS,EAAE,GAAG;AACtB,cAAAA,gBAAe,KAAK,EAAE;AAAA,YACxB;AAAA,UACF;AACA,UAAAA,kBAAiB,CAAC,GAAG,IAAI,IAAIA,eAAc,CAAC;AAC5C,sBAAY,QAAQA;AACpB,2BAAiBD,OAAM,MAAM,KAAK;AAElC;AAAA,QACF;AAGA,cAAM,iBAAiB,CAAC,GAAG,YAAY,OAAO,GAAG,IAAI;AACrD,oBAAY,QAAQ,CAAC,GAAG,IAAI,IAAI,cAAc,CAAC;AAC/C,yBAAiBA,OAAM,YAAY,OAAO,IAAI;AAE9C,YAAI,MAAM,iBAAiB,SAAS;AAClC,uBAAa,QAAQ,CAAC,GAAG,oBAAI,IAAI,CAAC,GAAI,YAAY,SAAS,CAAE,GAAG,GAAI,QAAQ,CAAE,CAAC,CAAC,CAAC;AACjF,4BAAkBA,OAAM,MAAM,IAAI;AAAA,QACpC;AACA;AAAA,MACF;AAIA,UAAID,yBAAAA,QAAQ,IAAI,GAAG;AACjB,eAAO,KAAK,CAAC;AAAA,MACf;AAEA,UAAI,CAACG,yBAAQ,SAAC,IAAI,KAAK,CAACC,yBAAQ,SAAC,IAAI,GAAG;AACtCV,iCAAQ,SAAC,sCAAsC,IAAI;AACnD;AAAA,MACF;AAEA,YAAM,OAAO,SAAS;AACtB,kBAAY,QAAQ,UAAU,OAAO;AAErC,UAAI,MAAM,iBAAiB,SAAS;AAClC,0BAAkB,MAAM,CAAC,IAAI,GAAG,IAAI;AAAA,MACtC;AAEA,uBAAiB,MAAM,MAAM,CAAC,CAAC,OAAO;AAAA,IACxC;AAIA,UAAM,qBAAqB,MAAMK,yBAAAA,eAAe,SAAS,OAAO,iBAAiBD,yBAAAA,mBAAmB,MAAM,eAAe;AAIzH,UAAM,mBAAmB,MAAMC,yBAAAA,eAAe,SAAS,OAAO,iBAAiBR,yBAAAA,iBAAiB,MAAM,eAAe;AAIrH,UAAM,kBAAkB,MAAMQ,yBAAc,eAAC,SAAS,OAAO,UAAU,IAAI;AAI3E,UAAM,oBAAoB,MAAMA,yBAAc,eAAC,SAAS,OAAO,UAAU,KAAK;AAO9E,aAAS,gBAAgB,MAAM,SAAS,MAAM;AAC5C,UAAI,CAAC,MAAM,QAAQ,IAAI,KAAK,SAAS,OAAO;AAC1CL,iCAAQ,SAAC,qCAAqC,IAAI;AAClD;AAAA,MACF;AAEA,YAAM,OAAO,SAAS;AAGtB,UAAI,SAAS,OAAO;AAClB,aAAK,QAAQ,OAAK;AAChB,YAAE,SAAS;AACX,cAAI,EAAE,QAAQ,GAAG;AACf,cAAE,OAAO;AAAA,UACX;AAAA,SACD;AACD;AAAA,MACF;AAGA,UAAI,WAAW,OAAO;AACpB,cAAMW,mBAAkB,CAAC;AACzB,iBAAS,IAAI,GAAG,IAAI,aAAa,MAAM,QAAQ,KAAK;AAClD,gBAAM,KAAK,aAAa,MAAM,CAAC;AAC/B,cAAI,CAAC,KAAK,SAAS,EAAE,GAAG;AACtB,YAAAA,iBAAgB,KAAK,EAAE;AAAA,UACzB;AAAA,QACF;AACA,qBAAa,QAAQ,CAAC,GAAG,IAAI,IAAIA,gBAAe,CAAC;AACjD,0BAAkB,MAAM,MAAM,KAAK;AAEnC;AAAA,MACF;AAGA,YAAM,kBAAkB,CAAC;AACzB,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,YAAI,KAAK,SAAS,KAAK,CAAC,EAAE,GAAG,GAAG;AAC9B,0BAAgB,KAAK,KAAK,CAAC,EAAE,GAAG;AAAA,QAClC;AAAA,MACF;AACA,mBAAa,QAAQ,CAAC,GAAG,IAAI,IAAI,eAAe,CAAC;AACjD,wBAAkB,MAAM,iBAAiB,IAAI;AAAA,IAC/C;AAIA,UAAM,kBAAkB,MAAMC,yBAAAA,YAAY,SAAS,OAAO,iBAAiBd,yBAAAA,iBAAiB,MAAM,eAAe;AAIjH,UAAM,sBAAsB,MAAMc,yBAAAA,YAAY,SAAS,OAAO,iBAAiBR,yBAAAA,mBAAmB,MAAM,eAAe;AAIvH,UAAM,oBAAoB,MAAMQ,yBAAAA,YAAY,SAAS,OAAO,iBAAiBf,yBAAAA,iBAAiB,MAAM,eAAe;AAInH,UAAM,mBAAmB,MAAMe,yBAAW,YAAC,SAAS,OAAO,UAAU,IAAI;AAIzE,UAAM,qBAAqB,MAAMA,yBAAW,YAAC,SAAS,OAAO,UAAU,KAAK;AAE5EC,kBAAK;AAAA,MACH,MAAM,MAAM;AAAA,MACZ,CAAC,MAAM;AACL,YAAI,uBAAG,QAAQ;AACb,uBAAa,QAAQ;AAAA,eAChB;AACL,uBAAa,QAAQ,CAAC;AAAA,QACxB;AAAA,MAGD;AAAA,MACD,EAAE,WAAW,KAAK;AAAA,IACpB;AAEAA,kBAAK;AAAA,MACH,MAAM,MAAM;AAAA,MACZ,CAAC,MAAM;AACL,YAAI,MAAM,cAAc;AACtB,cAAI,uBAAG,QAAQ;AACb,wBAAY,QAAQ;AAAA,iBACf;AACL,wBAAY,QAAQ,CAAC;AAAA,UACvB;AAAA,eACK;AACL,cAAI,KAAK,MAAM,GAAG;AAChB,wBAAY,QAAQ;AAAA,iBACf;AACL,wBAAY,QAAQ;AAAA,UACtB;AAAA,QACF;AAAA,MAED;AAAA,MACD,EAAE,WAAW,KAAK;AAAA,IACpB;AAEAA,kBAAK;AAAA,MACH,MAAM,MAAM;AAAA,MACZ,CAAC,MAAM;AACL,gBAAQ,QAAQnB,yBAAS,UAAC,CAAC;AAC3B,mBAAW,MAAM;AACf,mBAAS;AAAA,QACV,GAAE,EAAE;AAAA,MACN;AAAA,MACD,EAAE,WAAW,MAAM,MAAM,KAAK;AAAA,IAChC;AAEAmB,kBAAK;AAAA,MACH,MAAM,MAAM;AAAA,MACZ,MAAM;AACJ,mBAAW;AAAA,MACZ;AAAA,IACH;AAEA,WAAO;AAAA,MACL;AAAA,MACA,iBAAAhB,yBAAe;AAAA,MACf,mBAAAO,yBAAiB;AAAA,MACjB,iBAAAN,yBAAe;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA;AAAA;AAAA,MAKA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACD;AACH,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACr8BD,GAAG,gBAAgB,SAAS;"}