{"version": 3, "file": "form.js", "sources": ["../../../../../src/pages-data/psychology/form.vue", "../../../../../uniPage:/cGFnZXMtZGF0YVxwc3ljaG9sb2d5XGZvcm0udnVl"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n    <PageLayout>\r\n        <template #navbar>\r\n            <NavBar :title=\"mode === 'add' ? '新增' : '编辑'\" :showBack=\"true\" />\r\n        </template>\r\n\r\n        <scroll-view class=\"page-scroll-view\" :scroll-y=\"true\">\r\n            <view class=\"form-container\">\r\n                <view class=\"form-header\">\r\n                    <text class=\"form-title\">慢病心衰患者量心理量表</text>\r\n                </view>\r\n                <!-- 表单项 -->\r\n                <view class=\"form-section\">\r\n                    <view class=\"form-item\">\r\n                        <view class=\"form-label\">尊敬的用户：</view>\r\n                        <view class=\"form-label indent-text\">\r\n                            我们邀请您参加“院内慢性心力衰竭患者院外管理中应用研究”课题研究。本研究将在武汉亚心总医院开展。本研究已经得到伦理委员会的审查和批准。本研究的目的是探索在现有的慢性心力衰竭随访计划中增加线上远程随访的模式和效果，探索线上远程随访能否改善慢性心衰患者的身体机能、心理状况、生活质量、自我管理行为以及死亡率和再住院率，以推动慢性心衰患者线上远程随访模式的建立和发展，推动慢性心衰患者院外健康管理的智能化发展，为慢性心衰患者的院外健康管理提供进一步的支持。\r\n                        </view>\r\n                        <view class=\"form-label indent-text\">\r\n                            请您按监测表1-5项监测项目，请您真实填写，谢谢配合!\r\n                        </view>\r\n                    </view>\r\n                    <!-- 姓名 -->\r\n                    <view class=\"form-item\">\r\n                        <view class=\"inline-form-item\">\r\n                            <text class=\"inline-form-label\"><text class=\"required\">*</text>1.患者姓名：</text>\r\n                            <input class=\"inline-form-input\" v-model=\"formData.userName\" disabled />\r\n                        </view>\r\n                    </view>\r\n\r\n                    <!-- 单选项 -->\r\n                    <view class=\"form-item\">\r\n                        <text class=\"form-label\"><text class=\"required\">*</text>2.做事时提不起劲或没有乐趣</text>\r\n                        <view class=\"radio-group\">\r\n                            <view class=\"radio-row\">\r\n                                <view class=\"radio-item\" @click=\"(formData.apathyDay = '完全不会')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.apathyDay === '完全不会' }\"></view>\r\n                                    <text>完全不会</text>\r\n                                </view>\r\n                                <view class=\"radio-item\" @click=\"(formData.apathyDay = '1~3天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.apathyDay === '1~3天' }\">\r\n                                    </view>\r\n                                    <text>1~3天</text>\r\n                                </view>\r\n                            </view>\r\n                            <view class=\"radio-row\">\r\n                                <view class=\"radio-item\" @click=\"(formData.apathyDay = '4~6天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.apathyDay === '4~6天' }\">\r\n                                    </view>\r\n                                    <text>4~6天</text>\r\n                                </view>\r\n                                <view class=\"radio-item\" @click=\"(formData.apathyDay = '几乎每天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.apathyDay === '几乎每天' }\">\r\n                                    </view>\r\n                                    <text>几乎每天</text>\r\n                                </view>\r\n                            </view>\r\n                        </view>\r\n                    </view>\r\n\r\n                    <view class=\"form-item\">\r\n                        <text class=\"form-label\"><text class=\"required\">*</text>3.感到心情低落、沮丧或绝望</text>\r\n                        <view class=\"radio-group\">\r\n                            <view class=\"radio-row\">\r\n                                <view class=\"radio-item\" @click=\"(formData.sadDay = '完全不会')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.sadDay === '完全不会' }\">\r\n                                    </view>\r\n                                    <text>完全不会</text>\r\n                                </view>\r\n                                <view class=\"radio-item\" @click=\"(formData.sadDay = '1~3天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.sadDay === '1~3天' }\">\r\n                                    </view>\r\n                                    <text>1~3天</text>\r\n                                </view>\r\n                            </view>\r\n                            <view class=\"radio-row\">\r\n                                <view class=\"radio-item\" @click=\"(formData.sadDay = '4~6天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.sadDay === '4~6天' }\">\r\n                                    </view>\r\n                                    <text>4~6天</text>\r\n                                </view>\r\n                                <view class=\"radio-item\" @click=\"(formData.sadDay = '几乎每天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.sadDay === '几乎每天' }\">\r\n                                    </view>\r\n                                    <text>几乎每天</text>\r\n                                </view>\r\n                            </view>\r\n                        </view>\r\n                    </view>\r\n\r\n                    <view class=\"form-item\">\r\n                        <text class=\"form-label\"><text class=\"required\">*</text>4.入睡困难、睡不安稳或睡眠太多</text>\r\n                        <view class=\"radio-group\">\r\n                            <view class=\"radio-row\">\r\n                                <view class=\"radio-item\" @click=\"(formData.sleepDay = '完全不会')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.sleepDay === '完全不会' }\">\r\n                                    </view>\r\n                                    <text>完全不会</text>\r\n                                </view>\r\n                                <view class=\"radio-item\" @click=\"(formData.sleepDay = '1~3天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.sleepDay === '1~3天' }\">\r\n                                    </view>\r\n                                    <text>1~3天</text>\r\n                                </view>\r\n                            </view>\r\n                            <view class=\"radio-row\">\r\n                                <view class=\"radio-item\" @click=\"(formData.sleepDay = '4~6天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.sleepDay === '4~6天' }\">\r\n                                    </view>\r\n                                    <text>4~6天</text>\r\n                                </view>\r\n                                <view class=\"radio-item\" @click=\"(formData.sleepDay = '几乎每天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.sleepDay === '几乎每天' }\">\r\n                                    </view>\r\n                                    <text>几乎每天</text>\r\n                                </view>\r\n                            </view>\r\n                        </view>\r\n                    </view>\r\n\r\n                    <view class=\"form-item\">\r\n                        <text class=\"form-label\"><text class=\"required\">*</text>5.感觉疲劳或没有活力</text>\r\n                        <view class=\"radio-group\">\r\n                            <view class=\"radio-row\">\r\n                                <view class=\"radio-item\" @click=\"(formData.tiredDay = '完全不会')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.tiredDay === '完全不会' }\">\r\n                                    </view>\r\n                                    <text>完全不会</text>\r\n                                </view>\r\n                                <view class=\"radio-item\" @click=\"(formData.tiredDay = '1~3天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.tiredDay === '1~3天' }\">\r\n                                    </view>\r\n                                    <text>1~3天</text>\r\n                                </view>\r\n                            </view>\r\n                            <view class=\"radio-row\">\r\n                                <view class=\"radio-item\" @click=\"(formData.tiredDay = '4~6天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.tiredDay === '4~6天' }\">\r\n                                    </view>\r\n                                    <text>4~6天</text>\r\n                                </view>\r\n                                <view class=\"radio-item\" @click=\"(formData.tiredDay = '几乎每天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.tiredDay === '几乎每天' }\">\r\n                                    </view>\r\n                                    <text>几乎每天</text>\r\n                                </view>\r\n                            </view>\r\n                        </view>\r\n                    </view>\r\n\r\n                    <view class=\"form-item\">\r\n                        <text class=\"form-label\"><text class=\"required\">*</text>6.食欲不振或吃太多</text>\r\n                        <view class=\"radio-group\">\r\n                            <view class=\"radio-row\">\r\n                                <view class=\"radio-item\" @click=\"(formData.appetiteDay = '完全不会')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.appetiteDay === '完全不会' }\">\r\n                                    </view>\r\n                                    <text>完全不会</text>\r\n                                </view>\r\n                                <view class=\"radio-item\" @click=\"(formData.appetiteDay = '1~3天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.appetiteDay === '1~3天' }\">\r\n                                    </view>\r\n                                    <text>1~3天</text>\r\n                                </view>\r\n                            </view>\r\n                            <view class=\"radio-row\">\r\n                                <view class=\"radio-item\" @click=\"(formData.appetiteDay = '4~6天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.appetiteDay === '4~6天' }\">\r\n                                    </view>\r\n                                    <text>4~6天</text>\r\n                                </view>\r\n                                <view class=\"radio-item\" @click=\"(formData.appetiteDay = '几乎每天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.appetiteDay === '几乎每天' }\">\r\n                                    </view>\r\n                                    <text>几乎每天</text>\r\n                                </view>\r\n                            </view>\r\n                        </view>\r\n                    </view>\r\n\r\n                    <view class=\"form-item\">\r\n                        <text class=\"form-label\"><text class=\"required\">*</text>7.觉得自己很糟，或觉得自己很失败</text>\r\n                        <view class=\"radio-group\">\r\n                            <view class=\"radio-row\">\r\n                                <view class=\"radio-item\" @click=\"(formData.awfulDay = '完全不会')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.awfulDay === '完全不会' }\">\r\n                                    </view>\r\n                                    <text>完全不会</text>\r\n                                </view>\r\n                                <view class=\"radio-item\" @click=\"(formData.awfulDay = '1~3天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.awfulDay === '1~3天' }\">\r\n                                    </view>\r\n                                    <text>1~3天</text>\r\n                                </view>\r\n                            </view>\r\n                            <view class=\"radio-row\">\r\n                                <view class=\"radio-item\" @click=\"(formData.awfulDay = '4~6天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.awfulDay === '4~6天' }\">\r\n                                    </view>\r\n                                    <text>4~6天</text>\r\n                                </view>\r\n                                <view class=\"radio-item\" @click=\"(formData.awfulDay = '几乎每天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.awfulDay === '几乎每天' }\">\r\n                                    </view>\r\n                                    <text>几乎每天</text>\r\n                                </view>\r\n                            </view>\r\n                        </view>\r\n                    </view>\r\n\r\n                    <view class=\"form-item\">\r\n                        <text class=\"form-label\"><text class=\"required\">*</text>8.对事物专注有困难，例如阅读报纸或看电视时</text>\r\n                        <view class=\"radio-group\">\r\n                            <view class=\"radio-row\">\r\n                                <view class=\"radio-item\" @click=\"(formData.focusedDay = '完全不会')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.focusedDay === '完全不会' }\">\r\n                                    </view>\r\n                                    <text>完全不会</text>\r\n                                </view>\r\n                                <view class=\"radio-item\" @click=\"(formData.focusedDay = '1~3天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.focusedDay === '1~3天' }\">\r\n                                    </view>\r\n                                    <text>1~3天</text>\r\n                                </view>\r\n                            </view>\r\n                            <view class=\"radio-row\">\r\n                                <view class=\"radio-item\" @click=\"(formData.focusedDay = '4~6天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.focusedDay === '4~6天' }\">\r\n                                    </view>\r\n                                    <text>4~6天</text>\r\n                                </view>\r\n                                <view class=\"radio-item\" @click=\"(formData.focusedDay = '几乎每天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.focusedDay === '几乎每天' }\">\r\n                                    </view>\r\n                                    <text>几乎每天</text>\r\n                                </view>\r\n                            </view>\r\n                        </view>\r\n                    </view>\r\n                    <view class=\"form-item\">\r\n                        <text class=\"form-label\"><text\r\n                                class=\"required\">*</text>9.动作或说话速度缓慢到别人已经察觉，或正好相反，烦躁或坐立不安，动来动去的情况更甚于平常</text>\r\n                        <view class=\"radio-group\">\r\n                            <view class=\"radio-row\">\r\n                                <view class=\"radio-item\" @click=\"(formData.slowDay = '完全不会')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.slowDay === '完全不会' }\">\r\n                                    </view>\r\n                                    <text>完全不会</text>\r\n                                </view>\r\n                                <view class=\"radio-item\" @click=\"(formData.slowDay = '1~3天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.slowDay === '1~3天' }\">\r\n                                    </view>\r\n                                    <text>1~3天</text>\r\n                                </view>\r\n                            </view>\r\n                            <view class=\"radio-row\">\r\n                                <view class=\"radio-item\" @click=\"(formData.slowDay = '4~6天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.slowDay === '4~6天' }\">\r\n                                    </view>\r\n                                    <text>4~6天</text>\r\n                                </view>\r\n                                <view class=\"radio-item\" @click=\"(formData.slowDay = '几乎每天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.slowDay === '几乎每天' }\">\r\n                                    </view>\r\n                                    <text>几乎每天</text>\r\n                                </view>\r\n                            </view>\r\n                        </view>\r\n                    </view>\r\n                    <view class=\"form-item\">\r\n                        <text class=\"form-label\"><text class=\"required\">*</text>10.感觉紧张，焦虑或急切</text>\r\n                        <view class=\"radio-group\">\r\n                            <view class=\"radio-row\">\r\n                                <view class=\"radio-item\" @click=\"(formData.nervousDay = '完全不会')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.nervousDay === '完全不会' }\">\r\n                                    </view>\r\n                                    <text>完全不会</text>\r\n                                </view>\r\n                                <view class=\"radio-item\" @click=\"(formData.nervousDay = '1~3天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.nervousDay === '1~3天' }\">\r\n                                    </view>\r\n                                    <text>1~3天</text>\r\n                                </view>\r\n                            </view>\r\n                            <view class=\"radio-row\">\r\n                                <view class=\"radio-item\" @click=\"(formData.nervousDay = '4~6天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.nervousDay === '4~6天' }\">\r\n                                    </view>\r\n                                    <text>4~6天</text>\r\n                                </view>\r\n                                <view class=\"radio-item\" @click=\"(formData.nervousDay = '几乎每天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.nervousDay === '几乎每天' }\">\r\n                                    </view>\r\n                                    <text>几乎每天</text>\r\n                                </view>\r\n                            </view>\r\n                        </view>\r\n                    </view>\r\n                    <view class=\"form-item\">\r\n                        <text class=\"form-label\"><text class=\"required\">*</text>11.对各种各样的事情担忧过多</text>\r\n                        <view class=\"radio-group\">\r\n                            <view class=\"radio-row\">\r\n                                <view class=\"radio-item\" @click=\"(formData.concernDay = '完全不会')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.concernDay === '完全不会' }\">\r\n                                    </view>\r\n                                    <text>完全不会</text>\r\n                                </view>\r\n                                <view class=\"radio-item\" @click=\"(formData.concernDay = '1~3天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.concernDay === '1~3天' }\">\r\n                                    </view>\r\n                                    <text>1~3天</text>\r\n                                </view>\r\n                            </view>\r\n                            <view class=\"radio-row\">\r\n                                <view class=\"radio-item\" @click=\"(formData.concernDay = '4~6天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.concernDay === '4~6天' }\">\r\n                                    </view>\r\n                                    <text>4~6天</text>\r\n                                </view>\r\n                                <view class=\"radio-item\" @click=\"(formData.concernDay = '几乎每天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.concernDay === '几乎每天' }\">\r\n                                    </view>\r\n                                    <text>几乎每天</text>\r\n                                </view>\r\n                            </view>\r\n                        </view>\r\n                    </view>\r\n                    <view class=\"form-item\">\r\n                        <text class=\"form-label\"><text class=\"required\">*</text>12.不能够停止或控制担忧</text>\r\n                        <view class=\"radio-group\">\r\n                            <view class=\"radio-row\">\r\n                                <view class=\"radio-item\" @click=\"(formData.stopDay = '完全不会')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.stopDay === '完全不会' }\">\r\n                                    </view>\r\n                                    <text>完全不会</text>\r\n                                </view>\r\n                                <view class=\"radio-item\" @click=\"(formData.stopDay = '1~3天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.stopDay === '1~3天' }\">\r\n                                    </view>\r\n                                    <text>1~3天</text>\r\n                                </view>\r\n                            </view>\r\n                            <view class=\"radio-row\">\r\n                                <view class=\"radio-item\" @click=\"(formData.stopDay = '4~6天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.stopDay === '4~6天' }\">\r\n                                    </view>\r\n                                    <text>4~6天</text>\r\n                                </view>\r\n                                <view class=\"radio-item\" @click=\"(formData.stopDay = '几乎每天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.stopDay === '几乎每天' }\">\r\n                                    </view>\r\n                                    <text>几乎每天</text>\r\n                                </view>\r\n                            </view>\r\n                        </view>\r\n                    </view>\r\n\r\n                    <view class=\"form-item\">\r\n                        <text class=\"form-label\"><text class=\"required\">*</text>13.很难放松下来</text>\r\n                        <view class=\"radio-group\">\r\n                            <view class=\"radio-row\">\r\n                                <view class=\"radio-item\" @click=\"(formData.relaxDay = '完全不会')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.relaxDay === '完全不会' }\">\r\n                                    </view>\r\n                                    <text>完全不会</text>\r\n                                </view>\r\n                                <view class=\"radio-item\" @click=\"(formData.relaxDay = '1~3天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.relaxDay === '1~3天' }\">\r\n                                    </view>\r\n                                    <text>1~3天</text>\r\n                                </view>\r\n                            </view>\r\n                            <view class=\"radio-row\">\r\n                                <view class=\"radio-item\" @click=\"(formData.relaxDay = '4~6天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.relaxDay === '4~6天' }\">\r\n                                    </view>\r\n                                    <text>4~6天</text>\r\n                                </view>\r\n                                <view class=\"radio-item\" @click=\"(formData.relaxDay = '几乎每天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.relaxDay === '几乎每天' }\">\r\n                                    </view>\r\n                                    <text>几乎每天</text>\r\n                                </view>\r\n                            </view>\r\n                        </view>\r\n                    </view>\r\n\r\n                    <view class=\"form-item\">\r\n                        <text class=\"form-label\"><text class=\"required\">*</text>14.由于不安而无法静坐</text>\r\n                        <view class=\"radio-group\">\r\n                            <view class=\"radio-row\">\r\n                                <view class=\"radio-item\" @click=\"(formData.anxiousDay = '完全不会')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.anxiousDay === '完全不会' }\">\r\n                                    </view>\r\n                                    <text>完全不会</text>\r\n                                </view>\r\n                                <view class=\"radio-item\" @click=\"(formData.anxiousDay = '1~3天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.anxiousDay === '1~3天' }\">\r\n                                    </view>\r\n                                    <text>1~3天</text>\r\n                                </view>\r\n                            </view>\r\n                            <view class=\"radio-row\">\r\n                                <view class=\"radio-item\" @click=\"(formData.anxiousDay = '4~6天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.anxiousDay === '4~6天' }\">\r\n                                    </view>\r\n                                    <text>4~6天</text>\r\n                                </view>\r\n                                <view class=\"radio-item\" @click=\"(formData.anxiousDay = '几乎每天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.anxiousDay === '几乎每天' }\">\r\n                                    </view>\r\n                                    <text>几乎每天</text>\r\n                                </view>\r\n                            </view>\r\n                        </view>\r\n                    </view>\r\n\r\n                    <view class=\"form-item\">\r\n                        <text class=\"form-label\"><text class=\"required\">*</text>15.变得容易烦恼或急躁</text>\r\n                        <view class=\"radio-group\">\r\n                            <view class=\"radio-row\">\r\n                                <view class=\"radio-item\" @click=\"(formData.worryDay = '完全不会')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.worryDay === '完全不会' }\">\r\n                                    </view>\r\n                                    <text>完全不会</text>\r\n                                </view>\r\n                                <view class=\"radio-item\" @click=\"(formData.worryDay = '1~3天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.worryDay === '1~3天' }\">\r\n                                    </view>\r\n                                    <text>1~3天</text>\r\n                                </view>\r\n                            </view>\r\n                            <view class=\"radio-row\">\r\n                                <view class=\"radio-item\" @click=\"(formData.worryDay = '4~6天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.worryDay === '4~6天' }\">\r\n                                    </view>\r\n                                    <text>4~6天</text>\r\n                                </view>\r\n                                <view class=\"radio-item\" @click=\"(formData.worryDay = '几乎每天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.worryDay === '几乎每天' }\">\r\n                                    </view>\r\n                                    <text>几乎每天</text>\r\n                                </view>\r\n                            </view>\r\n                        </view>\r\n                    </view>\r\n\r\n                    <view class=\"form-item\">\r\n                        <text class=\"form-label\"><text class=\"required\">*</text>16.感到似乎有可怕的事发生而害怕</text>\r\n                        <view class=\"radio-group\">\r\n                            <view class=\"radio-row\">\r\n                                <view class=\"radio-item\" @click=\"(formData.scaredDay = '完全不会')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.scaredDay === '完全不会' }\">\r\n                                    </view>\r\n                                    <text>完全不会</text>\r\n                                </view>\r\n                                <view class=\"radio-item\" @click=\"(formData.scaredDay = '1~3天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.scaredDay === '1~3天' }\">\r\n                                    </view>\r\n                                    <text>1~3天</text>\r\n                                </view>\r\n                            </view>\r\n                            <view class=\"radio-row\">\r\n                                <view class=\"radio-item\" @click=\"(formData.scaredDay = '4~6天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.scaredDay === '4~6天' }\">\r\n                                    </view>\r\n                                    <text>4~6天</text>\r\n                                </view>\r\n                                <view class=\"radio-item\" @click=\"(formData.scaredDay = '几乎每天')\">\r\n                                    <view class=\"radio-btn\" :class=\"{ 'checked': formData.scaredDay === '几乎每天' }\">\r\n                                    </view>\r\n                                    <text>几乎每天</text>\r\n                                </view>\r\n                            </view>\r\n                        </view>\r\n                    </view>\r\n\r\n                    <view class=\"form-item\">\r\n                        <text class=\"form-label\"><text class=\"required\">*</text>17.您的地理位置</text>\r\n                        <view class=\"inline-form-item\">\r\n                            <!-- <button @click=\"getLocation\">选择位置</button> -->\r\n\r\n                            <view class=\"location-box\" @click=\"getLocation\">\r\n                                <text v-if=\"!formData.location\">点击获取位置</text>\r\n                                <text v-else>{{ formData.location }}</text>\r\n                                <view class=\"location-icon cuIcon-locationfill\"></view>\r\n                            </view>\r\n                        </view>\r\n                    </view>\r\n                </view>\r\n                <!-- 提交按钮 -->\r\n                <view class=\"submit-section\">\r\n                    <button class=\"submit-btn\" @click=\"submitForm\">{{ mode === 'add' ? '提交':'保存'}}</button>\r\n                </view>\r\n            </view>\r\n        </scroll-view>\r\n    </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { ref, computed, onMounted, watch } from 'vue'\r\nimport { fastLerp } from 'zrender/lib/tool/color'\r\nimport { useUserStore } from '@/store/user'\r\n\r\nconst userStore = useUserStore()\r\n\r\ndefineOptions({\r\n    name: 'psychologyForm',\r\n})\r\n// 获取页面参数\r\nconst query = ref<any>({})\r\nonMounted(() => {\r\n    const pages = getCurrentPages()\r\n    const currentPage = pages[pages.length - 1]\r\n    query.value = (currentPage as any).options || {}\r\n\r\n    // 如果是查看模式，加载数据\r\n    if (query.value.mode === 'view' && query.value.id) {\r\n        loadFormData(query.value.id)\r\n    }\r\n\r\n    // 自动填充患者姓名\r\n    if (userStore.userInfo.realname) {\r\n        formData.value.userName = userStore.userInfo.realname\r\n    }\r\n})\r\n\r\n// 表单模式：add 或 view\r\nconst mode = computed(() => query.value.mode || 'add')\r\n// const isViewMode = computed(() => mode.value === 'view')\r\n\r\n// 表单数据\r\nconst formData = ref({\r\n    userName: '',\r\n    apathyDay: '',\r\n    sadDay: '',\r\n    sleepDay: '',\r\n    tiredDay: '',\r\n    appetiteDay: '',\r\n    awfulDay: '',\r\n    focusedDay: '',\r\n    slowDay: '',\r\n    nervousDay: '',\r\n    concernDay: '',\r\n    stopDay: '',\r\n    relaxDay: '',\r\n    anxiousDay: '',\r\n    worryDay: '',\r\n    scaredDay: '',\r\n    location: '',\r\n})\r\n// 加载表单数据（根据ID获取数据）\r\nconst loadFormData = (id: string) => {\r\n    console.log('开始加载体征详情数据，ID:', id);\r\n    // 显示加载提示\r\n    uni.showLoading({\r\n        title: '加载中...'\r\n    })\r\n    \r\n    // 根据您提供的API路径格式构建URL\r\n    const url = `${import.meta.env.VITE_SERVER_BASEURL}/patient/${id}/psychologicaltable`\r\n    console.log('请求详情URL:', url)\r\n    \r\n    uni.request({\r\n        url,\r\n        method: 'GET',\r\n        success: (res: any) => {\r\n            const response = res.data\r\n            console.log('详情接口返回数据:', response)\r\n            \r\n            if (response && response.code === 200) {\r\n                // 解析返回的详细数据并填充表单\r\n                if (response.result) {\r\n                    formData.value = {\r\n                        userName: response.result.userName || '',\r\n                        apathyDay: response.result.apathyDay || '',\r\n                        sadDay: response.result.sadDay || '',\r\n                        sleepDay: response.result.sleepDay || '',\r\n                        tiredDay: response.result.tiredDay || '',\r\n                        appetiteDay: response.result.appetiteDay || '',\r\n                        awfulDay: response.result.awfulDay || '',\r\n                        focusedDay: response.result.focusedDay || '',\r\n                        slowDay: response.result.slowDay || '',\r\n                        nervousDay: response.result.nervousDay || '',\r\n                        concernDay: response.result.concernDay || '',\r\n                        stopDay: response.result.stopDay || '',\r\n                        relaxDay: response.result.relaxDay || '',\r\n                        anxiousDay: response.result.anxiousDay || '',\r\n                        worryDay: response.result.worryDay || '',\r\n                        scaredDay: response.result.scaredDay || '',\r\n                        location: response.result.location || '',\r\n                    }\r\n                    console.log('解析后的详情数据:', formData.value)\r\n                } else {\r\n                    console.log('没有找到详情数据')\r\n                    uni.showToast({\r\n                        title: '未找到记录详情',\r\n                        icon: 'none'\r\n                    })\r\n                }\r\n            } else {\r\n                uni.showToast({\r\n                    title: response.msg || '获取详情失败',\r\n                    icon: 'none'\r\n                })\r\n            }\r\n        },\r\n        fail: (err: any) => {\r\n            console.error('获取体征详情失败:', err)\r\n            uni.showToast({\r\n                title: '网络异常，请稍后重试',\r\n                icon: 'none'\r\n            })\r\n        },\r\n        complete: () => {\r\n            uni.hideLoading()\r\n        }\r\n    })\r\n}\r\n\r\n// 获取地理位置\r\nconst getLocation = () => {\r\n    // 使用腾讯位置选择器\r\n    uni.chooseLocation({\r\n        success: function(res) {\r\n            console.log('位置名称：' + res.name);\r\n            console.log('详细地址：' + res.address);\r\n            console.log('纬度：' + res.latitude);\r\n            console.log('经度：' + res.longitude);\r\n            \r\n            // 更新表单数据\r\n            formData.value.location = res.address;\r\n        },\r\n        fail: function(err) {\r\n            console.error('选择位置失败', err);\r\n            uni.showToast({\r\n                title: '选择位置失败，请检查定位权限',\r\n                icon: 'none'\r\n            });\r\n        }\r\n    });\r\n}\r\n\r\n// 提交表单\r\nconst submitForm = () => {\r\n\r\n    console.log('提交表单数据:', formData.value)\r\n\r\n    // 表单验证\r\n    if (!formData.value.userName) {\r\n        uni.showToast({\r\n            title: '请输入患者姓名',\r\n            icon: 'none'\r\n        })\r\n        return\r\n    }\r\n\r\n    if (!formData.value.apathyDay) {\r\n        uni.showToast({\r\n            title: '请选择做事时提不起劲或者没乐趣的天数',\r\n            icon: 'none'\r\n        })\r\n        return\r\n    }\r\n\r\n    if (!formData.value.sadDay) {\r\n        uni.showToast({\r\n            title: '请选择感到心情低落的天数',\r\n            icon: 'none'\r\n        })\r\n        return\r\n    }\r\n\r\n    if (!formData.value.sleepDay) {\r\n        uni.showToast({\r\n            title: '请选择入睡困难的天数',\r\n            icon: 'none'\r\n        })\r\n        return\r\n    }\r\n    if (!formData.value.tiredDay) {\r\n        uni.showToast({\r\n            title: '请选择感觉疲劳或没有活力的天数',\r\n            icon: 'none'\r\n        })\r\n        return\r\n    }\r\n    if (!formData.value.appetiteDay) {\r\n        uni.showToast({\r\n            title: '请选择食欲不振或吃太多的天数',\r\n            icon: 'none'\r\n        })\r\n        return\r\n    }\r\n    if (!formData.value.awfulDay) {\r\n        uni.showToast({\r\n            title: '请选择感觉自己很糟的天数',\r\n            icon: 'none'\r\n        })\r\n        return\r\n    }\r\n    if (!formData.value.focusedDay) {\r\n        uni.showToast({\r\n            title: '请选择对事物专注有困难的天数',\r\n            icon: 'none'\r\n        })\r\n        return\r\n    }\r\n    if (!formData.value.slowDay) {\r\n        uni.showToast({\r\n            title: '请选择动作或说话速度缓慢的天数',\r\n            icon: 'none'\r\n        })\r\n        return\r\n    }\r\n    if (!formData.value.nervousDay) {\r\n        uni.showToast({\r\n            title: '请选择感觉紧张，焦虑或急切的天数',\r\n            icon: 'none'\r\n        })\r\n        return\r\n    }\r\n    if (!formData.value.concernDay) {\r\n        uni.showToast({\r\n            title: '请选择对各种各样的事情担忧过多的天数',\r\n            icon: 'none'\r\n        })\r\n        return\r\n    }\r\n    if (!formData.value.stopDay) {\r\n        uni.showToast({\r\n            title: '请选择不能够停止或控制担忧的天数',\r\n            icon: 'none'\r\n        })\r\n        return\r\n    }\r\n    if (!formData.value.relaxDay) {\r\n        uni.showToast({\r\n            title: '请选择很难放松下来的天数',\r\n            icon: 'none'\r\n        })\r\n        return\r\n    }\r\n    if (!formData.value.anxiousDay) {\r\n        uni.showToast({\r\n            title: '请选择由于不安而无法静坐的天数',\r\n            icon: 'none'\r\n        })\r\n        return\r\n    }\r\n    if (!formData.value.worryDay) {\r\n        uni.showToast({\r\n            title: '请选择变得容易烦恼或急躁的天数',\r\n            icon: 'none'\r\n        })\r\n        return\r\n    }\r\n    if (!formData.value.scaredDay) {\r\n        uni.showToast({\r\n            title: '请选择似乎有可怕的事情发生而害怕天数',\r\n            icon: 'none'\r\n        })\r\n        return\r\n    }\r\n    \r\n    if (!formData.value.location) {\r\n        uni.showToast({\r\n            title: '请获取您的地理位置',\r\n            icon: 'none'\r\n        })\r\n        return\r\n    }\r\n\r\n    // 显示加载提示\r\n    uni.showLoading({\r\n        title: mode.value === 'add' ? '提交中...' : '保存中...'\r\n    })\r\n    const requestData = {\r\n        userName: formData.value.userName,\r\n        apathyDay: formData.value.apathyDay,\r\n        sadDay: formData.value.sadDay,\r\n        sleepDay: formData.value.sleepDay,\r\n        tiredDay: formData.value.tiredDay,\r\n        appetiteDay: formData.value.appetiteDay,\r\n        awfulDay: formData.value.awfulDay,\r\n        focusedDay: formData.value.focusedDay,\r\n        slowDay: formData.value.slowDay,\r\n        nervousDay: formData.value.nervousDay,\r\n        concernDay: formData.value.concernDay,\r\n        stopDay: formData.value.stopDay,\r\n        relaxDay: formData.value.relaxDay,\r\n        anxiousDay: formData.value.anxiousDay,\r\n        worryDay: formData.value.worryDay,\r\n        scaredDay: formData.value.scaredDay,\r\n        location: formData.value.location,\r\n        userId: userStore.userInfo.userid,\r\n    }\r\n\r\n    // 如果是查看模式，并且有id，则添加id到请求数据中\r\n    if (mode.value === 'view') {\r\n        requestData.id = query.value.id;\r\n        requestData.updateUserId = userStore.userInfo.userid\r\n    }\r\n\r\n    console.log('formData为：',formData.value);\r\n    console.log('requestData为',requestData);\r\n    \r\n    \r\n    //调用保存接口\r\n    uni.request({\r\n        url:`${import.meta.env.VITE_SERVER_BASEURL}/patient/savepsychologicaltable`,\r\n        method:'POST',\r\n        data: requestData,\r\n        success: (res) =>{\r\n            uni.hideLoading();\r\n\r\n            if (res.data?.success) {\r\n                uni.showModal({\r\n                    title: mode.value === 'add' ? '提交成功' : '保存成功',\r\n                    showCancel: false,\r\n                    success: () => {\r\n                        uni.navigateBack();\r\n                    }\r\n                });\r\n            } else {\r\n                const errorMsg = res.data?.message || '提交失败，未知错误';\r\n                uni.showModal({\r\n                    title: mode.value === 'add' ?'提交失败' : '保存失败',\r\n                    content: errorMsg,\r\n                    showCancel: false\r\n                });\r\n            }\r\n        },\r\n        fail: (err) => {\r\n            uni.hideLoading();\r\n            const errorMsg = err.errMsg || '网络错误，请稍后重试';\r\n            uni.showModal({\r\n                title: '提交失败',\r\n                content: errorMsg,\r\n                showCancel: false\r\n            });\r\n        }\r\n    })\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-scroll-view {\r\n    height: calc(100vh - 44px);\r\n    /* 减去导航栏高度 */\r\n    width: 100%;\r\n}\r\n\r\n.form-container {\r\n    padding: 20rpx;\r\n    padding-bottom: 120rpx;\r\n    /* 增加底部内边距，防止内容被遮挡 */\r\n\r\n    .form-header {\r\n        margin-bottom: 20rpx;\r\n        text-align: center;\r\n        /* 标题居中 */\r\n\r\n        .form-title {\r\n            font-size: 32rpx;\r\n            font-weight: bold;\r\n            color: #333;\r\n        }\r\n    }\r\n\r\n    .form-section {\r\n        background-color: #FFFFFF;\r\n        border-radius: 12rpx;\r\n        padding: 20rpx;\r\n        margin-bottom: 20rpx;\r\n\r\n        .form-item {\r\n            margin-bottom: 30rpx;\r\n\r\n            .form-label {\r\n                display: block;\r\n                font-size: 28rpx;\r\n                color: #333;\r\n                margin-bottom: 20rpx;\r\n            }\r\n\r\n            .indent-text {\r\n                text-indent: 2em;\r\n            }\r\n\r\n            .inline-form-item {\r\n                display: flex;\r\n                align-items: center;\r\n                margin-bottom: 20rpx;\r\n\r\n                .inline-form-label {\r\n                    font-size: 28rpx;\r\n                    color: #333;\r\n                    min-width: 160rpx;\r\n                }\r\n\r\n                .inline-form-input {\r\n                    flex: 1;\r\n                    background-color: #F7F7F7;\r\n                    border-radius: 8rpx;\r\n                    padding: 20rpx;\r\n                    font-size: 28rpx;\r\n                    color: #333;\r\n                    box-sizing: border-box;\r\n                    min-height: 80rpx;\r\n\r\n                    &:disabled {\r\n                        background-color: #F5F5F5;\r\n                        color: #666;\r\n                    }\r\n                }\r\n            }\r\n\r\n            .date-picker {\r\n                display: flex;\r\n                justify-content: space-between;\r\n                align-items: center;\r\n                background-color: #F7F7F7;\r\n                border-radius: 8rpx;\r\n                padding: 20rpx;\r\n                font-size: 28rpx;\r\n                color: #333;\r\n                box-sizing: border-box;\r\n                min-height: 80rpx;\r\n\r\n                .placeholder {\r\n                    color: #999;\r\n                }\r\n            }\r\n\r\n            .form-input,\r\n            .form-textarea {\r\n                width: 100%;\r\n                background-color: #F7F7F7;\r\n                border-radius: 8rpx;\r\n                padding: 20rpx;\r\n                font-size: 28rpx;\r\n                color: #333;\r\n                box-sizing: border-box;\r\n                min-height: 80rpx;\r\n\r\n                &:disabled {\r\n                    background-color: #F5F5F5;\r\n                    color: #666;\r\n                }\r\n            }\r\n\r\n            .form-textarea {\r\n                height: 180rpx;\r\n            }\r\n\r\n            .radio-group {\r\n                display: flex;\r\n                flex-direction: column;\r\n                gap: 30rpx;\r\n                /* 增加间距 */\r\n\r\n                .radio-row {\r\n                    display: flex;\r\n                    gap: 150rpx;\r\n                    /* 增加间距 */\r\n                    margin-left: 20rpx;\r\n                    /* 增加左边距，实现对齐 */\r\n                    justify-content: flex-start;\r\n                    /* 添加左对齐 */\r\n                }\r\n\r\n                .radio-item {\r\n                    display: flex;\r\n                    align-items: center;\r\n                    min-width: 180rpx;\r\n                    width: 240rpx;\r\n                    /* 确保宽度一致，增大宽度值以容纳更长的文本 */\r\n\r\n                    .radio-btn {\r\n                        width: 36rpx;\r\n                        height: 36rpx;\r\n                        border-radius: 50%;\r\n                        border: 2rpx solid #CCCCCC;\r\n                        margin-right: 10rpx;\r\n                        position: relative;\r\n\r\n                        &.checked {\r\n                            border-color: #07C160;\r\n\r\n                            &:after {\r\n                                content: '';\r\n                                position: absolute;\r\n                                width: 24rpx;\r\n                                height: 24rpx;\r\n                                background-color: #07C160;\r\n                                border-radius: 50%;\r\n                                top: 50%;\r\n                                left: 50%;\r\n                                transform: translate(-50%, -50%);\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    text {\r\n                        font-size: 28rpx;\r\n                        color: #333;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    // 添加省市区选择器样式\r\n    .area-picker-container {\r\n        display: flex;\r\n        flex-direction: column;\r\n        margin-bottom: 20rpx;\r\n\r\n        .inline-form-label {\r\n            font-size: 28rpx;\r\n            color: #333;\r\n            margin-bottom: 10rpx;\r\n            display: block;\r\n        }\r\n\r\n        .area-picker-wrapper {\r\n            width: 100%;\r\n            background-color: #F7F7F7;\r\n            border-radius: 8rpx;\r\n            box-sizing: border-box;\r\n            min-height: 80rpx;\r\n\r\n            :deep(.wd-picker__value) {\r\n                text-align: left;\r\n                color: #333;\r\n            }\r\n\r\n            :deep(.wd-picker__action) {\r\n                display: none;\r\n            }\r\n        }\r\n    }\r\n\r\n    // 行内选择器样式\r\n    .inline-picker-wrapper {\r\n        flex: 1;\r\n        width: 100%;\r\n    }\r\n\r\n    .required {\r\n        color: #FF0000;\r\n        margin-right: 4rpx;\r\n    }\r\n\r\n    .submit-section {\r\n        margin-top: 40rpx;\r\n        margin-bottom: 60rpx;\r\n        /* 增加底部间距 */\r\n\r\n        .submit-btn {\r\n            width: 100%;\r\n            background-color: #07C160;\r\n            color: #FFFFFF;\r\n            border-radius: 8rpx;\r\n            font-size: 32rpx;\r\n            padding: 20rpx 0;\r\n        }\r\n    }\r\n}\r\n\r\n.checkbox-group {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 30rpx;\r\n\r\n    .checkbox-row {\r\n        display: flex;\r\n        gap: 150rpx;\r\n        margin-left: 20rpx;\r\n        justify-content: flex-start;\r\n    }\r\n\r\n    .checkbox-item {\r\n        display: flex;\r\n        align-items: center;\r\n        min-width: 180rpx;\r\n        width: 240rpx;\r\n\r\n        .checkbox-btn {\r\n            width: 36rpx;\r\n            height: 36rpx;\r\n            border-radius: 4rpx;\r\n            border: 2rpx solid #CCCCCC;\r\n            margin-right: 10rpx;\r\n            position: relative;\r\n\r\n            &.checked {\r\n                border-color: #07C160;\r\n                background-color: #07C160;\r\n\r\n                &:after {\r\n                    content: '';\r\n                    position: absolute;\r\n                    width: 20rpx;\r\n                    height: 10rpx;\r\n                    border-left: 4rpx solid #FFFFFF;\r\n                    border-bottom: 4rpx solid #FFFFFF;\r\n                    top: 45%;\r\n                    left: 50%;\r\n                    transform: translate(-50%, -50%) rotate(-45deg);\r\n                }\r\n            }\r\n        }\r\n\r\n        text {\r\n            font-size: 28rpx;\r\n            color: #333;\r\n        }\r\n    }\r\n\r\n    .sub-form-item {\r\n        margin-left: 50rpx;\r\n        margin-top: -10rpx;\r\n        margin-bottom: 20rpx;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .sub-form-label {\r\n            font-size: 26rpx;\r\n            color: #666;\r\n            min-width: 180rpx;\r\n        }\r\n\r\n        .sub-form-input {\r\n            flex: 1;\r\n            background-color: #F7F7F7;\r\n            border-radius: 8rpx;\r\n            padding: 15rpx;\r\n            font-size: 26rpx;\r\n            color: #333;\r\n            min-height: 70rpx;\r\n            width: 100%;\r\n            box-sizing: border-box;\r\n        }\r\n\r\n        .picker {\r\n            flex: 1;\r\n            display: flex;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n            background-color: #F7F7F7;\r\n            border-radius: 8rpx;\r\n            padding: 15rpx;\r\n            font-size: 26rpx;\r\n            color: #333;\r\n            min-height: 70rpx;\r\n            width: 100%;\r\n            box-sizing: border-box;\r\n        }\r\n    }\r\n\r\n    /* 添加新的样式以支持两列布局 */\r\n    .sub-form-items-row {\r\n        display: flex;\r\n        margin-left: 50rpx;\r\n        gap: 20rpx;\r\n        margin-top: -10rpx;\r\n        margin-bottom: 20rpx;\r\n\r\n        .sub-form-item {\r\n            flex: 1;\r\n            width: 50%;\r\n            min-width: calc(50% - 10rpx);\r\n            /* 确保即使只有一个元素也占据一半宽度 */\r\n            margin: 0;\r\n            display: flex;\r\n            align-items: center;\r\n\r\n            .picker-container {\r\n                width: 100%;\r\n                flex: 1;\r\n                display: block;\r\n            }\r\n\r\n            .temp-picker {\r\n                width: 100%;\r\n                flex: 1;\r\n                display: block;\r\n            }\r\n\r\n            .sub-form-input {\r\n                flex: 1;\r\n                background-color: #F7F7F7;\r\n                border-radius: 8rpx;\r\n                padding: 15rpx;\r\n                font-size: 26rpx;\r\n                color: #333;\r\n                min-height: 70rpx;\r\n                width: 100%;\r\n                box-sizing: border-box;\r\n            }\r\n\r\n            .picker {\r\n                flex: 1;\r\n                display: flex;\r\n                justify-content: space-between;\r\n                align-items: center;\r\n                background-color: #F7F7F7;\r\n                border-radius: 8rpx;\r\n                padding: 15rpx;\r\n                font-size: 26rpx;\r\n                color: #333;\r\n                min-height: 70rpx;\r\n                width: 100%;\r\n                box-sizing: border-box;\r\n            }\r\n        }\r\n    }\r\n}\r\n.location-box {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    width: 100%;\r\n    background-color: #F7F7F7;\r\n    border-radius: 8rpx;\r\n    padding: 20rpx;\r\n    font-size: 28rpx;\r\n    color: #333;\r\n    box-sizing: border-box;\r\n    min-height: 80rpx;\r\n}\r\n\r\n.location-icon {\r\n    color: #07C160;\r\n    font-size: 36rpx;\r\n}\r\n</style>", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-data/psychology/form.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "ref", "onMounted", "computed", "uni"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4fA,UAAM,YAAYA,WAAAA,aAAa;AAMzB,UAAA,QAAQC,cAAS,IAAA,EAAE;AACzBC,kBAAAA,UAAU,MAAM;AACZ,YAAM,QAAQ,gBAAgB;AAC9B,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AACpC,YAAA,QAAS,YAAoB,WAAW,CAAC;AAG/C,UAAI,MAAM,MAAM,SAAS,UAAU,MAAM,MAAM,IAAI;AAClC,qBAAA,MAAM,MAAM,EAAE;AAAA,MAAA;AAI3B,UAAA,UAAU,SAAS,UAAU;AACpB,iBAAA,MAAM,WAAW,UAAU,SAAS;AAAA,MAAA;AAAA,IACjD,CACH;AAGD,UAAM,OAAOC,cAAAA,SAAS,MAAM,MAAM,MAAM,QAAQ,KAAK;AAIrD,UAAM,WAAWF,cAAAA,IAAI;AAAA,MACjB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,IAAA,CACb;AAEK,UAAA,eAAe,CAAC,OAAe;AACzB,cAAA,IAAI,kBAAkB,EAAE;AAEhCG,oBAAAA,MAAI,YAAY;AAAA,QACZ,OAAO;AAAA,MAAA,CACV;AAGD,YAAM,MAAM,GAAG,6BAAmC,YAAY,EAAE;AACxD,cAAA,IAAI,YAAY,GAAG;AAE3BA,oBAAAA,MAAI,QAAQ;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,QACR,SAAS,CAAC,QAAa;AACnB,gBAAM,WAAW,IAAI;AACb,kBAAA,IAAI,aAAa,QAAQ;AAE7B,cAAA,YAAY,SAAS,SAAS,KAAK;AAEnC,gBAAI,SAAS,QAAQ;AACjB,uBAAS,QAAQ;AAAA,gBACb,UAAU,SAAS,OAAO,YAAY;AAAA,gBACtC,WAAW,SAAS,OAAO,aAAa;AAAA,gBACxC,QAAQ,SAAS,OAAO,UAAU;AAAA,gBAClC,UAAU,SAAS,OAAO,YAAY;AAAA,gBACtC,UAAU,SAAS,OAAO,YAAY;AAAA,gBACtC,aAAa,SAAS,OAAO,eAAe;AAAA,gBAC5C,UAAU,SAAS,OAAO,YAAY;AAAA,gBACtC,YAAY,SAAS,OAAO,cAAc;AAAA,gBAC1C,SAAS,SAAS,OAAO,WAAW;AAAA,gBACpC,YAAY,SAAS,OAAO,cAAc;AAAA,gBAC1C,YAAY,SAAS,OAAO,cAAc;AAAA,gBAC1C,SAAS,SAAS,OAAO,WAAW;AAAA,gBACpC,UAAU,SAAS,OAAO,YAAY;AAAA,gBACtC,YAAY,SAAS,OAAO,cAAc;AAAA,gBAC1C,UAAU,SAAS,OAAO,YAAY;AAAA,gBACtC,WAAW,SAAS,OAAO,aAAa;AAAA,gBACxC,UAAU,SAAS,OAAO,YAAY;AAAA,cAC1C;AACQ,sBAAA,IAAI,aAAa,SAAS,KAAK;AAAA,YAAA,OACpC;AACH,sBAAQ,IAAI,UAAU;AACtBA,4BAAAA,MAAI,UAAU;AAAA,gBACV,OAAO;AAAA,gBACP,MAAM;AAAA,cAAA,CACT;AAAA,YAAA;AAAA,UACL,OACG;AACHA,0BAAAA,MAAI,UAAU;AAAA,cACV,OAAO,SAAS,OAAO;AAAA,cACvB,MAAM;AAAA,YAAA,CACT;AAAA,UAAA;AAAA,QAET;AAAA,QACA,MAAM,CAAC,QAAa;AACR,kBAAA,MAAM,aAAa,GAAG;AAC9BA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACT;AAAA,QACL;AAAA,QACA,UAAU,MAAM;AACZA,wBAAAA,MAAI,YAAY;AAAA,QAAA;AAAA,MACpB,CACH;AAAA,IACL;AAGA,UAAM,cAAc,MAAM;AAEtBA,oBAAAA,MAAI,eAAe;AAAA,QACf,SAAS,SAAS,KAAK;AACX,kBAAA,IAAI,UAAU,IAAI,IAAI;AACtB,kBAAA,IAAI,UAAU,IAAI,OAAO;AACzB,kBAAA,IAAI,QAAQ,IAAI,QAAQ;AACxB,kBAAA,IAAI,QAAQ,IAAI,SAAS;AAGxB,mBAAA,MAAM,WAAW,IAAI;AAAA,QAClC;AAAA,QACA,MAAM,SAAS,KAAK;AACR,kBAAA,MAAM,UAAU,GAAG;AAC3BA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACT;AAAA,QAAA;AAAA,MACL,CACH;AAAA,IACL;AAGA,UAAM,aAAa,MAAM;AAEb,cAAA,IAAI,WAAW,SAAS,KAAK;AAGjC,UAAA,CAAC,SAAS,MAAM,UAAU;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACT;AACD;AAAA,MAAA;AAGA,UAAA,CAAC,SAAS,MAAM,WAAW;AAC3BA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACT;AACD;AAAA,MAAA;AAGA,UAAA,CAAC,SAAS,MAAM,QAAQ;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACT;AACD;AAAA,MAAA;AAGA,UAAA,CAAC,SAAS,MAAM,UAAU;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACT;AACD;AAAA,MAAA;AAEA,UAAA,CAAC,SAAS,MAAM,UAAU;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACT;AACD;AAAA,MAAA;AAEA,UAAA,CAAC,SAAS,MAAM,aAAa;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACT;AACD;AAAA,MAAA;AAEA,UAAA,CAAC,SAAS,MAAM,UAAU;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACT;AACD;AAAA,MAAA;AAEA,UAAA,CAAC,SAAS,MAAM,YAAY;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACT;AACD;AAAA,MAAA;AAEA,UAAA,CAAC,SAAS,MAAM,SAAS;AACzBA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACT;AACD;AAAA,MAAA;AAEA,UAAA,CAAC,SAAS,MAAM,YAAY;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACT;AACD;AAAA,MAAA;AAEA,UAAA,CAAC,SAAS,MAAM,YAAY;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACT;AACD;AAAA,MAAA;AAEA,UAAA,CAAC,SAAS,MAAM,SAAS;AACzBA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACT;AACD;AAAA,MAAA;AAEA,UAAA,CAAC,SAAS,MAAM,UAAU;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACT;AACD;AAAA,MAAA;AAEA,UAAA,CAAC,SAAS,MAAM,YAAY;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACT;AACD;AAAA,MAAA;AAEA,UAAA,CAAC,SAAS,MAAM,UAAU;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACT;AACD;AAAA,MAAA;AAEA,UAAA,CAAC,SAAS,MAAM,WAAW;AAC3BA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACT;AACD;AAAA,MAAA;AAGA,UAAA,CAAC,SAAS,MAAM,UAAU;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACT;AACD;AAAA,MAAA;AAIJA,oBAAAA,MAAI,YAAY;AAAA,QACZ,OAAO,KAAK,UAAU,QAAQ,WAAW;AAAA,MAAA,CAC5C;AACD,YAAM,cAAc;AAAA,QAChB,UAAU,SAAS,MAAM;AAAA,QACzB,WAAW,SAAS,MAAM;AAAA,QAC1B,QAAQ,SAAS,MAAM;AAAA,QACvB,UAAU,SAAS,MAAM;AAAA,QACzB,UAAU,SAAS,MAAM;AAAA,QACzB,aAAa,SAAS,MAAM;AAAA,QAC5B,UAAU,SAAS,MAAM;AAAA,QACzB,YAAY,SAAS,MAAM;AAAA,QAC3B,SAAS,SAAS,MAAM;AAAA,QACxB,YAAY,SAAS,MAAM;AAAA,QAC3B,YAAY,SAAS,MAAM;AAAA,QAC3B,SAAS,SAAS,MAAM;AAAA,QACxB,UAAU,SAAS,MAAM;AAAA,QACzB,YAAY,SAAS,MAAM;AAAA,QAC3B,UAAU,SAAS,MAAM;AAAA,QACzB,WAAW,SAAS,MAAM;AAAA,QAC1B,UAAU,SAAS,MAAM;AAAA,QACzB,QAAQ,UAAU,SAAS;AAAA,MAC/B;AAGI,UAAA,KAAK,UAAU,QAAQ;AACX,oBAAA,KAAK,MAAM,MAAM;AACjB,oBAAA,eAAe,UAAU,SAAS;AAAA,MAAA;AAG1C,cAAA,IAAI,cAAa,SAAS,KAAK;AAC/B,cAAA,IAAI,gBAAe,WAAW;AAItCA,oBAAAA,MAAI,QAAQ;AAAA,QACR,KAAI,GAAG,6BAAmC;AAAA,QAC1C,QAAO;AAAA,QACP,MAAM;AAAA,QACN,SAAS,CAAC,QAAO;;AACbA,wBAAAA,MAAI,YAAY;AAEZ,eAAA,SAAI,SAAJ,mBAAU,SAAS;AACnBA,0BAAAA,MAAI,UAAU;AAAA,cACV,OAAO,KAAK,UAAU,QAAQ,SAAS;AAAA,cACvC,YAAY;AAAA,cACZ,SAAS,MAAM;AACXA,8BAAAA,MAAI,aAAa;AAAA,cAAA;AAAA,YACrB,CACH;AAAA,UAAA,OACE;AACG,kBAAA,aAAW,SAAI,SAAJ,mBAAU,YAAW;AACtCA,0BAAAA,MAAI,UAAU;AAAA,cACV,OAAO,KAAK,UAAU,QAAO,SAAS;AAAA,cACtC,SAAS;AAAA,cACT,YAAY;AAAA,YAAA,CACf;AAAA,UAAA;AAAA,QAET;AAAA,QACA,MAAM,CAAC,QAAQ;AACXA,wBAAAA,MAAI,YAAY;AACV,gBAAA,WAAW,IAAI,UAAU;AAC/BA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,UAAA,CACf;AAAA,QAAA;AAAA,MACL,CACH;AAAA,IACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC90BA,GAAG,WAAW,eAAe;"}