{"version": 3, "file": "i18n.js", "sources": ["../../../../../../../../../src/uni_modules/z-paging/components/z-paging/js/modules/i18n.js"], "sourcesContent": ["// [z-paging]i18n模块\r\nimport { initVueI18n } from '@dcloudio/uni-i18n'\r\nimport messages from '../../i18n/index.js'\r\nconst { t } = initVueI18n(messages)\r\n\r\nimport u from '.././z-paging-utils'\r\nimport c from '.././z-paging-constant'\r\nimport interceptor from '../z-paging-interceptor'\r\n\r\nconst language = uni.getSystemInfoSync().language;\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tlanguage\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\tfinalLanguage() {\r\n\t\t\ttry {\r\n\t\t\t\tconst local = uni.getLocale();\r\n\t\t\t\tconst language = this.language;\r\n\t\t\t\treturn local === 'auto' ? interceptor._handleLanguage2Local(language, this._language2Local(language)) : local;\r\n\t\t\t} catch (e) {\r\n\t\t\t\t// 如果获取系统本地语言异常，则默认返回中文，uni.getLocale在部分低版本HX或者cli中可能报找不到的问题\r\n\t\t\t\treturn 'zh-Hans';\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 最终的下拉刷新默认状态的文字\r\n\t\tfinalRefresherDefaultText() {\r\n\t\t\treturn this._getI18nText('zp.refresher.default', this.refresherDefaultText);\r\n\t\t},\r\n\t\t// 最终的下拉刷新下拉中的文字\r\n\t\tfinalRefresherPullingText() {\r\n\t\t\treturn this._getI18nText('zp.refresher.pulling', this.refresherPullingText);\r\n\t\t},\r\n\t\t// 最终的下拉刷新中文字\r\n\t\tfinalRefresherRefreshingText() {\r\n\t\t\treturn this._getI18nText('zp.refresher.refreshing', this.refresherRefreshingText);\r\n\t\t},\r\n\t\t// 最终的下拉刷新完成文字\r\n\t\tfinalRefresherCompleteText() {\r\n\t\t\treturn this._getI18nText('zp.refresher.complete', this.refresherCompleteText);\r\n\t\t},\r\n\t\t// 最终的下拉刷新上次更新时间文字\r\n\t\tfinalRefresherUpdateTimeTextMap() {\r\n\t\t\treturn {\r\n\t\t\t\ttitle: t('zp.refresherUpdateTime.title'),\r\n\t\t\t\tnone: t('zp.refresherUpdateTime.none'),\r\n\t\t\t\ttoday: t('zp.refresherUpdateTime.today'),\r\n\t\t\t\tyesterday: t('zp.refresherUpdateTime.yesterday')\r\n\t\t\t};\r\n\t\t},\r\n\t\t// 最终的继续下拉进入二楼文字\r\n\t\tfinalRefresherGoF2Text() {\r\n\t\t\treturn this._getI18nText('zp.refresher.f2', this.refresherGoF2Text);\r\n\t\t},\r\n\t\t// 最终的底部加载更多默认状态文字\r\n\t\tfinalLoadingMoreDefaultText() {\r\n\t\t\treturn this._getI18nText('zp.loadingMore.default', this.loadingMoreDefaultText);\r\n\t\t},\r\n\t\t// 最终的底部加载更多加载中文字\r\n\t\tfinalLoadingMoreLoadingText() {\r\n\t\t\treturn this._getI18nText('zp.loadingMore.loading', this.loadingMoreLoadingText);\r\n\t\t},\r\n\t\t// 最终的底部加载更多没有更多数据文字\r\n\t\tfinalLoadingMoreNoMoreText() {\r\n\t\t\treturn this._getI18nText('zp.loadingMore.noMore', this.loadingMoreNoMoreText);\r\n\t\t},\r\n\t\t// 最终的底部加载更多加载失败文字\r\n\t\tfinalLoadingMoreFailText() {\r\n\t\t\treturn this._getI18nText('zp.loadingMore.fail', this.loadingMoreFailText);\r\n\t\t},\r\n\t\t// 最终的空数据图title\r\n\t\tfinalEmptyViewText() {\r\n\t\t\treturn this.isLoadFailed ? this.finalEmptyViewErrorText : this._getI18nText('zp.emptyView.title', this.emptyViewText);\r\n\t\t},\r\n\t\t// 最终的空数据图reload title\r\n\t\tfinalEmptyViewReloadText() {\r\n\t\t\treturn this._getI18nText('zp.emptyView.reload', this.emptyViewReloadText);\r\n\t\t},\r\n\t\t// 最终的空数据图加载失败文字\r\n\t\tfinalEmptyViewErrorText() {\r\n\t\t\treturn this.customerEmptyViewErrorText || this._getI18nText('zp.emptyView.error', this.emptyViewErrorText);\r\n\t\t},\r\n\t\t// 最终的系统loading title\r\n\t\tfinalSystemLoadingText() {\r\n\t\t\treturn this._getI18nText('zp.systemLoading.title', this.systemLoadingText);\r\n\t\t},\r\n\t},\r\n\tmethods: {\r\n\t\t// 获取当前z-paging的语言\r\n\t\tgetLanguage() {\r\n\t\t\treturn this.finalLanguage;\r\n\t\t},\r\n\t\t// 获取国际化转换后的文本\r\n\t\t_getI18nText(key, value) {\r\n\t\t\tconst dataType = Object.prototype.toString.call(value);\r\n\t\t\tif (dataType === '[object Object]') {\r\n\t\t\t\tconst nextValue = value[this.finalLanguage];\r\n\t\t\t\tif (nextValue) return nextValue;\r\n\t\t\t} else if (dataType === '[object String]') {\r\n\t\t\t\treturn value;\r\n\t\t\t}\r\n\t\t\treturn t(key);\r\n\t\t},\r\n\t\t// 系统language转i18n local\r\n\t\t_language2Local(language) {\r\n\t\t\tconst formatedLanguage = language.toLowerCase().replace(new RegExp('_', ''), '-');\r\n\t\t\tif (formatedLanguage.indexOf('zh') !== -1) {\r\n\t\t\t\tif (formatedLanguage === 'zh' || formatedLanguage === 'zh-cn' || formatedLanguage.indexOf('zh-hans') !== -1) {\r\n\t\t\t\t\treturn 'zh-Hans';\r\n\t\t\t\t}\r\n\t\t\t\treturn 'zh-Hant';\r\n\t\t\t}\r\n\t\t\tif (formatedLanguage.indexOf('en') !== -1) return 'en';\r\n\t\t\treturn language;\r\n\t\t}\r\n\t}\r\n}\r\n"], "names": ["initVueI18n", "messages", "uni", "language", "interceptor"], "mappings": ";;;;AAGA,MAAM,EAAE,EAAC,IAAKA,cAAW,YAACC,0DAAQ;AAMlC,MAAM,WAAWC,cAAG,MAAC,kBAAmB,EAAC;AACzC,MAAe,aAAA;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN;AAAA,IACA;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACT,gBAAgB;AACf,UAAI;AACH,cAAM,QAAQA,oBAAI;AAClB,cAAMC,YAAW,KAAK;AACtB,eAAO,UAAU,SAASC,6DAAW,YAAC,sBAAsBD,WAAU,KAAK,gBAAgBA,SAAQ,CAAC,IAAI;AAAA,MACxG,SAAQ,GAAG;AAEX,eAAO;AAAA,MACP;AAAA,IACD;AAAA;AAAA,IAED,4BAA4B;AAC3B,aAAO,KAAK,aAAa,wBAAwB,KAAK,oBAAoB;AAAA,IAC1E;AAAA;AAAA,IAED,4BAA4B;AAC3B,aAAO,KAAK,aAAa,wBAAwB,KAAK,oBAAoB;AAAA,IAC1E;AAAA;AAAA,IAED,+BAA+B;AAC9B,aAAO,KAAK,aAAa,2BAA2B,KAAK,uBAAuB;AAAA,IAChF;AAAA;AAAA,IAED,6BAA6B;AAC5B,aAAO,KAAK,aAAa,yBAAyB,KAAK,qBAAqB;AAAA,IAC5E;AAAA;AAAA,IAED,kCAAkC;AACjC,aAAO;AAAA,QACN,OAAO,EAAE,8BAA8B;AAAA,QACvC,MAAM,EAAE,6BAA6B;AAAA,QACrC,OAAO,EAAE,8BAA8B;AAAA,QACvC,WAAW,EAAE,kCAAkC;AAAA,MACnD;AAAA,IACG;AAAA;AAAA,IAED,yBAAyB;AACxB,aAAO,KAAK,aAAa,mBAAmB,KAAK,iBAAiB;AAAA,IAClE;AAAA;AAAA,IAED,8BAA8B;AAC7B,aAAO,KAAK,aAAa,0BAA0B,KAAK,sBAAsB;AAAA,IAC9E;AAAA;AAAA,IAED,8BAA8B;AAC7B,aAAO,KAAK,aAAa,0BAA0B,KAAK,sBAAsB;AAAA,IAC9E;AAAA;AAAA,IAED,6BAA6B;AAC5B,aAAO,KAAK,aAAa,yBAAyB,KAAK,qBAAqB;AAAA,IAC5E;AAAA;AAAA,IAED,2BAA2B;AAC1B,aAAO,KAAK,aAAa,uBAAuB,KAAK,mBAAmB;AAAA,IACxE;AAAA;AAAA,IAED,qBAAqB;AACpB,aAAO,KAAK,eAAe,KAAK,0BAA0B,KAAK,aAAa,sBAAsB,KAAK,aAAa;AAAA,IACpH;AAAA;AAAA,IAED,2BAA2B;AAC1B,aAAO,KAAK,aAAa,uBAAuB,KAAK,mBAAmB;AAAA,IACxE;AAAA;AAAA,IAED,0BAA0B;AACzB,aAAO,KAAK,8BAA8B,KAAK,aAAa,sBAAsB,KAAK,kBAAkB;AAAA,IACzG;AAAA;AAAA,IAED,yBAAyB;AACxB,aAAO,KAAK,aAAa,0BAA0B,KAAK,iBAAiB;AAAA,IACzE;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAER,cAAc;AACb,aAAO,KAAK;AAAA,IACZ;AAAA;AAAA,IAED,aAAa,KAAK,OAAO;AACxB,YAAM,WAAW,OAAO,UAAU,SAAS,KAAK,KAAK;AACrD,UAAI,aAAa,mBAAmB;AACnC,cAAM,YAAY,MAAM,KAAK,aAAa;AAC1C,YAAI;AAAW,iBAAO;AAAA,MAC1B,WAAc,aAAa,mBAAmB;AAC1C,eAAO;AAAA,MACP;AACD,aAAO,EAAE,GAAG;AAAA,IACZ;AAAA;AAAA,IAED,gBAAgBA,WAAU;AACzB,YAAM,mBAAmBA,UAAS,YAAW,EAAG,QAAQ,IAAI,OAAO,KAAK,EAAE,GAAG,GAAG;AAChF,UAAI,iBAAiB,QAAQ,IAAI,MAAM,IAAI;AAC1C,YAAI,qBAAqB,QAAQ,qBAAqB,WAAW,iBAAiB,QAAQ,SAAS,MAAM,IAAI;AAC5G,iBAAO;AAAA,QACP;AACD,eAAO;AAAA,MACP;AACD,UAAI,iBAAiB,QAAQ,IAAI,MAAM;AAAI,eAAO;AAClD,aAAOA;AAAA,IACP;AAAA,EACD;AACF;;"}