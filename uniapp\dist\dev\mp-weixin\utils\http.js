"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../common/vendor.js");
const store_user = require("../store/user.js");
const utils_signMd5Utils = require("./signMd5Utils.js");
const utils_platform = require("./platform.js");
const http = (options) => {
  return new Promise((resolve, reject) => {
    const userStore = store_user.useUserStore();
    let params = options.query;
    if (options.data && Object.keys(options.data).length > 0) {
      params = Object.assign({}, options == null ? void 0 : options.query, options == null ? void 0 : options.data);
    }
    let sign = utils_signMd5Utils.signMd5Utils.getSign(options.url, params);
    let vSign = utils_signMd5Utils.signMd5Utils.getVSign(options.data, sign);
    if (JSON.parse("true") && JSON.parse("true") && utils_platform.isH5)
      ;
    common_vendor.index.request(__spreadProps(__spreadValues({
      dataType: "json",
      header: {
        "X-Access-Token": userStore.userInfo.token,
        "X-Tenant-Id": userStore.userInfo.tenantId,
        "X-Sign": sign,
        "V-Sign": vSign,
        "X-TIMESTAMP": utils_signMd5Utils.signMd5Utils.getTimestamp()
      }
    }, options), {
      // 响应成功
      success(res) {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res.data);
        } else {
          switch (res.statusCode) {
            case 401:
              userStore.clearUserInfo();
              common_vendor.index.navigateTo({ url: "/pages/login/login" });
              break;
            default:
              !options.hideErrorToast && common_vendor.index.showToast({
                icon: "none",
                title: res.data.msg || "请求错误"
              });
          }
          common_vendor.index.$emit("z-paging-error-emit");
          reject(res);
        }
      },
      // 响应失败
      fail(err) {
        common_vendor.index.showToast({
          icon: "none",
          title: "网络错误，换个网络试试"
        });
        reject(err);
      }
    }));
  });
};
const httpGet = (url, query, header) => {
  return http({
    url,
    query,
    method: "GET"
  });
};
const httpPost = (url, data, query) => {
  return http({
    url,
    query,
    data,
    method: "POST"
  });
};
const httpPUT = (url, data, query) => {
  return http({
    url,
    query,
    data,
    method: "PUT"
  });
};
const httpDelete = (url, query, header) => {
  return http({
    url,
    query,
    method: "DELETE"
  });
};
http.get = httpGet;
http.post = httpPost;
http.put = httpPUT;
http.delete = httpDelete;
exports.http = http;
//# sourceMappingURL=http.js.map
