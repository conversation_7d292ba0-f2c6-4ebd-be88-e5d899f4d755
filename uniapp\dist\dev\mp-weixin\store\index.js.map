{"version": 3, "file": "index.js", "sources": ["../../../../src/store/index.ts"], "sourcesContent": ["import { createPinia } from 'pinia'\r\nimport { createPersistedState } from 'pinia-plugin-persistedstate' // 数据持久化\r\n\r\nconst store = createPinia()\r\nstore.use(\r\n  createPersistedState({\r\n    storage: {\r\n      getItem: uni.getStorageSync,\r\n      setItem: uni.setStorageSync,\r\n    },\r\n  }),\r\n)\r\n\r\nexport default store\r\n\r\n// 模块统一导出\r\nexport * from './user'\r\n"], "names": ["createPinia", "createPersistedState", "uni"], "mappings": ";;;AAGA,MAAM,QAAQA,cAAY,YAAA;AAC1B,MAAM;AAAA,EACJC,mCAAqB;AAAA,IACnB,SAAS;AAAA,MACP,SAASC,cAAI,MAAA;AAAA,MACb,SAASA,cAAAA,MAAI;AAAA,IAAA;AAAA,EAEhB,CAAA;AACH;;"}