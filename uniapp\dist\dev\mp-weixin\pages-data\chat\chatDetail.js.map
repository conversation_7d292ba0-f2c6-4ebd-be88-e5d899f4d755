{"version": 3, "file": "chatDetail.js", "sources": ["../../../../../src/pages-data/chat/chatDetail.vue", "../../../../../uniPage:/cGFnZXMtZGF0YVxjaGF0XGNoYXREZXRhaWwudnVl"], "sourcesContent": ["\r\n<template>\n<layout-default-uni >\r\n  <view class=\"chat-detail-page\">\r\n    <PageLayout :navLeftArrow=\"true\" navLeftText=\"返回\">\r\n        <view class=\"page-wrapper\">\r\n            <scroll-view class=\"page-scroll-view\" scroll-y=\"true\">\r\n            <!-- 重新设计的患者问题卡片 -->\r\n            <view class=\"patient-question-card\">\r\n              <!-- 患者信息头部 -->\r\n              <view class=\"patient-header\">\r\n                <img class=\"patient-avatar\" :src=\"getAvatarUrl(searchParams.problem.patientAvatar)\" alt=\"头像\" @error=\"handleAvatarError\"/>\r\n                <view class=\"patient-info\">\r\n                  <text class=\"patient-name\">{{ searchParams.problem.patientName }}</text>\r\n                  <text class=\"question-time\">{{ formatTime(searchParams.problem.createTime || searchParams.problem.updateTime || '-') }}</text>\r\n                </view>\r\n              </view>\r\n\r\n              <!-- 问题标题区域 -->\r\n              <view v-if=\"searchParams.problem.title && searchParams.problem.title.trim()\" class=\"title-section\">\r\n                <text class=\"title-text\">{{ searchParams.problem.title }}</text>\r\n              </view>\r\n\r\n              <!-- 问题内容区域 -->\r\n              <view class=\"content-section\">\r\n                <view class=\"content-header\">\r\n                  <text class=\"content-label\">问题描述</text>\r\n                </view>\r\n                <text class=\"content-text\">{{ searchParams.problem.question }}</text>\r\n              </view>\r\n\r\n\r\n\r\n              <!-- 问题图片区域 - 简化版本 -->\r\n              <view v-if=\"problemImages.length > 0\" class=\"images-section\">\r\n                <view class=\"images-header\">\r\n                  <text class=\"images-label\">相关图片 ({{ problemImages.length }})</text>\r\n                </view>\r\n                <view class=\"images-grid\">\r\n                  <view\r\n                    v-for=\"(img, index) in problemImages\"\r\n                    :key=\"index\"\r\n                    class=\"image-item\"\r\n                    @click=\"previewSimpleImage(img)\"\r\n                  >\r\n                    <image\r\n                      class=\"question-image\"\r\n                      :src=\"img\"\r\n                      mode=\"aspectFill\"\r\n                      @error=\"handleSimpleImageError\"\r\n                    />\r\n                    <!-- 调试信息 -->\r\n                    <view class=\"image-debug\">\r\n                      <text>{{ img }}</text>\r\n                    </view>\r\n                  </view>\r\n                </view>\r\n              </view>\r\n\r\n\r\n\r\n              <!-- @医生信息区域 -->\r\n              <view v-if=\"mentionedDoctors && mentionedDoctors.length > 0\" class=\"mentioned-section\">\r\n                <view class=\"mentioned-doctors-list\">\r\n                  <text\r\n                    v-for=\"(doctor, index) in mentionedDoctors\"\r\n                    :key=\"doctor.userId || doctor.id || index\"\r\n                    class=\"doctor-tag\"\r\n                  >\r\n                    @{{ doctor.realname || doctor.realName || doctor.username || '医生' }}\r\n                  </text>\r\n                </view>\r\n              </view>\r\n\r\n\r\n            </view>\r\n            <!-- 回复 -->\r\n            <view class=\"message-container\">\r\n              <text class=\"message-title\">回复</text>\r\n              <view v-if=\"reply && reply.length >0\"> \r\n                <view class=\"reply\" v-for=\"(rep, index) in reply\" :key=\"index\" @click=\"setReplyTarget(rep)\">\r\n                  <image class=\"reply-avatar\" :src=\"getAvatarUrl(rep.userAvatar)\" alt=\"头像\" @error=\"handleAvatarError\"/>\r\n                  <view class=\"reply-part\">\r\n                    <view class=\"reply-name-container\">\r\n                      <text class=\"reply-name\" v-if=\"rep.userCategory === 0\">{{ rep.userName || '未知用户' }}医生</text>\r\n                      <text class=\"reply-name\" v-else>{{ rep.userName || '未知用户' }}</text>\r\n                    </view>\r\n        \r\n                    <view v-if=\"rep.replyUserId && rep.replyUserId !== '' && rep.replyUserId !== null\" class=\"reply-mention\">\r\n                      <text class=\"mention-text\">@{{ findUserNameById(rep.replyUserId) || '未知用户' }}：{{ getSafeReplyContent(rep.replyContent) || getReplyTargetText(rep.replyId, rep) || '原回复内容' }}</text>\r\n                    </view>\r\n\r\n                    <text class=\"reply-content\">{{ rep.text || '内容为空' }}</text>\r\n\r\n                    <!-- 显示回复的图片 -->\r\n                    <view v-if=\"getImageList(rep.image).length > 0\" class=\"reply-images\">\r\n                      <!-- 调试信息：显示图片数量和原始数据 -->\r\n                      <text class=\"debug-info\" style=\"font-size: 12px; color: #999; margin-bottom: 5px;\">\r\n                        图片 ({{ getImageList(rep.image).length }}) - 原始: {{ rep.image }}\r\n                      </text>\r\n                      <view\r\n                          v-for=\"(img, imgIndex) in getImageList(rep.image)\"\r\n                          :key=\"imgIndex\"\r\n                          class=\"reply-image-container\"\r\n                      >\r\n                        <image\r\n                          class=\"reply-image\"\r\n                          :src=\"img.startsWith('http') ? img : getReplyImageUrl(img)\"\r\n                          mode=\"aspectFill\"\r\n                          @click.stop=\"previewReplyImage(img, getImageList(rep.image))\"\r\n                          @error=\"handleSimpleImageError\"\r\n                        />\r\n                        <!-- 调试信息：显示最终URL -->\r\n                        <text class=\"debug-url\" style=\"font-size: 10px; color: #666;\">\r\n                          {{ img.startsWith('http') ? img : getReplyImageUrl(img) }}\r\n                        </text>\r\n                      </view>\r\n                    </view>\r\n                  </view>\r\n\r\n                  <!-- 删除按钮，只有当前用户的评论才显示 -->\r\n                  <view v-if=\"rep.userId === userStore.userInfo.userid\" class=\"delete-button\" @click.stop=\"deleteReply(rep)\">\r\n                    <view class=\"trash-icon\">\r\n                      <view class=\"trash-lid\"></view>\r\n                      <view class=\"trash-body\">\r\n                        <view class=\"trash-line\"></view>\r\n                        <view class=\"trash-line\"></view>\r\n                        <view class=\"trash-line\"></view>\r\n                      </view>\r\n                    </view>\r\n                  </view>\r\n\r\n                  <!-- 时间显示在右下角，在删除按钮下面 -->\r\n                  <text class=\"chat-time\">{{ formatTime(rep.createTime || rep.updateTime || '-') }}</text>\r\n                </view>\r\n              </view>\r\n              <view v-else class=\"empty-reply-container\">\r\n                <view class=\"empty-icon\">💬</view>\r\n                <text class=\"message-empty\">暂无回复</text>\r\n                <text class=\"empty-subtitle\">成为第一个回复的人吧</text>\r\n              </view>\r\n\r\n            </view>\r\n        </scroll-view>\r\n        </view>\r\n    </PageLayout>\r\n\r\n    <!-- 回复窗口 - 移到PageLayout外面 -->\r\n    <view class=\"reply-window-fixed\">\r\n      <!-- @回复提示框紧贴在输入框上方 -->\r\n      <view v-if=\"replyTarget\" class=\"reply-tip-above-input\">\r\n        <span class=\"reply-content-text\">@{{ replyTarget.userName }}：{{ truncatedReplyText }}</span>\r\n        <span class=\"cancel-reply\" @click=\"replyTarget = null\">取消</span>\r\n      </view>\r\n\r\n      <view class=\"reply-input-row\">\r\n        <image class=\"reply-avatar\" :src=\"avatarSrc\" alt=\"头像\" @error=\"handleAvatarError\"/>\r\n        <view class=\"reply-input-area\">\r\n          <textarea\r\n          ref=\"replyTextarea\"\r\n          class=\"reply-textarea\"\r\n          v-model=\"replyContent\"\r\n          placeholder=\"请输入回复内容\"\r\n          :focus=\"textareaFocus\"\r\n          :auto-height=\"true\"\r\n          :fixed=\"true\"\r\n          :show-confirm-bar=\"false\"\r\n        ></textarea>\r\n        </view>\r\n        <image src=\"https://www.mograine.cn/images/upload_picture.jpg\" class=\"upload-button\" @click=\"chooseImage\" />\r\n        <button class=\"sub-reply\" :class=\"{ 'disabled': !canSubmit }\" @click=\"submitReply\" :disabled=\"!canSubmit\">提交</button>\r\n      </view>\r\n\r\n      <!-- 显示选择的多张图片，独立一行 -->\r\n      <view v-if=\"replyImages.length > 0\" class=\"reply-input-images\">\r\n        <view\r\n          v-for=\"(img, index) in replyImages\"\r\n          :key=\"index\"\r\n          class=\"reply-input-image\"\r\n          @click=\"previewImage(img)\"\r\n        >\r\n          <image class=\"input-image-preview\" :src=\"img\" mode=\"aspectFill\" />\r\n          <view class=\"remove-image\" @click.stop=\"removeImageByIndex(index)\">×</view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n\r\n  </view>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { ref, onMounted, computed, nextTick } from 'vue';\r\nconst replyTextarea = ref<HTMLTextAreaElement | null>(null);\r\nimport { http } from '@/utils/http';\r\nimport { onLoad } from '@dcloudio/uni-app';\r\nimport { useUserStore } from '@/store/user'; // 请根据你的实际 store 路径调整\r\n\r\nconst userStore = useUserStore();\r\nconst replyContent = ref('');\r\nconst replyImages = ref<string[]>([]); // 改为数组，支持多张图片\r\nconst loadingChat = ref(false);\r\n// 问题图片 - 简化版本，使用相对路径\r\nconst problemImages = ref<string[]>([]); // 直接存储完整的图片URL\r\n\r\n// 简化的图片错误处理\r\nconst handleSimpleImageError = (event: any) => {\r\n  try {\r\n    const currentSrc = event?.target?.src;\r\n    console.log('📷 图片加载失败:', currentSrc);\r\n\r\n    // 设置默认图片\r\n    event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjVGNUY1Ii8+Cjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIiBmaWxsPSIjOTk5OTk5IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7lm77niYfliqDovb3lpLHotKU8L3RleHQ+Cjwvc3ZnPg==';\r\n  } catch (error) {\r\n    console.error('📷 图片错误处理失败:', error);\r\n  }\r\n};\r\n\r\n// 简单的图片预览\r\nconst previewSimpleImage = (url: string) => {\r\n  handleImagePreview(url, problemImages.value);\r\n};\r\nconst reply = ref<any[]>([]); // 回复列表\r\nconst communicationId = ref('');\r\nconst mentionedDoctors = ref<any[]>([]); // @的医生列表\r\n// 默认头像，提供多个备选方案\r\nconst defAvatar = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNGNUY1RjUiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iIzk5OTk5OSIvPgo8cGF0aCBkPSJNMzAgMzJDMzAgMjYuNDc3MSAyNS41MjI5IDIyIDIwIDIyQzE0LjQ3NzEgMjIgMTAgMjYuNDc3MSAxMCAzMkgzMFoiIGZpbGw9IiM5OTk5OTkiLz4KPC9zdmc+'\r\nconst replyTarget = ref<any | null>(null);\r\nconst textareaFocus = ref(false); // 用于控制输入框的聚焦状态\r\n\r\n\r\n\r\n\r\n\r\nonLoad((options) => {\r\n  console.log('页面加载时的参数:', options);\r\n  communicationId.value = options.communication_id || '';\r\n  console.log('communicationId:', communicationId.value);\r\n  \r\n});\r\n\r\n// 通用头像处理函数\r\nconst getAvatarUrl = (avatar: string | null | undefined) => {\r\n  // 如果头像为空、null、undefined或空字符串，返回默认头像\r\n  if (!avatar || avatar.trim() === '') {\r\n    return defAvatar;\r\n  }\r\n\r\n  // 检查是否已经是完整URL\r\n  if (avatar.startsWith('http')) {\r\n    return avatar;\r\n  }\r\n\r\n  // 如果是相对路径，拼接基础URL\r\n  if (avatar.startsWith('/')) {\r\n    return 'https://www.mograine.cn' + avatar;\r\n  }\r\n\r\n  // 如果是文件名，拼接完整路径\r\n  return 'https://www.mograine.cn/images/' + avatar;\r\n}\r\n\r\n// 头像加载错误处理\r\nconst handleAvatarError = (event: any) => {\r\n  event.target.src = defAvatar;\r\n}\r\n\r\n\r\n\r\n// 计算属性：返回最终显示的头像URL\r\nconst avatarSrc = computed(() => {\r\n  console.log('🧑 用户信息:', userStore.userInfo);\r\n  console.log('🧑 用户头像字段:', userStore.userInfo.avatar);\r\n  const avatarUrl = getAvatarUrl(userStore.userInfo.avatar);\r\n  console.log('🧑 最终头像URL:', avatarUrl);\r\n  return avatarUrl;\r\n})\r\n\r\n// 计算属性：判断是否可以提交\r\nconst canSubmit = computed(() => {\r\n  return replyContent.value.trim().length > 0 && !isSubmitting.value;\r\n})\r\n\r\n\r\n\r\n// 截断回复文本，限制显示长度\r\nconst truncatedReplyText = computed(() => {\r\n  if (!replyTarget.value) {\r\n    return '';\r\n  }\r\n\r\n  // 如果有文本内容，显示截断的文本\r\n  if (replyTarget.value.text && replyTarget.value.text.trim() !== '') {\r\n    return replyTarget.value.text.length > 30\r\n      ? replyTarget.value.text.substring(0, 30) + '...'\r\n      : replyTarget.value.text;\r\n  }\r\n\r\n  // 如果没有文本但有图片，显示[图片]\r\n  if (replyTarget.value.image && replyTarget.value.image.trim() !== '') {\r\n    return '[图片]';\r\n  }\r\n\r\n  // 既没有文本也没有图片\r\n  return '原回复内容';\r\n})\r\n\r\n// 根据用户ID查找用户名\r\nconst findUserNameById = (userId: string) => {\r\n  if (!userId || userId === \"\") {\r\n    return null;\r\n  }\r\n\r\n  console.log('查找用户ID:', userId);\r\n  console.log('当前回复列表:', reply.value);\r\n\r\n  // 先在回复列表中查找\r\n  const foundInReply = reply.value.find(r => r.userId === userId);\r\n  if (foundInReply) {\r\n    console.log('在回复列表中找到用户:', foundInReply.userName);\r\n    return foundInReply.userName;\r\n  }\r\n\r\n  // 如果在回复中没找到，检查是否是患者\r\n  // 注意：这里可能需要根据实际的患者userId来判断\r\n  if (searchParams.value.problem && searchParams.value.problem.patientName) {\r\n    console.log('未在回复中找到，返回患者名称作为备选:', searchParams.value.problem.patientName);\r\n    return searchParams.value.problem.patientName;\r\n  }\r\n\r\n  console.log('未找到用户，返回null');\r\n  return null;\r\n}\r\n\r\n// 根据回复ID获取被回复的内容文本（截断显示）\r\nconst getReplyTargetText = (replyId: string, currentReply: any) => {\r\n  console.log('🔍 查找被回复内容');\r\n  console.log('  - replyId:', replyId);\r\n  console.log('  - currentReply:', currentReply);\r\n  console.log('  - currentReply.replyContent:', currentReply.replyContent);\r\n\r\n  if (!replyId || replyId === \"\") {\r\n    console.log('❌ replyId为空');\r\n    return '原回复内容';\r\n  }\r\n\r\n  // 首先检查当前回复是否有replyContent对象，优先使用这个数据\r\n  if (currentReply && currentReply.replyContent) {\r\n    console.log('🔍 检查当前回复的replyContent对象:', currentReply.replyContent);\r\n\r\n    // 如果replyContent是对象且有text字段\r\n    if (typeof currentReply.replyContent === 'object' && currentReply.replyContent.text) {\r\n      const replyText = currentReply.replyContent.text.trim();\r\n      if (replyText && replyText !== '') {\r\n        const truncated = replyText.length > 30\r\n          ? replyText.substring(0, 30) + '...'\r\n          : replyText;\r\n        console.log('✅ 从replyContent对象获取到原回复内容:', truncated);\r\n        return truncated;\r\n      }\r\n    }\r\n\r\n    // 如果replyContent是字符串\r\n    if (typeof currentReply.replyContent === 'string' && currentReply.replyContent.trim() !== '') {\r\n      const truncated = currentReply.replyContent.length > 30\r\n        ? currentReply.replyContent.substring(0, 30) + '...'\r\n        : currentReply.replyContent;\r\n      console.log('✅ 从replyContent字符串获取到原回复内容:', truncated);\r\n      return truncated;\r\n    }\r\n  }\r\n\r\n  // 如果replyContent没有数据，再在回复列表中查找对应的回复内容（根据回复ID）\r\n  const targetReply = reply.value.find((rep: any) => String(rep.id) === String(replyId));\r\n\r\n  console.log('🔍 在回复列表中查找结果:', {\r\n    找到目标回复: !!targetReply,\r\n    回复列表中的所有ID: reply.value.map(r => r.id),\r\n    查找的replyId: replyId\r\n  });\r\n\r\n  if (targetReply) {\r\n    console.log('✅ 找到目标回复:', {\r\n      id: targetReply.id,\r\n      text: targetReply.text,\r\n      hasText: !!targetReply.text,\r\n      textType: typeof targetReply.text,\r\n      textLength: targetReply.text ? targetReply.text.length : 0\r\n    });\r\n\r\n    if (targetReply.text && targetReply.text.trim() !== '') {\r\n      // 截断文本，与弹窗中的 truncatedReplyText 保持一致\r\n      const truncated = targetReply.text.length > 30\r\n        ? targetReply.text.substring(0, 30) + '...'\r\n        : targetReply.text;\r\n      return truncated;\r\n    } else {\r\n      // 如果text为空但有图片，显示[图片]\r\n      if (targetReply.image && targetReply.image.trim() !== '') {\r\n        return '[图片]';\r\n      }\r\n      return '原回复内容';\r\n    }\r\n  }\r\n\r\n  // 如果在回复列表中没找到，可能是回复的原问题\r\n  console.log('🔍 未在回复中找到，检查原问题...');\r\n  if (searchParams.value.problem && searchParams.value.problem.question) {\r\n    const questionText = searchParams.value.problem.question;\r\n    const truncated = questionText.length > 30\r\n      ? questionText.substring(0, 30) + '...'\r\n      : questionText;\r\n    console.log('📋 使用原问题内容:', truncated);\r\n    return truncated;\r\n  }\r\n\r\n  console.log('❌ 未找到任何匹配内容');\r\n  return '原回复内容';\r\n}\r\n\r\n// 安全地获取replyContent内容\r\nconst getSafeReplyContent = (replyContent: any): string => {\r\n  if (!replyContent) {\r\n    return '';\r\n  }\r\n\r\n  // 如果replyContent是对象，尝试获取其中的text字段\r\n  if (typeof replyContent === 'object' && replyContent !== null) {\r\n    console.log('🔍 replyContent是对象，尝试获取text字段:', replyContent);\r\n\r\n    // 检查是否有text字段\r\n    if (replyContent.text && typeof replyContent.text === 'string') {\r\n      const trimmed = replyContent.text.trim();\r\n      if (trimmed && trimmed !== '' && trimmed !== 'null' && trimmed !== 'undefined') {\r\n        console.log('✅ 从replyContent对象中获取到text:', trimmed);\r\n        return trimmed;\r\n      }\r\n    }\r\n\r\n    // 如果没有text字段但有images，显示[图片]\r\n    if (replyContent.images && replyContent.images !== null) {\r\n      return '[图片]';\r\n    }\r\n\r\n    return '';\r\n  }\r\n\r\n  // 如果是字符串类型\r\n  if (typeof replyContent === 'string') {\r\n    const trimmed = replyContent.trim();\r\n    // 如果是空字符串或者是\"[object Object]\"这样的无效内容，返回空\r\n    if (trimmed === '' || trimmed === '[object Object]' || trimmed === 'null' || trimmed === 'undefined') {\r\n      return '';\r\n    }\r\n    return trimmed;\r\n  }\r\n\r\n  // 如果不是字符串也不是对象，尝试转换为字符串\r\n  try {\r\n    const converted = String(replyContent).trim();\r\n    // 检查转换后的结果是否有效\r\n    if (converted === '[object Object]' || converted === 'null' || converted === 'undefined' || converted === '') {\r\n      return '';\r\n    }\r\n    return converted;\r\n  } catch (error) {\r\n    console.warn('replyContent转换失败:', error);\r\n    return '';\r\n  }\r\n}\r\n\r\n// 格式化时间显示 - 显示完整的年月日时间\r\nconst formatTime = (timeStr: string): string => {\r\n  if (!timeStr || timeStr === '-') {\r\n    return '-';\r\n  }\r\n\r\n  try {\r\n    // 如果时间字符串包含日期和时间，显示完整的年月日时间\r\n    if (timeStr.includes(' ')) {\r\n      const parts = timeStr.split(' ');\r\n      if (parts.length >= 2) {\r\n        const datePart = parts[0]; // 日期部分 2025-06-18\r\n        const timePart = parts[1]; // 时间部分 17:33:40\r\n\r\n        // 格式化日期显示 (年-月-日 时:分)\r\n        if (datePart.includes('-') && timePart.includes(':')) {\r\n          const dateComponents = datePart.split('-');\r\n          const timeComponents = timePart.split(':');\r\n\r\n          if (dateComponents.length >= 3 && timeComponents.length >= 2) {\r\n            const year = dateComponents[0];\r\n            const month = dateComponents[1];\r\n            const day = dateComponents[2];\r\n            const hour = timeComponents[0];\r\n            const minute = timeComponents[1];\r\n\r\n            return `${year}-${month}-${day} ${hour}:${minute}`;\r\n          }\r\n        }\r\n\r\n        // 如果格式不标准，返回原始时间部分\r\n        return timePart;\r\n      }\r\n    }\r\n\r\n    // 如果只有时间部分，直接返回\r\n    return timeStr;\r\n  } catch (error) {\r\n    console.warn('时间格式化失败:', error);\r\n    return timeStr;\r\n  }\r\n}\r\n\r\n// 检查回复是否有@信息\r\nconst hasReplyInfo = (rep: any) => {\r\n  // 检查 replyUserId 和 replyId 是否存在且不为空（支持 null、undefined、空字符串的判断）\r\n  const hasValidReplyUserId = rep.replyUserId && rep.replyUserId !== \"\" && rep.replyUserId !== null;\r\n  const hasValidReplyId = rep.replyId && rep.replyId !== \"\" && rep.replyId !== null;\r\n  const hasInfo = hasValidReplyUserId && hasValidReplyId;\r\n\r\n  return hasInfo;\r\n}\r\n\r\n\r\n\r\nconst setReplyTarget = (rep: any) => {\r\n  console.log('🎯 设置回复目标:', {\r\n    userId: rep.userId,\r\n    userName: rep.userName,\r\n    id: rep.id,\r\n    text: rep.text\r\n  });\r\n\r\n  replyTarget.value = rep;\r\n  textareaFocus.value = false; // 先关闭，确保切换时能生效\r\n  // 可选：聚焦输入框\r\n  nextTick(() => {\r\n    textareaFocus.value = true; // 触发聚焦\r\n    // replyTextarea.value?.focus();\r\n  });\r\n};\r\n\r\nconst searchParams = ref({\r\n  communication_id: '',\r\n  problem: {\r\n    patientName: '',\r\n    patientAvatar: '',\r\n    images: [],\r\n    question: '',\r\n    title: '', // 添加title字段\r\n    createTime: '', // 添加创建时间字段\r\n    updateTime: '' // 添加更新时间字段\r\n  },\r\n  reply: {\r\n    id: '',\r\n    userAvatar: '',\r\n    userName: '',\r\n    replyContent: '',\r\n    replyImage: '',\r\n    image: [],\r\n    text: ''\r\n  }\r\n\r\n\r\n});\r\n\r\n// 根据医生ID获取医生信息\r\nconst fetchDoctorsByIds = async (doctorIds: string | string[]) => {\r\n  try {\r\n    console.log('获取医生信息，doctorIds:', doctorIds);\r\n\r\n    // 处理doctorIds，确保是数组格式\r\n    let idsArray: string[] = [];\r\n    if (typeof doctorIds === 'string') {\r\n      idsArray = doctorIds.split(',').filter(id => id.trim() !== '');\r\n    } else if (Array.isArray(doctorIds)) {\r\n      idsArray = doctorIds.filter(id => id && id.trim() !== '');\r\n    }\r\n\r\n    if (idsArray.length === 0) {\r\n      console.log('没有有效的医生ID');\r\n      mentionedDoctors.value = [];\r\n      return;\r\n    }\r\n\r\n    // 调用医生列表API\r\n    const response = await http.get('/sys/user/doctorList');\r\n\r\n    if (response.success && response.result) {\r\n      const allDoctors = response.result;\r\n\r\n      // 根据ID筛选出@的医生\r\n      const selectedDoctors = allDoctors.filter((doctor: any) =>\r\n        idsArray.includes(String(doctor.id)) || idsArray.includes(String(doctor.userId))\r\n      );\r\n\r\n      mentionedDoctors.value = selectedDoctors;\r\n      console.log('成功获取@医生信息:', selectedDoctors);\r\n    } else {\r\n      console.error('获取医生列表失败:', response.message);\r\n      mentionedDoctors.value = [];\r\n    }\r\n  } catch (error) {\r\n    console.error('获取医生信息异常:', error);\r\n    mentionedDoctors.value = [];\r\n  }\r\n};\r\n\r\nconst fetchReply = async () => {\r\n  try {\r\n    loadingChat.value = true; // 显示加载状态\r\n\r\n    // 验证参数\r\n    if (!communicationId.value) {\r\n      showError('页面参数错误，请重新进入');\r\n      return;\r\n    }\r\n\r\n    console.log('获取回复列表，communicationId:', communicationId.value);\r\n\r\n    // 简化参数，只传递必要的数据，避免URL过长\r\n    const params = {\r\n      communication_id: communicationId.value,\r\n      user_id: userStore.userInfo.userid || '',\r\n      category: Number(userStore.userInfo.userCategory)\r\n    };\r\n    console.log('获取回复列表请求参数:', params);\r\n    http.get('/communication/getDetail',  params ).then((res:any) => {\r\n        console.log('=== 聊天详情页面接口数据打印 ===');\r\n        console.log('📡 接口地址: GET /communication/getDetail');\r\n        console.log('📤 请求参数:', JSON.stringify(params, null, 2));\r\n        console.log('📥 完整响应数据:', JSON.stringify(res, null, 2));\r\n        console.log('✅ 响应状态:', res.success ? '成功' : '失败');\r\n        console.log('📊 响应代码:', res.code);\r\n        console.log('💬 响应消息:', res.message);\r\n        console.log('⏰ 响应时间戳:', res.timestamp);\r\n\r\n        if(res.success && res.result){\r\n          console.log('🎯 业务数据 (res.result):', JSON.stringify(res.result, null, 2));\r\n          console.log('📋 数据结构分析:');\r\n          console.log('  - communication_id:', res.result.communication_id);\r\n          console.log('  - problem 对象:', res.result.problem ? '存在' : '不存在');\r\n          console.log('  - reply 数组:', res.result.reply ? `存在，长度: ${res.result.reply.length}` : '不存在');\r\n\r\n          if (res.result.problem) {\r\n            console.log('🔍 问题详情 (problem):');\r\n\r\n          }\r\n\r\n          if (res.result.reply && Array.isArray(res.result.reply)) {\r\n            console.log('💬 回复列表 (reply):');\r\n            console.log('  - 回复总数:', res.result.reply.length);\r\n            res.result.reply.forEach((item: any, index: number) => {\r\n              console.log(`  📝 回复 ${index + 1}:`, {\r\n                id: item.id,\r\n                用户名: item.userName,\r\n                用户类别: item.userCategory,\r\n                用户ID: item.userId,\r\n                回复内容: item.text,\r\n                图片: item.image,\r\n                创建时间: item.createTime,\r\n                更新时间: item.updateTime,\r\n                回复用户ID: item.replyUserId,\r\n                回复ID: item.replyId,\r\n                头像: item.userAvatar\r\n              });\r\n            });\r\n          }\r\n          console.log('================================');\r\n\r\n          console.log('API返回的获取沟通记录', res.result);\r\n          console.log('当前 communicationId:', communicationId.value);\r\n          console.log('返回的 communication_id:', res.result.communication_id);\r\n\r\n          // 处理问题图片数组 - 使用完整URL\r\n          const rawImages = res.result.problem.images || [];\r\n          console.log('📷 原始图片数据:', rawImages);\r\n\r\n          problemImages.value = rawImages.map((filename: string) => {\r\n            // 如果已经是完整URL，直接返回\r\n            if (filename.startsWith('http')) {\r\n              return filename;\r\n            }\r\n\r\n            // 如果是相对路径，构建完整URL\r\n            if (filename.startsWith('/')) {\r\n              return `https://www.mograine.cn${filename}`;\r\n            }\r\n\r\n            // 对于纯文件名，构建完整URL\r\n            return `https://www.mograine.cn/tmp/${filename}`;\r\n          });\r\n\r\n          console.log('📷 处理后的图片URLs:', problemImages.value);\r\n\r\n          // 处理回复数据，添加回复关系信息\r\n          const replyList = res.result.reply || [];\r\n          console.log('原始回复列表:', replyList);\r\n          console.log('回复列表长度:', replyList.length);\r\n\r\n          // 直接使用后端返回的回复列表，不进行排序\r\n          const processedReply = replyList;\r\n\r\n\r\n\r\n          // 检查每个回复项的数据结构\r\n          processedReply.forEach((item: any, index: number) => {\r\n            console.log(`回复 ${index}:`, {\r\n              userName: item.userName,\r\n              userAvatar: item.userAvatar,\r\n              text: item.text,\r\n              createTime: item.createTime,\r\n              updateTime: item.updateTime,\r\n              userCategory: item.userCategory,\r\n              userId: item.userId,\r\n              image: item.image,\r\n              imageType: typeof item.image,\r\n              imageLength: Array.isArray(item.image) ? item.image.length : 'not array',\r\n              processedImages: getImageList(item.image), // 添加处理后的图片数组\r\n              finalImageUrls: getImageList(item.image).map(img => getReplyImageUrl(img)), // 最终的图片URL\r\n              replyUserId: item.replyUserId,\r\n              replyId: item.replyId,\r\n              hasReplyInfo: hasReplyInfo(item)\r\n            });\r\n          });\r\n\r\n\r\n\r\n          reply.value = processedReply;\r\n          searchParams.value.problem = res.result.problem || {};\r\n          searchParams.value.reply = res.result.reply || {};\r\n\r\n          // 处理@医生信息\r\n          if (res.result.problem && res.result.problem.doctors && Array.isArray(res.result.problem.doctors)) {\r\n            // 优先使用doctors字段（新的API响应格式）\r\n            mentionedDoctors.value = res.result.problem.doctors;\r\n            console.log('📋 获取到@医生信息 (doctors字段):', mentionedDoctors.value);\r\n          } else if (res.result.problem && res.result.problem.mentionedDoctors) {\r\n            // 兼容旧的mentionedDoctors字段\r\n            mentionedDoctors.value = res.result.problem.mentionedDoctors;\r\n            console.log('📋 获取到@医生信息 (mentionedDoctors字段):', mentionedDoctors.value);\r\n          } else if (res.result.problem && res.result.problem.doctorIds) {\r\n            // 如果后端返回的是doctorIds，需要根据ID获取医生信息\r\n            console.log('📋 检测到doctorIds，需要获取医生详细信息:', res.result.problem.doctorIds);\r\n            fetchDoctorsByIds(res.result.problem.doctorIds).catch(error => {\r\n              console.error('获取医生信息失败:', error);\r\n            });\r\n          } else {\r\n            mentionedDoctors.value = [];\r\n            console.log('📋 未检测到@医生信息');\r\n          }\r\n        } else {\r\n          console.log('❌ 接口调用失败:');\r\n          console.log('  - success:', res.success);\r\n          console.log('  - code:', res.code);\r\n          console.log('  - message:', res.message);\r\n          console.log('  - result:', res.result);\r\n          console.log('  - 完整响应:', JSON.stringify(res, null, 2));\r\n\r\n          uni.showToast({\r\n            title: res.message || '获取沟通记录失败',\r\n            icon: 'none',\r\n          })\r\n        }\r\n      }).catch((error: any) => {\r\n        console.log('🚨 网络请求异常:');\r\n        console.log('  - 错误类型:', typeof error);\r\n        console.log('  - 错误信息:', error.message || error);\r\n        console.log('  - 完整错误对象:', error);\r\n        console.log('  - 请求参数:', params);\r\n        console.log('  - 接口地址: GET /communication/getDetail');\r\n\r\n        uni.showToast({\r\n          title: '网络连接失败，请检查网络后重试',\r\n          icon: 'none',\r\n          duration: 3000\r\n        });\r\n      });\r\n  } finally {\r\n    loadingChat.value = false; // 隐藏加载状态\r\n  }\r\n}\r\n\r\nonMounted(() => {\r\n  fetchReply();\r\n});\r\n\r\n\r\n  \r\n// 从相册选择图片\r\nconst chooseFromGallery = () => {\r\n  if (replyImages.value.length >= 6) {\r\n    uni.showToast({ title: '最多只能上传6张图片', icon: 'none' });\r\n    return;\r\n  }\r\n\r\n  const remainingCount = 6 - replyImages.value.length;\r\n  uni.chooseImage({\r\n    count: remainingCount,\r\n    sourceType: ['album'],\r\n    success: (res: any) => {\r\n      if (res.tempFilePaths && res.tempFilePaths.length > 0) {\r\n        replyImages.value.push(...res.tempFilePaths);\r\n      }\r\n    },\r\n    fail: (err: any) => {\r\n      console.error('选择图片失败:', err);\r\n      uni.showToast({ title: '选择图片失败', icon: 'none' });\r\n    }\r\n  });\r\n};\r\n\r\n// 从相机拍照\r\nconst chooseFromCamera = () => {\r\n  if (replyImages.value.length >= 6) {\r\n    uni.showToast({ title: '最多只能上传6张图片', icon: 'none' });\r\n    return;\r\n  }\r\n\r\n  uni.chooseImage({\r\n    count: 1,\r\n    sourceType: ['camera'],\r\n    success: (res: any) => {\r\n      if (res.tempFilePaths && res.tempFilePaths.length > 0) {\r\n        replyImages.value.push(...res.tempFilePaths);\r\n      }\r\n    },\r\n    fail: (err: any) => {\r\n      console.error('拍照失败:', err);\r\n      uni.showToast({ title: '拍照失败', icon: 'none' });\r\n    }\r\n  });\r\n};\r\n\r\n// 选择图片来源\r\nconst chooseImage = () => {\r\n  uni.showActionSheet({\r\n    itemList: ['从相册选择', '拍照'],\r\n    success: (res) => {\r\n      if (res.tapIndex === 0) {\r\n        chooseFromGallery();\r\n      } else if (res.tapIndex === 1) {\r\n        chooseFromCamera();\r\n      }\r\n    },\r\n    fail: (err) => {\r\n      console.error('选择操作失败:', err);\r\n    }\r\n  });\r\n};\r\n\r\n\r\n\r\n  // 上传单张图片到服务器\r\n  const uploadImage = async (tempFilePath: string): Promise<string | null> => {\r\n    return new Promise((resolve, reject) => {\r\n      // 检查文件路径是否有效\r\n      if (!tempFilePath || tempFilePath.trim() === '') {\r\n        reject(new Error('文件路径无效'));\r\n        return;\r\n      }\r\n\r\n      // 检查是否是有效的临时文件路径\r\n      if (!tempFilePath.includes('tmp') && !tempFilePath.includes('temp') && !tempFilePath.startsWith('wxfile://')) {\r\n        reject(new Error('文件路径格式不正确'));\r\n        return;\r\n      }\r\n\r\n      // 使用正确的上传URL\r\n      const uploadUrl = 'https://www.mograine.cn/api/sys/common/upload';\r\n\r\n      uni.uploadFile({\r\n        url: uploadUrl,\r\n        filePath: tempFilePath,\r\n        name: 'file',\r\n        header: {\r\n          'X-Access-Token': userStore.userInfo.token || '',\r\n          'X-Tenant-Id': userStore.userInfo.tenantId || ''\r\n          // 不要设置Content-Type，让浏览器自动设置\r\n        },\r\n        formData: {\r\n          // 可能需要的额外参数\r\n          'biz': 'temp'\r\n        },\r\n        success: (uploadRes) => {\r\n          console.log('📤 图片上传响应:', {\r\n            statusCode: uploadRes.statusCode,\r\n            data: uploadRes.data\r\n          });\r\n\r\n          try {\r\n            // 检查响应状态码\r\n            if (uploadRes.statusCode === 404) {\r\n              reject(new Error('上传接口不存在，请检查服务器配置'));\r\n              return;\r\n            }\r\n\r\n            if (uploadRes.statusCode === 401 || uploadRes.statusCode === 403) {\r\n              reject(new Error('权限不足，请重新登录'));\r\n              return;\r\n            }\r\n\r\n            if (uploadRes.statusCode !== 200) {\r\n              reject(new Error(`上传失败，状态码: ${uploadRes.statusCode}`));\r\n              return;\r\n            }\r\n\r\n            // 尝试解析JSON响应\r\n            let result: any;\r\n            if (typeof uploadRes.data === 'string') {\r\n              result = JSON.parse(uploadRes.data);\r\n            } else {\r\n              result = uploadRes.data;\r\n            }\r\n\r\n            console.log('📤 解析后的上传响应:', result);\r\n\r\n            // 检查不同的响应格式，返回完整URL\r\n            if (result.success && result.message) {\r\n              // 格式1: {success: true, message: \"/upload/xxx.jpg\"}\r\n              let imageUrl = result.message;\r\n              // 如果不是完整URL，构建完整URL\r\n              if (!imageUrl.startsWith('http')) {\r\n                if (imageUrl.startsWith('/')) {\r\n                  imageUrl = `https://www.mograine.cn${imageUrl}`;\r\n                } else {\r\n                  imageUrl = `https://www.mograine.cn/tmp/${imageUrl}`;\r\n                }\r\n              }\r\n              console.log('📤 格式1 - 图片URL:', imageUrl);\r\n              resolve(imageUrl);\r\n            } else if (result.code === 200 && result.result) {\r\n              // 格式2: {code: 200, result: \"/upload/xxx.jpg\"} 或 {code: 200, result: \"filename.jpg\"}\r\n              let imageUrl: string = result.result;\r\n              // 如果不是完整URL，构建完整URL\r\n              if (!imageUrl.startsWith('http')) {\r\n                if (imageUrl.startsWith('/')) {\r\n                  imageUrl = `https://www.mograine.cn${imageUrl}`;\r\n                } else {\r\n                  imageUrl = `https://www.mograine.cn/tmp/${imageUrl}`;\r\n                }\r\n              }\r\n              console.log('📤 格式2 - 图片URL:', imageUrl);\r\n              resolve(imageUrl);\r\n            } else if (result.data) {\r\n              // 格式3: {data: \"/upload/xxx.jpg\"} 或 {data: \"filename.jpg\"}\r\n              let imageUrl: string = result.data;\r\n              // 如果不是完整URL，构建完整URL\r\n              if (!imageUrl.startsWith('http')) {\r\n                if (imageUrl.startsWith('/')) {\r\n                  imageUrl = `https://www.mograine.cn${imageUrl}`;\r\n                } else {\r\n                  imageUrl = `https://www.mograine.cn/tmp/${imageUrl}`;\r\n                }\r\n              }\r\n              console.log('📤 格式3 - 图片URL:', imageUrl);\r\n              resolve(imageUrl);\r\n            } else {\r\n              console.log('📤 上传响应格式不匹配，完整响应:', result);\r\n              reject(new Error(result.message || result.msg || '上传失败，响应格式不正确'));\r\n            }\r\n          } catch (parseError) {\r\n            reject(new Error('服务器响应格式错误'));\r\n          }\r\n        },\r\n        fail: () => {\r\n          reject(new Error('上传失败，请重试'));\r\n        }\r\n      });\r\n    });\r\n  }\r\nconsole.log(\"uploadedUrl\",replyImages.value)\r\n// 防重复提交标志\r\nconst isSubmitting = ref(false);\r\n\r\n// 通用验证函数\r\nconst showError = (message: string) => {\r\n  uni.showToast({ title: message, icon: 'none' });\r\n};\r\n\r\nconst validateSubmission = () => {\r\n  // 防重复提交\r\n  if (isSubmitting.value) {\r\n    showError('正在提交中，请勿重复操作');\r\n    return false;\r\n  }\r\n\r\n  // 验证必须输入文本内容\r\n  if (!replyContent.value.trim()) {\r\n    showError('请输入回复内容');\r\n    // 自动聚焦到输入框，先重置再设置确保聚焦生效\r\n    textareaFocus.value = false;\r\n    nextTick(() => {\r\n      textareaFocus.value = true;\r\n    });\r\n    return false;\r\n  }\r\n\r\n  // 验证内容长度\r\n  if (replyContent.value.trim().length > 1000) {\r\n    showError('回复内容不能超过1000个字符');\r\n    return false;\r\n  }\r\n\r\n  // 验证必要参数\r\n  if (!communicationId.value) {\r\n    showError('页面参数错误，请重新进入');\r\n    return false;\r\n  }\r\n\r\n  if (!userStore.userInfo.userid) {\r\n    showError('用户信息错误，请重新登录');\r\n    return false;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\n// 提交回复方法\r\nconst submitReply = async () => {\r\n  if (!validateSubmission()) return;\r\n\r\n  try {\r\n    isSubmitting.value = true; // 设置提交状态\r\n    uni.showLoading({ title: '提交中...' });\r\n\r\n    // 构建请求数据 - 严格按照后端接口文档格式\r\n    const requestData: any = {\r\n      communicationId: String(communicationId.value), // 问题id (string) - 必填\r\n      userId: String(userStore.userInfo.userid), // 用户id (string) - 必填\r\n      replyUserId: \"\", // 被回复的用户id (string) - 可选，没有时传空字符串\r\n      replyId: \"\", // 被回复的信息的id (string) - 可选，没有时传空字符串\r\n      text: \"\", // 回复内容 (string) - 可选，没有时传空字符串\r\n      images: [] // 回复的图片 (string | null) - 可选，注意字段名是 images\r\n    };\r\n\r\n    // 处理 text 字段 - 根据接口文档，传空字符串而不是null\r\n    const trimmedText = replyContent.value.trim();\r\n    if (trimmedText && trimmedText.length > 0) {\r\n      requestData.text = trimmedText;\r\n    }\r\n    // 如果没有文本内容，保持为空字符串\r\n\r\n    // 处理图片数据 - 先上传图片再提交\r\n    if (replyImages.value.length > 0) {\r\n      try {\r\n        uni.showLoading({ title: '上传图片中...' });\r\n\r\n        const uploadedUrls: string[] = [];\r\n\r\n        // 逐个上传图片\r\n        for (const tempPath of replyImages.value) {\r\n          // 如果已经是URL，直接使用\r\n          if (tempPath.startsWith('http')) {\r\n            uploadedUrls.push(tempPath);\r\n          } else {\r\n            // 上传本地图片\r\n            const uploadedUrl = await uploadImage(tempPath);\r\n            if (uploadedUrl) {\r\n              uploadedUrls.push(uploadedUrl);\r\n            } else {\r\n              uni.hideLoading();\r\n              showError('图片上传失败，请重试');\r\n              return;\r\n            }\r\n          }\r\n        }\r\n\r\n        uni.hideLoading();\r\n\r\n        if (uploadedUrls.length === 0) {\r\n          showError('图片上传失败，请重新选择图片');\r\n          return;\r\n        }\r\n\r\n        // 直接传数组给后端\r\n        requestData.images = uploadedUrls;\r\n      } catch (error) {\r\n        uni.hideLoading();\r\n        console.error('图片上传失败:', error);\r\n        showError('图片上传失败，请重试');\r\n        return;\r\n      }\r\n    }\r\n\r\n    // 处理回复相关字段 - 如果是回复别人的评论，设置相应字段；否则保持空字符串\r\n    if (replyTarget.value && replyTarget.value.userId && replyTarget.value.id) {\r\n      requestData.replyUserId = String(replyTarget.value.userId);\r\n      requestData.replyId = String(replyTarget.value.id);\r\n\r\n      // 添加replyContent字段，传入被回复的内容\r\n      if (replyTarget.value.text && replyTarget.value.text.trim() !== '') {\r\n        requestData.replyContent = replyTarget.value.text.trim();\r\n      }\r\n\r\n    }\r\n    // 如果没有回复目标，这两个字段保持为空字符串（已在初始化时设置）\r\n\r\n    // 简化的数据验证\r\n    const validateRequestData = (data: any) => {\r\n      if (!data.communicationId || !data.userId) {\r\n        showError('数据验证失败，请重试');\r\n        return false;\r\n      }\r\n\r\n      if (data.text && data.text.length > 2000) {\r\n        showError('回复内容不能超过2000个字符');\r\n        return false;\r\n      }\r\n\r\n      if (!/^\\d+$/.test(data.communicationId) || !/^\\d+$/.test(data.userId)) {\r\n        showError('数据格式错误');\r\n        return false;\r\n      }\r\n\r\n      if (!data.text && !data.images) {\r\n        showError('请输入回复内容或选择图片');\r\n        return false;\r\n      }\r\n\r\n      return true;\r\n    };\r\n\r\n    if (!validateRequestData(requestData)) return;\r\n\r\n    // 打印请求数据用于调试\r\n    console.log('📤 提交回复请求数据:', {\r\n      ...requestData,\r\n      imagesCount: requestData.images ? requestData.images.length : 0,\r\n      hasImages: !!requestData.images && requestData.images.length > 0,\r\n      imageUrls: requestData.images || []\r\n    });\r\n\r\n    if (requestData.images && requestData.images.length > 0) {\r\n      console.log('📸 提交的图片URL列表:', requestData.images);\r\n    }\r\n\r\n    // 使用 http 工具发送请求，与其他接口保持一致\r\n    // http 工具会自动添加 X-Access-Token 等必要的请求头\r\n    try {\r\n      const response = await http.post('/communication/reply', requestData);\r\n      console.log('📥 提交回复响应数据:', response);\r\n\r\n      if (response && response.result) {\r\n        console.log('📝 新创建的回复数据:', response.result);\r\n      }\r\n\r\n      if (response && response.success) {\r\n        uni.showToast({ title: '提交成功', icon: 'success' });\r\n\r\n        // 清空输入内容\r\n        replyContent.value = '';\r\n        replyImages.value = [];\r\n        replyTarget.value = null;\r\n\r\n        // 多次刷新确保数据完整显示\r\n        // 立即刷新\r\n        fetchReply();\r\n\r\n        // 短延迟刷新\r\n        setTimeout(() => {\r\n          fetchReply();\r\n        }, 800);\r\n\r\n        // 长延迟刷新，确保服务器数据已完全更新\r\n        setTimeout(() => {\r\n          fetchReply();\r\n        }, 2000);\r\n      } else {\r\n        // 详细的错误处理\r\n        let errorMsg = '提交失败';\r\n\r\n        if (response?.message) {\r\n          errorMsg = response.message;\r\n\r\n          // 针对数据库错误的特殊处理\r\n          if (response.message.includes('完整性例如') || response.message.includes('字段内容超出长度')) {\r\n            errorMsg = '内容过长或格式不正确，请检查后重试';\r\n          } else if (response.message.includes('违反唯一约束')) {\r\n            errorMsg = '数据重复，请勿重复提交';\r\n          } else if (response.message.includes('违反非空限制')) {\r\n            errorMsg = '必填信息缺失，请完善后重试';\r\n          }\r\n        } else if (response?.msg) {\r\n          errorMsg = response.msg;\r\n        }\r\n\r\n        console.error('提交失败详情:', {\r\n          response,\r\n          requestData,\r\n          errorMsg\r\n        });\r\n\r\n        uni.showToast({\r\n          title: errorMsg,\r\n          icon: 'none',\r\n          duration: 3000 // 延长显示时间\r\n        });\r\n      }\r\n    } catch (error) {\r\n      console.error('提交异常:', error);\r\n      uni.showToast({\r\n        title: '网络连接异常，请检查网络后重试',\r\n        icon: 'none',\r\n        duration: 3000\r\n      });\r\n    }\r\n  } finally {\r\n    isSubmitting.value = false; // 重置提交状态\r\n    uni.hideLoading();\r\n  }\r\n};\r\n\r\n// 删除回复方法\r\nconst deleteReply = async (rep: any) => {\r\n  try {\r\n    // 显示确认对话框\r\n    const result = await new Promise((resolve) => {\r\n      uni.showModal({\r\n        title: '确认删除',\r\n        content: '确定要删除这条评论吗？',\r\n        success: (res) => {\r\n          resolve(res.confirm);\r\n        },\r\n        fail: () => {\r\n          resolve(false);\r\n        }\r\n      });\r\n    });\r\n\r\n    if (!result) {\r\n      return; // 用户取消删除\r\n    }\r\n\r\n    uni.showLoading({ title: '删除中...' });\r\n\r\n    // DELETE请求使用查询参数，注意参数名要与服务器期望的一致\r\n    const queryParams = `reply_id=${rep.id}&user_id=${userStore.userInfo.userid}&communicationId=${communicationId.value}`;\r\n\r\n    const res = await http.delete(`/communication/del_reply?${queryParams}`);\r\n\r\n    if (res.success) {\r\n      uni.showToast({ title: '删除成功', icon: 'success' });\r\n      fetchReply(); // 刷新回复列表\r\n    } else {\r\n      uni.showToast({ title: res.message || '删除失败', icon: 'none' });\r\n    }\r\n  } catch (e) {\r\n    console.error('删除评论失败:', e);\r\n    uni.showToast({ title: '删除失败', icon: 'none' });\r\n  } finally {\r\n    uni.hideLoading();\r\n  }\r\n};\r\n\r\n// 通用图片预览函数\r\nconst handleImagePreview = (url: string, imageList: string[]) => {\r\n  // 过滤有效的图片URL\r\n  const validUrls = imageList.filter(img => img && typeof img === 'string' && img.trim() !== '');\r\n\r\n  if (validUrls.length === 0) {\r\n    showError('没有可预览的图片');\r\n    return;\r\n  }\r\n\r\n  // 确保当前URL有效\r\n  const currentUrl = url && typeof url === 'string' && url.trim() !== '' ? url : validUrls[0];\r\n\r\n  uni.previewImage({\r\n    current: currentUrl,\r\n    urls: validUrls\r\n  });\r\n};\r\n\r\n// 预览图片方法 - 已移动到上面的 previewProblemImage 函数\r\n\r\n// 预览回复中的图片\r\nconst previewReplyImage = (url: string, imageList: string[]) => {\r\n  handleImagePreview(url, imageList);\r\n};\r\n\r\n// 根据索引移除指定的图片\r\nconst removeImageByIndex = (index: number) => {\r\n  replyImages.value.splice(index, 1);\r\n  uni.showToast({\r\n    title: '图片已移除',\r\n    icon: 'success',\r\n    duration: 1000\r\n  });\r\n}\r\n\r\n// 预览图片（模仿addChat的逻辑）\r\nconst previewImage = (url: string) => {\r\n  uni.previewImage({\r\n    current: url,\r\n    urls: replyImages.value\r\n  });\r\n};\r\n\r\n// 工具函数：保证图片字段为数组\r\nconst getImageList = (imgField: any): string[] => {\r\n  if (!imgField) return [];\r\n  if (Array.isArray(imgField)) return imgField;\r\n  if (typeof imgField === 'string') {\r\n    // 逗号分隔或单个图片\r\n    if (imgField.startsWith('[')) {\r\n      try {\r\n        const arr = JSON.parse(imgField);\r\n        return Array.isArray(arr) ? arr : [];\r\n      } catch {\r\n        return [];\r\n      }\r\n    } else if (imgField.includes(',')) {\r\n      return imgField.split(',').map(s => s.trim()).filter(Boolean);\r\n    } else if (imgField.trim() !== '') {\r\n      return [imgField];\r\n    }\r\n  }\r\n  return [];\r\n};\r\n\r\n// 工具函数：构建回复图片的完整URL\r\nconst getReplyImageUrl = (img: string): string => {\r\n  if (!img) return '';\r\n\r\n  // 如果已经是完整URL，直接返回\r\n  if (img.startsWith('http')) {\r\n    return img;\r\n  }\r\n\r\n  // 如果是相对路径，构建完整URL\r\n  if (img.startsWith('/')) {\r\n    return `https://www.mograine.cn${img}`;\r\n  }\r\n\r\n  // 对于纯文件名，构建完整URL\r\n  return `https://www.mograine.cn/tmp/${img}`;\r\n};\r\n\r\n\r\n\r\n\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.chat-detail-page {\r\n  position: relative !important;\r\n  width: 100% !important;\r\n  height: 100vh !important;\r\n  background-color: #f8f9fa;        // 改为淡灰色背景\r\n  overflow: visible !important;      // 确保子元素可见\r\n}\r\n\r\n// 全局页面背景\r\n:deep(.page-layout) {\r\n  background-color: #f8f9fa !important; // 改为淡灰色背景\r\n  position: relative !important;\r\n  overflow: visible !important;\r\n}\r\n\r\n:deep(.page-layout-content) {\r\n  background-color: #f8f9fa !important; // 改为淡灰色背景\r\n  position: relative !important;\r\n  overflow: visible !important;\r\n}\r\n\r\n.page-wrapper {\r\n  background-color: #f8f9fa;        // 改为淡灰色背景，突出悬浮框\r\n  min-height: 100vh;\r\n  width: 100%;\r\n}\r\n.page-scroll-view {\r\n    height: 100%;\r\n    box-sizing: border-box;\r\n    background-color: transparent;    // 改为透明背景\r\n    padding-bottom: 180px;           // 增加底部内边距，确保不遮挡最后一条评论\r\n}\r\n// 重新设计的患者问题卡片\r\n.patient-question-card {\r\n    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\r\n    border-radius: 20px;\r\n    margin: 16px;\r\n    padding: 0;\r\n    box-shadow: 0 8px 32px rgba(0,0,0,0.12);\r\n    border: 1px solid #e9ecef;\r\n    overflow: hidden;\r\n}\r\n\r\n// 患者信息头部样式\r\n.patient-header {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 20px;\r\n    background-color: #ffffff;\r\n    border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.patient-avatar {\r\n    width: 48px;\r\n    height: 48px;\r\n    border-radius: 50%;\r\n    border: 2px solid #e9ecef;\r\n    object-fit: cover;\r\n    background-color: #f8f9fa;\r\n}\r\n\r\n.patient-info {\r\n    margin-left: 12px;\r\n    flex: 1;\r\n}\r\n\r\n.patient-name {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    color: #2c3e50;\r\n    display: block;\r\n    margin-bottom: 4px;\r\n}\r\n\r\n.question-time {\r\n    font-size: 12px;\r\n    color: #6c757d;\r\n    display: block;\r\n}\r\n\r\n// 标题区域样式 - 浮窗设计\r\n.title-section {\r\n    margin: 16px 20px;\r\n    padding: 16px 20px;\r\n    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\r\n    border-radius: 12px;\r\n    box-shadow: 0 4px 12px rgba(0,0,0,0.08);\r\n    border: 1px solid #e9ecef;\r\n    position: relative;\r\n}\r\n\r\n.title-section::before {\r\n    content: '';\r\n    position: absolute;\r\n    left: 16px;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    width: 4px;\r\n    height: 28px;\r\n    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\r\n    border-radius: 2px;\r\n    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);\r\n}\r\n\r\n.title-text {\r\n    font-size: 18px;\r\n    font-weight: 700;\r\n    color: #1a1a1a;\r\n    line-height: 1.4;\r\n    margin-left: 20px;\r\n    letter-spacing: 0.3px;\r\n}\r\n\r\n// 内容区域样式\r\n.content-section {\r\n    padding: 20px;\r\n}\r\n\r\n.content-header {\r\n    margin-bottom: 12px;\r\n}\r\n\r\n.content-label {\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    color: #6c757d;\r\n}\r\n\r\n.content-text {\r\n    font-size: 16px;\r\n    line-height: 1.6;\r\n    color: #2c3e50;\r\n    word-wrap: break-word;\r\n}\r\n// 图片区域样式\r\n.images-section {\r\n    padding: 0 20px 20px 20px;\r\n    border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.images-header {\r\n    margin-bottom: 12px;\r\n}\r\n\r\n.images-label {\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    color: #6c757d;\r\n}\r\n\r\n.images-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(3, 1fr);\r\n    gap: 8px;\r\n}\r\n\r\n.image-item {\r\n    position: relative;\r\n    aspect-ratio: 1;\r\n    border-radius: 12px;\r\n    overflow: hidden;\r\n    box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n    border: 2px solid transparent;\r\n\r\n    // 加载状态\r\n    &.loading {\r\n        border-color: #07C160;\r\n    }\r\n\r\n    // 错误状态\r\n    &.error {\r\n        border-color: #ff4444;\r\n        opacity: 0.5;\r\n    }\r\n\r\n    // 成功状态\r\n    &.success {\r\n        border-color: transparent;\r\n    }\r\n}\r\n\r\n.question-image {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n    transition: transform 0.2s ease;\r\n}\r\n\r\n.image-item:active .question-image {\r\n    transform: scale(0.95);\r\n}\r\n\r\n.image-overlay {\r\n    position: absolute;\r\n    top: 6px;\r\n    right: 6px;\r\n    background: rgba(0,0,0,0.6);\r\n    color: white;\r\n    border-radius: 12px;\r\n    padding: 2px 6px;\r\n}\r\n\r\n.image-index {\r\n    font-size: 10px;\r\n    font-weight: 600;\r\n}\r\n\r\n.image-debug {\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: 0;\r\n    right: 0;\r\n    background: rgba(0, 0, 0, 0.8);\r\n    color: white;\r\n    padding: 2px 4px;\r\n    font-size: 9px;\r\n    line-height: 1.2;\r\n\r\n    text {\r\n        display: block;\r\n        white-space: nowrap;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n    }\r\n}\r\n\r\n// 旧样式已移除，使用新的标题区域样式\r\n.message-container {\r\n    padding: 0 0 20px 0;             // 只保留底部内边距\r\n    background-color: #fff;\r\n    border-radius: 16px;             // 保持圆角\r\n    margin: 16px;                    // 统一边距\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: stretch;            // 改为stretch，让子元素可以自由对齐\r\n    box-shadow: 0 2px 12px rgba(0,0,0,0.08); // 保持阴影\r\n    border: 1px solid #f0f0f0;       // 保持边框\r\n    overflow: hidden;                // 确保圆角效果\r\n}\r\n// 旧的消息内容和图片样式已移除，使用新的内容区域和图片区域样式\r\n.message-title {\r\n    font-size: 18px;                 // 稍微减小字体\r\n    color: #333;\r\n    margin-bottom: 0;                // 移除底部边距\r\n    font-weight: 600;                // 增加字重\r\n    padding: 20px 20px 16px 20px;    // 添加内边距，底部稍小\r\n}\r\n.reply {\r\n    padding: 16px 20px 50px 20px;    // 增加底部内边距，为时间留出空间\r\n    margin: 0;                       // 移除负边距，保持在容器内\r\n    width: 100%;                     // 占满父容器宽度\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: flex-start;         // 顶端对齐\r\n    position: relative;              // 为删除按钮和时间定位做准备\r\n    background-color: transparent;   // 透明背景，继承父容器背景\r\n    box-sizing: border-box;          // 确保盒模型一致\r\n    cursor: pointer;                 // 鼠标指针样式\r\n    transition: background-color 0.2s ease; // 背景色过渡动画\r\n\r\n    // 禁用移动端点击高亮效果\r\n    -webkit-tap-highlight-color: transparent; // 禁用 WebKit 点击高亮\r\n    -webkit-touch-callout: none;     // 禁用长按菜单\r\n    -webkit-user-select: none;       // 禁用文本选择\r\n    -moz-user-select: none;          // Firefox 禁用文本选择\r\n    -ms-user-select: none;           // IE 禁用文本选择\r\n    user-select: none;               // 标准属性禁用文本选择\r\n}\r\n\r\n// 回复区域悬停效果\r\n.reply:hover {\r\n    background-color: rgba(0, 0, 0, 0.03); // 淡灰色悬停背景\r\n}\r\n\r\n// 移除点击效果，保持原始背景\r\n.reply:active {\r\n    background-color: transparent; // 点击时保持透明背景\r\n}\r\n\r\n\r\n\r\n.reply-content {\r\n  font-size: 14px;                 // 适中的字体大小\r\n  color: #333;                     // 深色文字，提高可读性\r\n  line-height: 1.4;                // 适中的行高\r\n  margin-top: 6px;                 // 减少上边距\r\n  margin-bottom: 4px;              // 减少下边距\r\n  white-space: pre-wrap;           // 保持空格和换行，允许自动换行\r\n  word-wrap: break-word;           // 长单词自动换行\r\n  word-break: break-word;          // 在任意字符间换行\r\n  width: 100%;                     // 占满可用宽度\r\n  box-sizing: border-box;          // 包含padding和border\r\n}\r\n\r\n// 旧的绝对定位时间样式已移除，时间现在显示在头部\r\n\r\n// @医生区域样式 - 去掉背景色，保持简洁\r\n.mentioned-section {\r\n    margin: 16rpx 0;\r\n    padding: 16rpx 24rpx;\r\n}\r\n\r\n.mentioned-doctors-list {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    align-items: center;\r\n    gap: 8rpx;\r\n}\r\n\r\n.doctor-tag {\r\n    font-size: 24rpx;\r\n    color: #07C160;\r\n    font-weight: 500;\r\n    background-color: rgba(7, 193, 96, 0.1);\r\n    padding: 8rpx 16rpx;\r\n    border-radius: 8rpx;\r\n    margin-right: 8rpx;\r\n    margin-bottom: 4rpx;\r\n}\r\n\r\n// 回复时间样式 - 显示在回复框右下角，删除按钮下面\r\n.chat-time {\r\n  font-size: 12px;                 // 保持小字体\r\n  color: #999;\r\n  position: absolute;              // 绝对定位\r\n  bottom: 16px;                    // 距离底部16px\r\n  right: 20px;                     // 距离右边20px\r\n  white-space: nowrap;             // 防止时间换行\r\n  min-width: 120px;                // 确保有足够空间显示完整的年月日时间\r\n  text-align: right;               // 文本右对齐\r\n}\r\n.reply-part {\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: flex-start;     // 顶端对齐\r\n    flex: 1;                         // 占据剩余空间\r\n    min-width: 0;                    // 确保flex子项能正确收缩\r\n    margin-left: 16px;               // 增加与头像的间距\r\n    padding-right: 60px;             // 为删除按钮预留更多空间\r\n}\r\n\r\n.reply-name-container {\r\n    display: flex;\r\n    flex-direction: column;\r\n    margin-left: 0;                  // 移除左边距\r\n    margin-bottom: 0;                // 移除底边距\r\n}\r\n\r\n.reply-name {\r\n    font-size: 15px;                 // 稍微减小字体\r\n    color: #333;\r\n    line-height: 1.3;\r\n    font-weight: 500;                // 适中的字重\r\n    margin-bottom: 2px;              // 减少与内容的间距\r\n}\r\n\r\n// @ 回复信息样式\r\n.reply-mention {\r\n    margin: 4px 0;                   // 上下间距\r\n}\r\n\r\n.mention-text {\r\n    font-size: 13px;                 // 稍小的字体\r\n    color: #07C160;                  // 主题绿色\r\n    background-color: #F0F9F5;       // 淡绿色背景\r\n    padding: 2px 6px;                // 内边距\r\n    border-radius: 4px;              // 圆角\r\n    font-weight: 400;                // 正常字重\r\n    display: block;                  // 块级元素\r\n    white-space: nowrap;             // 不换行\r\n    overflow: hidden;                // 隐藏超出部分\r\n    text-overflow: ellipsis;         // 显示省略号\r\n    max-width: 100%;                 // 最大宽度为容器宽度\r\n}\r\n\r\n// replyContent 引用内容样式\r\n.reply-content-quote {\r\n    margin: 4px 0;                   // 上下间距\r\n    padding: 8px 12px;               // 内边距\r\n    background-color: #f5f5f5;       // 浅灰色背景\r\n    border-left: 3px solid #ddd;     // 左侧边框，表示引用\r\n    border-radius: 4px;              // 圆角\r\n}\r\n\r\n.quote-text {\r\n    font-size: 13px;                 // 稍小的字体\r\n    color: #666;                     // 灰色文字\r\n    font-style: italic;              // 斜体，表示引用\r\n    line-height: 1.4;                // 行高\r\n}\r\n\r\n\r\n\r\n.delete-button {\r\n  position: absolute;\r\n  top: 16px;                   // 距离回复容器顶部16px\r\n  right: 20px;                 // 距离回复容器右边20px\r\n  width: 28px;\r\n  height: 28px;\r\n  background: rgba(128, 128, 128, 0.1);\r\n  border-radius: 6px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: pointer;\r\n  opacity: 0.8;\r\n  transition: all 0.3s ease;\r\n  z-index: 10;\r\n  border: 1px solid rgba(128, 128, 128, 0.2);\r\n  box-sizing: border-box;      // 确保盒模型一致\r\n  flex-shrink: 0;              // 防止按钮被压缩\r\n  flex-grow: 0;                // 防止按钮被拉伸\r\n}\r\n\r\n.delete-button:hover {\r\n  opacity: 1;\r\n  transform: scale(1.05);\r\n  background: rgba(128, 128, 128, 0.15);\r\n  border-color: rgba(128, 128, 128, 0.3);\r\n}\r\n\r\n// 垃圾筒图标样式\r\n.trash-icon {\r\n  position: relative;\r\n  width: 12px;\r\n  height: 14px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  flex-shrink: 0;              // 防止图标被压缩\r\n  flex-grow: 0;                // 防止图标被拉伸\r\n}\r\n\r\n.trash-lid {\r\n  width: 14px;\r\n  height: 2px;\r\n  background: #666;\r\n  border-radius: 1px;\r\n  position: relative;\r\n  margin-bottom: 1px;\r\n  flex-shrink: 0;              // 防止盖子被压缩\r\n}\r\n\r\n.trash-lid::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: -2px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 6px;\r\n  height: 2px;\r\n  background: #666;\r\n  border-radius: 1px 1px 0 0;\r\n}\r\n\r\n.trash-body {\r\n  width: 10px;\r\n  height: 11px;\r\n  background: transparent;\r\n  border: 1.5px solid #666;\r\n  border-top: none;\r\n  border-radius: 0 0 2px 2px;\r\n  position: relative;\r\n  display: flex;\r\n  justify-content: space-around;\r\n  align-items: flex-start;\r\n  padding-top: 2px;\r\n  flex-shrink: 0;              // 防止主体被压缩\r\n  box-sizing: border-box;      // 确保边框计算一致\r\n}\r\n\r\n.trash-line {\r\n  width: 1px;\r\n  height: 6px;\r\n  background: #666;\r\n  border-radius: 0.5px;\r\n  flex-shrink: 0;              // 防止竖线被压缩\r\n}\r\n\r\n// 暂无回复容器 - 确保完美居中\r\n.empty-reply-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 40px 20px;               // 增加上下内边距\r\n  background: transparent;          // 透明背景\r\n  margin: 0 auto;                   // 水平居中\r\n  width: 100%;                      // 占满宽度\r\n  min-height: 200px;                // 设置最小高度，确保有足够空间居中\r\n  text-align: center;               // 文本居中\r\n}\r\n\r\n// 图标样式 - 居中显示\r\n.empty-icon {\r\n  font-size: 32px;                  // 适中的图标大小\r\n  margin-bottom: 12px;              // 适中间距\r\n  opacity: 0.6;                     // 适中的透明度\r\n  display: block;                   // 确保块级显示\r\n  text-align: center;               // 文本居中\r\n}\r\n\r\n// 主文本样式 - 居中显示\r\n.message-empty {\r\n  color: #666;                      // 适中的颜色\r\n  font-size: 16px;                  // 适中的字体\r\n  font-weight: 500;                 // 稍微加粗\r\n  margin-bottom: 8px;               // 适中间距\r\n  text-align: center;               // 文本居中\r\n  display: block;                   // 确保块级显示\r\n  width: 100%;                      // 占满宽度\r\n}\r\n\r\n// 副标题样式 - 居中显示\r\n.empty-subtitle {\r\n  color: #999;                      // 适中的颜色\r\n  font-size: 14px;                  // 适中字体\r\n  font-weight: 400;\r\n  text-align: center;               // 文本居中\r\n  display: block;                   // 确保块级显示\r\n  width: 100%;                      // 占满宽度\r\n}\r\n\r\n\r\n\r\n.reply-window-fixed {\r\n  position: fixed !important;       // 改回固定定位\r\n  left: 16px !important;            // 左边距\r\n  right: 16px !important;           // 右边距\r\n  bottom: 20px !important;          // 距离底部20px\r\n  background: #fff !important;      // 恢复白色背景\r\n  border-radius: 16px !important;   // 全圆角，悬浮框效果\r\n  padding: 16px !important;         // 统一内边距\r\n  box-sizing: border-box;\r\n  box-shadow: 0 4px 20px rgba(0,0,0,0.15) !important; // 增强阴影，悬浮效果\r\n  display: flex !important;\r\n  flex-direction: column;           // 垂直布局，@框在上，输入行在下\r\n  z-index: 9999 !important;         // 高层级\r\n  width: auto !important;           // 自动宽度\r\n  min-height: 60px !important;      // 减少最小高度\r\n  visibility: visible !important;   // 确保可见\r\n  opacity: 1 !important;            // 确保不透明\r\n  border: 1px solid #e0e0e0;        // 添加边框\r\n}\r\n\r\n// @回复提示框紧贴输入框上方\r\n.reply-tip-above-input {\r\n  background: #f8f9fa;\r\n  color: #666;\r\n  border-radius: 12px;              // 增加圆角\r\n  padding: 10px 16px;               // 调整内边距，左右增加\r\n  font-size: 13px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  box-sizing: border-box;\r\n  border: 1px solid #e9ecef;        // 添加边框\r\n  margin: 0 0 12px 0;               // 移除左右边距，只保留底部边距\r\n  min-height: 40px;                 // 适中的最小高度\r\n  width: 100%;                      // 占满整个宽度\r\n  animation: slideDown 0.3s ease-out;\r\n}\r\n\r\n// 输入框行布局\r\n.reply-input-row {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;              // 垂直居中对齐\r\n  gap: 8px;                         // 元素间距\r\n  min-height: 50px;                 // 最小高度\r\n}\r\n.reply-avatar {\r\n  width: 42px !important;           // 稍微增大头像\r\n  height: 42px !important;\r\n  border-radius: 50%;\r\n  flex-shrink: 0 !important;\r\n  flex-grow: 0 !important;\r\n  object-fit: cover;\r\n  background-color: #f0f0f0;\r\n  display: block !important;\r\n  box-sizing: border-box;\r\n  border: 2px solid #fff;           // 添加白色边框\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1); // 添加轻微阴影\r\n}\r\n\r\n// 回复中的图片样式\r\n.reply-images {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n  margin: 8px 0;\r\n}\r\n\r\n.reply-image {\r\n  width: 80px;\r\n  height: 80px;\r\n  border-radius: 8px;\r\n  object-fit: cover;\r\n  cursor: pointer;\r\n  border: 1px solid #e0e0e0;\r\n}\r\n\r\n// 输入框中的多张图片容器\r\n.reply-input-images {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 6px;\r\n  margin: 8px 16px 16px 16px;  // 简单的外边距\r\n  max-width: calc(100% - 32px);\r\n  justify-content: flex-start;\r\n  padding: 8px;                     // 简单的内边距\r\n}\r\n\r\n// 输入框中的单张图片预览\r\n.reply-input-image {\r\n  position: relative;\r\n  display: inline-block;\r\n  flex-shrink: 0;\r\n  width: calc(33.333% - 4px);  // 一行3个，减去gap的影响\r\n  max-width: 70px;  // 减小最大宽度\r\n  cursor: pointer;\r\n}\r\n\r\n.input-image-preview {\r\n  width: 100%;\r\n  height: 70px;  // 减小固定高度\r\n  border-radius: 6px;\r\n  object-fit: cover;\r\n  border: 1px solid #e0e0e0;\r\n  display: block;\r\n  transition: transform 0.2s ease;\r\n\r\n  &:hover {\r\n    transform: scale(1.05);\r\n    border-color: #07C160;\r\n  }\r\n}\r\n\r\n.remove-image {\r\n  position: absolute;\r\n  top: -4px;\r\n  right: -4px;\r\n  width: 16px;\r\n  height: 16px;\r\n  background: #ff4757;\r\n  color: white;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 10px;\r\n  cursor: pointer;\r\n  font-weight: bold;\r\n  line-height: 1;\r\n  border: 1px solid white;\r\n  box-shadow: 0 1px 3px rgba(0,0,0,0.3);\r\n  z-index: 10;\r\n\r\n  &:hover {\r\n    background: #ff3742;\r\n    transform: scale(1.1);\r\n  }\r\n}\r\n\r\n\r\n\r\n// 滑入动画 - 从上往下滑入\r\n@keyframes slideDown {\r\n  from {\r\n    transform: translateY(-100%);\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    transform: translateY(0);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n.reply-content-text {\r\n  flex: 1;                         // 占据剩余空间\r\n  white-space: nowrap;             // 单行显示\r\n  overflow: hidden;                // 超出隐藏\r\n  text-overflow: ellipsis;         // 超出显示省略号\r\n  margin-right: 8px;               // 与取消按钮的间距\r\n  max-width: calc(100% - 60px);    // 为取消按钮预留空间，让文本区域更大\r\n}\r\n\r\n.cancel-reply {\r\n  color: #07C160;\r\n  cursor: pointer;\r\n  flex-shrink: 0;                  // 不被压缩\r\n  font-weight: 500;\r\n  font-size: 12px;                 // 减小字体\r\n  padding: 4px 8px;                // 添加内边距\r\n  border-radius: 8px;              // 添加圆角\r\n  transition: background-color 0.2s ease; // 添加过渡效果\r\n\r\n  &:hover {\r\n    background: rgba(7, 193, 96, 0.1); // 悬停时的淡绿色背景\r\n  }\r\n}\r\n\r\n.reply-input-area {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;         // 垂直居中\r\n  min-width: 0;                    // 确保能正确收缩\r\n}\r\n\r\n.reply-textarea {\r\n  width: 100% !important;\r\n  min-height: 36px !important;    // 减小最小高度\r\n  max-height: 100px !important;   // 减小最大高度\r\n  padding: 10px 12px !important;  // 调整内边距\r\n  border-radius: 18px !important; // 增加圆角，更现代\r\n  border: 1px solid #e0e0e0;      // 更淡的边框\r\n  background: #f8f9fa;            // 淡灰色背景\r\n  resize: none !important;        // 禁止手动调整大小\r\n  font-size: 14px;                // 减小字体\r\n  box-sizing: border-box !important;\r\n  overflow-y: auto;               // 超过最大高度时显示滚动条\r\n  line-height: 18px !important;   // 调整行高\r\n  vertical-align: top !important; // 防止基线对齐导致的高度变化\r\n  word-wrap: break-word !important; // 强制换行\r\n  white-space: pre-wrap !important; // 保持空格和换行，但允许自动换行\r\n  transition: border-color 0.2s ease; // 添加过渡效果\r\n\r\n  &:focus {\r\n    border-color: #07C160;          // 聚焦时的边框颜色\r\n    background: #fff;               // 聚焦时的背景色\r\n    outline: none;\r\n  }\r\n}\r\n\r\n.upload-button {\r\n  width: 28px;\r\n  height: 28px;\r\n  border-radius: 6px;               // 增加圆角\r\n  background: #f0f0f0;              // 更淡的背景\r\n  object-fit: cover;\r\n  flex-shrink: 0;\r\n  flex-grow: 0;\r\n  transition: background-color 0.2s ease; // 添加过渡效果\r\n\r\n  &:hover {\r\n    background: #e0e0e0;            // 悬停效果\r\n  }\r\n}\r\n\r\n.sub-reply{\r\n  background: #07C160;\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 18px;              // 增加圆角\r\n  font-size: 14px;                  // 减小字体\r\n  cursor: pointer;\r\n  height: 36px;                     // 减小高度\r\n  padding: 0 16px;                  // 添加水平内边距\r\n  flex-shrink: 0;                   // 防止按钮被压缩\r\n  flex-grow: 0;\r\n  transition: background-color 0.2s ease; // 添加过渡效果\r\n\r\n  &:hover {\r\n    background: #06a84f;            // 悬停时的深绿色\r\n  }\r\n\r\n  &:active {\r\n    background: #059142;            // 点击时的更深绿色\r\n  }\r\n\r\n  &.disabled {\r\n    background: #cccccc;            // 禁用状态的灰色背景\r\n    color: #999999;                 // 禁用状态的灰色文字\r\n    cursor: not-allowed;            // 禁用状态的鼠标样式\r\n\r\n    &:hover {\r\n      background: #cccccc;          // 禁用状态下悬停不变色\r\n    }\r\n\r\n    &:active {\r\n      background: #cccccc;          // 禁用状态下点击不变色\r\n    }\r\n  }\r\n}\r\n\r\n\r\n\r\n</style>", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-data/chat/chatDetail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "useUserStore", "onLoad", "computed", "replyContent", "nextTick", "http", "uni", "onMounted", "res"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmOA,MAAM,YAAY;;;;AAjCIA,kBAAAA,IAAgC,IAAI;AAK1D,UAAM,YAAYC,WAAAA,aAAa;AACzB,UAAA,eAAeD,kBAAI,EAAE;AACrB,UAAA,cAAcA,cAAc,IAAA,EAAE;AAC9B,UAAA,cAAcA,kBAAI,KAAK;AAEvB,UAAA,gBAAgBA,cAAc,IAAA,EAAE;AAGhC,UAAA,yBAAyB,CAAC,UAAe;;AACzC,UAAA;AACI,cAAA,cAAa,oCAAO,WAAP,mBAAe;AAC1B,gBAAA,IAAI,cAAc,UAAU;AAGpC,cAAM,OAAO,MAAM;AAAA,eACZ,OAAO;AACN,gBAAA,MAAM,gBAAgB,KAAK;AAAA,MAAA;AAAA,IAEvC;AAGM,UAAA,qBAAqB,CAAC,QAAgB;AACvB,yBAAA,KAAK,cAAc,KAAK;AAAA,IAC7C;AACM,UAAA,QAAQA,cAAW,IAAA,EAAE;AACrB,UAAA,kBAAkBA,kBAAI,EAAE;AACxB,UAAA,mBAAmBA,cAAW,IAAA,EAAE;AAGhC,UAAA,cAAcA,kBAAgB,IAAI;AAClC,UAAA,gBAAgBA,kBAAI,KAAK;AAM/BE,kBAAA,OAAO,CAAC,YAAY;AACV,cAAA,IAAI,aAAa,OAAO;AAChB,sBAAA,QAAQ,QAAQ,oBAAoB;AAC5C,cAAA,IAAI,oBAAoB,gBAAgB,KAAK;AAAA,IAAA,CAEtD;AAGK,UAAA,eAAe,CAAC,WAAsC;AAE1D,UAAI,CAAC,UAAU,OAAO,KAAA,MAAW,IAAI;AAC5B,eAAA;AAAA,MAAA;AAIL,UAAA,OAAO,WAAW,MAAM,GAAG;AACtB,eAAA;AAAA,MAAA;AAIL,UAAA,OAAO,WAAW,GAAG,GAAG;AAC1B,eAAO,4BAA4B;AAAA,MAAA;AAIrC,aAAO,oCAAoC;AAAA,IAC7C;AAGM,UAAA,oBAAoB,CAAC,UAAe;AACxC,YAAM,OAAO,MAAM;AAAA,IACrB;AAKM,UAAA,YAAYC,cAAAA,SAAS,MAAM;AACvB,cAAA,IAAI,YAAY,UAAU,QAAQ;AAC1C,cAAQ,IAAI,cAAc,UAAU,SAAS,MAAM;AACnD,YAAM,YAAY,aAAa,UAAU,SAAS,MAAM;AAChD,cAAA,IAAI,eAAe,SAAS;AAC7B,aAAA;AAAA,IAAA,CACR;AAGK,UAAA,YAAYA,cAAAA,SAAS,MAAM;AAC/B,aAAO,aAAa,MAAM,KAAA,EAAO,SAAS,KAAK,CAAC,aAAa;AAAA,IAAA,CAC9D;AAKK,UAAA,qBAAqBA,cAAAA,SAAS,MAAM;AACpC,UAAA,CAAC,YAAY,OAAO;AACf,eAAA;AAAA,MAAA;AAIL,UAAA,YAAY,MAAM,QAAQ,YAAY,MAAM,KAAK,WAAW,IAAI;AAClE,eAAO,YAAY,MAAM,KAAK,SAAS,KACnC,YAAY,MAAM,KAAK,UAAU,GAAG,EAAE,IAAI,QAC1C,YAAY,MAAM;AAAA,MAAA;AAIpB,UAAA,YAAY,MAAM,SAAS,YAAY,MAAM,MAAM,WAAW,IAAI;AAC7D,eAAA;AAAA,MAAA;AAIF,aAAA;AAAA,IAAA,CACR;AAGK,UAAA,mBAAmB,CAAC,WAAmB;AACvC,UAAA,CAAC,UAAU,WAAW,IAAI;AACrB,eAAA;AAAA,MAAA;AAGD,cAAA,IAAI,WAAW,MAAM;AACrB,cAAA,IAAI,WAAW,MAAM,KAAK;AAGlC,YAAM,eAAe,MAAM,MAAM,KAAK,CAAK,MAAA,EAAE,WAAW,MAAM;AAC9D,UAAI,cAAc;AACR,gBAAA,IAAI,eAAe,aAAa,QAAQ;AAChD,eAAO,aAAa;AAAA,MAAA;AAKtB,UAAI,aAAa,MAAM,WAAW,aAAa,MAAM,QAAQ,aAAa;AACxE,gBAAQ,IAAI,uBAAuB,aAAa,MAAM,QAAQ,WAAW;AAClE,eAAA,aAAa,MAAM,QAAQ;AAAA,MAAA;AAGpC,cAAQ,IAAI,cAAc;AACnB,aAAA;AAAA,IACT;AAGM,UAAA,qBAAqB,CAAC,SAAiB,iBAAsB;AACjE,cAAQ,IAAI,YAAY;AAChB,cAAA,IAAI,gBAAgB,OAAO;AAC3B,cAAA,IAAI,qBAAqB,YAAY;AACrC,cAAA,IAAI,kCAAkC,aAAa,YAAY;AAEnE,UAAA,CAAC,WAAW,YAAY,IAAI;AAC9B,gBAAQ,IAAI,aAAa;AAClB,eAAA;AAAA,MAAA;AAIL,UAAA,gBAAgB,aAAa,cAAc;AACrC,gBAAA,IAAI,6BAA6B,aAAa,YAAY;AAGlE,YAAI,OAAO,aAAa,iBAAiB,YAAY,aAAa,aAAa,MAAM;AACnF,gBAAM,YAAY,aAAa,aAAa,KAAK,KAAK;AAClD,cAAA,aAAa,cAAc,IAAI;AAC3B,kBAAA,YAAY,UAAU,SAAS,KACjC,UAAU,UAAU,GAAG,EAAE,IAAI,QAC7B;AACI,oBAAA,IAAI,8BAA8B,SAAS;AAC5C,mBAAA;AAAA,UAAA;AAAA,QACT;AAIE,YAAA,OAAO,aAAa,iBAAiB,YAAY,aAAa,aAAa,WAAW,IAAI;AAC5F,gBAAM,YAAY,aAAa,aAAa,SAAS,KACjD,aAAa,aAAa,UAAU,GAAG,EAAE,IAAI,QAC7C,aAAa;AACT,kBAAA,IAAI,+BAA+B,SAAS;AAC7C,iBAAA;AAAA,QAAA;AAAA,MACT;AAIF,YAAM,cAAc,MAAM,MAAM,KAAK,CAAC,QAAa,OAAO,IAAI,EAAE,MAAM,OAAO,OAAO,CAAC;AAErF,cAAQ,IAAI,kBAAkB;AAAA,QAC5B,QAAQ,CAAC,CAAC;AAAA,QACV,YAAY,MAAM,MAAM,IAAI,CAAA,MAAK,EAAE,EAAE;AAAA,QACrC,YAAY;AAAA,MAAA,CACb;AAED,UAAI,aAAa;AACf,gBAAQ,IAAI,aAAa;AAAA,UACvB,IAAI,YAAY;AAAA,UAChB,MAAM,YAAY;AAAA,UAClB,SAAS,CAAC,CAAC,YAAY;AAAA,UACvB,UAAU,OAAO,YAAY;AAAA,UAC7B,YAAY,YAAY,OAAO,YAAY,KAAK,SAAS;AAAA,QAAA,CAC1D;AAED,YAAI,YAAY,QAAQ,YAAY,KAAK,WAAW,IAAI;AAEtD,gBAAM,YAAY,YAAY,KAAK,SAAS,KACxC,YAAY,KAAK,UAAU,GAAG,EAAE,IAAI,QACpC,YAAY;AACT,iBAAA;AAAA,QAAA,OACF;AAEL,cAAI,YAAY,SAAS,YAAY,MAAM,WAAW,IAAI;AACjD,mBAAA;AAAA,UAAA;AAEF,iBAAA;AAAA,QAAA;AAAA,MACT;AAIF,cAAQ,IAAI,qBAAqB;AACjC,UAAI,aAAa,MAAM,WAAW,aAAa,MAAM,QAAQ,UAAU;AAC/D,cAAA,eAAe,aAAa,MAAM,QAAQ;AAC1C,cAAA,YAAY,aAAa,SAAS,KACpC,aAAa,UAAU,GAAG,EAAE,IAAI,QAChC;AACI,gBAAA,IAAI,eAAe,SAAS;AAC7B,eAAA;AAAA,MAAA;AAGT,cAAQ,IAAI,aAAa;AAClB,aAAA;AAAA,IACT;AAGM,UAAA,sBAAsB,CAACC,kBAA8B;AACzD,UAAI,CAACA,eAAc;AACV,eAAA;AAAA,MAAA;AAIT,UAAI,OAAOA,kBAAiB,YAAYA,kBAAiB,MAAM;AACrD,gBAAA,IAAI,kCAAkCA,aAAY;AAG1D,YAAIA,cAAa,QAAQ,OAAOA,cAAa,SAAS,UAAU;AACxD,gBAAA,UAAUA,cAAa,KAAK,KAAK;AACvC,cAAI,WAAW,YAAY,MAAM,YAAY,UAAU,YAAY,aAAa;AACtE,oBAAA,IAAI,8BAA8B,OAAO;AAC1C,mBAAA;AAAA,UAAA;AAAA,QACT;AAIF,YAAIA,cAAa,UAAUA,cAAa,WAAW,MAAM;AAChD,iBAAA;AAAA,QAAA;AAGF,eAAA;AAAA,MAAA;AAIL,UAAA,OAAOA,kBAAiB,UAAU;AAC9B,cAAA,UAAUA,cAAa,KAAK;AAElC,YAAI,YAAY,MAAM,YAAY,qBAAqB,YAAY,UAAU,YAAY,aAAa;AAC7F,iBAAA;AAAA,QAAA;AAEF,eAAA;AAAA,MAAA;AAIL,UAAA;AACF,cAAM,YAAY,OAAOA,aAAY,EAAE,KAAK;AAE5C,YAAI,cAAc,qBAAqB,cAAc,UAAU,cAAc,eAAe,cAAc,IAAI;AACrG,iBAAA;AAAA,QAAA;AAEF,eAAA;AAAA,eACA,OAAO;AACN,gBAAA,KAAK,qBAAqB,KAAK;AAChC,eAAA;AAAA,MAAA;AAAA,IAEX;AAGM,UAAA,aAAa,CAAC,YAA4B;AAC1C,UAAA,CAAC,WAAW,YAAY,KAAK;AACxB,eAAA;AAAA,MAAA;AAGL,UAAA;AAEE,YAAA,QAAQ,SAAS,GAAG,GAAG;AACnB,gBAAA,QAAQ,QAAQ,MAAM,GAAG;AAC3B,cAAA,MAAM,UAAU,GAAG;AACf,kBAAA,WAAW,MAAM,CAAC;AAClB,kBAAA,WAAW,MAAM,CAAC;AAGxB,gBAAI,SAAS,SAAS,GAAG,KAAK,SAAS,SAAS,GAAG,GAAG;AAC9C,oBAAA,iBAAiB,SAAS,MAAM,GAAG;AACnC,oBAAA,iBAAiB,SAAS,MAAM,GAAG;AAEzC,kBAAI,eAAe,UAAU,KAAK,eAAe,UAAU,GAAG;AACtD,sBAAA,OAAO,eAAe,CAAC;AACvB,sBAAA,QAAQ,eAAe,CAAC;AACxB,sBAAA,MAAM,eAAe,CAAC;AACtB,sBAAA,OAAO,eAAe,CAAC;AACvB,sBAAA,SAAS,eAAe,CAAC;AAExB,uBAAA,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI,IAAI,MAAM;AAAA,cAAA;AAAA,YAClD;AAIK,mBAAA;AAAA,UAAA;AAAA,QACT;AAIK,eAAA;AAAA,eACA,OAAO;AACN,gBAAA,KAAK,YAAY,KAAK;AACvB,eAAA;AAAA,MAAA;AAAA,IAEX;AAGM,UAAA,eAAe,CAAC,QAAa;AAEjC,YAAM,sBAAsB,IAAI,eAAe,IAAI,gBAAgB,MAAM,IAAI,gBAAgB;AAC7F,YAAM,kBAAkB,IAAI,WAAW,IAAI,YAAY,MAAM,IAAI,YAAY;AAC7E,YAAM,UAAU,uBAAuB;AAEhC,aAAA;AAAA,IACT;AAIM,UAAA,iBAAiB,CAAC,QAAa;AACnC,cAAQ,IAAI,cAAc;AAAA,QACxB,QAAQ,IAAI;AAAA,QACZ,UAAU,IAAI;AAAA,QACd,IAAI,IAAI;AAAA,QACR,MAAM,IAAI;AAAA,MAAA,CACX;AAED,kBAAY,QAAQ;AACpB,oBAAc,QAAQ;AAEtBC,oBAAAA,WAAS,MAAM;AACb,sBAAc,QAAQ;AAAA,MAAA,CAEvB;AAAA,IACH;AAEA,UAAM,eAAeL,cAAAA,IAAI;AAAA,MACvB,kBAAkB;AAAA,MAClB,SAAS;AAAA,QACP,aAAa;AAAA,QACb,eAAe;AAAA,QACf,QAAQ,CAAC;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA;AAAA,QACP,YAAY;AAAA;AAAA,QACZ,YAAY;AAAA;AAAA,MACd;AAAA,MACA,OAAO;AAAA,QACL,IAAI;AAAA,QACJ,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,OAAO,CAAC;AAAA,QACR,MAAM;AAAA,MAAA;AAAA,IACR,CAGD;AAGK,UAAA,oBAAoB,CAAO,cAAiC;AAC5D,UAAA;AACM,gBAAA,IAAI,qBAAqB,SAAS;AAG1C,YAAI,WAAqB,CAAC;AACtB,YAAA,OAAO,cAAc,UAAU;AACtB,qBAAA,UAAU,MAAM,GAAG,EAAE,OAAO,CAAM,OAAA,GAAG,KAAK,MAAM,EAAE;AAAA,QACpD,WAAA,MAAM,QAAQ,SAAS,GAAG;AACnC,qBAAW,UAAU,OAAO,CAAA,OAAM,MAAM,GAAG,WAAW,EAAE;AAAA,QAAA;AAGtD,YAAA,SAAS,WAAW,GAAG;AACzB,kBAAQ,IAAI,WAAW;AACvB,2BAAiB,QAAQ,CAAC;AAC1B;AAAA,QAAA;AAIF,cAAM,WAAW,MAAMM,gBAAK,IAAI,sBAAsB;AAElD,YAAA,SAAS,WAAW,SAAS,QAAQ;AACvC,gBAAM,aAAa,SAAS;AAG5B,gBAAM,kBAAkB,WAAW;AAAA,YAAO,CAAC,WACzC,SAAS,SAAS,OAAO,OAAO,EAAE,CAAC,KAAK,SAAS,SAAS,OAAO,OAAO,MAAM,CAAC;AAAA,UACjF;AAEA,2BAAiB,QAAQ;AACjB,kBAAA,IAAI,cAAc,eAAe;AAAA,QAAA,OACpC;AACG,kBAAA,MAAM,aAAa,SAAS,OAAO;AAC3C,2BAAiB,QAAQ,CAAC;AAAA,QAAA;AAAA,eAErB,OAAO;AACN,gBAAA,MAAM,aAAa,KAAK;AAChC,yBAAiB,QAAQ,CAAC;AAAA,MAAA;AAAA,IAE9B;AAEA,UAAM,aAAa,MAAY;AACzB,UAAA;AACF,oBAAY,QAAQ;AAGhB,YAAA,CAAC,gBAAgB,OAAO;AAC1B,oBAAU,cAAc;AACxB;AAAA,QAAA;AAGM,gBAAA,IAAI,2BAA2B,gBAAgB,KAAK;AAG5D,cAAM,SAAS;AAAA,UACb,kBAAkB,gBAAgB;AAAA,UAClC,SAAS,UAAU,SAAS,UAAU;AAAA,UACtC,UAAU,OAAO,UAAU,SAAS,YAAY;AAAA,QAClD;AACQ,gBAAA,IAAI,eAAe,MAAM;AACjCA,mBAAA,KAAK,IAAI,4BAA6B,MAAO,EAAE,KAAK,CAAC,QAAY;AAC7D,kBAAQ,IAAI,sBAAsB;AAClC,kBAAQ,IAAI,uCAAuC;AACnD,kBAAQ,IAAI,YAAY,KAAK,UAAU,QAAQ,MAAM,CAAC,CAAC;AACvD,kBAAQ,IAAI,cAAc,KAAK,UAAU,KAAK,MAAM,CAAC,CAAC;AACtD,kBAAQ,IAAI,WAAW,IAAI,UAAU,OAAO,IAAI;AACxC,kBAAA,IAAI,YAAY,IAAI,IAAI;AACxB,kBAAA,IAAI,YAAY,IAAI,OAAO;AAC3B,kBAAA,IAAI,YAAY,IAAI,SAAS;AAElC,cAAA,IAAI,WAAW,IAAI,QAAO;AACnB,oBAAA,IAAI,yBAAyB,KAAK,UAAU,IAAI,QAAQ,MAAM,CAAC,CAAC;AACxE,oBAAQ,IAAI,YAAY;AACxB,oBAAQ,IAAI,yBAAyB,IAAI,OAAO,gBAAgB;AAChE,oBAAQ,IAAI,mBAAmB,IAAI,OAAO,UAAU,OAAO,KAAK;AACxD,oBAAA,IAAI,iBAAiB,IAAI,OAAO,QAAQ,UAAU,IAAI,OAAO,MAAM,MAAM,KAAK,KAAK;AAEvF,gBAAA,IAAI,OAAO,SAAS;AACtB,sBAAQ,IAAI,oBAAoB;AAAA,YAAA;AAI9B,gBAAA,IAAI,OAAO,SAAS,MAAM,QAAQ,IAAI,OAAO,KAAK,GAAG;AACvD,sBAAQ,IAAI,kBAAkB;AAC9B,sBAAQ,IAAI,aAAa,IAAI,OAAO,MAAM,MAAM;AAChD,kBAAI,OAAO,MAAM,QAAQ,CAAC,MAAW,UAAkB;AACrD,wBAAQ,IAAI,WAAW,QAAQ,CAAC,KAAK;AAAA,kBACnC,IAAI,KAAK;AAAA,kBACT,KAAK,KAAK;AAAA,kBACV,MAAM,KAAK;AAAA,kBACX,MAAM,KAAK;AAAA,kBACX,MAAM,KAAK;AAAA,kBACX,IAAI,KAAK;AAAA,kBACT,MAAM,KAAK;AAAA,kBACX,MAAM,KAAK;AAAA,kBACX,QAAQ,KAAK;AAAA,kBACb,MAAM,KAAK;AAAA,kBACX,IAAI,KAAK;AAAA,gBAAA,CACV;AAAA,cAAA,CACF;AAAA,YAAA;AAEH,oBAAQ,IAAI,kCAAkC;AAEtC,oBAAA,IAAI,gBAAgB,IAAI,MAAM;AAC9B,oBAAA,IAAI,uBAAuB,gBAAgB,KAAK;AACxD,oBAAQ,IAAI,yBAAyB,IAAI,OAAO,gBAAgB;AAGhE,kBAAM,YAAY,IAAI,OAAO,QAAQ,UAAU,CAAC;AACxC,oBAAA,IAAI,cAAc,SAAS;AAEnC,0BAAc,QAAQ,UAAU,IAAI,CAAC,aAAqB;AAEpD,kBAAA,SAAS,WAAW,MAAM,GAAG;AACxB,uBAAA;AAAA,cAAA;AAIL,kBAAA,SAAS,WAAW,GAAG,GAAG;AAC5B,uBAAO,0BAA0B,QAAQ;AAAA,cAAA;AAI3C,qBAAO,+BAA+B,QAAQ;AAAA,YAAA,CAC/C;AAEO,oBAAA,IAAI,kBAAkB,cAAc,KAAK;AAGjD,kBAAM,YAAY,IAAI,OAAO,SAAS,CAAC;AAC/B,oBAAA,IAAI,WAAW,SAAS;AACxB,oBAAA,IAAI,WAAW,UAAU,MAAM;AAGvC,kBAAM,iBAAiB;AAKR,2BAAA,QAAQ,CAAC,MAAW,UAAkB;AAC3C,sBAAA,IAAI,MAAM,KAAK,KAAK;AAAA,gBAC1B,UAAU,KAAK;AAAA,gBACf,YAAY,KAAK;AAAA,gBACjB,MAAM,KAAK;AAAA,gBACX,YAAY,KAAK;AAAA,gBACjB,YAAY,KAAK;AAAA,gBACjB,cAAc,KAAK;AAAA,gBACnB,QAAQ,KAAK;AAAA,gBACb,OAAO,KAAK;AAAA,gBACZ,WAAW,OAAO,KAAK;AAAA,gBACvB,aAAa,MAAM,QAAQ,KAAK,KAAK,IAAI,KAAK,MAAM,SAAS;AAAA,gBAC7D,iBAAiB,aAAa,KAAK,KAAK;AAAA;AAAA,gBACxC,gBAAgB,aAAa,KAAK,KAAK,EAAE,IAAI,CAAA,QAAO,iBAAiB,GAAG,CAAC;AAAA;AAAA,gBACzE,aAAa,KAAK;AAAA,gBAClB,SAAS,KAAK;AAAA,gBACd,cAAc,aAAa,IAAI;AAAA,cAAA,CAChC;AAAA,YAAA,CACF;AAID,kBAAM,QAAQ;AACd,yBAAa,MAAM,UAAU,IAAI,OAAO,WAAW,CAAC;AACpD,yBAAa,MAAM,QAAQ,IAAI,OAAO,SAAS,CAAC;AAGhD,gBAAI,IAAI,OAAO,WAAW,IAAI,OAAO,QAAQ,WAAW,MAAM,QAAQ,IAAI,OAAO,QAAQ,OAAO,GAAG;AAEhF,+BAAA,QAAQ,IAAI,OAAO,QAAQ;AACpC,sBAAA,IAAI,4BAA4B,iBAAiB,KAAK;AAAA,YAAA,WACrD,IAAI,OAAO,WAAW,IAAI,OAAO,QAAQ,kBAAkB;AAEnD,+BAAA,QAAQ,IAAI,OAAO,QAAQ;AACpC,sBAAA,IAAI,qCAAqC,iBAAiB,KAAK;AAAA,YAAA,WAC9D,IAAI,OAAO,WAAW,IAAI,OAAO,QAAQ,WAAW;AAE7D,sBAAQ,IAAI,+BAA+B,IAAI,OAAO,QAAQ,SAAS;AACvE,gCAAkB,IAAI,OAAO,QAAQ,SAAS,EAAE,MAAM,CAAS,UAAA;AACrD,wBAAA,MAAM,aAAa,KAAK;AAAA,cAAA,CACjC;AAAA,YAAA,OACI;AACL,+BAAiB,QAAQ,CAAC;AAC1B,sBAAQ,IAAI,cAAc;AAAA,YAAA;AAAA,UAC5B,OACK;AACL,oBAAQ,IAAI,WAAW;AACf,oBAAA,IAAI,gBAAgB,IAAI,OAAO;AAC/B,oBAAA,IAAI,aAAa,IAAI,IAAI;AACzB,oBAAA,IAAI,gBAAgB,IAAI,OAAO;AAC/B,oBAAA,IAAI,eAAe,IAAI,MAAM;AACrC,oBAAQ,IAAI,aAAa,KAAK,UAAU,KAAK,MAAM,CAAC,CAAC;AAErDC,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO,IAAI,WAAW;AAAA,cACtB,MAAM;AAAA,YAAA,CACP;AAAA,UAAA;AAAA,QACH,CACD,EAAE,MAAM,CAAC,UAAe;AACvB,kBAAQ,IAAI,YAAY;AAChB,kBAAA,IAAI,aAAa,OAAO,KAAK;AACrC,kBAAQ,IAAI,aAAa,MAAM,WAAW,KAAK;AACvC,kBAAA,IAAI,eAAe,KAAK;AACxB,kBAAA,IAAI,aAAa,MAAM;AAC/B,kBAAQ,IAAI,wCAAwC;AAEpDA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAAA,CACX;AAAA,QAAA,CACF;AAAA,MAAA,UACH;AACA,oBAAY,QAAQ;AAAA,MAAA;AAAA,IAExB;AAEAC,kBAAAA,UAAU,MAAM;AACH,iBAAA;AAAA,IAAA,CACZ;AAKD,UAAM,oBAAoB,MAAM;AAC1B,UAAA,YAAY,MAAM,UAAU,GAAG;AACjCD,sBAAA,MAAI,UAAU,EAAE,OAAO,cAAc,MAAM,QAAQ;AACnD;AAAA,MAAA;AAGI,YAAA,iBAAiB,IAAI,YAAY,MAAM;AAC7CA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,YAAY,CAAC,OAAO;AAAA,QACpB,SAAS,CAAC,QAAa;AACrB,cAAI,IAAI,iBAAiB,IAAI,cAAc,SAAS,GAAG;AACrD,wBAAY,MAAM,KAAK,GAAG,IAAI,aAAa;AAAA,UAAA;AAAA,QAE/C;AAAA,QACA,MAAM,CAAC,QAAa;AACV,kBAAA,MAAM,WAAW,GAAG;AAC5BA,wBAAA,MAAI,UAAU,EAAE,OAAO,UAAU,MAAM,QAAQ;AAAA,QAAA;AAAA,MACjD,CACD;AAAA,IACH;AAGA,UAAM,mBAAmB,MAAM;AACzB,UAAA,YAAY,MAAM,UAAU,GAAG;AACjCA,sBAAA,MAAI,UAAU,EAAE,OAAO,cAAc,MAAM,QAAQ;AACnD;AAAA,MAAA;AAGFA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,YAAY,CAAC,QAAQ;AAAA,QACrB,SAAS,CAAC,QAAa;AACrB,cAAI,IAAI,iBAAiB,IAAI,cAAc,SAAS,GAAG;AACrD,wBAAY,MAAM,KAAK,GAAG,IAAI,aAAa;AAAA,UAAA;AAAA,QAE/C;AAAA,QACA,MAAM,CAAC,QAAa;AACV,kBAAA,MAAM,SAAS,GAAG;AAC1BA,wBAAA,MAAI,UAAU,EAAE,OAAO,QAAQ,MAAM,QAAQ;AAAA,QAAA;AAAA,MAC/C,CACD;AAAA,IACH;AAGA,UAAM,cAAc,MAAM;AACxBA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,SAAS,IAAI;AAAA,QACxB,SAAS,CAAC,QAAQ;AACZ,cAAA,IAAI,aAAa,GAAG;AACJ,8BAAA;AAAA,UAAA,WACT,IAAI,aAAa,GAAG;AACZ,6BAAA;AAAA,UAAA;AAAA,QAErB;AAAA,QACA,MAAM,CAAC,QAAQ;AACL,kBAAA,MAAM,WAAW,GAAG;AAAA,QAAA;AAAA,MAC9B,CACD;AAAA,IACH;AAKQ,UAAA,cAAc,CAAO,iBAAiD;AAC1E,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEtC,YAAI,CAAC,gBAAgB,aAAa,KAAA,MAAW,IAAI;AACxC,iBAAA,IAAI,MAAM,QAAQ,CAAC;AAC1B;AAAA,QAAA;AAIF,YAAI,CAAC,aAAa,SAAS,KAAK,KAAK,CAAC,aAAa,SAAS,MAAM,KAAK,CAAC,aAAa,WAAW,WAAW,GAAG;AACrG,iBAAA,IAAI,MAAM,WAAW,CAAC;AAC7B;AAAA,QAAA;AAIF,cAAM,YAAY;AAElBA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK;AAAA,UACL,UAAU;AAAA,UACV,MAAM;AAAA,UACN,QAAQ;AAAA,YACN,kBAAkB,UAAU,SAAS,SAAS;AAAA,YAC9C,eAAe,UAAU,SAAS,YAAY;AAAA;AAAA,UAEhD;AAAA,UACA,UAAU;AAAA;AAAA,YAER,OAAO;AAAA,UACT;AAAA,UACA,SAAS,CAAC,cAAc;AACtB,oBAAQ,IAAI,cAAc;AAAA,cACxB,YAAY,UAAU;AAAA,cACtB,MAAM,UAAU;AAAA,YAAA,CACjB;AAEG,gBAAA;AAEE,kBAAA,UAAU,eAAe,KAAK;AACzB,uBAAA,IAAI,MAAM,kBAAkB,CAAC;AACpC;AAAA,cAAA;AAGF,kBAAI,UAAU,eAAe,OAAO,UAAU,eAAe,KAAK;AACzD,uBAAA,IAAI,MAAM,YAAY,CAAC;AAC9B;AAAA,cAAA;AAGE,kBAAA,UAAU,eAAe,KAAK;AAChC,uBAAO,IAAI,MAAM,aAAa,UAAU,UAAU,EAAE,CAAC;AACrD;AAAA,cAAA;AAIE,kBAAA;AACA,kBAAA,OAAO,UAAU,SAAS,UAAU;AAC7B,yBAAA,KAAK,MAAM,UAAU,IAAI;AAAA,cAAA,OAC7B;AACL,yBAAS,UAAU;AAAA,cAAA;AAGb,sBAAA,IAAI,gBAAgB,MAAM;AAG9B,kBAAA,OAAO,WAAW,OAAO,SAAS;AAEpC,oBAAI,WAAW,OAAO;AAEtB,oBAAI,CAAC,SAAS,WAAW,MAAM,GAAG;AAC5B,sBAAA,SAAS,WAAW,GAAG,GAAG;AAC5B,+BAAW,0BAA0B,QAAQ;AAAA,kBAAA,OACxC;AACL,+BAAW,+BAA+B,QAAQ;AAAA,kBAAA;AAAA,gBACpD;AAEM,wBAAA,IAAI,mBAAmB,QAAQ;AACvC,wBAAQ,QAAQ;AAAA,cACP,WAAA,OAAO,SAAS,OAAO,OAAO,QAAQ;AAE/C,oBAAI,WAAmB,OAAO;AAE9B,oBAAI,CAAC,SAAS,WAAW,MAAM,GAAG;AAC5B,sBAAA,SAAS,WAAW,GAAG,GAAG;AAC5B,+BAAW,0BAA0B,QAAQ;AAAA,kBAAA,OACxC;AACL,+BAAW,+BAA+B,QAAQ;AAAA,kBAAA;AAAA,gBACpD;AAEM,wBAAA,IAAI,mBAAmB,QAAQ;AACvC,wBAAQ,QAAQ;AAAA,cAAA,WACP,OAAO,MAAM;AAEtB,oBAAI,WAAmB,OAAO;AAE9B,oBAAI,CAAC,SAAS,WAAW,MAAM,GAAG;AAC5B,sBAAA,SAAS,WAAW,GAAG,GAAG;AAC5B,+BAAW,0BAA0B,QAAQ;AAAA,kBAAA,OACxC;AACL,+BAAW,+BAA+B,QAAQ;AAAA,kBAAA;AAAA,gBACpD;AAEM,wBAAA,IAAI,mBAAmB,QAAQ;AACvC,wBAAQ,QAAQ;AAAA,cAAA,OACX;AACG,wBAAA,IAAI,sBAAsB,MAAM;AACxC,uBAAO,IAAI,MAAM,OAAO,WAAW,OAAO,OAAO,cAAc,CAAC;AAAA,cAAA;AAAA,qBAE3D,YAAY;AACZ,qBAAA,IAAI,MAAM,WAAW,CAAC;AAAA,YAAA;AAAA,UAEjC;AAAA,UACA,MAAM,MAAM;AACH,mBAAA,IAAI,MAAM,UAAU,CAAC;AAAA,UAAA;AAAA,QAC9B,CACD;AAAA,MAAA,CACF;AAAA,IACH;AACM,YAAA,IAAI,eAAc,YAAY,KAAK;AAErC,UAAA,eAAeP,kBAAI,KAAK;AAGxB,UAAA,YAAY,CAAC,YAAoB;AACrCO,oBAAA,MAAI,UAAU,EAAE,OAAO,SAAS,MAAM,QAAQ;AAAA,IAChD;AAEA,UAAM,qBAAqB,MAAM;AAE/B,UAAI,aAAa,OAAO;AACtB,kBAAU,cAAc;AACjB,eAAA;AAAA,MAAA;AAIT,UAAI,CAAC,aAAa,MAAM,QAAQ;AAC9B,kBAAU,SAAS;AAEnB,sBAAc,QAAQ;AACtBF,sBAAAA,WAAS,MAAM;AACb,wBAAc,QAAQ;AAAA,QAAA,CACvB;AACM,eAAA;AAAA,MAAA;AAIT,UAAI,aAAa,MAAM,KAAK,EAAE,SAAS,KAAM;AAC3C,kBAAU,iBAAiB;AACpB,eAAA;AAAA,MAAA;AAIL,UAAA,CAAC,gBAAgB,OAAO;AAC1B,kBAAU,cAAc;AACjB,eAAA;AAAA,MAAA;AAGL,UAAA,CAAC,UAAU,SAAS,QAAQ;AAC9B,kBAAU,cAAc;AACjB,eAAA;AAAA,MAAA;AAGF,aAAA;AAAA,IACT;AAGA,UAAM,cAAc,MAAY;AAC9B,UAAI,CAAC,mBAAmB;AAAG;AAEvB,UAAA;AACF,qBAAa,QAAQ;AACrBE,sBAAAA,MAAI,YAAY,EAAE,OAAO,SAAA,CAAU;AAGnC,cAAM,cAAmB;AAAA,UACvB,iBAAiB,OAAO,gBAAgB,KAAK;AAAA;AAAA,UAC7C,QAAQ,OAAO,UAAU,SAAS,MAAM;AAAA;AAAA,UACxC,aAAa;AAAA;AAAA,UACb,SAAS;AAAA;AAAA,UACT,MAAM;AAAA;AAAA,UACN,QAAQ,CAAA;AAAA;AAAA,QACV;AAGM,cAAA,cAAc,aAAa,MAAM,KAAK;AACxC,YAAA,eAAe,YAAY,SAAS,GAAG;AACzC,sBAAY,OAAO;AAAA,QAAA;AAKjB,YAAA,YAAY,MAAM,SAAS,GAAG;AAC5B,cAAA;AACFA,0BAAAA,MAAI,YAAY,EAAE,OAAO,WAAA,CAAY;AAErC,kBAAM,eAAyB,CAAC;AAGrB,uBAAA,YAAY,YAAY,OAAO;AAEpC,kBAAA,SAAS,WAAW,MAAM,GAAG;AAC/B,6BAAa,KAAK,QAAQ;AAAA,cAAA,OACrB;AAEC,sBAAA,cAAc,MAAM,YAAY,QAAQ;AAC9C,oBAAI,aAAa;AACf,+BAAa,KAAK,WAAW;AAAA,gBAAA,OACxB;AACLA,gCAAAA,MAAI,YAAY;AAChB,4BAAU,YAAY;AACtB;AAAA,gBAAA;AAAA,cACF;AAAA,YACF;AAGFA,0BAAAA,MAAI,YAAY;AAEZ,gBAAA,aAAa,WAAW,GAAG;AAC7B,wBAAU,gBAAgB;AAC1B;AAAA,YAAA;AAIF,wBAAY,SAAS;AAAA,mBACd,OAAO;AACdA,0BAAAA,MAAI,YAAY;AACR,oBAAA,MAAM,WAAW,KAAK;AAC9B,sBAAU,YAAY;AACtB;AAAA,UAAA;AAAA,QACF;AAIF,YAAI,YAAY,SAAS,YAAY,MAAM,UAAU,YAAY,MAAM,IAAI;AACzE,sBAAY,cAAc,OAAO,YAAY,MAAM,MAAM;AACzD,sBAAY,UAAU,OAAO,YAAY,MAAM,EAAE;AAG7C,cAAA,YAAY,MAAM,QAAQ,YAAY,MAAM,KAAK,WAAW,IAAI;AAClE,wBAAY,eAAe,YAAY,MAAM,KAAK,KAAK;AAAA,UAAA;AAAA,QACzD;AAMI,cAAA,sBAAsB,CAAC,SAAc;AACzC,cAAI,CAAC,KAAK,mBAAmB,CAAC,KAAK,QAAQ;AACzC,sBAAU,YAAY;AACf,mBAAA;AAAA,UAAA;AAGT,cAAI,KAAK,QAAQ,KAAK,KAAK,SAAS,KAAM;AACxC,sBAAU,iBAAiB;AACpB,mBAAA;AAAA,UAAA;AAGL,cAAA,CAAC,QAAQ,KAAK,KAAK,eAAe,KAAK,CAAC,QAAQ,KAAK,KAAK,MAAM,GAAG;AACrE,sBAAU,QAAQ;AACX,mBAAA;AAAA,UAAA;AAGT,cAAI,CAAC,KAAK,QAAQ,CAAC,KAAK,QAAQ;AAC9B,sBAAU,cAAc;AACjB,mBAAA;AAAA,UAAA;AAGF,iBAAA;AAAA,QACT;AAEI,YAAA,CAAC,oBAAoB,WAAW;AAAG;AAGvC,gBAAQ,IAAI,gBAAgB,iCACvB,cADuB;AAAA,UAE1B,aAAa,YAAY,SAAS,YAAY,OAAO,SAAS;AAAA,UAC9D,WAAW,CAAC,CAAC,YAAY,UAAU,YAAY,OAAO,SAAS;AAAA,UAC/D,WAAW,YAAY,UAAU,CAAA;AAAA,QAAC,EACnC;AAED,YAAI,YAAY,UAAU,YAAY,OAAO,SAAS,GAAG;AAC/C,kBAAA,IAAI,kBAAkB,YAAY,MAAM;AAAA,QAAA;AAK9C,YAAA;AACF,gBAAM,WAAW,MAAMD,WAAAA,KAAK,KAAK,wBAAwB,WAAW;AAC5D,kBAAA,IAAI,gBAAgB,QAAQ;AAEhC,cAAA,YAAY,SAAS,QAAQ;AACvB,oBAAA,IAAI,gBAAgB,SAAS,MAAM;AAAA,UAAA;AAGzC,cAAA,YAAY,SAAS,SAAS;AAChCC,0BAAA,MAAI,UAAU,EAAE,OAAO,QAAQ,MAAM,WAAW;AAGhD,yBAAa,QAAQ;AACrB,wBAAY,QAAQ,CAAC;AACrB,wBAAY,QAAQ;AAIT,uBAAA;AAGX,uBAAW,MAAM;AACJ,yBAAA;AAAA,eACV,GAAG;AAGN,uBAAW,MAAM;AACJ,yBAAA;AAAA,eACV,GAAI;AAAA,UAAA,OACF;AAEL,gBAAI,WAAW;AAEf,gBAAI,qCAAU,SAAS;AACrB,yBAAW,SAAS;AAGhB,kBAAA,SAAS,QAAQ,SAAS,OAAO,KAAK,SAAS,QAAQ,SAAS,UAAU,GAAG;AACpE,2BAAA;AAAA,cACF,WAAA,SAAS,QAAQ,SAAS,QAAQ,GAAG;AACnC,2BAAA;AAAA,cACF,WAAA,SAAS,QAAQ,SAAS,QAAQ,GAAG;AACnC,2BAAA;AAAA,cAAA;AAAA,YACb,WACS,qCAAU,KAAK;AACxB,yBAAW,SAAS;AAAA,YAAA;AAGtB,oBAAQ,MAAM,WAAW;AAAA,cACvB;AAAA,cACA;AAAA,cACA;AAAA,YAAA,CACD;AAEDA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA;AAAA,YAAA,CACX;AAAA,UAAA;AAAA,iBAEI,OAAO;AACN,kBAAA,MAAM,SAAS,KAAK;AAC5BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAAA,CACX;AAAA,QAAA;AAAA,MACH,UACA;AACA,qBAAa,QAAQ;AACrBA,sBAAAA,MAAI,YAAY;AAAA,MAAA;AAAA,IAEpB;AAGM,UAAA,cAAc,CAAO,QAAa;AAClC,UAAA;AAEF,cAAM,SAAS,MAAM,IAAI,QAAQ,CAAC,YAAY;AAC5CA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS;AAAA,YACT,SAAS,CAACE,SAAQ;AAChB,sBAAQA,KAAI,OAAO;AAAA,YACrB;AAAA,YACA,MAAM,MAAM;AACV,sBAAQ,KAAK;AAAA,YAAA;AAAA,UACf,CACD;AAAA,QAAA,CACF;AAED,YAAI,CAAC,QAAQ;AACX;AAAA,QAAA;AAGFF,sBAAAA,MAAI,YAAY,EAAE,OAAO,SAAA,CAAU;AAG7B,cAAA,cAAc,YAAY,IAAI,EAAE,YAAY,UAAU,SAAS,MAAM,oBAAoB,gBAAgB,KAAK;AAEpH,cAAM,MAAM,MAAMD,gBAAK,OAAO,4BAA4B,WAAW,EAAE;AAEvE,YAAI,IAAI,SAAS;AACfC,wBAAA,MAAI,UAAU,EAAE,OAAO,QAAQ,MAAM,WAAW;AACrC,qBAAA;AAAA,QAAA,OACN;AACDA,8BAAA,UAAU,EAAE,OAAO,IAAI,WAAW,QAAQ,MAAM,QAAQ;AAAA,QAAA;AAAA,eAEvD,GAAG;AACF,gBAAA,MAAM,WAAW,CAAC;AAC1BA,sBAAA,MAAI,UAAU,EAAE,OAAO,QAAQ,MAAM,QAAQ;AAAA,MAAA,UAC7C;AACAA,sBAAAA,MAAI,YAAY;AAAA,MAAA;AAAA,IAEpB;AAGM,UAAA,qBAAqB,CAAC,KAAa,cAAwB;AAEzD,YAAA,YAAY,UAAU,OAAO,CAAO,QAAA,OAAO,OAAO,QAAQ,YAAY,IAAI,KAAK,MAAM,EAAE;AAEzF,UAAA,UAAU,WAAW,GAAG;AAC1B,kBAAU,UAAU;AACpB;AAAA,MAAA;AAII,YAAA,aAAa,OAAO,OAAO,QAAQ,YAAY,IAAI,KAAA,MAAW,KAAK,MAAM,UAAU,CAAC;AAE1FA,oBAAAA,MAAI,aAAa;AAAA,QACf,SAAS;AAAA,QACT,MAAM;AAAA,MAAA,CACP;AAAA,IACH;AAKM,UAAA,oBAAoB,CAAC,KAAa,cAAwB;AAC9D,yBAAmB,KAAK,SAAS;AAAA,IACnC;AAGM,UAAA,qBAAqB,CAAC,UAAkB;AAChC,kBAAA,MAAM,OAAO,OAAO,CAAC;AACjCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MAAA,CACX;AAAA,IACH;AAGM,UAAA,eAAe,CAAC,QAAgB;AACpCA,oBAAAA,MAAI,aAAa;AAAA,QACf,SAAS;AAAA,QACT,MAAM,YAAY;AAAA,MAAA,CACnB;AAAA,IACH;AAGM,UAAA,eAAe,CAAC,aAA4B;AAChD,UAAI,CAAC;AAAU,eAAO,CAAC;AACnB,UAAA,MAAM,QAAQ,QAAQ;AAAU,eAAA;AAChC,UAAA,OAAO,aAAa,UAAU;AAE5B,YAAA,SAAS,WAAW,GAAG,GAAG;AACxB,cAAA;AACI,kBAAA,MAAM,KAAK,MAAM,QAAQ;AAC/B,mBAAO,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC;AAAA,UAAA,SAC7B;AACN,mBAAO,CAAC;AAAA,UAAA;AAAA,QAED,WAAA,SAAS,SAAS,GAAG,GAAG;AAC1B,iBAAA,SAAS,MAAM,GAAG,EAAE,IAAI,CAAK,MAAA,EAAE,KAAK,CAAC,EAAE,OAAO,OAAO;AAAA,QACnD,WAAA,SAAS,KAAK,MAAM,IAAI;AACjC,iBAAO,CAAC,QAAQ;AAAA,QAAA;AAAA,MAClB;AAEF,aAAO,CAAC;AAAA,IACV;AAGM,UAAA,mBAAmB,CAAC,QAAwB;AAChD,UAAI,CAAC;AAAY,eAAA;AAGb,UAAA,IAAI,WAAW,MAAM,GAAG;AACnB,eAAA;AAAA,MAAA;AAIL,UAAA,IAAI,WAAW,GAAG,GAAG;AACvB,eAAO,0BAA0B,GAAG;AAAA,MAAA;AAItC,aAAO,+BAA+B,GAAG;AAAA,IAC3C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1zCA,GAAG,WAAW,eAAe;"}