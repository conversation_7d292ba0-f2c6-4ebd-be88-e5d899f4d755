"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../../../common/vendor.js");
if (!Math) {
  wdResize();
}
const wdResize = () => "../wd-resize/wd-resize.js";
const __default__ = {
  name: "wd-sticky",
  options: {
    addGlobalClass: true,
    virtualHost: true,
    styleIsolation: "shared"
  }
};
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, __default__), {
  props: common_vendor.stickyProps,
  setup(__props, { expose: __expose }) {
    const props = __props;
    const styckyId = common_vendor.ref(`wd-sticky${common_vendor.uuid()}`);
    const observerList = common_vendor.ref([]);
    const stickyState = common_vendor.reactive({
      position: "absolute",
      boxLeaved: false,
      top: 0,
      height: 0,
      width: 0,
      state: ""
    });
    const { parent: stickyBox } = common_vendor.useParent(common_vendor.STICKY_BOX_KEY);
    const { proxy } = common_vendor.getCurrentInstance();
    const rootStyle = common_vendor.computed(() => {
      const style = {
        "z-index": props.zIndex,
        height: common_vendor.addUnit(stickyState.height),
        width: common_vendor.addUnit(stickyState.width)
      };
      if (!stickyState.boxLeaved) {
        style["position"] = "relative";
      }
      return `${common_vendor.objToStyle(style)}${props.customStyle}`;
    });
    const stickyStyle = common_vendor.computed(() => {
      const style = {
        "z-index": props.zIndex,
        height: common_vendor.addUnit(stickyState.height),
        width: common_vendor.addUnit(stickyState.width)
      };
      if (!stickyState.boxLeaved) {
        style["position"] = "relative";
      }
      return `${common_vendor.objToStyle(style)}`;
    });
    const containerStyle = common_vendor.computed(() => {
      const style = {
        position: stickyState.position,
        top: common_vendor.addUnit(stickyState.top)
      };
      return common_vendor.objToStyle(style);
    });
    const innerOffsetTop = common_vendor.computed(() => {
      let top = 0;
      return top + props.offsetTop;
    });
    function clearObserver() {
      while (observerList.value.length !== 0) {
        observerList.value.pop().disconnect();
      }
    }
    function createObserver() {
      const observer = common_vendor.index.createIntersectionObserver(proxy, { thresholds: [0, 0.5] });
      observerList.value.push(observer);
      return observer;
    }
    function handleResize(detail) {
      return __async(this, null, function* () {
        stickyState.width = detail.width;
        stickyState.height = detail.height;
        yield common_vendor.pause();
        observerContentScroll();
        if (!stickyBox || !stickyBox.observerForChild)
          return;
        stickyBox.observerForChild(proxy);
      });
    }
    function observerContentScroll() {
      if (stickyState.height === 0 && stickyState.width === 0)
        return;
      const offset = innerOffsetTop.value + stickyState.height;
      clearObserver();
      createObserver().relativeToViewport({
        top: -offset
      }).observe(`#${styckyId.value}`, (result) => {
        handleRelativeTo(result);
      });
      common_vendor.getRect(`#${styckyId.value}`, false, proxy).then((res) => {
        if (Number(res.bottom) <= offset)
          handleRelativeTo({ boundingClientRect: res });
      });
    }
    function handleRelativeTo({ boundingClientRect }) {
      if (stickyBox && stickyBox.boxStyle && stickyState.height >= stickyBox.boxStyle.height) {
        stickyState.position = "absolute";
        stickyState.top = 0;
        return;
      }
      let isStycky = boundingClientRect.top <= innerOffsetTop.value;
      if (isStycky) {
        stickyState.state = "sticky";
        stickyState.boxLeaved = false;
        stickyState.position = "fixed";
        stickyState.top = innerOffsetTop.value;
      } else {
        stickyState.state = "normal";
        stickyState.boxLeaved = false;
        stickyState.position = "absolute";
        stickyState.top = 0;
      }
    }
    function setPosition(boxLeaved, position, top) {
      stickyState.boxLeaved = boxLeaved;
      stickyState.position = position;
      stickyState.top = top;
    }
    __expose({
      setPosition,
      stickyState,
      offsetTop: props.offsetTop
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(handleResize),
        b: common_vendor.p({
          ["custom-style"]: "display: inline-block;"
        }),
        c: common_vendor.s(containerStyle.value),
        d: common_vendor.n(`wd-sticky ${_ctx.customClass}`),
        e: common_vendor.s(stickyStyle.value),
        f: styckyId.value,
        g: common_vendor.s(`${rootStyle.value};display: inline-block;`)
      };
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-c7f00bc1"]]);
wx.createComponent(Component);
//# sourceMappingURL=wd-sticky.js.map
