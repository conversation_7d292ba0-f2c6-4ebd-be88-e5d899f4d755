"use strict";
const common_vendor = require("../../common/vendor.js");
const common_work = require("../../common/work.js");
const common_uitls = require("../../common/uitls.js");
const common_constants = require("../../common/constants.js");
require("../../plugin/uni-mini-router/router/index.js");
const plugin_uniMiniRouter_core_index = require("../../plugin/uni-mini-router/core/index.js");
if (!Array) {
  const _easycom_wd_img2 = common_vendor.resolveComponent("wd-img");
  const _easycom_wd_cell2 = common_vendor.resolveComponent("wd-cell");
  const _easycom_wd_cell_group2 = common_vendor.resolveComponent("wd-cell-group");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_img2 + _easycom_wd_cell2 + _easycom_wd_cell_group2 + _easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_wd_img = () => "../../node-modules/wot-design-uni/components/wd-img/wd-img.js";
const _easycom_wd_cell = () => "../../node-modules/wot-design-uni/components/wd-cell/wd-cell.js";
const _easycom_wd_cell_group = () => "../../node-modules/wot-design-uni/components/wd-cell-group/wd-cell-group.js";
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  (_easycom_wd_img + _easycom_wd_cell + _easycom_wd_cell_group + _easycom_PageLayout)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "more",
  setup(__props) {
    const toast = common_vendor.useToast();
    const router = plugin_uniMiniRouter_core_index.useRouter();
    const routeList = common_vendor.ref([]);
    const isLocalConfig = getApp().globalData.isLocalConfig;
    let type = "common";
    const init = () => {
      if (isLocalConfig) {
        routeList.value = type == "common" ? common_work.us.data : common_work.os.data;
      } else {
        var indexRouteList = common_uitls.cache(common_constants.APP_ROUTE);
        routeList.value = indexRouteList.filter((item) => item.type == type);
      }
    };
    const goPage = (item) => {
      let page = item.routeIndex;
      console.log("-----------page------------", page);
      if (!page) {
        toast.info("该功能暂未实现");
      } else {
        if (page.indexOf("/app/online") == 0) {
          let code = page.substring(page.lastIndexOf("/") + 1);
          let real = { desformCode: code, desformName: item.title };
          common_vendor.index.navigateTo({
            url: "/pages/check/onlineForm/add?item=" + encodeURIComponent(JSON.stringify(real))
          });
        } else if (page.indexOf("/app/desform") == 0) {
          let code = page.substring(page.lastIndexOf("/") + 1);
          let real = { desformCode: code, desformName: item.title };
          common_vendor.index.navigateTo({
            url: "/pages/check/designForm/designForm?item=" + encodeURIComponent(JSON.stringify(real))
          });
        } else {
          router.replace({ name: page, params: { backRouteName: "index" } });
        }
      }
    };
    common_vendor.onLoad((params) => {
      type = params.type;
      init();
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.f(common_vendor.unref(routeList), (item, index, i0) => {
          return {
            a: "2d2365fa-4-" + i0 + "," + ("2d2365fa-3-" + i0),
            b: common_vendor.p({
              height: "20",
              width: "20",
              src: common_vendor.unref(isLocalConfig) ? item.icon : common_vendor.unref(common_uitls.getFileAccessHttpUrl)(item.icon)
            }),
            c: common_vendor.o(($event) => goPage(item), index),
            d: "2d2365fa-3-" + i0 + ",2d2365fa-2",
            e: common_vendor.p({
              title: item.title,
              ["is-link"]: true
            }),
            f: index
          };
        }),
        b: common_vendor.p({
          border: true,
          clickable: true
        }),
        c: common_vendor.p({
          navTitle: "更多",
          backRouteName: "index",
          routeMethod: "pushTab"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-2d2365fa"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=more.js.map
