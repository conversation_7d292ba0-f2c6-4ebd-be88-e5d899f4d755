{"version": 3, "file": "wd-signature.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-signature/wd-signature.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1zaWduYXR1cmUvd2Qtc2lnbmF0dXJlLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"wd-signature\">\n    <view class=\"wd-signature__content\">\n\n      <canvas\n        class=\"wd-signature__content-canvas\"\n        :style=\"canvasStyle\"\n        :width=\"canvasState.canvasWidth\"\n        :height=\"canvasState.canvasHeight\"\n        :canvas-id=\"canvasId\"\n        :id=\"canvasId\"\n        :disable-scroll=\"disableScroll\"\n        @touchstart=\"startDrawing\"\n        @touchend=\"stopDrawing\"\n        @touchmove=\"draw\"\n        type=\"2d\"\n      />\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n    </view>\n    <view class=\"wd-signature__footer\">\n      <slot\n        name=\"footer\"\n        :clear=\"clear\"\n        :confirm=\"confirmSignature\"\n        :current-step=\"currentStep\"\n        :revoke=\"revoke\"\n        :restore=\"restore\"\n        :can-undo=\"lines.length > 0\"\n        :can-redo=\"redoLines.length > 0\"\n        :history-list=\"lines\"\n      >\n        <template v-if=\"enableHistory\">\n          <wd-button size=\"small\" plain @click=\"revoke\" :disabled=\"lines.length <= 0\">\n            {{ revokeText || translate('revokeText') }}\n          </wd-button>\n          <wd-button size=\"small\" plain @click=\"restore\" :disabled=\"redoLines.length <= 0\">\n            {{ restoreText || translate('restoreText') }}\n          </wd-button>\n        </template>\n        <wd-button size=\"small\" plain @click=\"clear\">{{ clearText || translate('clearText') }}</wd-button>\n        <wd-button size=\"small\" @click=\"confirmSignature\">{{ confirmText || translate('confirmText') }}</wd-button>\n      </slot>\n    </view>\n  </view>\n</template>\n<script lang=\"ts\">\nexport default {\n  name: 'wd-signature',\n  options: {\n    addGlobalClass: true,\n    virtualHost: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n<script lang=\"ts\" setup>\nimport { computed, getCurrentInstance, onBeforeMount, onMounted, reactive, ref, watch, type CSSProperties } from 'vue'\nimport { addUnit, getRect, isDef, objToStyle, uuid } from '../common/util'\nimport { signatureProps, type SignatureExpose, type SignatureResult, type Point, type Line } from './types'\nimport { useTranslate } from '../composables/useTranslate'\n\nimport { canvas2dAdapter } from '../common/canvasHelper'\n\n\nconst props = defineProps(signatureProps)\nconst emit = defineEmits(['start', 'end', 'signing', 'confirm', 'clear'])\nconst { translate } = useTranslate('signature')\nconst { proxy } = getCurrentInstance() as any\nconst canvasId = ref<string>(`signature${uuid()}`) // canvas 组件的唯一标识符\nlet canvas: null = null //canvas对象 微信小程序生成图片必须传入\nconst drawing = ref<boolean>(false) // 是否正在绘制\nconst pixelRatio = ref<number>(1) // 像素比\n\nconst canvasState = reactive({\n  canvasWidth: 0,\n  canvasHeight: 0,\n  ctx: null as UniApp.CanvasContext | null // canvas上下文\n})\n\nwatch(\n  () => props.penColor,\n  () => {\n    setLine()\n  }\n)\n\nwatch(\n  () => props.lineWidth,\n  () => {\n    setLine()\n  }\n)\n\nconst canvasStyle = computed(() => {\n  const style: CSSProperties = {}\n  if (isDef(props.width)) {\n    style.width = addUnit(props.width)\n  }\n\n  if (isDef(props.height)) {\n    style.height = addUnit(props.height)\n  }\n\n  return `${objToStyle(style)}`\n})\n\nconst disableScroll = computed(() => props.disableScroll)\nconst enableHistory = computed(() => props.enableHistory)\n\nconst lines = ref<Line[]>([]) // 保存所有线条\nconst redoLines = ref<Line[]>([]) // 保存撤销的线条\nconst currentLine = ref<Line>() // 当前正在绘制的线\nconst currentStep = ref(0) // 当前步骤\n\n// 添加计算笔画宽度的方法\nfunction calculateLineWidth(speed: number): number {\n  if (!props.pressure) return props.lineWidth\n\n  const minSpeed = props.minSpeed || 1.5\n  const limitedSpeed = Math.min(minSpeed * 10, Math.max(minSpeed, speed))\n  const addWidth = ((props.maxWidth - props.minWidth) * (limitedSpeed - minSpeed)) / minSpeed\n  const lineWidth = Math.max(props.maxWidth - addWidth, props.minWidth)\n  return Math.min(lineWidth, props.maxWidth)\n}\n\n/* 获取默认笔画宽度 */\nconst getDefaultLineWidth = () => {\n  if (props.pressure) {\n    // 在压感模式下，使用最大和最小宽度的平均值作为默认值\n    return (props.maxWidth + props.minWidth) / 2\n  }\n  return props.lineWidth\n}\n\n/* 开始画线 */\nconst startDrawing = (e: any) => {\n  e.preventDefault()\n  drawing.value = true\n  setLine()\n  emit('start', e)\n\n  // 创建新线条，同时保存当前的所有绘制参数\n  const { x, y } = e.touches[0]\n  currentLine.value = {\n    points: [\n      {\n        x,\n        y,\n        t: Date.now() // 使用 t 替换 width\n      }\n    ],\n    color: props.penColor,\n    width: getDefaultLineWidth(),\n    backgroundColor: props.backgroundColor,\n    isPressure: props.pressure // 添加笔锋模式标记\n  }\n\n  // 清空重做记录\n  redoLines.value = []\n  draw(e)\n}\n\n/* 结束画线 */\nconst stopDrawing = (e: TouchEvent) => {\n  e.preventDefault()\n  drawing.value = false\n  if (currentLine.value) {\n    // 保存完整的线条信息，包括所有点的参数\n    lines.value.push({\n      ...currentLine.value,\n      points: currentLine.value.points.map((point) => ({\n        ...point,\n        t: point.t,\n        speed: point.speed,\n        distance: point.distance,\n        lineWidth: point.lineWidth,\n        lastX1: point.lastX1,\n        lastY1: point.lastY1,\n        lastX2: point.lastX2,\n        lastY2: point.lastY2,\n        isFirstPoint: point.isFirstPoint\n      }))\n    })\n    currentStep.value = lines.value.length\n  }\n  currentLine.value = undefined\n  const { ctx } = canvasState\n  if (ctx) ctx.beginPath()\n  emit('end', e)\n}\n\n/**\n * 初始化 canvas\n * @param forceUpdate 是否强制更新\n */\nconst initCanvas = (forceUpdate: boolean = false) => {\n  // 如果不是强制更新，且已经初始化过 canvas，则不再重复初始化\n  if (!forceUpdate && canvasState.canvasHeight && canvasState.canvasWidth) {\n    return\n  }\n  getContext().then(() => {\n    const { ctx } = canvasState\n    if (ctx && isDef(props.backgroundColor)) {\n      ctx.setFillStyle(props.backgroundColor)\n      ctx.fillRect(0, 0, canvasState.canvasWidth, canvasState.canvasHeight)\n      ctx.draw()\n    }\n  })\n}\n\n// 清空 canvas\nconst clear = () => {\n  lines.value = []\n  redoLines.value = []\n  currentStep.value = 0\n  clearCanvas()\n  emit('clear')\n}\n\n// 确认签名\nconst confirmSignature = () => {\n  canvasToImage()\n}\n\n//canvas划线\nconst draw = (e: any) => {\n  e.preventDefault()\n  const { ctx } = canvasState\n\n  if (!drawing.value || props.disabled || !ctx) return\n  const { x, y } = e.touches[0]\n\n  const point: Point = {\n    x,\n    y,\n    t: Date.now()\n  }\n\n  if (currentLine.value) {\n    const points = currentLine.value.points\n    const prePoint = points[points.length - 1]\n\n    if (prePoint.t === point.t || (prePoint.x === x && prePoint.y === y)) {\n      return\n    }\n\n    // 计算点的速度和距离\n    point.distance = Math.sqrt(Math.pow(point.x - prePoint.x, 2) + Math.pow(point.y - prePoint.y, 2))\n    point.speed = point.distance / (point.t - prePoint.t || 0.1)\n\n    if (props.pressure) {\n      point.lineWidth = calculateLineWidth(point.speed)\n      // 处理线宽变化率限制\n      if (points.length >= 2) {\n        const prePoint2 = points[points.length - 2]\n        if (prePoint2.lineWidth && prePoint.lineWidth) {\n          const rate = (point.lineWidth - prePoint.lineWidth) / prePoint.lineWidth\n          const maxRate = 0.2 // 最大变化率20%\n          if (Math.abs(rate) > maxRate) {\n            const per = rate > 0 ? maxRate : -maxRate\n            point.lineWidth = prePoint.lineWidth * (1 + per)\n          }\n        }\n      }\n    }\n\n    points.push(point)\n\n    // 非笔锋模式直接使用线段连接\n    if (!props.pressure) {\n      ctx.beginPath()\n      ctx.moveTo(prePoint.x, prePoint.y)\n      ctx.lineTo(point.x, point.y)\n      ctx.stroke()\n      ctx.draw(true)\n    } else if (points.length >= 2) {\n      // 笔锋模式使用贝塞尔曲线\n      drawSmoothLine(prePoint, point)\n    }\n  }\n\n  emit('signing', e)\n}\n\n/* 重绘整个画布 */\nconst redrawCanvas = () => {\n  const { ctx } = canvasState\n  if (!ctx) return\n\n  // 清除画布并设置背景\n  if (isDef(props.backgroundColor)) {\n    ctx.setFillStyle(props.backgroundColor)\n    ctx.fillRect(0, 0, canvasState.canvasWidth, canvasState.canvasHeight)\n  } else {\n    ctx.clearRect(0, 0, canvasState.canvasWidth, canvasState.canvasHeight)\n  }\n\n  // 如果没有线条，只需要清空画布\n  if (lines.value.length === 0) {\n    ctx.draw()\n    return\n  }\n\n  // 收集所有绘制操作，最后一次性 draw\n  lines.value.forEach((line) => {\n    if (!line.points.length) return\n\n    ctx.setStrokeStyle(line.color)\n    ctx.setLineJoin('round')\n    ctx.setLineCap('round')\n\n    if (line.isPressure && props.pressure) {\n      // 笔锋模式的重绘\n      line.points.forEach((point, index) => {\n        if (index === 0) return\n        const prePoint = line.points[index - 1]\n        const dis_x = point.x - prePoint.x\n        const dis_y = point.y - prePoint.y\n        const distance = Math.sqrt(dis_x * dis_x + dis_y * dis_y)\n\n        if (distance <= 2) {\n          point.lastX1 = point.lastX2 = prePoint.x + dis_x * 0.5\n          point.lastY1 = point.lastY2 = prePoint.y + dis_y * 0.5\n        } else {\n          const speed = point.speed || 0\n          const minSpeed = props.minSpeed || 1.5\n          const speedFactor = Math.max(0.1, Math.min(0.9, speed / (minSpeed * 10)))\n\n          point.lastX1 = prePoint.x + dis_x * (0.2 + speedFactor * 0.3)\n          point.lastY1 = prePoint.y + dis_y * (0.2 + speedFactor * 0.3)\n          point.lastX2 = prePoint.x + dis_x * (0.8 - speedFactor * 0.3)\n          point.lastY2 = prePoint.y + dis_y * (0.8 - speedFactor * 0.3)\n        }\n\n        const lineWidth = point.lineWidth || line.width\n        if (typeof prePoint.lastX1 === 'number') {\n          ctx.setLineWidth(lineWidth)\n          ctx.beginPath()\n          ctx.moveTo(prePoint.lastX2!, prePoint.lastY2!)\n          ctx.quadraticCurveTo(prePoint.x, prePoint.y, point.lastX1, point.lastY1)\n          ctx.stroke()\n\n          if (!prePoint.isFirstPoint) {\n            ctx.beginPath()\n            ctx.moveTo(prePoint.lastX1!, prePoint.lastY1!)\n            ctx.quadraticCurveTo(prePoint.x, prePoint.y, prePoint.lastX2!, prePoint.lastY2!)\n            ctx.stroke()\n          }\n        } else {\n          point.isFirstPoint = true\n        }\n      })\n    } else {\n      // 非笔锋模式的重绘\n      ctx.setLineWidth(line.width)\n      line.points.forEach((point, index) => {\n        if (index === 0) return\n        const prePoint = line.points[index - 1]\n        ctx.beginPath()\n        ctx.moveTo(prePoint.x, prePoint.y)\n        ctx.lineTo(point.x, point.y)\n        ctx.stroke()\n      })\n    }\n  })\n\n  // 所有线条绘制完成后，一次性更新画布\n  ctx.draw()\n}\n\n// 修改撤销功能\nconst revoke = () => {\n  if (!lines.value.length) return\n  const step = Math.min(props.step, lines.value.length)\n  const removedLines = lines.value.splice(lines.value.length - step)\n  redoLines.value.push(...removedLines)\n  currentStep.value = Math.max(0, currentStep.value - step)\n  redrawCanvas()\n}\n\n// 修改恢复功能\nconst restore = () => {\n  if (!redoLines.value.length) return\n  const step = Math.min(props.step, redoLines.value.length)\n  const restoredLines = redoLines.value.splice(redoLines.value.length - step)\n  lines.value.push(...restoredLines)\n  currentStep.value = Math.min(lines.value.length, currentStep.value + step)\n  redrawCanvas()\n}\n\n// 添加平滑线条绘制方法\nfunction drawSmoothLine(prePoint: Point, point: Point) {\n  const { ctx } = canvasState\n  if (!ctx) return\n\n  // 计算两点间距离\n  const dis_x = point.x - prePoint.x\n  const dis_y = point.y - prePoint.y\n  const distance = Math.sqrt(dis_x * dis_x + dis_y * dis_y)\n\n  if (distance <= 2) {\n    // 对于非常近的点，直接使用中点\n    point.lastX1 = point.lastX2 = prePoint.x + dis_x * 0.5\n    point.lastY1 = point.lastY2 = prePoint.y + dis_y * 0.5\n  } else {\n    // 根据点的速度计算控制点的偏移程度\n    const speed = point.speed || 0\n    const minSpeed = props.minSpeed || 1.5\n    const speedFactor = Math.max(0.1, Math.min(0.9, speed / (minSpeed * 10)))\n\n    // 计算控制点\n    point.lastX1 = prePoint.x + dis_x * (0.2 + speedFactor * 0.3)\n    point.lastY1 = prePoint.y + dis_y * (0.2 + speedFactor * 0.3)\n    point.lastX2 = prePoint.x + dis_x * (0.8 - speedFactor * 0.3)\n    point.lastY2 = prePoint.y + dis_y * (0.8 - speedFactor * 0.3)\n  }\n\n  // 计算线宽\n  const lineWidth = point.lineWidth || props.lineWidth\n\n  // 绘制贝塞尔曲线\n  if (typeof prePoint.lastX1 === 'number') {\n    // 设置线宽\n    ctx.setLineWidth(lineWidth)\n    // 绘制第一段曲线\n    ctx.beginPath()\n    ctx.moveTo(prePoint.lastX2!, prePoint.lastY2!)\n    ctx.quadraticCurveTo(prePoint.x, prePoint.y, point.lastX1, point.lastY1)\n    ctx.stroke()\n\n    if (!prePoint.isFirstPoint) {\n      // 绘制连接段曲线\n      ctx.beginPath()\n      ctx.moveTo(prePoint.lastX1!, prePoint.lastY1!)\n      ctx.quadraticCurveTo(prePoint.x, prePoint.y, prePoint.lastX2!, prePoint.lastY2!)\n      ctx.stroke()\n    }\n\n    // 批量更新绘制内容\n    ctx.draw(true)\n  } else {\n    point.isFirstPoint = true\n  }\n}\n\nonMounted(() => {\n  initCanvas()\n})\n\nonBeforeMount(() => {\n\n  pixelRatio.value = uni.getSystemInfoSync().pixelRatio\n\n})\n\n/**\n * 获取canvas上下文\n */\nfunction getContext() {\n  return new Promise<UniApp.CanvasContext>((resolve) => {\n    const { ctx } = canvasState\n\n    if (ctx) {\n      return resolve(ctx)\n    }\n\n\n\n\n\n\n\n\n\n\n\n\n    getRect(`#${canvasId.value}`, false, proxy, true).then((canvasRect: any) => {\n      if (canvasRect && canvasRect.node && canvasRect.width && canvasRect.height) {\n        const canvasInstance = canvasRect.node\n        canvasState.ctx = canvas2dAdapter(canvasInstance.getContext('2d') as CanvasRenderingContext2D)\n        canvasInstance.width = canvasRect.width * pixelRatio.value\n        canvasInstance.height = canvasRect.height * pixelRatio.value\n        canvasState.ctx.scale(pixelRatio.value, pixelRatio.value)\n        canvas = canvasInstance\n        setcanvasState(canvasRect.width, canvasRect.height)\n        resolve(canvasState.ctx)\n      }\n    })\n\n  })\n}\n\n/**\n * 设置 canvasState\n */\nfunction setcanvasState(width: number, height: number) {\n  canvasState.canvasHeight = height * pixelRatio.value\n  canvasState.canvasWidth = width * pixelRatio.value\n}\n\n/* 设置线段 */\nfunction setLine() {\n  const { ctx } = canvasState\n  if (ctx) {\n    ctx.setLineWidth(getDefaultLineWidth()) // 使用新的默认宽度\n    ctx.setStrokeStyle(props.penColor)\n    ctx.setLineJoin('round')\n    ctx.setLineCap('round')\n  }\n}\n\n/**\n *  canvas 绘制图片输出成文件类型\n */\nfunction canvasToImage() {\n  const { fileType, quality, exportScale } = props\n  const { canvasWidth, canvasHeight } = canvasState\n  uni.canvasToTempFilePath(\n    {\n      width: canvasWidth * exportScale,\n      height: canvasHeight * exportScale,\n      destWidth: canvasWidth * exportScale,\n      destHeight: canvasHeight * exportScale,\n      fileType,\n      quality,\n      canvasId: canvasId.value,\n      canvas: canvas,\n      success: (res) => {\n        const result: SignatureResult = {\n          tempFilePath: res.tempFilePath,\n          width: (canvasWidth * exportScale) / pixelRatio.value,\n          height: (canvasHeight * exportScale) / pixelRatio.value,\n          success: true\n        }\n\n\n\n        emit('confirm', result)\n      },\n      fail: () => {\n        const result: SignatureResult = {\n          tempFilePath: '',\n          width: (canvasWidth * exportScale) / pixelRatio.value,\n          height: (canvasHeight * exportScale) / pixelRatio.value,\n          success: false\n        }\n        emit('confirm', result)\n      }\n    },\n    proxy\n  )\n}\n\nfunction clearCanvas() {\n  const { canvasWidth, canvasHeight, ctx } = canvasState\n  if (ctx) {\n    ctx.clearRect(0, 0, canvasWidth, canvasHeight)\n    if (isDef(props.backgroundColor)) {\n      ctx.setFillStyle(props.backgroundColor)\n      ctx.fillRect(0, 0, canvasWidth, canvasHeight)\n    }\n    ctx.draw()\n  }\n}\n\ndefineExpose<SignatureExpose>({\n  init: initCanvas,\n  clear,\n  confirm: confirmSignature,\n  restore,\n  revoke\n})\n</script>\n<style scoped lang=\"scss\">\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-signature/wd-signature.vue'\nwx.createComponent(Component)"], "names": ["useTranslate", "getCurrentInstance", "ref", "uuid", "reactive", "watch", "computed", "isDef", "addUnit", "objToStyle", "onMounted", "onBeforeMount", "uni", "getRect", "canvas2dAdapter"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4DA,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,gBAAgB;AAAA,EAAA;AAEpB;;;;;AAWA,UAAM,QAAQ;AACd,UAAM,OAAO;AACb,UAAM,EAAE,UAAA,IAAcA,cAAA,aAAa,WAAW;AACxC,UAAA,EAAE,MAAM,IAAIC,iCAAmB;AACrC,UAAM,WAAWC,cAAAA,IAAY,YAAYC,cAAA,KAAA,CAAM,EAAE;AACjD,QAAI,SAAe;AACb,UAAA,UAAUD,kBAAa,KAAK;AAC5B,UAAA,aAAaA,kBAAY,CAAC;AAEhC,UAAM,cAAcE,cAAAA,SAAS;AAAA,MAC3B,aAAa;AAAA,MACb,cAAc;AAAA,MACd,KAAK;AAAA;AAAA,IAAA,CACN;AAEDC,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,MAAM;AACI,gBAAA;AAAA,MAAA;AAAA,IAEZ;AAEAA,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,MAAM;AACI,gBAAA;AAAA,MAAA;AAAA,IAEZ;AAEM,UAAA,cAAcC,cAAAA,SAAS,MAAM;AACjC,YAAM,QAAuB,CAAC;AAC1B,UAAAC,cAAA,MAAM,MAAM,KAAK,GAAG;AAChB,cAAA,QAAQC,sBAAQ,MAAM,KAAK;AAAA,MAAA;AAG/B,UAAAD,cAAA,MAAM,MAAM,MAAM,GAAG;AACjB,cAAA,SAASC,sBAAQ,MAAM,MAAM;AAAA,MAAA;AAG9B,aAAA,GAAGC,cAAAA,WAAW,KAAK,CAAC;AAAA,IAAA,CAC5B;AAED,UAAM,gBAAgBH,cAAAA,SAAS,MAAM,MAAM,aAAa;AACxD,UAAM,gBAAgBA,cAAAA,SAAS,MAAM,MAAM,aAAa;AAElD,UAAA,QAAQJ,cAAY,IAAA,EAAE;AACtB,UAAA,YAAYA,cAAY,IAAA,EAAE;AAChC,UAAM,cAAcA,cAAAA,IAAU;AACxB,UAAA,cAAcA,kBAAI,CAAC;AAGzB,aAAS,mBAAmB,OAAuB;AACjD,UAAI,CAAC,MAAM;AAAU,eAAO,MAAM;AAE5B,YAAA,WAAW,MAAM,YAAY;AAC7B,YAAA,eAAe,KAAK,IAAI,WAAW,IAAI,KAAK,IAAI,UAAU,KAAK,CAAC;AACtE,YAAM,YAAa,MAAM,WAAW,MAAM,aAAa,eAAe,YAAa;AACnF,YAAM,YAAY,KAAK,IAAI,MAAM,WAAW,UAAU,MAAM,QAAQ;AACpE,aAAO,KAAK,IAAI,WAAW,MAAM,QAAQ;AAAA,IAAA;AAI3C,UAAM,sBAAsB,MAAM;AAChC,UAAI,MAAM,UAAU;AAEV,gBAAA,MAAM,WAAW,MAAM,YAAY;AAAA,MAAA;AAE7C,aAAO,MAAM;AAAA,IACf;AAGM,UAAA,eAAe,CAAC,MAAW;AAC/B,QAAE,eAAe;AACjB,cAAQ,QAAQ;AACR,cAAA;AACR,WAAK,SAAS,CAAC;AAGf,YAAM,EAAE,GAAG,EAAA,IAAM,EAAE,QAAQ,CAAC;AAC5B,kBAAY,QAAQ;AAAA,QAClB,QAAQ;AAAA,UACN;AAAA,YACE;AAAA,YACA;AAAA,YACA,GAAG,KAAK,IAAI;AAAA;AAAA,UAAA;AAAA,QAEhB;AAAA,QACA,OAAO,MAAM;AAAA,QACb,OAAO,oBAAoB;AAAA,QAC3B,iBAAiB,MAAM;AAAA,QACvB,YAAY,MAAM;AAAA;AAAA,MACpB;AAGA,gBAAU,QAAQ,CAAC;AACnB,WAAK,CAAC;AAAA,IACR;AAGM,UAAA,cAAc,CAAC,MAAkB;AACrC,QAAE,eAAe;AACjB,cAAQ,QAAQ;AAChB,UAAI,YAAY,OAAO;AAErB,cAAM,MAAM,KAAK,iCACZ,YAAY,QADA;AAAA,UAEf,QAAQ,YAAY,MAAM,OAAO,IAAI,CAAC,UAAW,iCAC5C,QAD4C;AAAA,YAE/C,GAAG,MAAM;AAAA,YACT,OAAO,MAAM;AAAA,YACb,UAAU,MAAM;AAAA,YAChB,WAAW,MAAM;AAAA,YACjB,QAAQ,MAAM;AAAA,YACd,QAAQ,MAAM;AAAA,YACd,QAAQ,MAAM;AAAA,YACd,QAAQ,MAAM;AAAA,YACd,cAAc,MAAM;AAAA,UAAA,EACpB;AAAA,QAAA,EACH;AACW,oBAAA,QAAQ,MAAM,MAAM;AAAA,MAAA;AAElC,kBAAY,QAAQ;AACd,YAAA,EAAE,QAAQ;AACZ,UAAA;AAAK,YAAI,UAAU;AACvB,WAAK,OAAO,CAAC;AAAA,IACf;AAMM,UAAA,aAAa,CAAC,cAAuB,UAAU;AAEnD,UAAI,CAAC,eAAe,YAAY,gBAAgB,YAAY,aAAa;AACvE;AAAA,MAAA;AAES,iBAAA,EAAE,KAAK,MAAM;AAChB,cAAA,EAAE,QAAQ;AAChB,YAAI,OAAOK,cAAAA,MAAM,MAAM,eAAe,GAAG;AACnC,cAAA,aAAa,MAAM,eAAe;AACtC,cAAI,SAAS,GAAG,GAAG,YAAY,aAAa,YAAY,YAAY;AACpE,cAAI,KAAK;AAAA,QAAA;AAAA,MACX,CACD;AAAA,IACH;AAGA,UAAM,QAAQ,MAAM;AAClB,YAAM,QAAQ,CAAC;AACf,gBAAU,QAAQ,CAAC;AACnB,kBAAY,QAAQ;AACR,kBAAA;AACZ,WAAK,OAAO;AAAA,IACd;AAGA,UAAM,mBAAmB,MAAM;AACf,oBAAA;AAAA,IAChB;AAGM,UAAA,OAAO,CAAC,MAAW;AACvB,QAAE,eAAe;AACX,YAAA,EAAE,QAAQ;AAEhB,UAAI,CAAC,QAAQ,SAAS,MAAM,YAAY,CAAC;AAAK;AAC9C,YAAM,EAAE,GAAG,EAAA,IAAM,EAAE,QAAQ,CAAC;AAE5B,YAAM,QAAe;AAAA,QACnB;AAAA,QACA;AAAA,QACA,GAAG,KAAK,IAAI;AAAA,MACd;AAEA,UAAI,YAAY,OAAO;AACf,cAAA,SAAS,YAAY,MAAM;AACjC,cAAM,WAAW,OAAO,OAAO,SAAS,CAAC;AAErC,YAAA,SAAS,MAAM,MAAM,KAAM,SAAS,MAAM,KAAK,SAAS,MAAM,GAAI;AACpE;AAAA,QAAA;AAIF,cAAM,WAAW,KAAK,KAAK,KAAK,IAAI,MAAM,IAAI,SAAS,GAAG,CAAC,IAAI,KAAK,IAAI,MAAM,IAAI,SAAS,GAAG,CAAC,CAAC;AAChG,cAAM,QAAQ,MAAM,YAAY,MAAM,IAAI,SAAS,KAAK;AAExD,YAAI,MAAM,UAAU;AACZ,gBAAA,YAAY,mBAAmB,MAAM,KAAK;AAE5C,cAAA,OAAO,UAAU,GAAG;AACtB,kBAAM,YAAY,OAAO,OAAO,SAAS,CAAC;AACtC,gBAAA,UAAU,aAAa,SAAS,WAAW;AAC7C,oBAAM,QAAQ,MAAM,YAAY,SAAS,aAAa,SAAS;AAC/D,oBAAM,UAAU;AAChB,kBAAI,KAAK,IAAI,IAAI,IAAI,SAAS;AAC5B,sBAAM,MAAM,OAAO,IAAI,UAAU;AAC3B,sBAAA,YAAY,SAAS,aAAa,IAAI;AAAA,cAAA;AAAA,YAC9C;AAAA,UACF;AAAA,QACF;AAGF,eAAO,KAAK,KAAK;AAGb,YAAA,CAAC,MAAM,UAAU;AACnB,cAAI,UAAU;AACd,cAAI,OAAO,SAAS,GAAG,SAAS,CAAC;AACjC,cAAI,OAAO,MAAM,GAAG,MAAM,CAAC;AAC3B,cAAI,OAAO;AACX,cAAI,KAAK,IAAI;AAAA,QAAA,WACJ,OAAO,UAAU,GAAG;AAE7B,yBAAe,UAAU,KAAK;AAAA,QAAA;AAAA,MAChC;AAGF,WAAK,WAAW,CAAC;AAAA,IACnB;AAGA,UAAM,eAAe,MAAM;AACnB,YAAA,EAAE,QAAQ;AAChB,UAAI,CAAC;AAAK;AAGN,UAAAA,cAAA,MAAM,MAAM,eAAe,GAAG;AAC5B,YAAA,aAAa,MAAM,eAAe;AACtC,YAAI,SAAS,GAAG,GAAG,YAAY,aAAa,YAAY,YAAY;AAAA,MAAA,OAC/D;AACL,YAAI,UAAU,GAAG,GAAG,YAAY,aAAa,YAAY,YAAY;AAAA,MAAA;AAInE,UAAA,MAAM,MAAM,WAAW,GAAG;AAC5B,YAAI,KAAK;AACT;AAAA,MAAA;AAII,YAAA,MAAM,QAAQ,CAAC,SAAS;AACxB,YAAA,CAAC,KAAK,OAAO;AAAQ;AAErB,YAAA,eAAe,KAAK,KAAK;AAC7B,YAAI,YAAY,OAAO;AACvB,YAAI,WAAW,OAAO;AAElB,YAAA,KAAK,cAAc,MAAM,UAAU;AAErC,eAAK,OAAO,QAAQ,CAAC,OAAO,UAAU;AACpC,gBAAI,UAAU;AAAG;AACjB,kBAAM,WAAW,KAAK,OAAO,QAAQ,CAAC;AAChC,kBAAA,QAAQ,MAAM,IAAI,SAAS;AAC3B,kBAAA,QAAQ,MAAM,IAAI,SAAS;AACjC,kBAAM,WAAW,KAAK,KAAK,QAAQ,QAAQ,QAAQ,KAAK;AAExD,gBAAI,YAAY,GAAG;AACjB,oBAAM,SAAS,MAAM,SAAS,SAAS,IAAI,QAAQ;AACnD,oBAAM,SAAS,MAAM,SAAS,SAAS,IAAI,QAAQ;AAAA,YAAA,OAC9C;AACC,oBAAA,QAAQ,MAAM,SAAS;AACvB,oBAAA,WAAW,MAAM,YAAY;AAC7B,oBAAA,cAAc,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,SAAS,WAAW,GAAG,CAAC;AAExE,oBAAM,SAAS,SAAS,IAAI,SAAS,MAAM,cAAc;AACzD,oBAAM,SAAS,SAAS,IAAI,SAAS,MAAM,cAAc;AACzD,oBAAM,SAAS,SAAS,IAAI,SAAS,MAAM,cAAc;AACzD,oBAAM,SAAS,SAAS,IAAI,SAAS,MAAM,cAAc;AAAA,YAAA;AAGrD,kBAAA,YAAY,MAAM,aAAa,KAAK;AACtC,gBAAA,OAAO,SAAS,WAAW,UAAU;AACvC,kBAAI,aAAa,SAAS;AAC1B,kBAAI,UAAU;AACd,kBAAI,OAAO,SAAS,QAAS,SAAS,MAAO;AACzC,kBAAA,iBAAiB,SAAS,GAAG,SAAS,GAAG,MAAM,QAAQ,MAAM,MAAM;AACvE,kBAAI,OAAO;AAEP,kBAAA,CAAC,SAAS,cAAc;AAC1B,oBAAI,UAAU;AACd,oBAAI,OAAO,SAAS,QAAS,SAAS,MAAO;AACzC,oBAAA,iBAAiB,SAAS,GAAG,SAAS,GAAG,SAAS,QAAS,SAAS,MAAO;AAC/E,oBAAI,OAAO;AAAA,cAAA;AAAA,YACb,OACK;AACL,oBAAM,eAAe;AAAA,YAAA;AAAA,UACvB,CACD;AAAA,QAAA,OACI;AAED,cAAA,aAAa,KAAK,KAAK;AAC3B,eAAK,OAAO,QAAQ,CAAC,OAAO,UAAU;AACpC,gBAAI,UAAU;AAAG;AACjB,kBAAM,WAAW,KAAK,OAAO,QAAQ,CAAC;AACtC,gBAAI,UAAU;AACd,gBAAI,OAAO,SAAS,GAAG,SAAS,CAAC;AACjC,gBAAI,OAAO,MAAM,GAAG,MAAM,CAAC;AAC3B,gBAAI,OAAO;AAAA,UAAA,CACZ;AAAA,QAAA;AAAA,MACH,CACD;AAGD,UAAI,KAAK;AAAA,IACX;AAGA,UAAM,SAAS,MAAM;AACf,UAAA,CAAC,MAAM,MAAM;AAAQ;AACzB,YAAM,OAAO,KAAK,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM;AACpD,YAAM,eAAe,MAAM,MAAM,OAAO,MAAM,MAAM,SAAS,IAAI;AACvD,gBAAA,MAAM,KAAK,GAAG,YAAY;AACpC,kBAAY,QAAQ,KAAK,IAAI,GAAG,YAAY,QAAQ,IAAI;AAC3C,mBAAA;AAAA,IACf;AAGA,UAAM,UAAU,MAAM;AAChB,UAAA,CAAC,UAAU,MAAM;AAAQ;AAC7B,YAAM,OAAO,KAAK,IAAI,MAAM,MAAM,UAAU,MAAM,MAAM;AACxD,YAAM,gBAAgB,UAAU,MAAM,OAAO,UAAU,MAAM,SAAS,IAAI;AACpE,YAAA,MAAM,KAAK,GAAG,aAAa;AACrB,kBAAA,QAAQ,KAAK,IAAI,MAAM,MAAM,QAAQ,YAAY,QAAQ,IAAI;AAC5D,mBAAA;AAAA,IACf;AAGS,aAAA,eAAe,UAAiB,OAAc;AAC/C,YAAA,EAAE,QAAQ;AAChB,UAAI,CAAC;AAAK;AAGJ,YAAA,QAAQ,MAAM,IAAI,SAAS;AAC3B,YAAA,QAAQ,MAAM,IAAI,SAAS;AACjC,YAAM,WAAW,KAAK,KAAK,QAAQ,QAAQ,QAAQ,KAAK;AAExD,UAAI,YAAY,GAAG;AAEjB,cAAM,SAAS,MAAM,SAAS,SAAS,IAAI,QAAQ;AACnD,cAAM,SAAS,MAAM,SAAS,SAAS,IAAI,QAAQ;AAAA,MAAA,OAC9C;AAEC,cAAA,QAAQ,MAAM,SAAS;AACvB,cAAA,WAAW,MAAM,YAAY;AAC7B,cAAA,cAAc,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,SAAS,WAAW,GAAG,CAAC;AAGxE,cAAM,SAAS,SAAS,IAAI,SAAS,MAAM,cAAc;AACzD,cAAM,SAAS,SAAS,IAAI,SAAS,MAAM,cAAc;AACzD,cAAM,SAAS,SAAS,IAAI,SAAS,MAAM,cAAc;AACzD,cAAM,SAAS,SAAS,IAAI,SAAS,MAAM,cAAc;AAAA,MAAA;AAIrD,YAAA,YAAY,MAAM,aAAa,MAAM;AAGvC,UAAA,OAAO,SAAS,WAAW,UAAU;AAEvC,YAAI,aAAa,SAAS;AAE1B,YAAI,UAAU;AACd,YAAI,OAAO,SAAS,QAAS,SAAS,MAAO;AACzC,YAAA,iBAAiB,SAAS,GAAG,SAAS,GAAG,MAAM,QAAQ,MAAM,MAAM;AACvE,YAAI,OAAO;AAEP,YAAA,CAAC,SAAS,cAAc;AAE1B,cAAI,UAAU;AACd,cAAI,OAAO,SAAS,QAAS,SAAS,MAAO;AACzC,cAAA,iBAAiB,SAAS,GAAG,SAAS,GAAG,SAAS,QAAS,SAAS,MAAO;AAC/E,cAAI,OAAO;AAAA,QAAA;AAIb,YAAI,KAAK,IAAI;AAAA,MAAA,OACR;AACL,cAAM,eAAe;AAAA,MAAA;AAAA,IACvB;AAGFG,kBAAAA,UAAU,MAAM;AACH,iBAAA;AAAA,IAAA,CACZ;AAEDC,kBAAAA,cAAc,MAAM;AAEP,iBAAA,QAAQC,oBAAI,kBAAoB,EAAA;AAAA,IAAA,CAE5C;AAKD,aAAS,aAAa;AACb,aAAA,IAAI,QAA8B,CAAC,YAAY;AAC9C,cAAA,EAAE,QAAQ;AAEhB,YAAI,KAAK;AACP,iBAAO,QAAQ,GAAG;AAAA,QAAA;AAcZC,sBAAAA,QAAA,IAAI,SAAS,KAAK,IAAI,OAAO,OAAO,IAAI,EAAE,KAAK,CAAC,eAAoB;AAC1E,cAAI,cAAc,WAAW,QAAQ,WAAW,SAAS,WAAW,QAAQ;AAC1E,kBAAM,iBAAiB,WAAW;AAClC,wBAAY,MAAMC,cAAA,gBAAgB,eAAe,WAAW,IAAI,CAA6B;AAC9E,2BAAA,QAAQ,WAAW,QAAQ,WAAW;AACtC,2BAAA,SAAS,WAAW,SAAS,WAAW;AACvD,wBAAY,IAAI,MAAM,WAAW,OAAO,WAAW,KAAK;AAC/C,qBAAA;AACM,2BAAA,WAAW,OAAO,WAAW,MAAM;AAClD,oBAAQ,YAAY,GAAG;AAAA,UAAA;AAAA,QACzB,CACD;AAAA,MAAA,CAEF;AAAA,IAAA;AAMM,aAAA,eAAe,OAAe,QAAgB;AACzC,kBAAA,eAAe,SAAS,WAAW;AACnC,kBAAA,cAAc,QAAQ,WAAW;AAAA,IAAA;AAI/C,aAAS,UAAU;AACX,YAAA,EAAE,QAAQ;AAChB,UAAI,KAAK;AACH,YAAA,aAAa,qBAAqB;AAClC,YAAA,eAAe,MAAM,QAAQ;AACjC,YAAI,YAAY,OAAO;AACvB,YAAI,WAAW,OAAO;AAAA,MAAA;AAAA,IACxB;AAMF,aAAS,gBAAgB;AACvB,YAAM,EAAE,UAAU,SAAS,YAAgB,IAAA;AACrC,YAAA,EAAE,aAAa,aAAA,IAAiB;AAClCF,oBAAAA,MAAA;AAAA,QACF;AAAA,UACE,OAAO,cAAc;AAAA,UACrB,QAAQ,eAAe;AAAA,UACvB,WAAW,cAAc;AAAA,UACzB,YAAY,eAAe;AAAA,UAC3B;AAAA,UACA;AAAA,UACA,UAAU,SAAS;AAAA,UACnB;AAAA,UACA,SAAS,CAAC,QAAQ;AAChB,kBAAM,SAA0B;AAAA,cAC9B,cAAc,IAAI;AAAA,cAClB,OAAQ,cAAc,cAAe,WAAW;AAAA,cAChD,QAAS,eAAe,cAAe,WAAW;AAAA,cAClD,SAAS;AAAA,YACX;AAIA,iBAAK,WAAW,MAAM;AAAA,UACxB;AAAA,UACA,MAAM,MAAM;AACV,kBAAM,SAA0B;AAAA,cAC9B,cAAc;AAAA,cACd,OAAQ,cAAc,cAAe,WAAW;AAAA,cAChD,QAAS,eAAe,cAAe,WAAW;AAAA,cAClD,SAAS;AAAA,YACX;AACA,iBAAK,WAAW,MAAM;AAAA,UAAA;AAAA,QAE1B;AAAA,QACA;AAAA,MACF;AAAA,IAAA;AAGF,aAAS,cAAc;AACrB,YAAM,EAAE,aAAa,cAAc,IAAQ,IAAA;AAC3C,UAAI,KAAK;AACP,YAAI,UAAU,GAAG,GAAG,aAAa,YAAY;AACzC,YAAAL,cAAA,MAAM,MAAM,eAAe,GAAG;AAC5B,cAAA,aAAa,MAAM,eAAe;AACtC,cAAI,SAAS,GAAG,GAAG,aAAa,YAAY;AAAA,QAAA;AAE9C,YAAI,KAAK;AAAA,MAAA;AAAA,IACX;AAG4B,aAAA;AAAA,MAC5B,MAAM;AAAA,MACN;AAAA,MACA,SAAS;AAAA,MACT;AAAA,MACA;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzkBD,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}