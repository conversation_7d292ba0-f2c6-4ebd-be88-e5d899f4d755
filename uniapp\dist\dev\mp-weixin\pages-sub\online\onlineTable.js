"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
const utils_http = require("../../utils/http.js");
require("../../plugin/uni-mini-router/router/index.js");
const plugin_uniMiniRouter_core_index = require("../../plugin/uni-mini-router/core/index.js");
const store_user = require("../../store/user.js");
const store_pageParams = require("../../store/page-params.js");
if (!Array) {
  const _easycom_wd_table_col2 = common_vendor.resolveComponent("wd-table-col");
  const _easycom_wd_table2 = common_vendor.resolveComponent("wd-table");
  const _easycom_wd_status_tip2 = common_vendor.resolveComponent("wd-status-tip");
  const _easycom_wd_pagination2 = common_vendor.resolveComponent("wd-pagination");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _easycom_BottomOperate2 = common_vendor.resolveComponent("BottomOperate");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_table_col2 + _easycom_wd_table2 + _easycom_wd_status_tip2 + _easycom_wd_pagination2 + _easycom_PageLayout2 + _easycom_BottomOperate2 + _component_layout_default_uni)();
}
const _easycom_wd_table_col = () => "../../node-modules/wot-design-uni/components/wd-table-col/wd-table-col.js";
const _easycom_wd_table = () => "../../node-modules/wot-design-uni/components/wd-table/wd-table.js";
const _easycom_wd_status_tip = () => "../../node-modules/wot-design-uni/components/wd-status-tip/wd-status-tip.js";
const _easycom_wd_pagination = () => "../../node-modules/wot-design-uni/components/wd-pagination/wd-pagination.js";
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
const _easycom_BottomOperate = () => "../../components/BottomOperate/BottomOperate.js";
if (!Math) {
  (onlineTableCell + _easycom_wd_table_col + _easycom_wd_table + _easycom_wd_status_tip + _easycom_wd_pagination + _easycom_PageLayout + _easycom_BottomOperate)();
}
const onlineTableCell = () => "./components/onlineTableCell.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "onlineTable",
  options: {
    styleIsolation: "shared"
  }
}), {
  __name: "onlineTable",
  setup(__props) {
    const toast = common_vendor.useToast();
    const router = plugin_uniMiniRouter_core_index.useRouter();
    store_user.useUserStore();
    const paramsStore = store_pageParams.useParamsStore();
    const globalData = getApp().globalData;
    const { systemInfo } = globalData;
    const { safeArea } = systemInfo;
    const columns = common_vendor.ref([]);
    const columnsInfo = common_vendor.ref({});
    const tableIndex = common_vendor.ref(false);
    const pageNo = common_vendor.ref(1);
    const pageSize = common_vendor.ref(10);
    const pageTotal = common_vendor.ref(1);
    let pageParams = {};
    common_vendor.ref(0);
    const bottomOperatePopup = common_vendor.reactive({
      show: false,
      title: "操作",
      data: {},
      options: [
        { key: "edit", icon: "edit", label: "编辑" },
        { key: "detail", icon: "view", label: "查看" },
        { key: "delete", icon: "delete", label: "删除", color: "red" }
      ]
    });
    const queryParams = () => {
      return {
        pageNo: pageNo.value,
        pageSize: pageSize.value,
        order: "asc",
        column: "id",
        hasQuery: true
      };
    };
    const dataList = common_vendor.ref([]);
    const getColumns = () => {
      return new Promise((resove, reject) => {
        const analysis = (data) => {
          const len = data.length;
          const maxShowColumn = 3;
          let space = 1;
          if (len == 1) {
            space = 2;
          }
          const width = safeArea.width / (len > maxShowColumn ? maxShowColumn : len) - space;
          columns.value = data.map((item) => {
            return __spreadProps(__spreadValues({}, item), {
              prop: item.dataIndex,
              align: item.align,
              label: item.title,
              width
            });
          });
        };
        utils_http.http.get(`/online/cgform/api/getColumns/${pageParams.id}`).then((res) => {
          var _a, _b;
          if (res.success) {
            if ((_b = (_a = res.result) == null ? void 0 : _a.columns) == null ? void 0 : _b.length) {
              columnsInfo.value = res.result;
              analysis(res.result.columns);
            }
          } else {
            toast.warning(res.message);
          }
        }).catch((res) => {
          toast.error("加载列头失败~");
        });
      });
    };
    const getData = () => {
      utils_http.http.get(`/online/cgform/api/getData/${pageParams.id}`, __spreadValues({}, queryParams())).then((res) => {
        var _a, _b;
        if (res.success) {
          dataList.value = (_b = (_a = res.result) == null ? void 0 : _a.records) != null ? _b : [];
          pageTotal.value = res.result.total;
        } else {
          toast.warning(res.message);
        }
      }).catch((res) => {
        toast.error("加载表格数据失败~");
      });
    };
    const handlePaginChange = ({ value }) => {
      pageNo.value = value;
      getData();
    };
    const handleGo = () => {
      router.push({
        name: "onlineAdd",
        params: { desformCode: pageParams.tableName, desformName: pageParams.tableTxt }
      });
    };
    const handleEdit = (record) => {
      router.push({
        name: "onlineEdit",
        params: { desformCode: pageParams.tableName, desformName: pageParams.tableTxt, id: record.id }
      });
    };
    const handleView = (record) => {
      router.push({
        name: "onlineDetail",
        params: { desformCode: pageParams.tableName, desformName: pageParams.tableTxt, id: record.id }
      });
    };
    const handleLongPress = (item) => {
      bottomOperatePopup.show = true;
      bottomOperatePopup.data = item;
    };
    const handleChange = ({ option, data }) => {
      if (option.key === "edit") {
        handleEdit(data);
      } else if (option.key === "delete") {
        common_vendor.index.showModal({
          title: "提示",
          content: "确定要删除吗？",
          cancelText: "取消",
          confirmText: "确定",
          success: (res) => {
            if (res.confirm) {
              utils_http.http.delete(`/online/cgform/api/form/${pageParams.id}/${data.id}`).then((res2) => {
                toast.success("删除成功~");
                getData();
              });
            }
          },
          fail: (err) => {
            console.log(err);
          }
        });
      } else if (option.key === "detail") {
        handleView(data);
      }
    };
    const handleRow = ({ rowIndex: rowIndex2 }) => {
      handleEdit(dataList.value[rowIndex2]);
    };
    const init = () => {
      var _a, _b;
      pageParams = (_b = (_a = paramsStore.getPageParams("onlineTable")) == null ? void 0 : _a.data) != null ? _b : {};
      console.log("pageParams:", pageParams);
      getColumns();
      getData();
    };
    init();
    common_vendor.onMounted(() => {
      common_vendor.index.$on("refreshList", () => {
        getData();
      });
    });
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.f(common_vendor.unref(columns), (item, index, i0) => {
          return {
            a: common_vendor.w(({
              row,
              index: index2
            }, s1, i1) => {
              return {
                a: common_vendor.o(($event) => handleLongPress(row), item.prop),
                b: "7c1b946c-4-" + i0 + "-" + i1 + "," + ("7c1b946c-3-" + i0),
                c: common_vendor.p({
                  columnsInfo: common_vendor.unref(columnsInfo),
                  record: row,
                  column: item
                }),
                d: i1,
                e: s1
              };
            }, {
              name: "value",
              path: "a[" + i0 + "].a",
              vueId: "7c1b946c-3-" + i0 + ",7c1b946c-2"
            }),
            b: "7c1b946c-3-" + i0 + ",7c1b946c-2",
            c: common_vendor.p({
              prop: item.prop,
              label: item.label,
              width: item.width,
              fixed: item.fixed,
              align: item.align,
              sortable: item.sortable
            }),
            d: item.prop
          };
        }),
        b: common_vendor.o(handleRow),
        c: common_vendor.p({
          index: common_vendor.unref(tableIndex),
          data: common_vendor.unref(dataList)
        }),
        d: common_vendor.unref(dataList).length == 0
      }, common_vendor.unref(dataList).length == 0 ? {
        e: common_vendor.p({
          image: "content",
          tip: "暂无内容"
        })
      } : {}, {
        f: common_vendor.o(handlePaginChange),
        g: common_vendor.o(($event) => common_vendor.isRef(pageNo) ? pageNo.value = $event : null),
        h: common_vendor.p({
          total: common_vendor.unref(pageTotal),
          ["page-size"]: common_vendor.unref(pageSize),
          ["show-icon"]: true,
          modelValue: common_vendor.unref(pageNo)
        }),
        i: common_vendor.o(handleGo),
        j: common_vendor.p({
          backRouteName: "online",
          navTitle: "online在线表单"
        }),
        k: common_vendor.unref(bottomOperatePopup).show
      }, common_vendor.unref(bottomOperatePopup).show ? {
        l: common_vendor.o(() => common_vendor.unref(bottomOperatePopup).show = false),
        m: common_vendor.o(handleChange),
        n: common_vendor.p(__spreadValues({}, common_vendor.unref(bottomOperatePopup)))
      } : {});
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-7c1b946c"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=onlineTable.js.map
