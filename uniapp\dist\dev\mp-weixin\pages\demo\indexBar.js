"use strict";
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _easycom_wd_search2 = common_vendor.resolveComponent("wd-search");
  const _easycom_wd_index_anchor2 = common_vendor.resolveComponent("wd-index-anchor");
  const _easycom_wd_cell2 = common_vendor.resolveComponent("wd-cell");
  const _easycom_wd_index_bar2 = common_vendor.resolveComponent("wd-index-bar");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_search2 + _easycom_wd_index_anchor2 + _easycom_wd_cell2 + _easycom_wd_index_bar2 + _easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_wd_search = () => "../../node-modules/wot-design-uni/components/wd-search/wd-search.js";
const _easycom_wd_index_anchor = () => "../../node-modules/wot-design-uni/components/wd-index-anchor/wd-index-anchor.js";
const _easycom_wd_cell = () => "../../node-modules/wot-design-uni/components/wd-cell/wd-cell.js";
const _easycom_wd_index_bar = () => "../../node-modules/wot-design-uni/components/wd-index-bar/wd-index-bar.js";
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  (_easycom_wd_search + _easycom_wd_index_anchor + _easycom_wd_cell + _easycom_wd_index_bar + _easycom_PageLayout)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "indexBar",
  setup(__props) {
    const { show: showToast } = common_vendor.useToast();
    common_vendor.onMounted(() => {
      handleSearch();
    });
    const keyword = common_vendor.ref("");
    const showList = common_vendor.ref([]);
    const indexList = [
      {
        index: "A",
        data: ["阿坝", "阿拉善", "阿里", "安康", "安庆", "鞍山", "安顺", "安阳", "澳门"]
      },
      {
        index: "B",
        data: [
          "北京",
          "白银",
          "保定",
          "宝鸡",
          "保山",
          "包头",
          "巴中",
          "北海",
          "蚌埠",
          "本溪",
          "毕节",
          "滨州",
          "百色",
          "亳州"
        ]
      },
      {
        index: "C",
        data: [
          "重庆",
          "成都",
          "长沙",
          "长春",
          "沧州",
          "常德",
          "昌都",
          "长治",
          "常州",
          "巢湖",
          "潮州",
          "承德",
          "郴州",
          "赤峰",
          "池州",
          "崇左",
          "楚雄",
          "滁州",
          "朝阳"
        ]
      },
      {
        index: "D",
        data: [
          "大连",
          "东莞",
          "大理",
          "丹东",
          "大庆",
          "大同",
          "大兴安岭",
          "德宏",
          "德阳",
          "德州",
          "定西",
          "迪庆",
          "东营"
        ]
      },
      {
        index: "E",
        data: ["鄂尔多斯", "恩施", "鄂州"]
      },
      {
        index: "F",
        data: ["福州", "防城港", "佛山", "抚顺", "抚州", "阜新", "阜阳"]
      },
      {
        index: "G",
        data: ["广州", "桂林", "贵阳", "甘南", "赣州", "甘孜", "广安", "广元", "贵港", "果洛"]
      },
      {
        index: "H",
        data: [
          "杭州",
          "哈尔滨",
          "合肥",
          "海口",
          "呼和浩特",
          "海北",
          "海东",
          "海南",
          "海西",
          "邯郸",
          "汉中",
          "鹤壁",
          "河池",
          "鹤岗",
          "黑河",
          "衡水",
          "衡阳",
          "河源",
          "贺州",
          "红河",
          "淮安",
          "淮北",
          "怀化",
          "淮南",
          "黄冈",
          "黄南",
          "黄山",
          "黄石",
          "惠州",
          "葫芦岛",
          "呼伦贝尔",
          "湖州",
          "菏泽"
        ]
      }
    ];
    function handleClick(index, city) {
      showToast(`当前点击项：${index}，城市：${city}`);
    }
    function handleSearch() {
      showList.value = [];
      common_vendor.nextTick$1(() => {
        if (keyword.value) {
          showList.value = indexList.filter((item) => {
            return item.data.some((city) => {
              return city.includes(keyword.value);
            });
          });
        } else {
          showList.value = indexList;
        }
      });
    }
    function handleClear() {
      keyword.value = "";
      handleSearch();
    }
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.o(handleSearch),
        b: common_vendor.o(handleClear),
        c: common_vendor.o(($event) => keyword.value = $event),
        d: common_vendor.p({
          ["hide-cancel"]: true,
          placeholder: "我要去哪里？",
          modelValue: keyword.value
        }),
        e: showList.value.length
      }, showList.value.length ? {
        f: common_vendor.f(showList.value, (item, k0, i0) => {
          return {
            a: "6ec61418-4-" + i0 + ",6ec61418-3",
            b: common_vendor.p({
              index: item.index
            }),
            c: common_vendor.f(item.data, (city, k1, i1) => {
              return {
                a: city,
                b: common_vendor.o(($event) => handleClick(item.index, city), city),
                c: "6ec61418-5-" + i0 + "-" + i1 + ",6ec61418-3",
                d: common_vendor.p({
                  border: true,
                  clickable: true,
                  title: city
                })
              };
            }),
            d: item.index
          };
        }),
        g: common_vendor.p({
          sticky: true
        })
      } : {}, {
        h: common_vendor.p({
          navTitle: "通讯录"
        })
      });
    };
  }
});
wx.createPage(_sfc_main);
//# sourceMappingURL=indexBar.js.map
