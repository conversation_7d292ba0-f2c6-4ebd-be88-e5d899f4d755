"use strict";
const echartProps = {
  i: {
    type: [String, Number],
    default: ""
  },
  id: {
    type: String,
    default: ""
  },
  config: {
    type: Object,
    default: () => ({})
  },
  height: {
    type: Number
  },
  compName: {
    type: String,
    default: ""
  },
  horizontal: {
    type: Boolean,
    default: false
  },
  appId: {
    type: String,
    default: ""
  }
};
exports.echartProps = echartProps;
//# sourceMappingURL=props.js.map
