{"version": 3, "file": "useUpload.js", "sources": ["../../../../src/hooks/useUpload.ts"], "sourcesContent": ["// TODO: 别忘加更改环境变量的 VITE_UPLOAD_BASEURL 地址。\r\nimport { getEnvBaseUploadUrl } from '@/utils'\r\nimport { useUserStore } from '@/store/user'\r\n\r\nconst VITE_UPLOAD_BASEURL = `${getEnvBaseUploadUrl()}`\r\n\r\n/**\r\n * useUpload 是一个定制化的请求钩子，用于处理上传图片。\r\n * @param formData 额外传递给后台的数据，如{name: '张三'}。\r\n * @returns 返回一个对象{loading, error, data, run}，包含请求的加载状态、错误信息、响应数据和手动触发请求的函数。\r\n */\r\nexport default function useUpload<T = string>(\r\n  formData: Record<string, any> = {},\r\n  { url, sizeType = ['original', 'compressed'], sourceType = ['album', 'camera'] },\r\n) {\r\n  const loading = ref(false)\r\n  const error = ref(false)\r\n  const data = ref<T>()\r\n  const run = () => {\r\n    // #ifdef MP-WEIXIN\r\n    // 微信小程序从基础库 2.21.0 开始， wx.chooseImage 停止维护，请使用 uni.chooseMedia 代替。\r\n    // 微信小程序在2023年10月17日之后，使用本API需要配置隐私协议\r\n    uni.chooseMedia({\r\n      count: 1,\r\n      mediaType: ['image'],\r\n      sourceType,\r\n      sizeType, //可以指定是原图还是压缩图，默认二者都有\r\n      success: (res) => {\r\n        loading.value = true\r\n        const tempFilePath = res.tempFiles[0].tempFilePath\r\n        const fileName = res.type\r\n        formData.fileName = fileName;\r\n        uploadFile<T>({ url, tempFilePath, formData, data, error, loading, fileName })\r\n      },\r\n      fail: (err) => {\r\n        console.error('uni.chooseMedia err->', err)\r\n        error.value = true\r\n      },\r\n    })\r\n    // #endif\r\n    // #ifndef MP-WEIXIN\r\n    uni.chooseImage({\r\n      count: 1,\r\n      sourceType, //从相册选择\r\n      sizeType, //可以指定是原图还是压缩图，默认二者都有\r\n      success: (res) => {\r\n        loading.value = true\r\n        const tempFilePath = res.tempFilePaths[0]\r\n        const fileName = res.tempFiles[0].name\r\n        formData.fileName = fileName;\r\n        uploadFile<T>({ url, tempFilePath, formData, data, error, loading })\r\n      },\r\n      fail: (err) => {\r\n        console.error('uni.chooseImage err->', err)\r\n        error.value = true\r\n      },\r\n    })\r\n    // #endif\r\n  }\r\n\r\n  return { loading, error, data, run }\r\n}\r\n\r\nfunction uploadFile<T>({ url, tempFilePath, formData, data, error, loading }) {\r\n  const userStore = useUserStore()\r\n  uni.uploadFile({\r\n    url: url ?? VITE_UPLOAD_BASEURL,\r\n    filePath: tempFilePath,\r\n    name: 'file',\r\n    formData,\r\n    header: {\r\n      'X-Access-Token': userStore.userInfo.token,\r\n      'X-Tenant-Id': userStore.userInfo.tenantId,\r\n    },\r\n    success: (uploadFileRes) => {\r\n      data.value = JSON.parse(uploadFileRes.data)\r\n    },\r\n    fail: (err) => {\r\n      console.error('uni.uploadFile err->', err)\r\n      error.value = true\r\n    },\r\n    complete: () => {\r\n      loading.value = false\r\n    },\r\n  })\r\n}\r\n"], "names": ["getEnvBaseUploadUrl", "ref", "uni", "useUserStore"], "mappings": ";;;;AAIA,MAAM,sBAAsB,GAAGA,YAAA,oBAAA,CAAqB;AAOpD,SAAwB,UACtB,WAAgC,CAAA,GAChC,EAAE,KAAK,WAAW,CAAC,YAAY,YAAY,GAAG,aAAa,CAAC,SAAS,QAAQ,KAC7E;AACM,QAAA,UAAUC,kBAAI,KAAK;AACnB,QAAA,QAAQA,kBAAI,KAAK;AACvB,QAAM,OAAOA,cAAAA,IAAO;AACpB,QAAM,MAAM,MAAM;AAIhBC,kBAAAA,MAAI,YAAY;AAAA,MACd,OAAO;AAAA,MACP,WAAW,CAAC,OAAO;AAAA,MACnB;AAAA,MACA;AAAA;AAAA,MACA,SAAS,CAAC,QAAQ;AAChB,gBAAQ,QAAQ;AAChB,cAAM,eAAe,IAAI,UAAU,CAAC,EAAE;AACtC,cAAM,WAAW,IAAI;AACrB,iBAAS,WAAW;AACN,mBAAA,EAAE,KAAK,cAAc,UAAU,MAAM,OAAO,QAAkB,CAAC;AAAA,MAC/E;AAAA,MACA,MAAM,CAAC,QAAQ;AACL,gBAAA,MAAM,yBAAyB,GAAG;AAC1C,cAAM,QAAQ;AAAA,MAAA;AAAA,IAChB,CACD;AAAA,EAoBH;AAEA,SAAO,EAAE,SAAS,OAAO,MAAM,IAAI;AACrC;AAEA,SAAS,WAAc,EAAE,KAAK,cAAc,UAAU,MAAM,OAAO,WAAW;AAC5E,QAAM,YAAYC,WAAAA,aAAa;AAC/BD,gBAAAA,MAAI,WAAW;AAAA,IACb,KAAK,oBAAO;AAAA,IACZ,UAAU;AAAA,IACV,MAAM;AAAA,IACN;AAAA,IACA,QAAQ;AAAA,MACN,kBAAkB,UAAU,SAAS;AAAA,MACrC,eAAe,UAAU,SAAS;AAAA,IACpC;AAAA,IACA,SAAS,CAAC,kBAAkB;AAC1B,WAAK,QAAQ,KAAK,MAAM,cAAc,IAAI;AAAA,IAC5C;AAAA,IACA,MAAM,CAAC,QAAQ;AACL,cAAA,MAAM,wBAAwB,GAAG;AACzC,YAAM,QAAQ;AAAA,IAChB;AAAA,IACA,UAAU,MAAM;AACd,cAAQ,QAAQ;AAAA,IAAA;AAAA,EAClB,CACD;AACH;;"}