"use strict";
var NavTypeEnum = /* @__PURE__ */ ((NavTypeEnum2) => {
  NavTypeEnum2["push"] = "navigateTo";
  NavTypeEnum2["replace"] = "redirectTo";
  NavTypeEnum2["replaceAll"] = "reLaunch";
  NavTypeEnum2["pushTab"] = "switchTab";
  NavTypeEnum2["back"] = "navigateBack";
  return NavTypeEnum2;
})(NavTypeEnum || {});
const NavMethod = ["navigateTo", "redirectTo", "reLaunch", "switchTab", "navigateBack"];
exports.NavMethod = NavMethod;
exports.NavTypeEnum = NavTypeEnum;
//# sourceMappingURL=index.js.map
