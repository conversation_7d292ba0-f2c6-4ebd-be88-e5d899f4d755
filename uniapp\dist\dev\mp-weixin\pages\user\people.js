"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
require("../../plugin/uni-mini-router/router/index.js");
const plugin_uniMiniRouter_core_index = require("../../plugin/uni-mini-router/core/index.js");
const utils_http = require("../../utils/http.js");
const store_user = require("../../store/user.js");
require("../../hooks/useUpload.js");
const utils_index = require("../../utils/index.js");
const utils_request = require("../../utils/request.js");
if (!Array) {
  const _easycom_wd_img2 = common_vendor.resolveComponent("wd-img");
  const _easycom_wd_message_box2 = common_vendor.resolveComponent("wd-message-box");
  const _easycom_wd_cell2 = common_vendor.resolveComponent("wd-cell");
  const _easycom_wd_cell_group2 = common_vendor.resolveComponent("wd-cell-group");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_img2 + _easycom_wd_message_box2 + _easycom_wd_cell2 + _easycom_wd_cell_group2 + _easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_wd_img = () => "../../node-modules/wot-design-uni/components/wd-img/wd-img.js";
const _easycom_wd_message_box = () => "../../node-modules/wot-design-uni/components/wd-message-box/wd-message-box.js";
const _easycom_wd_cell = () => "../../node-modules/wot-design-uni/components/wd-cell/wd-cell.js";
const _easycom_wd_cell_group = () => "../../node-modules/wot-design-uni/components/wd-cell-group/wd-cell-group.js";
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  (_easycom_wd_img + _easycom_wd_message_box + _easycom_wd_cell + _easycom_wd_cell_group + _easycom_PageLayout)();
}
const defAvatar = "https://www.mograine.cn/images/big_logo.png";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "people",
  setup(__props) {
    const userStore = store_user.useUserStore();
    const toast = common_vendor.useToast();
    const router = plugin_uniMiniRouter_core_index.useRouter();
    const message = common_vendor.useMessage();
    common_vendor.ref(false);
    const avatarApi = {
      uploadUrl: `${utils_index.getEnvBaseUrl()}/file/uploadavatar`
    };
    const fetchedAvatarSrc = common_vendor.ref("");
    const avatarSrc = common_vendor.computed(() => {
      const userAvatar = userStore.userInfo.avatar;
      if (userAvatar) {
        if (userAvatar.startsWith("http")) {
          return userAvatar;
        }
        return fetchedAvatarSrc.value || defAvatar;
      }
      return defAvatar;
    });
    common_vendor.reactive({
      avatar: "",
      realname: "",
      username: "",
      post: "",
      depart: "",
      phone: ""
    });
    common_vendor.ref(userStore.userInfo.userid);
    common_vendor.ref("");
    ({
      uploadUrl: `${utils_index.getEnvBaseUrl()}/sys/common/upload`
    });
    const dataSource = [
      { key: "setttings", title: "编辑资料", class: "cuIcon-settingsfill text-cyan" },
      { key: "exit", title: "退出/切换角色", class: "cuIcon-exit text-cyan" }
    ];
    const fetchFileUrl = (objectName) => __async(this, null, function* () {
      var _a;
      try {
        const response = yield utils_request.request(`/file/url`, {
          method: "GET",
          params: {
            objectName,
            token: userStore.userInfo.token
          }
        });
        if ((response == null ? void 0 : response.success) && ((_a = response == null ? void 0 : response.result) == null ? void 0 : _a.fileUrl)) {
          fetchedAvatarSrc.value = response.result.fileUrl;
        } else {
          console.warn("响应数据格式异常:", response);
          fetchedAvatarSrc.value = "";
        }
        return response;
      } catch (error) {
        console.error("获取文件URL失败:", error);
        fetchedAvatarSrc.value = "";
        return null;
      }
    });
    const exit = () => {
      message.confirm({
        title: "提示",
        msg: "确定退出吗？"
      }).then(() => {
        userStore.clearUserInfo();
        router.replaceAll({ name: "login" });
      });
    };
    const handleCell = (item) => {
      switch (item.key) {
        case "setttings":
          router.push({ name: "userEdit" });
          break;
        case "exit":
          exit();
          break;
        default:
          toast.show("功能暂未开发~");
      }
    };
    common_vendor.watch(() => userStore.userInfo.avatar, (newAvatar) => {
      if (newAvatar && !newAvatar.startsWith("http")) {
        fetchFileUrl(newAvatar);
      } else {
        fetchedAvatarSrc.value = "";
      }
    }, { immediate: true });
    common_vendor.onMounted(() => {
      const userAvatar = userStore.userInfo.avatar;
      if (userAvatar && !userAvatar.startsWith("http")) {
        fetchFileUrl(userAvatar);
      }
    });
    const onChooseWechatAvatar = (e) => {
      console.log("微信头像选择回调:", e);
      const { avatarUrl } = e.detail;
      if (!avatarUrl) {
        toast.warning("未获取到头像信息");
        return;
      }
      uploadWechatAvatar(avatarUrl);
    };
    const saveAvatarToBackend = (fileUrl, fileName) => __async(this, null, function* () {
      try {
        const data = {
          id: userStore.userInfo.userid,
          avatar: fileName
          // 使用fileName而不是完整URL
        };
        const response = yield utils_http.http.post("/sys/user/login/setting/userEdit", data);
        if (response.success) {
          userStore.editUserInfo(__spreadProps(__spreadValues({}, data), { avatar: fileUrl }));
          console.log("头像信息已保存到后端");
          return true;
        } else {
          console.error("保存头像信息失败:", response.message);
          toast.warning("保存头像信息失败: " + response.message);
          return false;
        }
      } catch (error) {
        console.error("保存头像信息异常:", error);
        toast.warning("保存头像信息失败");
        return false;
      }
    });
    const uploadWechatAvatar = (avatarUrl) => {
      console.log("开始上传微信头像:", avatarUrl);
      if (!avatarUrl.includes("tmp") && !avatarUrl.includes("wxfile://")) {
        toast.warning("头像文件格式不正确");
        return;
      }
      common_vendor.index.showLoading({
        title: "上传头像中...",
        mask: true
      });
      common_vendor.index.uploadFile({
        url: avatarApi.uploadUrl,
        filePath: avatarUrl,
        name: "file",
        formData: {
          type: "avatar"
          // 标识为头像上传
        },
        header: {
          "X-Access-Token": userStore.userInfo.token
        },
        success: (res) => __async(this, null, function* () {
          console.log("微信头像上传响应:", res);
          try {
            const data = JSON.parse(res.data);
            if (data && data.success && data.result && data.result.fileUrl) {
              const saveSuccess = yield saveAvatarToBackend(data.result.fileUrl, data.result.fileName);
              if (saveSuccess) {
                toast.success("头像更新成功");
              } else {
                userStore.userInfo.avatar = data.result.fileUrl;
                toast.success("头像上传成功，但保存信息失败");
              }
            } else {
              console.error("上传失败:", data);
              let errorMessage = (data == null ? void 0 : data.message) || "头像上传失败";
              try {
                if (typeof errorMessage === "string" && /[^\u0000-\u007f]/.test(errorMessage)) {
                  console.error("可能的编码问题:", errorMessage);
                  errorMessage = "头像上传失败";
                }
              } catch (e) {
                console.error("处理错误消息异常:", e);
              }
              toast.warning(errorMessage);
            }
          } catch (error) {
            console.error("解析上传响应失败:", error);
            toast.warning("头像上传失败");
          }
        }),
        fail: (error) => {
          console.error("微信头像上传失败:", error);
          toast.warning("头像上传失败，请重试");
        },
        complete: () => {
          common_vendor.index.hideLoading();
        }
      });
    };
    common_vendor.onBeforeUnmount(() => {
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          width: "70",
          height: "70",
          round: true,
          radius: 35,
          src: avatarSrc.value
        }),
        b: common_vendor.o(onChooseWechatAvatar),
        c: common_vendor.t(common_vendor.unref(userStore).userInfo.realname),
        d: common_vendor.t(common_vendor.unref(userStore).userInfo.phone),
        e: common_vendor.f(dataSource, (item, index, i0) => {
          return {
            a: common_vendor.n(item.class),
            b: common_vendor.o(($event) => handleCell(item), index),
            c: "0d4590e5-5-" + i0 + ",0d4590e5-4",
            d: common_vendor.p({
              title: item.title,
              ["is-link"]: true
            }),
            e: index
          };
        }),
        f: common_vendor.p({
          ["custom-class"]: "shadow-warp",
          border: true,
          clickable: true
        }),
        g: common_vendor.p({
          navLeftArrow: false,
          navLeftText: ""
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-0d4590e5"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=people.js.map
