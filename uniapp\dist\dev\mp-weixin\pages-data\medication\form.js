"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
const store_user = require("../../store/user.js");
const pagesData_medication_medicationOptions = require("./medicationOptions.js");
if (!Array) {
  const _component_NavBar = common_vendor.resolveComponent("NavBar");
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_component_NavBar + _easycom_uni_icons2 + _easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  (_easycom_uni_icons + _easycom_PageLayout)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "medicationForm"
}), {
  __name: "form",
  setup(__props) {
    const userStore = store_user.useUserStore();
    const deleteIds = common_vendor.ref([]);
    const query = common_vendor.ref({});
    common_vendor.onMounted(() => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      query.value = currentPage.options || {};
      if (query.value.mode === "view" && query.value.idList) {
        loadFormData(query.value.idList);
      }
      initMedicationSearchData();
      document.addEventListener("click", () => {
        showSearchResults.value = false;
      });
    });
    const mode = common_vendor.computed(() => query.value.mode || "add");
    common_vendor.computed(() => mode.value === "view");
    const formData = common_vendor.ref({
      date: "",
      medications: []
    });
    const searchKeyword = common_vendor.ref("");
    const showSearchResults = common_vendor.ref(false);
    const selectedSpecificName = common_vendor.ref("");
    const selectedMedicationIndex = common_vendor.ref(0);
    const selectedMedication = common_vendor.ref("");
    common_vendor.ref("");
    const allMedications = common_vendor.ref([]);
    const initMedicationSearchData = () => {
      const medications = [];
      Object.keys(pagesData_medication_medicationOptions.specificMedicationOptions).forEach((category) => {
        pagesData_medication_medicationOptions.specificMedicationOptions[category].forEach((specificName) => {
          medications.push({
            category,
            specificName
          });
        });
      });
      allMedications.value = medications;
    };
    const filteredMedications = common_vendor.computed(() => {
      if (!searchKeyword.value)
        return [];
      return allMedications.value.filter(
        (med) => med.specificName.toLowerCase().includes(searchKeyword.value.toLowerCase())
      ).slice(0, 10);
    });
    const onSearchInput = () => {
      showSearchResults.value = true;
    };
    const selectMedication = (medication) => {
      selectedMedication.value = medication.category;
      selectedSpecificName.value = medication.specificName;
      const categoryIndex = pagesData_medication_medicationOptions.medicationOptions.findIndex((item) => item === medication.category);
      if (categoryIndex !== -1) {
        selectedMedicationIndex.value = categoryIndex;
      }
      showSearchResults.value = false;
      searchKeyword.value = medication.specificName;
    };
    const canAddMedication = common_vendor.computed(() => {
      if (!searchKeyword.value)
        return false;
      return true;
    });
    const addMedication = () => {
      if (!canAddMedication.value)
        return;
      const matchedMedication = allMedications.value.find(
        (med) => med.specificName.toLowerCase() === searchKeyword.value.toLowerCase()
      );
      let medicationName = "";
      let specificName = "";
      let specificMedicationIndex = 0;
      if (matchedMedication) {
        medicationName = matchedMedication.category;
        specificName = matchedMedication.specificName;
        specificMedicationIndex = getSpecificMedicationIndex(medicationName, specificName);
      } else {
        medicationName = "其他";
        specificName = searchKeyword.value;
        specificMedicationIndex = 0;
      }
      const newMedication = {
        name: medicationName,
        frequency: "",
        frequencyIndex: 0,
        dosage: "",
        dosageUnit: "",
        dosageUnitIndex: 0,
        specificName,
        specificMedicationIndex: specificMedicationIndex !== -1 ? specificMedicationIndex : 0
      };
      formData.value.medications.push(newMedication);
      selectedMedication.value = "";
      selectedMedicationIndex.value = 0;
      selectedSpecificName.value = "";
      searchKeyword.value = "";
    };
    const getSpecificMedicationIndex = (category, specificName) => {
      if (!category || !specificName)
        return -1;
      const options = pagesData_medication_medicationOptions.specificMedicationOptions[category] || [];
      return options.findIndex((name) => name === specificName);
    };
    const removeMedication = (index, id) => {
      if (mode.value === "view" && id) {
        deleteIds.value.push(id);
      }
      formData.value.medications.splice(index, 1);
    };
    const loadFormData = (idListStr) => {
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      const idList = idListStr.split(",").filter((id) => id);
      if (idList.length === 0) {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "无法获取药物记录ID",
          icon: "none"
        });
        return;
      }
      console.log("获取药物详情，ID列表:", idList);
      common_vendor.index.request({
        url: `${"https://www.mograine.cn/api"}/patient/getMedicationList`,
        method: "POST",
        data: {
          idList
        },
        success: (res) => {
          var _a;
          console.log("获取药物详情数据:", res.data);
          if (res.data && res.data.code === 200 && res.data.result) {
            const medicationData = res.data.result.records || [];
            formData.value.medications = [];
            medicationData.forEach((item) => {
              let category = "其他";
              let specificName = item.specificName || "";
              for (const cat in pagesData_medication_medicationOptions.specificMedicationOptions) {
                if (pagesData_medication_medicationOptions.specificMedicationOptions[cat].includes(specificName)) {
                  category = cat;
                  break;
                }
              }
              const frequencyIndex = pagesData_medication_medicationOptions.frequencyOptions.findIndex((f) => f === item.frequency);
              const dosageUnitIndex = pagesData_medication_medicationOptions.dosageUnitOptions.findIndex((u) => u === item.dosageUnit);
              formData.value.medications.push({
                id: item.id,
                // 保存药物记录的ID
                name: category,
                specificName,
                frequency: item.frequency || "",
                frequencyIndex: frequencyIndex !== -1 ? frequencyIndex : 0,
                dosage: item.dosage || "",
                dosageUnit: item.dosageUnit || "",
                dosageUnitIndex: dosageUnitIndex !== -1 ? dosageUnitIndex : 0,
                specificMedicationIndex: getSpecificMedicationIndex(category, specificName)
              });
            });
            console.log("处理后的表单数据:", formData.value);
          } else {
            common_vendor.index.showToast({
              title: ((_a = res.data) == null ? void 0 : _a.msg) || "获取数据失败",
              icon: "none"
            });
          }
        },
        fail: (err) => {
          console.error("获取数据失败:", err);
          common_vendor.index.showToast({
            title: "网络异常，请稍后重试",
            icon: "none"
          });
        },
        complete: () => {
          common_vendor.index.hideLoading();
        }
      });
    };
    const submitForm = () => {
      if (formData.value.medications.length === 0) {
        common_vendor.index.showToast({
          title: "请至少添加一种药物",
          icon: "none"
        });
        return;
      }
      for (const med of formData.value.medications) {
        if (!med.specificName) {
          common_vendor.index.showToast({
            title: "请确保药物的具体名称已填写",
            icon: "none"
          });
          return;
        }
      }
      for (const med of formData.value.medications) {
        if (!med.frequency) {
          common_vendor.index.showToast({
            title: "请选择药物的频率",
            icon: "none"
          });
          return;
        }
      }
      for (const med of formData.value.medications) {
        if (!med.dosage) {
          common_vendor.index.showToast({
            title: "请输入药物的剂量",
            icon: "none"
          });
          return;
        }
      }
      for (const med of formData.value.medications) {
        if (!med.dosageUnit) {
          common_vendor.index.showToast({
            title: "请选择药物的剂量单位",
            icon: "none"
          });
          return;
        }
      }
      common_vendor.index.showLoading({
        title: "提交中..."
      });
      const requestData = {
        medications: formData.value.medications,
        userId: userStore.userInfo.userid
      };
      if (mode.value === "view") {
        requestData.updateUserId = userStore.userInfo.userid;
      }
      common_vendor.index.request({
        url: `${"https://www.mograine.cn/api"}/patient/savemedications`,
        method: "POST",
        data: requestData,
        success: (res) => {
          var _a, _b;
          if (mode.value === "view" && deleteIds.value.length > 0) {
            deleteMedications();
          } else {
            common_vendor.index.hideLoading();
            if ((_a = res.data) == null ? void 0 : _a.success) {
              common_vendor.index.showModal({
                title: mode.value === "add" ? "提交成功" : "保存成功",
                showCancel: false,
                success: () => {
                  common_vendor.index.navigateBack();
                }
              });
            } else {
              const errorMsg = ((_b = res.data) == null ? void 0 : _b.message) || "提交失败，未知错误";
              common_vendor.index.showModal({
                title: mode.value === "add" ? "提交失败" : "保存失败",
                content: errorMsg,
                showCancel: false
              });
            }
          }
        },
        fail: (err) => {
          common_vendor.index.hideLoading();
          const errorMsg = err.errMsg || "网络错误，请稍后重试";
          common_vendor.index.showModal({
            title: "提交失败",
            content: errorMsg,
            showCancel: false
          });
        }
      });
    };
    const deleteMedications = () => {
      if (deleteIds.value.length === 0) {
        common_vendor.index.hideLoading();
        common_vendor.index.showModal({
          title: "保存成功",
          showCancel: false,
          success: () => {
            common_vendor.index.navigateBack();
          }
        });
        return;
      }
      common_vendor.index.request({
        url: `${"https://www.mograine.cn/api"}/patient/deletemedication`,
        method: "POST",
        data: {
          deleteId: deleteIds.value
        },
        success: (res) => {
          var _a, _b;
          common_vendor.index.hideLoading();
          if ((_a = res.data) == null ? void 0 : _a.success) {
            common_vendor.index.showModal({
              title: "保存成功",
              showCancel: false,
              success: () => {
                common_vendor.index.navigateBack();
              }
            });
          } else {
            const errorMsg = ((_b = res.data) == null ? void 0 : _b.message) || "删除药物失败，未知错误";
            common_vendor.index.showModal({
              title: "保存部分成功",
              content: "新药物已保存，但删除部分药物失败：" + errorMsg,
              showCancel: false,
              success: () => {
                common_vendor.index.navigateBack();
              }
            });
          }
        },
        fail: (err) => {
          common_vendor.index.hideLoading();
          const errorMsg = err.errMsg || "网络错误，请稍后重试";
          common_vendor.index.showModal({
            title: "保存部分成功",
            content: "新药物已保存，但删除部分药物失败：" + errorMsg,
            showCancel: false,
            success: () => {
              common_vendor.index.navigateBack();
            }
          });
        }
      });
    };
    const onFrequencyChange = (e, medicationIndex) => {
      const index = e.detail.value;
      const medication = formData.value.medications[medicationIndex];
      medication.frequencyIndex = index;
      medication.frequency = pagesData_medication_medicationOptions.frequencyOptions[index];
    };
    const onDosageUnitChange = (e, medicationIndex) => {
      const index = e.detail.value;
      const medication = formData.value.medications[medicationIndex];
      medication.dosageUnitIndex = index;
      medication.dosageUnit = pagesData_medication_medicationOptions.dosageUnitOptions[index];
    };
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.p({
          title: mode.value === "add" ? "新增" : "编辑",
          showBack: true
        }),
        b: mode.value === "add"
      }, mode.value === "add" ? common_vendor.e$1({
        c: common_vendor.o([($event) => searchKeyword.value = $event.detail.value, onSearchInput]),
        d: searchKeyword.value,
        e: filteredMedications.value.length > 0 && showSearchResults.value
      }, filteredMedications.value.length > 0 && showSearchResults.value ? {
        f: common_vendor.f(filteredMedications.value, (item, idx, i0) => {
          return {
            a: common_vendor.t(item.specificName),
            b: common_vendor.t(item.category),
            c: idx,
            d: common_vendor.o(($event) => selectMedication(item), idx)
          };
        })
      } : {}, {
        g: common_vendor.o(addMedication),
        h: !canAddMedication.value
      }) : {}, {
        i: formData.value.medications.length > 0
      }, formData.value.medications.length > 0 ? {
        j: common_vendor.f(formData.value.medications, (med, index, i0) => {
          return common_vendor.e$1({
            a: common_vendor.t(med.name)
          }, mode.value === "add" || mode.value === "view" ? {
            b: "5d191dff-3-" + i0 + ",5d191dff-1",
            c: common_vendor.p({
              type: "trash",
              size: "18",
              color: "#FF0000"
            }),
            d: common_vendor.o(($event) => removeMedication(index, med.id), index)
          } : {}, {
            e: med.specificName,
            f: common_vendor.t(med.frequency || "请选择"),
            g: !med.frequency ? 1 : "",
            h: "5d191dff-4-" + i0 + ",5d191dff-1",
            i: med.frequencyIndex || 0,
            j: common_vendor.o((e) => onFrequencyChange(e, index), index),
            k: med.dosage,
            l: common_vendor.o(($event) => med.dosage = $event.detail.value, index),
            m: common_vendor.t(med.dosageUnit || "单位"),
            n: !med.dosageUnit ? 1 : "",
            o: "5d191dff-5-" + i0 + ",5d191dff-1",
            p: med.dosageUnitIndex || 0,
            q: common_vendor.o((e) => onDosageUnitChange(e, index), index)
          }, mode.value === "add" ? {
            r: common_vendor.o(($event) => removeMedication(index), index)
          } : {}, mode.value === "view" ? {
            s: common_vendor.o(($event) => removeMedication(index, med.id), index)
          } : {}, {
            t: index
          });
        }),
        k: mode.value === "add" || mode.value === "view",
        l: common_vendor.p({
          type: "bottom",
          size: "12",
          color: "#999999"
        }),
        m: common_vendor.unref(pagesData_medication_medicationOptions.frequencyOptions),
        n: common_vendor.p({
          type: "bottom",
          size: "12",
          color: "#999999"
        }),
        o: common_vendor.unref(pagesData_medication_medicationOptions.dosageUnitOptions),
        p: mode.value === "add",
        q: mode.value === "view"
      } : {}, {
        r: common_vendor.t(mode.value === "add" ? "提交" : "保存"),
        s: common_vendor.o(submitForm)
      });
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-5d191dff"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=form.js.map
