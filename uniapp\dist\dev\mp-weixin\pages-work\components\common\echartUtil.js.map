{"version": 3, "file": "echartUtil.js", "sources": ["../../../../../../src/pages-work/components/common/echartUtil.ts"], "sourcesContent": ["import {conditionOptions,colorPanel,tableEllipsis,selectType} from './concants.js'\r\nimport dayjs from 'dayjs';\r\nimport weekday from 'dayjs/plugin/weekday';\r\nimport localeData from 'dayjs/plugin/localeData';\r\nimport { cloneDeep } from 'lodash-es';\r\nimport {isNumber,isString,isNullOrUnDef,isArray} from '@/utils/is'\r\nimport { getEnvBaseUrl } from '@/utils'\r\ndayjs.extend(weekday);\r\ndayjs.extend(localeData);\r\n\r\nexport function parseTime(time, cFormat) {\r\n    if (arguments.length === 0) {\r\n        return null\r\n    }\r\n\r\n    let date\r\n    const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'\r\n\r\n    if (typeof time === 'object') {\r\n        date = time\r\n    } else {\r\n        if (typeof time === 'string' && /^[0-9]+$/.test(time)) {\r\n            time = parseInt(time)\r\n        }else{\r\n            time = new Date(time)\r\n        }\r\n        //update-begin---author:wangshuai ---date:20220802  for：[VUEN-1755]集成 LumenIM：时间先转成字符串在替换------------\r\n        date = new Date(time.toString().replace(/-/g, '/'))\r\n        //update-end---author:wangshuai ---date:20220802  for：[VUEN-1755]集成 LumenIM：时间先转成字符串在替换------------\r\n    }\r\n\r\n    const formatObj = {\r\n        y: date.getFullYear(),\r\n        m: date.getMonth() + 1,\r\n        d: date.getDate(),\r\n        h: date.getHours(),\r\n        i: date.getMinutes(),\r\n        s: date.getSeconds(),\r\n        a: date.getDay(),\r\n    }\r\n\r\n    const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {\r\n        const value = formatObj[key]\r\n        // Note: getDay() returns 0 on Sunday\r\n        if (key === 'a') {\r\n            return ['日', '一', '二', '三', '四', '五', '六'][value]\r\n        }\r\n\r\n        return value.toString().padStart(2, '0')\r\n    })\r\n\r\n    return time_str\r\n}\r\n\r\n/**\r\n * 根据类型获取时间范围\r\n * @param type\r\n */\r\nexport function getRange(type) {\r\n    let date = new Date();\r\n    switch (type) {\r\n        case 'today':\r\n            return nowDay(date);\r\n            break;\r\n        case 'yesterday':\r\n            return preDay(date);\r\n            break;\r\n        case 'befYesterday':\r\n            return preDay(date, 2);\r\n            break;\r\n        case 'tomorrow':\r\n            return nextDay(date);\r\n            break;\r\n        case 'week':\r\n            return nowWeek(date);\r\n            break;\r\n        case 'preWeek':\r\n            return preWeek(date);\r\n            break;\r\n        case 'befPreWeek':\r\n            return preWeek(date, 2);\r\n            break;\r\n        case 'nextWeek':\r\n            return nextWeek(date);\r\n            break;\r\n        case 'month':\r\n            return nowMonth(date);\r\n            break;\r\n        case 'preMonth':\r\n            return preMonth(date);\r\n            break;\r\n        case 'befPreMonth':\r\n            return preMonth(date, 2);\r\n            break;\r\n        case 'nextMonth':\r\n            return nextMonth(date);\r\n            break;\r\n        case 'year':\r\n            return nowYear(date);\r\n            break;\r\n        case 'preYear':\r\n            return preYear(date);\r\n            break;\r\n        case 'befPreYear':\r\n            return preYear(date, 2);\r\n            break;\r\n        case 'nextYear':\r\n            return nextYear(date);\r\n            break;\r\n        default:\r\n            return null;\r\n    }\r\n}\r\n/**\r\n * https://blog.csdn.net/hhhppj/article/details/122432517\r\n * 当前天\r\n * @param date\r\n */\r\nexport function nowDay(date) {\r\n    let startDate = dayjs(date).startOf('days').format('YYYY-MM-DD HH:mm:ss');\r\n    let endDate = dayjs(date).endOf('days').format('YYYY-MM-DD HH:mm:ss');\r\n    return [startDate, endDate];\r\n}\r\n/**\r\n * 当前周\r\n * @param date\r\n */\r\nexport function nowWeek(date) {\r\n    let startDate = dayjs(date).startOf('week').format('YYYY-MM-DD HH:mm:ss');\r\n    let endDate = dayjs(date).endOf('week').format('YYYY-MM-DD HH:mm:ss');\r\n    return [startDate, endDate];\r\n}\r\n\r\n/**\r\n * 当前月\r\n * @param date\r\n */\r\nexport function nowMonth(date) {\r\n    let startDate = dayjs(date).startOf('month').format('YYYY-MM-DD HH:mm:ss');\r\n    let endDate = dayjs(date).endOf('month').format('YYYY-MM-DD HH:mm:ss');\r\n    return [startDate, endDate];\r\n}\r\n\r\n/**\r\n * 当前年\r\n * @param date\r\n */\r\nexport function nowYear(date) {\r\n    let startDate = dayjs(date).startOf('year').format('YYYY-MM-DD HH:mm:ss');\r\n    let endDate = dayjs(date).endOf('year').format('YYYY-MM-DD HH:mm:ss');\r\n    return [startDate, endDate];\r\n}\r\n/**\r\n * 前一天\r\n * @param date\r\n */\r\nfunction preDay(date, interval = 1) {\r\n    let startDate = dayjs(date).subtract(interval, 'days').startOf('days').format('YYYY-MM-DD HH:mm:ss');\r\n    let endDate = dayjs(date).subtract(interval, 'days').endOf('days').format('YYYY-MM-DD HH:mm:ss');\r\n    return [startDate, endDate];\r\n}\r\n\r\n/**\r\n * 后一天\r\n * @param date\r\n */\r\nfunction nextDay(date) {\r\n    let startDate = dayjs(date).add(1, 'days').startOf('days').format('YYYY-MM-DD HH:mm:ss');\r\n    let endDate = dayjs(date).add(1, 'days').endOf('days').format('YYYY-MM-DD HH:mm:ss');\r\n    return [startDate, endDate];\r\n}\r\n\r\n/**\r\n * 前一周\r\n * @param date\r\n */\r\nfunction preWeek(date, interval = 1) {\r\n    let startDate = dayjs(date).subtract(interval, 'week').startOf('week').format('YYYY-MM-DD HH:mm:ss');\r\n    let endDate = dayjs(date).subtract(interval, 'week').endOf('week').format('YYYY-MM-DD HH:mm:ss');\r\n    return [startDate, endDate];\r\n}\r\n\r\n/**\r\n * 后一周\r\n * @param date\r\n */\r\nfunction nextWeek(date) {\r\n    let startDate = dayjs(date).add(1, 'week').startOf('week').format('YYYY-MM-DD HH:mm:ss');\r\n    let endDate = dayjs(date).add(1, 'week').endOf('week').format('YYYY-MM-DD HH:mm:ss');\r\n    return [startDate, endDate];\r\n}\r\n\r\n/**\r\n * 前一月\r\n * @param date\r\n */\r\nfunction preMonth(date, interval = 1) {\r\n    let startDate = dayjs(date).subtract(interval, 'month').startOf('month').format('YYYY-MM-DD HH:mm:ss');\r\n    let endDate = dayjs(date).subtract(interval, 'month').endOf('month').format('YYYY-MM-DD HH:mm:ss');\r\n    return [startDate, endDate];\r\n}\r\n\r\n/**\r\n * 后一月\r\n * @param date\r\n */\r\nfunction nextMonth(date) {\r\n    let startDate = dayjs(date).add(1, 'month').startOf('month').format('YYYY-MM-DD HH:mm:ss');\r\n    let endDate = dayjs(date).add(1, 'month').endOf('month').format('YYYY-MM-DD HH:mm:ss');\r\n    return [startDate, endDate];\r\n}\r\n\r\n/**\r\n * 前一年\r\n * @param date\r\n */\r\nfunction preYear(date, interval = 1) {\r\n    let startDate = dayjs(date).subtract(interval, 'year').startOf('year').format('YYYY-MM-DD HH:mm:ss');\r\n    let endDate = dayjs(date).subtract(interval, 'year').endOf('year').format('YYYY-MM-DD HH:mm:ss');\r\n    return [startDate, endDate];\r\n}\r\n\r\n/**\r\n * 后一年\r\n * @param date\r\n */\r\nfunction nextYear(date) {\r\n    let startDate = dayjs(date).add(1, 'year').startOf('year').format('YYYY-MM-DD HH:mm:ss');\r\n    let endDate = dayjs(date).add(1, 'year').endOf('year').format('YYYY-MM-DD HH:mm:ss');\r\n    return [startDate, endDate];\r\n}\r\n\r\n/**\r\n * mongodb字段翻译\r\n * @param chartData 数值\r\n * @param config 配置\r\n */\r\nexport function handleTranslate(chartData, config) {\r\n    let {\r\n        nameFields,\r\n        typeFields,\r\n        assistTypeFields\r\n    } = config;\r\n    let nameHasArea = nameFields.some(name => name.widgetType == 'area-linkage');\r\n    let typeHasArea = typeFields.some(type => type.widgetType == 'area-linkage');\r\n    let assistTypeHasDate = assistTypeFields.some(type => type.widgetType == 'area-linkage');\r\n    if (nameHasArea || typeHasArea || assistTypeHasDate) {\r\n        chartData.forEach((item) => {\r\n            if (nameHasArea && item.name) {\r\n                let cityName = getAreaTextByCode(item.name);\r\n                item[nameFields[0].fieldName + '_dictVal'] = item.name;\r\n                cityName && (item.name = cityName);\r\n            }\r\n            if (typeHasArea && item.type) {\r\n                let cityName = getAreaTextByCode(item.type);\r\n                item[typeFields[0].fieldName + '_dictVal'] = item.type;\r\n                cityName && (item.type = cityName);\r\n            }\r\n            if (assistTypeHasDate && item.yAxisIndex == '1' && item.type) {\r\n                let cityName = getAreaTextByCode(item.type);\r\n                item[typeFields[0].fieldName + '_dictVal'] = item.type;\r\n                cityName && (item.type = cityName);\r\n            }\r\n        });\r\n    }\r\n    return chartData;\r\n}\r\n\r\n/**\r\n *TODO 根据code获取地区名称\r\n */\r\nfunction getAreaTextByCode(code){\r\nreturn code\r\n}\r\n/**\r\n * 字符串计算\r\n * @param fn\r\n */\r\nexport function calcStr(fn) {\r\n    try {\r\n        const Fn = Function;\r\n        return new Fn('return ' + fn)();\r\n    } catch (e) {\r\n        console.log('calcStr', e)\r\n        return null\r\n    }\r\n}\r\n\r\n/**\r\n * 根据条件获取查询时间范围\r\n * @param queryCondition\r\n */\r\nexport function getTimeRange(queryCondition) {\r\n    let params = [];\r\n    if(queryCondition.customTime && queryCondition.customTime.length==2){\r\n        let startTime = `${queryCondition.customTime[0]} 00:00:00`;\r\n        let endTime = `${queryCondition.customTime[1]} 23:59:59`;\r\n        return [startTime,endTime]\r\n    }\r\n    if (queryCondition.queryRange != 'all') {\r\n        let timeRange = getRange(queryCondition.queryRange);\r\n        if (timeRange && timeRange.length > 0) {\r\n            params[0] = timeRange[0];\r\n            params[1] = timeRange[1];\r\n        }\r\n        return params\r\n    }\r\n    return params\r\n}\r\n\r\n/**\r\n * 获取筛选条件\r\n * @param ele\r\n */\r\nexport function getConditionOptions(ele) {\r\n    if (ele.widgetType == 'pca') {\r\n        return conditionOptions['pca']\r\n    }\r\n    if (ele.widgetType == 'sub-table-design') {\r\n        return conditionOptions['sub-table-design']\r\n    }\r\n    if (ele.widgetType == 'link-record') {\r\n        return conditionOptions['link-record']\r\n    }\r\n    if (ele.widgetType == 'table-dict' && ele.options.queryScope !== 'database') {\r\n        return conditionOptions['link-record']\r\n    }\r\n    if (ele.widgetType == 'time') {\r\n        return conditionOptions['time']\r\n    }\r\n    if (ele.fieldType == 'Date' || ele.fieldType == 'date') {\r\n        return conditionOptions['date']\r\n    }\r\n    if (selectType.includes(ele.widgetType)) {\r\n        return conditionOptions['select']\r\n    }\r\n    if (['int', 'double', 'BigDecimal', 'number'].includes(ele.fieldType) || [\"money\", \"integer\", \"rate\", \"slider\"]\r\n        .includes(ele.widgetType)) {\r\n        return conditionOptions['number']\r\n    }\r\n    return conditionOptions['text']\r\n};\r\n/**\r\n * 组装筛选条件\r\n * @param conditionFields\r\n */\r\nexport function packageConditionFields(conditionFields, formType) {\r\n    let arr = [];\r\n    conditionFields.forEach(fieldItem => {\r\n        let obj = {};\r\n        let condition = fieldItem.condition;\r\n        let fieldName = fieldItem.fieldName;\r\n        let fieldType = fieldItem.fieldType;\r\n        let widgetType = fieldItem.widgetType;\r\n        let fieldValue = fieldItem.fieldValue;\r\n        obj['cType'] = condition;\r\n        obj['field'] = fieldName;\r\n        obj['type'] = fieldType;\r\n        obj['wType'] = widgetType;\r\n        obj['value'] = fieldValue;\r\n        //获取表达式\r\n        let conditionOption = getConditionOptions(fieldItem).filter(item => item.value == condition);\r\n        obj['expression'] = `common_${condition}`;\r\n        //关联记录和子表\r\n        if (['link-record', 'sub-table-design'].includes(widgetType)) {\r\n            obj['code'] = fieldItem.localField || fieldItem.sourceCode;\r\n        }\r\n        //表字典\r\n        if ('table-dict' == widgetType && fieldItem.options.queryScope !== 'database') {\r\n            obj['code'] = fieldName;\r\n        }\r\n        //开关\r\n        if ('switch' == widgetType && !fieldValue) {\r\n            fieldValue = fieldItem.options.defaultValue || fieldItem.options.inactiveValue;\r\n            obj['value'] = fieldValue;\r\n        }\r\n        //判断非空和不为空\r\n        if (condition == '7' || condition == '8') {\r\n            arr.push(obj);\r\n        } else {\r\n            //是否有数值\r\n            let hasValueFlag = fieldValue ? true : false;\r\n            //1.数值条件\r\n            if ([\"int\", \"integer\", \"double\", \"BigDecimal\", \"number\"].includes(fieldType)) {\r\n                obj['type'] = \"number\";\r\n                //1.1数值条件范围查询\r\n                if (condition == '9' || condition == '10') {\r\n                    obj['begin'] = fieldItem.beginValue ? Number(fieldItem.beginValue) : fieldItem.beginValue;\r\n                    obj['end'] = fieldItem.endValue ? Number(fieldItem.endValue) : fieldItem.endValue;\r\n                    hasValueFlag = (obj['begin'] || obj['begin'] == 0) && (obj['end'] || obj['end'] == 0) ?\r\n                        true : false;\r\n                } else {\r\n                    obj['value'] = Number(fieldValue);\r\n                    hasValueFlag = obj['value'] || obj['value'] == 0 ? true : false;\r\n                }\r\n            } else if (fieldItem.widgetType == 'time') {\r\n                obj['type'] = \"time\";\r\n                if (condition == '9' || condition == '10') {\r\n                    obj['begin'] = fieldItem.beginValue ? parseTime(fieldItem.beginValue,'HH:mm:ss'): null;\r\n                    obj['end'] = fieldItem.endValue ? parseTime(fieldItem.endValue,'HH:mm:ss') : null;\r\n                    hasValueFlag = (!obj['begin'] || !obj['end']) ? false : true\r\n                } else {\r\n                    obj['value'] = fieldValue ? fieldValue.format('HH:mm:ss') : null;\r\n                    hasValueFlag = obj['value'] ? true : false;\r\n                }\r\n            } else if (fieldItem.widgetType == 'pca' || fieldItem.widgetType == 'area-linkage') {\r\n                obj['type'] = \"pca\";\r\n                obj['value'] = fieldValue && isArray(fieldValue) ? fieldValue[fieldValue.length - 1] : null;\r\n                hasValueFlag = obj['value'] ? true : false;\r\n            } else if (fieldType == 'Date' || fieldType == 'date') {\r\n                //2.日期条件\r\n                obj['type'] = \"date\";\r\n                //2.1 日期条件范围查询\r\n                if (condition == '9' || condition == '10') {\r\n                    if (fieldItem.options && fieldItem.options.type == 'datetime') {\r\n                        //日期时间\r\n                        obj['begin'] = fieldItem.fieldValue[0] ? fieldItem.fieldValue[0] : null;\r\n                        obj['end'] = fieldItem.fieldValue[1] ? fieldItem.fieldValue[1] : null;\r\n                    } else {\r\n                        //非日期时间\r\n                        if (fieldItem.fieldValue[0]) {\r\n                            const timeBegin = new Date(parseTime(fieldItem.fieldValue[0],`YYYY-MM-DD HH:mm:ss`));\r\n                            timeBegin.setHours(0, 0, 0, 0);\r\n                            obj['begin'] = parseTime(timeBegin.getTime(),`YYYY-MM-DD HH:mm:ss`);\r\n                        }\r\n                        if (fieldItem.fieldValue[1]) {\r\n                            const timeEnd = new Date(parseTime(fieldItem.fieldValue[1],`YYYY-MM-DD HH:mm:ss`));\r\n                            timeEnd.setHours(23, 59, 59, 999);\r\n                            obj['end'] = parseTime(timeEnd.getTime(),`YYYY-MM-DD HH:mm:ss`);\r\n                        }\r\n                    }\r\n                    hasValueFlag = (!obj['begin'] || !obj['end']) ? false : true;\r\n                } else {\r\n                    if (fieldItem.timeCondition == 'customTime') {\r\n                        if (fieldValue) {\r\n                            const timeBegin = new Date(fieldValue);\r\n                            timeBegin.setHours(0, 0, 0, 0);\r\n                            const timeEnd = new Date(fieldValue);\r\n                            timeEnd.setHours(23, 59, 59, 999);\r\n                            obj['begin'] = parseTime(timeBegin.getTime(),`YYYY-MM-DD HH:mm:ss`);\r\n                            obj['end'] = parseTime(timeEnd.getTime(),`YYYY-MM-DD HH:mm:ss`);\r\n                            hasValueFlag = (!obj['begin'] || !obj['end']) ? false : true;\r\n                        }\r\n                    } else {\r\n                        let range = getRange(fieldItem.timeCondition);\r\n                        obj['value'] = range && range.length > 0 ? range[0] : null;\r\n                        obj['begin'] = range && range.length > 0 ? range[0] : null;\r\n                        obj['end'] = range && range.length > 1 ? range[1] : null;\r\n                        if (condition == '1' || condition == '2') {\r\n                            hasValueFlag = (!obj['begin'] || !obj['end']) ? false : true;\r\n                        }\r\n                        //早于等于\r\n                        if (condition == '4' || condition == '6') {\r\n                            obj['value'] = range && range.length > 0 ? range[0] : null;\r\n                            hasValueFlag = obj['value'] ? true : false;\r\n                        }\r\n                        //晚于等于\r\n                        if (condition == '3' || condition == '5') {\r\n                            obj['value'] = range && range.length > 0 ? range[1] : null;\r\n                            hasValueFlag = obj['value'] ? true : false;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            //下拉多选\r\n            if (selectType.includes(fieldItem.widgetType)) {\r\n                obj['type'] = \"select\";\r\n                obj['value'] = null;\r\n                if (fieldValue) {\r\n                    if (isArray(fieldValue)) {\r\n                        obj['value'] = fieldValue.length > 0 ? JSON.stringify(fieldValue) : null;\r\n                    } else {\r\n                        obj['value'] = JSON.stringify(fieldValue.split(\",\"));\r\n                    }\r\n                }\r\n                hasValueFlag = obj['value'] ? true : false;\r\n            }\r\n            //3.设置条件表达式\r\n            //表达式存在的情况下，才当作查询条件\r\n            if (conditionOption && conditionOption.length > 0) {\r\n                if (['string', 'text', 'Text'].includes(obj['type']) && ['4', '5', '6'].includes(condition)) {\r\n                    obj['expression'] = `text_${condition}`;\r\n                    //like条件设置查询值的前后缀\r\n                    if (formType && formType == 'design') {\r\n                        obj['value'] = `${fieldValue}`;\r\n                    } else {\r\n                        obj['value'] = conditionOption[0]['value'] == '5' ? `${fieldValue}%` : `%${fieldValue}`;\r\n                    }\r\n                }\r\n                if (obj['type'] == 'select' && ['3', '4'].includes(condition)) {\r\n                    obj['expression'] = `select_${condition}`;\r\n                }\r\n                hasValueFlag && arr.push(obj);\r\n            }\r\n        }\r\n    });\r\n    return arr\r\n}\r\n/**\r\n * 组装筛选条件\r\n * @param conditionFields\r\n */\r\nexport function packageConditionQuery(conditionFields) {\r\n    let arr = [];\r\n    conditionFields.forEach(fieldItem => {\r\n        let obj = {};\r\n        let condition = fieldItem.condition;\r\n        let fieldName = fieldItem.fieldName;\r\n        let fieldType = fieldItem.fieldType;\r\n        let widgetType = fieldItem.widgetType;\r\n        let fieldValue = fieldItem.fieldValue;\r\n        obj['cType'] = condition;\r\n        obj['field'] = fieldName;\r\n        obj['type'] = fieldType;\r\n        obj['wType'] = widgetType;\r\n        obj['value'] = fieldValue;\r\n        //获取表达式\r\n        let conditionOption = getConditionOptions(fieldItem).filter(item => item.value == condition);\r\n        obj['expression'] = `common_${condition}`;\r\n        //关联记录和子表\r\n        if (['link-record', 'sub-table-design'].includes(widgetType)) {\r\n            obj['code'] = fieldItem.localField || fieldItem.sourceCode;\r\n        }\r\n        //表字典\r\n        if ('table-dict' == widgetType && fieldItem.options.queryScope !== 'database') {\r\n            obj['code'] = fieldName;\r\n        }\r\n        //开关\r\n        if ('switch' == widgetType && !fieldValue) {\r\n            obj['value'] = fieldValue;\r\n        }\r\n        //是否有数值\r\n        let hasValueFlag = fieldValue ? true : false;\r\n        //1.数值条件\r\n        if ([\"int\", \"integer\", \"double\", \"BigDecimal\", \"number\"].includes(fieldType)) {\r\n            obj['type'] = \"number\";\r\n            //1.1数值条件范围查询\r\n            if (condition == '9') {\r\n                obj['begin'] = fieldItem.beginValue ? Number(fieldItem.beginValue) : fieldItem.beginValue;\r\n                obj['end'] = fieldItem.endValue ? Number(fieldItem.endValue) : fieldItem.endValue;\r\n                hasValueFlag = (obj['begin'] || obj['begin'] == 0) && (obj['end'] || obj['end'] == 0) ?\r\n                    true : false;\r\n            } else {\r\n                obj['value'] = fieldValue?Number(fieldValue):null;\r\n                hasValueFlag = obj['value'] || obj['value'] == 0 ? true : false;\r\n            }\r\n        } else if (fieldItem.widgetType == 'time') {\r\n            obj['type'] = \"time\";\r\n            if (condition == '9') {\r\n                obj['begin'] = fieldItem.beginValue ? parseTime(fieldItem.beginValue,'HH:mm:ss'): null;\r\n                obj['end'] = fieldItem.endValue ? parseTime(fieldItem.endValue,'HH:mm:ss') : null;\r\n                hasValueFlag = (!obj['begin'] || !obj['end']) ? false : true\r\n            } else {\r\n                obj['value'] = fieldValue;\r\n                hasValueFlag = obj['value'] ? true : false;\r\n            }\r\n        } else if (fieldItem.widgetType == 'pca' || fieldItem.widgetType == 'area-linkage') {\r\n            obj['type'] = \"pca\";\r\n            obj['value'] = fieldValue && isArray(fieldValue) ? fieldValue[fieldValue.length - 1] : null;\r\n            hasValueFlag = obj['value'] ? true : false;\r\n        } else if (fieldType == 'Date' || fieldType == 'date') {\r\n            //2.日期条件\r\n            obj['type'] = \"date\";\r\n            //2.1 日期条件范围查询\r\n            if (condition == '9') {\r\n                if (fieldItem.options && fieldItem.options.type == 'datetime') {\r\n                    //日期时间\r\n                    obj['begin'] = fieldItem.fieldValue[0] ? fieldItem.fieldValue[0] : null;\r\n                    obj['end'] = fieldItem.fieldValue[1] ? fieldItem.fieldValue[1] : null;\r\n                } else {\r\n                    //非日期时间\r\n                    if (fieldItem.beginValue) {\r\n                        const timeBegin = new Date(fieldItem.beginValue);\r\n                        timeBegin.setHours(0, 0, 0, 0);\r\n                        obj['begin'] = dayjs(timeBegin.getTime()).format(`YYYY-MM-DD HH:mm:ss`);\r\n                    }\r\n                    if (fieldItem.endValue) {\r\n                        const timeEnd = new Date(fieldItem.endValue);\r\n                        timeEnd.setHours(23, 59, 59, 999);\r\n                        obj['end'] = dayjs(timeEnd.getTime()).format(`YYYY-MM-DD HH:mm:ss`);\r\n                    }\r\n                }\r\n                hasValueFlag = (!obj['begin'] || !obj['end']) ? false : true;\r\n            } else {\r\n                if (fieldValue) {\r\n                    const timeBegin = new Date(fieldValue);\r\n                    timeBegin.setHours(0, 0, 0, 0);\r\n                    const timeEnd = new Date(fieldValue);\r\n                    timeEnd.setHours(23, 59, 59, 999);\r\n                    obj['begin'] = dayjs(timeBegin.getTime()).format(`YYYY-MM-DD HH:mm:ss`);\r\n                    obj['end'] = dayjs(timeEnd.getTime()).format(`YYYY-MM-DD HH:mm:ss`);\r\n                    hasValueFlag = (!obj['begin'] || !obj['end']) ? false : true;\r\n                }\r\n            }\r\n        }\r\n        //下拉多选\r\n        if (selectType.includes(fieldItem.widgetType)) {\r\n            obj['type'] = \"select\";\r\n            obj['value'] = null;\r\n            if (fieldValue) {\r\n                if (isArray(fieldValue)) {\r\n                    obj['value'] = fieldValue.length > 0 ? JSON.stringify(fieldValue) : null;\r\n                } else {\r\n                    obj['value'] = JSON.stringify(fieldValue.split(\",\"));\r\n                }\r\n            }\r\n            hasValueFlag = obj['value'] ? true : false;\r\n        }\r\n        //3.设置条件表达式\r\n        //表达式存在的情况下，才当作查询条件\r\n        if (conditionOption && conditionOption.length > 0) {\r\n            if (obj['type'] == 'select' && ['3', '4'].includes(condition)) {\r\n                obj['expression'] = `select_${condition}`;\r\n            }\r\n            hasValueFlag && arr.push(obj);\r\n        }\r\n    });\r\n    console.log(\"arr****************>>>>>>\",arr)\r\n    return arr\r\n}\r\n/**\r\n * 组装查询数据\r\n * @param config\r\n * @param params\r\n */\r\nexport function packageParams(config,params) {\r\n    console.log(\"config\",config)\r\n    console.log(\"params\",params)\r\n    //=====================================\r\n    let sorts = {};\r\n    if(config.sorts && config.sorts.name && config.sorts.name.indexOf(\"null\") == -1){\r\n        let lastIndexOf = config.sorts.name.lastIndexOf(\"_\");\r\n        sorts[\"name\"] = config.sorts.name.substring(0,lastIndexOf);\r\n        sorts[\"order\"] = config.sorts.name.substring(lastIndexOf+1);\r\n        sorts[\"type\"] = config.sorts.type;\r\n    }\r\n    //=============筛选条件begin============\r\n    let conditionFields = [];\r\n    if (config && config.filter && config.filter.conditionFields && config.filter.conditionFields.length > 0) {\r\n        conditionFields = packageConditionFields(config.filter.conditionFields, config.formType);\r\n    }\r\n    let filter = {\r\n        field: config.filter.queryField,\r\n        range: getTimeRange(config.filter),\r\n        mode: config.filter.conditionMode,\r\n        fields: conditionFields\r\n    };\r\n    //=============筛选条件end=====================\r\n\r\n    //===========查询筛选条件begin=================\r\n    if(params){\r\n        if(params.type && params.type == 'fieldQuery'){\r\n            //2.设置查询字段\r\n            if(params.conditionFields && params.conditionFields.length>0){\r\n                filter['mode'] = \"and\";\r\n                filter['fields'] = packageConditionQuery(params.conditionFields);\r\n            }\r\n            console.log(\"筛选器筛选条件filter:::::>>>>>\",filter)\r\n        }\r\n    }\r\n    //===========查询筛选条件end=================\r\n    return {\r\n        sorts,\r\n        filter\r\n    }\r\n}\r\n/**\r\n * 格式化时间\r\n * @param dateStr\r\n * @param fmt\r\n */\r\nexport function formatDate(dateStr, fmt = 'YYYY-MM-D hh:mm:ss') {\r\n    fmt = fmt.toUpperCase();\r\n    if (fmt.indexOf('SS') != -1) {\r\n        fmt = fmt.replace('SS', 'ss')\r\n    }\r\n    return dayjs(dateStr).format(fmt);\r\n}\r\n/**\r\n * mongodb类型格式化时间戳\r\n * @param chartData 数值\r\n * @param config 配置\r\n */\r\nexport function formatTimestamp(chartData, config) {\r\n    let nameField = config.nameFields;\r\n    let typeField = config.typeFields;\r\n    let assistType = config.assistTypeFields;\r\n    let nameHasDate = nameField.some(name => name.widgetType == 'date');\r\n    let typeHasDate = typeField.some(type => type.widgetType == 'date');\r\n    let assistTypeHasDate = assistType.some(type => type.widgetType == 'date');\r\n    if (nameHasDate || typeHasDate || assistTypeHasDate) {\r\n        chartData.forEach((item) => {\r\n            if (nameHasDate) {\r\n                let defVal = parseInt(item.name);\r\n                let nameFormat = nameField[0]?.options?.format || 'YYYY-MM-DD';\r\n                item.name = item.name ? formatDate(defVal, nameFormat) : \"\";\r\n                item[nameField[0].fieldName + '_dictVal'] = defVal;\r\n            }\r\n            if (typeHasDate) {\r\n                let defVal = parseInt(item.type);\r\n                let typeFormat = typeField[0]?.options?.format || 'YYYY-MM-DD';\r\n                item.type = item.type ? formatDate(defVal, typeFormat) : \"\";\r\n                item[typeField[0].fieldName + '_dictVal'] = defVal;\r\n            }\r\n            if (assistTypeHasDate && item.yAxisIndex == '1' && item.type) {\r\n                let defVal = parseInt(item.type);\r\n                let typeFormat = assistType[0]?.options?.format || 'YYYY-MM-DD';\r\n                item.type = item.type ? formatDate(defVal, typeFormat) : \"\";\r\n                item[typeField[0].fieldName + '_dictVal'] = defVal;\r\n            }\r\n        });\r\n    }\r\n    return chartData;\r\n}\r\n/**\r\n * 处理计算字段\r\n * @param valueFields\r\n */\r\nexport function handleCalcFields(arr, valueFields, assistYFields) {\r\n    let valField = valueFields.filter((item) => item.widgetType == 'calcVal');\r\n    let assistYField = assistYFields.filter((item) => item.widgetType == 'calcVal');\r\n    // @ts-ignore\r\n    let calcField = [...new Set([...valField, ...assistYField])];\r\n    if (calcField && calcField.length > 0) {\r\n        calcField.forEach((field) => {\r\n            let fieldName = field.fieldName;\r\n            arr.forEach((item) => {\r\n                if (Object.keys(item).includes(fieldName)) {\r\n                    let formulaStr = fieldName.replace(/\\$(.*?)\\$/g, function(str) {\r\n                        if (str) {\r\n                            let field = str.replace(/\\$/g, '');\r\n                            if (field && isString(field) && !isNullOrUnDef(item[field])) {\r\n                                return item[field];\r\n                            }\r\n                        }\r\n                        return str;\r\n                    });\r\n                    item['value'] = keepTwoDecimals(calcStr(formulaStr));\r\n                }\r\n            });\r\n        });\r\n    }\r\n    return arr;\r\n}\r\n/**\r\n * 处理计算字段\r\n * @param valueFields\r\n */\r\nexport function handleTableCalcFields(arr,valueFields) {\r\n    let calcField = valueFields.filter((item) => item.widgetType == 'calcVal');\r\n    if (calcField && calcField.length > 0) {\r\n        calcField.forEach((field) => {\r\n            let fieldName = field.fieldName;\r\n            arr.forEach((item) => {\r\n                if (Object.keys(item).includes(fieldName)) {\r\n                    let formulaStr = fieldName.replace(/\\$(.*?)\\$/g, function (str) {\r\n                        if (str) {\r\n                            let field = str.replace(/\\$/g, '');\r\n                            if (field && isString(field) && !isNullOrUnDef(item[field])) {\r\n                                return item[field];\r\n                            }\r\n                        }\r\n                        return str;\r\n                    });\r\n                    item['value'] = keepTwoDecimals(calcStr(formulaStr));\r\n                }\r\n            });\r\n        });\r\n    }\r\n    return arr;\r\n}\r\n/**\r\n * mongodb类型格式化时间戳\r\n * @param chartData 数值\r\n * @param config 配置\r\n */\r\nexport function handleDateFields(chartData,config) {\r\n    let nameField = config.nameFields;\r\n    let typeField = config.typeFields;\r\n    let assistType = config.assistTypeFields;\r\n    let nameHasDate = nameField.some(name=> name.widgetType== 'date');\r\n    let typeHasDate = typeField.some(type=> type.widgetType== 'date');\r\n    let assistTypeHasDate = assistType.some(type=> type.widgetType== 'date');\r\n    if (nameHasDate || typeHasDate || assistTypeHasDate) {\r\n        chartData.forEach((item) => {\r\n            if (nameHasDate && !item[nameField[0].fieldName+'_dictVal']) {\r\n                let defVal = item.name.indexOf(\"-\")>=0?item.name:parseInt(item.name);\r\n                let nameFormat = nameField[0].options && nameField[0].options.format?nameField[0].options.format:'YYYY-MM-DD'\r\n                item.name = item.name ? formatDate(defVal, nameFormat) : \"\";\r\n            }\r\n            if (typeHasDate && !item[typeField[0].fieldName+'_dictVal']) {\r\n                let defVal = item.type.indexOf(\"-\")>=0?item.type:parseInt(item.type);\r\n                let typeFormat = typeField[0].options && typeField[0].options.format?typeField[0].options.format:'YYYY-MM-DD'\r\n                item.type = item.type ? formatDate(defVal, typeFormat) : \"\";\r\n            }\r\n            if (assistTypeHasDate && !item[assistType[0].fieldName+'_dictVal'] && item.yAxisIndex == '1' && item.type) {\r\n                let defVal = item.type.indexOf(\"-\")>=0?item.type:parseInt(item.type);\r\n                let typeFormat = assistType[0].options && assistType[0].options.format?assistType[0].options.format:'YYYY-MM-DD'\r\n                item.type = item.type ? formatDate(defVal, typeFormat) : \"\";\r\n            }\r\n        });\r\n    }\r\n    return chartData;\r\n}\r\n/**\r\n * 保留两位小数\r\n * @param fn\r\n */\r\nexport function keepTwoDecimals(total) {\r\n    if (total) {\r\n        let dot = String(total).indexOf(\".\");\r\n        if (dot != -1) {\r\n            let dotCnt = String(total).substring(dot + 1, total.length);\r\n            if (dotCnt.length > 2) {\r\n                total = total.toFixed(2);\r\n            }\r\n        }\r\n    }\r\n    return total;\r\n}\r\n/**\r\n * 深度合并代码，思路来自 zepto.js 源代码\r\n * 切记不要对象递归引用，否则会陷入递归跳不出来，导致堆栈溢出\r\n * 作用是会合并 target 和 other 对应位置的值，冲突的会保留 target 的值\r\n */\r\nexport function deepMerge(target,other){\r\n    const targetToString=Object.prototype.toString.call(target);\r\n    const otherToString=Object.prototype.toString.call(target);\r\n    if(targetToString===\"[object Object]\" && otherToString===\"[object Object]\"){\r\n        for(let [key,val] of Object.entries(other)){\r\n            if(!target[key]){\r\n                target[key]=val;\r\n            }else{\r\n                target[key]=deepMerge(target[key],val);\r\n            }\r\n        }\r\n    }else if(targetToString===\"[object Array]\" && otherToString===\"[object Array]\"){\r\n        for(let [key,val] of Object.entries(other)){\r\n            if(target[key]){\r\n                target[key]=deepMerge(target[key],val);\r\n            }else{\r\n                target.push(val);\r\n            }\r\n        }\r\n    }\r\n    return target;\r\n}\r\n/**\r\n * 根据配置获取颜色\r\n * @param index\r\n */\r\nexport function getCustomColor(customColor) {\r\n    let colors = colorPanel.classic.map(color=>({color}));\r\n    return customColor ? customColor : colors;\r\n}\r\n/**\r\n * 计算多数据柱形图获取dataset\r\n */\r\nexport function getDataSet(chartData,config) {\r\n    let dataObj = { dimensions: [], source: [] };\r\n    let dataList = [];\r\n    //获取系列\r\n    // @ts-ignore\r\n    let dimensions = ['stack', ...new Set(chartData.map((item) => item['type']))];\r\n    //获取name集合\r\n    // @ts-ignore\r\n    let nameArr = [...new Set(chartData.map((item) => item['name']))];\r\n    if(config.dataFilterNum && isNumber(config.dataFilterNum)){\r\n        nameArr = nameArr.slice(0,config.dataFilterNum)\r\n    }\r\n    //遍历name获取value\r\n    nameArr.forEach((name) => {\r\n        //筛选出指定name的对象集合\r\n        let arr = chartData.filter((item) => item['name'] == name);\r\n        //获取对象集合的value\r\n        let valueList = arr.map((item) => item['value']);\r\n        //首位置存放的是当前name\r\n        valueList.unshift(name);\r\n        dataList.push(valueList);\r\n    });\r\n    dataObj.dimensions = dimensions;\r\n    dataObj.source = dataList;\r\n    return dataObj;\r\n}\r\n/**\r\n *计算显示单位\r\n * @param value 数值\r\n * @param calcConfig 计算配置\r\n */\r\nexport function calcUnit(value, calcConfig,defDecimal=0) {\r\n    let numberLevel = calcConfig.numberLevel;\r\n    let decimal = calcConfig.decimal||defDecimal;\r\n    let mapping = {\"0\":1,\"1\":100, \"2\":1000, \"3\":1/1000, \"4\":1/10000, \"5\":1/1000000};\r\n    let multiple = numberLevel?mapping[numberLevel]:1;\r\n    value = (value * multiple).toFixed(decimal);\r\n    return value\r\n}\r\n/**\r\n * 计算总计\r\n * @param series\r\n * @param summaryConfig\r\n */\r\nexport function calcTotal(summaryConfig,rawData,config){\r\n    if(rawData && rawData.length>0){\r\n        let showField = summaryConfig.showField;\r\n        // 找单位\r\n        const findItem = config.valueFields?.find(item => item.fieldName === showField);\r\n        const unitText = findItem?.options?.unitText || \"\";\r\n        let showName = summaryConfig.showName || '总计';\r\n        let totalType = summaryConfig.totalType || 'sum';\r\n        let valueField = showField=='all'?'value':showField;\r\n        // update-begin-author:liaozhiyang date:2023-12-1 for:【QQYUN-7911】解决总计是0\r\n        let valueArr = rawData.map(item=> {\r\n            if(isNumber(item[valueField])){\r\n                return item[valueField];\r\n            }else{\r\n                const value = Number(item[valueField].replace(unitText,\"\"));\r\n                if(Number.isNaN(value)){\r\n                    return 0;\r\n                }else{\r\n                    return value;\r\n                }\r\n            }\r\n        });\r\n        // update-end-author:liaozhiyang date:2024-01-17 for:【QQYUN-7911】解决总计是0\r\n        let total = 0;\r\n        if(valueArr.length>0){\r\n            if(totalType=='sum'){\r\n                total = valueArr.reduce((prev,cur)=>prev + cur,0);\r\n            }else if(totalType=='max'){\r\n                total = Math.max.apply(Math,valueArr);\r\n            }else if(totalType=='min'){\r\n                total = Math.min.apply(Math,valueArr);\r\n            }else if(totalType=='average'){\r\n                // @ts-ignore\r\n                total = ((valueArr.reduce((prev,cur)=>prev + cur,0))/ valueArr.length).toFixed(2);\r\n            }\r\n        }\r\n        //TODO 换算单位和数值级别\r\n        return `${showName}: ${keepTwoDecimals(total)}`;\r\n    }\r\n    return ''\r\n}\r\n/**\r\n * 处理总计和显示单位\r\n * @param {Object} compName\r\n * @param {Object} chartOption\r\n * @param {Object} config\r\n * @param {Object} chartData\r\n */\r\nexport function\thandleTotalAndUnit(compName,chartOption,config,chartData){\r\n    //1.获取到label配置项\r\n    if (config.compStyleConfig) {\r\n        //显示单位配置\r\n        let showUnitConfig = config.compStyleConfig.showUnit;\r\n        let unit = showUnitConfig.unit?showUnitConfig.unit:'';//单位\r\n        let numberLevel = showUnitConfig.numberLevel?showUnitConfig.numberLevel:'';//数值数量级\r\n        chartOption.series.forEach((item) => {\r\n            if(item.yAxisIndex == '1'){\r\n                showUnitConfig = config.compStyleConfig.assist.showUnit;\r\n            }\r\n            //数值显示位置\r\n            let labelPosition = compName.indexOf('Pie')!=-1?chartOption.pieLabelPosition:\"top\";\r\n            //数值配置\r\n            let labelConfig;\r\n            switch(compName){\r\n                case \"JColorGauge\":\r\n                case \"JGauge\":\r\n                    delete item.detail.formatter;\r\n                    labelConfig = {\r\n                        detail:{\r\n                            formatter: (value) => {\r\n                                let showLabel = showUnitConfig.position == 'suffix' ? `${calcUnit(value, showUnitConfig)}${unit}` : `${unit}${calcUnit(value, showUnitConfig)}`;\r\n                                return showLabel;\r\n                            }\r\n                        }\r\n                    }\r\n                    break;\r\n                default:\r\n                    labelConfig = {\r\n                        label: {\r\n                            position:compName.indexOf('Funnel')>=0?\"inside\":labelPosition,\r\n                            formatter: (params) => {\r\n                                let type = params.seriesType;\r\n                                let showLabel = \"\";\r\n                                if(type == 'pie'){\r\n                                    showLabel = (`${params.name || '空'}:`);\r\n                                }\r\n                                if(type == 'funnel'){\r\n                                    showLabel = (`${params.name || '空'}: `);\r\n                                }\r\n                                let value = 0;\r\n                                if(Array.isArray(params.value)){\r\n                                    value = type == 'scatter'?params.value[1]:params.value[params.seriesIndex + 1];\r\n                                }else{\r\n                                    value = params.value;\r\n                                }\r\n                                //计算显示数值和添加前后缀\r\n                                showLabel += showUnitConfig.position == 'suffix' ? `${calcUnit(value, showUnitConfig)}${unit}` : `${unit}${calcUnit(value, showUnitConfig)}`;\r\n                                return showLabel;\r\n                            },\r\n                        }\r\n                    }\r\n            };\r\n            deepMerge(item, {...labelConfig});\r\n        });\r\n        //显示总计配置\r\n        let summaryConfig = config.compStyleConfig.summary;\r\n        if(summaryConfig.showTotal && chartData && chartData.length>0){\r\n            //左y轴\r\n            let leftData = chartData.filter(item=>(!item.yAxisIndex || item.yAxisIndex == '0'));\r\n            let totalTitle = summaryConfig.showY?calcTotal(summaryConfig,leftData,config):'';\r\n            Object.assign(chartOption.title, {text:totalTitle});\r\n        }\r\n    }\r\n\r\n    //其他配置设置\r\n    chartOption = otherConfig(chartOption)\r\n    return chartOption\r\n}\r\n/**\r\n * 处理X轴和Y轴文字显示\r\n * @param {String} compName\r\n * @param {Object} chartOption\r\n * @param {Object} config\r\n * @param {Object} chartData\r\n */\r\nexport function disposeGridLayout(compName, chartOption, config, chartData) {\r\n    // 柱形图（JBar[基础柱形图]、JStackBar[堆叠柱形图]、JMultipleBar[多数据对比柱形图]、JNegativeBar[正负条形图]）\r\n    // 折线图 (JLine[基础折线图]、JMultipleLine[多数据对比折线图]、DoubleLineBar[双轴图])\r\n    // 散点图（JScatter[基础散点图]、JBubble[气泡图]）\r\n    chartOption.grid = {containLabel: true,top: 30,bottom: 60,left: 5,right: 5};\r\n    const {xAxis, yAxis, series} = chartOption\r\n    if(xAxis){\r\n        const {name, nameTextStyle = {}} = xAxis;\r\n        if(name){\r\n            chartOption.grid.top += 30;\r\n        }\r\n    };\r\n    if(yAxis){\r\n        const {name, nameTextStyle = {} } = yAxis;\r\n        if(name){\r\n            const {fontSize = 12} = nameTextStyle;\r\n            //15是轴到文字的距离\r\n            chartOption.grid.right += name.length * (fontSize + 1) + 15;\r\n        };\r\n    };\r\n    if(chartOption.title?.show){\r\n        // 【QQYUN-7911】标题和图表会重叠\r\n        // 有标题\r\n        const {textStyle = {} } = chartOption.title;\r\n        const { fontSize = 18 } = textStyle;\r\n        chartOption.grid.top += fontSize;\r\n        if(chartOption.grid.top > 200){\r\n            chartOption.grid.top = 30;\r\n        }\r\n    }\r\n    // series大于1说明有分组即有图例\r\n    if(series.length <=1){\r\n        chartOption.grid.bottom = 10;\r\n        // 【QQYUN-7911】安卓底部间距小\r\n        // #ifdef APP\r\n        chartOption.grid.bottom = 30;\r\n        // #endif\r\n    }\r\n    console.log('---chartOption--',chartOption);\r\n    return chartOption;\r\n}\r\n/**\r\n * 配置设置\r\n * @param {Object} chartOption\r\n */\r\nfunction otherConfig(chartOption){\r\n    //设置内边距\r\n    chartOption.grid = {\r\n        left:30,\r\n        right:30,\r\n        top:30,\r\n        bottom:60\r\n    }\r\n    //设置图例位置\r\n    chartOption.legend = {\r\n        bottom:15\r\n    }\r\n    //设置提示层级\r\n    chartOption.tooltip && (chartOption.tooltip.extraCssText ='z-index:9');\r\n    return chartOption\r\n}\r\n/**\r\n * 获取随机颜色\r\n * @param index\r\n */\r\nexport function getRandomColor(index) {\r\n    let naturalColors = ['rgb(133, 202, 205)','rgb(167, 214, 118)','rgb(254, 225, 89)','rgb(251, 199, 142)','rgb(239, 145, 139)','rgb(169, 181, 255)','rgb(231, 218, 202)','rgb(252, 128, 58)','rgb(254, 161, 172)','rgb(194, 163, 205)']\r\n    let colors = ['rgb(100, 181, 246)','rgb(77, 182, 172)','rgb(255, 183, 77)','rgb(229, 115, 115)','rgb(149, 117, 205)','rgb(161, 136, 127)','rgb(144, 164, 174)','rgb(77, 208, 225)','rgb(129, 199, 132)','rgb(255, 138, 101)',...naturalColors]\r\n    return index && index<19?colors[index]: colors[Math.floor((Math.random()*(colors.length-1)))];\r\n}\r\n\r\n/**\r\n * 处理参数\r\n */\r\nexport function handleParam(config) {\r\n    let paramList = config.paramOption;\r\n    let url = cloneDeep(config.dataSetApi);\r\n    //获得原数据Map\r\n    let dataMap = {};\r\n    if (paramList && paramList.length > 0) {\r\n        paramList.forEach((item) => {\r\n            dataMap[item.label] = item.defaultVal || '';\r\n        });\r\n    }\r\n    let reg = /\\$\\{[^}]*\\}/g;\r\n    //包含参数则去处理参数配置\r\n    if (reg.test(config.dataSetApi)) {\r\n        url = url.split('?')[0];\r\n    }\r\n    return { dataMap, url };\r\n}\r\n\r\n/**\r\n * 获取地图数据地理坐标信息\r\n */\r\nexport function getGeoCoordMap(mapDataJson) {\r\n    if(mapDataJson.features){\r\n        /*获取地图数据*/\r\n        let mapFeatures = mapDataJson.features;\r\n        //地理坐标信息\r\n        let geoCoordMap = {};\r\n        mapFeatures.forEach((v) => {\r\n            // 地区名称\r\n            let name = v.properties.name;\r\n            // 地区经纬度\r\n            geoCoordMap[name] = {\r\n                center: v.properties.cp,\r\n                adcode: v.properties.adcode,\r\n            };\r\n        });\r\n        return geoCoordMap;\r\n    }\r\n    return null;\r\n}\r\n/**\r\n * 设置地图配色\r\n * @param options\r\n */\r\nexport function setGeoAreaColor(options,config) {\r\n    //当视觉映射关闭时删除属性\r\n    if (options.visualMap && options.visualMap.show == false) {\r\n        delete options.visualMap;\r\n    }\r\n    if (options.visualMap && options.visualMap.show == true) {\r\n        options.visualMap.inRange = {\r\n            color: config.commonOption.inRange.color,\r\n        };\r\n    }\r\n    //不使用渐变色\r\n    if (config.commonOption && config.commonOption.gradientColor == false) {\r\n        options.geo.itemStyle.normal.areaColor = config.commonOption.areaColor.color1;\r\n    }\r\n\r\n    //开启渐变色\r\n    if (config.commonOption && config.commonOption.gradientColor == true) {\r\n        options.geo.itemStyle.normal.areaColor = {\r\n            type: 'radial',\r\n            x: 0.5,\r\n            y: 0.5,\r\n            r: 0.8,\r\n            colorStops: [\r\n                {\r\n                    offset: 0,\r\n                    color: config.commonOption.areaColor.color1,\r\n                },\r\n                {\r\n                    offset: 1,\r\n                    color: config.commonOption.areaColor.color2,\r\n                },\r\n            ],\r\n            globalCoord: false,\r\n        };\r\n    }\r\n    return options;\r\n}\r\n/**\r\n * 处理总计和显示单位\r\n * @param {Object} compName\r\n * @param {Object} chartOption\r\n * @param {Object} config\r\n * @param {Object} chartData\r\n */\r\nexport function\thandleTotalAndUnitMap(compName,chartOption,config,chartData){\r\n    //1.获取到label配置项\r\n    if (config.compStyleConfig) {\r\n        //显示单位配置\r\n        let showUnitConfig = config.compStyleConfig.showUnit;\r\n        let unit = showUnitConfig.unit?showUnitConfig.unit:'';//单位\r\n        let numberLevel = showUnitConfig.numberLevel?showUnitConfig.numberLevel:'';//数值数量级\r\n        chartOption.series.forEach((item) => {\r\n            if(item.name == \"数据\"){\r\n                let labelConfig = {\r\n                    label: {\r\n                        normal:{\r\n                            show:unit?true:false,\r\n                            formatter: (params) => {\r\n                                let showLabel = `${params.name}: `;\r\n                                let value = 0;\r\n                                if(params.seriesType==\"effectScatter\"){\r\n                                    if(Array.isArray(params.value)){\r\n                                        value = params.value[2];\r\n                                    }else{\r\n                                        value = params.value;\r\n                                    }\r\n                                }\r\n                                //计算显示数值和添加前后缀\r\n                                if(unit){\r\n                                    showLabel += showUnitConfig.position == 'suffix' ? `${calcUnit(value, showUnitConfig)}${unit}` : `${unit}${calcUnit(value, showUnitConfig)}`;\r\n                                }\r\n                                return showLabel;\r\n                            }\r\n                        }\r\n                    }\r\n                };\r\n                deepMerge(item, {...labelConfig});\r\n            }\r\n        });\r\n        //显示总计配置\r\n        let summaryConfig = config.compStyleConfig.summary;\r\n        if(summaryConfig.showTotal && chartData && chartData.length>0){\r\n            //左y轴\r\n            let leftData = chartData.filter(item=>(!item.yAxisIndex || item.yAxisIndex == '0'));\r\n            let totalTitle = summaryConfig.showY?calcTotal(summaryConfig,leftData,config):'';\r\n            Object.assign(chartOption.title, {text:totalTitle});\r\n        }\r\n    }\r\n    //设置内边距\r\n    chartOption.geo.top = 20;\r\n    return chartOption\r\n}\r\n/**\r\n * 设置常规配置\r\n * @param {Object} chartOption\r\n */\r\nexport function handleCommonOpt(chartOption){\r\n    if(chartOption.visualMap){\r\n        chartOption.visualMap.show = false\r\n    }\r\n    return chartOption\r\n}\r\n/**\r\n * 数组等拆分子数组\r\n * @param array\r\n * @param subGroupLength\r\n */\r\nexport function arrDivide(array = [], subGroupLength = 0){\r\n    let index = 0;\r\n    const newArray = [];\r\n    while (index < array.length) {\r\n        newArray.push(array.slice(index, index += subGroupLength));\r\n    }\r\n    return newArray;\r\n}\r\n/**\r\n * 重新拼接URL，将参数自动拼接在现有URL后面\r\n * @param url string\r\n * @param query\r\n */\r\nexport function urlAssign(url, paramList) {\r\n    if (!url) return '';\r\n    if (paramList && paramList.length > 0) {\r\n        let queryArr = []\r\n        paramList.forEach(param=>{\r\n            queryArr.push(`${param['label']}=${param['value']}`)\r\n        })\r\n        if (url.indexOf('?') !== -1) {\r\n            url =`${url}&${queryArr.join('&')}`\r\n        } else {\r\n            url =`${url}?${queryArr.join('&')}`\r\n        }\r\n    }\r\n    return url.trim()\r\n}\r\n/**\r\n * 数据处理成模板结构\r\n * @param data\r\n */\r\nexport function dataToTemplate(rawData,config) {\r\n    let nameFields = config.nameFields;\r\n    let typeFields = config.typeFields;\r\n    let valueFields = config.valueFields;\r\n    let pivotTable = config.pivotTable;\r\n    let tempObj = {\r\n        x: [],\r\n        data: [],\r\n    };\r\n    //处理x数据\r\n    nameFields.forEach((obj) => {\r\n        let nameArr = rawData.map((item) => item[obj.fieldName]);\r\n        if(obj.fieldType == 'date'){\r\n            nameArr = rawData.map((item) => {\r\n                //数据兼容处理\r\n                return item[obj.fieldName]?item[obj.fieldName]:item[obj.fieldName.substring(0,obj.fieldName.length - 1)]\r\n            });\r\n        }\r\n        let nameObj = {};\r\n        nameObj[obj.fieldName] = nameArr;\r\n        tempObj.x.push(nameObj);\r\n    });\r\n    //处理y数据\r\n    let dataArr = [];\r\n    //包含列数据的情况\r\n    if (typeFields.length > 0) {\r\n        let typeArr = [];\r\n        rawData.forEach((item) => {\r\n            let arr = [];\r\n            typeFields.forEach((obj) => {\r\n                arr.push(item[obj.fieldName]);\r\n            });\r\n            let hasType = typeArr.filter((item) => item.toString() == arr.toString());\r\n            hasType.length == 0 && typeArr.push(arr);\r\n        });\r\n        typeArr.forEach((type) => {\r\n            valueFields.forEach((value) => {\r\n                let obj = { key: value.fieldName };\r\n                obj['y'] = type;\r\n                obj['data'] = rawData.map((item) => {\r\n                    let arr = [];\r\n                    typeFields.forEach((obj) => {\r\n                        arr.push(item[obj.fieldName]);\r\n                    });\r\n                    let existVal = type.toString() === arr.toString();\r\n                    return existVal ? item[value.fieldName] : null;\r\n                });\r\n                let summaryConfig = pivotTable.lineSummary.controlList.filter((control) => control.key == value.fieldName);\r\n                let totalType = summaryConfig && summaryConfig.length > 0 ? summaryConfig[0].totalType : 'sum';\r\n                let sum = calcSummary(obj['data'], totalType);\r\n                obj['sum'] = sum?sum:tableEllipsis;\r\n                obj['summary_col'] = false;\r\n                dataArr.push(obj);\r\n            });\r\n        });\r\n        //处理列汇总信息\r\n        let showColumnTotal = pivotTable?.showColumnTotal;\r\n        if (showColumnTotal) {\r\n            let length = rawData.length;\r\n            valueFields.forEach((value) => {\r\n                let obj = { key: value.fieldName };\r\n                obj['y'] = [];\r\n                let dataMap = dataArr.filter((item) => item.key == value.fieldName).map((a) => a.data);\r\n                let summaryConfig = pivotTable.columnSummary.controlList.filter((control) => control.key == value.fieldName);\r\n                let totalType = summaryConfig && summaryConfig.length > 0 ? summaryConfig[0].totalType : 'sum';\r\n                let show = summaryConfig && summaryConfig.length > 0 ? summaryConfig[0].show : false;\r\n                let data = [];\r\n                for (let i = 0; i < length; i++) {\r\n                    let map = dataMap.map((item) => item[i]);\r\n                    let val = show ? calcSummary(map, totalType) : tableEllipsis;\r\n                    let number = val;\r\n                    data.push(number);\r\n                }\r\n                obj['data'] = data;\r\n                //处理数值翻译的情况\r\n                let originalData = getOriginalVal(value,data);\r\n                let sum = calcSummary(originalData, totalType);\r\n                obj['sum'] = sum?sum:tableEllipsis;\r\n                obj['summary_col'] = true;\r\n                dataArr.push(obj);\r\n            });\r\n        }\r\n        tempObj.data = dataArr;\r\n    } else {\r\n        //没有type的情况下的处理\r\n        valueFields.forEach((value) => {\r\n            let obj = { key: value.fieldName };\r\n            obj['y'] = [];\r\n            obj['data'] = rawData.map((item) => {\r\n                return item[value.fieldName];\r\n            });\r\n            let summaryConfig = pivotTable.lineSummary.controlList.filter((control) => control.key == value.fieldName);\r\n            let type = summaryConfig && summaryConfig.length > 0 ? summaryConfig[0].totalType : 'sum';\r\n            //处理数值翻译的情况\r\n            let originalData = getOriginalVal(value,obj['data']);\r\n            obj['sum'] = calcSummary(originalData, type)? calcSummary(originalData, type):tableEllipsis;\r\n            obj['summary_col'] = false;\r\n            dataArr.push(obj);\r\n        });\r\n        tempObj.data = dataArr;\r\n    }\r\n    return { ...tempObj };\r\n}\r\n\r\n/**\r\n * 获取原始数值数据\r\n * @param value\r\n * @param data\r\n */\r\nfunction getOriginalVal(value,data){\r\n    if(value.options && value.options.unitText){\r\n        let originalData = data.map(d=>{\r\n            return d?Number(d.replace(value.options.unitText,'')):0;\r\n        })\r\n        return originalData;\r\n    }\r\n    return data\r\n}\r\n\r\n/**\r\n * 计算合计\r\n * @param valueArr\r\n * @param type\r\n */\r\nfunction calcSummary(valueArr, type) {\r\n    valueArr = valueArr.filter((value) => value && value != null);\r\n    let total = 0;\r\n    if (valueArr.length > 0) {\r\n        if (type == 'sum') {\r\n            total = valueArr.reduce((prev, cur) => prev + cur, 0);\r\n        } else if (type == 'max') {\r\n            total = Math.max.apply(Math, valueArr);\r\n        } else if (type == 'min') {\r\n            total = Math.min.apply(Math, valueArr);\r\n        } else if (type == 'average') {\r\n            // @ts-ignore\r\n            total = (valueArr.reduce((prev, cur) => prev + cur, 0) / valueArr.length).toFixed(2);\r\n        }\r\n    }\r\n    return isNumber(total)?Number(keepTwoDecimals(total)):null;\r\n}\r\n/**\r\n * 数据处理\r\n * @param chartData\r\n */\r\nfunction handleData(props,rawData,chartData) {\r\n    let nameFields = props.config.nameFields;\r\n    let typeFields = props.config.typeFields;\r\n    let showLineTotal = props.config.pivotTable.showLineTotal;\r\n    let lineSummary = props.config.pivotTable.lineSummary;\r\n    let columnSummary = props.config.pivotTable.columnSummary;\r\n    //表格数据\r\n    let dataSource = [];\r\n    //行总计数据\r\n    let lineTotalObj = {};\r\n    nameFields.forEach((name, index) => {\r\n        chartData.x[index][name.fieldName].forEach((item, xindex) => {\r\n            if (dataSource[xindex]) {\r\n                dataSource[xindex][name.fieldName] = item;\r\n            } else {\r\n                let obj = {};\r\n                obj[name.fieldName] = item;\r\n                dataSource.push(obj);\r\n            }\r\n        });\r\n        //配置行总计\r\n        if (showLineTotal && index==0) {\r\n            lineTotalObj[name.fieldName] = lineSummary.name ? lineSummary.name : '行汇总';\r\n        }\r\n    });\r\n    chartData.data.forEach((item, xindex) => {\r\n        let key = typeFields && typeFields.length > 0 ? item.key + xindex : item.key;\r\n        item.data.forEach((val, index) => {\r\n            dataSource[index][key] = val;\r\n        });\r\n        if (showLineTotal) {\r\n            if (!item.summary_col) {\r\n                let control = lineSummary.controlList.filter((control) => control.key == item.key);\r\n                let show = control[0].show;\r\n                let showName = control[0].showName;\r\n                lineTotalObj[key] = show ? `${showName}${item.sum}` : tableEllipsis;\r\n            } else {\r\n                //合计列的数据处理\r\n                let control = columnSummary.controlList.filter((control) => control.key == item.key);\r\n                let show = control[0].show;\r\n                lineTotalObj[key] = show ? item.sum : tableEllipsis;\r\n            }\r\n        }\r\n    });\r\n    //显示行汇总\r\n    if (showLineTotal && rawData.value.length>0) {\r\n        lineTotalObj['colSpan'] = nameFields.length;\r\n        if (lineSummary.location == '1') {\r\n            dataSource.unshift(lineTotalObj);\r\n        } else {\r\n            dataSource.push(lineTotalObj);\r\n        }\r\n    }\r\n    //处理单位显示\r\n    return dataSource;\r\n}\r\n/**\r\n * 添加图片前缀\r\n * @param imgUrl\r\n */\r\nexport function addImgPrefix (imgUrl){\r\n    if(imgUrl){\r\n        if (imgUrl.startsWith('http://') || imgUrl.startsWith('https://')) {\r\n            return imgUrl;\r\n        }\r\n        if (imgUrl.startsWith('data:image/png;base64')) {\r\n            return imgUrl;\r\n        } else {\r\n            imgUrl = imgUrl.indexOf('/img/bg/source/')>=0?imgUrl.replaceAll('/img/bg/source/',\"/img/\"):imgUrl;\r\n            imgUrl = imgUrl.indexOf('/img/bg/border/')>=0?imgUrl.replaceAll('/img/bg/border/',\"/img/\"):imgUrl;\r\n            let url = imgUrl && imgUrl.indexOf('/img/bg/')>=0?imgUrl.replaceAll('/img/bg/',\"/img/\"):imgUrl;\r\n            return `${getEnvBaseUrl()}/drag/lib${url}`;\r\n        }\r\n    }\r\n};\r\n/**\r\n * 校验协议\r\n * @param url\r\n */\r\nexport function checkUrlPrefix(url) {\r\n    // 获取当前页面的协议\r\n    const currentProtocol = window.location.protocol;\r\n    // 获取传入 url 的协议\r\n    const urlObj = new URL(url);\r\n    const urlProtocol = urlObj.protocol;\r\n\r\n    // 判断协议是否一致\r\n    const isDiffProtocol = currentProtocol.startsWith('https') &&  currentProtocol != urlProtocol;\r\n    // 返回当前页面的协议\r\n    return {\r\n        isDiffProtocol: isDiffProtocol,\r\n        currentProtocol: currentProtocol\r\n    };\r\n}\r\n/**\r\n * 字典转换\r\n */\r\nexport function dictTransform(chartData,dictOptions) {\r\n    if (dictOptions && Object.keys(dictOptions).length > 0) {\r\n        Object.keys(dictOptions).forEach((code) => {\r\n            if( dictOptions[code] && isArray(dictOptions[code])){\r\n                chartData.forEach((item) => {\r\n                    let obj = dictOptions[code].filter((dict) => dict.value === item[code] + '');\r\n                    item[code] = obj && obj.length > 0 ? obj[0]['text'] : item[code];\r\n                });\r\n            }\r\n        });\r\n    }\r\n    return chartData;\r\n}\r\n// 获取url中的参数\r\nexport const getUrlParams = (url) => {\r\n    let result = {\r\n        url: '',\r\n        params: {}\r\n    };\r\n    let list = url.split('?');\r\n    result.url = list[0];\r\n    let params = list[1];\r\n    if (params) {\r\n        let list = params.split('&');\r\n        list.forEach(ele => {\r\n            let dic = ele.split('=');\r\n            let label = dic[0];\r\n            let value = dic[1];\r\n            result.params[label] = decodeURIComponent(value);\r\n        });\r\n    }\r\n    return result;\r\n};\r\n"], "names": ["dayjs", "weekday", "localeData", "conditionOptions", "selectType", "isArray", "field", "isString", "isNullOrUnDef", "colorPanel", "isNumber", "cloneDeep", "getEnvBaseUrl", "list"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAOAA,cAAAA,MAAM,OAAOC,cAAAA,OAAO;AACpBD,cAAAA,MAAM,OAAOE,cAAAA,UAAU;AAEP,SAAA,UAAU,MAAM,SAAS;AACjC,MAAA,UAAU,WAAW,GAAG;AACjB,WAAA;AAAA,EAAA;AAGP,MAAA;AACJ,QAAM,SAAS,WAAW;AAEtB,MAAA,OAAO,SAAS,UAAU;AACnB,WAAA;AAAA,EAAA,OACJ;AACH,QAAI,OAAO,SAAS,YAAY,WAAW,KAAK,IAAI,GAAG;AACnD,aAAO,SAAS,IAAI;AAAA,IAAA,OACnB;AACM,aAAA,IAAI,KAAK,IAAI;AAAA,IAAA;AAGjB,WAAA,IAAI,KAAK,KAAK,WAAW,QAAQ,MAAM,GAAG,CAAC;AAAA,EAAA;AAItD,QAAM,YAAY;AAAA,IACd,GAAG,KAAK,YAAY;AAAA,IACpB,GAAG,KAAK,SAAA,IAAa;AAAA,IACrB,GAAG,KAAK,QAAQ;AAAA,IAChB,GAAG,KAAK,SAAS;AAAA,IACjB,GAAG,KAAK,WAAW;AAAA,IACnB,GAAG,KAAK,WAAW;AAAA,IACnB,GAAG,KAAK,OAAO;AAAA,EACnB;AAEA,QAAM,WAAW,OAAO,QAAQ,mBAAmB,CAAC,QAAQ,QAAQ;AAC1D,UAAA,QAAQ,UAAU,GAAG;AAE3B,QAAI,QAAQ,KAAK;AACN,aAAA,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,EAAE,KAAK;AAAA,IAAA;AAGpD,WAAO,MAAM,SAAA,EAAW,SAAS,GAAG,GAAG;AAAA,EAAA,CAC1C;AAEM,SAAA;AACX;AAMO,SAAS,SAAS,MAAM;AACvB,MAAA,2BAAW,KAAK;AACpB,UAAQ,MAAM;AAAA,IACV,KAAK;AACD,aAAO,OAAO,IAAI;AAAA,IAEtB,KAAK;AACD,aAAO,OAAO,IAAI;AAAA,IAEtB,KAAK;AACM,aAAA,OAAO,MAAM,CAAC;AAAA,IAEzB,KAAK;AACD,aAAO,QAAQ,IAAI;AAAA,IAEvB,KAAK;AACD,aAAO,QAAQ,IAAI;AAAA,IAEvB,KAAK;AACD,aAAO,QAAQ,IAAI;AAAA,IAEvB,KAAK;AACM,aAAA,QAAQ,MAAM,CAAC;AAAA,IAE1B,KAAK;AACD,aAAO,SAAS,IAAI;AAAA,IAExB,KAAK;AACD,aAAO,SAAS,IAAI;AAAA,IAExB,KAAK;AACD,aAAO,SAAS,IAAI;AAAA,IAExB,KAAK;AACM,aAAA,SAAS,MAAM,CAAC;AAAA,IAE3B,KAAK;AACD,aAAO,UAAU,IAAI;AAAA,IAEzB,KAAK;AACD,aAAO,QAAQ,IAAI;AAAA,IAEvB,KAAK;AACD,aAAO,QAAQ,IAAI;AAAA,IAEvB,KAAK;AACM,aAAA,QAAQ,MAAM,CAAC;AAAA,IAE1B,KAAK;AACD,aAAO,SAAS,IAAI;AAAA,IAExB;AACW,aAAA;AAAA,EAAA;AAEnB;AAMO,SAAS,OAAO,MAAM;AACrB,MAAA,YAAYF,oBAAM,IAAI,EAAE,QAAQ,MAAM,EAAE,OAAO,qBAAqB;AACpE,MAAA,UAAUA,oBAAM,IAAI,EAAE,MAAM,MAAM,EAAE,OAAO,qBAAqB;AAC7D,SAAA,CAAC,WAAW,OAAO;AAC9B;AAKO,SAAS,QAAQ,MAAM;AACtB,MAAA,YAAYA,oBAAM,IAAI,EAAE,QAAQ,MAAM,EAAE,OAAO,qBAAqB;AACpE,MAAA,UAAUA,oBAAM,IAAI,EAAE,MAAM,MAAM,EAAE,OAAO,qBAAqB;AAC7D,SAAA,CAAC,WAAW,OAAO;AAC9B;AAMO,SAAS,SAAS,MAAM;AACvB,MAAA,YAAYA,oBAAM,IAAI,EAAE,QAAQ,OAAO,EAAE,OAAO,qBAAqB;AACrE,MAAA,UAAUA,oBAAM,IAAI,EAAE,MAAM,OAAO,EAAE,OAAO,qBAAqB;AAC9D,SAAA,CAAC,WAAW,OAAO;AAC9B;AAMO,SAAS,QAAQ,MAAM;AACtB,MAAA,YAAYA,oBAAM,IAAI,EAAE,QAAQ,MAAM,EAAE,OAAO,qBAAqB;AACpE,MAAA,UAAUA,oBAAM,IAAI,EAAE,MAAM,MAAM,EAAE,OAAO,qBAAqB;AAC7D,SAAA,CAAC,WAAW,OAAO;AAC9B;AAKA,SAAS,OAAO,MAAM,WAAW,GAAG;AAChC,MAAI,YAAYA,cAAAA,MAAM,IAAI,EAAE,SAAS,UAAU,MAAM,EAAE,QAAQ,MAAM,EAAE,OAAO,qBAAqB;AACnG,MAAI,UAAUA,cAAAA,MAAM,IAAI,EAAE,SAAS,UAAU,MAAM,EAAE,MAAM,MAAM,EAAE,OAAO,qBAAqB;AACxF,SAAA,CAAC,WAAW,OAAO;AAC9B;AAMA,SAAS,QAAQ,MAAM;AACnB,MAAI,YAAYA,cAAAA,MAAM,IAAI,EAAE,IAAI,GAAG,MAAM,EAAE,QAAQ,MAAM,EAAE,OAAO,qBAAqB;AACvF,MAAI,UAAUA,cAAAA,MAAM,IAAI,EAAE,IAAI,GAAG,MAAM,EAAE,MAAM,MAAM,EAAE,OAAO,qBAAqB;AAC5E,SAAA,CAAC,WAAW,OAAO;AAC9B;AAMA,SAAS,QAAQ,MAAM,WAAW,GAAG;AACjC,MAAI,YAAYA,cAAAA,MAAM,IAAI,EAAE,SAAS,UAAU,MAAM,EAAE,QAAQ,MAAM,EAAE,OAAO,qBAAqB;AACnG,MAAI,UAAUA,cAAAA,MAAM,IAAI,EAAE,SAAS,UAAU,MAAM,EAAE,MAAM,MAAM,EAAE,OAAO,qBAAqB;AACxF,SAAA,CAAC,WAAW,OAAO;AAC9B;AAMA,SAAS,SAAS,MAAM;AACpB,MAAI,YAAYA,cAAAA,MAAM,IAAI,EAAE,IAAI,GAAG,MAAM,EAAE,QAAQ,MAAM,EAAE,OAAO,qBAAqB;AACvF,MAAI,UAAUA,cAAAA,MAAM,IAAI,EAAE,IAAI,GAAG,MAAM,EAAE,MAAM,MAAM,EAAE,OAAO,qBAAqB;AAC5E,SAAA,CAAC,WAAW,OAAO;AAC9B;AAMA,SAAS,SAAS,MAAM,WAAW,GAAG;AAClC,MAAI,YAAYA,cAAAA,MAAM,IAAI,EAAE,SAAS,UAAU,OAAO,EAAE,QAAQ,OAAO,EAAE,OAAO,qBAAqB;AACrG,MAAI,UAAUA,cAAAA,MAAM,IAAI,EAAE,SAAS,UAAU,OAAO,EAAE,MAAM,OAAO,EAAE,OAAO,qBAAqB;AAC1F,SAAA,CAAC,WAAW,OAAO;AAC9B;AAMA,SAAS,UAAU,MAAM;AACrB,MAAI,YAAYA,cAAAA,MAAM,IAAI,EAAE,IAAI,GAAG,OAAO,EAAE,QAAQ,OAAO,EAAE,OAAO,qBAAqB;AACzF,MAAI,UAAUA,cAAAA,MAAM,IAAI,EAAE,IAAI,GAAG,OAAO,EAAE,MAAM,OAAO,EAAE,OAAO,qBAAqB;AAC9E,SAAA,CAAC,WAAW,OAAO;AAC9B;AAMA,SAAS,QAAQ,MAAM,WAAW,GAAG;AACjC,MAAI,YAAYA,cAAAA,MAAM,IAAI,EAAE,SAAS,UAAU,MAAM,EAAE,QAAQ,MAAM,EAAE,OAAO,qBAAqB;AACnG,MAAI,UAAUA,cAAAA,MAAM,IAAI,EAAE,SAAS,UAAU,MAAM,EAAE,MAAM,MAAM,EAAE,OAAO,qBAAqB;AACxF,SAAA,CAAC,WAAW,OAAO;AAC9B;AAMA,SAAS,SAAS,MAAM;AACpB,MAAI,YAAYA,cAAAA,MAAM,IAAI,EAAE,IAAI,GAAG,MAAM,EAAE,QAAQ,MAAM,EAAE,OAAO,qBAAqB;AACvF,MAAI,UAAUA,cAAAA,MAAM,IAAI,EAAE,IAAI,GAAG,MAAM,EAAE,MAAM,MAAM,EAAE,OAAO,qBAAqB;AAC5E,SAAA,CAAC,WAAW,OAAO;AAC9B;AAgDO,SAAS,QAAQ,IAAI;AACpB,MAAA;AACA,UAAM,KAAK;AACX,WAAO,IAAI,GAAG,YAAY,EAAE,EAAE;AAAA,WACzB,GAAG;AACA,YAAA,IAAI,WAAW,CAAC;AACjB,WAAA;AAAA,EAAA;AAEf;AAMO,SAAS,aAAa,gBAAgB;AACzC,MAAI,SAAS,CAAC;AACd,MAAG,eAAe,cAAc,eAAe,WAAW,UAAQ,GAAE;AAChE,QAAI,YAAY,GAAG,eAAe,WAAW,CAAC,CAAC;AAC/C,QAAI,UAAU,GAAG,eAAe,WAAW,CAAC,CAAC;AACtC,WAAA,CAAC,WAAU,OAAO;AAAA,EAAA;AAEzB,MAAA,eAAe,cAAc,OAAO;AAChC,QAAA,YAAY,SAAS,eAAe,UAAU;AAC9C,QAAA,aAAa,UAAU,SAAS,GAAG;AAC5B,aAAA,CAAC,IAAI,UAAU,CAAC;AAChB,aAAA,CAAC,IAAI,UAAU,CAAC;AAAA,IAAA;AAEpB,WAAA;AAAA,EAAA;AAEJ,SAAA;AACX;AAMO,SAAS,oBAAoB,KAAK;AACjC,MAAA,IAAI,cAAc,OAAO;AACzB,WAAOG,qCAAAA,iBAAiB,KAAK;AAAA,EAAA;AAE7B,MAAA,IAAI,cAAc,oBAAoB;AACtC,WAAOA,qCAAAA,iBAAiB,kBAAkB;AAAA,EAAA;AAE1C,MAAA,IAAI,cAAc,eAAe;AACjC,WAAOA,qCAAAA,iBAAiB,aAAa;AAAA,EAAA;AAEzC,MAAI,IAAI,cAAc,gBAAgB,IAAI,QAAQ,eAAe,YAAY;AACzE,WAAOA,qCAAAA,iBAAiB,aAAa;AAAA,EAAA;AAErC,MAAA,IAAI,cAAc,QAAQ;AAC1B,WAAOA,qCAAAA,iBAAiB,MAAM;AAAA,EAAA;AAElC,MAAI,IAAI,aAAa,UAAU,IAAI,aAAa,QAAQ;AACpD,WAAOA,qCAAAA,iBAAiB,MAAM;AAAA,EAAA;AAElC,MAAIC,gDAAW,SAAS,IAAI,UAAU,GAAG;AACrC,WAAOD,qCAAAA,iBAAiB,QAAQ;AAAA,EAAA;AAEpC,MAAI,CAAC,OAAO,UAAU,cAAc,QAAQ,EAAE,SAAS,IAAI,SAAS,KAAK,CAAC,SAAS,WAAW,QAAQ,QAAQ,EACzG,SAAS,IAAI,UAAU,GAAG;AAC3B,WAAOA,qCAAAA,iBAAiB,QAAQ;AAAA,EAAA;AAEpC,SAAOA,qCAAAA,iBAAiB,MAAM;AAClC;AAKgB,SAAA,uBAAuB,iBAAiB,UAAU;AAC9D,MAAI,MAAM,CAAC;AACX,kBAAgB,QAAQ,CAAa,cAAA;AACjC,QAAI,MAAM,CAAC;AACX,QAAI,YAAY,UAAU;AAC1B,QAAI,YAAY,UAAU;AAC1B,QAAI,YAAY,UAAU;AAC1B,QAAI,aAAa,UAAU;AAC3B,QAAI,aAAa,UAAU;AAC3B,QAAI,OAAO,IAAI;AACf,QAAI,OAAO,IAAI;AACf,QAAI,MAAM,IAAI;AACd,QAAI,OAAO,IAAI;AACf,QAAI,OAAO,IAAI;AAEX,QAAA,kBAAkB,oBAAoB,SAAS,EAAE,OAAO,CAAQ,SAAA,KAAK,SAAS,SAAS;AACvF,QAAA,YAAY,IAAI,UAAU,SAAS;AAEvC,QAAI,CAAC,eAAe,kBAAkB,EAAE,SAAS,UAAU,GAAG;AAC1D,UAAI,MAAM,IAAI,UAAU,cAAc,UAAU;AAAA,IAAA;AAGpD,QAAI,gBAAgB,cAAc,UAAU,QAAQ,eAAe,YAAY;AAC3E,UAAI,MAAM,IAAI;AAAA,IAAA;AAGd,QAAA,YAAY,cAAc,CAAC,YAAY;AACvC,mBAAa,UAAU,QAAQ,gBAAgB,UAAU,QAAQ;AACjE,UAAI,OAAO,IAAI;AAAA,IAAA;AAGf,QAAA,aAAa,OAAO,aAAa,KAAK;AACtC,UAAI,KAAK,GAAG;AAAA,IAAA,OACT;AAEC,UAAA,eAAe,aAAa,OAAO;AAEnC,UAAA,CAAC,OAAO,WAAW,UAAU,cAAc,QAAQ,EAAE,SAAS,SAAS,GAAG;AAC1E,YAAI,MAAM,IAAI;AAEV,YAAA,aAAa,OAAO,aAAa,MAAM;AACnC,cAAA,OAAO,IAAI,UAAU,aAAa,OAAO,UAAU,UAAU,IAAI,UAAU;AAC3E,cAAA,KAAK,IAAI,UAAU,WAAW,OAAO,UAAU,QAAQ,IAAI,UAAU;AACzE,0BAAgB,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,OAAO,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAC/E,OAAO;AAAA,QAAA,OACR;AACC,cAAA,OAAO,IAAI,OAAO,UAAU;AAChC,yBAAe,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO;AAAA,QAAA;AAAA,MAC9D,WACO,UAAU,cAAc,QAAQ;AACvC,YAAI,MAAM,IAAI;AACV,YAAA,aAAa,OAAO,aAAa,MAAM;AACnC,cAAA,OAAO,IAAI,UAAU,aAAa,UAAU,UAAU,YAAW,UAAU,IAAG;AAC9E,cAAA,KAAK,IAAI,UAAU,WAAW,UAAU,UAAU,UAAS,UAAU,IAAI;AAC7D,yBAAA,CAAC,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,IAAK,QAAQ;AAAA,QAAA,OACrD;AACH,cAAI,OAAO,IAAI,aAAa,WAAW,OAAO,UAAU,IAAI;AAC7C,yBAAA,IAAI,OAAO,IAAI,OAAO;AAAA,QAAA;AAAA,MACzC,WACO,UAAU,cAAc,SAAS,UAAU,cAAc,gBAAgB;AAChF,YAAI,MAAM,IAAI;AACV,YAAA,OAAO,IAAI,cAAcE,iBAAQ,UAAU,IAAI,WAAW,WAAW,SAAS,CAAC,IAAI;AACxE,uBAAA,IAAI,OAAO,IAAI,OAAO;AAAA,MAC9B,WAAA,aAAa,UAAU,aAAa,QAAQ;AAEnD,YAAI,MAAM,IAAI;AAEV,YAAA,aAAa,OAAO,aAAa,MAAM;AACvC,cAAI,UAAU,WAAW,UAAU,QAAQ,QAAQ,YAAY;AAEvD,gBAAA,OAAO,IAAI,UAAU,WAAW,CAAC,IAAI,UAAU,WAAW,CAAC,IAAI;AAC/D,gBAAA,KAAK,IAAI,UAAU,WAAW,CAAC,IAAI,UAAU,WAAW,CAAC,IAAI;AAAA,UAAA,OAC9D;AAEC,gBAAA,UAAU,WAAW,CAAC,GAAG;AACnB,oBAAA,YAAY,IAAI,KAAK,UAAU,UAAU,WAAW,CAAC,GAAE,qBAAqB,CAAC;AACnF,wBAAU,SAAS,GAAG,GAAG,GAAG,CAAC;AAC7B,kBAAI,OAAO,IAAI,UAAU,UAAU,WAAU,qBAAqB;AAAA,YAAA;AAElE,gBAAA,UAAU,WAAW,CAAC,GAAG;AACnB,oBAAA,UAAU,IAAI,KAAK,UAAU,UAAU,WAAW,CAAC,GAAE,qBAAqB,CAAC;AACjF,sBAAQ,SAAS,IAAI,IAAI,IAAI,GAAG;AAChC,kBAAI,KAAK,IAAI,UAAU,QAAQ,WAAU,qBAAqB;AAAA,YAAA;AAAA,UAClE;AAEY,yBAAA,CAAC,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,IAAK,QAAQ;AAAA,QAAA,OACrD;AACC,cAAA,UAAU,iBAAiB,cAAc;AACzC,gBAAI,YAAY;AACN,oBAAA,YAAY,IAAI,KAAK,UAAU;AACrC,wBAAU,SAAS,GAAG,GAAG,GAAG,CAAC;AACvB,oBAAA,UAAU,IAAI,KAAK,UAAU;AACnC,sBAAQ,SAAS,IAAI,IAAI,IAAI,GAAG;AAChC,kBAAI,OAAO,IAAI,UAAU,UAAU,WAAU,qBAAqB;AAClE,kBAAI,KAAK,IAAI,UAAU,QAAQ,WAAU,qBAAqB;AAC9C,6BAAA,CAAC,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,IAAK,QAAQ;AAAA,YAAA;AAAA,UAC5D,OACG;AACC,gBAAA,QAAQ,SAAS,UAAU,aAAa;AACxC,gBAAA,OAAO,IAAI,SAAS,MAAM,SAAS,IAAI,MAAM,CAAC,IAAI;AAClD,gBAAA,OAAO,IAAI,SAAS,MAAM,SAAS,IAAI,MAAM,CAAC,IAAI;AAClD,gBAAA,KAAK,IAAI,SAAS,MAAM,SAAS,IAAI,MAAM,CAAC,IAAI;AAChD,gBAAA,aAAa,OAAO,aAAa,KAAK;AACtB,6BAAA,CAAC,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,IAAK,QAAQ;AAAA,YAAA;AAGxD,gBAAA,aAAa,OAAO,aAAa,KAAK;AAClC,kBAAA,OAAO,IAAI,SAAS,MAAM,SAAS,IAAI,MAAM,CAAC,IAAI;AACvC,6BAAA,IAAI,OAAO,IAAI,OAAO;AAAA,YAAA;AAGrC,gBAAA,aAAa,OAAO,aAAa,KAAK;AAClC,kBAAA,OAAO,IAAI,SAAS,MAAM,SAAS,IAAI,MAAM,CAAC,IAAI;AACvC,6BAAA,IAAI,OAAO,IAAI,OAAO;AAAA,YAAA;AAAA,UACzC;AAAA,QACJ;AAAA,MACJ;AAGJ,UAAID,gDAAW,SAAS,UAAU,UAAU,GAAG;AAC3C,YAAI,MAAM,IAAI;AACd,YAAI,OAAO,IAAI;AACf,YAAI,YAAY;AACR,cAAAC,SAAAA,QAAQ,UAAU,GAAG;AACjB,gBAAA,OAAO,IAAI,WAAW,SAAS,IAAI,KAAK,UAAU,UAAU,IAAI;AAAA,UAAA,OACjE;AACH,gBAAI,OAAO,IAAI,KAAK,UAAU,WAAW,MAAM,GAAG,CAAC;AAAA,UAAA;AAAA,QACvD;AAEW,uBAAA,IAAI,OAAO,IAAI,OAAO;AAAA,MAAA;AAIrC,UAAA,mBAAmB,gBAAgB,SAAS,GAAG;AAC/C,YAAI,CAAC,UAAU,QAAQ,MAAM,EAAE,SAAS,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,KAAK,GAAG,EAAE,SAAS,SAAS,GAAG;AACrF,cAAA,YAAY,IAAI,QAAQ,SAAS;AAEjC,cAAA,YAAY,YAAY,UAAU;AAC9B,gBAAA,OAAO,IAAI,GAAG,UAAU;AAAA,UAAA,OACzB;AACH,gBAAI,OAAO,IAAI,gBAAgB,CAAC,EAAE,OAAO,KAAK,MAAM,GAAG,UAAU,MAAM,IAAI,UAAU;AAAA,UAAA;AAAA,QACzF;AAEA,YAAA,IAAI,MAAM,KAAK,YAAY,CAAC,KAAK,GAAG,EAAE,SAAS,SAAS,GAAG;AACvD,cAAA,YAAY,IAAI,UAAU,SAAS;AAAA,QAAA;AAE3B,wBAAA,IAAI,KAAK,GAAG;AAAA,MAAA;AAAA,IAChC;AAAA,EACJ,CACH;AACM,SAAA;AACX;AAKO,SAAS,sBAAsB,iBAAiB;AACnD,MAAI,MAAM,CAAC;AACX,kBAAgB,QAAQ,CAAa,cAAA;AACjC,QAAI,MAAM,CAAC;AACX,QAAI,YAAY,UAAU;AAC1B,QAAI,YAAY,UAAU;AAC1B,QAAI,YAAY,UAAU;AAC1B,QAAI,aAAa,UAAU;AAC3B,QAAI,aAAa,UAAU;AAC3B,QAAI,OAAO,IAAI;AACf,QAAI,OAAO,IAAI;AACf,QAAI,MAAM,IAAI;AACd,QAAI,OAAO,IAAI;AACf,QAAI,OAAO,IAAI;AAEX,QAAA,kBAAkB,oBAAoB,SAAS,EAAE,OAAO,CAAQ,SAAA,KAAK,SAAS,SAAS;AACvF,QAAA,YAAY,IAAI,UAAU,SAAS;AAEvC,QAAI,CAAC,eAAe,kBAAkB,EAAE,SAAS,UAAU,GAAG;AAC1D,UAAI,MAAM,IAAI,UAAU,cAAc,UAAU;AAAA,IAAA;AAGpD,QAAI,gBAAgB,cAAc,UAAU,QAAQ,eAAe,YAAY;AAC3E,UAAI,MAAM,IAAI;AAAA,IAAA;AAGd,QAAA,YAAY,cAAc,CAAC,YAAY;AACvC,UAAI,OAAO,IAAI;AAAA,IAAA;AAGf,QAAA,eAAe,aAAa,OAAO;AAEnC,QAAA,CAAC,OAAO,WAAW,UAAU,cAAc,QAAQ,EAAE,SAAS,SAAS,GAAG;AAC1E,UAAI,MAAM,IAAI;AAEd,UAAI,aAAa,KAAK;AACd,YAAA,OAAO,IAAI,UAAU,aAAa,OAAO,UAAU,UAAU,IAAI,UAAU;AAC3E,YAAA,KAAK,IAAI,UAAU,WAAW,OAAO,UAAU,QAAQ,IAAI,UAAU;AACzE,wBAAgB,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,OAAO,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAC/E,OAAO;AAAA,MAAA,OACR;AACH,YAAI,OAAO,IAAI,aAAW,OAAO,UAAU,IAAE;AAC7C,uBAAe,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO;AAAA,MAAA;AAAA,IAC9D,WACO,UAAU,cAAc,QAAQ;AACvC,UAAI,MAAM,IAAI;AACd,UAAI,aAAa,KAAK;AACd,YAAA,OAAO,IAAI,UAAU,aAAa,UAAU,UAAU,YAAW,UAAU,IAAG;AAC9E,YAAA,KAAK,IAAI,UAAU,WAAW,UAAU,UAAU,UAAS,UAAU,IAAI;AAC7D,uBAAA,CAAC,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,IAAK,QAAQ;AAAA,MAAA,OACrD;AACH,YAAI,OAAO,IAAI;AACA,uBAAA,IAAI,OAAO,IAAI,OAAO;AAAA,MAAA;AAAA,IACzC,WACO,UAAU,cAAc,SAAS,UAAU,cAAc,gBAAgB;AAChF,UAAI,MAAM,IAAI;AACV,UAAA,OAAO,IAAI,cAAcA,iBAAQ,UAAU,IAAI,WAAW,WAAW,SAAS,CAAC,IAAI;AACxE,qBAAA,IAAI,OAAO,IAAI,OAAO;AAAA,IAC9B,WAAA,aAAa,UAAU,aAAa,QAAQ;AAEnD,UAAI,MAAM,IAAI;AAEd,UAAI,aAAa,KAAK;AAClB,YAAI,UAAU,WAAW,UAAU,QAAQ,QAAQ,YAAY;AAEvD,cAAA,OAAO,IAAI,UAAU,WAAW,CAAC,IAAI,UAAU,WAAW,CAAC,IAAI;AAC/D,cAAA,KAAK,IAAI,UAAU,WAAW,CAAC,IAAI,UAAU,WAAW,CAAC,IAAI;AAAA,QAAA,OAC9D;AAEH,cAAI,UAAU,YAAY;AACtB,kBAAM,YAAY,IAAI,KAAK,UAAU,UAAU;AAC/C,sBAAU,SAAS,GAAG,GAAG,GAAG,CAAC;AACzB,gBAAA,OAAO,IAAIL,cAAAA,MAAM,UAAU,SAAS,EAAE,OAAO,qBAAqB;AAAA,UAAA;AAE1E,cAAI,UAAU,UAAU;AACpB,kBAAM,UAAU,IAAI,KAAK,UAAU,QAAQ;AAC3C,oBAAQ,SAAS,IAAI,IAAI,IAAI,GAAG;AAC5B,gBAAA,KAAK,IAAIA,cAAAA,MAAM,QAAQ,SAAS,EAAE,OAAO,qBAAqB;AAAA,UAAA;AAAA,QACtE;AAEY,uBAAA,CAAC,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,IAAK,QAAQ;AAAA,MAAA,OACrD;AACH,YAAI,YAAY;AACN,gBAAA,YAAY,IAAI,KAAK,UAAU;AACrC,oBAAU,SAAS,GAAG,GAAG,GAAG,CAAC;AACvB,gBAAA,UAAU,IAAI,KAAK,UAAU;AACnC,kBAAQ,SAAS,IAAI,IAAI,IAAI,GAAG;AAC5B,cAAA,OAAO,IAAIA,cAAAA,MAAM,UAAU,SAAS,EAAE,OAAO,qBAAqB;AAClE,cAAA,KAAK,IAAIA,cAAAA,MAAM,QAAQ,SAAS,EAAE,OAAO,qBAAqB;AAClD,yBAAA,CAAC,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,IAAK,QAAQ;AAAA,QAAA;AAAA,MAC5D;AAAA,IACJ;AAGJ,QAAII,gDAAW,SAAS,UAAU,UAAU,GAAG;AAC3C,UAAI,MAAM,IAAI;AACd,UAAI,OAAO,IAAI;AACf,UAAI,YAAY;AACR,YAAAC,SAAAA,QAAQ,UAAU,GAAG;AACjB,cAAA,OAAO,IAAI,WAAW,SAAS,IAAI,KAAK,UAAU,UAAU,IAAI;AAAA,QAAA,OACjE;AACH,cAAI,OAAO,IAAI,KAAK,UAAU,WAAW,MAAM,GAAG,CAAC;AAAA,QAAA;AAAA,MACvD;AAEW,qBAAA,IAAI,OAAO,IAAI,OAAO;AAAA,IAAA;AAIrC,QAAA,mBAAmB,gBAAgB,SAAS,GAAG;AAC3C,UAAA,IAAI,MAAM,KAAK,YAAY,CAAC,KAAK,GAAG,EAAE,SAAS,SAAS,GAAG;AACvD,YAAA,YAAY,IAAI,UAAU,SAAS;AAAA,MAAA;AAE3B,sBAAA,IAAI,KAAK,GAAG;AAAA,IAAA;AAAA,EAChC,CACH;AACO,UAAA,IAAI,6BAA4B,GAAG;AACpC,SAAA;AACX;AAMgB,SAAA,cAAc,QAAO,QAAQ;AACjC,UAAA,IAAI,UAAS,MAAM;AACnB,UAAA,IAAI,UAAS,MAAM;AAE3B,MAAI,QAAQ,CAAC;AACV,MAAA,OAAO,SAAS,OAAO,MAAM,QAAQ,OAAO,MAAM,KAAK,QAAQ,MAAM,KAAK,IAAG;AAC5E,QAAI,cAAc,OAAO,MAAM,KAAK,YAAY,GAAG;AACnD,UAAM,MAAM,IAAI,OAAO,MAAM,KAAK,UAAU,GAAE,WAAW;AACzD,UAAM,OAAO,IAAI,OAAO,MAAM,KAAK,UAAU,cAAY,CAAC;AACpD,UAAA,MAAM,IAAI,OAAO,MAAM;AAAA,EAAA;AAGjC,MAAI,kBAAkB,CAAC;AACnB,MAAA,UAAU,OAAO,UAAU,OAAO,OAAO,mBAAmB,OAAO,OAAO,gBAAgB,SAAS,GAAG;AACtG,sBAAkB,uBAAuB,OAAO,OAAO,iBAAiB,OAAO,QAAQ;AAAA,EAAA;AAE3F,MAAI,SAAS;AAAA,IACT,OAAO,OAAO,OAAO;AAAA,IACrB,OAAO,aAAa,OAAO,MAAM;AAAA,IACjC,MAAM,OAAO,OAAO;AAAA,IACpB,QAAQ;AAAA,EACZ;AAIA,MAAG,QAAO;AACN,QAAG,OAAO,QAAQ,OAAO,QAAQ,cAAa;AAE1C,UAAG,OAAO,mBAAmB,OAAO,gBAAgB,SAAO,GAAE;AACzD,eAAO,MAAM,IAAI;AACjB,eAAO,QAAQ,IAAI,sBAAsB,OAAO,eAAe;AAAA,MAAA;AAE3D,cAAA,IAAI,2BAA0B,MAAM;AAAA,IAAA;AAAA,EAChD;AAGG,SAAA;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;AAMgB,SAAA,WAAW,SAAS,MAAM,sBAAsB;AAC5D,QAAM,IAAI,YAAY;AACtB,MAAI,IAAI,QAAQ,IAAI,KAAK,IAAI;AACnB,UAAA,IAAI,QAAQ,MAAM,IAAI;AAAA,EAAA;AAEhC,SAAOL,oBAAM,OAAO,EAAE,OAAO,GAAG;AACpC;AAyCgB,SAAA,iBAAiB,KAAK,aAAa,eAAe;AAC9D,MAAI,WAAW,YAAY,OAAO,CAAC,SAAS,KAAK,cAAc,SAAS;AACxE,MAAI,eAAe,cAAc,OAAO,CAAC,SAAS,KAAK,cAAc,SAAS;AAE1E,MAAA,YAAY,CAAC,GAAO,oBAAA,IAAI,CAAC,GAAG,UAAU,GAAG,YAAY,CAAC,CAAC;AACvD,MAAA,aAAa,UAAU,SAAS,GAAG;AACzB,cAAA,QAAQ,CAAC,UAAU;AACzB,UAAI,YAAY,MAAM;AAClB,UAAA,QAAQ,CAAC,SAAS;AAClB,YAAI,OAAO,KAAK,IAAI,EAAE,SAAS,SAAS,GAAG;AACvC,cAAI,aAAa,UAAU,QAAQ,cAAc,SAAS,KAAK;AAC3D,gBAAI,KAAK;AACL,kBAAIM,SAAQ,IAAI,QAAQ,OAAO,EAAE;AAC7BA,kBAAAA,UAASC,kBAASD,MAAK,KAAK,CAACE,SAAAA,cAAc,KAAKF,MAAK,CAAC,GAAG;AACzD,uBAAO,KAAKA,MAAK;AAAA,cAAA;AAAA,YACrB;AAEG,mBAAA;AAAA,UAAA,CACV;AACD,eAAK,OAAO,IAAI,gBAAgB,QAAQ,UAAU,CAAC;AAAA,QAAA;AAAA,MACvD,CACH;AAAA,IAAA,CACJ;AAAA,EAAA;AAEE,SAAA;AACX;AAiCgB,SAAA,iBAAiB,WAAU,QAAQ;AAC/C,MAAI,YAAY,OAAO;AACvB,MAAI,YAAY,OAAO;AACvB,MAAI,aAAa,OAAO;AACxB,MAAI,cAAc,UAAU,KAAK,CAAO,SAAA,KAAK,cAAa,MAAM;AAChE,MAAI,cAAc,UAAU,KAAK,CAAO,SAAA,KAAK,cAAa,MAAM;AAChE,MAAI,oBAAoB,WAAW,KAAK,CAAO,SAAA,KAAK,cAAa,MAAM;AACnE,MAAA,eAAe,eAAe,mBAAmB;AACvC,cAAA,QAAQ,CAAC,SAAS;AACpB,UAAA,eAAe,CAAC,KAAK,UAAU,CAAC,EAAE,YAAU,UAAU,GAAG;AACrD,YAAA,SAAS,KAAK,KAAK,QAAQ,GAAG,KAAG,IAAE,KAAK,OAAK,SAAS,KAAK,IAAI;AACnE,YAAI,aAAa,UAAU,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,QAAQ,SAAO,UAAU,CAAC,EAAE,QAAQ,SAAO;AACjG,aAAK,OAAO,KAAK,OAAO,WAAW,QAAQ,UAAU,IAAI;AAAA,MAAA;AAEzD,UAAA,eAAe,CAAC,KAAK,UAAU,CAAC,EAAE,YAAU,UAAU,GAAG;AACrD,YAAA,SAAS,KAAK,KAAK,QAAQ,GAAG,KAAG,IAAE,KAAK,OAAK,SAAS,KAAK,IAAI;AACnE,YAAI,aAAa,UAAU,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,QAAQ,SAAO,UAAU,CAAC,EAAE,QAAQ,SAAO;AACjG,aAAK,OAAO,KAAK,OAAO,WAAW,QAAQ,UAAU,IAAI;AAAA,MAAA;AAE7D,UAAI,qBAAqB,CAAC,KAAK,WAAW,CAAC,EAAE,YAAU,UAAU,KAAK,KAAK,cAAc,OAAO,KAAK,MAAM;AACnG,YAAA,SAAS,KAAK,KAAK,QAAQ,GAAG,KAAG,IAAE,KAAK,OAAK,SAAS,KAAK,IAAI;AACnE,YAAI,aAAa,WAAW,CAAC,EAAE,WAAW,WAAW,CAAC,EAAE,QAAQ,SAAO,WAAW,CAAC,EAAE,QAAQ,SAAO;AACpG,aAAK,OAAO,KAAK,OAAO,WAAW,QAAQ,UAAU,IAAI;AAAA,MAAA;AAAA,IAC7D,CACH;AAAA,EAAA;AAEE,SAAA;AACX;AAKO,SAAS,gBAAgB,OAAO;AACnC,MAAI,OAAO;AACP,QAAI,MAAM,OAAO,KAAK,EAAE,QAAQ,GAAG;AACnC,QAAI,OAAO,IAAI;AACP,UAAA,SAAS,OAAO,KAAK,EAAE,UAAU,MAAM,GAAG,MAAM,MAAM;AACtD,UAAA,OAAO,SAAS,GAAG;AACX,gBAAA,MAAM,QAAQ,CAAC;AAAA,MAAA;AAAA,IAC3B;AAAA,EACJ;AAEG,SAAA;AACX;AAMgB,SAAA,UAAU,QAAO,OAAM;AACnC,QAAM,iBAAe,OAAO,UAAU,SAAS,KAAK,MAAM;AAC1D,QAAM,gBAAc,OAAO,UAAU,SAAS,KAAK,MAAM;AACtD,MAAA,mBAAiB,qBAAqB,kBAAgB,mBAAkB;AACvE,aAAQ,CAAC,KAAI,GAAG,KAAK,OAAO,QAAQ,KAAK,GAAE;AACpC,UAAA,CAAC,OAAO,GAAG,GAAE;AACZ,eAAO,GAAG,IAAE;AAAA,MAAA,OACX;AACD,eAAO,GAAG,IAAE,UAAU,OAAO,GAAG,GAAE,GAAG;AAAA,MAAA;AAAA,IACzC;AAAA,EAEC,WAAA,mBAAiB,oBAAoB,kBAAgB,kBAAiB;AAC3E,aAAQ,CAAC,KAAI,GAAG,KAAK,OAAO,QAAQ,KAAK,GAAE;AACpC,UAAA,OAAO,GAAG,GAAE;AACX,eAAO,GAAG,IAAE,UAAU,OAAO,GAAG,GAAE,GAAG;AAAA,MAAA,OACpC;AACD,eAAO,KAAK,GAAG;AAAA,MAAA;AAAA,IACnB;AAAA,EACJ;AAEG,SAAA;AACX;AAKO,SAAS,eAAe,aAAa;AACxC,MAAI,SAASG,qCAAW,WAAA,QAAQ,IAAI,CAAQ,WAAA,EAAC,QAAO;AACpD,SAAO,cAAc,cAAc;AACvC;AAIgB,SAAA,WAAW,WAAU,QAAQ;AACzC,MAAI,UAAU,EAAE,YAAY,CAAA,GAAI,QAAQ,CAAA,EAAG;AAC3C,MAAI,WAAW,CAAC;AAGhB,MAAI,aAAa,CAAC,SAAS,GAAG,IAAI,IAAI,UAAU,IAAI,CAAC,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC;AAG5E,MAAI,UAAU,CAAC,GAAG,IAAI,IAAI,UAAU,IAAI,CAAC,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC;AAChE,MAAG,OAAO,iBAAiBC,SAAS,SAAA,OAAO,aAAa,GAAE;AACtD,cAAU,QAAQ,MAAM,GAAE,OAAO,aAAa;AAAA,EAAA;AAG1C,UAAA,QAAQ,CAAC,SAAS;AAElB,QAAA,MAAM,UAAU,OAAO,CAAC,SAAS,KAAK,MAAM,KAAK,IAAI;AAEzD,QAAI,YAAY,IAAI,IAAI,CAAC,SAAS,KAAK,OAAO,CAAC;AAE/C,cAAU,QAAQ,IAAI;AACtB,aAAS,KAAK,SAAS;AAAA,EAAA,CAC1B;AACD,UAAQ,aAAa;AACrB,UAAQ,SAAS;AACV,SAAA;AACX;AAMO,SAAS,SAAS,OAAO,YAAW,aAAW,GAAG;AACrD,MAAI,cAAc,WAAW;AACzB,MAAA,UAAU,WAAW,WAAS;AAClC,MAAI,UAAU,EAAC,KAAI,GAAE,KAAI,KAAK,KAAI,KAAM,KAAI,IAAE,KAAM,KAAI,IAAE,KAAO,KAAI,IAAE,IAAO;AAC9E,MAAI,WAAW,cAAY,QAAQ,WAAW,IAAE;AACvC,WAAA,QAAQ,UAAU,QAAQ,OAAO;AACnC,SAAA;AACX;AAMgB,SAAA,UAAU,eAAc,SAAQ,QAAO;;AAChD,MAAA,WAAW,QAAQ,SAAO,GAAE;AAC3B,QAAI,YAAY,cAAc;AAE9B,UAAM,YAAW,YAAO,gBAAP,mBAAoB,KAAK,CAAQ,SAAA,KAAK,cAAc;AAC/D,UAAA,aAAW,0CAAU,YAAV,mBAAmB,aAAY;AAC5C,QAAA,WAAW,cAAc,YAAY;AACrC,QAAA,YAAY,cAAc,aAAa;AACvC,QAAA,aAAa,aAAW,QAAM,UAAQ;AAEtC,QAAA,WAAW,QAAQ,IAAI,CAAO,SAAA;AAC9B,UAAGA,kBAAS,KAAK,UAAU,CAAC,GAAE;AAC1B,eAAO,KAAK,UAAU;AAAA,MAAA,OACrB;AACK,cAAA,QAAQ,OAAO,KAAK,UAAU,EAAE,QAAQ,UAAS,EAAE,CAAC;AACvD,YAAA,OAAO,MAAM,KAAK,GAAE;AACZ,iBAAA;AAAA,QAAA,OACN;AACM,iBAAA;AAAA,QAAA;AAAA,MACX;AAAA,IACJ,CACH;AAED,QAAI,QAAQ;AACT,QAAA,SAAS,SAAO,GAAE;AACjB,UAAG,aAAW,OAAM;AAChB,gBAAQ,SAAS,OAAO,CAAC,MAAK,QAAM,OAAO,KAAI,CAAC;AAAA,MAAA,WAC3C,aAAW,OAAM;AACtB,gBAAQ,KAAK,IAAI,MAAM,MAAK,QAAQ;AAAA,MAAA,WAC/B,aAAW,OAAM;AACtB,gBAAQ,KAAK,IAAI,MAAM,MAAK,QAAQ;AAAA,MAAA,WAC/B,aAAW,WAAU;AAE1B,iBAAU,SAAS,OAAO,CAAC,MAAK,QAAM,OAAO,KAAI,CAAC,IAAI,SAAS,QAAQ,QAAQ,CAAC;AAAA,MAAA;AAAA,IACpF;AAGJ,WAAO,GAAG,QAAQ,KAAK,gBAAgB,KAAK,CAAC;AAAA,EAAA;AAE1C,SAAA;AACX;AAQO,SAAS,mBAAmB,UAAS,aAAY,QAAO,WAAU;AAErE,MAAI,OAAO,iBAAiB;AAEpB,QAAA,iBAAiB,OAAO,gBAAgB;AAC5C,QAAI,OAAO,eAAe,OAAK,eAAe,OAAK;AACjC,mBAAe,cAAY,eAAe,cAAY;AAC5D,gBAAA,OAAO,QAAQ,CAAC,SAAS;AAC9B,UAAA,KAAK,cAAc,KAAI;AACL,yBAAA,OAAO,gBAAgB,OAAO;AAAA,MAAA;AAGnD,UAAI,gBAAgB,SAAS,QAAQ,KAAK,KAAG,KAAG,YAAY,mBAAiB;AAEzE,UAAA;AACJ,cAAO,UAAS;AAAA,QACZ,KAAK;AAAA,QACL,KAAK;AACD,iBAAO,KAAK,OAAO;AACL,wBAAA;AAAA,YACV,QAAO;AAAA,cACH,WAAW,CAAC,UAAU;AAClB,oBAAI,YAAY,eAAe,YAAY,WAAW,GAAG,SAAS,OAAO,cAAc,CAAC,GAAG,IAAI,KAAK,GAAG,IAAI,GAAG,SAAS,OAAO,cAAc,CAAC;AACtI,uBAAA;AAAA,cAAA;AAAA,YACX;AAAA,UAER;AACA;AAAA,QACJ;AACkB,wBAAA;AAAA,YACV,OAAO;AAAA,cACH,UAAS,SAAS,QAAQ,QAAQ,KAAG,IAAE,WAAS;AAAA,cAChD,WAAW,CAAC,WAAW;AACnB,oBAAI,OAAO,OAAO;AAClB,oBAAI,YAAY;AAChB,oBAAG,QAAQ,OAAM;AACA,8BAAA,GAAG,OAAO,QAAQ,GAAG;AAAA,gBAAA;AAEtC,oBAAG,QAAQ,UAAS;AACH,8BAAA,GAAG,OAAO,QAAQ,GAAG;AAAA,gBAAA;AAEtC,oBAAI,QAAQ;AACZ,oBAAG,MAAM,QAAQ,OAAO,KAAK,GAAE;AACnB,0BAAA,QAAQ,YAAU,OAAO,MAAM,CAAC,IAAE,OAAO,MAAM,OAAO,cAAc,CAAC;AAAA,gBAAA,OAC5E;AACD,0BAAQ,OAAO;AAAA,gBAAA;AAGnB,6BAAa,eAAe,YAAY,WAAW,GAAG,SAAS,OAAO,cAAc,CAAC,GAAG,IAAI,KAAK,GAAG,IAAI,GAAG,SAAS,OAAO,cAAc,CAAC;AACnI,uBAAA;AAAA,cAAA;AAAA,YACX;AAAA,UAER;AAAA,MAAA;AAER,gBAAU,MAAM,mBAAI,YAAY;AAAA,IAAA,CACnC;AAEG,QAAA,gBAAgB,OAAO,gBAAgB;AAC3C,QAAG,cAAc,aAAa,aAAa,UAAU,SAAO,GAAE;AAEtD,UAAA,WAAW,UAAU,OAAO,CAAA,SAAO,CAAC,KAAK,cAAc,KAAK,cAAc,GAAI;AAClF,UAAI,aAAa,cAAc,QAAM,UAAU,eAAc,UAAS,MAAM,IAAE;AAC9E,aAAO,OAAO,YAAY,OAAO,EAAC,MAAK,YAAW;AAAA,IAAA;AAAA,EACtD;AAIJ,gBAAc,YAAY,WAAW;AAC9B,SAAA;AACX;AAQO,SAAS,kBAAkB,UAAU,aAAa,QAAQ,WAAW;;AAI5D,cAAA,OAAO,EAAC,cAAc,MAAK,KAAK,IAAG,QAAQ,IAAG,MAAM,GAAE,OAAO,EAAC;AAC1E,QAAM,EAAC,OAAO,OAAO,OAAU,IAAA;AAC/B,MAAG,OAAM;AACL,UAAM,EAAC,MAAM,gBAAgB,CAAA,EAAM,IAAA;AACnC,QAAG,MAAK;AACJ,kBAAY,KAAK,OAAO;AAAA,IAAA;AAAA,EAC5B;AAEJ,MAAG,OAAM;AACL,UAAM,EAAC,MAAM,gBAAgB,CAAA,EAAO,IAAA;AACpC,QAAG,MAAK;AACE,YAAA,EAAC,WAAW,GAAA,IAAM;AAExB,kBAAY,KAAK,SAAS,KAAK,UAAU,WAAW,KAAK;AAAA,IAAA;AAAA,EAC5D;AAEF,OAAA,iBAAY,UAAZ,mBAAmB,MAAK;AAGvB,UAAM,EAAC,YAAY,OAAO,YAAY;AAChC,UAAA,EAAE,WAAW,GAAA,IAAO;AAC1B,gBAAY,KAAK,OAAO;AACrB,QAAA,YAAY,KAAK,MAAM,KAAI;AAC1B,kBAAY,KAAK,MAAM;AAAA,IAAA;AAAA,EAC3B;AAGD,MAAA,OAAO,UAAS,GAAE;AACjB,gBAAY,KAAK,SAAS;AAAA,EAAA;AAMtB,UAAA,IAAI,oBAAmB,WAAW;AACnC,SAAA;AACX;AAKA,SAAS,YAAY,aAAY;AAE7B,cAAY,OAAO;AAAA,IACf,MAAK;AAAA,IACL,OAAM;AAAA,IACN,KAAI;AAAA,IACJ,QAAO;AAAA,EACX;AAEA,cAAY,SAAS;AAAA,IACjB,QAAO;AAAA,EACX;AAEY,cAAA,YAAY,YAAY,QAAQ,eAAc;AACnD,SAAA;AACX;AAKO,SAAS,eAAe,OAAO;AAC9B,MAAA,gBAAgB,CAAC,sBAAqB,sBAAqB,qBAAoB,sBAAqB,sBAAqB,sBAAqB,sBAAqB,qBAAoB,sBAAqB,oBAAoB;AACpO,MAAI,SAAS,CAAC,sBAAqB,qBAAoB,qBAAoB,sBAAqB,sBAAqB,sBAAqB,sBAAqB,qBAAoB,sBAAqB,sBAAqB,GAAG,aAAa;AAC7O,SAAO,SAAS,QAAM,KAAG,OAAO,KAAK,IAAG,OAAO,KAAK,MAAO,KAAK,OAAO,KAAG,OAAO,SAAO,EAAG,CAAC;AAChG;AAKO,SAAS,YAAY,QAAQ;AAChC,MAAI,YAAY,OAAO;AACnB,MAAA,MAAMC,cAAAA,UAAU,OAAO,UAAU;AAErC,MAAI,UAAU,CAAC;AACX,MAAA,aAAa,UAAU,SAAS,GAAG;AACzB,cAAA,QAAQ,CAAC,SAAS;AACxB,cAAQ,KAAK,KAAK,IAAI,KAAK,cAAc;AAAA,IAAA,CAC5C;AAAA,EAAA;AAEL,MAAI,MAAM;AAEV,MAAI,IAAI,KAAK,OAAO,UAAU,GAAG;AAC7B,UAAM,IAAI,MAAM,GAAG,EAAE,CAAC;AAAA,EAAA;AAEnB,SAAA,EAAE,SAAS,IAAI;AAC1B;AAKO,SAAS,eAAe,aAAa;AACxC,MAAG,YAAY,UAAS;AAEpB,QAAI,cAAc,YAAY;AAE9B,QAAI,cAAc,CAAC;AACP,gBAAA,QAAQ,CAAC,MAAM;AAEnB,UAAA,OAAO,EAAE,WAAW;AAExB,kBAAY,IAAI,IAAI;AAAA,QAChB,QAAQ,EAAE,WAAW;AAAA,QACrB,QAAQ,EAAE,WAAW;AAAA,MACzB;AAAA,IAAA,CACH;AACM,WAAA;AAAA,EAAA;AAEJ,SAAA;AACX;AA6VO,SAAS,aAAc,QAAO;AACjC,MAAG,QAAO;AACN,QAAI,OAAO,WAAW,SAAS,KAAK,OAAO,WAAW,UAAU,GAAG;AACxD,aAAA;AAAA,IAAA;AAEP,QAAA,OAAO,WAAW,uBAAuB,GAAG;AACrC,aAAA;AAAA,IAAA,OACJ;AACM,eAAA,OAAO,QAAQ,iBAAiB,KAAG,IAAE,OAAO,WAAW,mBAAkB,OAAO,IAAE;AAClF,eAAA,OAAO,QAAQ,iBAAiB,KAAG,IAAE,OAAO,WAAW,mBAAkB,OAAO,IAAE;AACvF,UAAA,MAAM,UAAU,OAAO,QAAQ,UAAU,KAAG,IAAE,OAAO,WAAW,YAAW,OAAO,IAAE;AACxF,aAAO,GAAGC,YAAA,cAAA,CAAe,YAAY,GAAG;AAAA,IAAA;AAAA,EAC5C;AAER;AAKO,SAAS,eAAe,KAAK;AAE1B,QAAA,kBAAkB,OAAO,SAAS;AAElC,QAAA,SAAS,IAAI,IAAI,GAAG;AAC1B,QAAM,cAAc,OAAO;AAG3B,QAAM,iBAAiB,gBAAgB,WAAW,OAAO,KAAM,mBAAmB;AAE3E,SAAA;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;AAIgB,SAAA,cAAc,WAAU,aAAa;AACjD,MAAI,eAAe,OAAO,KAAK,WAAW,EAAE,SAAS,GAAG;AACpD,WAAO,KAAK,WAAW,EAAE,QAAQ,CAAC,SAAS;AACvC,UAAI,YAAY,IAAI,KAAKP,iBAAQ,YAAY,IAAI,CAAC,GAAE;AACtC,kBAAA,QAAQ,CAAC,SAAS;AACxB,cAAI,MAAM,YAAY,IAAI,EAAE,OAAO,CAAC,SAAS,KAAK,UAAU,KAAK,IAAI,IAAI,EAAE;AAC3E,eAAK,IAAI,IAAI,OAAO,IAAI,SAAS,IAAI,IAAI,CAAC,EAAE,MAAM,IAAI,KAAK,IAAI;AAAA,QAAA,CAClE;AAAA,MAAA;AAAA,IACL,CACH;AAAA,EAAA;AAEE,SAAA;AACX;AAEa,MAAA,eAAe,CAAC,QAAQ;AACjC,MAAI,SAAS;AAAA,IACT,KAAK;AAAA,IACL,QAAQ,CAAA;AAAA,EACZ;AACI,MAAA,OAAO,IAAI,MAAM,GAAG;AACjB,SAAA,MAAM,KAAK,CAAC;AACf,MAAA,SAAS,KAAK,CAAC;AACnB,MAAI,QAAQ;AACJQ,QAAAA,QAAO,OAAO,MAAM,GAAG;AAC3BA,UAAK,QAAQ,CAAO,QAAA;AACZ,UAAA,MAAM,IAAI,MAAM,GAAG;AACnB,UAAA,QAAQ,IAAI,CAAC;AACb,UAAA,QAAQ,IAAI,CAAC;AACjB,aAAO,OAAO,KAAK,IAAI,mBAAmB,KAAK;AAAA,IAAA,CAClD;AAAA,EAAA;AAEE,SAAA;AACX;;;;;;;;;;;;;;;;;;"}