{"version": 3, "file": "wd-index-bar.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-index-bar/wd-index-bar.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1pbmRleC1iYXIvd2QtaW5kZXgtYmFyLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"wd-index-bar\" :id=\"indexBarId\">\n\n\n\n      <scroll-view :scrollTop=\"scrollState.scrollTop\" :scroll-y=\"true\" class=\"wd-index-bar__content\" @scroll=\"hanleScroll\">\n        <slot></slot>\n      </scroll-view>\n      <view\n        class=\"wd-index-bar__sidebar\"\n        @touchstart.stop.prevent=\"handleTouchStart\"\n        @touchmove.stop.prevent=\"handleTouchMove\"\n        @touchend.stop.prevent=\"handleTouchEnd\"\n        @touchcancel.stop.prevent=\"handleTouchEnd\"\n      >\n        <view class=\"wd-index-bar__index\" :class=\"{ 'is-active': item.index === state.activeIndex }\" v-for=\"item in children\" :key=\"item.index\">\n          {{ item.index }}\n        </view>\n      </view>\n\n\n\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport type { AnchorIndex } from './type'\nimport { indexBarInjectionKey, indexBarProps } from './type'\nimport { ref, getCurrentInstance, onMounted, reactive, nextTick, watch } from 'vue'\nimport { getRect, isDef, uuid, pause } from '../common/util'\nimport { useChildren } from '../composables/useChildren'\n\nconst props = defineProps(indexBarProps)\n\nconst indexBarId = ref<string>(`indexBar${uuid()}`)\n\nconst { proxy } = getCurrentInstance()!\n\nconst state = reactive({\n  activeIndex: null as AnchorIndex | null\n})\n\nconst { linkChildren, children } = useChildren(indexBarInjectionKey)\n\nlinkChildren({ props, anchorState: state })\n\nwatch(\n  () => children,\n  (newValue) => {\n    if (!newValue.length) {\n      state.activeIndex = null // 或者设置为一个默认值，如第一个子项的索引\n      return\n    }\n\n    if (!isDef(state.activeIndex) || !newValue.find((item) => item.index === state.activeIndex)) {\n      state.activeIndex = newValue[0].index\n    }\n  },\n  { deep: true, immediate: true }\n)\n\nconst scrollState = reactive({\n  scrollTop: 0, // 即将滚动到的位置\n  prevScrollTop: 0, // 上次记录的位置\n  // 滚动距离\n  touching: false\n})\n\n// 组件距离页面顶部的高度\nlet offsetTop = 0\nlet sidebarInfo = {\n  // 侧边栏距离顶部的高度\n  offsetTop: 0,\n  // 高度固定24px\n  indexHeight: 24\n}\n\nfunction init() {\n  setTimeout(() => {\n    Promise.all([\n      getRect(`#${indexBarId.value}`, false, proxy),\n      getRect('.wd-index-bar__sidebar', false, proxy),\n      getRect('.wd-index-bar__index', false, proxy)\n    ]).then(([bar, sidebar, index]) => {\n      offsetTop = bar.top!\n      sidebarInfo.offsetTop = sidebar.top!\n      sidebarInfo.indexHeight = index.height!\n    })\n  }, 100)\n}\n\nonMounted(() => {\n  init()\n})\n\nfunction hanleScroll(scrollEvent: any) {\n  if (scrollState.touching) {\n    return\n  }\n  const { detail } = scrollEvent\n  const scrolltop = Math.floor(detail.scrollTop)\n  const anchor = children.find((item, index) => {\n    if (!isDef(children[index + 1])) return true\n    if (item.$.exposed!.top.value - offsetTop <= scrolltop && children[index + 1].$.exposed!.top.value - offsetTop > scrolltop) return true\n    return false\n  })\n  if (isDef(anchor) && state.activeIndex !== anchor.index) {\n    state.activeIndex = anchor.index\n  }\n  scrollState.prevScrollTop = scrolltop\n}\n\nfunction getAnchorByPageY(pageY: number) {\n  const y = pageY - sidebarInfo.offsetTop\n  let idx = Math.floor(y / sidebarInfo.indexHeight)\n  if (idx < 0) idx = 0\n  else if (idx > children.length - 1) idx = children.length - 1\n  return children[idx]\n}\n\nfunction handleTouchStart() {\n  scrollState.touching = true\n}\n\nfunction handleTouchMove(e: TouchEvent) {\n  const clientY = e.touches[0].pageY\n  if (state.activeIndex === getAnchorByPageY(clientY).index) {\n    return\n  }\n  state.activeIndex = getAnchorByPageY(clientY).index\n  setScrollTop(getAnchorByPageY(clientY).$.exposed!.top.value - offsetTop)\n}\n\nasync function handleTouchEnd(e: TouchEvent) {\n  const clientY = e.changedTouches[0].pageY\n  state.activeIndex = getAnchorByPageY(clientY).index\n  setScrollTop(getAnchorByPageY(clientY).$.exposed!.top.value - offsetTop)\n  await pause()\n  scrollState.touching = false\n}\n\nfunction setScrollTop(top: number) {\n  if (scrollState.scrollTop === top) {\n    scrollState.scrollTop = scrollState.prevScrollTop\n    nextTick(() => {\n      scrollState.scrollTop = top\n    })\n  } else {\n    scrollState.scrollTop = top\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-index-bar/wd-index-bar.vue'\nwx.createComponent(Component)"], "names": ["ref", "uuid", "getCurrentInstance", "reactive", "useChildren", "indexBarInjectionKey", "watch", "isDef", "getRect", "onMounted", "pause", "nextTick"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA,UAAM,QAAQ;AAEd,UAAM,aAAaA,cAAAA,IAAY,WAAWC,cAAA,KAAA,CAAM,EAAE;AAE5C,UAAA,EAAE,MAAM,IAAIC,iCAAmB;AAErC,UAAM,QAAQC,cAAAA,SAAS;AAAA,MACrB,aAAa;AAAA,IAAA,CACd;AAED,UAAM,EAAE,cAAc,aAAaC,cAAAA,YAAYC,cAAAA,oBAAoB;AAEnE,iBAAa,EAAE,OAAO,aAAa,MAAA,CAAO;AAE1CC,kBAAA;AAAA,MACE,MAAM;AAAA,MACN,CAAC,aAAa;AACR,YAAA,CAAC,SAAS,QAAQ;AACpB,gBAAM,cAAc;AACpB;AAAA,QAAA;AAGF,YAAI,CAACC,cAAAA,MAAM,MAAM,WAAW,KAAK,CAAC,SAAS,KAAK,CAAC,SAAS,KAAK,UAAU,MAAM,WAAW,GAAG;AACrF,gBAAA,cAAc,SAAS,CAAC,EAAE;AAAA,QAAA;AAAA,MAEpC;AAAA,MACA,EAAE,MAAM,MAAM,WAAW,KAAK;AAAA,IAChC;AAEA,UAAM,cAAcJ,cAAAA,SAAS;AAAA,MAC3B,WAAW;AAAA;AAAA,MACX,eAAe;AAAA;AAAA;AAAA,MAEf,UAAU;AAAA,IAAA,CACX;AAGD,QAAI,YAAY;AAChB,QAAI,cAAc;AAAA;AAAA,MAEhB,WAAW;AAAA;AAAA,MAEX,aAAa;AAAA,IACf;AAEA,aAAS,OAAO;AACd,iBAAW,MAAM;AACf,gBAAQ,IAAI;AAAA,UACVK,sBAAQ,IAAI,WAAW,KAAK,IAAI,OAAO,KAAK;AAAA,UAC5CA,sBAAQ,0BAA0B,OAAO,KAAK;AAAA,UAC9CA,sBAAQ,wBAAwB,OAAO,KAAK;AAAA,QAAA,CAC7C,EAAE,KAAK,CAAC,CAAC,KAAK,SAAS,KAAK,MAAM;AACjC,sBAAY,IAAI;AAChB,sBAAY,YAAY,QAAQ;AAChC,sBAAY,cAAc,MAAM;AAAA,QAAA,CACjC;AAAA,SACA,GAAG;AAAA,IAAA;AAGRC,kBAAAA,UAAU,MAAM;AACT,WAAA;AAAA,IAAA,CACN;AAED,aAAS,YAAY,aAAkB;AACrC,UAAI,YAAY,UAAU;AACxB;AAAA,MAAA;AAEI,YAAA,EAAE,WAAW;AACnB,YAAM,YAAY,KAAK,MAAM,OAAO,SAAS;AAC7C,YAAM,SAAS,SAAS,KAAK,CAAC,MAAM,UAAU;AAC5C,YAAI,CAACF,cAAAA,MAAM,SAAS,QAAQ,CAAC,CAAC;AAAU,iBAAA;AACxC,YAAI,KAAK,EAAE,QAAS,IAAI,QAAQ,aAAa,aAAa,SAAS,QAAQ,CAAC,EAAE,EAAE,QAAS,IAAI,QAAQ,YAAY;AAAkB,iBAAA;AAC5H,eAAA;AAAA,MAAA,CACR;AACD,UAAIA,cAAAA,MAAM,MAAM,KAAK,MAAM,gBAAgB,OAAO,OAAO;AACvD,cAAM,cAAc,OAAO;AAAA,MAAA;AAE7B,kBAAY,gBAAgB;AAAA,IAAA;AAG9B,aAAS,iBAAiB,OAAe;AACjC,YAAA,IAAI,QAAQ,YAAY;AAC9B,UAAI,MAAM,KAAK,MAAM,IAAI,YAAY,WAAW;AAChD,UAAI,MAAM;AAAS,cAAA;AAAA,eACV,MAAM,SAAS,SAAS;AAAG,cAAM,SAAS,SAAS;AAC5D,aAAO,SAAS,GAAG;AAAA,IAAA;AAGrB,aAAS,mBAAmB;AAC1B,kBAAY,WAAW;AAAA,IAAA;AAGzB,aAAS,gBAAgB,GAAe;AACtC,YAAM,UAAU,EAAE,QAAQ,CAAC,EAAE;AAC7B,UAAI,MAAM,gBAAgB,iBAAiB,OAAO,EAAE,OAAO;AACzD;AAAA,MAAA;AAEI,YAAA,cAAc,iBAAiB,OAAO,EAAE;AAC9C,mBAAa,iBAAiB,OAAO,EAAE,EAAE,QAAS,IAAI,QAAQ,SAAS;AAAA,IAAA;AAGzE,aAAe,eAAe,GAAe;AAAA;AAC3C,cAAM,UAAU,EAAE,eAAe,CAAC,EAAE;AAC9B,cAAA,cAAc,iBAAiB,OAAO,EAAE;AAC9C,qBAAa,iBAAiB,OAAO,EAAE,EAAE,QAAS,IAAI,QAAQ,SAAS;AACvE,cAAMG,oBAAM;AACZ,oBAAY,WAAW;AAAA,MAAA;AAAA;AAGzB,aAAS,aAAa,KAAa;AAC7B,UAAA,YAAY,cAAc,KAAK;AACjC,oBAAY,YAAY,YAAY;AACpCC,sBAAAA,WAAS,MAAM;AACb,sBAAY,YAAY;AAAA,QAAA,CACzB;AAAA,MAAA,OACI;AACL,oBAAY,YAAY;AAAA,MAAA;AAAA,IAC1B;;;;;;;;;;;;;;;;;;;;;;ACpJF,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}