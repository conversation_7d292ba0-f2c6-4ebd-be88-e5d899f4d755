/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 混合宏
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/**
 * 辅助函数
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/* 转换成字符串 */
/* 判断是否存在 Modifier */
/* 判断是否存在伪类 */
/**
 * 主题色切换
 * @params $theme-color 主题色
 * @params $type 变暗’dark‘ 变亮 'light'
 * @params $mix-color 自己设置的混色
 */
/**
 * 颜色结果切换， 如果开启线性渐变色 使用渐变色，如果没有开启，那么使用主题色
 * @params $open-linear 是否开启线性渐变色
 * @params $deg 渐变色角度
 * @params $theme-color 当前配色
 * @params [Array] $set 主题色明暗设置，与 $color-list 数量对应
 * @params [Array] $color-list 渐变色顺序， $color-list 和 $per-list 数量相同
 * @params [Array] $per-list 渐变色比例
 */
/**
  * BEM，定义块（b)
  */
/* 定义元素（e），对于伪类，会自动将 e 嵌套在 伪类 底下 */
/* 此方法用于生成穿透样式 */
/* 定义元素（e），对于伪类，会自动将 e 嵌套在 伪类 底下 */
/* 定义状态（m） */
/* 定义状态（m） */
/* 对于需要需要嵌套在 m 底下的 e，调用这个混合宏，一般在切换整个组件的状态，如切换颜色的时候 */
/* 状态，生成 is-$state 类名 */
/**
  * 常用混合宏
  */
/* 单行超出隐藏 */
/* 多行超出隐藏 */
/* 清除浮动 */
/* 0.5px 边框 指定方向*/
/* 0.5px 边框 环绕 */
/**
  * 三角形实现尖角样式，适用于背景透明情况
  * @param $size 三角形高，底边为 $size * 2
  * @param $bg 三角形背景颜色
  */
/**
  * 正方形实现尖角样式，适用于背景不透明情况
  * @param $size 正方形边长
  * @param $bg 正方形背景颜色
  * @param $z-index z-index属性值，不得大于外部包裹器
  * @param $box-shadow 阴影
*/
/**
 * 辅助函数
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/* 转换成字符串 */
/* 判断是否存在 Modifier */
/* 判断是否存在伪类 */
/**
 * 主题色切换
 * @params $theme-color 主题色
 * @params $type 变暗’dark‘ 变亮 'light'
 * @params $mix-color 自己设置的混色
 */
/**
 * 颜色结果切换， 如果开启线性渐变色 使用渐变色，如果没有开启，那么使用主题色
 * @params $open-linear 是否开启线性渐变色
 * @params $deg 渐变色角度
 * @params $theme-color 当前配色
 * @params [Array] $set 主题色明暗设置，与 $color-list 数量对应
 * @params [Array] $color-list 渐变色顺序， $color-list 和 $per-list 数量相同
 * @params [Array] $per-list 渐变色比例
 */
/**
 * UI规范基础变量
 */
/*----------------------------------------- Theme color. start ----------------------------------------*/
/* 主题颜色 */
/* 辅助色 */
/* 文字颜色（默认浅色背景下 */
/* 暗黑模式 */
/* 图形颜色 */
/*----------------------------------------- Theme color. end -------------------------------------------*/
/*-------------------------------- Theme color application size.  start --------------------------------*/
/* 文字字号 */
/* 文字字重 */
/* 尺寸 */
/*-------------------------------- Theme color application size.  end --------------------------------*/
/* component var */
/* action-sheet */
/* badge */
/* button */
/* cell */
/* calendar */
/* checkbox */
/* collapse */
/* divider */
/* drop-menu */
/* input-number */
/* input */
/* textarea */
/* loadmore */
/* message-box */
/* notice-bar */
/* pagination */
/* picker */
/* col-picker */
/* overlay */
/* popup */
/* progress */
/* radio */
/* search */
/* slider */
/* sort-button */
/* steps */
/* switch */
/* tabs */
/* tag */
/* toast */
/* loading */
/* tooltip */
/* popover */
/* grid-item */
/* statustip */
/* card */
/* upload */
/* curtain */
/* notify */
/* skeleton */
/* circle */
/* swiper */
/* swiper-nav */
/* segmented */
/* tabbar */
/* tabbar-item */
/* navbar */
/* navbar-capsule */
/* table */
/* sidebar */
/* sidebar-item */
/* fab */
/* count-down */
/* keyboard */
/* number-keyboard */
/* passwod-input */
/* form-item */
/* backtop */
/* index-bar */
/* text */
/* video-preview */
/* img-cropper */
/* floating-panel */
/* signature */
.wot-theme-dark .wd-checkbox__shape.data-v-d444ca85 {
  background: transparent;
  border-color: var(--wot-checkbox-border-color, #dcdcdc);
  color: var(--wot-checkbox-check-color, var(--wot-color-white, rgb(255, 255, 255)));
}
.wot-theme-dark .wd-checkbox__label.data-v-d444ca85 {
  color: var(--wot-dark-color, var(--wot-color-white, rgb(255, 255, 255)));
}
.wot-theme-dark .wd-checkbox.is-disabled .wd-checkbox__shape.data-v-d444ca85 {
  border-color: var(--wot-dark-color-gray, var(--wot-color-secondary, #595959));
  background: var(--wot-dark-background4, #323233);
}
.wot-theme-dark .wd-checkbox.is-disabled .wd-checkbox__label.data-v-d444ca85 {
  color: var(--wot-dark-color-gray, var(--wot-color-secondary, #595959));
}
.wot-theme-dark .wd-checkbox.is-disabled.data-v-d444ca85 .wd-checkbox__check {
  color: var(--wot-dark-color-gray, var(--wot-color-secondary, #595959));
}
.wot-theme-dark .wd-checkbox.is-disabled.is-checked .wd-checkbox__shape.data-v-d444ca85 {
  color: var(--wot-dark-color-gray, var(--wot-color-secondary, #595959));
}
.wot-theme-dark .wd-checkbox.is-disabled.is-checked .wd-checkbox__label.data-v-d444ca85 {
  color: var(--wot-dark-color-gray, var(--wot-color-secondary, #595959));
}
.wot-theme-dark .wd-checkbox.is-disabled.is-button .wd-checkbox__label.data-v-d444ca85 {
  border-color: #c8c9cc;
  background: #3a3a3c;
  color: var(--wot-dark-color-gray, var(--wot-color-secondary, #595959));
}
.wot-theme-dark .wd-checkbox.is-disabled.is-button.is-checked .wd-checkbox__label.data-v-d444ca85 {
  border-color: #c8c9cc;
  background: #3a3a3c;
  color: #c8c9cc;
}
.wot-theme-dark .wd-checkbox.is-button .wd-checkbox__label.data-v-d444ca85 {
  background-color: var(--wot-dark-background, #131313);
}
.wot-theme-dark .wd-checkbox.is-button.is-checked .wd-checkbox__label.data-v-d444ca85 {
  background-color: var(--wot-dark-background2, #1b1b1b);
}
.wd-checkbox.data-v-d444ca85 {
  display: block;
  margin-bottom: var(--wot-checkbox-margin, 10px);
  font-size: 0;
  -webkit-tap-highlight-color: transparent;
  line-height: 1.2;
}
.wd-checkbox.is-last-child.data-v-d444ca85 {
  margin-bottom: 0;
}
.wd-checkbox__shape.data-v-d444ca85 {
  position: relative;
  display: inline-block;
  width: var(--wot-checkbox-size, 16px);
  height: var(--wot-checkbox-size, 16px);
  border: 2px solid var(--wot-checkbox-border-color, #dcdcdc);
  border-radius: 50%;
  color: var(--wot-checkbox-check-color, var(--wot-color-white, rgb(255, 255, 255)));
  background: var(--wot-checkbox-bg, var(--wot-color-white, rgb(255, 255, 255)));
  vertical-align: middle;
  transition: background 0.2s;
  box-sizing: border-box;
}
.wd-checkbox__shape.is-square.data-v-d444ca85 {
  border-radius: var(--wot-checkbox-square-radius, 4px);
}
.wd-checkbox__input.data-v-d444ca85 {
  position: absolute;
  width: 0;
  height: 0;
  margin: 0;
  opacity: 0;
}
.data-v-d444ca85  .wd-checkbox__btn-check {
  display: inline-block;
  font-size: var(--wot-checkbox-icon-size, 14px);
  margin-right: 4px;
  vertical-align: middle;
}
.wd-checkbox__txt.data-v-d444ca85 {
  display: inline-block;
  vertical-align: middle;
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.wd-checkbox__label.data-v-d444ca85 {
  position: relative;
  display: inline-block;
  margin-left: var(--wot-checkbox-label-margin, 9px);
  vertical-align: middle;
  font-size: var(--wot-checkbox-label-fs, 14px);
  color: var(--wot-checkbox-label-color, rgba(0, 0, 0, 0.85));
}
.data-v-d444ca85  .wd-checkbox__check {
  color: var(--wot-checkbox-check-color, var(--wot-color-white, rgb(255, 255, 255)));
  font-size: var(--wot-checkbox-icon-size, 14px);
  opacity: 0;
  transition: opacity 0.2s;
}
.wd-checkbox.is-checked .wd-checkbox__shape.data-v-d444ca85 {
  color: var(--wot-checkbox-checked-color, var(--wot-color-theme, #4d80f0));
  background: currentColor;
  border-color: currentColor;
}
.wd-checkbox.is-checked.data-v-d444ca85 .wd-checkbox__check {
  opacity: 1;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.wd-checkbox.is-button.data-v-d444ca85 {
  display: inline-block;
  margin-bottom: 0;
  margin-right: var(--wot-checkbox-margin, 10px);
  vertical-align: top;
  font-size: var(--wot-checkbox-button-font-size, 14px);
}
.wd-checkbox.is-button.is-last-child.data-v-d444ca85 {
  margin-right: 0;
}
.wd-checkbox.is-button .wd-checkbox__shape.data-v-d444ca85 {
  width: 0;
  height: 0;
  overflow: hidden;
  opacity: 0;
  border: none;
}
.wd-checkbox.is-button .wd-checkbox__label.data-v-d444ca85 {
  display: inline-flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  min-width: var(--wot-checkbox-button-min-width, 78px);
  height: var(--wot-checkbox-button-height, 32px);
  font-size: var(--wot-checkbox-button-font-size, 14px);
  margin-left: 0;
  padding: 5px 15px;
  border: 1px solid var(--wot-checkbox-button-border, #f5f5f5);
  background-color: var(--wot-checkbox-button-bg, rgba(0, 0, 0, 0.04));
  border-radius: var(--wot-checkbox-button-radius, 16px);
  transition: color 0.2s, border 0.2s;
  box-sizing: border-box;
}
.wd-checkbox.is-button.is-checked .wd-checkbox__label.data-v-d444ca85 {
  color: var(--wot-checkbox-checked-color, var(--wot-color-theme, #4d80f0));
  background-color: var(--wot-checkbox-bg, var(--wot-color-white, rgb(255, 255, 255)));
  border-color: var(--wot-checkbox-checked-color, var(--wot-color-theme, #4d80f0));
  border-color: currentColor;
}
.wd-checkbox.is-inline.data-v-d444ca85 {
  display: inline-block;
  margin-bottom: 0;
  margin-right: var(--wot-checkbox-margin, 10px);
}
.wd-checkbox.is-inline.is-last-child.data-v-d444ca85 {
  margin-right: 0;
}
.wd-checkbox.is-disabled .wd-checkbox__shape.data-v-d444ca85 {
  border-color: var(--wot-checkbox-border-color, #dcdcdc);
  background: var(--wot-checkbox-disabled-check-bg, rgba(0, 0, 0, 0.15));
}
.wd-checkbox.is-disabled .wd-checkbox__label.data-v-d444ca85 {
  color: var(--wot-checkbox-disabled-label-color, rgba(0, 0, 0, 0.25));
}
.wd-checkbox.is-disabled.is-checked .wd-checkbox__shape.data-v-d444ca85 {
  color: var(--wot-checkbox-disabled-check-color, rgba(0, 0, 0, 0.15));
}
.wd-checkbox.is-disabled.is-checked .wd-checkbox__label.data-v-d444ca85 {
  color: var(--wot-checkbox-disabled-label-color, rgba(0, 0, 0, 0.25));
}
.wd-checkbox.is-disabled.is-button .wd-checkbox__label.data-v-d444ca85 {
  background: var(--wot-checkbox-disabled-color, rgba(0, 0, 0, 0.04));
  border-color: var(--wot-checkbox-button-border, #f5f5f5);
  color: var(--wot-checkbox-disabled-label-color, rgba(0, 0, 0, 0.25));
}
.wd-checkbox.is-disabled.is-button.is-checked .wd-checkbox__label.data-v-d444ca85 {
  border-color: var(--wot-checkbox-button-disabled-border, rgba(0, 0, 0, 0.15));
}
.wd-checkbox.is-cell-box.data-v-d444ca85 {
  padding: 13px 15px;
  margin: 0;
}
.wd-checkbox.is-cell-box.is-large.data-v-d444ca85 {
  padding: 14px 15px;
}
.wd-checkbox.is-button-box.data-v-d444ca85 {
  display: inline-flex;
  width: 33.3333%;
  padding: 12px 12px 0 0;
  box-sizing: border-box;
}
.wd-checkbox.is-button-box .wd-checkbox__label.data-v-d444ca85 {
  width: 100%;
}
.wd-checkbox.is-button-box.data-v-d444ca85:last-child::after {
  content: "";
  display: table;
  clear: both;
}
.wd-checkbox.is-large .wd-checkbox__shape.data-v-d444ca85 {
  width: var(--wot-checkbox-large-size, 18px);
  height: var(--wot-checkbox-large-size, 18px);
  font-size: var(--wot-checkbox-large-size, 18px);
}
.wd-checkbox.is-large .wd-checkbox__label.data-v-d444ca85 {
  font-size: var(--wot-checkbox-large-label-fs, 16px);
}