{"version": 3, "file": "people.js", "sources": ["../../../../../src/pages/user/people.vue", "../../../../../uniPage:/cGFnZXMvdXNlci9wZW9wbGUudnVl"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n  <PageLayout :navLeftArrow=\"false\" navLeftText=\"\">\r\n    <view class=\"user-card\">\r\n      <view class=\"user-info\">\r\n        <view class=\"avatar-wrapper\">\r\n\r\n          <!-- 微信小程序直接使用button的chooseAvatar功能 -->\r\n          <button class=\"avatar-button\" open-type=\"chooseAvatar\" @chooseavatar=\"onChooseWechatAvatar\">\r\n            <wd-img width=\"70\" height=\"70\" :round=\"true\" :radius=\"35\" :src=\"avatarSrc\"></wd-img>\r\n          </button>\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n        </view>\r\n        <view class=\"user-details\">\r\n          <view class=\"username\">{{ userStore.userInfo.realname }}</view>\r\n          <view class=\"contact\">手机号：{{ userStore.userInfo.phone }}</view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 非微信环境的头像选择弹窗 -->\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n    <wd-message-box></wd-message-box>\r\n\r\n    <scroll-view scroll-y>\r\n      <wd-cell-group custom-class=\"shadow-warp\" border clickable>\r\n        <template v-for=\"(item, index) in dataSource\" :key=\"index\">\r\n          <wd-cell :title=\"item.title\" is-link @click=\"handleCell(item)\">\r\n            <template #icon>\r\n              <view :class=\"item.class\" class=\"mr-2\"></view>\r\n            </template>\r\n          </wd-cell>\r\n        </template>\r\n      </wd-cell-group>\r\n    </scroll-view>\r\n  </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { ref, reactive, watch, onBeforeUnmount, computed, onMounted } from 'vue'\r\nimport { cache, getFileAccessHttpUrl, hasRoute } from '@/common/uitls'\r\nimport { onLaunch, onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'\r\nimport { useToast, useMessage, useNotify } from 'wot-design-uni'\r\nimport { useRouter } from '@/plugin/uni-mini-router'\r\nimport {\r\n  ACCESS_TOKEN,\r\n  USER_NAME,\r\n  USER_INFO,\r\n  APP_ROUTE,\r\n  APP_CONFIG,\r\n  HOME_CONFIG_EXPIRED_TIME,\r\n} from '@/common/constants'\r\nimport { http } from '@/utils/http'\r\nimport { useUserStore } from '@/store/user'\r\nimport useUpload from '@/hooks/useUpload'\r\nimport { getEnvBaseUrl } from '@/utils/index'\r\nimport request from '@/utils/request'\r\n\r\n//\r\nconst userStore = useUserStore()\r\nconst toast = useToast()\r\nconst router = useRouter()\r\nconst message = useMessage()\r\nconst defAvatar = 'https://www.mograine.cn/images/big_logo.png'\r\n\r\n// 头像上传相关变量\r\nconst showOptions = ref(false)\r\nlet avatarStopWatch: any = null\r\nconst avatarApi = {\r\n  uploadUrl: `${getEnvBaseUrl()}/file/uploadavatar`,\r\n}\r\n\r\n// 保存通过请求获取的图片URL\r\nconst fetchedAvatarSrc = ref('')\r\n\r\n// 计算属性：返回最终显示的头像URL\r\nconst avatarSrc = computed(() => {\r\n  const userAvatar = userStore.userInfo.avatar\r\n  if (userAvatar) {\r\n    // 检查是否已经是完整URL（以http开头）\r\n    if (userAvatar.startsWith('http')) {\r\n      return userAvatar\r\n    }\r\n    // 如果是文件名，返回通过请求获取的URL，如果还没获取到则返回默认头像\r\n    return fetchedAvatarSrc.value || defAvatar\r\n  }\r\n  return defAvatar\r\n})\r\nconst personalList = reactive({\r\n  avatar: '',\r\n  realname: '',\r\n  username: '',\r\n  post: '',\r\n  depart: '',\r\n  phone: '',\r\n})\r\nconst userId = ref(userStore.userInfo.userid)\r\nconst id = ref('')\r\nlet stopWatch: any = null\r\nconst api = {\r\n  positionUrl: '/sys/position/list',\r\n  departUrl: '/sys/user/userDepartList',\r\n  userUrl: '/sys/user/queryById',\r\n  postUrl: '/sys/position/queryByCode',\r\n  uploadUrl: `${getEnvBaseUrl()}/sys/common/upload`,\r\n}\r\nconst dataSource = [\r\n  { key: 'setttings', title: '编辑资料', class: 'cuIcon-settingsfill text-cyan' },\r\n  { key: 'exit', title: '退出/切换角色', class: 'cuIcon-exit text-cyan' },\r\n]\r\n\r\n// 获取文件URL的函数\r\nconst fetchFileUrl = async (objectName: string) => {\r\n  try {\r\n    const response: any = await request(`/file/url`, {\r\n      method: 'GET',\r\n      params: {\r\n        objectName: objectName,\r\n        token: userStore.userInfo.token\r\n      }\r\n    })\r\n\r\n    // 根据接口返回的数据结构：response.result.fileUrl\r\n    if (response?.success && response?.result?.fileUrl) {\r\n      fetchedAvatarSrc.value = response.result.fileUrl\r\n    } else {\r\n      console.warn('响应数据格式异常:', response)\r\n      fetchedAvatarSrc.value = ''\r\n    }\r\n    return response\r\n  } catch (error) {\r\n    console.error('获取文件URL失败:', error)\r\n    fetchedAvatarSrc.value = ''\r\n    return null\r\n  }\r\n}\r\nconst exit = () => {\r\n  message\r\n    .confirm({\r\n      title: '提示',\r\n      msg: '确定退出吗？'\r\n    })\r\n    .then(() => {\r\n      userStore.clearUserInfo()\r\n      router.replaceAll({ name: 'login' })\r\n    })\r\n}\r\nconst handleCell = (item) => {\r\n  switch (item.key) {\r\n    case 'setttings':\r\n      router.push({ name: 'userEdit' })\r\n      break\r\n    case 'exit':\r\n      exit()\r\n      break\r\n    default:\r\n      toast.show('功能暂未开发~')\r\n  }\r\n}\r\nconst handleEditProfile = () => {\r\n  router.push({ name: 'userEdit' })\r\n}\r\n\r\n// 监听用户头像变化\r\nwatch(() => userStore.userInfo.avatar, (newAvatar) => {\r\n  if (newAvatar && !newAvatar.startsWith('http')) {\r\n    // 如果是文件名，发起请求获取URL\r\n    fetchFileUrl(newAvatar)\r\n  } else {\r\n    // 如果是完整URL或空值，清空请求获取的URL\r\n    fetchedAvatarSrc.value = ''\r\n  }\r\n}, { immediate: true })\r\n\r\n// 页面加载时初始化头像\r\nonMounted(() => {\r\n  const userAvatar = userStore.userInfo.avatar\r\n  if (userAvatar && !userAvatar.startsWith('http')) {\r\n    fetchFileUrl(userAvatar)\r\n  }\r\n})\r\n\r\n// 显示头像选择选项（仅非微信环境使用）\r\nconst showAvatarOptions = () => {\r\n\r\n\r\n\r\n}\r\n\r\n// 微信头像选择回调\r\nconst onChooseWechatAvatar = (e: any) => {\r\n  console.log('微信头像选择回调:', e)\r\n  const { avatarUrl } = e.detail\r\n\r\n  if (!avatarUrl) {\r\n    toast.warning('未获取到头像信息')\r\n    return\r\n  }\r\n\r\n  // 将微信头像上传到服务器\r\n  uploadWechatAvatar(avatarUrl)\r\n}\r\n\r\n// 保存头像信息到后端\r\nconst saveAvatarToBackend = async (fileUrl: string, fileName: string) => {\r\n  try {\r\n    const data = {\r\n      id: userStore.userInfo.userid,\r\n      avatar: fileName, // 使用fileName而不是完整URL\r\n    }\r\n\r\n    const response = await http.post('/sys/user/login/setting/userEdit', data)\r\n\r\n    if (response.success) {\r\n      // 更新本地用户信息\r\n      userStore.editUserInfo({ ...data, avatar: fileUrl }) // 本地存储完整URL\r\n      console.log('头像信息已保存到后端')\r\n      return true\r\n    } else {\r\n      console.error('保存头像信息失败:', response.message)\r\n      toast.warning('保存头像信息失败: ' + response.message)\r\n      return false\r\n    }\r\n  } catch (error) {\r\n    console.error('保存头像信息异常:', error)\r\n    toast.warning('保存头像信息失败')\r\n    return false\r\n  }\r\n}\r\n\r\n// 上传微信头像到服务器\r\nconst uploadWechatAvatar = (avatarUrl: string) => {\r\n  console.log('开始上传微信头像:', avatarUrl)\r\n\r\n  // 检查文件路径是否为微信临时文件\r\n  if (!avatarUrl.includes('tmp') && !avatarUrl.includes('wxfile://')) {\r\n    toast.warning('头像文件格式不正确')\r\n    return\r\n  }\r\n\r\n  uni.showLoading({\r\n    title: '上传头像中...',\r\n    mask: true\r\n  })\r\n\r\n  uni.uploadFile({\r\n    url: avatarApi.uploadUrl,\r\n    filePath: avatarUrl,\r\n    name: 'file',\r\n    formData: {\r\n      type: 'avatar' // 标识为头像上传\r\n    },\r\n    header: {\r\n      'X-Access-Token': userStore.userInfo.token\r\n    },\r\n    success: async (res) => {\r\n      console.log('微信头像上传响应:', res)\r\n      try {\r\n        const data = JSON.parse(res.data)\r\n        if (data && data.success && data.result && data.result.fileUrl) {\r\n          // 保存头像信息到后端\r\n          const saveSuccess = await saveAvatarToBackend(data.result.fileUrl, data.result.fileName)\r\n\r\n          if (saveSuccess) {\r\n            toast.success('头像更新成功')\r\n          } else {\r\n            // 即使保存失败，也更新本地显示\r\n            userStore.userInfo.avatar = data.result.fileUrl\r\n            toast.success('头像上传成功，但保存信息失败')\r\n          }\r\n        } else {\r\n          console.error('上传失败:', data)\r\n          let errorMessage = data?.message || '头像上传失败'\r\n          try {\r\n            if (typeof errorMessage === 'string' && /[^\\u0000-\\u007f]/.test(errorMessage)) {\r\n              console.error('可能的编码问题:', errorMessage)\r\n              errorMessage = '头像上传失败'\r\n            }\r\n          } catch (e) {\r\n            console.error('处理错误消息异常:', e)\r\n          }\r\n          toast.warning(errorMessage)\r\n        }\r\n      } catch (error) {\r\n        console.error('解析上传响应失败:', error)\r\n        toast.warning('头像上传失败')\r\n      }\r\n    },\r\n    fail: (error) => {\r\n      console.error('微信头像上传失败:', error)\r\n      toast.warning('头像上传失败，请重试')\r\n    },\r\n    complete: () => {\r\n      uni.hideLoading()\r\n    }\r\n  })\r\n}\r\n\r\n// 从相册选择\r\nconst handleUploadFromAlbum = () => {\r\n  showOptions.value = false\r\n  const { loading, data, error, run } = useUpload<any>({ name: 'file', type: 'avatar' }, {\r\n    url: avatarApi.uploadUrl,\r\n    sourceType: ['album']\r\n  })\r\n\r\n  if (avatarStopWatch) avatarStopWatch()\r\n  run()\r\n\r\n  avatarStopWatch = watch(\r\n    () => [loading.value, error.value, data.value],\r\n    async ([loading, err, uploadData]) => {\r\n      if (loading == false) {\r\n        if (err) {\r\n          toast.warning('上传失败')\r\n        } else {\r\n          if (uploadData && uploadData.success && uploadData.result && uploadData.result.fileUrl) {\r\n            // 保存头像信息到后端\r\n            const saveSuccess = await saveAvatarToBackend(uploadData.result.fileUrl, uploadData.result.fileName)\r\n\r\n            if (saveSuccess) {\r\n              toast.success('头像更新成功')\r\n            } else {\r\n              // 即使保存失败，也更新本地显示\r\n              userStore.userInfo.avatar = uploadData.result.fileUrl\r\n              toast.success('头像上传成功，但保存信息失败')\r\n            }\r\n          } else {\r\n            console.error('上传数据格式不正确:', uploadData)\r\n            toast.warning('头像上传失败')\r\n          }\r\n        }\r\n      }\r\n    },\r\n  )\r\n}\r\n\r\n// 拍照\r\nconst handleUploadFromCamera = () => {\r\n  showOptions.value = false\r\n  const { loading, data, error, run } = useUpload<any>({ name: 'file', type: 'avatar' }, {\r\n    url: avatarApi.uploadUrl,\r\n    sourceType: ['camera']\r\n  })\r\n\r\n  if (avatarStopWatch) avatarStopWatch()\r\n  run()\r\n\r\n  avatarStopWatch = watch(\r\n    () => [loading.value, error.value, data.value],\r\n    async ([loading, err, uploadData]) => {\r\n      if (loading == false) {\r\n        if (err) {\r\n          toast.warning('上传失败')\r\n        } else {\r\n          if (uploadData && uploadData.success && uploadData.result && uploadData.result.fileUrl) {\r\n            // 保存头像信息到后端\r\n            const saveSuccess = await saveAvatarToBackend(uploadData.result.fileUrl, uploadData.result.fileName)\r\n\r\n            if (saveSuccess) {\r\n              toast.success('头像更新成功')\r\n            } else {\r\n              // 即使保存失败，也更新本地显示\r\n              userStore.userInfo.avatar = uploadData.result.fileUrl\r\n              toast.success('头像上传成功，但保存信息失败')\r\n            }\r\n          } else {\r\n            console.error('上传数据格式不正确:', uploadData)\r\n            toast.warning('头像上传失败')\r\n          }\r\n        }\r\n      }\r\n    },\r\n  )\r\n}\r\n\r\nonBeforeUnmount(() => {\r\n  stopWatch?.()\r\n  avatarStopWatch?.()\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.user-card {\r\n  background-color: #fff;\r\n  margin: 20rpx;\r\n  padding: 30rpx;\r\n  border-radius: 10rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.user-info {\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n.avatar-wrapper {\r\n  position: relative;\r\n  cursor: pointer;\r\n\r\n  .avatar-button {\r\n    padding: 0;\r\n    border: none;\r\n    background: transparent;\r\n    display: block;\r\n  }\r\n}\r\n\r\n.user-badge {\r\n  font-size: 12rpx;\r\n  color: #888;\r\n  margin-top: 8rpx;\r\n  text-align: center;\r\n}\r\n\r\n.user-details {\r\n  margin-left: 20rpx;\r\n  flex: 1;\r\n}\r\n\r\n.username {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.user-id,\r\n.contact,\r\n.emergency {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.edit-btn {\r\n  background-color: #f0f0f0;\r\n  color: #333;\r\n  font-size: 28rpx;\r\n  padding: 10rpx 60rpx;\r\n  border-radius: 30rpx;\r\n  margin-top: 20rpx;\r\n  text-align: center;\r\n  display: inline-block;\r\n}\r\n\r\n.notification-settings {\r\n  display: flex;\r\n  background-color: #fff;\r\n  margin: 20rpx;\r\n  border-radius: 10rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.notification-item,\r\n.settings-item {\r\n  flex: 1;\r\n  text-align: center;\r\n  padding: 30rpx;\r\n  position: relative;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.notification-item {\r\n  border-right: 1px solid #f0f0f0;\r\n}\r\n\r\n.badge {\r\n  position: absolute;\r\n  top: 20rpx;\r\n  right: 60rpx;\r\n  background-color: #ff3a3a;\r\n  color: #fff;\r\n  font-size: 24rpx;\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n  line-height: 32rpx;\r\n  text-align: center;\r\n  border-radius: 50%;\r\n}\r\n\r\n:deep(.wd-cell-group) {\r\n  margin: 0 26upx;\r\n  border-radius: 18upx;\r\n  overflow: hidden;\r\n  --wot-cell-line-height: 32px;\r\n\r\n  .wd-cell {\r\n    --wot-cell-title-fs: 15px;\r\n    --wot-cell-title-color: var(--color-gray);\r\n\r\n    .wd-cell__left {\r\n      font-size: 15px;\r\n    }\r\n  }\r\n}\r\n\r\n// 添加深层选择器来覆盖确认按钮的样式\r\n:deep() {\r\n  .wd-message__button--confirm {\r\n    color: #07C160 !important;\r\n  }\r\n\r\n  .wd-button--primary {\r\n    background-color: #07C160 !important;\r\n    border-color: #07C160 !important;\r\n  }\r\n}\r\n\r\n// 头像选择弹窗样式\r\n.avatar-options {\r\n  background: #fff;\r\n  border-radius: 12px 12px 0 0;\r\n  padding: 0;\r\n\r\n  .options-title {\r\n    text-align: center;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n    color: #333;\r\n    padding: 20px 0 10px;\r\n    border-bottom: 1px solid #f0f0f0;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .options-list {\r\n    padding: 0 20px;\r\n\r\n    .option-item {\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 15px 0;\r\n      border: none;\r\n      background: transparent;\r\n      width: 100%;\r\n      text-align: left;\r\n      font-size: 16px;\r\n      color: #333;\r\n      border-bottom: 1px solid #f5f5f5;\r\n\r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n\r\n      &.option-item-hover {\r\n        background-color: #f5f5f5;\r\n      }\r\n\r\n      .option-icon {\r\n        width: 24px;\r\n        height: 24px;\r\n        margin-right: 12px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        .cuIcon-pic {\r\n          color: #576b95;\r\n          font-size: 20px;\r\n        }\r\n\r\n        .cuIcon-camera {\r\n          color: #ff6b6b;\r\n          font-size: 20px;\r\n        }\r\n      }\r\n\r\n      .option-text {\r\n        flex: 1;\r\n        font-size: 16px;\r\n        color: #333;\r\n      }\r\n    }\r\n  }\r\n\r\n  .options-cancel {\r\n    text-align: center;\r\n    padding: 15px 0;\r\n    font-size: 16px;\r\n    color: #666;\r\n    border-top: 8px solid #f5f5f5;\r\n    cursor: pointer;\r\n\r\n    &:active {\r\n      background-color: #f0f0f0;\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages/user/people.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "useToast", "useRouter", "useMessage", "ref", "getEnvBaseUrl", "computed", "reactive", "request", "watch", "onMounted", "http", "uni", "onBeforeUnmount"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoGA,MAAM,YAAY;;;;AAJlB,UAAM,YAAYA,WAAAA,aAAa;AAC/B,UAAM,QAAQC,cAAAA,SAAS;AACvB,UAAM,SAASC,gCAAAA,UAAU;AACzB,UAAM,UAAUC,cAAAA,WAAW;AAIPC,kBAAAA,IAAI,KAAK;AAE7B,UAAM,YAAY;AAAA,MAChB,WAAW,GAAGC,YAAA,cAAA,CAAe;AAAA,IAC/B;AAGM,UAAA,mBAAmBD,kBAAI,EAAE;AAGzB,UAAA,YAAYE,cAAAA,SAAS,MAAM;AACzB,YAAA,aAAa,UAAU,SAAS;AACtC,UAAI,YAAY;AAEV,YAAA,WAAW,WAAW,MAAM,GAAG;AAC1B,iBAAA;AAAA,QAAA;AAGT,eAAO,iBAAiB,SAAS;AAAA,MAAA;AAE5B,aAAA;AAAA,IAAA,CACR;AACoBC,2BAAS;AAAA,MAC5B,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACR,CAAA;AACcH,sBAAI,UAAU,SAAS,MAAM;AACjCA,kBAAAA,IAAI,EAAE;AAEL,KAAA;AAAA,MAKV,WAAW,GAAGC,YAAA,cAAA,CAAe;AAAA,IAAA;AAE/B,UAAM,aAAa;AAAA,MACjB,EAAE,KAAK,aAAa,OAAO,QAAQ,OAAO,gCAAgC;AAAA,MAC1E,EAAE,KAAK,QAAQ,OAAO,WAAW,OAAO,wBAAwB;AAAA,IAClE;AAGM,UAAA,eAAe,CAAO,eAAuB;;AAC7C,UAAA;AACI,cAAA,WAAgB,MAAMG,sBAAQ,aAAa;AAAA,UAC/C,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN;AAAA,YACA,OAAO,UAAU,SAAS;AAAA,UAAA;AAAA,QAC5B,CACD;AAGD,aAAI,qCAAU,cAAW,0CAAU,WAAV,mBAAkB,UAAS;AACjC,2BAAA,QAAQ,SAAS,OAAO;AAAA,QAAA,OACpC;AACG,kBAAA,KAAK,aAAa,QAAQ;AAClC,2BAAiB,QAAQ;AAAA,QAAA;AAEpB,eAAA;AAAA,eACA,OAAO;AACN,gBAAA,MAAM,cAAc,KAAK;AACjC,yBAAiB,QAAQ;AAClB,eAAA;AAAA,MAAA;AAAA,IAEX;AACA,UAAM,OAAO,MAAM;AACjB,cACG,QAAQ;AAAA,QACP,OAAO;AAAA,QACP,KAAK;AAAA,MAAA,CACN,EACA,KAAK,MAAM;AACV,kBAAU,cAAc;AACxB,eAAO,WAAW,EAAE,MAAM,QAAA,CAAS;AAAA,MAAA,CACpC;AAAA,IACL;AACM,UAAA,aAAa,CAAC,SAAS;AAC3B,cAAQ,KAAK,KAAK;AAAA,QAChB,KAAK;AACH,iBAAO,KAAK,EAAE,MAAM,WAAA,CAAY;AAChC;AAAA,QACF,KAAK;AACE,eAAA;AACL;AAAA,QACF;AACE,gBAAM,KAAK,SAAS;AAAA,MAAA;AAAA,IAE1B;AAMAC,kBAAAA,MAAM,MAAM,UAAU,SAAS,QAAQ,CAAC,cAAc;AACpD,UAAI,aAAa,CAAC,UAAU,WAAW,MAAM,GAAG;AAE9C,qBAAa,SAAS;AAAA,MAAA,OACjB;AAEL,yBAAiB,QAAQ;AAAA,MAAA;AAAA,IAC3B,GACC,EAAE,WAAW,MAAM;AAGtBC,kBAAAA,UAAU,MAAM;AACR,YAAA,aAAa,UAAU,SAAS;AACtC,UAAI,cAAc,CAAC,WAAW,WAAW,MAAM,GAAG;AAChD,qBAAa,UAAU;AAAA,MAAA;AAAA,IACzB,CACD;AAUK,UAAA,uBAAuB,CAAC,MAAW;AAC/B,cAAA,IAAI,aAAa,CAAC;AACpB,YAAA,EAAE,cAAc,EAAE;AAExB,UAAI,CAAC,WAAW;AACd,cAAM,QAAQ,UAAU;AACxB;AAAA,MAAA;AAIF,yBAAmB,SAAS;AAAA,IAC9B;AAGM,UAAA,sBAAsB,CAAO,SAAiB,aAAqB;AACnE,UAAA;AACF,cAAM,OAAO;AAAA,UACX,IAAI,UAAU,SAAS;AAAA,UACvB,QAAQ;AAAA;AAAA,QACV;AAEA,cAAM,WAAW,MAAMC,WAAAA,KAAK,KAAK,oCAAoC,IAAI;AAEzE,YAAI,SAAS,SAAS;AAEpB,oBAAU,aAAa,iCAAK,OAAL,EAAW,QAAQ,UAAS;AACnD,kBAAQ,IAAI,YAAY;AACjB,iBAAA;AAAA,QAAA,OACF;AACG,kBAAA,MAAM,aAAa,SAAS,OAAO;AACrC,gBAAA,QAAQ,eAAe,SAAS,OAAO;AACtC,iBAAA;AAAA,QAAA;AAAA,eAEF,OAAO;AACN,gBAAA,MAAM,aAAa,KAAK;AAChC,cAAM,QAAQ,UAAU;AACjB,eAAA;AAAA,MAAA;AAAA,IAEX;AAGM,UAAA,qBAAqB,CAAC,cAAsB;AACxC,cAAA,IAAI,aAAa,SAAS;AAG9B,UAAA,CAAC,UAAU,SAAS,KAAK,KAAK,CAAC,UAAU,SAAS,WAAW,GAAG;AAClE,cAAM,QAAQ,WAAW;AACzB;AAAA,MAAA;AAGFC,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,MAAM;AAAA,MAAA,CACP;AAEDA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,UAAU;AAAA,QACf,UAAU;AAAA,QACV,MAAM;AAAA,QACN,UAAU;AAAA,UACR,MAAM;AAAA;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,UACN,kBAAkB,UAAU,SAAS;AAAA,QACvC;AAAA,QACA,SAAS,CAAO,QAAQ;AACd,kBAAA,IAAI,aAAa,GAAG;AACxB,cAAA;AACF,kBAAM,OAAO,KAAK,MAAM,IAAI,IAAI;AAChC,gBAAI,QAAQ,KAAK,WAAW,KAAK,UAAU,KAAK,OAAO,SAAS;AAExD,oBAAA,cAAc,MAAM,oBAAoB,KAAK,OAAO,SAAS,KAAK,OAAO,QAAQ;AAEvF,kBAAI,aAAa;AACf,sBAAM,QAAQ,QAAQ;AAAA,cAAA,OACjB;AAEK,0BAAA,SAAS,SAAS,KAAK,OAAO;AACxC,sBAAM,QAAQ,gBAAgB;AAAA,cAAA;AAAA,YAChC,OACK;AACG,sBAAA,MAAM,SAAS,IAAI;AACvB,kBAAA,gBAAe,6BAAM,YAAW;AAChC,kBAAA;AACF,oBAAI,OAAO,iBAAiB,YAAY,mBAAmB,KAAK,YAAY,GAAG;AACrE,0BAAA,MAAM,YAAY,YAAY;AACvB,iCAAA;AAAA,gBAAA;AAAA,uBAEV,GAAG;AACF,wBAAA,MAAM,aAAa,CAAC;AAAA,cAAA;AAE9B,oBAAM,QAAQ,YAAY;AAAA,YAAA;AAAA,mBAErB,OAAO;AACN,oBAAA,MAAM,aAAa,KAAK;AAChC,kBAAM,QAAQ,QAAQ;AAAA,UAAA;AAAA,QAE1B;AAAA,QACA,MAAM,CAAC,UAAU;AACP,kBAAA,MAAM,aAAa,KAAK;AAChC,gBAAM,QAAQ,YAAY;AAAA,QAC5B;AAAA,QACA,UAAU,MAAM;AACdA,wBAAAA,MAAI,YAAY;AAAA,QAAA;AAAA,MAClB,CACD;AAAA,IACH;AAgFAC,kBAAAA,gBAAgB,MAAM;AAAA,IAEF,CACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/ZD,GAAG,WAAW,eAAe;"}