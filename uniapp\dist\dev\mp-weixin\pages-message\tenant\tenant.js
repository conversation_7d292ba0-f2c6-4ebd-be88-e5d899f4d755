"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _easycom_wd_tab2 = common_vendor.resolveComponent("wd-tab");
  const _easycom_wd_tabs2 = common_vendor.resolveComponent("wd-tabs");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_tab2 + _easycom_wd_tabs2 + _easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_wd_tab = () => "../../node-modules/wot-design-uni/components/wd-tab/wd-tab.js";
const _easycom_wd_tabs = () => "../../node-modules/wot-design-uni/components/wd-tabs/wd-tabs.js";
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  (workmate + department + _easycom_wd_tab + _easycom_wd_tabs + rightConditionFilter + _easycom_PageLayout)();
}
const workmate = () => "./components/workmate.js";
const department = () => "./components/department.js";
const rightConditionFilter = () => "../../components/RightConditionFilter/RightConditionFilter.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "tenant",
  options: {
    // apply-shared‌：当前页面样式会影响到子组件样式.(小程序)
    // shared‌：当前页面样式影响到子组件，子组件样式也会影响到当前页面.(小程序)
    styleIsolation: "‌shared‌"
  }
}), {
  __name: "tenant",
  setup(__props) {
    const reloadKey = common_vendor.ref(1);
    const tabList = common_vendor.ref([
      { key: "1", title: "全部" },
      { key: "2", title: "按部门" }
    ]);
    const conditionFilter = common_vendor.reactive({
      show: false,
      checked: "all",
      options: [
        { key: "all", title: "所有同事" },
        { key: "group", title: "群组" }
      ]
    });
    const tabActive = common_vendor.ref("2");
    const tenantId = common_vendor.ref();
    const title = common_vendor.ref();
    const workType = common_vendor.ref("");
    const handleChange = ({ option }) => {
      conditionFilter.checked = option.key;
      if (option.key == "all") {
        tabList.value[1]["title"] = "按部门";
        workType.value = "";
      } else if (option.key == "group") {
        tabList.value[1]["title"] = "我创建的";
        if (tabActive.value == "1") {
          workType.value = "allGroup";
        } else {
          workType.value = "createdGroup";
        }
      }
      tabList.value = [...tabList.value];
      reloadKey.value = Math.random();
    };
    common_vendor.onLoad((options) => {
      tenantId.value = options.id;
      title.value = options.title;
    });
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.f(tabList.value, (item, index, i0) => {
          return common_vendor.e$1(tabActive.value == "1" ? {
            a: reloadKey.value,
            b: "b9bbc3e3-4-" + i0 + "," + ("b9bbc3e3-3-" + i0),
            c: common_vendor.p({
              tenantId: tenantId.value,
              workType: workType.value
            })
          } : {}, tabActive.value == "2" ? {
            d: "b9bbc3e3-5-" + i0 + "," + ("b9bbc3e3-3-" + i0),
            e: common_vendor.p({
              tenantId: tenantId.value
            })
          } : {}, {
            f: "b9bbc3e3-3-" + i0 + ",b9bbc3e3-2",
            g: common_vendor.p({
              title: item.title,
              name: item.key
            }),
            h: index
          });
        }),
        b: tabActive.value == "1",
        c: tabActive.value == "2",
        d: common_vendor.o(($event) => tabActive.value = $event),
        e: common_vendor.p({
          customClass: "",
          modelValue: tabActive.value
        }),
        f: common_vendor.o(() => common_vendor.unref(conditionFilter).show = true),
        g: common_vendor.unref(conditionFilter).show
      }, common_vendor.unref(conditionFilter).show ? {
        h: common_vendor.o(() => common_vendor.unref(conditionFilter).show = false),
        i: common_vendor.o(handleChange),
        j: common_vendor.p(__spreadValues({}, common_vendor.unref(conditionFilter)))
      } : {}, {
        k: common_vendor.p({
          navTitle: title.value,
          backRouteName: "message",
          routeMethod: "pushTab"
        })
      });
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-b9bbc3e3"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=tenant.js.map
