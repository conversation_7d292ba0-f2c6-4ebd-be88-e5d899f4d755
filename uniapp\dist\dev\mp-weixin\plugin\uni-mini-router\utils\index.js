"use strict";
function getUrlParams(path) {
  const params = {};
  const pathArray = path.split("?");
  let paramString = "";
  let paramArrary = [];
  if (pathArray.length > 1) {
    paramString = pathArray[1];
  }
  paramArrary = paramString.split("&");
  for (let index = 0; index < paramArrary.length; index++) {
    if (paramArrary[index].split("=").length === 2) {
      params[paramArrary[index].split("=")[0]] = paramArrary[index].split("=")[1];
    }
  }
  return params;
}
function setUrlParams(path, params) {
  for (const key in params) {
    if (path.indexOf("?") > -1) {
      path = path + `&${key}=${params[key]}`;
    } else {
      path = path + `?${key}=${params[key]}`;
    }
  }
  return path;
}
function replaceAll(str, find, replace) {
  return str.replace(new RegExp(find, "g"), replace);
}
function beautifyUrl(url) {
  url = replaceAll(url, "//", "/");
  url = replaceAll(url, "https:/", "https://");
  url = replaceAll(url, "http:/", "http://");
  return url;
}
function queryStringify(query) {
  const result = {};
  if (query) {
    for (const key in query) {
      let value = query[key];
      if (value === void 0) {
        value = "";
      }
      result[key] = value;
    }
  }
  return result;
}
function isEmptyObject(obj) {
  return obj === void 0 || obj === null || Object.keys(obj).length === 0;
}
exports.beautifyUrl = beautifyUrl;
exports.getUrlParams = getUrlParams;
exports.isEmptyObject = isEmptyObject;
exports.queryStringify = queryStringify;
exports.setUrlParams = setUrlParams;
//# sourceMappingURL=index.js.map
