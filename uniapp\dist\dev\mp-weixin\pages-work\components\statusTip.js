"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  __name: "statusTip",
  props: {
    status: {
      type: Number,
      default: 0
    },
    text: {
      type: String,
      default: ""
    }
  },
  setup(__props) {
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: __props.status == 0
      }, __props.status == 0 ? {
        b: common_vendor.t(__props.text || "加载中...")
      } : __props.status == 1 ? {
        d: common_assets._imports_0,
        e: common_vendor.t(__props.text || "暂无数据！")
      } : {
        g: common_vendor.t(__props.text || "网络超时，请点击重试！")
      }, {
        c: __props.status == 1,
        f: __props.status == 2
      });
    };
  }
};
wx.createComponent(_sfc_main);
//# sourceMappingURL=statusTip.js.map
