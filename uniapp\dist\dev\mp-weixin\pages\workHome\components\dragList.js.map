{"version": 3, "file": "dragList.js", "sources": ["../../../../../../src/pages/workHome/components/dragList.vue", "../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvcGFnZXMvd29ya0hvbWUvY29tcG9uZW50cy9kcmFnTGlzdC52dWU"], "sourcesContent": ["<template>\r\n  <z-paging ref=\"paging\" :fixed=\"false\" v-model=\"dataList\" @query=\"queryList\">\r\n\t<wd-row>\r\n\t  <wd-col :span=\"6\"  v-for=\"(item, index) in dataList\" :key=\"index\">\r\n\t\t  <view class=\"li-img\" @click=\"handleGo(item)\" :style=\"[{backgroundColor: item?.backgroundColor?item.backgroundColor : getRandomColor()}]\">\r\n\t\t    <wd-icon name=\"chart\" size=\"22px\" />\r\n\t\t  </view>\r\n\t\t  <view class=\"li-text\">{{ item.name }}</view>\r\n\t  </wd-col>\r\n\t</wd-row>\r\n  </z-paging>\r\n  <wd-toast />\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { http } from '@/utils/http'\r\nimport { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'\r\nimport { useRouter } from '@/plugin/uni-mini-router'\r\nimport { getRandomColor } from '@/common/uitls'\r\nimport { useParamsStore } from '@/store/page-params'\r\n\r\ndefineOptions({\r\n  name: 'dragList',\r\n  options: {\r\n    styleIsolation: 'shared',\r\n  },\r\n})\r\nconst toast = useToast()\r\nconst router = useRouter()\r\nconst paramsStore = useParamsStore()\r\nconst paging = ref(null)\r\nconst dataList = ref([])\r\n\r\nconst queryList = (pageNo, pageSize) => {\r\n  http\r\n    .get('/drag/page/list', {\r\n      izTemplate: 0,\r\n\t  pageNo,\r\n\t  pageSize,\r\n      style: 'default'\r\n    })\r\n    .then((res: any) => {\r\n      if (res.success) {\r\n        paging.value.complete(res.result.records)\r\n      } else {\r\n        paging.value.complete(false)\r\n      }\r\n    })\r\n    .catch((res) => {\r\n      paging.value.complete(false)\r\n    })\r\n}\r\n\r\n\r\n// 跳转\r\nconst handleGo = (item) => {\r\n  console.log(\"handleGo\",item);\r\n  uni.navigateTo({\r\n\t  url: '/pages-work/dragPage/index?id=' + item.id,\r\n\t})\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.z-paging-content {\r\n  background-color: #f1f1f1;\r\n}\r\n:deep(.wd-row){\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tlist-style-type: none;\r\n\tbackground-color: #ffffff;\r\n\t.wd-col{\r\n\t\tpadding: 10px;\r\n\t\tmin-width: 80px;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\r\n\t\t.li-img {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\twidth: 80upx;\r\n\t\t\t\theight: 80upx;\r\n\t\t\t\tbackground-color: #00bcd4;\r\n\t\t\t\tborder-radius: 100%;\r\n\t\t\t\tcolor: #fff;\r\n\t\t}\r\n\r\n\t\t.li-text {\r\n\t\t\t\tcolor: #000;\r\n\t\t\t\tmargin-top: 6px;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-size: 12px;\r\n\t\t\t\twidth: 120upx; /* 设置元素宽度 */\r\n\t\t\t\twhite-space: nowrap; /* 禁止文本换行 */\r\n\t\t\t\toverflow: hidden; /* 隐藏超出部分 */\r\n\t\t\t\ttext-overflow: ellipsis; /* 超出部分显示为省略号 */\r\n\t\t}\r\n\t}\r\n}\r\n.list-ul {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  list-style-type: none;\r\n  background-color: #ffffff;\r\n\r\n  .list-li{\r\n\t  padding: 10px;\r\n\t  min-width: 80px;\r\n\t  display: flex;\r\n\t  flex-direction: column;\r\n\t  align-items: center;\r\n\r\n\t  .li-img {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\twidth: 80upx;\r\n\t\theight: 80upx;\r\n\t\tbackground-color: #00bcd4;\r\n\t\tborder-radius: 100%;\r\n\t\tcolor: #fff;\r\n\t  }\r\n\r\n\t  .li-text {\r\n\t\tcolor: #000;\r\n\t\tmargin-top: 6px;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 12px;\r\n\t\twidth: 120upx; /* 设置元素宽度 */\r\n\t\twhite-space: nowrap; /* 禁止文本换行 */\r\n\t\toverflow: hidden; /* 隐藏超出部分 */\r\n\t\ttext-overflow: ellipsis; /* 超出部分显示为省略号 */\r\n\t  }\r\n  }\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages/workHome/components/dragList.vue'\nwx.createComponent(Component)"], "names": ["useToast", "useRouter", "useParamsStore", "ref", "http", "uni"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BcA,kBAAS,SAAA;AACRC,oCAAU,UAAA;AACLC,qBAAe,eAAA;AAC7B,UAAA,SAASC,kBAAI,IAAI;AACjB,UAAA,WAAWA,cAAI,IAAA,EAAE;AAEjB,UAAA,YAAY,CAAC,QAAQ,aAAa;AACtCC,iBAAA,KACG,IAAI,mBAAmB;AAAA,QACtB,YAAY;AAAA,QACf;AAAA,QACA;AAAA,QACG,OAAO;AAAA,MAAA,CACR,EACA,KAAK,CAAC,QAAa;AAClB,YAAI,IAAI,SAAS;AACf,iBAAO,MAAM,SAAS,IAAI,OAAO,OAAO;AAAA,QAAA,OACnC;AACE,iBAAA,MAAM,SAAS,KAAK;AAAA,QAAA;AAAA,MAC7B,CACD,EACA,MAAM,CAAC,QAAQ;AACP,eAAA,MAAM,SAAS,KAAK;AAAA,MAAA,CAC5B;AAAA,IACL;AAIM,UAAA,WAAW,CAAC,SAAS;AACjB,cAAA,IAAI,YAAW,IAAI;AAC3BC,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,mCAAmC,KAAK;AAAA,MAAA,CAC9C;AAAA,IACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3DA,GAAG,gBAAgB,SAAS;"}