{"version": 3, "file": "wd-textarea.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-textarea/wd-textarea.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC10ZXh0YXJlYS93ZC10ZXh0YXJlYS52dWU"], "sourcesContent": ["<template>\n  <view :class=\"rootClass\" :style=\"customStyle\">\n    <view v-if=\"label || $slots.label\" :class=\"labelClass\" :style=\"labelStyle\">\n      <view v-if=\"prefixIcon || $slots.prefix\" class=\"wd-textarea__prefix\">\n        <wd-icon v-if=\"prefixIcon && !$slots.prefix\" custom-class=\"wd-textarea__icon\" :name=\"prefixIcon\" @click=\"onClickPrefixIcon\" />\n        <slot v-else name=\"prefix\"></slot>\n      </view>\n      <view class=\"wd-textarea__label-inner\">\n        <text v-if=\"label && !$slots.label\">{{ label }}</text>\n        <slot v-else name=\"label\"></slot>\n      </view>\n    </view>\n\n    <!-- 文本域 -->\n    <view :class=\"`wd-textarea__value ${showClear ? 'is-suffix' : ''} ${customTextareaContainerClass} ${showWordCount ? 'is-show-limit' : ''}`\">\n      <textarea\n        :class=\"`wd-textarea__inner ${customTextareaClass}`\"\n        v-model=\"inputValue\"\n        :show-count=\"false\"\n        :placeholder=\"placeholderValue\"\n        :disabled=\"disabled || readonly\"\n        :maxlength=\"maxlength\"\n        :focus=\"focused\"\n        :auto-focus=\"autoFocus\"\n        :placeholder-style=\"placeholderStyle\"\n        :placeholder-class=\"inputPlaceholderClass\"\n        :auto-height=\"autoHeight\"\n        :cursor-spacing=\"cursorSpacing\"\n        :fixed=\"fixed\"\n        :cursor=\"cursor\"\n        :show-confirm-bar=\"showConfirmBar\"\n        :selection-start=\"selectionStart\"\n        :selection-end=\"selectionEnd\"\n        :adjust-position=\"adjustPosition\"\n        :hold-keyboard=\"holdKeyboard\"\n        :confirm-type=\"confirmType\"\n        :confirm-hold=\"confirmHold\"\n        :disable-default-padding=\"disableDefaultPadding\"\n        :ignoreCompositionEvent=\"ignoreCompositionEvent\"\n        :inputmode=\"inputmode\"\n        @input=\"handleInput\"\n        @focus=\"handleFocus\"\n        @blur=\"handleBlur\"\n        @confirm=\"handleConfirm\"\n        @linechange=\"handleLineChange\"\n        @keyboardheightchange=\"handleKeyboardheightchange\"\n      />\n      <view v-if=\"errorMessage\" class=\"wd-textarea__error-message\">{{ errorMessage }}</view>\n\n      <view v-if=\"props.readonly\" class=\"wd-textarea__readonly-mask\" />\n      <view class=\"wd-textarea__suffix\">\n        <wd-icon v-if=\"showClear\" custom-class=\"wd-textarea__clear\" name=\"error-fill\" @click=\"handleClear\" />\n        <view v-if=\"showWordCount\" class=\"wd-textarea__count\">\n          <text :class=\"countClass\">\n            {{ currentLength }}\n          </text>\n          /{{ maxlength }}\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script lang=\"ts\">\nexport default {\n  name: 'wd-textarea',\n  options: {\n    virtualHost: true,\n    addGlobalClass: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport { computed, onBeforeMount, ref, watch, useSlots, type Slots } from 'vue'\nimport wdIcon from '../wd-icon/wd-icon.vue'\nimport { objToStyle, isDef, pause } from '../common/util'\nimport { useCell } from '../composables/useCell'\nimport { FORM_KEY, type FormItemRule } from '../wd-form/types'\nimport { useParent } from '../composables/useParent'\nimport { useTranslate } from '../composables/useTranslate'\nimport { textareaProps } from './types'\n\ninterface TextareaSlots extends Slots {\n  prefix?: () => any\n  label?: () => any\n}\n\nconst { translate } = useTranslate('textarea')\n\nconst props = defineProps(textareaProps)\nconst emit = defineEmits([\n  'update:modelValue',\n  'clear',\n  'blur',\n  'focus',\n  'input',\n  'keyboardheightchange',\n  'confirm',\n  'linechange',\n  'clickprefixicon',\n  'click'\n])\nconst slots = useSlots() as TextareaSlots\n\nconst placeholderValue = computed(() => {\n  return isDef(props.placeholder) ? props.placeholder : translate('placeholder')\n})\n\nconst clearing = ref<boolean>(false)\nconst focused = ref<boolean>(false) // 控制聚焦\nconst focusing = ref<boolean>(false) // 当前是否激活状态\nconst inputValue = ref<string>('') // 输入框的值\nconst cell = useCell()\n\nwatch(\n  () => props.focus,\n  (newValue) => {\n    focused.value = newValue\n  },\n  { immediate: true, deep: true }\n)\n\nwatch(\n  () => props.modelValue,\n  (newValue) => {\n    inputValue.value = isDef(newValue) ? String(newValue) : ''\n  },\n  { immediate: true, deep: true }\n)\n\nconst { parent: form } = useParent(FORM_KEY)\n\n/**\n * 展示清空按钮\n */\nconst showClear = computed(() => {\n  const { disabled, readonly, clearable, clearTrigger } = props\n  if (clearable && !readonly && !disabled && inputValue.value && (clearTrigger === 'always' || (props.clearTrigger === 'focus' && focusing.value))) {\n    return true\n  } else {\n    return false\n  }\n})\n\n/**\n * 展示字数统计\n */\nconst showWordCount = computed(() => {\n  const { disabled, readonly, maxlength, showWordLimit } = props\n  return Boolean(!disabled && !readonly && isDef(maxlength) && maxlength > -1 && showWordLimit)\n})\n\n// 表单校验错误信息\nconst errorMessage = computed(() => {\n  if (form && props.prop && form.errorMessages && form.errorMessages[props.prop]) {\n    return form.errorMessages[props.prop]\n  } else {\n    return ''\n  }\n})\n\n// 是否展示必填\nconst isRequired = computed(() => {\n  let formRequired = false\n  if (form && form.props.rules) {\n    const rules = form.props.rules\n    for (const key in rules) {\n      if (Object.prototype.hasOwnProperty.call(rules, key) && key === props.prop && Array.isArray(rules[key])) {\n        formRequired = rules[key].some((rule: FormItemRule) => rule.required)\n      }\n    }\n  }\n  return props.required || props.rules.some((rule) => rule.required) || formRequired\n})\n\n// 当前文本域文字长度\nconst currentLength = computed(() => {\n  /**\n   * 使用Array.from处理多码元字符以获取正确的长度\n   * @link https://github.com/Moonofweisheng/wot-design-uni/issues/933\n   */\n  return Array.from(String(formatValue(props.modelValue))).length\n})\n\nconst rootClass = computed(() => {\n  return `wd-textarea   ${props.label || slots.label ? 'is-cell' : ''} ${props.center ? 'is-center' : ''} ${cell.border.value ? 'is-border' : ''} ${\n    props.size ? 'is-' + props.size : ''\n  } ${props.error ? 'is-error' : ''} ${props.disabled ? 'is-disabled' : ''} ${props.autoHeight ? 'is-auto-height' : ''} ${\n    currentLength.value > 0 ? 'is-not-empty' : ''\n  }  ${props.noBorder ? 'is-no-border' : ''} ${props.customClass}`\n})\n\nconst labelClass = computed(() => {\n  return `wd-textarea__label ${props.customLabelClass} ${isRequired.value ? 'is-required' : ''}`\n})\n\nconst inputPlaceholderClass = computed(() => {\n  return `wd-textarea__placeholder  ${props.placeholderClass}`\n})\n\nconst countClass = computed(() => {\n  return `${currentLength.value > 0 ? 'wd-textarea__count-current' : ''} ${currentLength.value > props.maxlength ? 'is-error' : ''}`\n})\n\nconst labelStyle = computed(() => {\n  return props.labelWidth\n    ? objToStyle({\n        'min-width': props.labelWidth,\n        'max-width': props.labelWidth\n      })\n    : ''\n})\n\nonBeforeMount(() => {\n  initState()\n})\n\n// 状态初始化\nfunction initState() {\n  inputValue.value = formatValue(inputValue.value)\n  emit('update:modelValue', inputValue.value)\n}\n\nfunction formatValue(value: string | number) {\n  if (value === null || value === undefined) return ''\n  const { maxlength, showWordLimit } = props\n  if (showWordLimit && maxlength !== -1 && String(value).length > maxlength) {\n    return value.toString().substring(0, maxlength)\n  }\n  return `${value}`\n}\n\nasync function handleClear() {\n  clearing.value = true\n  focusing.value = false\n  inputValue.value = ''\n  if (props.focusWhenClear) {\n    focused.value = false\n  }\n  await pause()\n  if (props.focusWhenClear) {\n    focused.value = true\n    focusing.value = true\n  }\n  emit('update:modelValue', inputValue.value)\n  emit('clear')\n}\nasync function handleBlur({ detail }: any) {\n  // 等待150毫秒，clear执行完毕\n  await pause(150)\n\n  if (clearing.value) {\n    clearing.value = false\n    return\n  }\n\n  focusing.value = false\n  emit('blur', {\n    value: inputValue.value,\n    cursor: detail.cursor ? detail.cursor : null\n  })\n}\nfunction handleFocus({ detail }: any) {\n  focusing.value = true\n  emit('focus', detail)\n}\nfunction handleInput({ detail }: any) {\n  inputValue.value = formatValue(inputValue.value as string)\n  emit('update:modelValue', inputValue.value)\n  emit('input', detail)\n}\nfunction handleKeyboardheightchange({ detail }: any) {\n  emit('keyboardheightchange', detail)\n}\nfunction handleConfirm({ detail }: any) {\n  emit('confirm', detail)\n}\nfunction handleLineChange({ detail }: any) {\n  emit('linechange', detail)\n}\nfunction onClickPrefixIcon() {\n  emit('clickprefixicon')\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n\n<style lang=\"scss\">\n@import './placeholder.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-textarea/wd-textarea.vue'\nwx.createComponent(Component)"], "names": ["useTranslate", "useSlots", "computed", "isDef", "ref", "useCell", "watch", "useParent", "FORM_KEY", "objToStyle", "onBeforeMount", "pause"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4EA,MAAA,SAAmB,MAAA;AAZnB,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,EAAA;AAEpB;;;;;;;;;;;;;;;;AAkBA,UAAM,EAAE,UAAA,IAAcA,cAAA,aAAa,UAAU;AAE7C,UAAM,QAAQ;AACd,UAAM,OAAO;AAYb,UAAM,QAAQC,cAAAA,SAAS;AAEjB,UAAA,mBAAmBC,cAAAA,SAAS,MAAM;AACtC,aAAOC,cAAAA,MAAM,MAAM,WAAW,IAAI,MAAM,cAAc,UAAU,aAAa;AAAA,IAAA,CAC9E;AAEK,UAAA,WAAWC,kBAAa,KAAK;AAC7B,UAAA,UAAUA,kBAAa,KAAK;AAC5B,UAAA,WAAWA,kBAAa,KAAK;AAC7B,UAAA,aAAaA,kBAAY,EAAE;AACjC,UAAM,OAAOC,cAAAA,QAAQ;AAErBC,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,aAAa;AACZ,gBAAQ,QAAQ;AAAA,MAClB;AAAA,MACA,EAAE,WAAW,MAAM,MAAM,KAAK;AAAA,IAChC;AAEAA,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,aAAa;AACZ,mBAAW,QAAQH,oBAAM,QAAQ,IAAI,OAAO,QAAQ,IAAI;AAAA,MAC1D;AAAA,MACA,EAAE,WAAW,MAAM,MAAM,KAAK;AAAA,IAChC;AAEA,UAAM,EAAE,QAAQ,SAASI,cAAAA,UAAUC,cAAAA,QAAQ;AAKrC,UAAA,YAAYN,cAAAA,SAAS,MAAM;AAC/B,YAAM,EAAE,UAAU,UAAU,WAAW,aAAiB,IAAA;AACxD,UAAI,aAAa,CAAC,YAAY,CAAC,YAAY,WAAW,UAAU,iBAAiB,YAAa,MAAM,iBAAiB,WAAW,SAAS,QAAS;AACzI,eAAA;AAAA,MAAA,OACF;AACE,eAAA;AAAA,MAAA;AAAA,IACT,CACD;AAKK,UAAA,gBAAgBA,cAAAA,SAAS,MAAM;AACnC,YAAM,EAAE,UAAU,UAAU,WAAW,cAAkB,IAAA;AAClD,aAAA,QAAQ,CAAC,YAAY,CAAC,YAAYC,oBAAM,SAAS,KAAK,YAAY,MAAM,aAAa;AAAA,IAAA,CAC7F;AAGK,UAAA,eAAeD,cAAAA,SAAS,MAAM;AAC9B,UAAA,QAAQ,MAAM,QAAQ,KAAK,iBAAiB,KAAK,cAAc,MAAM,IAAI,GAAG;AACvE,eAAA,KAAK,cAAc,MAAM,IAAI;AAAA,MAAA,OAC/B;AACE,eAAA;AAAA,MAAA;AAAA,IACT,CACD;AAGK,UAAA,aAAaA,cAAAA,SAAS,MAAM;AAChC,UAAI,eAAe;AACf,UAAA,QAAQ,KAAK,MAAM,OAAO;AACtB,cAAA,QAAQ,KAAK,MAAM;AACzB,mBAAW,OAAO,OAAO;AACvB,cAAI,OAAO,UAAU,eAAe,KAAK,OAAO,GAAG,KAAK,QAAQ,MAAM,QAAQ,MAAM,QAAQ,MAAM,GAAG,CAAC,GAAG;AACvG,2BAAe,MAAM,GAAG,EAAE,KAAK,CAAC,SAAuB,KAAK,QAAQ;AAAA,UAAA;AAAA,QACtE;AAAA,MACF;AAEK,aAAA,MAAM,YAAY,MAAM,MAAM,KAAK,CAAC,SAAS,KAAK,QAAQ,KAAK;AAAA,IAAA,CACvE;AAGK,UAAA,gBAAgBA,cAAAA,SAAS,MAAM;AAK5B,aAAA,MAAM,KAAK,OAAO,YAAY,MAAM,UAAU,CAAC,CAAC,EAAE;AAAA,IAAA,CAC1D;AAEK,UAAA,YAAYA,cAAAA,SAAS,MAAM;AACxB,aAAA,iBAAiB,MAAM,SAAS,MAAM,QAAQ,YAAY,EAAE,IAAI,MAAM,SAAS,cAAc,EAAE,IAAI,KAAK,OAAO,QAAQ,cAAc,EAAE,IAC5I,MAAM,OAAO,QAAQ,MAAM,OAAO,EACpC,IAAI,MAAM,QAAQ,aAAa,EAAE,IAAI,MAAM,WAAW,gBAAgB,EAAE,IAAI,MAAM,aAAa,mBAAmB,EAAE,IAClH,cAAc,QAAQ,IAAI,iBAAiB,EAC7C,KAAK,MAAM,WAAW,iBAAiB,EAAE,IAAI,MAAM,WAAW;AAAA,IAAA,CAC/D;AAEK,UAAA,aAAaA,cAAAA,SAAS,MAAM;AAChC,aAAO,sBAAsB,MAAM,gBAAgB,IAAI,WAAW,QAAQ,gBAAgB,EAAE;AAAA,IAAA,CAC7F;AAEK,UAAA,wBAAwBA,cAAAA,SAAS,MAAM;AACpC,aAAA,6BAA6B,MAAM,gBAAgB;AAAA,IAAA,CAC3D;AAEK,UAAA,aAAaA,cAAAA,SAAS,MAAM;AAChC,aAAO,GAAG,cAAc,QAAQ,IAAI,+BAA+B,EAAE,IAAI,cAAc,QAAQ,MAAM,YAAY,aAAa,EAAE;AAAA,IAAA,CACjI;AAEK,UAAA,aAAaA,cAAAA,SAAS,MAAM;AACzB,aAAA,MAAM,aACTO,yBAAW;AAAA,QACT,aAAa,MAAM;AAAA,QACnB,aAAa,MAAM;AAAA,MACpB,CAAA,IACD;AAAA,IAAA,CACL;AAEDC,kBAAAA,cAAc,MAAM;AACR,gBAAA;AAAA,IAAA,CACX;AAGD,aAAS,YAAY;AACR,iBAAA,QAAQ,YAAY,WAAW,KAAK;AAC1C,WAAA,qBAAqB,WAAW,KAAK;AAAA,IAAA;AAG5C,aAAS,YAAY,OAAwB;AACvC,UAAA,UAAU,QAAQ,UAAU;AAAkB,eAAA;AAC5C,YAAA,EAAE,WAAW,cAAA,IAAkB;AACrC,UAAI,iBAAiB,cAAc,MAAM,OAAO,KAAK,EAAE,SAAS,WAAW;AACzE,eAAO,MAAM,SAAA,EAAW,UAAU,GAAG,SAAS;AAAA,MAAA;AAEhD,aAAO,GAAG,KAAK;AAAA,IAAA;AAGjB,aAAe,cAAc;AAAA;AAC3B,iBAAS,QAAQ;AACjB,iBAAS,QAAQ;AACjB,mBAAW,QAAQ;AACnB,YAAI,MAAM,gBAAgB;AACxB,kBAAQ,QAAQ;AAAA,QAAA;AAElB,cAAMC,oBAAM;AACZ,YAAI,MAAM,gBAAgB;AACxB,kBAAQ,QAAQ;AAChB,mBAAS,QAAQ;AAAA,QAAA;AAEd,aAAA,qBAAqB,WAAW,KAAK;AAC1C,aAAK,OAAO;AAAA,MAAA;AAAA;AAEC,aAAA,WAAW,IAAiB;AAAA,iDAAjB,EAAE,UAAe;AAEzC,cAAMA,cAAAA,MAAM,GAAG;AAEf,YAAI,SAAS,OAAO;AAClB,mBAAS,QAAQ;AACjB;AAAA,QAAA;AAGF,iBAAS,QAAQ;AACjB,aAAK,QAAQ;AAAA,UACX,OAAO,WAAW;AAAA,UAClB,QAAQ,OAAO,SAAS,OAAO,SAAS;AAAA,QAAA,CACzC;AAAA,MAAA;AAAA;AAEM,aAAA,YAAY,EAAE,UAAe;AACpC,eAAS,QAAQ;AACjB,WAAK,SAAS,MAAM;AAAA,IAAA;AAEb,aAAA,YAAY,EAAE,UAAe;AACzB,iBAAA,QAAQ,YAAY,WAAW,KAAe;AACpD,WAAA,qBAAqB,WAAW,KAAK;AAC1C,WAAK,SAAS,MAAM;AAAA,IAAA;AAEb,aAAA,2BAA2B,EAAE,UAAe;AACnD,WAAK,wBAAwB,MAAM;AAAA,IAAA;AAE5B,aAAA,cAAc,EAAE,UAAe;AACtC,WAAK,WAAW,MAAM;AAAA,IAAA;AAEf,aAAA,iBAAiB,EAAE,UAAe;AACzC,WAAK,cAAc,MAAM;AAAA,IAAA;AAE3B,aAAS,oBAAoB;AAC3B,WAAK,iBAAiB;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1RxB,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}