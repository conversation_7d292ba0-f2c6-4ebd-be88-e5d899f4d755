{"version": 3, "file": "onlineTable.js", "sources": ["../../../../../src/pages-sub/online/onlineTable.vue", "../../../../../uniPage:/cGFnZXMtc3ViXG9ubGluZVxvbmxpbmVUYWJsZS52dWU"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n  <PageLayout backRouteName=\"online\" navTitle=\"online在线表单\">\r\n    <wd-table :index=\"tableIndex\" :data=\"dataList\" @row-click=\"handleRow\">\r\n      <template v-for=\"(item, index) in columns\" :key=\"item.prop\">\r\n        <wd-table-col\r\n          :prop=\"item.prop\"\r\n          :label=\"item.label\"\r\n          :width=\"item.width\"\r\n          :fixed=\"item.fixed\"\r\n          :align=\"item.align\"\r\n          :sortable=\"item.sortable\"\r\n        >\r\n          <template #value=\"{ row, index }\">\r\n            <onlineTableCell\r\n              :columnsInfo=\"columnsInfo\"\r\n              :record=\"row\"\r\n              :column=\"item\"\r\n              @longpress.prevent=\"handleLongPress(row)\"\r\n            ></onlineTableCell>\r\n          </template>\r\n        </wd-table-col>\r\n      </template>\r\n    </wd-table>\r\n    <wd-status-tip v-if=\"dataList.length == 0\" image=\"content\" tip=\"暂无内容\" />\r\n    <wd-pagination\r\n      v-model=\"pageNo\"\r\n      :total=\"pageTotal\"\r\n      :page-size=\"pageSize\"\r\n      @change=\"handlePaginChange\"\r\n      show-icon\r\n    ></wd-pagination>\r\n    <view class=\"add u-iconfont u-icon-add\" @click=\"handleGo\"></view>\r\n  </PageLayout>\r\n  <BottomOperate\r\n    v-if=\"bottomOperatePopup.show\"\r\n    v-bind=\"bottomOperatePopup\"\r\n    @close=\"() => (bottomOperatePopup.show = false)\"\r\n    @change=\"handleChange\"\r\n  ></BottomOperate>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { http } from '@/utils/http'\r\nimport { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'\r\nimport { useRouter } from '@/plugin/uni-mini-router'\r\nimport { useUserStore } from '@/store/user'\r\nimport { useParamsStore } from '@/store/page-params'\r\nimport onlineTableCell from './components/onlineTableCell.vue'\r\ndefineOptions({\r\n  name: 'onlineTable',\r\n  options: {\r\n    styleIsolation: 'shared',\r\n  },\r\n})\r\nconst toast = useToast()\r\nconst router = useRouter()\r\nconst userStore = useUserStore()\r\nconst paramsStore = useParamsStore()\r\nconst globalData = getApp().globalData\r\nconst { systemInfo } = globalData\r\nconst { safeArea } = systemInfo\r\n\r\nconst columns = ref([])\r\nconst columnsInfo = ref({})\r\nconst tableIndex = ref(false)\r\nconst pageNo = ref(1)\r\nconst pageSize = ref(10)\r\nconst pageTotal = ref(1)\r\nlet pageParams: any = {}\r\nlet rowIndex: any = ref(0)\r\n// 底部操作\r\nconst bottomOperatePopup = reactive({\r\n  show: false,\r\n  title: '操作',\r\n  data: {},\r\n  options: [\r\n    { key: 'edit', icon: 'edit', label: '编辑' },\r\n    { key: 'detail', icon: 'view', label: '查看' },\r\n    { key: 'delete', icon: 'delete', label: '删除', color: 'red' },\r\n  ],\r\n})\r\nconst queryParams = () => {\r\n  return {\r\n    pageNo: pageNo.value,\r\n    pageSize: pageSize.value,\r\n    order: 'asc',\r\n    column: 'id',\r\n    hasQuery: true,\r\n  }\r\n}\r\nconst dataList = ref([])\r\nconst getColumns = () => {\r\n  return new Promise<void>((resove, reject) => {\r\n    const analysis = (data) => {\r\n      const len = data.length\r\n      const maxShowColumn = 3\r\n      let space = 1\r\n      if (len == 1) {\r\n        space = 2\r\n      }\r\n      const width = safeArea.width / (len > maxShowColumn ? maxShowColumn : len) - space\r\n      columns.value = data.map((item) => {\r\n        return {\r\n          ...item,\r\n          prop: item.dataIndex,\r\n          align: item.align,\r\n          label: item.title,\r\n          width,\r\n        }\r\n      })\r\n    }\r\n    http\r\n      .get(`/online/cgform/api/getColumns/${pageParams.id}`)\r\n      .then((res: any) => {\r\n        if (res.success) {\r\n          if (res.result?.columns?.length) {\r\n            columnsInfo.value = res.result\r\n            analysis(res.result.columns)\r\n          }\r\n        } else {\r\n          toast.warning(res.message)\r\n        }\r\n      })\r\n      .catch((res) => {\r\n        toast.error('加载列头失败~')\r\n      })\r\n  })\r\n}\r\nconst getData = () => {\r\n  http\r\n    .get(`/online/cgform/api/getData/${pageParams.id}`, { ...queryParams() })\r\n    .then((res: any) => {\r\n      if (res.success) {\r\n        dataList.value = res.result?.records ?? []\r\n        pageTotal.value = res.result.total\r\n      } else {\r\n        toast.warning(res.message)\r\n      }\r\n    })\r\n    .catch((res) => {\r\n      toast.error('加载表格数据失败~')\r\n    })\r\n}\r\nconst handlePaginChange = ({ value }) => {\r\n  pageNo.value = value\r\n  getData()\r\n}\r\n//go 新增页\r\nconst handleGo = () => {\r\n  router.push({\r\n    name: 'onlineAdd',\r\n    params: { desformCode: pageParams.tableName, desformName: pageParams.tableTxt },\r\n  })\r\n}\r\n//go 编辑页\r\nconst handleEdit = (record) => {\r\n  router.push({\r\n    name: 'onlineEdit',\r\n    params: { desformCode: pageParams.tableName, desformName: pageParams.tableTxt, id: record.id },\r\n  })\r\n}\r\n//go 编辑页\r\nconst handleView = (record) => {\r\n  router.push({\r\n    name: 'onlineDetail',\r\n    params: { desformCode: pageParams.tableName, desformName: pageParams.tableTxt, id: record.id },\r\n  })\r\n}\r\n//长按操作\r\nconst handleLongPress = (item) => {\r\n  bottomOperatePopup.show = true\r\n  bottomOperatePopup.data = item\r\n}\r\n//操作切换\r\nconst handleChange = ({ option, data }) => {\r\n  if (option.key === 'edit') {\r\n    handleEdit(data)\r\n  } else if (option.key === 'delete') {\r\n    uni.showModal({\r\n      title: '提示',\r\n      content: '确定要删除吗？',\r\n      cancelText: '取消',\r\n      confirmText: '确定',\r\n      success: (res) => {\r\n        if (res.confirm) {\r\n          http.delete(`/online/cgform/api/form/${pageParams.id}/${data.id}`).then((res) => {\r\n            toast.success('删除成功~')\r\n            getData()\r\n          })\r\n        }\r\n      },\r\n      fail: (err) => {\r\n        console.log(err)\r\n      },\r\n    })\r\n  } else if (option.key === 'detail') {\r\n    handleView(data)\r\n  }\r\n}\r\nconst handleRow = ({ rowIndex }) => {\r\n  handleEdit(dataList.value[rowIndex])\r\n}\r\nconst init = () => {\r\n  pageParams = paramsStore.getPageParams('onlineTable')?.data ?? {}\r\n  console.log('pageParams:', pageParams)\r\n  getColumns()\r\n  getData()\r\n}\r\ninit()\r\n\r\nonMounted(() => {\r\n  // 监听刷新列表事件\r\n  uni.$on('refreshList', () => {\r\n    getData()\r\n  })\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n//\r\n.add {\r\n  height: 70upx;\r\n  width: 70upx;\r\n  text-align: center;\r\n  line-height: 70upx;\r\n  background-color: #fff;\r\n  border-radius: 50%;\r\n  position: fixed;\r\n  bottom: 80upx;\r\n  right: 30upx;\r\n  box-shadow: 0 0 5px 2px rgba(0, 0, 0, 0.1);\r\n  color: #666;\r\n}\r\n:deep(.wd-table__content) {\r\n  .wot-theme-light {\r\n    // width: 100%;\r\n    // height: 100%;\r\n  }\r\n}\r\n\r\n:deep(.wd-table) {\r\n  --wot-table-font-size: 14px;\r\n  .wd-table__body {\r\n    --wot-table-color: var(--color-gray);\r\n  }\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-sub/online/onlineTable.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useToast", "useRouter", "useUserStore", "useParamsStore", "ref", "reactive", "http", "uni", "res", "rowIndex", "onMounted"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmDA,MAAA,kBAA4B,MAAA;;;;;;;;;AAO5B,UAAM,QAAQA,cAAAA,SAAS;AACvB,UAAM,SAASC,gCAAAA,UAAU;AACPC,eAAa,aAAA;AAC/B,UAAM,cAAcC,iBAAAA,eAAe;AAC7B,UAAA,aAAa,SAAS;AACtB,UAAA,EAAE,eAAe;AACjB,UAAA,EAAE,aAAa;AAEf,UAAA,UAAUC,cAAI,IAAA,EAAE;AAChB,UAAA,cAAcA,cAAI,IAAA,EAAE;AACpB,UAAA,aAAaA,kBAAI,KAAK;AACtB,UAAA,SAASA,kBAAI,CAAC;AACd,UAAA,WAAWA,kBAAI,EAAE;AACjB,UAAA,YAAYA,kBAAI,CAAC;AACvB,QAAI,aAAkB,CAAC;AACHA,kBAAAA,IAAI,CAAC;AAEzB,UAAM,qBAAqBC,cAAAA,SAAS;AAAA,MAClC,MAAM;AAAA,MACN,OAAO;AAAA,MACP,MAAM,CAAC;AAAA,MACP,SAAS;AAAA,QACP,EAAE,KAAK,QAAQ,MAAM,QAAQ,OAAO,KAAK;AAAA,QACzC,EAAE,KAAK,UAAU,MAAM,QAAQ,OAAO,KAAK;AAAA,QAC3C,EAAE,KAAK,UAAU,MAAM,UAAU,OAAO,MAAM,OAAO,MAAM;AAAA,MAAA;AAAA,IAC7D,CACD;AACD,UAAM,cAAc,MAAM;AACjB,aAAA;AAAA,QACL,QAAQ,OAAO;AAAA,QACf,UAAU,SAAS;AAAA,QACnB,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,UAAU;AAAA,MACZ;AAAA,IACF;AACM,UAAA,WAAWD,cAAI,IAAA,EAAE;AACvB,UAAM,aAAa,MAAM;AACvB,aAAO,IAAI,QAAc,CAAC,QAAQ,WAAW;AACrC,cAAA,WAAW,CAAC,SAAS;AACzB,gBAAM,MAAM,KAAK;AACjB,gBAAM,gBAAgB;AACtB,cAAI,QAAQ;AACZ,cAAI,OAAO,GAAG;AACJ,oBAAA;AAAA,UAAA;AAEV,gBAAM,QAAQ,SAAS,SAAS,MAAM,gBAAgB,gBAAgB,OAAO;AAC7E,kBAAQ,QAAQ,KAAK,IAAI,CAAC,SAAS;AAC1B,mBAAA,iCACF,OADE;AAAA,cAEL,MAAM,KAAK;AAAA,cACX,OAAO,KAAK;AAAA,cACZ,OAAO,KAAK;AAAA,cACZ;AAAA,YACF;AAAA,UAAA,CACD;AAAA,QACH;AAEGE,wBAAA,IAAI,iCAAiC,WAAW,EAAE,EAAE,EACpD,KAAK,CAAC,QAAa;;AAClB,cAAI,IAAI,SAAS;AACX,iBAAA,eAAI,WAAJ,mBAAY,YAAZ,mBAAqB,QAAQ;AAC/B,0BAAY,QAAQ,IAAI;AACf,uBAAA,IAAI,OAAO,OAAO;AAAA,YAAA;AAAA,UAC7B,OACK;AACC,kBAAA,QAAQ,IAAI,OAAO;AAAA,UAAA;AAAA,QAC3B,CACD,EACA,MAAM,CAAC,QAAQ;AACd,gBAAM,MAAM,SAAS;AAAA,QAAA,CACtB;AAAA,MAAA,CACJ;AAAA,IACH;AACA,UAAM,UAAU,MAAM;AACpBA,iBAAAA,KACG,IAAI,8BAA8B,WAAW,EAAE,IAAI,mBAAK,YAAA,EAAe,EACvE,KAAK,CAAC,QAAa;;AAClB,YAAI,IAAI,SAAS;AACf,mBAAS,SAAQ,eAAI,WAAJ,mBAAY,YAAZ,YAAuB,CAAC;AAC/B,oBAAA,QAAQ,IAAI,OAAO;AAAA,QAAA,OACxB;AACC,gBAAA,QAAQ,IAAI,OAAO;AAAA,QAAA;AAAA,MAC3B,CACD,EACA,MAAM,CAAC,QAAQ;AACd,cAAM,MAAM,WAAW;AAAA,MAAA,CACxB;AAAA,IACL;AACA,UAAM,oBAAoB,CAAC,EAAE,YAAY;AACvC,aAAO,QAAQ;AACP,cAAA;AAAA,IACV;AAEA,UAAM,WAAW,MAAM;AACrB,aAAO,KAAK;AAAA,QACV,MAAM;AAAA,QACN,QAAQ,EAAE,aAAa,WAAW,WAAW,aAAa,WAAW,SAAS;AAAA,MAAA,CAC/E;AAAA,IACH;AAEM,UAAA,aAAa,CAAC,WAAW;AAC7B,aAAO,KAAK;AAAA,QACV,MAAM;AAAA,QACN,QAAQ,EAAE,aAAa,WAAW,WAAW,aAAa,WAAW,UAAU,IAAI,OAAO,GAAG;AAAA,MAAA,CAC9F;AAAA,IACH;AAEM,UAAA,aAAa,CAAC,WAAW;AAC7B,aAAO,KAAK;AAAA,QACV,MAAM;AAAA,QACN,QAAQ,EAAE,aAAa,WAAW,WAAW,aAAa,WAAW,UAAU,IAAI,OAAO,GAAG;AAAA,MAAA,CAC9F;AAAA,IACH;AAEM,UAAA,kBAAkB,CAAC,SAAS;AAChC,yBAAmB,OAAO;AAC1B,yBAAmB,OAAO;AAAA,IAC5B;AAEA,UAAM,eAAe,CAAC,EAAE,QAAQ,WAAW;AACrC,UAAA,OAAO,QAAQ,QAAQ;AACzB,mBAAW,IAAI;AAAA,MAAA,WACN,OAAO,QAAQ,UAAU;AAClCC,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,SAAS,CAAC,QAAQ;AAChB,gBAAI,IAAI,SAAS;AACVD,yBAAAA,KAAA,OAAO,2BAA2B,WAAW,EAAE,IAAI,KAAK,EAAE,EAAE,EAAE,KAAK,CAACE,SAAQ;AAC/E,sBAAM,QAAQ,OAAO;AACb,wBAAA;AAAA,cAAA,CACT;AAAA,YAAA;AAAA,UAEL;AAAA,UACA,MAAM,CAAC,QAAQ;AACb,oBAAQ,IAAI,GAAG;AAAA,UAAA;AAAA,QACjB,CACD;AAAA,MAAA,WACQ,OAAO,QAAQ,UAAU;AAClC,mBAAW,IAAI;AAAA,MAAA;AAAA,IAEnB;AACA,UAAM,YAAY,CAAC,EAAE,UAAAC,gBAAe;AACvB,iBAAA,SAAS,MAAMA,SAAQ,CAAC;AAAA,IACrC;AACA,UAAM,OAAO,MAAM;;AACjB,oBAAa,uBAAY,cAAc,aAAa,MAAvC,mBAA0C,SAA1C,YAAkD,CAAC;AACxD,cAAA,IAAI,eAAe,UAAU;AAC1B,iBAAA;AACH,cAAA;AAAA,IACV;AACK,SAAA;AAELC,kBAAAA,UAAU,MAAM;AAEVH,0BAAA,IAAI,eAAe,MAAM;AACnB,gBAAA;AAAA,MAAA,CACT;AAAA,IAAA,CACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1ND,GAAG,WAAW,eAAe;"}