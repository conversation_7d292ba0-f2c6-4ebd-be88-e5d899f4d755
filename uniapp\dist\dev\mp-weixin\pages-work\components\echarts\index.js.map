{"version": 3, "file": "index.js", "sources": ["../../../../../../src/pages-work/components/echarts/index.vue", "../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvcGFnZXMtd29yay9jb21wb25lbnRzL2VjaGFydHMvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n\t<l-echart ref=\"chartRef\"></l-echart>\r\n</template>\r\n\r\n<script setup>\r\n//#ifdef MP-WEIXIN\r\n//const uniEchartsWx = require('../../../uni_modules/lime-echart/static/echarts.min');\r\nconst uniEchartsWx = require('../../../pages-work/static/echarts.min');\r\n// #endif\r\n\r\n//#ifndef MP-WEIXIN\r\nimport * as uniEcharts from 'echarts'\r\n// #endif\r\nconst props = defineProps({\r\n\toption: {\r\n      type: Object,\r\n      default: () => {\r\n          return {}\r\n      }\r\n\t},\r\n\tmapName: {\r\n\t  type: String,\r\n\t  default:'china'\r\n\t},\r\n\tmapData: {\r\n\t  type: Object,\r\n\t  default: () => {\r\n\t      return {}\r\n\t  }\r\n\t}\r\n})\r\nconst chartRef = ref(null)\r\n\r\nwatchEffect(()=>{\r\n\tprops.option && init(props.option)\r\n})\r\n\r\nfunction init(finalOption){\r\n\tif(finalOption){\r\n\t\t// 组件能被调用必须是组件的节点已经被渲染到页面上\r\n\t\tsetTimeout(async()=>{\r\n\t\t\tif(!chartRef.value) return\r\n      //#ifdef MP-WEIXIN\r\n\t\t\tconst myChartWx = await chartRef.value.init(uniEchartsWx)\r\n\t\t\tif(props.mapName){\r\n        uniEchartsWx.registerMap(props.mapName,props.mapData)\r\n\t\t\t}\r\n      myChartWx.setOption(finalOption)\r\n      // #endif\r\n\r\n      //#ifndef MP-WEIXIN\r\n\t\t\tconst myChart = await chartRef.value.init(uniEcharts)\r\n\t\t\tif(props.mapName){\r\n\t\t\t\tuniEcharts.registerMap(props.mapName,props.mapData)\r\n\t\t\t}\r\n      myChart.setOption(finalOption)\r\n      // #endif\r\n\t\t},300)\r\n\t}\r\n}\r\n\r\n\r\n</script>\r\n\r\n<style scoped>\r\n\t.content{\r\n\r\n\t}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-work/components/echarts/index.vue'\nwx.createComponent(Component)"], "names": ["ref", "watchEffect"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,UAAM,eAAe,QAAQ,wCAAwC;AAMrE,UAAM,QAAQ;AAkBd,UAAM,WAAWA,cAAG,IAAC,IAAI;AAEzBC,kBAAAA,YAAY,MAAI;AACf,YAAM,UAAU,KAAK,MAAM,MAAM;AAAA,IAClC,CAAC;AAED,aAAS,KAAK,aAAY;AACzB,UAAG,aAAY;AAEd,mBAAW,MAAS;AACnB,cAAG,CAAC,SAAS;AAAO;AAEpB,gBAAM,YAAY,MAAM,SAAS,MAAM,KAAK,YAAY;AACxD,cAAG,MAAM,SAAQ;AACZ,yBAAa,YAAY,MAAM,SAAQ,MAAM,OAAO;AAAA,UACxD;AACE,oBAAU,UAAU,WAAW;AAAA,QAUlC,IAAC,GAAG;AAAA,MACL;AAAA,IACF;;;;;;;;;;;AC1DA,GAAG,gBAAgB,SAAS;"}