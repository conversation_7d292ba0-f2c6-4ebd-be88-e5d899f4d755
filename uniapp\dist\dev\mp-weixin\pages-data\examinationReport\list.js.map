{"version": 3, "file": "list.js", "sources": ["../../../../../src/pages-data/examinationReport/list.vue", "../../../../../uniPage:/cGFnZXMtZGF0YVxleGFtaW5hdGlvblJlcG9ydFxsaXN0LnZ1ZQ"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n    <PageLayout>\r\n        <template #navbar>\r\n            <NavBar title=\"院外检查报告\" />\r\n        </template>\r\n        <scroll-view class=\"page-scroll-view\" scroll-y=\"true\">\r\n            <view class=\"form-container\">\r\n                <view class=\"form-header\">\r\n                    <text class=\"form-title\">院外检查报告</text>\r\n                </view>\r\n\r\n                <view class=\"list-container\">\r\n                    <!-- 时间筛选区域 -->\r\n                    <view class=\"filter-section\">\r\n                        <view class=\"date-filter\">\r\n                            <text class=\"filter-label\">填写时间：</text>\r\n                            <picker mode=\"date\" :value=\"startDate\" start=\"1900-01-01\" end=\"2099-12-31\"\r\n                                @change=\"onStartDateChange\">\r\n                                <view class=\"date-picker\">\r\n                                    <text>{{ startDate || '开始日期' }}</text>\r\n                                    <uni-icons type=\"calendar\" size=\"15\" color=\"#666666\" />\r\n                                </view>\r\n                            </picker>\r\n                            <view class=\"date-separator\">至</view>\r\n                            <picker mode=\"date\" :value=\"endDate\" start=\"1900-01-01\" end=\"2099-12-31\"\r\n                                @change=\"onEndDateChange\">\r\n                                <view class=\"date-picker\">\r\n                                    <text>{{ endDate || '结束日期' }}</text>\r\n                                    <uni-icons type=\"calendar\" size=\"15\" color=\"#666666\" />\r\n                                </view>\r\n                            </picker>\r\n                            <view class=\"reset-btn\" @click=\"resetDateFilter\">\r\n                                <uni-icons type=\"reload\" size=\"18\" color=\"#07C160\" />\r\n                            </view>\r\n                        </view>\r\n                        <view class=\"buttons-row\">\r\n                            <view class=\"filter-btn\" @click=\"searchByDate\">\r\n                                <uni-icons type=\"search\" size=\"16\" color=\"#FFFFFF\"></uni-icons>\r\n                                <text>查询</text>\r\n                            </view>\r\n                            <view class=\"add-btn\" @click=\"navigateToAdd\">\r\n                                <uni-icons type=\"plusempty\" size=\"16\" color=\"#FFFFFF\"></uni-icons>\r\n                                <text>新增</text>\r\n                            </view>\r\n                        </view>\r\n                    </view>\r\n\r\n                    <!-- 列表内容 -->\r\n                    <view class=\"list-content\">\r\n                        <view class=\"empty-state\" v-if=\"recordList.length === 0\">\r\n                            <text>暂无记录</text>\r\n                        </view>\r\n\r\n                        <view class=\"record-card\" v-for=\"(item, index) in recordList\" :key=\"index\"\r\n                            @click=\"navigateToDetail(item)\">\r\n                            <view class=\"record-info\">\r\n                                <view class=\"record-icon\">\r\n                                    <image src=\"https://www.mograine.cn/images/report.png\" class=\"card-image\"\r\n                                        mode=\"contain\">\r\n                                    </image>\r\n                                </view>\r\n                                <view class=\"record-details\">\r\n                                    <text class=\"record-date\">{{ item.date }}</text>\r\n                                    <view class=\"record-values\">\r\n                                        <view class=\"report-type\" v-if=\"item.reportType\">\r\n                                            <text class=\"value-label\">报告类型：</text>\r\n                                            <text class=\"value-item\">{{ item.reportType || '常规检查' }}</text>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                            </view>\r\n                            <view class=\"record-action\">\r\n                                <text class=\"action-text\">查看详情</text>\r\n                                <uni-icons type=\"right\" size=\"16\" color=\"#07C160\"></uni-icons>\r\n                            </view>\r\n                        </view>\r\n                    </view>\r\n                </view>\r\n            </view>\r\n        </scroll-view>\r\n    </PageLayout>\r\n\r\n    <!-- 日期选择器弹窗 -->\r\n    <uni-calendar ref=\"calendar\" :insert=\"false\" @confirm=\"dateConfirm\" :range=\"false\" />\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { ref, onMounted } from 'vue'\r\nimport { useUserStore } from '@/store/user'\r\nimport { onShow } from '@dcloudio/uni-app'\r\n\r\ndefineOptions({\r\n    name: 'ExaminationReportList',\r\n})\r\n\r\nconst userStore = useUserStore()\r\nconst recordList = ref([])\r\nconst loading = ref(false)\r\n\r\n// 日期筛选\r\nconst startDate = ref('')\r\nconst endDate = ref('')\r\nconst datePickerType = ref('') // 用于标识当前打开的是开始日期还是结束日期选择器\r\nconst calendar = ref(null)\r\n\r\n// 处理开始日期变化\r\nfunction onStartDateChange(e: any) {\r\n    startDate.value = e.detail.value\r\n}\r\n\r\n// 处理结束日期变化\r\nfunction onEndDateChange(e: any) {\r\n    endDate.value = e.detail.value\r\n}\r\n\r\n// 日期选择确认\r\nconst dateConfirm = (e) => {\r\n    const selectedDate = e.fulldate\r\n    if (datePickerType.value === 'start') {\r\n        startDate.value = selectedDate\r\n    } else if (datePickerType.value === 'end') {\r\n        endDate.value = selectedDate\r\n    }\r\n}\r\n\r\nconst fetchData = () => {\r\n    loading.value = true\r\n\r\n    // 获取基础URL\r\n    const url = `${import.meta.env.VITE_SERVER_BASEURL}/patient/getreport`\r\n\r\n    const params: Record<string, any> = {}\r\n    // 添加日期过滤参数\r\n    if (startDate.value) {\r\n        params.beginDate = startDate.value\r\n    }\r\n    if (endDate.value) {\r\n        params.endDate = endDate.value\r\n    }\r\n    try {\r\n        params.userId = userStore.userInfo.userid\r\n        console.log('用户ID:', params.userId)\r\n    } catch (error) {\r\n        console.error('获取用户ID失败:', error)\r\n        uni.showToast({\r\n            title: '获取用户信息失败',\r\n            icon: 'none'\r\n        })\r\n        loading.value = false\r\n        return\r\n    }\r\n\r\n    uni.request({\r\n        url,\r\n        method: 'GET',\r\n        data: params,\r\n        success: (res: any) => {\r\n            const response = res.data\r\n            console.log('接口返回数据:', response)\r\n\r\n            if (response && response.code === 200) {\r\n                // 从result.records中获取数据列表\r\n                if (response.result && response.result.records) {\r\n                    const rawRecords = response.result.records || []\r\n                    // 处理数据格式，将后端格式转换为前端需要的格式\r\n                    recordList.value = rawRecords.map((item: any) => ({\r\n                        date: item.creatTime,\r\n                        reportType: item.reportTypeList ? item.reportTypeList.join('、') : '', // 将报告类型数组转为字符串\r\n                        idList: item.idList || []\r\n                    }))\r\n                    console.log('解析后的列表数据:', recordList.value)\r\n                } else {\r\n                    recordList.value = []\r\n                    console.log('没有找到records数据')\r\n                }\r\n            } else {\r\n                uni.showToast({\r\n                    title: response.msg || '获取数据失败',\r\n                    icon: 'none'\r\n                })\r\n            }\r\n        },\r\n        fail: (err: any) => {\r\n            console.error('获取数据失败:', err)\r\n            uni.showToast({\r\n                title: '网络异常，请稍后重试',\r\n                icon: 'none'\r\n            })\r\n        },\r\n        complete: () => {\r\n            loading.value = false\r\n        }\r\n    })\r\n}\r\n// 按日期查询\r\nconst searchByDate = () => {\r\n    fetchData()\r\n}\r\n\r\nconst resetDateFilter = () => {\r\n    startDate.value = ''\r\n    endDate.value = ''\r\n    fetchData() // 清空日期条件并重新获取数据\r\n}\r\nconst navigateToAdd = () => {\r\n    uni.navigateTo({\r\n        url: '/pages-data/examinationReport/form'\r\n    })\r\n}\r\n\r\n// 跳转到详情页面\r\nconst navigateToDetail = (item) => {\r\n    // 将idList数组转换为字符串传递\r\n    const idListStr = item.idList ? item.idList.join(',') : ''\r\n    uni.navigateTo({\r\n        url: `/pages-data/examinationReport/form?idList=${idListStr}&mode=view`\r\n    })\r\n}\r\n\r\n// 页面加载时获取数据\r\nonMounted(() => {\r\n    fetchData()\r\n})\r\n\r\nonShow(() => {\r\n    resetDateFilter()\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-scroll-view {\r\n    height: calc(100vh - 44px);\r\n    width: 100%;\r\n    background-color: #f5f7fa;\r\n}\r\n\r\n.form-container {\r\n    padding: 20rpx;\r\n    padding-bottom: 120rpx;\r\n}\r\n\r\n.form-header {\r\n    margin-bottom: 20rpx;\r\n    text-align: center;\r\n\r\n    .form-title {\r\n        font-size: 36rpx;\r\n        font-weight: bold;\r\n        color: #333;\r\n    }\r\n}\r\n\r\n.list-container {\r\n    .filter-section {\r\n        background-color: #FFFFFF;\r\n        border-radius: 16rpx;\r\n        padding: 24rpx;\r\n        margin-bottom: 20rpx;\r\n        box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\r\n        .date-filter {\r\n            display: flex;\r\n            align-items: center;\r\n            margin-bottom: 20rpx;\r\n            flex-wrap: wrap;\r\n            gap: 10rpx 0;\r\n\r\n            .filter-label {\r\n                color: #333333;\r\n                font-size: 28rpx;\r\n                margin-right: 10rpx;\r\n                font-weight: 500;\r\n            }\r\n\r\n            .date-picker {\r\n                flex: 2;\r\n                min-width: 210rpx;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: space-between;\r\n                background-color: #F7F7F7;\r\n                padding: 12rpx 20rpx;\r\n                border-radius: 8rpx;\r\n\r\n                text {\r\n                    color: #666666;\r\n                    font-size: 26rpx;\r\n                    white-space: nowrap;\r\n                    overflow: hidden;\r\n                    text-overflow: ellipsis;\r\n                }\r\n            }\r\n\r\n            .date-separator {\r\n                margin: 0 15rpx;\r\n                color: #666666;\r\n                font-size: 28rpx;\r\n            }\r\n\r\n            .reset-btn {\r\n                margin-left: 3rpx;\r\n                width: 35rpx;\r\n                height: 35rpx;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                background-color: rgba(7, 193, 96, 0.1);\r\n                border-radius: 50%;\r\n                transition: all 0.2s;\r\n\r\n                &:active {\r\n                    transform: scale(0.9) rotate(180deg);\r\n                }\r\n            }\r\n        }\r\n\r\n        .buttons-row {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            gap: 20rpx;\r\n\r\n            .filter-btn,\r\n            .add-btn {\r\n                flex: 1;\r\n                background-color: #07C160;\r\n                color: #FFFFFF;\r\n                text-align: center;\r\n                padding: 15rpx 0;\r\n                border-radius: 8rpx;\r\n                font-size: 28rpx;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                box-shadow: 0 4rpx 8rpx rgba(7, 193, 96, 0.2);\r\n                transition: all 0.3s;\r\n\r\n                &:active {\r\n                    transform: scale(0.98);\r\n                    box-shadow: 0 2rpx 4rpx rgba(7, 193, 96, 0.2);\r\n                }\r\n\r\n                text {\r\n                    margin-left: 10rpx;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .list-content {\r\n        background-color: #F5F7FA;\r\n        border-radius: 12rpx;\r\n        padding: 10rpx;\r\n\r\n        .empty-state {\r\n            padding: 60rpx 0;\r\n            text-align: center;\r\n            color: #999999;\r\n            font-size: 28rpx;\r\n            display: flex;\r\n            flex-direction: column;\r\n            align-items: center;\r\n\r\n            .empty-image {\r\n                width: 200rpx;\r\n                height: 200rpx;\r\n                margin-bottom: 20rpx;\r\n            }\r\n        }\r\n\r\n        .record-card {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n            padding: 24rpx;\r\n            background-color: white;\r\n            border-radius: 16rpx;\r\n            margin-bottom: 16rpx;\r\n            box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\r\n            transition: all 0.3s;\r\n\r\n            &:active {\r\n                transform: scale(0.99);\r\n                background-color: #fafafa;\r\n            }\r\n\r\n            .record-info {\r\n                display: flex;\r\n                align-items: flex-start;\r\n                flex: 1;\r\n\r\n                .record-icon {\r\n                    margin-right: 16rpx;\r\n                    background-color: rgba(7, 193, 96, 0.1);\r\n                    width: 80rpx;\r\n                    height: 80rpx;\r\n                    border-radius: 50%;\r\n                    display: flex;\r\n                    align-items: center;\r\n                    justify-content: center;\r\n                    flex-shrink: 0;\r\n\r\n                    .card-image {\r\n                        width: 50rpx;\r\n                        height: 50rpx;\r\n                        display: block;\r\n                    }\r\n                }\r\n\r\n                .record-details {\r\n                    flex: 1;\r\n                    overflow: hidden;\r\n\r\n                    .record-date {\r\n                        font-size: 30rpx;\r\n                        color: #333333;\r\n                        font-weight: 500;\r\n                        margin-bottom: 8rpx;\r\n                    }\r\n\r\n                    .record-values {\r\n                        display: flex;\r\n                        flex-direction: column;\r\n\r\n                        .patient-detail,\r\n                        .report-type {\r\n                            display: flex;\r\n                            margin-top: 4rpx;\r\n                            align-items: center;\r\n\r\n                            .value-label {\r\n                                color: #666666;\r\n                                font-size: 24rpx;\r\n                                margin-right: 10rpx;\r\n                            }\r\n\r\n                            .value-item {\r\n                                color: #666666;\r\n                                font-size: 24rpx;\r\n                                flex: 1;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n\r\n            .record-action {\r\n                display: flex;\r\n                align-items: center;\r\n                margin-left: 20rpx;\r\n\r\n                .action-text {\r\n                    color: #07C160;\r\n                    font-size: 26rpx;\r\n                    margin-right: 10rpx;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-data/examinationReport/list.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "ref", "uni", "onMounted", "onShow"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmGA,UAAM,YAAYA,WAAAA,aAAa;AACzB,UAAA,aAAaC,cAAI,IAAA,EAAE;AACnB,UAAA,UAAUA,kBAAI,KAAK;AAGnB,UAAA,YAAYA,kBAAI,EAAE;AAClB,UAAA,UAAUA,kBAAI,EAAE;AAChB,UAAA,iBAAiBA,kBAAI,EAAE;AACvB,UAAA,WAAWA,kBAAI,IAAI;AAGzB,aAAS,kBAAkB,GAAQ;AACrB,gBAAA,QAAQ,EAAE,OAAO;AAAA,IAAA;AAI/B,aAAS,gBAAgB,GAAQ;AACrB,cAAA,QAAQ,EAAE,OAAO;AAAA,IAAA;AAIvB,UAAA,cAAc,CAAC,MAAM;AACvB,YAAM,eAAe,EAAE;AACnB,UAAA,eAAe,UAAU,SAAS;AAClC,kBAAU,QAAQ;AAAA,MAAA,WACX,eAAe,UAAU,OAAO;AACvC,gBAAQ,QAAQ;AAAA,MAAA;AAAA,IAExB;AAEA,UAAM,YAAY,MAAM;AACpB,cAAQ,QAAQ;AAGV,YAAA,MAAM,GAAG,6BAAmC;AAElD,YAAM,SAA8B,CAAC;AAErC,UAAI,UAAU,OAAO;AACjB,eAAO,YAAY,UAAU;AAAA,MAAA;AAEjC,UAAI,QAAQ,OAAO;AACf,eAAO,UAAU,QAAQ;AAAA,MAAA;AAEzB,UAAA;AACO,eAAA,SAAS,UAAU,SAAS;AAC3B,gBAAA,IAAI,SAAS,OAAO,MAAM;AAAA,eAC7B,OAAO;AACJ,gBAAA,MAAM,aAAa,KAAK;AAChCC,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACT;AACD,gBAAQ,QAAQ;AAChB;AAAA,MAAA;AAGJA,oBAAAA,MAAI,QAAQ;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,SAAS,CAAC,QAAa;AACnB,gBAAM,WAAW,IAAI;AACb,kBAAA,IAAI,WAAW,QAAQ;AAE3B,cAAA,YAAY,SAAS,SAAS,KAAK;AAEnC,gBAAI,SAAS,UAAU,SAAS,OAAO,SAAS;AAC5C,oBAAM,aAAa,SAAS,OAAO,WAAW,CAAC;AAE/C,yBAAW,QAAQ,WAAW,IAAI,CAAC,UAAe;AAAA,gBAC9C,MAAM,KAAK;AAAA,gBACX,YAAY,KAAK,iBAAiB,KAAK,eAAe,KAAK,GAAG,IAAI;AAAA;AAAA,gBAClE,QAAQ,KAAK,UAAU,CAAA;AAAA,cAAC,EAC1B;AACM,sBAAA,IAAI,aAAa,WAAW,KAAK;AAAA,YAAA,OACtC;AACH,yBAAW,QAAQ,CAAC;AACpB,sBAAQ,IAAI,eAAe;AAAA,YAAA;AAAA,UAC/B,OACG;AACHA,0BAAAA,MAAI,UAAU;AAAA,cACV,OAAO,SAAS,OAAO;AAAA,cACvB,MAAM;AAAA,YAAA,CACT;AAAA,UAAA;AAAA,QAET;AAAA,QACA,MAAM,CAAC,QAAa;AACR,kBAAA,MAAM,WAAW,GAAG;AAC5BA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACT;AAAA,QACL;AAAA,QACA,UAAU,MAAM;AACZ,kBAAQ,QAAQ;AAAA,QAAA;AAAA,MACpB,CACH;AAAA,IACL;AAEA,UAAM,eAAe,MAAM;AACb,gBAAA;AAAA,IACd;AAEA,UAAM,kBAAkB,MAAM;AAC1B,gBAAU,QAAQ;AAClB,cAAQ,QAAQ;AACN,gBAAA;AAAA,IACd;AACA,UAAM,gBAAgB,MAAM;AACxBA,oBAAAA,MAAI,WAAW;AAAA,QACX,KAAK;AAAA,MAAA,CACR;AAAA,IACL;AAGM,UAAA,mBAAmB,CAAC,SAAS;AAE/B,YAAM,YAAY,KAAK,SAAS,KAAK,OAAO,KAAK,GAAG,IAAI;AACxDA,oBAAAA,MAAI,WAAW;AAAA,QACX,KAAK,6CAA6C,SAAS;AAAA,MAAA,CAC9D;AAAA,IACL;AAGAC,kBAAAA,UAAU,MAAM;AACF,gBAAA;AAAA,IAAA,CACb;AAEDC,kBAAAA,OAAO,MAAM;AACO,sBAAA;AAAA,IAAA,CACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrOD,GAAG,WAAW,eAAe;"}