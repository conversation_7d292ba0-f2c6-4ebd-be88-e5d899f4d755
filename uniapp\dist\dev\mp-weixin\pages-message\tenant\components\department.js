"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_user = require("../../../store/user.js");
const utils_http = require("../../../utils/http.js");
require("../../../plugin/uni-mini-router/router/index.js");
const plugin_uniMiniRouter_core_index = require("../../../plugin/uni-mini-router/core/index.js");
const store_pageParams = require("../../../store/page-params.js");
const common_assets = require("../../../common/assets.js");
if (!Array) {
  const _easycom_wd_img2 = common_vendor.resolveComponent("wd-img");
  const _easycom_wd_cell2 = common_vendor.resolveComponent("wd-cell");
  const _easycom_wd_status_tip2 = common_vendor.resolveComponent("wd-status-tip");
  const _easycom_wd_toast2 = common_vendor.resolveComponent("wd-toast");
  (_easycom_wd_img2 + _easycom_wd_cell2 + _easycom_wd_status_tip2 + _easycom_wd_toast2)();
}
const _easycom_wd_img = () => "../../../node-modules/wot-design-uni/components/wd-img/wd-img.js";
const _easycom_wd_cell = () => "../../../node-modules/wot-design-uni/components/wd-cell/wd-cell.js";
const _easycom_wd_status_tip = () => "../../../node-modules/wot-design-uni/components/wd-status-tip/wd-status-tip.js";
const _easycom_wd_toast = () => "../../../node-modules/wot-design-uni/components/wd-toast/wd-toast.js";
if (!Math) {
  (_easycom_wd_img + _easycom_wd_cell + _easycom_wd_status_tip + _easycom_wd_toast)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "department",
  props: ["tenantId"],
  setup(__props) {
    const router = plugin_uniMiniRouter_core_index.useRouter();
    store_user.useUserStore();
    const toast = common_vendor.useToast();
    const props = __props;
    const dataSource = common_vendor.ref([]);
    const paramsStore = store_pageParams.useParamsStore();
    const api = {
      depTree: "/sys/sysDepart/queryBookDepTreeSync",
      list: "/sys/user/list"
    };
    const getImg = (item) => {
      if (item.parentId != null) {
        return common_assets.folderImg;
      } else {
        return item.avatar;
      }
    };
    const getName = (item) => {
      if (item.parentId != null) {
        return item.departName;
      } else {
        return item.realname;
      }
    };
    const getRadius = (item) => {
      return item.parentId != null ? null : "50%";
    };
    const handleClick = (item) => {
      if (item.parentId != null) {
        query({ id: item.id });
      } else {
        paramsStore.setPageParams("personPage", { data: item });
        router.push({ name: "personPage" });
      }
    };
    const query = (params = {}) => {
      var _a;
      const pararms = { pid: (_a = params.id) != null ? _a : "", departId: params.id, tenantId: props.tenantId };
      Promise.all([
        utils_http.http.get(api.depTree, pararms),
        pararms.pid ? utils_http.http.get(api.list, pararms) : Promise.resolve({ success: true, result: { records: [] } })
      ]).then((res) => {
        var _a2, _b, _c, _d, _e;
        if (res[0].success == true && res[1].success == true) {
          const result = (_b = (_a2 = res[0]) == null ? void 0 : _a2.result) != null ? _b : [];
          const records = (_e = (_d = (_c = res[1]) == null ? void 0 : _c.result) == null ? void 0 : _d.records) != null ? _e : [];
          const data = [...result, ...records];
          if (params.id) {
            if (data.length) {
              dataSource.value = data;
            } else {
              toast.warning("下一级无数据~");
            }
          } else {
            dataSource.value = data;
          }
        }
      }).catch((res) => {
      });
    };
    query();
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: dataSource.value.length
      }, dataSource.value.length ? {
        b: common_vendor.f(dataSource.value, (item, index, i0) => {
          return {
            a: "9f64ca52-1-" + i0 + "," + ("9f64ca52-0-" + i0),
            b: common_vendor.p({
              customClass: "mr2",
              radius: getRadius(item),
              width: 30,
              height: 30,
              src: getImg(item)
            }),
            c: common_vendor.t(getName(item)),
            d: common_vendor.o(($event) => handleClick(item)),
            e: "9f64ca52-0-" + i0
          };
        }),
        c: common_vendor.p({
          border: true,
          clickable: true
        })
      } : {
        d: common_vendor.p({
          image: "content",
          tip: "暂无内容"
        })
      });
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-9f64ca52"]]);
wx.createComponent(Component);
//# sourceMappingURL=department.js.map
