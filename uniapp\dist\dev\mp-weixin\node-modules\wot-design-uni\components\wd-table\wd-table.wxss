/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 辅助函数
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/* 转换成字符串 */
/* 判断是否存在 Modifier */
/* 判断是否存在伪类 */
/**
 * 主题色切换
 * @params $theme-color 主题色
 * @params $type 变暗’dark‘ 变亮 'light'
 * @params $mix-color 自己设置的混色
 */
/**
 * 颜色结果切换， 如果开启线性渐变色 使用渐变色，如果没有开启，那么使用主题色
 * @params $open-linear 是否开启线性渐变色
 * @params $deg 渐变色角度
 * @params $theme-color 当前配色
 * @params [Array] $set 主题色明暗设置，与 $color-list 数量对应
 * @params [Array] $color-list 渐变色顺序， $color-list 和 $per-list 数量相同
 * @params [Array] $per-list 渐变色比例
 */
/**
 * UI规范基础变量
 */
/*----------------------------------------- Theme color. start ----------------------------------------*/
/* 主题颜色 */
/* 辅助色 */
/* 文字颜色（默认浅色背景下 */
/* 暗黑模式 */
/* 图形颜色 */
/*----------------------------------------- Theme color. end -------------------------------------------*/
/*-------------------------------- Theme color application size.  start --------------------------------*/
/* 文字字号 */
/* 文字字重 */
/* 尺寸 */
/*-------------------------------- Theme color application size.  end --------------------------------*/
/* component var */
/* action-sheet */
/* badge */
/* button */
/* cell */
/* calendar */
/* checkbox */
/* collapse */
/* divider */
/* drop-menu */
/* input-number */
/* input */
/* textarea */
/* loadmore */
/* message-box */
/* notice-bar */
/* pagination */
/* picker */
/* col-picker */
/* overlay */
/* popup */
/* progress */
/* radio */
/* search */
/* slider */
/* sort-button */
/* steps */
/* switch */
/* tabs */
/* tag */
/* toast */
/* loading */
/* tooltip */
/* popover */
/* grid-item */
/* statustip */
/* card */
/* upload */
/* curtain */
/* notify */
/* skeleton */
/* circle */
/* swiper */
/* swiper-nav */
/* segmented */
/* tabbar */
/* tabbar-item */
/* navbar */
/* navbar-capsule */
/* table */
/* sidebar */
/* sidebar-item */
/* fab */
/* count-down */
/* keyboard */
/* number-keyboard */
/* passwod-input */
/* form-item */
/* backtop */
/* index-bar */
/* text */
/* video-preview */
/* img-cropper */
/* floating-panel */
/* signature */
/**
 * 混合宏
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/**
 * 辅助函数
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/* 转换成字符串 */
/* 判断是否存在 Modifier */
/* 判断是否存在伪类 */
/**
 * 主题色切换
 * @params $theme-color 主题色
 * @params $type 变暗’dark‘ 变亮 'light'
 * @params $mix-color 自己设置的混色
 */
/**
 * 颜色结果切换， 如果开启线性渐变色 使用渐变色，如果没有开启，那么使用主题色
 * @params $open-linear 是否开启线性渐变色
 * @params $deg 渐变色角度
 * @params $theme-color 当前配色
 * @params [Array] $set 主题色明暗设置，与 $color-list 数量对应
 * @params [Array] $color-list 渐变色顺序， $color-list 和 $per-list 数量相同
 * @params [Array] $per-list 渐变色比例
 */
/**
  * BEM，定义块（b)
  */
/* 定义元素（e），对于伪类，会自动将 e 嵌套在 伪类 底下 */
/* 此方法用于生成穿透样式 */
/* 定义元素（e），对于伪类，会自动将 e 嵌套在 伪类 底下 */
/* 定义状态（m） */
/* 定义状态（m） */
/* 对于需要需要嵌套在 m 底下的 e，调用这个混合宏，一般在切换整个组件的状态，如切换颜色的时候 */
/* 状态，生成 is-$state 类名 */
/**
  * 常用混合宏
  */
/* 单行超出隐藏 */
/* 多行超出隐藏 */
/* 清除浮动 */
/* 0.5px 边框 指定方向*/
/* 0.5px 边框 环绕 */
/**
  * 三角形实现尖角样式，适用于背景透明情况
  * @param $size 三角形高，底边为 $size * 2
  * @param $bg 三角形背景颜色
  */
/**
  * 正方形实现尖角样式，适用于背景不透明情况
  * @param $size 正方形边长
  * @param $bg 正方形背景颜色
  * @param $z-index z-index属性值，不得大于外部包裹器
  * @param $box-shadow 阴影
*/
.wot-theme-dark .wd-table.data-v-d423803e {
  background: var(--wot-dark-background, #131313);
}
.wot-theme-dark .wd-table.is-border.data-v-d423803e {
  border: 1px solid var(--wot-dark-border-color, #3a3a3c);
}
.wot-theme-dark .wd-table__cell.data-v-d423803e {
  color: var(--wot-dark-color, var(--wot-color-white, rgb(255, 255, 255)));
  background: var(--wot-dark-background2, #1b1b1b);
}
.wot-theme-dark .wd-table__cell.is-stripe.data-v-d423803e {
  background: var(--wot-dark-background4, #323233);
}
.wot-theme-dark .wd-table__cell.is-border.data-v-d423803e {
  border-right: 1px solid var(--wot-dark-border-color, #3a3a3c);
  border-bottom: 1px solid var(--wot-dark-border-color, #3a3a3c);
}
.wot-theme-dark .wd-table__cell.is-shadow.data-v-d423803e::after {
  background: linear-gradient(270deg, rgba(17, 17, 17, 0.2) 0%, rgba(0, 0, 0, 0) 100%);
}
.wd-table.data-v-d423803e {
  position: relative;
  width: 100%;
  overflow: auto;
  background: var(--wot-table-bg, #ffffff);
}
.wd-table.is-border.data-v-d423803e {
  border: 1px solid var(--wot-table-border-color, #ececec);
}
.wd-table.data-v-d423803e  .wd-table-col:last-child .wd-table__cell.is-border {
  border-right: none;
}
.wd-table__header.data-v-d423803e {
  width: 100%;
  height: 50px;
  position: sticky;
  top: 0;
  z-index: 2;
  display: flex;
  overflow-x: auto;
  white-space: nowrap;
}
.wd-table__body.data-v-d423803e {
  width: 100%;
  box-sizing: border-box;
}
.wd-table__content.data-v-d423803e {
  display: flex;
}
.wd-table__content--header.data-v-d423803e {
  position: sticky;
  top: 0;
  z-index: 2;
}
.wd-table__cell.data-v-d423803e {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  background: var(--wot-table-bg, #ffffff);
  width: 100px;
  min-height: 50px;
  padding: 8px 10px;
  font-size: var(--wot-table-font-size, 13px);
  color: var(--wot-table-color, var(--wot-font-gray-1, rgba(0, 0, 0, 0.9)));
  --wot-sort-button-height: 30px;
}
.wd-table__cell.is-border.data-v-d423803e {
  border-right: 1px solid var(--wot-table-border-color, #ececec);
  border-bottom: 1px solid var(--wot-table-border-color, #ececec);
}
.wd-table__cell.is-stripe.data-v-d423803e {
  background: var(--wot-table-stripe-bg, #f3f3f3);
}
.wd-table__cell.is-fixed.data-v-d423803e {
  position: sticky;
  z-index: 1;
  left: 0;
}
.wd-table__cell.is-shadow.data-v-d423803e::after {
  content: " ";
  position: absolute;
  height: 100%;
  right: -30rpx;
  top: 0;
  width: 30rpx;
  height: 100%;
  background: linear-gradient(270deg, rgba(255, 255, 255, 0) 0%, rgba(0, 0, 0, 0.04) 100%);
}
.wd-table__cell.is-left.data-v-d423803e {
  justify-content: flex-start;
}
.wd-table__cell.is-center.data-v-d423803e {
  justify-content: center;
}
.wd-table__cell.is-right.data-v-d423803e {
  justify-content: flex-end;
}
.wd-table__wrapper.data-v-d423803e {
  width: 100%;
  overflow: auto;
}
.wd-table__inner.data-v-d423803e {
  display: flex;
  flex-direction: column;
}
.wd-table__header-row.data-v-d423803e {
  display: flex;
  flex-direction: row;
  width: 100%;
}
.wd-table__body.data-v-d423803e {
  display: flex;
  width: 100%;
}
.wd-table__value.is-ellipsis.data-v-d423803e {
  word-break: break-all;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}