"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
const common_vendor = require("../../../common/vendor.js");
const plugin_uniMiniRouter_interfaces_index = require("../interfaces/index.js");
const plugin_uniMiniRouter_utils_index = require("../utils/index.js");
const navMethods = {
  navigateTo: common_vendor.index.navigateTo,
  redirectTo: common_vendor.index.redirectTo,
  reLaunch: common_vendor.index.reLaunch,
  switchTab: common_vendor.index.switchTab,
  navigateBack: common_vendor.index.navigateBack
};
function navjump(to, router, navType) {
  const url = getRoutePath(to, router);
  switch (navType) {
    case "push":
      navMethods.navigateTo({ url });
      break;
    case "replace":
      navMethods.redirectTo({ url });
      break;
    case "pushTab":
      navMethods.switchTab({ url });
      break;
    case "replaceAll":
      navMethods.reLaunch({ url });
      break;
    default:
      throw new Error("无效的路由类型，请确保提供正确的路由类型");
  }
  return;
}
function getRoutePath(to, router) {
  let url = "";
  let query = {};
  if (typeof to === "string") {
    url = to;
  } else {
    if (to.name) {
      const route = router.routes.find((item) => {
        return item.name === to.name;
      });
      if (route && route.path) {
        url = route.path;
      } else {
        throw new Error("您正在尝试访问的路由未在路由表中定义。请检查您的路由配置。");
      }
      query = to.params;
    } else if (to.path) {
      url = plugin_uniMiniRouter_utils_index.beautifyUrl(`/${to.path.split("?")[0]}`);
      query = __spreadValues(__spreadValues({}, plugin_uniMiniRouter_utils_index.getUrlParams(to.path)), to.query || {});
    }
    if (query) {
      query = plugin_uniMiniRouter_utils_index.queryStringify(query);
      url = plugin_uniMiniRouter_utils_index.setUrlParams(url, query);
    }
  }
  return url;
}
function getCurrentPage() {
  const pages = getCurrentPages();
  return pages.length > 0 ? pages[pages.length - 1] : void 0;
}
function saveCurrRouteByCurrPage(router) {
  router.route.value = getCurrentPageRoute(router);
}
function getCurrentPageRoute(router) {
  const page = getCurrentPage();
  if (!page || !page.route || !router.routes) {
    return;
  }
  const currRoute = getRouteByPath(`/${page.route}`, router);
  if (page.$page) {
    currRoute.fullPath = page.$page.fullPath ? page.$page.fullPath : "";
    currRoute.query = page.$page.fullPath ? plugin_uniMiniRouter_utils_index.getUrlParams(page.$page.fullPath) : {};
    currRoute.params = page.$page.fullPath ? plugin_uniMiniRouter_utils_index.getUrlParams(page.$page.fullPath) : {};
  }
  return currRoute;
}
function getRouteByPath(path, router) {
  path = plugin_uniMiniRouter_utils_index.beautifyUrl(path.split("?")[0]);
  const route = router.routes.find((route2) => {
    return route2.path === path || route2.aliasPath === path;
  });
  return JSON.parse(JSON.stringify(route));
}
function registerEachHooks(router, hookType, userGuard) {
  router.guardHooks[hookType] = [userGuard];
}
const oldMethods = {
  navigateTo: common_vendor.index.navigateTo,
  redirectTo: common_vendor.index.redirectTo,
  reLaunch: common_vendor.index.reLaunch,
  switchTab: common_vendor.index.switchTab,
  navigateBack: common_vendor.index.navigateBack
};
function rewriteNavMethod(router) {
  plugin_uniMiniRouter_interfaces_index.NavMethod.forEach((name) => {
    navMethods[name] = function(options) {
      if (name === "navigateBack") {
        oldMethods[name](options);
      } else {
        if (router.guardHooks.beforeHooks && router.guardHooks.beforeHooks[0]) {
          const to = getRouteByPath(options.url, router);
          guardToPromiseFn(router.guardHooks.beforeHooks[0], to, router.route.value).then((resp) => {
            if (resp === true) {
              oldMethods[name](options);
            } else {
              if (typeof resp === "string") {
                const url = getRoutePath(resp, router);
                oldMethods[name]({ url });
              } else if (resp.navType === "back") {
                oldMethods["navigateBack"](resp);
              } else {
                const url = getRoutePath(resp, router);
                oldMethods[resp.navType ? plugin_uniMiniRouter_interfaces_index.NavTypeEnum[resp.navType] : name]({ url });
              }
            }
          }).catch((error) => {
            throw error;
          });
        } else {
          oldMethods[name](options);
        }
      }
    };
  });
}
function guardToPromiseFn(guard, to, from) {
  return new Promise((reslove, reject) => {
    const next = (rule) => {
      next._called = true;
      if (rule === false) {
        reject({});
      } else if (rule === void 0 || rule === true) {
        reslove(true);
      } else {
        reslove(rule);
      }
    };
    const guardReturn = guard.call(void 0, to, from, next);
    let guardCall = Promise.resolve(guardReturn);
    if (guard.length < 3)
      guardCall = guardCall.then(next);
    if (guard.length > 2) {
      const message = `The "next" callback was never called inside of ${guard.name ? '"' + guard.name + '"' : ""}:
${guard.toString()}
. If you are returning a value instead of calling "next", make sure to remove the "next" parameter from your function.`;
      if (guardReturn !== null && typeof guardReturn === "object" && "then" in guardReturn) {
        guardCall = guardCall.then((resolvedValue) => {
          if (!next._called) {
            console.warn(message);
            return Promise.reject(new Error("Invalid navigation guard"));
          }
          return resolvedValue;
        });
      } else {
        if (!next._called) {
          console.warn(message);
          reject(new Error("Invalid navigation guard"));
          return;
        }
      }
    }
    guardCall.catch((err) => reject(err));
  });
}
exports.getCurrentPageRoute = getCurrentPageRoute;
exports.navjump = navjump;
exports.registerEachHooks = registerEachHooks;
exports.rewriteNavMethod = rewriteNavMethod;
exports.saveCurrRouteByCurrPage = saveCurrRouteByCurrPage;
//# sourceMappingURL=index.js.map
