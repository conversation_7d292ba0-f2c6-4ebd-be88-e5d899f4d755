"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../../common/vendor.js");
const pagesWork_components_echarts_props = require("../props.js");
const pagesWork_components_common_echartUtil = require("../../common/echartUtil.js");
const utils_is = require("../../../../utils/is.js");
const pagesWork_components_hooks_useEchart = require("../../hooks/useEchart.js");
const uni_modules_daTree_utils = require("../../../../uni_modules/da-tree/utils.js");
if (!Math) {
  (statusTip + echartsUniapp)();
}
const echartsUniapp = () => "../index.js";
const statusTip = () => "../../statusTip.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  props: __spreadValues({}, pagesWork_components_echarts_props.echartProps),
  setup(__props) {
    const props = __props;
    const option = common_vendor.ref({});
    let chartOption = {
      title: {
        show: true
      },
      legend: {
        show: true,
        data: [],
        bottom: "5%"
      },
      xAxis: {
        type: "category"
      },
      yAxis: {
        type: "value"
      },
      series: []
    };
    let [{ dataSource, reload, pageTips, config }, { queryData }] = pagesWork_components_hooks_useEchart.useChartHook(
      props,
      initOption
    );
    function initOption(data) {
      let chartData = dataSource.value;
      if (typeof chartData === "string") {
        chartData = JSON.parse(chartData);
      }
      if (chartData && chartData.length > 0) {
        let configOption = config.option;
        let dataset = getDataset(chartData);
        chartOption.series = [];
        let series = configOption.series && configOption.series.length > 0 ? JSON.parse(JSON.stringify(configOption.series[0])) : {};
        dataset.dimensions.forEach((name, index) => {
          let hasCustom = configOption.customColor && configOption.customColor.length > 0 && configOption.customColor[index];
          let color = hasCustom ? configOption.customColor[index].color : pagesWork_components_common_echartUtil.getRandomColor(index);
          let edgeColor = utils_is.isObject(color) ? `rgb(${color.r},${color.g},${color.b})` : color;
          chartOption.series.push(__spreadProps(__spreadValues({
            type: "scatter",
            name,
            data: dataset.source[index],
            symbolSize: 30
          }, series), {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: "rgba(25, 100, 150, 0.5)",
              shadowOffsetY: 5,
              color: {
                type: "radial",
                x: 0.4,
                y: 0.3,
                r: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgb(255, 255, 255)"
                    // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: edgeColor
                    // 100% 处的颜色
                  }
                ]
              }
            }
          }));
        });
        chartOption.legend.data = chartOption.series.map((item) => item.name);
        if (config.option.xAxis && config.option.xAxis.type) {
          chartOption.yAxis["type"] = config.option.xAxis["type"] == "value" ? "category" : "value";
        }
        if (props.config && config.option) {
          common_vendor.merge(chartOption, config.option);
          chartOption = pagesWork_components_common_echartUtil.handleTotalAndUnit(props.compName, chartOption, config, chartData);
          chartOption = pagesWork_components_common_echartUtil.disposeGridLayout(props.compName, chartOption);
          option.value = uni_modules_daTree_utils.deepClone(chartOption);
          pageTips.show = false;
        }
      } else {
        pageTips.status = 1;
        pageTips.show = true;
      }
    }
    function getDataset(chartData) {
      let dataObj = { dimensions: [], source: [] };
      let dataList = [];
      let typeArr = new Set(chartData.map((item) => item["type"]));
      let dimensions = [...typeArr];
      typeArr.forEach((type) => {
        let arr = chartData.filter((item) => item.type == type).map((item) => {
          return [item.name, item.value];
        });
        dataList.push(arr);
      });
      dataObj.dimensions = dimensions;
      dataObj.source = dataList;
      return dataObj;
    }
    common_vendor.onMounted(() => {
      queryData();
    });
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.unref(pageTips).show
      }, common_vendor.unref(pageTips).show ? {
        b: common_vendor.p({
          status: common_vendor.unref(pageTips).status
        })
      } : {
        c: common_vendor.p({
          option: common_vendor.unref(option)
        })
      });
    };
  }
});
wx.createComponent(_sfc_main);
//# sourceMappingURL=index.js.map
