{"version": 3, "file": "platform.js", "sources": ["../../../../src/utils/platform.ts"], "sourcesContent": ["export const platform = __UNI_PLATFORM__\r\nexport const isH5 = __UNI_PLATFORM__ === 'h5'\r\nexport const isApp = __UNI_PLATFORM__ === 'app'\r\nexport const isMp = __UNI_PLATFORM__.startsWith('mp-')\r\nexport const isMpWeixin = __UNI_PLATFORM__.startsWith('mp-weixin')\r\nexport const isMpAplipay = __UNI_PLATFORM__.startsWith('mp-alipay')\r\nexport const isMpToutiao = __UNI_PLATFORM__.startsWith('mp-toutiao')\r\n\r\nconst PLATFORM = {\r\n  platform,\r\n  isH5,\r\n  isApp,\r\n  isMp,\r\n  isMpWeixin,\r\n  isMpAplipay,\r\n  isMpToutiao,\r\n}\r\nexport default PLATFORM\r\n"], "names": [], "mappings": ";AAAO,MAAM,WAAW;AACjB,MAAM,OAAO;AAEP,MAAA,OAAO,YAAiB,WAAW,KAAK;AACxC,MAAA,aAAa,YAAiB,WAAW,WAAW;;;;;"}