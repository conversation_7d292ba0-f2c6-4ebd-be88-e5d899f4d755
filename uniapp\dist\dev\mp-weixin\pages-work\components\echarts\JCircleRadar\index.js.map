{"version": 3, "file": "index.js", "sources": ["../../../../../../../src/pages-work/components/echarts/JCircleRadar/index.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvcGFnZXMtd29yay9jb21wb25lbnRzL2VjaGFydHMvSkNpcmNsZVJhZGFyL2luZGV4LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"content\">\r\n    <statusTip v-if=\"pageTips.show\" :status=\"pageTips.status\"></statusTip>\r\n    <echartsUniapp v-else :option=\"option\"></echartsUniapp>\r\n  </view>\r\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { echartProps } from '@/pages-work/components/echarts/props'\r\nimport {\r\n  deepMerge,\r\n  handleTotalAndUnit,\r\n  disposeGridLayout,\r\n  getCustomColor,\r\n} from '../../common/echartUtil'\r\nimport { isNumber } from '@/utils/is'\r\nimport useChartHook from '@/pages-work/components/hooks/useEchart'\r\nimport { deepClone } from '@/uni_modules/da-tree/utils'\r\nimport echartsUniapp from '@/pages-work/components/echarts/index.vue'\r\nimport statusTip from '@/pages-work/components/statusTip.vue'\r\nimport {merge} from \"lodash-es\";\r\n//组件传参\r\nconst props = defineProps({\r\n  ...echartProps,\r\n})\r\n\r\n//最终图表配置项\r\nconst option = ref({})\r\nlet chartOption = {\r\n  legend: {\r\n    r: 0,\r\n    data: ['文综', '理综'],\r\n  },\r\n  radar: [\r\n    {\r\n      shape: 'circle',\r\n      indicator: [],\r\n    },\r\n  ],\r\n  series: [\r\n    {\r\n      type: 'radar',\r\n      data: [\r\n        {\r\n          value: [],\r\n          name: '',\r\n        },\r\n      ],\r\n    },\r\n  ],\r\n}\r\n//图表数据查询\r\nlet [{ dataSource, reload, pageTips, config }, { queryData }] = useChartHook(props, initOption)\r\n\r\n//初始化图表配置项\r\nfunction initOption(data) {\r\n  let chartData: any = dataSource.value\r\n  if (typeof chartData === 'string') {\r\n    chartData = JSON.parse(chartData)\r\n  }\r\n  if (chartData && chartData.length > 0) {\r\n    //显示坐标轴前几项\r\n    if (config.dataFilterNum && isNumber(config.dataFilterNum)) {\r\n      chartData = chartData.slice(0, config.dataFilterNum)\r\n    }\r\n    //最大值\r\n    const maxValue = Math.max(...chartData.map(({ value }) => value))\r\n    //图例类型\r\n    let typeArr = Array.from(new Set(chartData.map((item) => item.type)))\r\n    //雷达数据\r\n    let indicator = Array.from(\r\n      new Set(\r\n        chartData.map((item) => {\r\n          let { name, max } = item\r\n          return { name, max: max ? max : maxValue }\r\n        }),\r\n      ),\r\n    )\r\n    let data = []\r\n    const colors = getCustomColor(config.option.customColor)\r\n    //设置配色\r\n    typeArr.forEach((type, index) => {\r\n      let obj = { name: type, itemStyle: { color: colors[index].color || null } }\r\n      let chartArr = chartData.filter((item) => type === item.type)\r\n      obj['value'] = chartArr.map((item) => item.value)\r\n      //data数据\r\n      data.push(obj)\r\n    })\r\n    chartOption.radar[0].indicator = indicator\r\n    chartOption.legend['data'] = typeArr\r\n    chartOption.series[0]['data'] = data\r\n    //中心坐标\r\n    chartOption.radar[0].center = [\r\n      (config.option.grid.left || 50) + '%',\r\n      (config.option.grid.top || 50) + '%',\r\n    ]\r\n\r\n    // 合并配置\r\n    if (props.config && config.option) {\r\n      merge(chartOption, config.option)\r\n      chartOption = handleTotalAndUnit(props.compName, chartOption, config, chartData)\r\n      chartOption = disposeGridLayout(props.compName, chartOption, config, chartData)\r\n      option.value = deepClone(chartOption)\r\n      pageTips.show = false\r\n    }\r\n  } else {\r\n    pageTips.status = 1\r\n    pageTips.show = true\r\n  }\r\n}\r\n\r\nonMounted(() => {\r\n  queryData()\r\n})\r\n</script>\r\n<style>\r\n.content {\r\n  padding: 10px;\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-work/components/echarts/JCircleRadar/index.vue'\nwx.createComponent(Component)"], "names": ["ref", "useChartHook", "isNumber", "data", "getCustomColor", "merge", "handleTotalAndUnit", "disposeGridLayout", "deepClone", "onMounted", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAkBA,MAAA,gBAA0B,MAAA;AAC1B,MAAA,YAAsB,MAAA;;;;;AAGtB,UAAM,QAAQ;AAKR,UAAA,SAASA,cAAI,IAAA,EAAE;AACrB,QAAI,cAAc;AAAA,MAChB,QAAQ;AAAA,QACN,GAAG;AAAA,QACH,MAAM,CAAC,MAAM,IAAI;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL;AAAA,UACE,OAAO;AAAA,UACP,WAAW,CAAA;AAAA,QAAC;AAAA,MAEhB;AAAA,MACA,QAAQ;AAAA,QACN;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,YACJ;AAAA,cACE,OAAO,CAAC;AAAA,cACR,MAAM;AAAA,YAAA;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IAEJ;AAEA,QAAI,CAAC,EAAE,YAAY,QAAQ,UAAU,OAAA,GAAU,EAAE,WAAW,IAAIC,kDAAa,OAAO,UAAU;AAG9F,aAAS,WAAW,MAAM;AACxB,UAAI,YAAiB,WAAW;AAC5B,UAAA,OAAO,cAAc,UAAU;AACrB,oBAAA,KAAK,MAAM,SAAS;AAAA,MAAA;AAE9B,UAAA,aAAa,UAAU,SAAS,GAAG;AAErC,YAAI,OAAO,iBAAiBC,SAAS,SAAA,OAAO,aAAa,GAAG;AAC1D,sBAAY,UAAU,MAAM,GAAG,OAAO,aAAa;AAAA,QAAA;AAG/C,cAAA,WAAW,KAAK,IAAI,GAAG,UAAU,IAAI,CAAC,EAAE,YAAY,KAAK,CAAC;AAEhE,YAAI,UAAU,MAAM,KAAK,IAAI,IAAI,UAAU,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,CAAC;AAEpE,YAAI,YAAY,MAAM;AAAA,UACpB,IAAI;AAAA,YACF,UAAU,IAAI,CAAC,SAAS;AAClB,kBAAA,EAAE,MAAM,IAAA,IAAQ;AACpB,qBAAO,EAAE,MAAM,KAAK,MAAM,MAAM,SAAS;AAAA,YAC1C,CAAA;AAAA,UAAA;AAAA,QAEL;AACA,YAAIC,QAAO,CAAC;AACZ,cAAM,SAASC,uCAAA,eAAe,OAAO,OAAO,WAAW;AAE/C,gBAAA,QAAQ,CAAC,MAAM,UAAU;AAC/B,cAAI,MAAM,EAAE,MAAM,MAAM,WAAW,EAAE,OAAO,OAAO,KAAK,EAAE,SAAS,KAAA,EAAO;AAC1E,cAAI,WAAW,UAAU,OAAO,CAAC,SAAS,SAAS,KAAK,IAAI;AAC5D,cAAI,OAAO,IAAI,SAAS,IAAI,CAAC,SAAS,KAAK,KAAK;AAEhDD,gBAAK,KAAK,GAAG;AAAA,QAAA,CACd;AACW,oBAAA,MAAM,CAAC,EAAE,YAAY;AACrB,oBAAA,OAAO,MAAM,IAAI;AAC7B,oBAAY,OAAO,CAAC,EAAE,MAAM,IAAIA;AAEpB,oBAAA,MAAM,CAAC,EAAE,SAAS;AAAA,WAC3B,OAAO,OAAO,KAAK,QAAQ,MAAM;AAAA,WACjC,OAAO,OAAO,KAAK,OAAO,MAAM;AAAA,QACnC;AAGI,YAAA,MAAM,UAAU,OAAO,QAAQ;AAC3BE,8BAAA,aAAa,OAAO,MAAM;AAChC,wBAAcC,uCAAmB,mBAAA,MAAM,UAAU,aAAa,QAAQ,SAAS;AAC/E,wBAAcC,uCAAkB,kBAAA,MAAM,UAAU,WAA8B;AACvE,iBAAA,QAAQC,mCAAU,WAAW;AACpC,mBAAS,OAAO;AAAA,QAAA;AAAA,MAClB,OACK;AACL,iBAAS,SAAS;AAClB,iBAAS,OAAO;AAAA,MAAA;AAAA,IAClB;AAGFC,kBAAAA,UAAU,MAAM;AACJ,gBAAA;AAAA,IAAA,CACX;;;;;;;;;;;;;;;;AChHD,GAAG,gBAAgBC,SAAS;"}