"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const store_user = require("../../store/user.js");
const utils_http = require("../../utils/http.js");
if (!Array) {
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  const _easycom_wd_loading2 = common_vendor.resolveComponent("wd-loading");
  const _easycom_wd_text2 = common_vendor.resolveComponent("wd-text");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_uni_icons2 + _easycom_wd_loading2 + _easycom_wd_text2 + _easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
const _easycom_wd_loading = () => "../../node-modules/wot-design-uni/components/wd-loading/wd-loading.js";
const _easycom_wd_text = () => "../../node-modules/wot-design-uni/components/wd-text/wd-text.js";
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  (_easycom_uni_icons + _easycom_wd_loading + _easycom_wd_text + _easycom_PageLayout)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "data",
  options: {
    styleIsolation: "shared"
  }
}), {
  __name: "index",
  setup(__props) {
    const globalData = getApp().globalData;
    const { systemInfo, navHeight } = globalData;
    const { statusBarHeight } = systemInfo;
    const userStore = store_user.useUserStore();
    const loadingPatient = common_vendor.ref(false);
    const patientList = common_vendor.ref([]);
    const searchParams = common_vendor.ref({
      userName: ""
    });
    const isPatientRole = common_vendor.computed(() => {
      const userRoles = userStore.userInfo.roleList || [];
      return Array.isArray(userRoles) && userRoles.includes("1");
    });
    const isDoctorRole = common_vendor.computed(() => {
      const userRoles = userStore.userInfo.roleList || [];
      return Array.isArray(userRoles) && userRoles.includes("0");
    });
    const isSocialRole = common_vendor.computed(() => {
      const userRoles = userStore.userInfo.roleList || [];
      return Array.isArray(userRoles) && userRoles.includes("2");
    });
    const dataItems = common_vendor.ref([
      {
        title: "日常体征监测",
        type: "vital-signs",
        path: "/pages-data/vital-signs/list",
        iconPath: "https://www.mograine.cn/images/vital-signs.png"
      },
      {
        title: "用药情况",
        type: "medication",
        path: "/pages-data/medication/list",
        iconPath: "https://www.mograine.cn/images/medication.png"
      },
      {
        title: "院外检查报告",
        type: "examinationReport",
        path: "/pages-data/examinationReport/list",
        iconPath: "https://www.mograine.cn/images/report.png"
      },
      {
        title: "慢性心衰患者监测表\n（3次/周）",
        type: "monitor",
        path: "/pages-data/monitor/list",
        iconPath: "https://www.mograine.cn/images/monitor.png"
      },
      {
        title: "慢性心衰患者心理量表\n（1次/周）",
        type: "psychology",
        path: "/pages-data/psychology/list",
        iconPath: "https://www.mograine.cn/images/psychology.png"
      },
      {
        title: "慢性心衰患者日常生活指数评估\n（1次/周）",
        type: "dailyLife",
        path: "/pages-data/dailyLife/list",
        iconPath: "https://www.mograine.cn/images/dailyLife.png"
      }
    ]);
    const navigateToDetail = (item) => {
      if (!isPatientRole.value) {
        common_vendor.index.showToast({
          title: "请先填写知情同意与登记！",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      common_vendor.index.navigateTo({
        url: item.path
      });
    };
    const viewPatientDetail = (item) => {
      common_vendor.index.navigateTo({
        url: `/pages/data/detail?id=${item.userId}`
      });
    };
    const calculateAge = (birthDate) => {
      if (!birthDate)
        return "-";
      const birth = new Date(birthDate);
      const today = /* @__PURE__ */ new Date();
      let age = today.getFullYear() - birth.getFullYear();
      const monthDiff = today.getMonth() - birth.getMonth();
      if (monthDiff < 0 || monthDiff === 0 && today.getDate() < birth.getDate()) {
        age--;
      }
      return age;
    };
    const handleSearch = () => {
      if (!isSocialRole.value || !isDoctorRole.value) {
        common_vendor.index.showToast({
          title: "请先注册账号！",
          icon: "none",
          duration: 2e3
        });
        return;
      } else {
        fetchPatientList();
      }
    };
    const hasCommAndDataPermission = common_vendor.computed(() => {
      const userRoles = userStore.userInfo.roleList || [];
      const userCategory = Number(userStore.userInfo.userCategory);
      if (userCategory === 0) {
        return userRoles.includes("0");
      } else if (userCategory === 2) {
        return userRoles.includes("2");
      }
      return false;
    });
    const fetchPatientList = () => __async(this, null, function* () {
      if (!hasCommAndDataPermission.value) {
        patientList.value = [];
        return;
      }
      try {
        loadingPatient.value = true;
        const params = {
          realName: searchParams.value.userName
        };
        utils_http.http.get("/sys/user/1/getUserList", params).then((res) => {
          if (res.success && res.result) {
            console.log("API返回的患者数据:", res.result.records);
            patientList.value = res.result.records || [];
          } else {
            common_vendor.index.showToast({
              title: "获取患者列表失败",
              icon: "none"
            });
          }
        });
      } finally {
        loadingPatient.value = false;
      }
    });
    common_vendor.onMounted(() => {
      fetchPatientList();
    });
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: Number(common_vendor.unref(userStore).userInfo.userCategory) === 1
      }, Number(common_vendor.unref(userStore).userInfo.userCategory) === 1 ? {
        b: common_vendor.f(dataItems.value, (item, index, i0) => {
          return {
            a: item.iconPath,
            b: common_vendor.t(item.title),
            c: "d3826533-2-" + i0 + ",d3826533-1",
            d: index,
            e: common_vendor.o(($event) => navigateToDetail(item), index)
          };
        }),
        c: common_vendor.p({
          type: "right",
          size: "14",
          color: "#07C160"
        })
      } : {}, {
        d: Number(common_vendor.unref(userStore).userInfo.userCategory) === 0 || Number(common_vendor.unref(userStore).userInfo.userCategory) === 2
      }, Number(common_vendor.unref(userStore).userInfo.userCategory) === 0 || Number(common_vendor.unref(userStore).userInfo.userCategory) === 2 ? common_vendor.e$1({
        e: searchParams.value.userName,
        f: common_vendor.o(($event) => searchParams.value.userName = $event.detail.value),
        g: searchParams.value.userName
      }, searchParams.value.userName ? {
        h: common_vendor.o(($event) => searchParams.value.userName = ""),
        i: common_vendor.p({
          type: "clear",
          size: "14",
          color: "#666666"
        })
      } : {}, {
        j: common_vendor.p({
          type: "search",
          size: "16",
          color: "#FFFFFF"
        }),
        k: common_vendor.o(handleSearch),
        l: loadingPatient.value
      }, loadingPatient.value ? {} : patientList.value.length === 0 ? {
        n: common_vendor.p({
          text: "暂无记录"
        })
      } : {
        o: common_vendor.f(patientList.value, (item, index, i0) => {
          return {
            a: item.avatarUrl || "/static/default-avatar.png",
            b: common_vendor.t(item.realName || "-"),
            c: common_vendor.t(item.sex || "-"),
            d: common_vendor.t(calculateAge(item.birthDate) || "-"),
            e: common_vendor.t(item.phoneNumber || "-"),
            f: "d3826533-7-" + i0 + ",d3826533-1",
            g: index,
            h: common_vendor.o(($event) => viewPatientDetail(item), index)
          };
        }),
        p: common_vendor.p({
          type: "right",
          size: "16",
          color: "#07C160"
        })
      }, {
        m: patientList.value.length === 0
      }) : {}, {
        q: common_vendor.p({
          navLeftArrow: false,
          navLeftText: ""
        })
      });
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d3826533"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=index.js.map
