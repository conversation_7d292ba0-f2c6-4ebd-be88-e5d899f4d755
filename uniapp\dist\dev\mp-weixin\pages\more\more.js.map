{"version": 3, "file": "more.js", "sources": ["../../../../../src/pages/more/more.vue", "../../../../../uniPage:/cGFnZXMvbW9yZS9tb3JlLnZ1ZQ"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n  <PageLayout navTitle=\"更多\" backRouteName=\"index\" routeMethod=\"pushTab\">\r\n    <wd-cell-group border clickable>\r\n      <template v-for=\"(item, index) in routeList\" :key=\"index\">\r\n        <wd-cell :title=\"item.title\" is-link @click=\"goPage(item)\">\r\n          <template #icon>\r\n            <wd-img\r\n              height=\"20\"\r\n              width=\"20\"\r\n              :src=\"isLocalConfig ? item.icon : getFileAccessHttpUrl(item.icon)\"\r\n            ></wd-img>\r\n          </template>\r\n        </wd-cell>\r\n      </template>\r\n    </wd-cell-group>\r\n  </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { us, os } from '@/common/work'\r\nimport { cache, getFileAccessHttpUrl } from '@/common/uitls'\r\nimport {\r\n  ACCESS_TOKEN,\r\n  USER_NAME,\r\n  USER_INFO,\r\n  APP_ROUTE,\r\n  APP_CONFIG,\r\n  HOME_CONFIG_EXPIRED_TIME,\r\n} from '@/common/constants'\r\nimport { http } from '@/utils/http'\r\nimport { onLaunch, onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'\r\nimport { useToast, useMessage, useNotify } from 'wot-design-uni'\r\nimport { useRouter } from '@/plugin/uni-mini-router'\r\n//\r\nconst toast = useToast()\r\nconst router = useRouter()\r\nconst routeList = ref([])\r\nconst isLocalConfig = getApp().globalData.isLocalConfig\r\nlet type = 'common'\r\n\r\nconst init = () => {\r\n  if (isLocalConfig) {\r\n    routeList.value = type == 'common' ? us.data : os.data\r\n  } else {\r\n    var indexRouteList = cache(APP_ROUTE)\r\n    routeList.value = indexRouteList.filter((item) => item.type == type)\r\n  }\r\n}\r\nconst goPage = (item) => {\r\n  let page = item.routeIndex\r\n  console.log('-----------page------------', page)\r\n  if (!page) {\r\n    toast.info('该功能暂未实现')\r\n  } else {\r\n    if (page.indexOf('/app/online') == 0) {\r\n      let code = page.substring(page.lastIndexOf('/') + 1)\r\n      let real = { desformCode: code, desformName: item.title }\r\n      uni.navigateTo({\r\n        url: '/pages/check/onlineForm/add?item=' + encodeURIComponent(JSON.stringify(real)),\r\n      })\r\n    } else if (page.indexOf('/app/desform') == 0) {\r\n      let code = page.substring(page.lastIndexOf('/') + 1)\r\n      let real = { desformCode: code, desformName: item.title }\r\n      uni.navigateTo({\r\n        url: '/pages/check/designForm/designForm?item=' + encodeURIComponent(JSON.stringify(real)),\r\n      })\r\n    } else {\r\n      // if (!hasRoute(page)) {\r\n      //   this.$tip.alert('路由地址不存在')\r\n      // } else {\r\n      //   this.$Router.replace({ name: page, params: { backRouteName: 'index' } })\r\n      // }\r\n      router.replace({ name: page, params: { backRouteName: 'index' } })\r\n    }\r\n  }\r\n}\r\nonLoad((params) => {\r\n  type = params.type\r\n  init()\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n//\r\n:deep(.wd-img) {\r\n  margin-right: 16upx;\r\n}\r\n:deep(.wd-cell) {\r\n  line-height: 30px;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages/more/more.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useToast", "useRouter", "ref", "us", "os", "cache", "APP_ROUTE", "uni", "onLoad"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAsCA,UAAM,QAAQA,cAAAA,SAAS;AACvB,UAAM,SAASC,gCAAAA,UAAU;AACnB,UAAA,YAAYC,cAAI,IAAA,EAAE;AAClB,UAAA,gBAAgB,SAAS,WAAW;AAC1C,QAAI,OAAO;AAEX,UAAM,OAAO,MAAM;AACjB,UAAI,eAAe;AACjB,kBAAU,QAAQ,QAAQ,WAAWC,YAAAA,GAAG,OAAOC,YAAAA,GAAG;AAAA,MAAA,OAC7C;AACD,YAAA,iBAAiBC,mBAAMC,0BAAS;AACpC,kBAAU,QAAQ,eAAe,OAAO,CAAC,SAAS,KAAK,QAAQ,IAAI;AAAA,MAAA;AAAA,IAEvE;AACM,UAAA,SAAS,CAAC,SAAS;AACvB,UAAI,OAAO,KAAK;AACR,cAAA,IAAI,+BAA+B,IAAI;AAC/C,UAAI,CAAC,MAAM;AACT,cAAM,KAAK,SAAS;AAAA,MAAA,OACf;AACL,YAAI,KAAK,QAAQ,aAAa,KAAK,GAAG;AACpC,cAAI,OAAO,KAAK,UAAU,KAAK,YAAY,GAAG,IAAI,CAAC;AACnD,cAAI,OAAO,EAAE,aAAa,MAAM,aAAa,KAAK,MAAM;AACxDC,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK,sCAAsC,mBAAmB,KAAK,UAAU,IAAI,CAAC;AAAA,UAAA,CACnF;AAAA,QACQ,WAAA,KAAK,QAAQ,cAAc,KAAK,GAAG;AAC5C,cAAI,OAAO,KAAK,UAAU,KAAK,YAAY,GAAG,IAAI,CAAC;AACnD,cAAI,OAAO,EAAE,aAAa,MAAM,aAAa,KAAK,MAAM;AACxDA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK,6CAA6C,mBAAmB,KAAK,UAAU,IAAI,CAAC;AAAA,UAAA,CAC1F;AAAA,QAAA,OACI;AAME,iBAAA,QAAQ,EAAE,MAAM,MAAM,QAAQ,EAAE,eAAe,QAAQ,GAAG;AAAA,QAAA;AAAA,MACnE;AAAA,IAEJ;AACAC,kBAAA,OAAO,CAAC,WAAW;AACjB,aAAO,OAAO;AACT,WAAA;AAAA,IAAA,CACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClFD,GAAG,WAAW,eAAe;"}