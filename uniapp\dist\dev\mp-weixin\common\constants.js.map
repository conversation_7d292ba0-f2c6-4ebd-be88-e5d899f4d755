{"version": 3, "file": "constants.js", "sources": ["../../../../src/common/constants.ts"], "sourcesContent": ["export const ACCESS_TOKEN = 'Access-Token'\r\nexport const USER_NAME = 'login_username'\r\nexport const USER_INFO = 'login_user_info'\r\nexport const NAV_BAR_COLOR = 'bg-gradual-blue'\r\nexport const APP_ROUTE = 'app_route_list'\r\nexport const APP_CONFIG = 'app_config'\r\nexport const X_TENANT_ID = 'X-Tenant-Id'\r\nexport const X_Low_App_ID = 'X-Low-App-ID'\r\nexport const TENANT_LIST = 'tenant_list'\r\nexport const ROUTE_PARAMS = \"cacheRouteParams\"\r\n// export const HOME_PAGE = \"/pages/message/message\"\r\nexport const HOME_PAGE = \"/pages/index/index\"\r\n//首页配置项缓存时间10分钟\r\nexport const HOME_CONFIG_EXPIRED_TIME = 10*60\r\nexport const phone = '---'\r\nexport const email = '---'\r\nexport const company = '---'\r\n\r\n\r\nconst STORAGE_OPTIONS = {\r\n  namespace: 'pro__', // key prefix\r\n    name: 'ls', // name variable Vue.[ls] or this.[$ls],\r\n    storage: 'local', // storage name session, local, memory\r\n}\r\n\r\nexport default STORAGE_OPTIONS;\r\n//类型条件\r\nexport const conditionObj = {\r\n \tinput:[{label:\"包含\",value:\"like\"},{label:\"以...开始\",value:\"right_like\"},{label:\"以...结尾\",value:\"left_like\"},{label:\"在...中\",value:\"in\"}],\r\n\tnumber:[{label:\"大于\",value:\"gt\"},{label:\"大于等于\",value:\"ge\"},{label:\"小于\",value:\"lt\"},{label:\"小于等于\",value:\"le\"}],\r\n\tdate:[{label:\"大于\",value:\"gt\"},{label:\"大于等于\",value:\"ge\"},{label:\"小于\",value:\"lt\"},{label:\"小于等于\",value:\"le\"}],\r\n\tselect:[],\r\n\tcheckbox:[{label:\"多词匹配\",value:\"elemMatch\"}],\r\n}\r\n/**\r\n * 颜色板\r\n * classic：经典\r\n * technology：科技\r\n * business：商务\r\n * botany：植物\r\n * natural：自然\r\n * colour：彩色\r\n */\r\nexport const colorPanel = {\r\n  classic:[\"#64b5f6\",\"#4db6ac\",\"#ffb74d\",\"#e57373\",\"#9575cd\",\"#a1887f\",\"#90a4ae\",\"#4dd0e1\",\"#81c784\",\"#ff8a65\"],\r\n  technology:[\"#3a5b84\",\"#4d6e98\",\"#7594b9\",\"#bfd7f2\",\"#18619f\",\"#408aca\",\"#5ea8e9\",\"#81c3fc\",\"#71a5cb\",\"#a1cae4\"],\r\n  business:[\"#ccedf7\",\"#b9dcf0\",\"#12a0e7\",\"#0663a4\",\"#458890\",\"#97d9cd\",\"#4bb8bf\",\"#20899c\",\"#f44336  \",\"#a2c7d9\"],\r\n  botany:[\"#34b392\",\"#4ac2a6\",\"#8ed1c0\",\"#ccdec6\",\"#61bdb5\",\"#7993a1\",\"#93a889\",\"#5e8d83\",\"#115040\",\"#bcc5b4\"],\r\n  natural:[\"#85cacd\",\"#a7d676\",\"#fee159\",\"#fbc78e\",\"#ef918b\",\"#a9b5ff\",\"#e7daca\",\"#fc803a\",\"#fea1ac\",\"#c2a3cd\"],\r\n  colour:[\"#fddb9c\",\"#f9ae91\",\"#f59193\",\"#d47f97\",\"#bd86a6\",\"#f595a1\",\"#624772\",\"#fe7156\",\"#ffbda3\",\"#877fa8\"]\r\n};\r\n//所有条件\r\nexport const allCondition = [\r\n  {label:\"包含\",value:\"like\"},\r\n\t{label:\"以...开始\",value:\"right_like\"},\r\n\t{label:\"以...结尾\",value:\"left_like\"},\r\n\t{label:\"在...中\",value:\"in\"},\r\n\t{label:\"大于\",value:\"gt\"},\r\n\t{label:\"大于等于\",value:\"ge\"},\r\n\t{label:\"小于\",value:\"lt\"},\r\n\t{label:\"小于等于\",value:\"le\"},\r\n\t{label:\"多词匹配\",value:\"elemMatch\"},\r\n\t{label:\"等于\",value:\"eq\"},\r\n\t{label:\"不等于\",value:\"ne\"},\r\n\t{label:\"为空\",value:\"empty\"},\r\n\t{label:\"不为空\",value:\"not_empty\"}\r\n]\r\n//仪表盘组件\r\nexport const compList = [\r\n \t\"JBar\",\r\n \t\"JStackBar\",\r\n \t\"JMultipleBar\",\r\n \t\"JNegativeBar\",\r\n\r\n \t\"JLine\",\r\n \t\"JMultipleLine\",\r\n \t\"DoubleLineBar\",\r\n\r\n \t\"JPie\",\r\n \t\"JRing\",\r\n\r\n \t\"JFunnel\",\r\n \t\"JPyramidFunnel\",\r\n\r\n \t\"JRadar\",\r\n \t\"JCircleRadar\",\r\n\r\n \t\"JGauge\",\r\n \t\"JColorGauge\",\r\n\r\n \t\"JScatter\",\r\n \t\"JBubble\",\r\n\r\n\t\"JDragEditor\",\r\n\t\"JCarousel\",\r\n\t\"JIframe\",\r\n\t\"JNumber\",\r\n\t\"JCustomButton\",\r\n\t\"JPivotTable\",\r\n\r\n\t\"JBubbleMap\",\r\n\t\"JBarMap\",\r\n\t\"JHeatMap\",\r\n ];\r\n //不包含操作的组件\r\nexport const noActionList = [\r\n  \"JCustomButton\",\r\n\t\"JIframe\",\r\n\t\"JCarousel\",\r\n\t\"JDragEditor\",\r\n];\r\n\r\n//系统字段\r\nexport const systemFields = [{\r\n\tdataIndex:\"create_time\",\r\n\tkey:\"create_time\",\r\n\ttitle:\"创建时间\",\r\n\ttype:'date',\r\n\twidth:200\r\n},{\r\n\tdataIndex:\"create_by\",\r\n\tkey:\"create_by\",\r\n\ttitle:\"创建人\",\r\n\twidth:150\r\n},{\r\n\tdataIndex:\"update_time\",\r\n\tkey:\"update_time\",\r\n\ttitle:\"修改时间\",\r\n\ttype:'date',\r\n\twidth:200\r\n},{\r\n\tdataIndex:\"update_by\",\r\n\tkey:\"update_by\",\r\n\ttitle:\"修改人\",\r\n\twidth:150\r\n}]\r\n"], "names": [], "mappings": ";AAAO,MAAM,eAAe;AAIrB,MAAM,YAAY;AAIlB,MAAM,cAAc;AAmCpB,MAAM,aAAa;AAAA,EAKxB,SAAQ,CAAC,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,SAAS;AAE9G;;;;;"}