{"version": 3, "file": "wd-resize.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-resize/wd-resize.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1yZXNpemUvd2QtcmVzaXplLnZ1ZQ"], "sourcesContent": ["<template>\n  <view :class=\"`wd-resize ${customClass}`\" :style=\"rootStyle\">\n    <!--插槽需要脱离父容器文档流，防止父容器固宽固高，进而导致插槽大小被被父容器限制-->\n    <view :id=\"resizeId\" :class=\"`wd-resize__container ${customContainerClass}`\">\n      <!--被监听的插槽-->\n      <slot />\n      <!--监听插槽变大-->\n      <scroll-view\n        class=\"wd-resize__wrapper\"\n        :scroll-y=\"true\"\n        :scroll-top=\"expandScrollTop\"\n        :scroll-x=\"true\"\n        :scroll-left=\"expandScrollLeft\"\n        @scroll=\"onScrollHandler\"\n      >\n        <view class=\"wd-resize__wrapper--placeholder\" style=\"height: 100000px; width: 100000px\"></view>\n      </scroll-view>\n      <!--监听插槽变小-->\n      <scroll-view\n        class=\"wd-resize__wrapper\"\n        :scroll-y=\"true\"\n        :scroll-top=\"shrinkScrollTop\"\n        :scroll-x=\"true\"\n        :scroll-left=\"shrinkScrollLeft\"\n        @scroll=\"onScrollHandler\"\n      >\n        <view class=\"wd-resize__wrapper--placeholder\" style=\"height: 250%; width: 250%\"></view>\n      </scroll-view>\n    </view>\n  </view>\n</template>\n\n<script lang=\"ts\">\nexport default {\n  name: 'wd-resize',\n  options: {\n    virtualHost: true,\n    addGlobalClass: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport { computed, getCurrentInstance, onMounted, ref } from 'vue'\nimport { addUnit, objToStyle, uuid } from '../common/util'\nimport { resizeProps } from './types'\n\nconst props = defineProps(resizeProps)\nconst emit = defineEmits(['resize'])\n\nconst expandScrollTop = ref<number>(0)\nconst shrinkScrollTop = ref<number>(0)\nconst expandScrollLeft = ref<number>(0)\nconst shrinkScrollLeft = ref<number>(0)\nconst height = ref<number>(0)\nconst width = ref<number>(0)\nconst scrollEventCount = ref<number>(0)\n\nconst rootStyle = computed(() => {\n  const style: Record<string, string | number> = {\n    width: addUnit(width.value),\n    height: addUnit(height.value)\n  }\n  return `${objToStyle(style)}${props.customStyle}`\n})\nlet onScrollHandler = () => {}\nconst { proxy } = getCurrentInstance() as any\n\nconst resizeId = ref<string>(`resize${uuid()}`)\n\nonMounted(() => {\n  // 初始化数据获取\n  const query = uni.createSelectorQuery().in(proxy).select(`#${resizeId.value}`).boundingClientRect()\n  query.exec(([res]) => {\n    // 闭包记录容器高度\n    let lastHeight = res.height\n    let lastWidth = res.width\n    // 立即填充父容器高宽\n    height.value = lastHeight\n    width.value = lastWidth\n    // 监听滚动事件\n    onScrollHandler = () => {\n      const query = uni.createSelectorQuery().in(proxy).select(`#${resizeId.value}`).boundingClientRect()\n      query.exec(([res]) => {\n        // 前两次滚动事件被触发，说明 created 的修改已渲染，通知用户代码当前容器大小\n        if (scrollEventCount.value++ === 0) {\n          const result: Record<string, string | number> = {}\n          ;['bottom', 'top', 'left', 'right', 'height', 'width'].forEach((propName) => {\n            result[propName] = res[propName]\n          })\n          emit('resize', result)\n        }\n        // 滚动条拉到底部会触发两次多余的事件，屏蔽掉。\n        if (scrollEventCount.value < 3) return\n        // 手动设置父容器高宽，防止父容器坍塌\n        // 滚动完，重新获取容器新的高度\n        const newHeight = res.height\n        const newWidth = res.width\n        // 立即填充父容器高宽\n        height.value = newHeight\n        width.value = newWidth\n        // 宽高都改变时，只需要触发一次 size 事件\n        const emitStack: number[] = []\n        if (newHeight !== lastHeight) {\n          lastHeight = newHeight\n          emitStack.push(1)\n        }\n        if (newWidth !== lastWidth) {\n          lastWidth = newWidth\n          emitStack.push(1)\n        }\n        if (emitStack.length !== 0) {\n          const result: Record<string, any> = {}\n          ;['bottom', 'top', 'left', 'right', 'height', 'width'].forEach((propName) => {\n            result[propName] = res[propName]\n          })\n          emit('resize', result)\n        }\n        // 滚动条拉到底部（如果使用 nextTick 效果更佳）\n        scrollToBottom({\n          lastWidth: lastWidth,\n          lastHeight: lastHeight\n        })\n      })\n    }\n    // 滚动条拉到底部（如果使用 nextTick 效果更佳）\n    scrollToBottom({\n      lastWidth: lastWidth,\n      lastHeight: lastHeight\n    })\n  })\n})\n\nfunction scrollToBottom({ lastWidth, lastHeight }: { lastWidth: number; lastHeight: number }) {\n  expandScrollTop.value = 100000 + lastHeight\n  shrinkScrollTop.value = 3 * height.value + lastHeight\n  expandScrollLeft.value = 100000 + lastWidth\n  shrinkScrollLeft.value = 3 * width.value + lastWidth\n}\n</script>\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-resize/wd-resize.vue'\nwx.createComponent(Component)"], "names": ["ref", "computed", "addUnit", "objToStyle", "getCurrentInstance", "uuid", "onMounted", "uni", "query", "res"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAiCA,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,EAAA;AAEpB;;;;;AAQA,UAAM,QAAQ;AACd,UAAM,OAAO;AAEP,UAAA,kBAAkBA,kBAAY,CAAC;AAC/B,UAAA,kBAAkBA,kBAAY,CAAC;AAC/B,UAAA,mBAAmBA,kBAAY,CAAC;AAChC,UAAA,mBAAmBA,kBAAY,CAAC;AAChC,UAAA,SAASA,kBAAY,CAAC;AACtB,UAAA,QAAQA,kBAAY,CAAC;AACrB,UAAA,mBAAmBA,kBAAY,CAAC;AAEhC,UAAA,YAAYC,cAAAA,SAAS,MAAM;AAC/B,YAAM,QAAyC;AAAA,QAC7C,OAAOC,cAAAA,QAAQ,MAAM,KAAK;AAAA,QAC1B,QAAQA,cAAAA,QAAQ,OAAO,KAAK;AAAA,MAC9B;AACA,aAAO,GAAGC,cAAAA,WAAW,KAAK,CAAC,GAAG,MAAM,WAAW;AAAA,IAAA,CAChD;AACD,QAAI,kBAAkB,MAAM;AAAA,IAAC;AACvB,UAAA,EAAE,MAAM,IAAIC,iCAAmB;AAErC,UAAM,WAAWJ,cAAAA,IAAY,SAASK,cAAA,KAAA,CAAM,EAAE;AAE9CC,kBAAAA,UAAU,MAAM;AAEd,YAAM,QAAQC,cAAA,MAAI,oBAAoB,EAAE,GAAG,KAAK,EAAE,OAAO,IAAI,SAAS,KAAK,EAAE,EAAE,mBAAmB;AAClG,YAAM,KAAK,CAAC,CAAC,GAAG,MAAM;AAEpB,YAAI,aAAa,IAAI;AACrB,YAAI,YAAY,IAAI;AAEpB,eAAO,QAAQ;AACf,cAAM,QAAQ;AAEd,0BAAkB,MAAM;AACtB,gBAAMC,SAAQD,cAAA,MAAI,oBAAoB,EAAE,GAAG,KAAK,EAAE,OAAO,IAAI,SAAS,KAAK,EAAE,EAAE,mBAAmB;AAClGC,iBAAM,KAAK,CAAC,CAACC,IAAG,MAAM;AAEhB,gBAAA,iBAAiB,YAAY,GAAG;AAClC,oBAAM,SAA0C,CAAC;AAC/C,eAAA,UAAU,OAAO,QAAQ,SAAS,UAAU,OAAO,EAAE,QAAQ,CAAC,aAAa;AACpE,uBAAA,QAAQ,IAAIA,KAAI,QAAQ;AAAA,cAAA,CAChC;AACD,mBAAK,UAAU,MAAM;AAAA,YAAA;AAGvB,gBAAI,iBAAiB,QAAQ;AAAG;AAGhC,kBAAM,YAAYA,KAAI;AACtB,kBAAM,WAAWA,KAAI;AAErB,mBAAO,QAAQ;AACf,kBAAM,QAAQ;AAEd,kBAAM,YAAsB,CAAC;AAC7B,gBAAI,cAAc,YAAY;AACf,2BAAA;AACb,wBAAU,KAAK,CAAC;AAAA,YAAA;AAElB,gBAAI,aAAa,WAAW;AACd,0BAAA;AACZ,wBAAU,KAAK,CAAC;AAAA,YAAA;AAEd,gBAAA,UAAU,WAAW,GAAG;AAC1B,oBAAM,SAA8B,CAAC;AACnC,eAAA,UAAU,OAAO,QAAQ,SAAS,UAAU,OAAO,EAAE,QAAQ,CAAC,aAAa;AACpE,uBAAA,QAAQ,IAAIA,KAAI,QAAQ;AAAA,cAAA,CAChC;AACD,mBAAK,UAAU,MAAM;AAAA,YAAA;AAGR,2BAAA;AAAA,cACb;AAAA,cACA;AAAA,YAAA,CACD;AAAA,UAAA,CACF;AAAA,QACH;AAEe,uBAAA;AAAA,UACb;AAAA,UACA;AAAA,QAAA,CACD;AAAA,MAAA,CACF;AAAA,IAAA,CACF;AAED,aAAS,eAAe,EAAE,WAAW,cAAyD;AAC5F,sBAAgB,QAAQ,MAAS;AACjB,sBAAA,QAAQ,IAAI,OAAO,QAAQ;AAC3C,uBAAiB,QAAQ,MAAS;AACjB,uBAAA,QAAQ,IAAI,MAAM,QAAQ;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;ACzI7C,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}