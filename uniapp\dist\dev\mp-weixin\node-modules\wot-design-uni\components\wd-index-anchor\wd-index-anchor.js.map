{"version": 3, "file": "wd-index-anchor.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-index-anchor/wd-index-anchor.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1pbmRleC1hbmNob3Ivd2QtaW5kZXgtYW5jaG9yLnZ1ZQ"], "sourcesContent": ["<template>\n\n\n\n    <view :class=\"`wd-index-anchor ${isSticky ? 'is-sticky' : ''} ${customClass}`\" :style=\"customStyle\" :id=\"indexAnchorId\">\n      <slot>\n        {{ index }}\n      </slot>\n    </view>\n\n\n\n</template>\n\n<script setup lang=\"ts\">\nimport { indexAnchorProps } from './type'\nimport { onMounted, getCurrentInstance, ref, computed } from 'vue'\nimport { indexBarInjectionKey } from '../wd-index-bar/type'\nimport { getRect, isDef, uuid } from '../common/util'\nimport { useParent } from '../composables/useParent'\n\nconst props = defineProps(indexAnchorProps)\n\nconst { parent: indexBar } = useParent(indexBarInjectionKey)\n\nconst indexAnchorId = ref<string>(`indexBar${uuid()}`)\n\nconst { proxy } = getCurrentInstance()!\n\nconst top = ref<number>(0)\n\nconst isSticky = computed(() => {\n  return indexBar && indexBar.props.sticky && indexBar.anchorState.activeIndex === props.index\n})\n\nfunction getInfo() {\n  getRect(`#${indexAnchorId.value}`, false, proxy).then((res) => {\n    if (isDef(indexBar)) {\n      top.value = Math.floor(res.top!)\n    }\n  })\n}\n\nonMounted(() => {\n  getInfo()\n})\n\ndefineExpose({\n  top\n})\n</script>\n\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-index-anchor/wd-index-anchor.vue'\nwx.createComponent(Component)"], "names": ["useParent", "indexBarInjectionKey", "ref", "uuid", "getCurrentInstance", "computed", "getRect", "isDef", "onMounted"], "mappings": ";;;;;;AAqBA,UAAM,QAAQ;AAEd,UAAM,EAAE,QAAQ,aAAaA,cAAAA,UAAUC,cAAAA,oBAAoB;AAE3D,UAAM,gBAAgBC,cAAAA,IAAY,WAAWC,cAAA,KAAA,CAAM,EAAE;AAE/C,UAAA,EAAE,MAAM,IAAIC,iCAAmB;AAE/B,UAAA,MAAMF,kBAAY,CAAC;AAEnB,UAAA,WAAWG,cAAAA,SAAS,MAAM;AAC9B,aAAO,YAAY,SAAS,MAAM,UAAU,SAAS,YAAY,gBAAgB,MAAM;AAAA,IAAA,CACxF;AAED,aAAS,UAAU;AACTC,oBAAAA,QAAA,IAAI,cAAc,KAAK,IAAI,OAAO,KAAK,EAAE,KAAK,CAAC,QAAQ;AACzD,YAAAC,cAAAA,MAAM,QAAQ,GAAG;AACnB,cAAI,QAAQ,KAAK,MAAM,IAAI,GAAI;AAAA,QAAA;AAAA,MACjC,CACD;AAAA,IAAA;AAGHC,kBAAAA,UAAU,MAAM;AACN,cAAA;AAAA,IAAA,CACT;AAEY,aAAA;AAAA,MACX;AAAA,IAAA,CACD;;;;;;;;;;;;AChDD,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}