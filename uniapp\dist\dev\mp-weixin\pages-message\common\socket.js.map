{"version": 3, "file": "socket.js", "sources": ["../../../../../src/pages-message/common/socket.ts"], "sourcesContent": ["// @ts-nocheck\r\nimport { randomString } from '@/common/uitls'\r\nimport { useUserStore } from '@/store/user'\r\n\r\nconst baseUrl = import.meta.env.VITE_SERVER_BASEURL\r\n\r\nclass socket {\r\n  constructor() {\r\n    this.socketUrl = baseUrl\r\n    this.socketStart = false\r\n    this.socketType = ''\r\n    this.monitorSocketError()\r\n    this.monitorSocketClose()\r\n    this.socketReceive()\r\n  }\r\n  init(socket_type, callback?) {\r\n\t\tconst userStore = useUserStore()\r\n    const _this = this\r\n    if (baseUrl) {\r\n      if (this.socketStart) {\r\n        console.log('webSocket已经启动了')\r\n      } else {\r\n        _this.socketType = socket_type\r\n        let url =\r\n          this.socketUrl.replace('https://', 'wss://').replace('http://', 'ws://') +\r\n          '/' +\r\n          socket_type +\r\n          '/' +\r\n          userStore.userInfo.userid +\r\n          '_app'\r\n        if (socket_type == 'eoaNewChatSocket') {\r\n          let randomMessageId = randomString(6)\r\n          url =\r\n            this.socketUrl.replace('https://', 'wss://').replace('http://', 'ws://') +\r\n            '/eoaNewChatSocket/' +\r\n            userStore.userInfo.userid +\r\n            '/' +\r\n            randomMessageId\r\n        }\r\n        console.log('启动this.socketUrl连接地址：', url)\r\n        // update-begin-author:taoyan date:20220422 for:v2.4.6 的 websocket 服务端，存在性能和安全问题。 #3278\r\n\r\n        let token = userStore.userInfo.token\r\n        uni.connectSocket({\r\n          url: url,\r\n          method: 'GET',\r\n          protocols: [token],\r\n        })\r\n        // update-end-author:taoyan date:20220422 for: v2.4.6 的 websocket 服务端，存在性能和安全问题。 #3278\r\n        uni.onSocketOpen((res) => {\r\n          this.socketStart = true\r\n          callback && callback()\r\n          console.log('WebSocket连接已打开！')\r\n        })\r\n        /* setTimeout(() => {\r\n\t\t\t\t   _this.getHeartbeat();\r\n\t\t\t\t}, 5000); */\r\n      }\r\n    } else {\r\n      console.log('config/baseUrl socketUrl为空')\r\n    }\r\n  }\r\n  // Socket给服务器发送消息\r\n  send(data, callback) {\r\n\t\tconst userStore = useUserStore()\r\n    const _this = this\r\n    if (userStore.userInfo.userid) {\r\n      data.userUid = userStore.userInfo.userid\r\n    }\r\n    console.log(data)\r\n    uni.sendSocketMessage({\r\n      data: JSON.stringify(data),\r\n      success: () => {\r\n        callback && callback(true)\r\n      },\r\n      fail: () => {\r\n        callback && callback(false)\r\n      },\r\n    })\r\n  }\r\n  // Socket接收服务器发送过来的消息\r\n  socketReceive() {\r\n    const _this = this\r\n    uni.onSocketMessage(function (res) {\r\n      console.log('APP:--》收到服务器内容：')\r\n      let data = JSON.parse(res.data)\r\n      // console.log('收到服务器内容：', data);\r\n      _this.acceptMessage && _this.acceptMessage(data)\r\n    })\r\n  }\r\n  // 关闭Socket\r\n  closeSocket() {\r\n    const _this = this\r\n    uni.closeSocket()\r\n    _this.socketStart = false\r\n  }\r\n  // 监听Socket关闭\r\n  monitorSocketClose() {\r\n    const _this = this\r\n    uni.onSocketClose(function (res) {\r\n      console.log('WebSocket 已关闭！')\r\n      _this.socketStart = false\r\n      setTimeout(function () {\r\n        _this.init(_this.socketType)\r\n      }, 3000)\r\n    })\r\n  }\r\n  // 监听Socket错误\r\n  monitorSocketError() {\r\n    const _this = this\r\n    uni.onSocketError(function (res) {\r\n      _this.socketStart = false\r\n      console.log('WebSocket连接打开失败，请检查！')\r\n    })\r\n  }\r\n  // 心跳\r\n  getHeartbeat() {\r\n\t\tconst userStore = useUserStore()\r\n    const _this = this\r\n    this.send(\r\n      {\r\n        type: '心跳',\r\n        userUid: userStore.userInfo.userid,\r\n      },\r\n      (val) => {\r\n        setTimeout(() => {\r\n          if (val) {\r\n            // _this.getHeartbeat();\r\n          } else {\r\n            if (!_this.socketStart) {\r\n              // _this.init();\r\n            }\r\n          }\r\n        }, 10000)\r\n      },\r\n    )\r\n  }\r\n}\r\nconst mySocket = new socket()\r\nexport default mySocket "], "names": ["useUserStore", "randomString", "uni"], "mappings": ";;;;AAIA,MAAM,UAAU;AAEhB,MAAM,OAAO;AAAA,EACX,cAAc;AACZ,SAAK,YAAY;AACjB,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,mBAAmB;AACxB,SAAK,mBAAmB;AACxB,SAAK,cAAc;AAAA,EAAA;AAAA,EAErB,KAAK,aAAa,UAAW;AAC7B,UAAM,YAAYA,WAAAA,aAAa;AAC7B,UAAM,QAAQ;AACD;AACX,UAAI,KAAK,aAAa;AACpB,gBAAQ,IAAI,gBAAgB;AAAA,MAAA,OACvB;AACL,cAAM,aAAa;AACnB,YAAI,MACF,KAAK,UAAU,QAAQ,YAAY,QAAQ,EAAE,QAAQ,WAAW,OAAO,IACvE,MACA,cACA,MACA,UAAU,SAAS,SACnB;AACF,YAAI,eAAe,oBAAoB;AACjC,cAAA,kBAAkBC,0BAAa,CAAC;AACpC,gBACE,KAAK,UAAU,QAAQ,YAAY,QAAQ,EAAE,QAAQ,WAAW,OAAO,IACvE,uBACA,UAAU,SAAS,SACnB,MACA;AAAA,QAAA;AAEI,gBAAA,IAAI,yBAAyB,GAAG;AAGpC,YAAA,QAAQ,UAAU,SAAS;AAC/BC,sBAAAA,MAAI,cAAc;AAAA,UAChB;AAAA,UACA,QAAQ;AAAA,UACR,WAAW,CAAC,KAAK;AAAA,QAAA,CAClB;AAEGA,4BAAA,aAAa,CAAC,QAAQ;AACxB,eAAK,cAAc;AACnB,sBAAY,SAAS;AACrB,kBAAQ,IAAI,iBAAiB;AAAA,QAAA,CAC9B;AAAA,MAAA;AAAA,IAIH;AAAA,EAGF;AAAA;AAAA,EAGF,KAAK,MAAM,UAAU;AACrB,UAAM,YAAYF,WAAAA,aAAa;AAEzB,QAAA,UAAU,SAAS,QAAQ;AACxB,WAAA,UAAU,UAAU,SAAS;AAAA,IAAA;AAEpC,YAAQ,IAAI,IAAI;AAChBE,kBAAAA,MAAI,kBAAkB;AAAA,MACpB,MAAM,KAAK,UAAU,IAAI;AAAA,MACzB,SAAS,MAAM;AACb,oBAAY,SAAS,IAAI;AAAA,MAC3B;AAAA,MACA,MAAM,MAAM;AACV,oBAAY,SAAS,KAAK;AAAA,MAAA;AAAA,IAC5B,CACD;AAAA,EAAA;AAAA;AAAA,EAGH,gBAAgB;AACd,UAAM,QAAQ;AACVA,wBAAA,gBAAgB,SAAU,KAAK;AACjC,cAAQ,IAAI,iBAAiB;AAC7B,UAAI,OAAO,KAAK,MAAM,IAAI,IAAI;AAExB,YAAA,iBAAiB,MAAM,cAAc,IAAI;AAAA,IAAA,CAChD;AAAA,EAAA;AAAA;AAAA,EAGH,cAAc;AACZ,UAAM,QAAQ;AACdA,kBAAAA,MAAI,YAAY;AAChB,UAAM,cAAc;AAAA,EAAA;AAAA;AAAA,EAGtB,qBAAqB;AACnB,UAAM,QAAQ;AACVA,wBAAA,cAAc,SAAU,KAAK;AAC/B,cAAQ,IAAI,gBAAgB;AAC5B,YAAM,cAAc;AACpB,iBAAW,WAAY;AACf,cAAA,KAAK,MAAM,UAAU;AAAA,SAC1B,GAAI;AAAA,IAAA,CACR;AAAA,EAAA;AAAA;AAAA,EAGH,qBAAqB;AACnB,UAAM,QAAQ;AACVA,wBAAA,cAAc,SAAU,KAAK;AAC/B,YAAM,cAAc;AACpB,cAAQ,IAAI,sBAAsB;AAAA,IAAA,CACnC;AAAA,EAAA;AAAA;AAAA,EAGH,eAAe;AACf,UAAM,YAAYF,WAAAA,aAAa;AAC7B,UAAM,QAAQ;AACT,SAAA;AAAA,MACH;AAAA,QACE,MAAM;AAAA,QACN,SAAS,UAAU,SAAS;AAAA,MAC9B;AAAA,MACA,CAAC,QAAQ;AACP,mBAAW,MAAM;AACf,cAAI;AAAK;AAAA,eAEF;AACD,gBAAA,CAAC,MAAM;AAAa;AAAA,UAExB;AAAA,WAED,GAAK;AAAA,MAAA;AAAA,IAEZ;AAAA,EAAA;AAEJ;AACM,MAAA,WAAW,IAAI,OAAO;;"}