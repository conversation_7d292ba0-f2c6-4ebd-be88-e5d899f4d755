<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationBarTitleText: '医患沟通',
        navigationStyle: 'custom'
      },
    }
</route>
<template>
  <PageLayout :navLeftArrow="true" navLeftText="返回">
    <!-- 医生端查询区域 - 固定在顶部 -->
    <view class="doctor-filter-fixed" v-if="Number(userStore.userInfo.userCategory) === 0">
      <view class="filter-section">
            <view class="date-filter">
              <text class="filter-label">时间：</text>
              <picker mode="date" :value="startDate" start="1900-01-01" end="2099-12-31" @change="onStartDateChange">
                <view class="date-picker">
                  <text>{{ startDate || '开始日期' }}</text>
                  <uni-icons type="calendar" size="15" color="#666666" />
                </view>
              </picker>
              <view class="date-separator">至</view>
              <picker mode="date" :value="endDate" start="1900-01-01" end="2099-12-31" @change="onEndDateChange">
                <view class="date-picker">
                  <text>{{ endDate || '结束日期' }}</text>
                  <uni-icons type="calendar" size="15" color="#666666" />
                </view>
              </picker>
              <view class="reset-btn" :class="{ 'refreshing': isRefreshing }" @click="resetDateFilter">
                <uni-icons type="reload" size="18" color="#07C160" />
              </view>
            </view>
          <!-- 患者姓名搜索 -->
          <view class="search-row">
            <text class="search-label">姓名：</text>
            <view class="search-input-container">
              <input
                type="text"
                v-model="searchParams.patientName"
                placeholder="请输入患者姓名"
                class="search-input name-input"
                :maxlength="20"
                @input="onPatientNameInput"
              />
              <text
                v-if="searchParams.patientName"
                class="clear-btn"
                @click="clearPatientName"
              >
                ✕
              </text>
            </view>
          </view>

          <!-- 问题关键字搜索 -->
          <view class="search-row">
            <text class="search-label">问题：</text>
            <view class="search-input-container">
              <input
                type="text"
                v-model="searchParams.question"
                placeholder="请输入问题关键字"
                class="search-input question-input"
                :maxlength="50"
                @input="onQuestionInput"
              />
              <text
                v-if="searchParams.question"
                class="clear-btn"
                @click="clearQuestion"
              >
                ✕
              </text>
            </view>
          </view>

          <!-- 回复状态筛选 -->
          <view class="search-row">
            <text class="search-label">状态：</text>
            <view class="status-radio-container">
              <view
                v-for="option in replyStatusOptions"
                :key="option.value"
                class="radio-option"
                @click="onReplyStatusChange(option.value)"
              >
                <view class="radio-circle" :class="{ 'radio-checked': option.checked }">
                  <view v-if="option.checked" class="radio-dot"></view>
                </view>
                <text class="radio-label">{{ option.label }}</text>
              </view>
            </view>
          </view>

          <!-- @我的问题筛选 -->
          <view class="search-row">
            <text class="search-label">筛选：</text>
            <view class="mention-filter-container">
              <view
                class="mention-filter-option"
                :class="{ 'mention-active': showOnlyMentioned }"
                @click="toggleMentionFilter"
              >
                <view class="mention-checkbox" :class="{ 'mention-checked': showOnlyMentioned }">
                  <uni-icons v-if="showOnlyMentioned" type="checkmarkempty" size="12" color="#FFFFFF"></uni-icons>
                </view>
                <text class="mention-label">只看@我的问题</text>
              </view>
            </view>
          </view>

          <view class="button-row">
            <view class="search-button" @click="handleSearch">
              <uni-icons type="search" size="16" color="#FFFFFF"></uni-icons>
              <text class="search-button-text">查询</text>
            </view>
            <view class="clear-all-button" @click="clearAllSearch">
              <uni-icons type="clear" size="16" color="#666666"></uni-icons>
              <text class="clear-all-button-text">清空</text>
            </view>
          </view>
        </view>
    </view>

    <!-- 医生端滚动内容区域 -->
    <scroll-view class="page-scroll-view doctor-scroll" scroll-y="true" v-if="Number(userStore.userInfo.userCategory) === 0">
        <!-- 医生端聊天列表 -->
        <!-- 加载状态 -->
        <view class="loading-container" v-if="loadingChat">
          <wd-loading />
        </view>

        <!-- 空状态 -->
        <view v-if="chatList.length === 0 && !loadingChat" class="empty-tip">
          <text class="empty-text">暂时没有问题记录</text>
          <text class="empty-hint">使用上方筛选条件查找患者问题</text>
        </view>

        <!-- 聊天记录列表 -->
        <view class="chat-list" v-if="chatList.length > 0">
          <view class="chat-item" v-for="(item, index) in chatList" :key="index" @click="goToChatDetail(item.id)">
            <image class="chat-avatar" :src="getAvatarUrl(getDisplayAvatar(item))" mode="aspectFill" @error="handleAvatarError"/>
            <view class="chat-content">
              <view class="chat-row">
                <view class="chat-nickname-container">
                  <text class="chat-nickname">{{ getDisplayName(item) }}</text>
                  <!-- 显示回复关系 -->
                  <text v-if="item.replyToName" class="reply-text">
                    回复 <text class="reply-target">{{ item.replyToName }}</text>：<text class="reply-content">{{ item.replyToContent || '原消息' }}</text>
                  </text>
                </view>
                <text class="chat-time">{{ item.createTime || '-' }}</text>
              </view>
              <!-- 问题标题 -->
              <view v-if="item.title" class="chat-title-row">
                <text class="chat-title">{{ item.title }}</text>
              </view>
              <!-- @医生信息 -->
              <view v-if="item.doctors && item.doctors.length > 0" class="mentioned-doctors-row">
                <view class="mentioned-doctors">
                  <text
                    v-for="(doctor, doctorIndex) in item.doctors"
                    :key="doctorIndex"
                    class="mentioned-doctor"
                  >
                    @{{ doctor.realname || doctor.username || '医生' }}
                  </text>
                </view>
              </view>
              <view class="chat-bottom-row">
                <text class="chat-msg">{{ item.question || '-' }}</text>
                <!-- 回复状态标志 - 移动到右下角 -->
                <view class="reply-status-badge" :class="getReplyStatusClass(item.status)">
                  <text class="status-text">{{ getReplyStatusText(item.status) }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
    </scroll-view>

    <!-- 患者界面的查询区域 - 固定在顶部 -->
    <view class="patient-filter-fixed" v-if="Number(userStore.userInfo.userCategory) === 1">
      <view class="filter-section">
        <view class="date-filter">
          <text class="filter-label">时间：</text>
          <picker mode="date" :value="startDate" start="1900-01-01" end="2099-12-31" @change="onStartDateChange">
            <view class="date-picker">
              <text>{{ startDate || '开始日期' }}</text>
              <uni-icons type="calendar" size="15" color="#666666" />
            </view>
          </picker>
          <view class="date-separator">至</view>
          <picker mode="date" :value="endDate" start="1900-01-01" end="2099-12-31" @change="onEndDateChange">
            <view class="date-picker">
              <text>{{ endDate || '结束日期' }}</text>
              <uni-icons type="calendar" size="15" color="#666666" />
            </view>
          </picker>
          <view class="reset-btn" :class="{ 'refreshing': isRefreshing }" @click="resetDateFilter">
            <uni-icons type="reload" size="18" color="#07C160" />
          </view>
        </view>

        <!-- 问题关键字搜索 -->
        <view class="search-row">
          <text class="search-label">问题：</text>
          <view class="search-input-container">
            <input
              type="text"
              v-model="searchParams.question"
              placeholder="请输入问题关键字"
              class="search-input question-input"
              :maxlength="50"
              @input="onQuestionInput"
            />
            <text
              v-if="searchParams.question"
              class="clear-btn"
              @click="clearQuestion"
            >
              ✕
            </text>
          </view>
        </view>

        <!-- 回复状态筛选 -->
        <view class="search-row">
          <text class="search-label">状态：</text>
          <view class="status-radio-container">
            <view
              v-for="option in replyStatusOptions"
              :key="option.value"
              class="radio-option"
              @click="onReplyStatusChange(option.value)"
            >
              <view class="radio-circle" :class="{ 'radio-checked': option.checked }">
                <view v-if="option.checked" class="radio-dot"></view>
              </view>
              <text class="radio-label">{{ option.label }}</text>
            </view>
          </view>
        </view>

        <view class="button-row">
          <view class="search-button" @click="handleSearch">
            <uni-icons type="search" size="16" color="#FFFFFF"></uni-icons>
            <text class="search-button-text">查询</text>
          </view>
          <view class="clear-all-button" @click="clearAllSearch">
            <uni-icons type="clear" size="16" color="#666666"></uni-icons>
            <text class="clear-all-button-text">清空</text>
          </view>
        </view>
      </view>
      <!-- 发起提问按钮放在查询逻辑下面 -->
      <button class="add-btn" @click="addChat" :disabled="isViewMode">发起提问</button>
    </view>

    <!-- 滚动内容区域 - 仅患者端使用 -->
    <scroll-view class="page-scroll-view" :class="{ 'patient-scroll': Number(userStore.userInfo.userCategory) === 1 }" scroll-y="true" @navLeftClick="goHome" v-if="Number(userStore.userInfo.userCategory) === 1">
        <!-- 加载状态 -->
        <view class="loading-container" v-if="loadingChat">
          <wd-loading />
        </view>

        <!-- 空状态 -->
        <view v-if="chatList.length === 0 && !loadingChat" class="empty-tip">
          <text class="empty-text">暂时没有问题记录</text>
          <text class="empty-hint">点击下方"发起提问"按钮开始咨询</text>
        </view>

        <!-- 聊天记录列表 -->
        <view class="chat-list" v-if="chatList.length > 0">
          <view class="chat-item" v-for="(item, index) in chatList" :key="index" @click="goToChatDetail(item.id)">
            <image class="chat-avatar" :src="getAvatarUrl(getDisplayAvatar(item))" mode="aspectFill" @error="handleAvatarError"/>
            <view class="chat-content">
              <view class="chat-row">
                <view class="chat-nickname-container">
                  <text class="chat-nickname">{{ getDisplayName(item) }}</text>
                  <!-- 显示回复关系 -->
                  <text v-if="item.replyToName" class="reply-text">
                    回复 <text class="reply-target">{{ item.replyToName }}</text>：<text class="reply-content">{{ item.replyToContent || '原消息' }}</text>
                  </text>
                </view>
                <text class="chat-time">{{ item.createTime || '-' }}</text>
              </view>
              <!-- 问题标题 -->
              <view v-if="item.title" class="chat-title-row">
                <text class="chat-title">{{ item.title }}</text>
              </view>
              <!-- @医生信息 -->
              <view v-if="item.doctors && item.doctors.length > 0" class="mentioned-doctors-row">
                <view class="mentioned-doctors">
                  <text
                    v-for="(doctor, doctorIndex) in item.doctors"
                    :key="doctorIndex"
                    class="mentioned-doctor"
                  >
                    @{{ doctor.realname || doctor.username || '医生' }}
                  </text>
                </view>
              </view>
              <view class="chat-bottom-row">
                <text class="chat-msg">{{ item.question || '-' }}</text>
                <!-- 回复状态标志 - 移动到右下角 -->
                <view class="reply-status-badge" :class="getReplyStatusClass(item.status)">
                  <text class="status-text">{{ getReplyStatusText(item.status) }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
    </scroll-view>
    <!-- 日期选择器弹窗 -->
    <uni-calendar ref="calendar" :insert="false" @confirm="dateConfirm" :range="false" />
  </PageLayout>
</template>
<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useUserStore } from '@/store/user';
import { http } from '@/utils/http';
import { onMounted } from 'vue';
import { onShow } from '@dcloudio/uni-app';

const userStore = useUserStore();
const chatList = ref([]); // 聊天记录列表
const loadingChat = ref(false); // 加载状态
const isRefreshing = ref(false); // 刷新状态


// 日期筛选
const startDate = ref('')
const endDate = ref('')
const datePickerType = ref('') // 用于标识当前打开的是开始日期还是结束日期选择器
const calendar = ref(null)


// 处理开始日期变化
function onStartDateChange(e: any) {
  startDate.value = e.detail.value
}

// 处理结束日期变化
function onEndDateChange(e: any) {
  endDate.value = e.detail.value
}

// 日期选择确认
const dateConfirm = (e: any) => {
  const selectedDate = e.fulldate
  if (datePickerType.value === 'start') {
    startDate.value = selectedDate
  } else if (datePickerType.value === 'end') {
    endDate.value = selectedDate
  }
}
const loading = ref(false) // 数据加载状态（保留以防其他地方使用）

// 刷新整个页面 - 完全重置页面状态并重新获取所有数据
const resetDateFilter = () => {
  // 防止重复刷新
  if (isRefreshing.value) {
    return;
  }

  // 设置刷新状态
  isRefreshing.value = true;

  // 显示刷新提示
  uni.showLoading({
    title: '刷新中...',
    mask: true
  });


  // 1. 完全重置所有搜索条件和页面状态
  startDate.value = '';
  endDate.value = '';
  searchParams.value.patientName = '';
  searchParams.value.question = '';
  searchParams.value.patientAvatar = '';
  searchParams.value.createTime = '';

  // 2. 清空当前列表数据
  chatList.value = [];

  // 3. 重置加载状态
  loadingChat.value = false;

  // 4. 重新获取完整的数据列表
  fetchChatList().then(() => {
    uni.hideLoading();
    uni.showToast({
      title: '页面刷新成功',
      icon: 'success',
      duration: 1500
    });

    // 延迟重置刷新状态，让动画效果更明显
    setTimeout(() => {
      isRefreshing.value = false;
    }, 500);
  }).catch((error) => {
    uni.hideLoading();
    console.error('❌ 页面刷新失败:', error);
    uni.showToast({
      title: '刷新失败，请重试',
      icon: 'none',
      duration: 2000
    });

    // 失败时也要重置刷新状态
    setTimeout(() => {
      isRefreshing.value = false;
    }, 500);
  });
}

// 检查是否有搜索条件
const hasSearchConditions = () => {
  const hasReplyStatusFilter = getSelectedReplyStatus() !== 'all';

  // 患者端不检查姓名条件
  if (Number(userStore.userInfo.userCategory) === 1) {
    return !!(
      (searchParams.value.question && searchParams.value.question.trim()) ||
      startDate.value ||
      endDate.value ||
      hasReplyStatusFilter
    );
  }

  // 医生端检查所有条件
  return !!(
    (searchParams.value.patientName && searchParams.value.patientName.trim()) ||
    (searchParams.value.question && searchParams.value.question.trim()) ||
    startDate.value ||
    endDate.value ||
    hasReplyStatusFilter ||
    showOnlyMentioned.value
  );
};

// 搜索处理函数
const handleSearch = () => {
  fetchChatList();
};

console.log('userCategory:', userStore.userInfo.userCategory)

// 默认头像
// 默认头像，使用SVG格式确保显示
const defAvatar = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNGNUY1RjUiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iIzk5OTk5OSIvPgo8cGF0aCBkPSJNMzAgMzJDMzAgMjYuNDc3MSAyNS41MjI5IDIyIDIwIDIyQzE0LjQ3NzEgMjIgMTAgMjYuNDc3MSAxMCAzMkgzMFoiIGZpbGw9IiM5OTk5OTkiLz4KPC9zdmc+'

// 缓存用户头像URL
const userAvatarCache = ref('')

// 获取文件URL的函数
const fetchFileUrl = async (objectName: string) => {
  try {
    const response: any = await http.get('/file/url', {
      objectName: objectName,
      token: userStore.userInfo.token
    })

    if (response?.success && response?.result?.fileUrl) {
      return response.result.fileUrl;
    } else {
      return '';
    }
  } catch (error) {
    return '';
  }
}

// 通用头像处理函数
const getAvatarUrl = (avatar: string | null | undefined) => {
  // 如果头像为空、null、undefined或空字符串，返回默认头像
  if (!avatar || avatar.trim() === '') {
    return defAvatar;
  }

  // 检查是否已经是完整URL (http或https开头)
  if (avatar.startsWith('http://') || avatar.startsWith('https://')) {
    return avatar;
  }

  // 检查是否是base64格式
  if (avatar.startsWith('data:image/')) {
    return avatar;
  }

  // 如果是相对路径，拼接基础URL
  if (avatar.startsWith('/')) {
    return 'https://www.mograine.cn' + avatar;
  }

  // 如果是文件名，先检查缓存
  if (userAvatarCache.value && avatar === userStore.userInfo.avatar) {
    return userAvatarCache.value;
  }

  // 如果是文件名，拼接完整路径作为备用
  return 'https://www.mograine.cn/images/' + avatar;
}

// 头像加载错误处理
const handleAvatarError = (event: any) => {
  event.target.src = defAvatar;
}

const searchParams = ref({
    patientName: '',     // 患者姓名
    question: '',        // 问题关键字
    patientAvatar: '',
    createTime: '',
})

// 回复状态筛选选项
const replyStatusOptions = ref([
  { value: 'all', label: '全部', checked: true },
  { value: 'pending', label: '待回复', checked: false },
  { value: 'replied', label: '已回复', checked: false }
])

// @我的问题筛选
const showOnlyMentioned = ref(false)



// 获取当前选中的回复状态
const getSelectedReplyStatus = () => {
  const selected = replyStatusOptions.value.find(option => option.checked)
  return selected ? selected.value : 'all'
}

// 处理回复状态选择
const onReplyStatusChange = (selectedValue: string) => {
  // 更新选中状态
  replyStatusOptions.value.forEach(option => {
    option.checked = option.value === selectedValue
  })
}

// 切换@我的问题筛选
const toggleMentionFilter = () => {
  showOnlyMentioned.value = !showOnlyMentioned.value
  console.log('🔍 切换@我的问题筛选:', showOnlyMentioned.value ? '开启' : '关闭')
}



//获取沟通记录 - 统一查询函数
const fetchChatList = async () => {
  return new Promise((resolve, reject) => {
    try {
      loadingChat.value = true; // 显示加载状态

      // 构建查询参数 - 根据后端接口文档要求
      const params: any = {};

      // 必需参数：用户ID
      if (userStore.userInfo.userid) {
        params.user_id = userStore.userInfo.userid;
      }

      // 必需参数：用户类别 (0=医生, 1=患者)
      if (userStore.userInfo.userCategory !== undefined && userStore.userInfo.userCategory !== '') {
        params.category = Number(userStore.userInfo.userCategory);
      }

      // 可选参数：患者姓名（仅医生端）
      if (Number(userStore.userInfo.userCategory) === 0 && searchParams.value.patientName && searchParams.value.patientName.trim()) {
        params.patientName = searchParams.value.patientName.trim();
      }

      // 可选参数：问题关键字
      if (searchParams.value.question && searchParams.value.question.trim()) {
        params.question = searchParams.value.question.trim();
      }

      // 可选参数：时间范围
      if (startDate.value) {
        params.startDate = startDate.value;
      }
      if (endDate.value) {
        params.endDate = endDate.value;
      }


      if (!userStore.userInfo.token) {
        console.error('❌ Token未设置，可能需要重新登录');
        uni.showToast({
          title: '登录状态已过期，请重新登录',
          icon: 'none',
          duration: 3000
        });
        // 跳转到登录页面
        uni.reLaunch({
          url: '/pages/login/login'
        });
        reject(new Error('Token未设置'));
        return;
      }
      // 使用最终的参数组合
      http.get('/communication/list', params).then((res:any) => {

        if(res.success && res.result){
          console.log('API返回的获取沟通记录详情:', res.result);
          // 处理回复关系数据
          const processedData = res.result.map((item: any) => {
            let replyToContent = null;

            // 如果有回复关系（点击了某个评论进行回复，出现了@符号）
            if (item.replyToUserId) {
              console.log('聊天列表检测到回复关系，replyToUserId:', item.replyToUserId);

              // 有 replyToUserId 就说明是通过点击评论触发的回复，需要显示回复关系
              if (item.replyToContent && typeof item.replyToContent === 'string' && item.replyToContent.trim()) {
                // 截断过长的内容，最多显示15个字符
                replyToContent = item.replyToContent.length > 15
                  ? item.replyToContent.substring(0, 15) + '...'
                  : item.replyToContent;
              } else {
                // 如果没有具体的回复内容，显示默认文本
                replyToContent = '原评论';
              }
            }

            return {
              ...item,
              // 有 replyToUserId 就显示回复关系
              replyToName: item.replyToUserId ? (item.replyToUserName || '某用户') : null,
              replyToContent: replyToContent
            };
          });

          // 前端综合过滤逻辑
          let filteredData = processedData || [];
          const originalCount = filteredData.length;

          // 患者端过滤：只显示自己的问题
          if (Number(userStore.userInfo.userCategory) === 1) {
            const currentUserId = userStore.userInfo.userid;

            filteredData = filteredData.filter((item: any) => {
              // 尝试多个可能的字段来匹配当前用户
              const match = item.userId === currentUserId ||
                           item.patientId === currentUserId ||
                           item.user_id === currentUserId ||
                           item.patient_id === currentUserId;
              return match;
            });
          }

          // 医生端：应用患者姓名过滤
          if (Number(userStore.userInfo.userCategory) === 0 && searchParams.value.patientName && searchParams.value.patientName.trim()) {
            const nameKeyword = searchParams.value.patientName.trim().toLowerCase();
            filteredData = filteredData.filter((item: any) => {
              const patientName = (item.patientName || '').toLowerCase();
              return patientName.includes(nameKeyword);
            });
          }

          // 应用问题关键字过滤
          if (searchParams.value.question && searchParams.value.question.trim()) {
            const questionKeyword = searchParams.value.question.trim().toLowerCase();
            filteredData = filteredData.filter((item: any) => {
              const question = (item.question || '').toLowerCase();
              return question.includes(questionKeyword);
            });
          }

          // 应用时间范围过滤
          if (startDate.value || endDate.value) {
            filteredData = filteredData.filter((item: any) => {
              const itemDate = item.createTime || item.updateTime;
              if (!itemDate) return false; // 没有时间信息的数据不显示

              const itemDateStr = itemDate.substring(0, 10); // 提取日期部分 YYYY-MM-DD

              // 检查是否在开始时间之后
              if (startDate.value && itemDateStr < startDate.value) {
                return false;
              }

              // 检查是否在结束时间之前
              if (endDate.value && itemDateStr > endDate.value) {
                return false;
              }

              return true;
            });

          }

          // 应用回复状态过滤
          const selectedStatus = getSelectedReplyStatus();
          if (selectedStatus !== 'all') {
            filteredData = filteredData.filter((item: any) => {
              if (selectedStatus === 'pending') {
                return item.status === 0; // 待回复
              } else if (selectedStatus === 'replied') {
                return item.status === 1; // 已回复
              }
              return true;
            })
          }

          // 应用@我的问题筛选（仅医生端）
          if (Number(userStore.userInfo.userCategory) === 0 && showOnlyMentioned.value) {
            const currentUserId = userStore.userInfo.userid;
            filteredData = filteredData.filter((item: any) => {
              // 检查doctors数组中是否包含当前医生
              if (item.doctors && Array.isArray(item.doctors)) {
                return item.doctors.some((doctor: any) =>
                  doctor.id === currentUserId ||
                  doctor.userId === currentUserId ||
                  doctor.user_id === currentUserId
                );
              }
              return false;
            });
            console.log('🔍 @我的问题筛选结果:', {
              当前医生ID: currentUserId,
              筛选后数量: filteredData.length,
              筛选前数量: processedData?.length || 0
            });
          }

          chatList.value = filteredData;
          // 显示查询结果提示
          if (hasSearchConditions()) {
            uni.showToast({
              title: `找到 ${chatList.value.length} 条记录`,
              icon: 'none',
              duration: 1500
            });
          }

          resolve(res); // 成功时 resolve
        } else {
          // 更详细的错误处理
          const errorMessage = res.message || '获取沟通记录失败';
          console.error('❌ API返回错误:', {
            success: res.success,
            message: res.message,
            code: res.code,
            result: res.result
          });

          uni.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 3000
          });

          // 如果是500错误，可能是服务器问题，设置空数据而不是完全失败
          if (res.code === 500) {
            console.log('🔄 服务器错误，设置空数据列表');
            chatList.value = [];
            resolve(res); // 仍然resolve，避免阻塞后续操作
          } else {
            reject(new Error(errorMessage));
          }
        }
      }).catch((error: any) => {
        console.error('❌ 网络请求失败:', error);

        // 网络错误处理
        let errorMessage = '网络连接失败，请检查网络设置';
        if (error.message) {
          errorMessage = error.message;
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        });

        // 设置空数据，避免页面崩溃
        chatList.value = [];
        reject(error);
      });
    } catch (error) {
      console.error('❌ fetchChatList 异常:', error);

      uni.showToast({
        title: '系统异常，请稍后重试',
        icon: 'none',
        duration: 3000
      });

      // 设置空数据，避免页面崩溃
      chatList.value = [];
      reject(error);
    } finally {
      loadingChat.value = false; // 隐藏加载状态
    }
  });
}







onMounted(async () => {

  // 如果是患者端且用户头像是文件名，获取真实URL
  if (Number(userStore.userInfo.userCategory) === 1 && userStore.userInfo.avatar && !userStore.userInfo.avatar.startsWith('http')) {
    const realUrl = await fetchFileUrl(userStore.userInfo.avatar);
    if (realUrl) {
      userAvatarCache.value = realUrl;
    }
  }

  fetchChatList();


});

// 页面显示时重新加载数据
onShow(() => {
  fetchChatList();
});



function goHome() {
  uni.switchTab({
    url: '/pages/index/index' 
  });
}
function goToChatDetail(id: any) {
  console.log('跳转到聊天详情，ID:', id);
  uni.navigateTo({
    url: `/pages-data/chat/chatDetail?communication_id=${id}`
  })
}

// 获取显示的头像
function getDisplayAvatar(item: any) {
  // 患者端显示自己的头像，医生端显示患者头像
  if (Number(userStore.userInfo.userCategory) === 1) {
    const userAvatar = userStore.userInfo.avatar;
    const patientAvatar = item.patientAvatar;

    // 优先使用用户自己的头像
    if (userAvatar) {
      return userAvatar;
    }

    // 如果用户头像为空，使用患者头像
    if (patientAvatar) {
      return patientAvatar;
    }

    // 都没有则返回空，让 getAvatarUrl 处理默认头像
    return '';
  } else {
    return item.patientAvatar || '';
  }
}

// 获取显示的姓名
function getDisplayName(item: any) {
  // 患者端显示用户的真实姓名，医生端显示患者姓名
  if (Number(userStore.userInfo.userCategory) === 1) {
    return userStore.userInfo.realname || userStore.userInfo.username || '我的提问';
  } else {
    return item.patientName || '-';
  }
}



// 添加聊天功能
function addChat() {
  console.log('发起提问');

  // 直接跳转到发起提问页面
  uni.navigateTo({
    url: '/pages-data/chat/addChat'
  });
}

// 检查是否为查看模式
const isViewMode = ref(false);

// 处理患者姓名输入
const onPatientNameInput = (e: any) => {
  const value = e.detail.value;
  // 限制字符长度，超过20个字符时截断
  if (value.length > 20) {
    searchParams.value.patientName = value.substring(0, 20);
    uni.showToast({
      title: '患者姓名最多输入20个字符',
      icon: 'none',
      duration: 1500
    });
  } else {
    searchParams.value.patientName = value;
  }
};

// 处理问题关键字输入
const onQuestionInput = (e: any) => {
  const value = e.detail.value;
  // 限制字符长度，超过50个字符时截断
  if (value.length > 50) {
    searchParams.value.question = value.substring(0, 50);
    uni.showToast({
      title: '问题关键字最多输入50个字符',
      icon: 'none',
      duration: 1500
    });
  } else {
    searchParams.value.question = value;
  }
};

// 清除患者姓名
const clearPatientName = () => {
  searchParams.value.patientName = '';
};

// 清除问题关键字
const clearQuestion = () => {
  searchParams.value.question = '';
};

// 清空所有搜索条件
const clearAllSearch = () => {
  startDate.value = '';
  endDate.value = '';
  // 只有医生端才清空患者姓名
  if (Number(userStore.userInfo.userCategory) === 0) {
    searchParams.value.patientName = '';
  }
  searchParams.value.question = '';
  // 重置回复状态为"全部"
  replyStatusOptions.value.forEach(option => {
    option.checked = option.value === 'all'
  });
  // 重置@我的问题筛选
  showOnlyMentioned.value = false;
  uni.showToast({
    title: '已清空搜索条件',
    icon: 'none',
    duration: 1000
  });
  // 清空后自动查询所有数据
  fetchChatList();
};



// 新增计算属性来截断显示文本
const truncatedPatientName = computed(() => {
  return searchParams.value.patientName.length > 5
    ? searchParams.value.patientName.slice(0, 5) + '...'
    : searchParams.value.patientName;
});



// 获取回复状态对应的CSS类名
const getReplyStatusClass = (status: number) => {
  switch (status) {
    case 0:
      return 'status-pending'; // 待回复 - 红色
    case 1:
      return 'status-replied'; // 已回复 - 绿色
    default:
      return 'status-pending'; // 默认为待回复
  }
};

// 获取回复状态对应的文本
const getReplyStatusText = (status: number) => {
  switch (status) {
    case 0:
      return '待回复';
    case 1:
      return '已回复';
    default:
      return '待回复';
  }
};
</script>

<style lang="scss" scoped>
// 患者端固定查询区域样式
.patient-filter-fixed {
  position: fixed;
  top: 88px;
  left: 0;
  right: 0;
  background-color: #fff;
  z-index: 999;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 10px; // 减少内边距
  box-sizing: border-box;
  // 移除 max-height 和 overflow-y，让内容自动撑开高度
}

// 医生端固定查询区域样式
.doctor-filter-fixed {
  position: fixed;
  top: 88px;
  left: 0;
  right: 0;
  background-color: #fff;
  z-index: 999;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 10px;
  box-sizing: border-box;
  // 移除 max-height 和 overflow-y，让内容自动撑开高度
}

.page-scroll-view {
  height: calc(100vh - 44px);
  width: 100%;
  padding-bottom: 120rpx; // 增加底部内边距，确保内容不被遮挡
  box-sizing: border-box;
}

// 患者端的scroll-view需要顶部内边距，避免被固定查询区域遮挡
.page-scroll-view.patient-scroll {
  padding-top: 220px; // 减少顶部内边距，最小化灰色空白区域
}

// 医生端的scroll-view需要顶部内边距，避免被固定查询区域遮挡
.page-scroll-view.doctor-scroll {
  padding-top: 240px; // 减少顶部内边距，最小化空白区域
  padding-left: 10rpx;
  padding-right: 10rpx;
}

.search-container {
    background: #FFFFFF;
    padding: 12rpx; // 减少内边距
    border-radius: 12rpx;
    margin-bottom: 12rpx; // 减少底部间距
}

.filter-section {
    background-color: #FFFFFF;
    border-radius: 16rpx;
    padding: 12rpx; // 进一步减少内边距
    margin-bottom: 8rpx; // 进一步减少底部间距
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    }



    .date-filter {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;
      gap: 16rpx; // 增加间距，让布局更均匀
      width: 100%; // 确保铺满整行
      }

      .filter-label {
        color: #333333;
        font-size: 28rpx;
        font-weight: 500;
        flex-shrink: 0;
      }

      .date-picker {
        flex: 1;
        min-width: 180rpx;
        // 移除最大宽度限制，让日期选择器能够更好地利用空间
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #F7F7F7;
        padding: 12rpx 16rpx;
        border-radius: 8rpx;

        text {
          color: #666666;
          font-size: 24rpx;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .date-separator {
        color: #666666;
        font-size: 24rpx;
        flex-shrink: 0;
      }


    .reset-btn {
        width: 35rpx;
        height: 35rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: transparent;
        border: none;
        transition: all 0.3s ease;
        cursor: pointer;
        flex-shrink: 0;

        &:active {
          transform: scale(0.85) rotate(360deg);
          opacity: 0.7;
        }

        &:hover {
          transform: scale(1.1);
          opacity: 0.8;
        }

        // 刷新动画效果
        &.refreshing {
          animation: refresh-spin 1s linear infinite;
        }
      }

      // 刷新旋转动画
      @keyframes refresh-spin {
        from {
          transform: rotate(0deg);
        }
        to {
          transform: rotate(360deg);
        }
      }
    
.search-row {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 15rpx;
  margin-bottom: 30rpx; /* 添加底部间距，分隔不同的输入框 */
}

.search-row:last-of-type {
  margin-bottom: 0; /* 最后一个输入框不需要底部间距 */
}

.search-label {
  color: #333333;
  font-size: 28rpx;
  font-weight: 500;
  white-space: nowrap;
  flex-shrink: 0;
}

.search-input-container {
  display: flex;
  align-items: center;
  position: relative;
  flex: 1;
}

.search-input {
    height: 80rpx;
    background: #F5F5F5;
    border-radius: 8rpx;
    padding: 0 60rpx 0 20rpx;
    font-size: 28rpx;
    border: none;
    outline: none;
    box-sizing: border-box;
    flex: 1;
    overflow: hidden;
    text-overflow: clip;
    white-space: nowrap;
}

/* 姓名输入框和问题输入框 - 保持相同的宽度 */
.search-input.name-input,
.search-input.question-input {
    flex: 1;
    width: 100%;
}

.clear-btn {
    position: absolute;
    right: 15rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 40rpx;
    height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    color: #666666;
    font-size: 24rpx;
    font-weight: bold;
    z-index: 10;
    cursor: pointer;
    transition: all 0.2s ease;

    &:active {
      background: rgba(0, 0, 0, 0.2);
      transform: translateY(-50%) scale(0.9);
    }
}

.button-row {
  display: flex;
  gap: 20rpx;
  margin-top: 20rpx;
}

.search-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #07C160;
  height: 80rpx;
  border-radius: 8rpx;
  cursor: pointer;
  padding: 0 30rpx;
  flex: 1;
  vertical-align: middle;
}

.search-button-text {
  color: #FFFFFF;
  font-size: 28rpx;
  margin-left: 10rpx;
}

.clear-all-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #F5F5F5;
  height: 80rpx;
  border-radius: 8rpx;
  cursor: pointer;
  padding: 0 30rpx;
  flex: 1;
  border: 1px solid #E0E0E0;
}

.clear-all-button-text {
  color: #666666;
  font-size: 28rpx;
  margin-left: 10rpx;
}

// 回复状态单选按钮样式
.status-radio-container {
  display: flex;
  flex: 1;
  gap: 30rpx;
  align-items: center;
}

.radio-option {
  display: flex;
  align-items: center;
  cursor: pointer;
  gap: 12rpx;
}

.radio-circle {
  width: 36rpx;
  height: 36rpx;
  border: 3rpx solid #D0D0D0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.radio-checked {
  border-color: #07C160;
}

.radio-dot {
  width: 20rpx;
  height: 20rpx;
  background-color: #07C160;
  border-radius: 50%;
}

// @我的问题筛选样式
.mention-filter-container {
  display: flex;
  align-items: center;
}

.mention-filter-option {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.mention-filter-option:hover {
  opacity: 0.8;
}

.mention-checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 3rpx solid #D0D0D0;
  border-radius: 8rpx;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.mention-checkbox.mention-checked {
  background-color: #07C160;
  border-color: #07C160;
}

.mention-label {
  font-size: 28rpx;
  color: #333;
  user-select: none;
}

.mention-filter-option.mention-active .mention-label {
  color: #07C160;
  font-weight: 500;
}



.radio-label {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}
.add-btn {
background-color: #07C160;
color: #FFFFFF;
font-size: 28rpx;
padding: 15rpx 25rpx;
border-radius: 8rpx;
margin: 20rpx;

&:disabled {
    background-color: #cccccc;
}
}
.scrollView {
    padding: 0 10rpx;
    padding-top: 0; // 移除顶部内边距，减少空白
}

.empty-tip {
  color: #999;
  text-align: center;
  margin: 40rpx 20rpx; // 增加上下间距，确保文字不被遮挡
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx; // 增加元素间距
  padding: 30rpx 15rpx; // 增加上下内边距
  min-height: 200rpx; // 确保有足够的高度显示内容
}

.empty-text {
  font-size: 36rpx; // 进一步增大字体
  color: #333; // 使用更深的颜色，提高可读性
  font-weight: 500;
  line-height: 1.5; // 增加行高
  word-wrap: break-word; // 确保长文本能够换行
  white-space: normal; // 允许文字换行
  max-width: 100%; // 确保不会超出容器
}

.empty-hint {
  font-size: 30rpx; // 增大提示文字
  color: #666; // 使用更深的颜色，提高可读性
  line-height: 1.5; // 增加行高
  word-wrap: break-word; // 确保长文本能够换行
  white-space: normal; // 允许文字换行
  max-width: 100%; // 确保不会超出容器
}


// 移除 patient-data 样式，不再需要

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30rpx 20rpx; // 减少内边距
  min-height: 100rpx; // 减少最小高度
}

.chat-list {
  padding: 0 20rpx; // 移除顶部内边距
  padding-bottom: 100rpx; // 增加底部间距，确保最后一个项目不被遮挡
  margin-top: 70px;
}

// 为最后一个聊天项添加额外的底部间距
.chat-item:last-child {
  margin-bottom: 120rpx; // 增加最后一个项目的底部空间，确保不被遮挡
}
.chat-item {
  min-height: 160rpx; // 最小高度，允许内容撑开
  height: auto; // 自动高度，根据内容调整
  border-radius: 16rpx; // 增加圆角
  margin: 8rpx 16rpx; // 减少外边距，特别是上下间距
  display: flex;
  align-items: flex-start; // 改为顶部对齐，适应多行内容
  padding: 20rpx 24rpx; // 减少内边距
  background: #ffffff;
  border-bottom: 1px solid #f0f0f0;
  transition: background 0.2s;
  position: relative; // 添加相对定位，为状态标志提供定位基准
  box-sizing: border-box; // 确保padding不会影响总宽度
}
.chat-item:active {
  background: #e6e6e6;
}
.chat-avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  margin-right: 32rpx; // 增加与内容的间距
  margin-top: 0; // 移除顶部间距，与名字顶部对齐
  object-fit: cover;
  background: #eee;
  flex-shrink: 0; // 防止头像被压缩
  align-self: flex-start; // 确保头像始终在顶部对齐
}
.chat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start; // 改为顶部对齐，不再居中
  min-width: 0;
  align-self: flex-start; // 确保内容区域从顶部开始
}
.chat-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start; // 保持顶部对齐
  width: 100%;
  margin-bottom: 12rpx; // 增加底部间距
  margin-top: 0; // 确保没有顶部间距
}

// 标题行布局
.chat-title-row {
  width: 100%;
  margin: 12rpx 0; // 增加上下间距
}

.chat-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
  line-height: 1.3;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 底部行布局 - 只包含问题内容
.chat-bottom-row {
  display: flex;
  width: 100%;
  padding-right: 140rpx; // 增加为右下角状态标志预留的空间
  margin-top: 8rpx; // 增加与标题的间距
}

.chat-nickname-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-left: 0; // 移除左边距，因为头像已经有足够间距
  min-width: 0;
  align-self: flex-start; // 确保名字容器从顶部开始
}

.chat-nickname {
  font-size: 32rpx;
  color: #222;
  font-weight: 500;
  line-height: 1.2;
  margin-top: 0; // 确保没有顶部外边距
  padding-top: 0; // 确保没有顶部内边距
}

.reply-text {
  font-size: 24rpx;
  color: #666;
  margin-top: 4rpx;
  line-height: 1.3;
  display: flex;
  flex-wrap: wrap;
  align-items: baseline;
}

.reply-target {
  color: #07C160;
  font-weight: 500;
}

.reply-content {
  color: #999;
  font-style: italic;
  margin-left: 4rpx;
  word-break: break-all;
  flex: 1;
}

.chat-time {
  font-size: 24rpx;
  color: #999;
  margin-left: 16rpx;
  flex-shrink: 0;
  line-height: 1.2;
}

// 回复状态标志 - 固定在右下角
.reply-status-badge {
  position: absolute; // 使用绝对定位
  bottom: 24rpx; // 增加距离底部的距离，适应新的内边距
  right: 32rpx; // 增加距离右边的距离，适应新的内边距
  padding: 8rpx 18rpx; // 增加内边距，让标志更大一些
  border-radius: 18rpx; // 增加圆角
  font-size: 22rpx;
  font-weight: 500;
  text-align: center;
  min-width: 80rpx;
  box-sizing: border-box;
  z-index: 1; // 确保在其他元素之上
}

// 待回复状态 - 红色
.status-pending {
  background-color: #ffebee;
  border: 1rpx solid #ffcdd2;

  .status-text {
    color: #d32f2f;
  }
}

// 已回复状态 - 绿色
.status-replied {
  background-color: #e8f5e8;
  border: 1rpx solid #c8e6c9;

  .status-text {
    color: #2e7d32;
  }
}



.status-text {
  font-size: 22rpx;
  font-weight: 500;
  line-height: 1.2;
}
.chat-msg {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4; // 增加行高，提高可读性
  flex: 1; // 占据剩余空间
  word-wrap: break-word; // 允许长单词换行
  word-break: break-all; // 在必要时断开单词
  // 移除单行限制，允许多行显示
  // overflow: hidden;
  // text-overflow: ellipsis;
  // white-space: nowrap;
}

// @医生信息样式 - 去掉背景色，保持简洁
.mentioned-doctors-row {
  display: flex;
  align-items: center;
  margin: 8rpx 0;
  padding: 8rpx 12rpx;
}

.mentioned-doctors {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  flex: 1;
}

.mentioned-doctor {
  font-size: 24rpx;
  color: #07C160;
  font-weight: 500;
  background-color: rgba(7, 193, 96, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  margin-right: 4rpx;
  margin-bottom: 2rpx;
}


</style>