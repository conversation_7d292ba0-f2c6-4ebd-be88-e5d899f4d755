{"version": 3, "file": "index.js", "sources": ["../../../../../../../src/pages-work/components/echarts/JRadar/index.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvcGFnZXMtd29yay9jb21wb25lbnRzL2VjaGFydHMvSlJhZGFyL2luZGV4LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"content\">\r\n    <statusTip v-if=\"pageTips.show\" :status=\"pageTips.status\"></statusTip>\r\n    <echartsUniapp v-else :option=\"option\"></echartsUniapp>\r\n  </view>\r\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { echartProps } from '@/pages-work/components/echarts/props'\r\nimport {deepMerge, handleTotalAndUnit, disposeGridLayout, getCustomColor} from '../../common/echartUtil'\r\nimport { isNumber } from '@/utils/is'\r\nimport useChartHook from '@/pages-work/components/hooks/useEchart'\r\nimport { deepClone } from '@/uni_modules/da-tree/utils'\r\nimport echartsUniapp from '@/pages-work/components/echarts/index.vue'\r\nimport statusTip from '@/pages-work/components/statusTip.vue'\r\nimport {merge} from \"lodash-es\";\r\n//组件传参\r\nconst props = defineProps({\r\n  ...echartProps,\r\n})\r\n\r\n//最终图表配置项\r\nconst option = ref({})\r\nlet chartOption = {\r\n  legend: {\r\n    t: 0,\r\n    r: 0,\r\n    bottom:0,right:0,\r\n    data: ['文综', '理综'] as any,\r\n  },\r\n  radar: [\r\n    {\r\n      indicator: [],\r\n    },\r\n  ],\r\n  series: [\r\n    {\r\n      type: 'radar',\r\n      data: [\r\n        {\r\n          value: [],\r\n          name: '',\r\n        },\r\n      ],\r\n    },\r\n  ],\r\n}\r\n//图表数据查询\r\nlet [{ dataSource, reload, pageTips, config }, { queryData }] = useChartHook(props, initOption)\r\n\r\n//初始化图表配置项\r\nfunction initOption(data) {\r\n  let chartData: any = dataSource.value\r\n  if (typeof chartData === 'string') {\r\n    chartData = JSON.parse(chartData)\r\n  }\r\n  if (chartData && chartData.length > 0) {\r\n      //显示坐标轴前几项\r\n      if(config.dataFilterNum && isNumber(config.dataFilterNum)){\r\n          chartData = chartData.slice(0,config.dataFilterNum)\r\n      }\r\n      //最大值\r\n      const maxValue =  Math.max(...chartData.map(({value})=> value));\r\n      //图例类型\r\n      let typeArr = Array.from(new Set(chartData.map((item) => item.type)));\r\n      //雷达数据\r\n      let indicator = Array.from(\r\n          new Set(\r\n              chartData.map((item) => {\r\n                  let { name, max } = item;\r\n                  return { name, max: max ? max : maxValue };\r\n              })\r\n          )\r\n      );\r\n      let data = [];\r\n      const colors = getCustomColor(config.option.customColor);\r\n      //设置配色\r\n      typeArr.forEach((type,index) => {\r\n          let obj = { name: type,itemStyle:{color:colors[index].color||null} };\r\n          let chartArr = chartData.filter((item) => type === item.type);\r\n          obj['value'] = chartArr.map((item) => item.value);\r\n          //data数据\r\n          data.push(obj);\r\n      });\r\n      chartOption.radar[0].indicator = indicator;\r\n      chartOption.legend.data = typeArr;\r\n      chartOption.series[0]['data'] = data;\r\n      //中心坐标\r\n      chartOption.radar[0].center = [(config.option.grid.left || 50) + '%', (config.option.grid.top || 50) + '%'];\r\n      // 合并配置\r\n    if (props.config && config.option) {\r\n      merge(chartOption, config.option)\r\n      chartOption = handleTotalAndUnit(props.compName, chartOption, config, chartData)\r\n      chartOption.legend.bottom = 0;\r\n      chartOption.legend.right = 0;\r\n      option.value = deepClone(chartOption)\r\n      pageTips.show = false\r\n    }\r\n  } else {\r\n    pageTips.status = 1\r\n    pageTips.show = true\r\n  }\r\n}\r\n\r\nonMounted(() => {\r\n  queryData()\r\n})\r\n</script>\r\n<style>\r\n.content {\r\n  padding: 10px;\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-work/components/echarts/JRadar/index.vue'\nwx.createComponent(Component)"], "names": ["ref", "useChartHook", "isNumber", "data", "getCustomColor", "merge", "handleTotalAndUnit", "deepClone", "onMounted", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAaA,MAAA,gBAA0B,MAAA;AAC1B,MAAA,YAAsB,MAAA;;;;;AAGtB,UAAM,QAAQ;AAKR,UAAA,SAASA,cAAI,IAAA,EAAE;AACrB,QAAI,cAAc;AAAA,MAChB,QAAQ;AAAA,QACN,GAAG;AAAA,QACH,GAAG;AAAA,QACH,QAAO;AAAA,QAAE,OAAM;AAAA,QACf,MAAM,CAAC,MAAM,IAAI;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL;AAAA,UACE,WAAW,CAAA;AAAA,QAAC;AAAA,MAEhB;AAAA,MACA,QAAQ;AAAA,QACN;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,YACJ;AAAA,cACE,OAAO,CAAC;AAAA,cACR,MAAM;AAAA,YAAA;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IAEJ;AAEA,QAAI,CAAC,EAAE,YAAY,QAAQ,UAAU,OAAA,GAAU,EAAE,WAAW,IAAIC,kDAAa,OAAO,UAAU;AAG9F,aAAS,WAAW,MAAM;AACxB,UAAI,YAAiB,WAAW;AAC5B,UAAA,OAAO,cAAc,UAAU;AACrB,oBAAA,KAAK,MAAM,SAAS;AAAA,MAAA;AAE9B,UAAA,aAAa,UAAU,SAAS,GAAG;AAEnC,YAAG,OAAO,iBAAiBC,SAAS,SAAA,OAAO,aAAa,GAAE;AACtD,sBAAY,UAAU,MAAM,GAAE,OAAO,aAAa;AAAA,QAAA;AAGhD,cAAA,WAAY,KAAK,IAAI,GAAG,UAAU,IAAI,CAAC,EAAC,YAAU,KAAK,CAAC;AAE9D,YAAI,UAAU,MAAM,KAAK,IAAI,IAAI,UAAU,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,CAAC;AAEpE,YAAI,YAAY,MAAM;AAAA,UAClB,IAAI;AAAA,YACA,UAAU,IAAI,CAAC,SAAS;AAChB,kBAAA,EAAE,MAAM,IAAA,IAAQ;AACpB,qBAAO,EAAE,MAAM,KAAK,MAAM,MAAM,SAAS;AAAA,YAC5C,CAAA;AAAA,UAAA;AAAA,QAET;AACA,YAAIC,QAAO,CAAC;AACZ,cAAM,SAASC,uCAAA,eAAe,OAAO,OAAO,WAAW;AAE/C,gBAAA,QAAQ,CAAC,MAAK,UAAU;AAC5B,cAAI,MAAM,EAAE,MAAM,MAAK,WAAU,EAAC,OAAM,OAAO,KAAK,EAAE,SAAO,KAAA,EAAM;AACnE,cAAI,WAAW,UAAU,OAAO,CAAC,SAAS,SAAS,KAAK,IAAI;AAC5D,cAAI,OAAO,IAAI,SAAS,IAAI,CAAC,SAAS,KAAK,KAAK;AAEhDD,gBAAK,KAAK,GAAG;AAAA,QAAA,CAChB;AACW,oBAAA,MAAM,CAAC,EAAE,YAAY;AACjC,oBAAY,OAAO,OAAO;AAC1B,oBAAY,OAAO,CAAC,EAAE,MAAM,IAAIA;AAEhC,oBAAY,MAAM,CAAC,EAAE,SAAS,EAAE,OAAO,OAAO,KAAK,QAAQ,MAAM,MAAM,OAAO,OAAO,KAAK,OAAO,MAAM,GAAG;AAExG,YAAA,MAAM,UAAU,OAAO,QAAQ;AAC3BE,8BAAA,aAAa,OAAO,MAAM;AAChC,wBAAcC,uCAAmB,mBAAA,MAAM,UAAU,aAAa,QAAQ,SAAS;AAC/E,sBAAY,OAAO,SAAS;AAC5B,sBAAY,OAAO,QAAQ;AACpB,iBAAA,QAAQC,mCAAU,WAAW;AACpC,mBAAS,OAAO;AAAA,QAAA;AAAA,MAClB,OACK;AACL,iBAAS,SAAS;AAClB,iBAAS,OAAO;AAAA,MAAA;AAAA,IAClB;AAGFC,kBAAAA,UAAU,MAAM;AACJ,gBAAA;AAAA,IAAA,CACX;;;;;;;;;;;;;;;;ACzGD,GAAG,gBAAgBC,SAAS;"}