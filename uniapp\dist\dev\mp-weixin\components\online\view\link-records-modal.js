"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../common/vendor.js");
const utils_http = require("../../../utils/http.js");
const utils_is = require("../../../utils/is.js");
const common_uitls = require("../../../common/uitls.js");
if (!Array) {
  const _easycom_wd_checkbox2 = common_vendor.resolveComponent("wd-checkbox");
  const _easycom_wd_checkbox_group2 = common_vendor.resolveComponent("wd-checkbox-group");
  const _easycom_wd_radio2 = common_vendor.resolveComponent("wd-radio");
  const _easycom_wd_cell2 = common_vendor.resolveComponent("wd-cell");
  const _easycom_wd_radio_group2 = common_vendor.resolveComponent("wd-radio-group");
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _easycom_wd_popup2 = common_vendor.resolveComponent("wd-popup");
  (_easycom_wd_checkbox2 + _easycom_wd_checkbox_group2 + _easycom_wd_radio2 + _easycom_wd_cell2 + _easycom_wd_radio_group2 + _easycom_z_paging2 + _easycom_PageLayout2 + _easycom_wd_popup2)();
}
const _easycom_wd_checkbox = () => "../../../node-modules/wot-design-uni/components/wd-checkbox/wd-checkbox.js";
const _easycom_wd_checkbox_group = () => "../../../node-modules/wot-design-uni/components/wd-checkbox-group/wd-checkbox-group.js";
const _easycom_wd_radio = () => "../../../node-modules/wot-design-uni/components/wd-radio/wd-radio.js";
const _easycom_wd_cell = () => "../../../node-modules/wot-design-uni/components/wd-cell/wd-cell.js";
const _easycom_wd_radio_group = () => "../../../node-modules/wot-design-uni/components/wd-radio-group/wd-radio-group.js";
const _easycom_z_paging = () => "../../../uni_modules/z-paging/components/z-paging/z-paging.js";
const _easycom_PageLayout = () => "../../PageLayout/PageLayout.js";
const _easycom_wd_popup = () => "../../../node-modules/wot-design-uni/components/wd-popup/wd-popup.js";
if (!Math) {
  (_easycom_wd_checkbox + _easycom_wd_checkbox_group + _easycom_wd_radio + _easycom_wd_cell + _easycom_wd_radio_group + _easycom_z_paging + _easycom_PageLayout + _easycom_wd_popup)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "popupReportModal",
  options: {
    styleIsolation: "shared"
  }
}), {
  __name: "link-records-modal",
  props: {
    dictTable: {
      type: String,
      required: true
    },
    dictCode: {
      type: String,
      required: true
    },
    dictText: {
      type: String,
      required: true
    },
    multi: {
      type: Boolean,
      required: false
    },
    imageField: {
      type: String,
      required: false
    }
  },
  emits: ["change", "close"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const toast = common_vendor.useToast();
    const show = common_vendor.ref(true);
    const api = {
      getColumns: "/online/cgform/api/getColumns",
      getData: "/online/cgform/api/getData"
    };
    console.log("props:::", props);
    const navTitle = common_vendor.ref("");
    const paging = common_vendor.ref(null);
    const dataList = common_vendor.ref([]);
    let loadedColumns = false;
    const columns = common_vendor.ref([]);
    const selectArr = common_vendor.ref([]);
    const checkedValue = common_vendor.ref(props.multi ? [] : "");
    const checkboxRef = common_vendor.ref(null);
    const search = common_vendor.reactive({
      keyword: "",
      placeholder: "",
      field: ""
    });
    const handleClose = () => {
      setTimeout(() => {
        emit("close");
      }, 400);
    };
    const beforeOpen = (arr) => {
      selectArr.value = arr || [];
    };
    const handleConfirm = () => {
      if (checkedValue.value.length == 0) {
        toast.warning("还没选择~");
        return;
      }
      const result = [];
      let value = checkedValue.value;
      if (!Array.isArray(checkedValue.value)) {
        value = [checkedValue.value];
      }
      value.forEach((index) => {
        result.push(dataList.value[index]);
      });
      show.value = false;
      emit("change", result);
      handleClose();
    };
    const handleCancel = () => {
      show.value = false;
      handleClose();
      console.log("取消了~");
    };
    const hanldeCheck = (index) => {
      if (props.multi) {
        if (Array.isArray(checkboxRef.value)) {
          checkboxRef.value[index].toggle();
        }
      } else {
        checkedValue.value = index;
      }
    };
    const getRpColumns = () => {
      return new Promise((resolve, reject) => {
        if (loadedColumns) {
          resolve();
        } else {
          let linkTableSelectFields = props.dictCode + "," + props.dictText;
          utils_http.http.get(`${api.getColumns}/${props.dictTable}?linkTableSelectFields=${linkTableSelectFields}`).then((res) => {
            var _a;
            if (res.success) {
              loadedColumns = true;
              const { result } = res;
              navTitle.value = result.description;
              result.code;
              (_a = result.columns) == null ? void 0 : _a.forEach((item) => {
                if (linkTableSelectFields.includes(item.dataIndex)) {
                  columns.value.push(item);
                }
              });
              resolve();
            } else {
              reject();
            }
          }).catch((err) => {
            reject();
          });
        }
      });
    };
    const queryList = (pageNo, pageSize) => {
      const pararms = { pageNo, pageSize, linkTableSelectFields: "" };
      if (search.keyword) {
        pararms[search.field] = `*${search.keyword}*`;
      }
      getRpColumns().then(() => {
        let linkTableSelectFields = props.dictCode + "," + props.dictText;
        if (props.imageField) {
          linkTableSelectFields = linkTableSelectFields + "," + props.imageField;
        }
        pararms.linkTableSelectFields = linkTableSelectFields;
        utils_http.http.get(`${api.getData}/${props.dictTable}`, pararms).then((res) => {
          if (res.success && res.result.records) {
            let dataRecords = res.result.records;
            if (dataRecords && dataRecords.length > 0) {
              let id = dataRecords[0]["id"];
              for (let item of dataRecords) {
                if (!id) {
                  item.id = (/* @__PURE__ */ new Date()).getTime();
                }
                if (props.imageField && item[props.imageField]) {
                  let imgUrlArr = item[props.imageField].split(",");
                  item[props.imageField] = imgUrlArr.length > 0 ? common_uitls.getFileAccessHttpUrl(imgUrlArr[0]) : "";
                }
              }
            }
            if (selectArr.value && utils_is.isArray(selectArr) && selectArr.length > 0)
              ;
            paging.value.complete(dataRecords != null ? dataRecords : []);
          } else {
            paging.value.complete(false);
          }
        }).catch((err) => {
        });
      }).catch((err) => {
      });
    };
    __expose({
      beforeOpen
    });
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: __props.multi
      }, __props.multi ? {
        b: common_vendor.f(dataList.value, (item, index, i0) => {
          return common_vendor.e$1({
            a: __props.imageField && item[__props.imageField]
          }, __props.imageField && item[__props.imageField] ? {
            b: common_vendor.s({
              backgroundImage: "url(" + item[__props.imageField] + ")"
            })
          } : {}, {
            c: common_vendor.f(columns.value, (cItem, cIndex, i1) => {
              return {
                a: common_vendor.t(cItem.title),
                b: common_vendor.t(item[cItem.dataIndex]),
                c: cIndex
              };
            }),
            d: common_vendor.sr(checkboxRef, "d434fff2-4-" + i0 + ",d434fff2-3", {
              "k": "checkboxRef",
              "f": 1
            }),
            e: "d434fff2-4-" + i0 + ",d434fff2-3",
            f: common_vendor.p({
              modelValue: index
            }),
            g: common_vendor.o(() => {
            }, index),
            h: common_vendor.o(($event) => hanldeCheck(index), index),
            i: index
          });
        }),
        c: common_vendor.o(($event) => checkedValue.value = $event),
        d: common_vendor.p({
          shape: "square",
          modelValue: checkedValue.value
        })
      } : {
        e: common_vendor.f(dataList.value, (item, index, i0) => {
          return common_vendor.e$1({
            a: __props.imageField && item[__props.imageField]
          }, __props.imageField && item[__props.imageField] ? {
            b: common_vendor.s({
              backgroundImage: "url(" + item[__props.imageField] + ")"
            })
          } : {}, {
            c: common_vendor.f(columns.value, (cItem, cIndex, i1) => {
              return {
                a: common_vendor.t(cItem.title),
                b: common_vendor.t(item[cItem.dataIndex]),
                c: cIndex
              };
            }),
            d: "d434fff2-7-" + i0 + "," + ("d434fff2-6-" + i0),
            e: common_vendor.p({
              value: index
            }),
            f: common_vendor.o(() => {
            }, index),
            g: common_vendor.o(($event) => hanldeCheck(index), index),
            h: "d434fff2-6-" + i0 + ",d434fff2-5",
            i: index
          });
        }),
        f: common_vendor.o(($event) => checkedValue.value = $event),
        g: common_vendor.p({
          shape: "dot",
          modelValue: checkedValue.value
        })
      }, {
        h: common_vendor.sr(paging, "d434fff2-2,d434fff2-1", {
          "k": "paging"
        }),
        i: common_vendor.o(queryList),
        j: common_vendor.o(($event) => dataList.value = $event),
        k: common_vendor.p({
          fixed: false,
          ["default-page-size"]: 15,
          modelValue: dataList.value
        }),
        l: common_vendor.o(handleConfirm),
        m: common_vendor.o(handleCancel),
        n: common_vendor.p({
          navTitle: navTitle.value,
          type: "popup",
          navRightText: "确定"
        }),
        o: common_vendor.o(($event) => show.value = $event),
        p: common_vendor.p({
          position: "bottom",
          modelValue: show.value
        })
      });
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d434fff2"]]);
wx.createComponent(Component);
//# sourceMappingURL=link-records-modal.js.map
