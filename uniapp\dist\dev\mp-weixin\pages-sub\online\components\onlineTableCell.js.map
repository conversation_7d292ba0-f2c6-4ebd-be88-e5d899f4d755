{"version": 3, "file": "onlineTableCell.js", "sources": ["../../../../../../src/pages-sub/online/components/onlineTableCell.vue", "../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvcGFnZXMtc3ViL29ubGluZS9jb21wb25lbnRzL29ubGluZVRhYmxlQ2VsbC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"onlineTableCell\">\r\n    <!--图片-->\r\n    <template v-if=\"column?.scopedSlots?.customRender === 'imgSlot'\">\r\n      <template v-if=\"record[column.dataIndex]\">\r\n        <wd-img\r\n          width=\"30\"\r\n          height=\"30\"\r\n          :src=\"getFirstImg(record[column.dataIndex])\"\r\n          @click=\"handleClickImg\"\r\n        ></wd-img>\r\n        <ImgPreview\r\n          v-if=\"imgPreview.show\"\r\n          :urls=\"imgPreview.urls\"\r\n          @close=\"() => (imgPreview.show = false)\"\r\n        ></ImgPreview>\r\n      </template>\r\n      <template v-else>\r\n        <text>无图片</text>\r\n      </template>\r\n    </template>\r\n    <!--下载-->\r\n    <template v-else-if=\"column?.scopedSlots?.customRender === 'fileSlot'\">\r\n      <template v-if=\"record[column.dataIndex]\">\r\n        <wd-button @click=\"handleDownload(record[column.dataIndex])\">下载</wd-button>\r\n      </template>\r\n      <template v-else>\r\n        <text>无文件</text>\r\n      </template>\r\n    </template>\r\n    <template v-else-if=\"column?.scopedSlots?.customRender === 'htmlSlot'\">\r\n      <!-- 增加富文本控件配置href跳转 -->\r\n      <template v-if=\"column.fieldHref\">\r\n        <text class=\"ellipsis-2\">暂不支持</text>\r\n      </template>\r\n      <template v-else>\r\n        <rich-text :nodes=\"record[column.dataIndex]\"></rich-text>\r\n      </template>\r\n    </template>\r\n    <template v-else-if=\"column?.scopedSlots?.customRender === 'pcaSlot'\">\r\n      <text class=\"ellipsis-2\">{{ getPcaText(record[column.dataIndex]) || '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'}}</text>\r\n    </template>\r\n    <template v-else-if=\"column?.scopedSlots?.customRender === 'dateSlot'\">\r\n      <text class=\"ellipsis-2\">{{ getFormatDate(record[column.dataIndex], column) || '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'}}</text>\r\n    </template>\r\n    <template v-else>\r\n      <text class=\"ellipsis-2\">{{ renderVal(record, column) || '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' }}</text>\r\n    </template>\r\n  </view>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { getFormatDate, filterMultiDictText } from '../utils/index'\r\nimport { isString } from '@/utils/is'\r\nimport { getFileAccessHttpUrl } from '@/common/uitls'\r\nimport { getAreaTextByCode } from '@/common/areaData/Area'\r\ndefineOptions({\r\n  name: 'onlineTableCell',\r\n  options: {\r\n    styleIsolation: 'shared',\r\n  },\r\n})\r\nconst props = defineProps({\r\n  columnsInfo: {\r\n    type: Object,\r\n    default: () => {},\r\n  },\r\n  column: {\r\n    type: Object,\r\n    default: () => {},\r\n  },\r\n  record: {\r\n    type: Object,\r\n    default: () => {},\r\n  },\r\n})\r\nconst imgPreview = ref({\r\n  show: false,\r\n  urls: [],\r\n})\r\n// 下载\r\nconst handleDownload = (text) => {\r\n  uni.downloadFile({\r\n    url: text,\r\n    success: (res) => {\r\n      if (res.statusCode === 200) {\r\n        console.log('下载成功')\r\n        console.log(res);\r\n      }\r\n    },\r\n  })\r\n}\r\n// 省市区\r\nconst getPcaText = (code) => {\r\n  if (!code) {\r\n    return ''\r\n  }\r\n  return getAreaTextByCode(code)\r\n}\r\n// 列表只显示第一张图\r\nconst getFirstImg = (text) => {\r\n  if (isString(text)) {\r\n    var imgs = text.split(',')\r\n    return getFileAccessHttpUrl(imgs[0])\r\n  } else {\r\n    return ''\r\n  }\r\n}\r\n// 点击图时\r\nconst handleClickImg = () => {\r\n  imgPreview.value.show = true\r\n}\r\n// 渲染值\r\nconst renderVal = (record, column) => {\r\n  const { customRender, hrefSlotName, dataIndex, fieldType } = column\r\n  let text = record[dataIndex]\r\n  if (['date', 'Date'].includes(column['fieldType'])) {\r\n    // online报表中类型配置为日期（yyyy-MM-dd ），但是实际展示为日期时间格式(yyyy-MM-dd HH:mm:ss)\r\n    if (!text) {\r\n      return ''\r\n    }\r\n    if (text.length > 10) {\r\n      return text.substring(0, 10)\r\n    }\r\n    return text\r\n  } else if (['popup_dict'].includes(column['fieldType'])) {\r\n    const dict = record[dataIndex + '_dictText']\r\n    if (dict != undefined) {\r\n      return record[dataIndex + '_dictText']\r\n    }\r\n    return text\r\n  } else if (customRender) {\r\n    // 字典\r\n    let dictCode = customRender as string\r\n    let replaceFlag = '_replace_text_'\r\n    let value = text\r\n    // 如果 dictCode 有值，就进行字典转换\r\n    if (dictCode) {\r\n      if (dictCode.startsWith(replaceFlag)) {\r\n        let textFieldName = dictCode.replace(replaceFlag, '')\r\n        value = record[textFieldName]\r\n      } else {\r\n        value = filterMultiDictText(unref(props.columnsInfo.dictOptions)[dictCode], text + '')\r\n      }\r\n    }\r\n    // 扩展参数设置列的内容长度\r\n    if (column.showLength) {\r\n      if (value && value.length > column.showLength) {\r\n        value = value.substr(0, column.showLength) + '...'\r\n      }\r\n    }\r\n    return value\r\n  } else {\r\n    return text\r\n  }\r\n}\r\n// 初始化\r\nconst init = () => {\r\n  const field = props.column.dataIndex\r\n  if (props.column?.scopedSlots?.customRender === 'imgSlot') {\r\n    const text = props.record[field]\r\n    if (isString(text)) {\r\n      imgPreview.value.urls = text.split(',').map((item) => getFileAccessHttpUrl(item))\r\n    } else {\r\n      return ''\r\n    }\r\n  }\r\n}\r\ninit()\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n:deep(.wd-button) {\r\n  --wot-button-medium-height: 30px;\r\n  --wot-button-medium-fs: 12px;\r\n  --wot-button-medium-padding: 8px;\r\n  &.is-medium.is-round {\r\n    min-width: 80px;\r\n  }\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-sub/online/components/onlineTableCell.vue'\nwx.createComponent(Component)"], "names": ["ref", "uni", "getAreaTextByCode", "isString", "getFileAccessHttpUrl", "filterMultiDictText", "unref"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DA,UAAM,QAAQ;AAcd,UAAM,aAAaA,cAAAA,IAAI;AAAA,MACrB,MAAM;AAAA,MACN,MAAM,CAAA;AAAA,IAAC,CACR;AAEK,UAAA,iBAAiB,CAAC,SAAS;AAC/BC,oBAAAA,MAAI,aAAa;AAAA,QACf,KAAK;AAAA,QACL,SAAS,CAAC,QAAQ;AACZ,cAAA,IAAI,eAAe,KAAK;AAC1B,oBAAQ,IAAI,MAAM;AAClB,oBAAQ,IAAI,GAAG;AAAA,UAAA;AAAA,QACjB;AAAA,MACF,CACD;AAAA,IACH;AAEM,UAAA,aAAa,CAAC,SAAS;AAC3B,UAAI,CAAC,MAAM;AACF,eAAA;AAAA,MAAA;AAET,aAAOC,qBAAAA,kBAAkB,IAAI;AAAA,IAC/B;AAEM,UAAA,cAAc,CAAC,SAAS;AACxB,UAAAC,SAAAA,SAAS,IAAI,GAAG;AACd,YAAA,OAAO,KAAK,MAAM,GAAG;AAClB,eAAAC,aAAA,qBAAqB,KAAK,CAAC,CAAC;AAAA,MAAA,OAC9B;AACE,eAAA;AAAA,MAAA;AAAA,IAEX;AAEA,UAAM,iBAAiB,MAAM;AAC3B,iBAAW,MAAM,OAAO;AAAA,IAC1B;AAEM,UAAA,YAAY,CAAC,QAAQ,WAAW;AACpC,YAAM,EAAE,cAAc,cAAc,WAAW,UAAc,IAAA;AACzD,UAAA,OAAO,OAAO,SAAS;AACvB,UAAA,CAAC,QAAQ,MAAM,EAAE,SAAS,OAAO,WAAW,CAAC,GAAG;AAElD,YAAI,CAAC,MAAM;AACF,iBAAA;AAAA,QAAA;AAEL,YAAA,KAAK,SAAS,IAAI;AACb,iBAAA,KAAK,UAAU,GAAG,EAAE;AAAA,QAAA;AAEtB,eAAA;AAAA,MAAA,WACE,CAAC,YAAY,EAAE,SAAS,OAAO,WAAW,CAAC,GAAG;AACjD,cAAA,OAAO,OAAO,YAAY,WAAW;AAC3C,YAAI,QAAQ,QAAW;AACd,iBAAA,OAAO,YAAY,WAAW;AAAA,QAAA;AAEhC,eAAA;AAAA,iBACE,cAAc;AAEvB,YAAI,WAAW;AACf,YAAI,cAAc;AAClB,YAAI,QAAQ;AAEZ,YAAI,UAAU;AACR,cAAA,SAAS,WAAW,WAAW,GAAG;AACpC,gBAAI,gBAAgB,SAAS,QAAQ,aAAa,EAAE;AACpD,oBAAQ,OAAO,aAAa;AAAA,UAAA,OACvB;AACG,oBAAAC,4BAAAA,oBAAoBC,oBAAM,MAAM,YAAY,WAAW,EAAE,QAAQ,GAAG,OAAO,EAAE;AAAA,UAAA;AAAA,QACvF;AAGF,YAAI,OAAO,YAAY;AACrB,cAAI,SAAS,MAAM,SAAS,OAAO,YAAY;AAC7C,oBAAQ,MAAM,OAAO,GAAG,OAAO,UAAU,IAAI;AAAA,UAAA;AAAA,QAC/C;AAEK,eAAA;AAAA,MAAA,OACF;AACE,eAAA;AAAA,MAAA;AAAA,IAEX;AAEA,UAAM,OAAO,MAAM;;AACX,YAAA,QAAQ,MAAM,OAAO;AAC3B,YAAI,iBAAM,WAAN,mBAAc,gBAAd,mBAA2B,kBAAiB,WAAW;AACnD,cAAA,OAAO,MAAM,OAAO,KAAK;AAC3B,YAAAH,SAAAA,SAAS,IAAI,GAAG;AACP,qBAAA,MAAM,OAAO,KAAK,MAAM,GAAG,EAAE,IAAI,CAAC,SAASC,aAAqB,qBAAA,IAAI,CAAC;AAAA,QAAA,OAC3E;AACE,iBAAA;AAAA,QAAA;AAAA,MACT;AAAA,IAEJ;AACK,SAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvKL,GAAG,gBAAgB,SAAS;"}