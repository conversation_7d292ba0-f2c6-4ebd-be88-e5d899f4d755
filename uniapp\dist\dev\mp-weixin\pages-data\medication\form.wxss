/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-scroll-view.data-v-5d191dff {
  height: calc(100vh - 44px);
  /* 减去导航栏高度 */
  width: 100%;
}
.form-container.data-v-5d191dff {
  padding: 20rpx;
  padding-bottom: 120rpx;
  /* 增加底部内边距，防止内容被遮挡 */
}
.form-container .form-header.data-v-5d191dff {
  margin-bottom: 20rpx;
  text-align: center;
  /* 标题居中 */
}
.form-container .form-header .form-title.data-v-5d191dff {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.form-container .form-section.data-v-5d191dff {
  background-color: #FFFFFF;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}
.form-container .form-section .form-item.data-v-5d191dff {
  margin-bottom: 30rpx;
}
.form-container .form-section .form-item .form-label.data-v-5d191dff {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}
.form-container .form-section .form-item .inline-form-item.data-v-5d191dff {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.form-container .form-section .form-item .inline-form-item .inline-form-label.data-v-5d191dff {
  font-size: 28rpx;
  color: #333;
  min-width: 160rpx;
}
.form-container .form-section .form-item .inline-form-item .inline-form-input.data-v-5d191dff {
  flex: 1;
  background-color: #F7F7F7;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  min-height: 80rpx;
}
.form-container .form-section .form-item .inline-form-item .inline-form-input.data-v-5d191dff:disabled {
  background-color: #F5F5F5;
  color: #666;
}
.form-container .form-section .form-item .date-picker.data-v-5d191dff {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #F7F7F7;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  min-height: 80rpx;
}
.form-container .form-section .form-item .date-picker .placeholder.data-v-5d191dff {
  color: #999;
}
.form-container .form-section .form-item .form-input.data-v-5d191dff,
.form-container .form-section .form-item .form-textarea.data-v-5d191dff {
  width: 100%;
  background-color: #F7F7F7;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  min-height: 80rpx;
}
.form-container .form-section .form-item .form-input.data-v-5d191dff:disabled,
.form-container .form-section .form-item .form-textarea.data-v-5d191dff:disabled {
  background-color: #F5F5F5;
  color: #666;
}
.form-container .form-section .form-item .form-textarea.data-v-5d191dff {
  height: 180rpx;
}
.form-container .form-section .form-item .radio-group.data-v-5d191dff {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  /* 增加间距 */
}
.form-container .form-section .form-item .radio-group .radio-row.data-v-5d191dff {
  display: flex;
  gap: 150rpx;
  /* 增加间距 */
  margin-left: 20rpx;
  /* 增加左边距，实现对齐 */
  justify-content: flex-start;
  /* 添加左对齐 */
}
.form-container .form-section .form-item .radio-group .radio-item.data-v-5d191dff {
  display: flex;
  align-items: center;
  min-width: 180rpx;
  width: 240rpx;
  /* 确保宽度一致，增大宽度值以容纳更长的文本 */
}
.form-container .form-section .form-item .radio-group .radio-item .radio-btn.data-v-5d191dff {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 2rpx solid #CCCCCC;
  margin-right: 10rpx;
  position: relative;
}
.form-container .form-section .form-item .radio-group .radio-item .radio-btn.checked.data-v-5d191dff {
  border-color: #07C160;
}
.form-container .form-section .form-item .radio-group .radio-item .radio-btn.checked.data-v-5d191dff:after {
  content: "";
  position: absolute;
  width: 24rpx;
  height: 24rpx;
  background-color: #07C160;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.form-container .form-section .form-item .radio-group .radio-item text.data-v-5d191dff {
  font-size: 28rpx;
  color: #333;
}
.form-container .inline-picker-wrapper.data-v-5d191dff {
  flex: 1;
  width: 100%;
}
.form-container .required.data-v-5d191dff {
  color: #FF0000;
  margin-right: 4rpx;
}
.form-container .submit-section.data-v-5d191dff {
  margin-top: 40rpx;
  margin-bottom: 60rpx;
  /* 增加底部间距 */
}
.form-container .submit-section .submit-btn.data-v-5d191dff {
  width: 100%;
  background-color: #07C160;
  color: #FFFFFF;
  border-radius: 8rpx;
  font-size: 32rpx;
  padding: 20rpx 0;
}
.checkbox-group.data-v-5d191dff {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}
.checkbox-group .checkbox-row.data-v-5d191dff {
  display: flex;
  gap: 150rpx;
  margin-left: 20rpx;
  justify-content: flex-start;
}
.checkbox-group .checkbox-item.data-v-5d191dff {
  display: flex;
  align-items: center;
  min-width: 180rpx;
  width: 240rpx;
}
.checkbox-group .checkbox-item .checkbox-btn.data-v-5d191dff {
  width: 36rpx;
  height: 36rpx;
  border-radius: 4rpx;
  border: 2rpx solid #CCCCCC;
  margin-right: 10rpx;
  position: relative;
}
.checkbox-group .checkbox-item .checkbox-btn.checked.data-v-5d191dff {
  border-color: #07C160;
  background-color: #07C160;
}
.checkbox-group .checkbox-item .checkbox-btn.checked.data-v-5d191dff:after {
  content: "";
  position: absolute;
  width: 20rpx;
  height: 10rpx;
  border-left: 4rpx solid #FFFFFF;
  border-bottom: 4rpx solid #FFFFFF;
  top: 45%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
}
.checkbox-group .checkbox-item text.data-v-5d191dff {
  font-size: 28rpx;
  color: #333;
}
.checkbox-group .sub-form-item.data-v-5d191dff {
  margin-left: 50rpx;
  margin-top: -10rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}
.checkbox-group .sub-form-item .sub-form-label.data-v-5d191dff {
  font-size: 26rpx;
  color: #666;
  min-width: 180rpx;
}
.checkbox-group .sub-form-item .sub-form-input.data-v-5d191dff {
  flex: 1;
  background-color: #F7F7F7;
  border-radius: 8rpx;
  padding: 15rpx;
  font-size: 26rpx;
  color: #333;
  min-height: 70rpx;
  width: 100%;
  box-sizing: border-box;
}
.checkbox-group .sub-form-item .picker.data-v-5d191dff {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #F7F7F7;
  border-radius: 8rpx;
  padding: 15rpx;
  font-size: 26rpx;
  color: #333;
  min-height: 70rpx;
  width: 100%;
  box-sizing: border-box;
}
.checkbox-group .sub-form-items-row.data-v-5d191dff {
  display: flex;
  margin-left: 50rpx;
  gap: 20rpx;
  margin-top: -10rpx;
  margin-bottom: 20rpx;
}
.checkbox-group .sub-form-items-row .sub-form-item.data-v-5d191dff {
  flex: 1;
  width: 50%;
  min-width: calc(50% - 10rpx);
  margin: 0;
  display: flex;
  align-items: center;
}
.checkbox-group .sub-form-items-row .sub-form-item .picker-container.data-v-5d191dff {
  width: 100%;
  flex: 1;
  display: block;
}
.checkbox-group .sub-form-items-row .sub-form-item .temp-picker.data-v-5d191dff {
  width: 100%;
  flex: 1;
  display: block;
}
.checkbox-group .sub-form-items-row .sub-form-item .sub-form-input.data-v-5d191dff {
  flex: 1;
  background-color: #F7F7F7;
  border-radius: 8rpx;
  padding: 15rpx;
  font-size: 26rpx;
  color: #333;
  min-height: 70rpx;
  width: 100%;
  box-sizing: border-box;
}
.checkbox-group .sub-form-items-row .sub-form-item .picker.data-v-5d191dff {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #F7F7F7;
  border-radius: 8rpx;
  padding: 15rpx;
  font-size: 26rpx;
  color: #333;
  min-height: 70rpx;
  width: 100%;
  box-sizing: border-box;
}

/* 添加药物选择相关样式 */
.add-btn.data-v-5d191dff {
  background-color: #07C160;
  color: #FFFFFF;
  font-size: 28rpx;
  padding: 15rpx 25rpx;
  border-radius: 8rpx;
  margin-left: 20rpx;
}
.add-btn.data-v-5d191dff:disabled {
  background-color: #cccccc;
}
.search-wrapper.data-v-5d191dff {
  position: relative;
  flex: 1;
}
.search-wrapper .search-input.data-v-5d191dff {
  width: 100%;
  background-color: #F7F7F7;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  min-height: 80rpx;
}
.search-wrapper .search-results.data-v-5d191dff {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  max-height: 400rpx;
  overflow-y: auto;
  z-index: 100;
  margin-top: 5rpx;
}
.search-wrapper .search-results .search-result-item.data-v-5d191dff {
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1rpx solid #F0F0F0;
}
.search-wrapper .search-results .search-result-item.data-v-5d191dff:hover, .search-wrapper .search-results .search-result-item.data-v-5d191dff:active {
  background-color: #F7F7F7;
}
.search-wrapper .search-results .search-result-item .category-name.data-v-5d191dff {
  color: #999;
  font-size: 26rpx;
  margin-left: 10rpx;
}
.picker.data-v-5d191dff {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #F7F7F7;
  border-radius: 8rpx;
  padding: 15rpx 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  min-height: 80rpx;
}
.picker .placeholder.data-v-5d191dff {
  color: #999;
}
.medication-list-header.data-v-5d191dff {
  margin-bottom: 20rpx;
}
.medication-list-header .medication-list-title.data-v-5d191dff {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}
.medication-item.data-v-5d191dff {
  background-color: #F7F7F7;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  position: relative;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.medication-item .medication-name.data-v-5d191dff {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #eaeaea;
}
.medication-item .medication-name .medication-other-name.data-v-5d191dff {
  font-weight: normal;
  font-size: 28rpx;
  color: #666;
  margin-left: 10rpx;
}
.medication-item .medication-name .delete-report-btn.data-v-5d191dff {
  margin-left: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5rpx;
}
.medication-item .medication-details.data-v-5d191dff {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 8rpx;
  padding: 15rpx;
}
.medication-item .medication-details .inline-form-item.data-v-5d191dff {
  margin-bottom: 25rpx;
  /* 频率选择器的特殊处理 */
}
.medication-item .medication-details .inline-form-item.data-v-5d191dff:last-child {
  margin-bottom: 0;
}
.medication-item .medication-details .inline-form-item .inline-form-label.data-v-5d191dff {
  margin-bottom: 12rpx;
  font-weight: 500;
  color: #2C2C2C;
}
.medication-item .medication-details .inline-form-item .inline-form-input.data-v-5d191dff {
  width: 100%;
}
.medication-item .medication-details .inline-form-item .frequency-input-wrapper.data-v-5d191dff,
.medication-item .medication-details .inline-form-item .dosage-input-wrapper.data-v-5d191dff {
  display: flex;
  align-items: center;
  width: 100%;
}
.medication-item .medication-details .inline-form-item .frequency-input-wrapper .picker.data-v-5d191dff,
.medication-item .medication-details .inline-form-item .dosage-input-wrapper .picker.data-v-5d191dff {
  flex: 1;
  min-width: 200rpx;
  /* 增加最小宽度 */
  flex-basis: 220rpx;
  /* 增加基础宽度，使剂量和频率输入框保持一致 */
  flex-grow: 1;
  /* 允许适当增长 */
}
.medication-item .medication-details .inline-form-item .frequency-input-wrapper .unit-text.data-v-5d191dff,
.medication-item .medication-details .inline-form-item .dosage-input-wrapper .unit-text.data-v-5d191dff {
  margin-left: 10rpx;
  color: #666;
  font-size: 26rpx;
  flex-shrink: 0;
  width: 120rpx;
  text-align: center;
}
.medication-item .medication-details .inline-form-item .frequency-input-wrapper .dosage-input.data-v-5d191dff,
.medication-item .medication-details .inline-form-item .dosage-input-wrapper .dosage-input.data-v-5d191dff {
  flex: 1;
  flex-basis: 220rpx;
  /* 与频率选择器保持相同的基础宽度 */
  flex-grow: 1;
  /* 允许适当增长 */
  background-color: #F7F7F7;
  border-radius: 8rpx;
  padding: 15rpx 20rpx;
  font-size: 28rpx;
  color: #333;
  min-height: 80rpx;
  min-width: 200rpx;
  /* 增加最小宽度，确保与频率选择器保持一致 */
}
.medication-item .medication-details .inline-form-item .frequency-input-wrapper .dosage-input.data-v-5d191dff::-moz-placeholder, .medication-item .medication-details .inline-form-item .dosage-input-wrapper .dosage-input.data-v-5d191dff::-moz-placeholder {
  color: #999;
}
.medication-item .medication-details .inline-form-item .frequency-input-wrapper .dosage-input.data-v-5d191dff::placeholder,
.medication-item .medication-details .inline-form-item .dosage-input-wrapper .dosage-input.data-v-5d191dff::placeholder {
  color: #999;
}
.medication-item .medication-details .inline-form-item .frequency-input-wrapper .unit-picker.data-v-5d191dff,
.medication-item .medication-details .inline-form-item .dosage-input-wrapper .unit-picker.data-v-5d191dff {
  width: 120rpx;
  margin-left: 10rpx;
  flex-shrink: 0;
}
.medication-item .medication-details .inline-form-item .frequency-input-wrapper .unit-picker .unit-picker-inner.data-v-5d191dff,
.medication-item .medication-details .inline-form-item .dosage-input-wrapper .unit-picker .unit-picker-inner.data-v-5d191dff {
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* 改为space-between使文字和图标分开 */
  background-color: #F7F7F7;
  border-radius: 8rpx;
  padding: 15rpx 20rpx;
  font-size: 28rpx;
  color: #333;
  min-height: 80rpx;
  min-width: 120rpx;
  /* 确保内容有足够的宽度 */
}
.medication-item .medication-details .inline-form-item .frequency-input-wrapper .unit-picker .unit-picker-inner .placeholder.data-v-5d191dff,
.medication-item .medication-details .inline-form-item .dosage-input-wrapper .unit-picker .unit-picker-inner .placeholder.data-v-5d191dff {
  color: #999;
}
.medication-item .medication-details .inline-form-item .frequency-picker.data-v-5d191dff {
  width: 100%;
  min-width: 200rpx;
  flex-basis: 220rpx;
  flex-grow: 1;
}
.medication-item .medication-details .inline-form-item .frequency-picker .picker.data-v-5d191dff {
  width: 100%;
  min-width: 200rpx;
}
.medication-item .delete-btn.data-v-5d191dff {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  color: #FF0000;
  font-size: 28rpx;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 5rpx 12rpx;
  border-radius: 6rpx;
  display: none;
  /* 隐藏原删除按钮 */
}

/* 禁用输入框样式 */
.disabled-input.data-v-5d191dff {
  flex: 1;
  background-color: #F5F5F5;
  border-radius: 8rpx;
  padding: 15rpx 20rpx;
  font-size: 28rpx;
  color: #666;
  min-height: 80rpx;
  width: 100%;
  box-sizing: border-box;
}