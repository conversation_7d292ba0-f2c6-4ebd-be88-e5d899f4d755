{"version": 3, "file": "signMd5Utils.js", "sources": ["../../../../src/utils/signMd5Utils.ts"], "sourcesContent": ["import { MD5 } from 'crypto-js'\r\nimport { isString } from './is'\r\n//签名密钥串(前后端要一致，正式发布请自行修改)\r\nconst signatureSecret = 'dd05f1c54d63749eda95f9fa6d49v442a'\r\n\r\nexport default class signMd5Utils {\r\n  /**\r\n   * json参数升序\r\n   * @param jsonObj 发送参数\r\n   */\r\n\r\n  static sortAsc(jsonObj) {\r\n    let arr = new Array()\r\n    let num = 0\r\n    for (let i in jsonObj) {\r\n      arr[num] = i\r\n      num++\r\n    }\r\n    let sortArr = arr.sort()\r\n    let sortObj = {}\r\n    for (let i in sortArr) {\r\n      sortObj[sortArr[i]] = jsonObj[sortArr[i]]\r\n    }\r\n    return sortObj\r\n  }\r\n\r\n  /**\r\n   * @param url 请求的url,应该包含请求参数(url的?后面的参数)\r\n   * @param requestParams 请求参数(POST的JSON参数)\r\n   * @returns {string} 获取签名\r\n   */\r\n  static getSign(url, requestParams,requestBodyParams?) {\r\n    let urlParams = this.parseQueryString(url);\r\n    let jsonObj = this.mergeObject(urlParams, requestParams);\r\n    //update-begin---author:wangshuai---date:2024-04-16---for:【QQYUN-9005】发送短信加签---\r\n    if(requestBodyParams){\r\n      jsonObj = this.mergeObject(jsonObj, requestBodyParams)\r\n    }\r\n    //update-end---author:wangshuai---date:2024-04-16---for:【QQYUN-9005】发送短信加签---\r\n    let requestBody:any = this.sortAsc(jsonObj);\r\n    delete requestBody._t;\r\n    console.log('sign requestBody:', requestBody);\r\n    return MD5(JSON.stringify(requestBody) + signatureSecret).toString().toUpperCase();\r\n  }\r\n  /**\r\n   * @param requestData 请求数据(POST的JSON参数)\r\n   * @returns {string} 获取签名\r\n   */\r\n  static getVSign(data, sign) {\r\n    try {\r\n      let jsonObj = data ? (isString(data) ? JSON.parse(JSON.stringify(data)) : { ...data }) : {}\r\n      jsonObj['sign'] = sign\r\n      let signParamObj = {}\r\n      Object.keys(jsonObj).forEach((key) => {\r\n        let val = jsonObj[key]\r\n        if (val && isString(val)) {\r\n          signParamObj[key] = val\r\n        }\r\n      })\r\n      let requestBody = this.sortAsc(signParamObj)\r\n      return MD5(JSON.stringify(requestBody) + signatureSecret).toString().toUpperCase();\r\n    } catch (e) {\r\n      return ''\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @param url 请求的url\r\n   * @returns {{}} 将url中请求参数组装成json对象(url的?后面的参数)\r\n   */\r\n  static parseQueryString(url) {\r\n    let urlReg = /^[^\\?]+\\?([\\w\\W]+)$/,\r\n      paramReg = /([^&=]+)=([\\w\\W]*?)(&|$|#)/g,\r\n      urlArray = urlReg.exec(url),\r\n      result = {}\r\n\r\n    // 获取URL上最后带逗号的参数变量 sys/dict/getDictItems/sys_user,realname,username\r\n    //【这边条件没有encode】带条件参数例子：/sys/dict/getDictItems/sys_user,realname,id,username!='admin'%20order%20by%20create_time\r\n    let lastpathVariable = url.substring(url.lastIndexOf('/') + 1)\r\n    if (lastpathVariable.includes(',')) {\r\n      if (lastpathVariable.includes('?')) {\r\n        lastpathVariable = lastpathVariable.substring(0, lastpathVariable.indexOf('?'))\r\n      }\r\n      result['x-path-variable'] = decodeURI(lastpathVariable)\r\n    }\r\n    if (urlArray && urlArray[1]) {\r\n      let paramString = urlArray[1],\r\n        paramResult\r\n      while ((paramResult = paramReg.exec(paramString)) != null) {\r\n        //数字值转为string类型，前后端加密规则保持一致\r\n        if (this.myIsNaN(paramResult[2])) {\r\n          paramResult[2] = paramResult[2].toString()\r\n        }\r\n        result[paramResult[1]] = paramResult[2]\r\n      }\r\n    }\r\n    return result\r\n  }\r\n\r\n  /**\r\n   * @returns {*} 将两个对象合并成一个\r\n   */\r\n  static mergeObject(objectOne, objectTwo) {\r\n    if (objectTwo && Object.keys(objectTwo).length > 0) {\r\n      for (let key in objectTwo) {\r\n        if (objectTwo.hasOwnProperty(key) === true) {\r\n          //数字值转为string类型，前后端加密规则保持一致\r\n          if (this.myIsNaN(objectTwo[key])) {\r\n            objectTwo[key] = objectTwo[key].toString()\r\n          }\r\n          objectOne[key] = objectTwo[key]\r\n        }\r\n      }\r\n    }\r\n    return objectOne\r\n  }\r\n\r\n  static urlEncode(param, key, encode) {\r\n    if (param == null) return ''\r\n    let paramStr = ''\r\n    let t = typeof param\r\n    if (t == 'string' || t == 'number' || t == 'boolean') {\r\n      paramStr += '&' + key + '=' + (encode == null || encode ? encodeURIComponent(param) : param)\r\n    } else {\r\n      for (let i in param) {\r\n        let k = key == null ? i : key + (param instanceof Array ? '[' + i + ']' : '.' + i)\r\n        paramStr += this.urlEncode(param[i], k, encode)\r\n      }\r\n    }\r\n    return paramStr\r\n  }\r\n\r\n  /**\r\n   * 接口签名用 生成header中的时间戳\r\n   * @returns {number}\r\n   */\r\n  static getTimestamp() {\r\n    return new Date().getTime()\r\n  }\r\n\r\n  static getDateTimeToString() {\r\n    const date_ = new Date()\r\n    const year:any = date_.getFullYear()\r\n    let month:any = date_.getMonth() + 1\r\n    let day:any = date_.getDate()\r\n    if (month < 10) month = '0' + month\r\n    if (day < 10) day = '0' + day\r\n    let hours:any = date_.getHours()\r\n    let mins:any = date_.getMinutes()\r\n    let secs:any = date_.getSeconds()\r\n    const msecs = date_.getMilliseconds()\r\n    if (hours < 10) hours = '0' + hours\r\n    if (mins < 10) mins = '0' + mins\r\n    if (secs < 10) secs = '0' + secs\r\n    if (msecs < 10) secs = '0' + msecs\r\n    return year + '' + month + '' + day + '' + hours + '' + mins + '' + secs\r\n  }\r\n  // true:数值型的，false：非数值型\r\n  static myIsNaN(value) {\r\n    return typeof value === 'number' && !isNaN(value)\r\n  }\r\n}\r\n"], "names": ["MD5", "isString"], "mappings": ";;;;;;;;;;;;;;;;;;;AAGA,MAAM,kBAAkB;AAExB,MAAqB,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAMhC,OAAO,QAAQ,SAAS;AAClB,QAAA,MAAM,IAAI,MAAM;AACpB,QAAI,MAAM;AACV,aAAS,KAAK,SAAS;AACrB,UAAI,GAAG,IAAI;AACX;AAAA,IAAA;AAEE,QAAA,UAAU,IAAI,KAAK;AACvB,QAAI,UAAU,CAAC;AACf,aAAS,KAAK,SAAS;AACrB,cAAQ,QAAQ,CAAC,CAAC,IAAI,QAAQ,QAAQ,CAAC,CAAC;AAAA,IAAA;AAEnC,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQT,OAAO,QAAQ,KAAK,eAAc,mBAAoB;AAChD,QAAA,YAAY,KAAK,iBAAiB,GAAG;AACzC,QAAI,UAAU,KAAK,YAAY,WAAW,aAAa;AAEvD,QAAG,mBAAkB;AACT,gBAAA,KAAK,YAAY,SAAS,iBAAiB;AAAA,IAAA;AAGnD,QAAA,cAAkB,KAAK,QAAQ,OAAO;AAC1C,WAAO,YAAY;AACX,YAAA,IAAI,qBAAqB,WAAW;AACrC,WAAAA,cAAA,gBAAA,IAAI,KAAK,UAAU,WAAW,IAAI,eAAe,EAAE,SAAS,EAAE,YAAY;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMnF,OAAO,SAAS,MAAM,MAAM;AACtB,QAAA;AACF,UAAI,UAAU,OAAQC,SAAA,SAAS,IAAI,IAAI,KAAK,MAAM,KAAK,UAAU,IAAI,CAAC,IAAI,mBAAK,QAAU,CAAC;AAC1F,cAAQ,MAAM,IAAI;AAClB,UAAI,eAAe,CAAC;AACpB,aAAO,KAAK,OAAO,EAAE,QAAQ,CAAC,QAAQ;AAChC,YAAA,MAAM,QAAQ,GAAG;AACjB,YAAA,OAAOA,kBAAS,GAAG,GAAG;AACxB,uBAAa,GAAG,IAAI;AAAA,QAAA;AAAA,MACtB,CACD;AACG,UAAA,cAAc,KAAK,QAAQ,YAAY;AACpC,aAAAD,cAAA,gBAAA,IAAI,KAAK,UAAU,WAAW,IAAI,eAAe,EAAE,SAAS,EAAE,YAAY;AAAA,aAC1E,GAAG;AACH,aAAA;AAAA,IAAA;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAOF,OAAO,iBAAiB,KAAK;AACvB,QAAA,SAAS,uBACX,WAAW,+BACX,WAAW,OAAO,KAAK,GAAG,GAC1B,SAAS,CAAC;AAIZ,QAAI,mBAAmB,IAAI,UAAU,IAAI,YAAY,GAAG,IAAI,CAAC;AACzD,QAAA,iBAAiB,SAAS,GAAG,GAAG;AAC9B,UAAA,iBAAiB,SAAS,GAAG,GAAG;AAClC,2BAAmB,iBAAiB,UAAU,GAAG,iBAAiB,QAAQ,GAAG,CAAC;AAAA,MAAA;AAEzE,aAAA,iBAAiB,IAAI,UAAU,gBAAgB;AAAA,IAAA;AAEpD,QAAA,YAAY,SAAS,CAAC,GAAG;AACvB,UAAA,cAAc,SAAS,CAAC,GAC1B;AACF,cAAQ,cAAc,SAAS,KAAK,WAAW,MAAM,MAAM;AAEzD,YAAI,KAAK,QAAQ,YAAY,CAAC,CAAC,GAAG;AAChC,sBAAY,CAAC,IAAI,YAAY,CAAC,EAAE,SAAS;AAAA,QAAA;AAE3C,eAAO,YAAY,CAAC,CAAC,IAAI,YAAY,CAAC;AAAA,MAAA;AAAA,IACxC;AAEK,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,OAAO,YAAY,WAAW,WAAW;AACvC,QAAI,aAAa,OAAO,KAAK,SAAS,EAAE,SAAS,GAAG;AAClD,eAAS,OAAO,WAAW;AACzB,YAAI,UAAU,eAAe,GAAG,MAAM,MAAM;AAE1C,cAAI,KAAK,QAAQ,UAAU,GAAG,CAAC,GAAG;AAChC,sBAAU,GAAG,IAAI,UAAU,GAAG,EAAE,SAAS;AAAA,UAAA;AAEjC,oBAAA,GAAG,IAAI,UAAU,GAAG;AAAA,QAAA;AAAA,MAChC;AAAA,IACF;AAEK,WAAA;AAAA,EAAA;AAAA,EAGT,OAAO,UAAU,OAAO,KAAK,QAAQ;AACnC,QAAI,SAAS;AAAa,aAAA;AAC1B,QAAI,WAAW;AACf,QAAI,IAAI,OAAO;AACf,QAAI,KAAK,YAAY,KAAK,YAAY,KAAK,WAAW;AACxC,kBAAA,MAAM,MAAM,OAAO,UAAU,QAAQ,SAAS,mBAAmB,KAAK,IAAI;AAAA,IAAA,OACjF;AACL,eAAS,KAAK,OAAO;AACf,YAAA,IAAI,OAAO,OAAO,IAAI,OAAO,iBAAiB,QAAQ,MAAM,IAAI,MAAM,MAAM;AAChF,oBAAY,KAAK,UAAU,MAAM,CAAC,GAAG,GAAG,MAAM;AAAA,MAAA;AAAA,IAChD;AAEK,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOT,OAAO,eAAe;AACb,YAAA,oBAAI,KAAK,GAAE,QAAQ;AAAA,EAAA;AAAA,EAG5B,OAAO,sBAAsB;AACrB,UAAA,4BAAY,KAAK;AACjB,UAAA,OAAW,MAAM,YAAY;AAC/B,QAAA,QAAY,MAAM,SAAA,IAAa;AAC/B,QAAA,MAAU,MAAM,QAAQ;AAC5B,QAAI,QAAQ;AAAI,cAAQ,MAAM;AAC9B,QAAI,MAAM;AAAI,YAAM,MAAM;AACtB,QAAA,QAAY,MAAM,SAAS;AAC3B,QAAA,OAAW,MAAM,WAAW;AAC5B,QAAA,OAAW,MAAM,WAAW;AAC1B,UAAA,QAAQ,MAAM,gBAAgB;AACpC,QAAI,QAAQ;AAAI,cAAQ,MAAM;AAC9B,QAAI,OAAO;AAAI,aAAO,MAAM;AAC5B,QAAI,OAAO;AAAI,aAAO,MAAM;AAC5B,QAAI,QAAQ;AAAI,aAAO,MAAM;AAC7B,WAAO,OAAO,KAAK,QAAa,MAAW,QAAa,OAAY;AAAA,EAAA;AAAA;AAAA,EAGtE,OAAO,QAAQ,OAAO;AACpB,WAAO,OAAO,UAAU,YAAY,CAAC,MAAM,KAAK;AAAA,EAAA;AAEpD;;"}