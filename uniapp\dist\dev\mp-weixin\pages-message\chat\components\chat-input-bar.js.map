{"version": 3, "file": "chat-input-bar.js", "sources": ["../../../../../../src/pages-message/chat/components/chat-input-bar.vue", "../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvcGFnZXMtbWVzc2FnZS9jaGF0L2NvbXBvbmVudHMvY2hhdC1pbnB1dC1iYXIudnVl"], "sourcesContent": ["<!-- z-paging聊天输入框 -->\r\n\r\n<template>\r\n  <view class=\"chat-input-bar-container\">\r\n    <view class=\"chat-input-bar\">\r\n      <view class=\"add-container\" @click=\"tooglePanl('more')\">\r\n        <view class=\"icon add\"></view>\r\n      </view>\r\n      <view class=\"chat-input-container\">\r\n        <!-- :adjust-position=\"false\"必须设置，防止键盘弹窗自动上顶，交由z-paging内部处理 -->\r\n        <input\r\n          :focus=\"focus\"\r\n          class=\"chat-input\"\r\n          v-model=\"msg\"\r\n          :adjust-position=\"false\"\r\n          confirm-type=\"send\"\r\n          type=\"text\"\r\n          placeholder=\"请输入内容\"\r\n          @confirm=\"sendClick\"\r\n        />\r\n      </view>\r\n      <!-- 表情图标（如果不需要切换表情面板则不用写） -->\r\n      <view class=\"emoji-container\">\r\n        <image class=\"emoji-img\" :src=\"getEmoji\" @click=\"tooglePanl('emoji')\"></image>\r\n      </view>\r\n      <view class=\"chat-input-send\" @click=\"sendClick\">\r\n        <text class=\"chat-input-send-text\">发送</text>\r\n      </view>\r\n    </view>\r\n    <!--  表情面板，这里使用height控制隐藏显示是为了有高度变化的动画效果（如果不需要切换表情面板则不用写） -->\r\n    <view\r\n      class=\"emoji-panel-container\"\r\n      :style=\"[{ height: ['emoji', 'more'].includes(chatBarType) ? '320rpx' : '0px' }]\"\r\n    >\r\n      <scroll-view scroll-y style=\"height: 100%; flex: 1\">\r\n        <template v-if=\"['emoji'].includes(chatBarType)\">\r\n          <!-- 表情 -->\r\n          <view class=\"emoji-panel\">\r\n            <swiper class=\"emoji-swiper zdybq\" :indicator-dots=\"true\" :duration=\"150\">\r\n              <swiper-item class=\"swiperItem\" v-for=\"(page, pid) in emojiArray\" :key=\"pid\">\r\n                <view class=\"item\" v-for=\"(em, eid) in page\" :key=\"eid\" @tap=\"emojiClick(em)\">\r\n                  <image mode=\"scaleToFill\" :src=\"em.url\" style=\"width: 28px; height: 28px\"></image>\r\n                </view>\r\n              </swiper-item>\r\n            </swiper>\r\n          </view>\r\n        </template>\r\n        <template v-if=\"['more'].includes(chatBarType)\">\r\n          <!-- 相册、照相 -->\r\n          <view class=\"more-panel\">\r\n            <view class=\"box\" @tap=\"getImage('album')\"><view class=\"icon tupian2\"></view></view>\r\n            <view class=\"box\" @tap=\"getImage('camera')\"><view class=\"icon paizhao\"></view></view>\r\n          </view>\r\n        </template>\r\n      </scroll-view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { textReplaceEmoji, getEmojiImageUrl } from '../emojis'\r\nimport { computed } from 'vue'\r\n\r\ndefineOptions({\r\n  name: 'chat-input-bar',\r\n  options: {\r\n    // apply-shared‌：当前页面样式会影响到子组件样式.(小程序)\r\n    // shared‌：当前页面样式影响到子组件，子组件样式也会影响到当前页面.(小程序)\r\n    styleIsolation: '‌apply-shared‌',\r\n  },\r\n})\r\nconst emit = defineEmits(['emojiTypeChange', 'send', 'image'])\r\nlet emojiArray = getEmojiImageUrl()\r\nconst msg = ref('')\r\n// 当前input focus\r\nconst focus = ref(false)\r\n// emoji、more\r\nconst chatBarType = ref('')\r\n\r\nconst getEmoji = computed(() => {\r\n  let img\r\n  if (['', 'more'].includes(chatBarType.value)) {\r\n    img = 'emoji'\r\n  } else if (['emoji'].includes(chatBarType.value)) {\r\n    img = 'keyboard'\r\n  }\r\n  return `/static/chat/${img}.png`\r\n})\r\n\r\nconst updateKeyboardHeightChange = (res) => {\r\n  if (res.height > 0) {\r\n    chatBarType.value = ''\r\n  }\r\n}\r\nconst hidedKeyboard = () => {\r\n  if (['emoji', 'more'].includes(chatBarType.value)) {\r\n    chatBarType.value = ''\r\n  }\r\n}\r\n// 点击了切换表情面板/键盘（如果不需要切换表情面板则不用写）\r\nconst tooglePanl = (val) => {\r\n  if (chatBarType.value == val) {\r\n    // 点击了键盘，展示键盘\r\n    focus.value = true\r\n    chatBarType.value = ''\r\n  } else {\r\n    // 点击了切换表情面板\r\n    focus.value = false\r\n    // 隐藏键盘\r\n    uni.hideKeyboard()\r\n    chatBarType.value = val\r\n  }\r\n}\r\n// 点击了某个表情，将其插入输入内容中（如果不需要切换表情面板则不用写）\r\nconst emojiClick = (em) => {\r\n  msg.value += em.alt\r\n}\r\n\r\n// 点击了发送按钮\r\nconst sendClick = () => {\r\n  if (!msg.value.length) return\r\n  emit('send', msg.value)\r\n  msg.value = ''\r\n}\r\n// 点击了发送按钮\r\nconst getImage = (type) => {\r\n\temit('image', type)\r\n}\r\ndefineExpose({\r\n  updateKeyboardHeightChange,\r\n  hidedKeyboard,\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.chat-input-bar {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  border-top: solid 1px #f5f5f5;\r\n  background-color: #f8f8f8;\r\n\r\n  padding: 10rpx 20rpx;\r\n}\r\n.add-container {\r\n  margin-right: 8px;\r\n  .icon {\r\n    font-size: 26px;\r\n  }\r\n}\r\n.chat-input-container {\r\n  flex: 1;\r\n\r\n  display: flex;\r\n\r\n  padding: 15rpx;\r\n  background-color: white;\r\n  border-radius: 10rpx;\r\n}\r\n.chat-input {\r\n  flex: 1;\r\n  font-size: 28rpx;\r\n}\r\n.emoji-container {\r\n  width: 54rpx;\r\n  height: 54rpx;\r\n  margin: 10rpx 0rpx 10rpx 20rpx;\r\n}\r\n.emoji-img {\r\n  width: 54rpx;\r\n  height: 54rpx;\r\n}\r\n.chat-input-send {\r\n  background-color: #007aff;\r\n  margin: 10rpx 10rpx 10rpx 20rpx;\r\n  border-radius: 10rpx;\r\n  width: 110rpx;\r\n  height: 60rpx;\r\n\r\n  display: flex;\r\n\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n.chat-input-send-text {\r\n  color: white;\r\n  font-size: 26rpx;\r\n}\r\n.emoji-panel-container {\r\n  border-top: 1px solid #e8e8e8;\r\n  background-color: #f8f8f8;\r\n  overflow: hidden;\r\n  transition-property: height;\r\n  transition-duration: 0.15s;\r\n}\r\n.emoji-panel {\r\n  height: 100%;\r\n  padding: 0 8vw;\r\n  .swiperItem {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    align-content: flex-start;\r\n    .item {\r\n      width: 12vw;\r\n      height: 12vw;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n    }\r\n  }\r\n}\r\n.more-panel {\r\n  display: flex;\r\n  padding-top: 3vw;\r\n  .box {\r\n    width: 18vw;\r\n    height: 18vw;\r\n    border-radius: 10px;\r\n    background-color: #fff;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    margin: 0 3vw 2vw 3vw;\r\n    .icon {\r\n      font-size: 30px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-message/chat/components/chat-input-bar.vue'\nwx.createComponent(Component)"], "names": ["getEmojiImageUrl", "ref", "computed", "uni"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuEA,UAAM,OAAO;AACb,QAAI,aAAaA,yBAAAA,iBAAiB;AAC5B,UAAA,MAAMC,kBAAI,EAAE;AAEZ,UAAA,QAAQA,kBAAI,KAAK;AAEjB,UAAA,cAAcA,kBAAI,EAAE;AAEpB,UAAA,WAAWC,cAAAA,SAAS,MAAM;AAC1B,UAAA;AACJ,UAAI,CAAC,IAAI,MAAM,EAAE,SAAS,YAAY,KAAK,GAAG;AACtC,cAAA;AAAA,MAAA,WACG,CAAC,OAAO,EAAE,SAAS,YAAY,KAAK,GAAG;AAC1C,cAAA;AAAA,MAAA;AAER,aAAO,gBAAgB,GAAG;AAAA,IAAA,CAC3B;AAEK,UAAA,6BAA6B,CAAC,QAAQ;AACtC,UAAA,IAAI,SAAS,GAAG;AAClB,oBAAY,QAAQ;AAAA,MAAA;AAAA,IAExB;AACA,UAAM,gBAAgB,MAAM;AAC1B,UAAI,CAAC,SAAS,MAAM,EAAE,SAAS,YAAY,KAAK,GAAG;AACjD,oBAAY,QAAQ;AAAA,MAAA;AAAA,IAExB;AAEM,UAAA,aAAa,CAAC,QAAQ;AACtB,UAAA,YAAY,SAAS,KAAK;AAE5B,cAAM,QAAQ;AACd,oBAAY,QAAQ;AAAA,MAAA,OACf;AAEL,cAAM,QAAQ;AAEdC,sBAAAA,MAAI,aAAa;AACjB,oBAAY,QAAQ;AAAA,MAAA;AAAA,IAExB;AAEM,UAAA,aAAa,CAAC,OAAO;AACzB,UAAI,SAAS,GAAG;AAAA,IAClB;AAGA,UAAM,YAAY,MAAM;AAClB,UAAA,CAAC,IAAI,MAAM;AAAQ;AAClB,WAAA,QAAQ,IAAI,KAAK;AACtB,UAAI,QAAQ;AAAA,IACd;AAEM,UAAA,WAAW,CAAC,SAAS;AAC1B,WAAK,SAAS,IAAI;AAAA,IACnB;AACa,aAAA;AAAA,MACX;AAAA,MACA;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClID,GAAG,gBAAgB,SAAS;"}