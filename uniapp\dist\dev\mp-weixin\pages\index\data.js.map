{"version": 3, "file": "data.js", "sources": ["../../../../../src/pages/index/data.vue", "../../../../../uniPage:/cGFnZXMvaW5kZXgvZGF0YS52dWU"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n  <PageLayout>\r\n    <template #navbar>\r\n      <NavBar title=\"更多数据\" />\r\n    </template>\r\n    <scroll-view class=\"page-scroll-view\" scroll-y=\"true\">\r\n      <view class=\"form-container\">\r\n        <view class=\"list-container\">\r\n          <view class=\"filter-section\">\r\n            <view class=\"date-filter\">\r\n              <text class=\"filter-label\">填写时间：</text>\r\n              <picker mode=\"date\" :value=\"startDate\" start=\"1900-01-01\" end=\"2099-12-31\" @change=\"onStartDateChange\">\r\n                <view class=\"date-picker\">\r\n                  <text>{{ startDate || '开始日期' }}</text>\r\n                  <uni-icons type=\"calendar\" size=\"15\" color=\"#666666\" />\r\n                </view>\r\n              </picker>\r\n              <view class=\"date-separator\">至</view>\r\n              <picker mode=\"date\" :value=\"endDate\" start=\"1900-01-01\" end=\"2099-12-31\" @change=\"onEndDateChange\">\r\n                <view class=\"date-picker\">\r\n                  <text>{{ endDate || '结束日期' }}</text>\r\n                  <uni-icons type=\"calendar\" size=\"15\" color=\"#666666\" />\r\n                </view>\r\n              </picker>\r\n              <view class=\"reset-btn\" @click=\"resetDateFilter\">\r\n                <uni-icons type=\"reload\" size=\"18\" color=\"#07C160\" />\r\n              </view>\r\n            </view>\r\n            <view class=\"type-filter\">\r\n              <text class=\"filter-label\">数据类型：</text>\r\n              <picker mode=\"selector\" :value=\"selectedTypeIndex\" :range=\"dataTypes\" range-key=\"label\"\r\n                @change=\"onDataTypeChange\">\r\n                <view class=\"type-picker\">\r\n                  <text>{{ selectedTypeIndex === -1 ? '全部类型' : dataTypes[selectedTypeIndex].label }}</text>\r\n                  <uni-icons type=\"down\" size=\"15\" color=\"#666666\" />\r\n                </view>\r\n              </picker>\r\n            </view>\r\n            <!-- 患者姓名搜索框，仅在医生端和社工端显示 -->\r\n            <view class=\"name-search\" v-if=\"Number(userStore.userInfo.userCategory) !== 1\">\r\n              <text class=\"filter-label\">患者姓名：</text>\r\n              <view class=\"search-input-container\">\r\n                <input type=\"text\" v-model=\"searchName\" class=\"search-input\" placeholder=\"请输入患者姓名\" />\r\n                <uni-icons v-if=\"searchName\" type=\"clear\" size=\"14\" color=\"#666666\" @click=\"searchName = ''\"\r\n                  class=\"clear-icon\" />\r\n              </view>\r\n            </view>\r\n            <view class=\"buttons-row\">\r\n              <view class=\"filter-btn\" @click=\"searchByDate\">\r\n                <uni-icons type=\"search\" size=\"16\" color=\"#FFFFFF\"></uni-icons>\r\n                <text>查询</text>\r\n              </view>\r\n            </view>\r\n          </view> <!-- 列表内容 -->\r\n          <view class=\"list-content\">\r\n            <view class=\"loading-container\" v-if=\"loading\">\r\n              <wd-loading />\r\n            </view>\r\n\r\n            <view class=\"empty-state\" v-else-if=\"recordList.length === 0\">\r\n              <text>暂无记录</text>\r\n            </view>\r\n\r\n            <view v-else class=\"data-items\">\r\n              <view class=\"data-card\" v-for=\"(item, index) in recordList\" :key=\"index\" @click=\"navigateToDetail(item)\">\r\n                <view class=\"data-info\">\r\n                  <view class=\"data-icon\">\r\n                    <image :src=\"item.iconPath\" class=\"card-image\" mode=\"contain\"></image>\r\n                  </view>\r\n                  <view class=\"data-details\">\r\n                    <view class=\"data-title-row\">\r\n                      <text class=\"data-type\">{{ item.type }}</text>\r\n                      <text class=\"medication-count\" v-if=\"item.type === '用药情况' && item.count > 1\">{{ item.count\r\n                      }}条记录</text>\r\n                    </view>\r\n                    <view class=\"data-content\">\r\n                      <view class=\"data-fields\">\r\n                        <view class=\"data-field\" v-if=\"Number(userStore.userInfo.userCategory) !== 1\">\r\n                          <text class=\"field-value\">姓名：{{ item.userName }}</text>\r\n                        </view>\r\n                        <view class=\"data-field\">\r\n                          <text class=\"field-value\">{{ item.createTime }}</text>\r\n                        </view>\r\n                      </view>\r\n                    </view>\r\n                  </view>\r\n                </view>\r\n                <view class=\"data-action\">\r\n                  <text class=\"action-text\">查看详情</text>\r\n                  <uni-icons type=\"right\" size=\"16\" color=\"#07C160\"></uni-icons>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </scroll-view>\r\n  </PageLayout>\r\n\r\n  <!-- 日期选择器弹窗 -->\r\n  <uni-calendar ref=\"calendar\" :insert=\"false\" @confirm=\"dateConfirm\" :range=\"false\" />\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { ref, onMounted } from 'vue'\r\nimport { useUserStore } from '@/store/user'\r\nimport { onShow } from '@dcloudio/uni-app'\r\nimport { http } from '@/utils/http'\r\n\r\ndefineOptions({\r\n  name: 'AllDataList',\r\n})\r\n\r\n// 获取用户信息\r\nconst userStore = useUserStore()\r\n\r\n// 数据列表\r\nconst recordList = ref([])\r\nconst loading = ref(false)\r\n\r\n// 日期筛选\r\nconst startDate = ref('')\r\nconst endDate = ref('')\r\nconst datePickerType = ref('') // 用于标识当前打开的是开始日期还是结束日期选择器\r\nconst calendar = ref(null)\r\n\r\n// 患者姓名搜索\r\nconst searchName = ref('')\r\n\r\n// 数据类型筛选\r\nconst selectedTypeIndex = ref(-1) // -1表示全部类型\r\nconst dataTypes = ref([\r\n  { label: '日常体征监测', value: 'vital-signs', api: '/patient/getdailysigns' },\r\n  { label: '用药情况', value: 'medication', api: '/patient/getmedications' },\r\n  { label: '院外检查报告', value: 'report', api: '/patient/getreport' },\r\n  { label: '监测表', value: 'monitor', api: '/patient/getmonitoring' },\r\n  { label: '心理量表', value: 'psychology', api: '/patient/getpsychologicaltable' },\r\n  { label: '日常生活指数评估', value: 'daily-life', api: '/patient/getdailyassessment' }\r\n])\r\n\r\n// 根据数据类型返回对应的图标\r\nconst getIconByType = (type) => {\r\n  const iconMap = {\r\n    '日常体征监测': 'https://www.mograine.cn/images/vital-signs.png',\r\n    '用药情况': 'https://www.mograine.cn/images/medication.png',\r\n    '院外检查报告': 'https://www.mograine.cn/images/report.png',\r\n    '监测表': 'https://www.mograine.cn/images/monitor.png',\r\n    '心理量表': 'https://www.mograine.cn/images/psychology.png',\r\n    '日常生活指数评估': 'https://www.mograine.cn/images/dailyLife.png',\r\n    '知情同意与登记表': 'https://www.mograine.cn/images/registration.png',\r\n  }\r\n\r\n  return iconMap[type]\r\n}\r\n\r\n// 处理开始日期变化\r\nfunction onStartDateChange(e: any) {\r\n  startDate.value = e.detail.value\r\n}\r\n\r\n// 处理结束日期变化\r\nfunction onEndDateChange(e: any) {\r\n  endDate.value = e.detail.value\r\n}\r\n\r\n// 处理数据类型变化\r\nfunction onDataTypeChange(e: any) {\r\n  selectedTypeIndex.value = e.detail.value\r\n}\r\n\r\n// 日期选择确认\r\nconst dateConfirm = (e) => {\r\n  const selectedDate = e.fulldate\r\n  if (datePickerType.value === 'start') {\r\n    startDate.value = selectedDate\r\n  } else if (datePickerType.value === 'end') {\r\n    endDate.value = selectedDate\r\n  }\r\n}\r\n\r\n// 获取所有数据\r\nconst fetchData = () => {\r\n  loading.value = true\r\n  console.log('开始获取数据...')\r\n\r\n  // 检查是否选择了特定的数据类型\r\n  if (selectedTypeIndex.value !== -1) {\r\n    // 调用特定类型的接口\r\n    fetchSpecificTypeData()\r\n  } else {\r\n    // 调用原有的获取所有数据的接口\r\n    fetchAllData()\r\n  }\r\n}\r\n\r\n// 获取特定类型的数据\r\nconst fetchSpecificTypeData = () => {\r\n  const selectedType = dataTypes.value[selectedTypeIndex.value]\r\n  const apiUrl = selectedType.api\r\n\r\n  console.log(`获取${selectedType.label}数据...`)\r\n\r\n  const params: Record<string, any> = {}\r\n\r\n  // 只有患者端才传递userId参数\r\n  if (Number(userStore.userInfo.userCategory) === 1) {\r\n    params.userId = userStore.userInfo.userid\r\n  }\r\n\r\n  // 添加日期过滤参数\r\n  if (startDate.value) {\r\n    params.beginDate = startDate.value\r\n  }\r\n  if (endDate.value) {\r\n    params.endDate = endDate.value\r\n  }\r\n\r\n  // 添加患者姓名搜索参数（仅适用于医生端和社工端）\r\n  if (Number(userStore.userInfo.userCategory) !== 1 && searchName.value.trim()) {\r\n    params.userName = searchName.value.trim()\r\n  }\r\n\r\n  uni.request({\r\n    url: `${import.meta.env.VITE_SERVER_BASEURL}${apiUrl}`,\r\n    method: 'GET',\r\n    data: params,\r\n    success: (res: any) => {\r\n      const response = res.data\r\n      console.log('接口返回数据:', response)\r\n      if (response && response.code === 200) {        // 从result.records中获取数据列表\r\n        if (response.result && response.result.records) {\r\n          const processedRecords = (response.result.records || []).map(item => {\r\n            const iconPath = getIconByType(selectedType.label)\r\n            // 院外检查报告的时间字段是 creatTime，需要统一为 createTime\r\n            let createTime = item.createTime\r\n            if (selectedType.label === '院外检查报告' && item.creatTime) {\r\n              createTime = item.creatTime\r\n            }\r\n            return {\r\n              ...item,\r\n              type: selectedType.label,\r\n              typeName: selectedType.label,\r\n              iconPath: iconPath,\r\n              createTime: createTime\r\n            }\r\n          })\r\n          recordList.value = processedRecords\r\n          console.log('解析后的列表数据:', recordList.value)\r\n        } else {\r\n          recordList.value = []\r\n          console.log('没有找到records数据')\r\n        }\r\n      } else {\r\n        console.error('获取数据失败', response.msg)\r\n        uni.showToast({\r\n          title: response.msg || '获取数据失败',\r\n          icon: 'none'\r\n        })\r\n        recordList.value = []\r\n      }\r\n    },\r\n    fail: (err: any) => {\r\n      console.error('获取数据异常:', err)\r\n      uni.showToast({\r\n        title: '网络异常，请稍后重试',\r\n        icon: 'none'\r\n      })\r\n      recordList.value = []\r\n    },\r\n    complete: () => {\r\n      loading.value = false\r\n    }\r\n  })\r\n}\r\n\r\n// 获取所有类型的数据（原有逻辑）\r\nconst fetchAllData = () => {\r\n  const params: Record<string, any> = {}\r\n\r\n  // 只有患者端才传递userId参数\r\n  if (Number(userStore.userInfo.userCategory) === 1) {\r\n    params.userId = userStore.userInfo.userid\r\n  }\r\n\r\n  // 添加日期过滤参数\r\n  if (startDate.value) {\r\n    params.beginDate = startDate.value\r\n  }\r\n  if (endDate.value) {\r\n    params.endDate = endDate.value\r\n  }\r\n\r\n  // 添加患者姓名搜索参数（仅适用于医生端和社工端）\r\n  if (Number(userStore.userInfo.userCategory) !== 1 && searchName.value.trim()) {\r\n    params.userName = searchName.value.trim()\r\n  }\r\n\r\n  http.get('/patient/getalldata', params).then((res: any) => {\r\n    if (res.success && res.result) {      // 处理获取到的所有数据，按照后端返回的顺序展示，不进行排序\r\n      const originalRecords = res.result.records || []\r\n      const processedRecords = []\r\n      const medicationTimeMap = new Map()\r\n\r\n      // 按照原始顺序处理每条记录\r\n      originalRecords.forEach(item => {\r\n        // 过滤掉\"知情同意与登记表\"类型的数据\r\n        if (item.type === '知情同意与登记表') {\r\n          return\r\n        }\r\n\r\n        // 根据type获取相应的图标\r\n        const iconPath = getIconByType(item.type)\r\n        const processedItem = {\r\n          ...item,\r\n          typeName: item.type,\r\n          iconPath: iconPath,\r\n        }\r\n\r\n        // 处理用药情况的合并逻辑，但保持原始顺序\r\n        if (item.type === '用药情况') {\r\n          const timeKey = item.createTime // 使用创建时间作为唯一标识\r\n          if (medicationTimeMap.has(timeKey)) {\r\n            // 如果已经存在相同时间的记录，则将ID添加到idList中\r\n            const existingItem = medicationTimeMap.get(timeKey)\r\n            if (!existingItem.idList) existingItem.idList = [existingItem.id]\r\n            existingItem.idList.push(item.id)\r\n            existingItem.count = (existingItem.count || 1) + 1\r\n          } else {\r\n            // 如果不存在，则添加新记录并保存其在数组中的位置\r\n            const medicationItem = { ...processedItem, count: 1 }\r\n            medicationTimeMap.set(timeKey, medicationItem)\r\n            processedRecords.push(medicationItem)\r\n          }\r\n        } else {\r\n          // 非用药情况直接添加\r\n          processedRecords.push(processedItem)\r\n        }\r\n      })\r\n\r\n      recordList.value = processedRecords\r\n      console.log('解析后的列表数据:', recordList.value)\r\n    } else {\r\n      console.error('获取数据失败', res.message)\r\n      uni.showToast({\r\n        title: res.message || '获取数据失败',\r\n        icon: 'none'\r\n      })\r\n      recordList.value = []\r\n    }\r\n  }).catch(err => {\r\n    console.error('获取数据异常:', err)\r\n    uni.showToast({\r\n      title: '网络异常，请稍后重试',\r\n      icon: 'none'\r\n    })\r\n    recordList.value = []\r\n  }).finally(() => {\r\n    loading.value = false\r\n  })\r\n}\r\n\r\n// 按日期查询\r\nconst searchByDate = () => {\r\n  fetchData()\r\n}\r\n\r\n// 重置日期过滤器\r\nconst resetDateFilter = () => {\r\n  startDate.value = ''\r\n  endDate.value = ''\r\n  searchName.value = '' // 清空姓名搜索\r\n  selectedTypeIndex.value = -1 // 重置数据类型选择\r\n  fetchData() // 清空日期条件并重新获取数据\r\n}\r\n\r\n// 跳转到详情页面\r\nconst navigateToDetail = (item) => {\r\n  // 根据数据类型跳转到不同的表单页面\r\n  let targetUrl = '';\r\n  let urlParams = '';\r\n\r\n  console.log('查看详情，数据类型:', item.type);\r\n\r\n  switch (item.type) {\r\n    case '日常体征监测':\r\n      targetUrl = '/pages-data/vital-signs/form';\r\n      // 常规数据处理：先保存ID到本地存储\r\n      uni.setStorageSync('detail_query', { id: item.id });\r\n      urlParams = `id=${item.id}&mode=view`;\r\n      break;\r\n    case '用药情况':\r\n      targetUrl = '/pages-data/medication/form';\r\n      // 用药情况需要传递idList参数\r\n      urlParams = `idList=${item.idList.join(',')}&mode=view`;\r\n      break;\r\n    case '院外检查报告':\r\n      targetUrl = '/pages-data/examinationReport/form';\r\n      // 院外检查报告需要传递idList参数\r\n      urlParams = `idList=${item.idList.join(',')}&mode=view`;\r\n      break;\r\n    case '监测表':\r\n      targetUrl = '/pages-data/monitor/form';\r\n      // 常规数据处理：先保存ID到本地存储\r\n      uni.setStorageSync('detail_query', { id: item.id });\r\n      urlParams = `id=${item.id}&mode=view`;\r\n      break;\r\n    case '心理量表':\r\n      targetUrl = '/pages-data/psychology/form';\r\n      // 常规数据处理：先保存ID到本地存储\r\n      uni.setStorageSync('detail_query', { id: item.id });\r\n      urlParams = `id=${item.id}&mode=view`;\r\n      break;\r\n    case '日常生活指数评估':\r\n      targetUrl = '/pages-data/dailyLife/form';\r\n      // 常规数据处理：先保存ID到本地存储\r\n      uni.setStorageSync('detail_query', { id: item.id });\r\n      urlParams = `id=${item.id}&mode=view`;\r\n      break;\r\n    // case '知情同意与登记表':\r\n    //   targetUrl = '/pages-data/registration/form';\r\n    //   // 常规数据处理：先保存ID到本地存储\r\n    //   uni.setStorageSync('detail_query', { id: item.id });\r\n    //   urlParams = `id=${item.id}&mode=view`;\r\n    //   break;\r\n    default:\r\n      targetUrl = '';\r\n      urlParams = '';\r\n  }\r\n\r\n  console.log('跳转到页面:', targetUrl);\r\n\r\n  // 跳转到对应页面\r\n  if (targetUrl) {\r\n    uni.navigateTo({\r\n      url: `${targetUrl}?${urlParams}`\r\n    })\r\n  }\r\n}\r\n\r\n// 页面加载时获取数据\r\nonMounted(() => {\r\n  console.log('页面加载中，准备获取数据...')\r\n  fetchData()\r\n})\r\n\r\n// 每次页面显示时获取数据，确保从其他页面返回时数据会刷新\r\nonShow(() => {\r\n  console.log('页面显示（从其他页面返回），刷新数据...')\r\n  fetchData()\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-scroll-view {\r\n  height: calc(100vh - 44px);\r\n  width: 100%;\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.form-container {\r\n  padding: 20rpx;\r\n  padding-bottom: 120rpx;\r\n}\r\n\r\n.form-header {\r\n  margin-bottom: 20rpx;\r\n  text-align: center;\r\n\r\n  .form-title {\r\n    font-size: 36rpx;\r\n    font-weight: bold;\r\n    color: #333;\r\n  }\r\n}\r\n\r\n.list-container {\r\n  .filter-section {\r\n    background-color: #FFFFFF;\r\n    border-radius: 16rpx;\r\n    padding: 24rpx;\r\n    margin-bottom: 20rpx;\r\n    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\r\n    .date-filter {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: 20rpx;\r\n      flex-wrap: wrap;\r\n      /* 允许在窄屏设备上换行 */\r\n      gap: 10rpx 0;\r\n      /* 设置行间距，以防换行时元素太挤 */\r\n\r\n      .filter-label {\r\n        color: #333333;\r\n        font-size: 28rpx;\r\n        margin-right: 10rpx;\r\n        font-weight: 500;\r\n      }\r\n\r\n      .date-picker {\r\n        flex: 2;\r\n        min-width: 210rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        background-color: #F7F7F7;\r\n        padding: 12rpx 20rpx;\r\n        border-radius: 8rpx;\r\n\r\n        text {\r\n          color: #666666;\r\n          font-size: 26rpx;\r\n          white-space: nowrap;\r\n          overflow: hidden;\r\n          text-overflow: ellipsis;\r\n        }\r\n      }\r\n\r\n      .date-separator {\r\n        margin: 0 15rpx;\r\n        color: #666666;\r\n        font-size: 28rpx;\r\n      }\r\n\r\n      .reset-btn {\r\n        margin-left: 3rpx;\r\n        width: 35rpx;\r\n        height: 35rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        background-color: rgba(7, 193, 96, 0.1);\r\n        border-radius: 50%;\r\n        transition: all 0.2s;\r\n\r\n        &:active {\r\n          transform: scale(0.9) rotate(180deg);\r\n        }\r\n      }\r\n    }\r\n\r\n    .name-search {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: 20rpx;\r\n\r\n      .filter-label {\r\n        color: #333333;\r\n        font-size: 28rpx;\r\n        margin-right: 10rpx;\r\n        font-weight: 500;\r\n      }\r\n\r\n      .search-input-container {\r\n        min-width: 475rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        position: relative;\r\n        background-color: #F7F7F7;\r\n        padding: 12rpx 20rpx;\r\n        border-radius: 8rpx;\r\n\r\n        .search-input {\r\n          flex: 1;\r\n          font-size: 26rpx;\r\n          color: #666;\r\n          height: 40rpx;\r\n          padding-right: 40rpx;\r\n        }\r\n\r\n        .clear-icon {\r\n          position: absolute;\r\n          right: 20rpx;\r\n        }\r\n      }\r\n    }\r\n\r\n    .type-filter {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: 20rpx;\r\n\r\n      .filter-label {\r\n        color: #333333;\r\n        font-size: 28rpx;\r\n        margin-right: 10rpx;\r\n        font-weight: 500;\r\n      }\r\n\r\n      .type-picker {\r\n        flex: 1;\r\n        min-width: 475rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        background-color: #F7F7F7;\r\n        padding: 12rpx 20rpx;\r\n        border-radius: 8rpx;\r\n\r\n        text {\r\n          color: #666666;\r\n          font-size: 26rpx;\r\n          white-space: nowrap;\r\n          overflow: hidden;\r\n          text-overflow: ellipsis;\r\n        }\r\n      }\r\n    }\r\n\r\n    .buttons-row {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      gap: 20rpx;\r\n\r\n      .filter-btn {\r\n        flex: 1;\r\n        background-color: #07C160;\r\n        color: #FFFFFF;\r\n        text-align: center;\r\n        padding: 15rpx 0;\r\n        border-radius: 8rpx;\r\n        font-size: 28rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        box-shadow: 0 4rpx 8rpx rgba(7, 193, 96, 0.2);\r\n        transition: all 0.3s;\r\n\r\n        &:active {\r\n          transform: scale(0.98);\r\n          box-shadow: 0 2rpx 4rpx rgba(7, 193, 96, 0.2);\r\n        }\r\n\r\n        text {\r\n          margin-left: 10rpx;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .list-content {\r\n    background-color: #F5F7FA;\r\n    border-radius: 12rpx;\r\n    padding: 10rpx;\r\n    position: relative;\r\n    min-height: 200rpx;\r\n\r\n    .loading-container {\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      width: 100%;\r\n      height: 100%;\r\n      z-index: 10;\r\n    }\r\n\r\n    .empty-state {\r\n      padding: 60rpx 0;\r\n      text-align: center;\r\n      color: #999999;\r\n      font-size: 28rpx;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n    }\r\n\r\n    .data-items {\r\n      .data-card {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        padding: 24rpx;\r\n        background-color: white;\r\n        border-radius: 16rpx;\r\n        margin-bottom: 16rpx;\r\n        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\r\n        transition: all 0.3s;\r\n\r\n        &:active {\r\n          transform: scale(0.99);\r\n          background-color: #fafafa;\r\n        }\r\n\r\n        .data-info {\r\n          display: flex;\r\n          align-items: flex-start;\r\n          flex: 1;\r\n\r\n          .data-icon {\r\n            margin-right: 16rpx;\r\n            background-color: rgba(7, 193, 96, 0.1);\r\n            width: 80rpx;\r\n            height: 80rpx;\r\n            border-radius: 50%;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            flex-shrink: 0;\r\n          }\r\n\r\n          .card-image {\r\n            width: 50rpx;\r\n            height: 50rpx;\r\n            display: block;\r\n          }\r\n\r\n          .data-details {\r\n            flex: 1;\r\n            overflow: hidden;\r\n\r\n            .data-title-row {\r\n              display: flex;\r\n              justify-content: space-between;\r\n              margin-bottom: 8rpx;\r\n\r\n              .data-type {\r\n                font-size: 30rpx;\r\n                color: #333333;\r\n                font-weight: 500;\r\n              }\r\n\r\n              .medication-count {\r\n                font-size: 24rpx;\r\n                color: #07C160;\r\n                background-color: rgba(7, 193, 96, 0.1);\r\n                padding: 4rpx 12rpx;\r\n                border-radius: 20rpx;\r\n                margin-left: 10rpx;\r\n              }\r\n            }\r\n\r\n            .data-content {\r\n              .data-fields {\r\n                display: flex;\r\n                flex-direction: column;\r\n\r\n                .data-field {\r\n                  margin-top: 4rpx;\r\n\r\n                  .field-value {\r\n                    color: #666666;\r\n                    font-size: 24rpx;\r\n                    display: block;\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        .data-action {\r\n          display: flex;\r\n          align-items: center;\r\n          margin-left: 20rpx;\r\n\r\n          .action-text {\r\n            color: #07C160;\r\n            font-size: 26rpx;\r\n            margin-right: 10rpx;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages/index/data.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "ref", "uni", "http", "onMounted", "onShow"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsHA,UAAM,YAAYA,WAAAA,aAAa;AAGzB,UAAA,aAAaC,cAAI,IAAA,EAAE;AACnB,UAAA,UAAUA,kBAAI,KAAK;AAGnB,UAAA,YAAYA,kBAAI,EAAE;AAClB,UAAA,UAAUA,kBAAI,EAAE;AAChB,UAAA,iBAAiBA,kBAAI,EAAE;AACvB,UAAA,WAAWA,kBAAI,IAAI;AAGnB,UAAA,aAAaA,kBAAI,EAAE;AAGnB,UAAA,oBAAoBA,kBAAI,EAAE;AAChC,UAAM,YAAYA,cAAAA,IAAI;AAAA,MACpB,EAAE,OAAO,UAAU,OAAO,eAAe,KAAK,yBAAyB;AAAA,MACvE,EAAE,OAAO,QAAQ,OAAO,cAAc,KAAK,0BAA0B;AAAA,MACrE,EAAE,OAAO,UAAU,OAAO,UAAU,KAAK,qBAAqB;AAAA,MAC9D,EAAE,OAAO,OAAO,OAAO,WAAW,KAAK,yBAAyB;AAAA,MAChE,EAAE,OAAO,QAAQ,OAAO,cAAc,KAAK,iCAAiC;AAAA,MAC5E,EAAE,OAAO,YAAY,OAAO,cAAc,KAAK,8BAA8B;AAAA,IAAA,CAC9E;AAGK,UAAA,gBAAgB,CAAC,SAAS;AAC9B,YAAM,UAAU;AAAA,QACd,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,YAAY;AAAA,MACd;AAEA,aAAO,QAAQ,IAAI;AAAA,IACrB;AAGA,aAAS,kBAAkB,GAAQ;AACvB,gBAAA,QAAQ,EAAE,OAAO;AAAA,IAAA;AAI7B,aAAS,gBAAgB,GAAQ;AACvB,cAAA,QAAQ,EAAE,OAAO;AAAA,IAAA;AAI3B,aAAS,iBAAiB,GAAQ;AACd,wBAAA,QAAQ,EAAE,OAAO;AAAA,IAAA;AAI/B,UAAA,cAAc,CAAC,MAAM;AACzB,YAAM,eAAe,EAAE;AACnB,UAAA,eAAe,UAAU,SAAS;AACpC,kBAAU,QAAQ;AAAA,MAAA,WACT,eAAe,UAAU,OAAO;AACzC,gBAAQ,QAAQ;AAAA,MAAA;AAAA,IAEpB;AAGA,UAAM,YAAY,MAAM;AACtB,cAAQ,QAAQ;AAChB,cAAQ,IAAI,WAAW;AAGnB,UAAA,kBAAkB,UAAU,IAAI;AAEZ,8BAAA;AAAA,MAAA,OACjB;AAEQ,qBAAA;AAAA,MAAA;AAAA,IAEjB;AAGA,UAAM,wBAAwB,MAAM;AAClC,YAAM,eAAe,UAAU,MAAM,kBAAkB,KAAK;AAC5D,YAAM,SAAS,aAAa;AAE5B,cAAQ,IAAI,KAAK,aAAa,KAAK,OAAO;AAE1C,YAAM,SAA8B,CAAC;AAGrC,UAAI,OAAO,UAAU,SAAS,YAAY,MAAM,GAAG;AAC1C,eAAA,SAAS,UAAU,SAAS;AAAA,MAAA;AAIrC,UAAI,UAAU,OAAO;AACnB,eAAO,YAAY,UAAU;AAAA,MAAA;AAE/B,UAAI,QAAQ,OAAO;AACjB,eAAO,UAAU,QAAQ;AAAA,MAAA;AAIvB,UAAA,OAAO,UAAU,SAAS,YAAY,MAAM,KAAK,WAAW,MAAM,QAAQ;AACrE,eAAA,WAAW,WAAW,MAAM,KAAK;AAAA,MAAA;AAG1CC,oBAAAA,MAAI,QAAQ;AAAA,QACV,KAAK,GAAG,6BAAmC,GAAG,MAAM;AAAA,QACpD,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,SAAS,CAAC,QAAa;AACrB,gBAAM,WAAW,IAAI;AACb,kBAAA,IAAI,WAAW,QAAQ;AAC3B,cAAA,YAAY,SAAS,SAAS,KAAK;AACrC,gBAAI,SAAS,UAAU,SAAS,OAAO,SAAS;AACxC,oBAAA,oBAAoB,SAAS,OAAO,WAAW,IAAI,IAAI,CAAQ,SAAA;AAC7D,sBAAA,WAAW,cAAc,aAAa,KAAK;AAEjD,oBAAI,aAAa,KAAK;AACtB,oBAAI,aAAa,UAAU,YAAY,KAAK,WAAW;AACrD,+BAAa,KAAK;AAAA,gBAAA;AAEb,uBAAA,iCACF,OADE;AAAA,kBAEL,MAAM,aAAa;AAAA,kBACnB,UAAU,aAAa;AAAA,kBACvB;AAAA,kBACA;AAAA,gBACF;AAAA,cAAA,CACD;AACD,yBAAW,QAAQ;AACX,sBAAA,IAAI,aAAa,WAAW,KAAK;AAAA,YAAA,OACpC;AACL,yBAAW,QAAQ,CAAC;AACpB,sBAAQ,IAAI,eAAe;AAAA,YAAA;AAAA,UAC7B,OACK;AACG,oBAAA,MAAM,UAAU,SAAS,GAAG;AACpCA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO,SAAS,OAAO;AAAA,cACvB,MAAM;AAAA,YAAA,CACP;AACD,uBAAW,QAAQ,CAAC;AAAA,UAAA;AAAA,QAExB;AAAA,QACA,MAAM,CAAC,QAAa;AACV,kBAAA,MAAM,WAAW,GAAG;AAC5BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACP;AACD,qBAAW,QAAQ,CAAC;AAAA,QACtB;AAAA,QACA,UAAU,MAAM;AACd,kBAAQ,QAAQ;AAAA,QAAA;AAAA,MAClB,CACD;AAAA,IACH;AAGA,UAAM,eAAe,MAAM;AACzB,YAAM,SAA8B,CAAC;AAGrC,UAAI,OAAO,UAAU,SAAS,YAAY,MAAM,GAAG;AAC1C,eAAA,SAAS,UAAU,SAAS;AAAA,MAAA;AAIrC,UAAI,UAAU,OAAO;AACnB,eAAO,YAAY,UAAU;AAAA,MAAA;AAE/B,UAAI,QAAQ,OAAO;AACjB,eAAO,UAAU,QAAQ;AAAA,MAAA;AAIvB,UAAA,OAAO,UAAU,SAAS,YAAY,MAAM,KAAK,WAAW,MAAM,QAAQ;AACrE,eAAA,WAAW,WAAW,MAAM,KAAK;AAAA,MAAA;AAG1CC,iBAAA,KAAK,IAAI,uBAAuB,MAAM,EAAE,KAAK,CAAC,QAAa;AACrD,YAAA,IAAI,WAAW,IAAI,QAAQ;AAC7B,gBAAM,kBAAkB,IAAI,OAAO,WAAW,CAAC;AAC/C,gBAAM,mBAAmB,CAAC;AACpB,gBAAA,wCAAwB,IAAI;AAGlB,0BAAA,QAAQ,CAAQ,SAAA;AAE1B,gBAAA,KAAK,SAAS,YAAY;AAC5B;AAAA,YAAA;AAII,kBAAA,WAAW,cAAc,KAAK,IAAI;AACxC,kBAAM,gBAAgB,iCACjB,OADiB;AAAA,cAEpB,UAAU,KAAK;AAAA,cACf;AAAA,YACF;AAGI,gBAAA,KAAK,SAAS,QAAQ;AACxB,oBAAM,UAAU,KAAK;AACjB,kBAAA,kBAAkB,IAAI,OAAO,GAAG;AAE5B,sBAAA,eAAe,kBAAkB,IAAI,OAAO;AAClD,oBAAI,CAAC,aAAa;AAAqB,+BAAA,SAAS,CAAC,aAAa,EAAE;AACnD,6BAAA,OAAO,KAAK,KAAK,EAAE;AACnB,6BAAA,SAAS,aAAa,SAAS,KAAK;AAAA,cAAA,OAC5C;AAEL,sBAAM,iBAAiB,iCAAK,gBAAL,EAAoB,OAAO,EAAE;AAClC,kCAAA,IAAI,SAAS,cAAc;AAC7C,iCAAiB,KAAK,cAAc;AAAA,cAAA;AAAA,YACtC,OACK;AAEL,+BAAiB,KAAK,aAAa;AAAA,YAAA;AAAA,UACrC,CACD;AAED,qBAAW,QAAQ;AACX,kBAAA,IAAI,aAAa,WAAW,KAAK;AAAA,QAAA,OACpC;AACG,kBAAA,MAAM,UAAU,IAAI,OAAO;AACnCD,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,IAAI,WAAW;AAAA,YACtB,MAAM;AAAA,UAAA,CACP;AACD,qBAAW,QAAQ,CAAC;AAAA,QAAA;AAAA,MACtB,CACD,EAAE,MAAM,CAAO,QAAA;AACN,gBAAA,MAAM,WAAW,GAAG;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD,mBAAW,QAAQ,CAAC;AAAA,MAAA,CACrB,EAAE,QAAQ,MAAM;AACf,gBAAQ,QAAQ;AAAA,MAAA,CACjB;AAAA,IACH;AAGA,UAAM,eAAe,MAAM;AACf,gBAAA;AAAA,IACZ;AAGA,UAAM,kBAAkB,MAAM;AAC5B,gBAAU,QAAQ;AAClB,cAAQ,QAAQ;AAChB,iBAAW,QAAQ;AACnB,wBAAkB,QAAQ;AAChB,gBAAA;AAAA,IACZ;AAGM,UAAA,mBAAmB,CAAC,SAAS;AAEjC,UAAI,YAAY;AAChB,UAAI,YAAY;AAER,cAAA,IAAI,cAAc,KAAK,IAAI;AAEnC,cAAQ,KAAK,MAAM;AAAA,QACjB,KAAK;AACS,sBAAA;AAEZA,wBAAA,MAAI,eAAe,gBAAgB,EAAE,IAAI,KAAK,IAAI;AACtC,sBAAA,MAAM,KAAK,EAAE;AACzB;AAAA,QACF,KAAK;AACS,sBAAA;AAEZ,sBAAY,UAAU,KAAK,OAAO,KAAK,GAAG,CAAC;AAC3C;AAAA,QACF,KAAK;AACS,sBAAA;AAEZ,sBAAY,UAAU,KAAK,OAAO,KAAK,GAAG,CAAC;AAC3C;AAAA,QACF,KAAK;AACS,sBAAA;AAEZA,wBAAA,MAAI,eAAe,gBAAgB,EAAE,IAAI,KAAK,IAAI;AACtC,sBAAA,MAAM,KAAK,EAAE;AACzB;AAAA,QACF,KAAK;AACS,sBAAA;AAEZA,wBAAA,MAAI,eAAe,gBAAgB,EAAE,IAAI,KAAK,IAAI;AACtC,sBAAA,MAAM,KAAK,EAAE;AACzB;AAAA,QACF,KAAK;AACS,sBAAA;AAEZA,wBAAA,MAAI,eAAe,gBAAgB,EAAE,IAAI,KAAK,IAAI;AACtC,sBAAA,MAAM,KAAK,EAAE;AACzB;AAAA,QAOF;AACc,sBAAA;AACA,sBAAA;AAAA,MAAA;AAGR,cAAA,IAAI,UAAU,SAAS;AAG/B,UAAI,WAAW;AACbA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,GAAG,SAAS,IAAI,SAAS;AAAA,QAAA,CAC/B;AAAA,MAAA;AAAA,IAEL;AAGAE,kBAAAA,UAAU,MAAM;AACd,cAAQ,IAAI,iBAAiB;AACnB,gBAAA;AAAA,IAAA,CACX;AAGDC,kBAAAA,OAAO,MAAM;AACX,cAAQ,IAAI,uBAAuB;AACzB,gBAAA;AAAA,IAAA,CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpcD,GAAG,WAAW,eAAe;"}