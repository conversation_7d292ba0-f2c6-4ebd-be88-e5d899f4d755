{"version": 3, "file": "wd-loading.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-loading/wd-loading.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1sb2FkaW5nL3dkLWxvYWRpbmcudnVl"], "sourcesContent": ["<template>\n  <view :class=\"`wd-loading ${props.customClass}`\" :style=\"rootStyle\">\n    <view class=\"wd-loading__body\">\n      <view class=\"wd-loading__svg\" :style=\"`background-image: url(${svg});`\"></view>\n    </view>\n  </view>\n</template>\n<script lang=\"ts\">\nexport default {\n  name: 'wd-loading',\n  options: {\n    virtualHost: true,\n    addGlobalClass: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport { computed, onBeforeMount, ref, watch, type CSSProperties } from 'vue'\nimport base64 from '../common/base64'\nimport { gradient, context, objToStyle, addUnit, isDef } from '../common/util'\nimport { loadingProps } from './types'\n\nconst svgDefineId = context.id++\nconst svgDefineId1 = context.id++\nconst svgDefineId2 = context.id++\n\nconst icon = {\n  outline(color = '#4D80F0') {\n    return `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 42 42\"><defs><linearGradient x1=\"100%\" y1=\"0%\" x2=\"0%\" y2=\"0%\" id=\"${svgDefineId}\"><stop stop-color=\"#FFF\" offset=\"0%\" stop-opacity=\"0\"/><stop stop-color=\"#FFF\" offset=\"100%\"/></linearGradient></defs><g fill=\"none\" fill-rule=\"evenodd\"><path d=\"M21 1c11.046 0 20 8.954 20 20s-8.954 20-20 20S1 32.046 1 21 9.954 1 21 1zm0 7C13.82 8 8 13.82 8 21s5.82 13 13 13 13-5.82 13-13S28.18 8 21 8z\" fill=\"${color}\"/><path d=\"M4.599 21c0 9.044 7.332 16.376 16.376 16.376 9.045 0 16.376-7.332 16.376-16.376\" stroke=\"url(#${svgDefineId}) \" stroke-width=\"3.5\" stroke-linecap=\"round\"/></g></svg>`\n  },\n  ring(color = '#4D80F0', intermediateColor = '#a6bff7') {\n    return `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 200 200\"><linearGradient id=\"${svgDefineId1}\" gradientUnits=\"userSpaceOnUse\" x1=\"50\" x2=\"50\" y2=\"180\"><stop offset=\"0\" stop-color=\"${color}\"></stop> <stop offset=\"1\" stop-color=\"${intermediateColor}\"></stop></linearGradient> <path fill=\"url(#${svgDefineId1})\" d=\"M20 100c0-44.1 35.9-80 80-80V0C44.8 0 0 44.8 0 100s44.8 100 100 100v-20c-44.1 0-80-35.9-80-80z\"></path> <linearGradient id=\"${svgDefineId2}\" gradientUnits=\"userSpaceOnUse\" x1=\"150\" y1=\"20\" x2=\"150\" y2=\"180\"><stop offset=\"0\" stop-color=\"#fff\" stop-opacity=\"0\"></stop> <stop offset=\"1\" stop-color=\"${intermediateColor}\"></stop></linearGradient> <path fill=\"url(#${svgDefineId2})\" d=\"M100 0v20c44.1 0 80 35.9 80 80s-35.9 80-80 80v20c55.2 0 100-44.8 100-100S155.2 0 100 0z\"></path> <circle cx=\"100\" cy=\"10\" r=\"10\" fill=\"${color}\"></circle></svg>`\n  }\n}\n\nconst props = defineProps(loadingProps)\n\nconst svg = ref<string>('')\nconst intermediateColor = ref<string>('')\nconst iconSize = ref<string | number | null>(null)\n\nwatch(\n  () => props.size,\n  (newVal) => {\n    iconSize.value = addUnit(newVal)\n  },\n  {\n    deep: true,\n    immediate: true\n  }\n)\n\nwatch(\n  () => props.type,\n  () => {\n    buildSvg()\n  },\n  {\n    deep: true,\n    immediate: true\n  }\n)\n\nconst rootStyle = computed(() => {\n  const style: CSSProperties = {}\n  if (isDef(iconSize.value)) {\n    style.height = addUnit(iconSize.value)\n    style.width = addUnit(iconSize.value)\n  }\n  return `${objToStyle(style)} ${props.customStyle}`\n})\n\nonBeforeMount(() => {\n  intermediateColor.value = gradient(props.color, '#ffffff', 2)[1]\n  buildSvg()\n})\n\nfunction buildSvg() {\n  const { type, color } = props\n  let ringType: 'outline' | 'ring' = isDef(type) ? type : 'ring'\n  const svgStr = `\"data:image/svg+xml;base64,${base64(ringType === 'ring' ? icon[ringType](color, intermediateColor.value) : icon[ringType](color))}\"`\n  svg.value = svgStr\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-loading/wd-loading.vue'\nwx.createComponent(Component)"], "names": ["context", "intermediateColor", "ref", "watch", "addUnit", "computed", "isDef", "objToStyle", "onBeforeMount", "gradient", "base64"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAQA,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,EAAA;AAEpB;;;;AASA,UAAM,cAAcA,cAAQ,QAAA;AAC5B,UAAM,eAAeA,cAAQ,QAAA;AAC7B,UAAM,eAAeA,cAAQ,QAAA;AAE7B,UAAM,OAAO;AAAA,MACX,QAAQ,QAAQ,WAAW;AACzB,eAAO,2HAA2H,WAAW,0TAA0T,KAAK,6GAA6G,WAAW;AAAA,MACtkB;AAAA,MACA,KAAK,QAAQ,WAAWC,qBAAoB,WAAW;AACrD,eAAO,qFAAqF,YAAY,0FAA0F,KAAK,0CAA0CA,kBAAiB,+CAA+C,YAAY,qIAAqI,YAAY,gKAAgKA,kBAAiB,+CAA+C,YAAY,gJAAgJ,KAAK;AAAA,MAAA;AAAA,IAEn1B;AAEA,UAAM,QAAQ;AAER,UAAA,MAAMC,kBAAY,EAAE;AACpB,UAAA,oBAAoBA,kBAAY,EAAE;AAClC,UAAA,WAAWA,kBAA4B,IAAI;AAEjDC,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,WAAW;AACD,iBAAA,QAAQC,sBAAQ,MAAM;AAAA,MACjC;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,WAAW;AAAA,MAAA;AAAA,IAEf;AAEAD,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,MAAM;AACK,iBAAA;AAAA,MACX;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,WAAW;AAAA,MAAA;AAAA,IAEf;AAEM,UAAA,YAAYE,cAAAA,SAAS,MAAM;AAC/B,YAAM,QAAuB,CAAC;AAC1B,UAAAC,cAAA,MAAM,SAAS,KAAK,GAAG;AACnB,cAAA,SAASF,sBAAQ,SAAS,KAAK;AAC/B,cAAA,QAAQA,sBAAQ,SAAS,KAAK;AAAA,MAAA;AAEtC,aAAO,GAAGG,cAAAA,WAAW,KAAK,CAAC,IAAI,MAAM,WAAW;AAAA,IAAA,CACjD;AAEDC,kBAAAA,cAAc,MAAM;AAClB,wBAAkB,QAAQC,cAAAA,SAAS,MAAM,OAAO,WAAW,CAAC,EAAE,CAAC;AACtD,eAAA;AAAA,IAAA,CACV;AAED,aAAS,WAAW;AACZ,YAAA,EAAE,MAAM,MAAA,IAAU;AACxB,UAAI,WAA+BH,cAAA,MAAM,IAAI,IAAI,OAAO;AACxD,YAAM,SAAS,8BAA8BI,cAAAA,OAAO,aAAa,SAAS,KAAK,QAAQ,EAAE,OAAO,kBAAkB,KAAK,IAAI,KAAK,QAAQ,EAAE,KAAK,CAAC,CAAC;AACjJ,UAAI,QAAQ;AAAA,IAAA;;;;;;;;;;;AClFd,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}