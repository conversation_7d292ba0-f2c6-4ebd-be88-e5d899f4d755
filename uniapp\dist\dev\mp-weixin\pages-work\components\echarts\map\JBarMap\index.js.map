{"version": 3, "file": "index.js", "sources": ["../../../../../../../../src/pages-work/components/echarts/map/JBarMap/index.vue", "../../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvcGFnZXMtd29yay9jb21wb25lbnRzL2VjaGFydHMvbWFwL0pCYXJNYXAvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"content\">\r\n    <statusTip v-if=\"pageTips.show\" :status=\"pageTips.status\"></statusTip>\r\n    <!-- #ifdef APP-PLUS || H5 -->\r\n<!--    <EchartsMap v-else v-model:option=\"option\" v-model:map=\"mapObject\" v-model:echartId=\"echartId\" />-->\r\n    <!-- #endif -->\r\n    <!-- #ifdef APP-PLUS || H5 || MP-WEIXIN -->\r\n    <echartsUniapp v-else :option=\"option\" :mapName=\"mapName\" :mapData=\"mapDataJson\"></echartsUniapp>\r\n    <!-- #endif -->\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted } from 'vue';\r\nimport { echartProps } from '@/pages-work/components/echarts/props'\r\nimport EchartsMap from \"../index.vue\";\r\nimport echartsUniapp from \"../../index.vue\";\r\nimport {deepMerge, handleTotalAndUnit, disposeGridLayout, getGeoCoordMap} from '../../../common/echartUtil'\r\nimport useChartHook from '@/pages-work/components/hooks/useEchartMap'\r\nimport {merge} from \"lodash-es\";\r\n// 定义 props\r\nconst props = defineProps({\r\n  ...echartProps\r\n});\r\n// 定义响应式数据\r\nconst option = ref({});\r\nconst chartOption = ref({\r\n  geo: {\r\n    map: '',\r\n    itemStyle: {},\r\n  },\r\n  tooltip: {\r\n    textStyle: {\r\n      color: \"#fff\"\r\n    },\r\n    padding: 5,\r\n    formatter: null\r\n  }\r\n});\r\nlet [{ dataSource, reload, pageTips, config,mapDataJson,mapName,getAreaCode,city_point },\r\n  { queryData,registerMap,setGeoAreaColor,handleTotalAndUnitMap,handleCommonOpt,queryCityCenter}] = useChartHook(props, initOption)\r\nconst echartId = ref(\"\");\r\n// 地图属性\r\nconst mapObject = computed(() => ({ code: getAreaCode.value, data: mapDataJson.value }));\r\n\r\n// 初始化配置选项\r\nlet geoCoordMap = {};\r\nasync function initOption(data){\r\n  let chartData = dataSource.value;\r\n  let mapName = await registerMap();\r\n  try {\r\n    // 使用 registerMap 注册的地图名称\r\n    geoCoordMap = getGeoCoordMap(mapDataJson.value);\r\n    chartOption.value.geo.map = mapName;\r\n    let barSize = config?.commonOption?.barSize || 17;\r\n    let barColor = config?.commonOption?.barColor || '#F8E71C';\r\n    let barColor2 = config?.commonOption?.barColor2 || '#F8E71C';\r\n    chartOption.value.series = [\r\n      {\r\n        geoIndex: 0,\r\n        // coordinateSystem: 'geo',\r\n        showLegendSymbol: false,\r\n        type: 'map',\r\n        roam: true,\r\n        label: {\r\n          show: false,\r\n          color: '#ffffff',\r\n        },\r\n        emphasis: {\r\n          color: 'white',\r\n          show: false,\r\n          itemStyle:{\r\n            areaColor: '#FA8C16',\r\n            borderWidth: 0,\r\n            color: 'green',\r\n          }\r\n        },\r\n        itemStyle: {\r\n          borderColor: '#2980b9',\r\n          borderWidth: 1,\r\n          areaColor: '#12235c',\r\n        },\r\n        map: mapName, // 使用\r\n        animation: true,\r\n        data: chartData,\r\n      },\r\n      {\r\n        type: 'lines',\r\n        zlevel: 5,\r\n        geoIndex: 0,\r\n        effect: {\r\n          show: false,\r\n          symbolSize: 5, // 图标大小\r\n        },\r\n        lineStyle: {\r\n          width: barSize, // 尾迹线条宽度\r\n          color: {\r\n            type: 'linear',\r\n            x: 0,\r\n            y: 0,\r\n            x2: 1,\r\n            y2: 0,\r\n            colorStops: [\r\n              {\r\n                offset: 0,\r\n                color: barColor, // 0% 处的颜色\r\n              },\r\n              {\r\n                offset: 0.5,\r\n                color: barColor, // 0% 处的颜色\r\n              },\r\n              {\r\n                offset: 0.5,\r\n                color: barColor2, // 0% 处的颜色\r\n              },\r\n              {\r\n                offset: 1,\r\n                color: barColor2, // 0% 处的颜色\r\n              },\r\n              {\r\n                offset: 1,\r\n                color: barColor, // 100% 处的颜色\r\n              },\r\n            ],\r\n            global: false, // 缺省为 false\r\n          },\r\n          opacity: 1, // 尾迹线条透明度\r\n          curveness: 0, // 尾迹线条曲直度\r\n        },\r\n        label: {\r\n          show: 0,\r\n          position: 'end',\r\n          formatter: '245',\r\n        },\r\n        silent: true,\r\n        data: lineData(chartData),\r\n      },\r\n      {\r\n        type: 'scatter',\r\n        coordinateSystem: 'geo',\r\n        geoIndex: 0,\r\n        zlevel: 2,\r\n        label: {\r\n          show: false,\r\n          position: 'bottom',\r\n          formatter: (params) => {\r\n            return data[params.dataIndex]?.value\r\n          },\r\n          padding: [4, 8],\r\n          backgroundColor: '#003F5E',\r\n          borderRadius: 5,\r\n          borderColor: '#67F0EF',\r\n          borderWidth: 1,\r\n          color: '#67F0EF',\r\n        },\r\n        symbol: 'diamond',\r\n        symbolSize: [barSize - 1, 8],\r\n        itemStyle: {\r\n          color: barColor,\r\n          opacity: 1,\r\n        },\r\n        silent: true,\r\n        data: scatterData(chartData),\r\n      },\r\n      {\r\n        type: 'scatter',\r\n        coordinateSystem: 'geo',\r\n        geoIndex: 0,\r\n        zlevel: 1,\r\n        label: {\r\n          formatter: '{b}',\r\n          position: 'bottom',\r\n          color: '#fff',\r\n          fontSize: 11,\r\n          distance: 10,\r\n          show: false,\r\n        },\r\n        symbol: 'diamond',\r\n        symbolSize: [barSize - 1, 8],\r\n        itemStyle: {\r\n          // color: '#F7AF21',\r\n          color: {\r\n            type: 'linear',\r\n            x: 0,\r\n            y: 0,\r\n            x2: 1,\r\n            y2: 0,\r\n            colorStops: [\r\n              {\r\n                offset: 0,\r\n                color: barColor, // 0% 处的颜色\r\n              },\r\n              {\r\n                offset: 0.5,\r\n                color: barColor, // 0% 处的颜色\r\n              },\r\n              {\r\n                offset: 0.5,\r\n                color: barColor2, // 0% 处的颜色\r\n              },\r\n              {\r\n                offset: 1,\r\n                color: barColor2, // 0% 处的颜色\r\n              },\r\n              {\r\n                offset: 1,\r\n                color: barColor, // 100% 处的颜色\r\n              },\r\n            ],\r\n            global: false, // 缺省为 false\r\n          },\r\n          opacity: 1,\r\n          // shadowColor: '#fff',\r\n          // shadowBlur: 5,\r\n          // shadowOffsetY: 2\r\n        },\r\n        silent: true,\r\n        data: scatterData2(chartData),\r\n      },\r\n    ];\r\n    // 合并配置\r\n    if (props.config && props.config.option) {\r\n      merge(chartOption.value, props.config.option);\r\n      chartOption.value = setGeoAreaColor(chartOption.value, props.config);\r\n      chartOption.value = handleTotalAndUnitMap(props.compName, chartOption.value, props.config, chartData);\r\n      chartOption.value = handleCommonOpt(chartOption.value);\r\n      setTimeout(() => {\r\n        option.value = { ...chartOption.value };\r\n        console.log(\"柱形地图option.value\", option.value);\r\n        pageTips.show = false;\r\n        echartId.value = props.i;\r\n      }, 300);\r\n    }\r\n    if (dataSource.value && dataSource.value.length === 0) {\r\n      pageTips.status = 1;\r\n      pageTips.show = true;\r\n    }\r\n  } catch (e) {\r\n    console.log(\"柱形地图报错\", e);\r\n  }\r\n};\r\n// 动态计算柱形图的高度（定一个max）\r\nfunction lineMaxHeight(chartData) {\r\n  const maxValue = Math.max(...chartData.map((item) => item.value));\r\n  return maxValue < 10 ? maxValue : 1 / maxValue;\r\n}\r\n\r\n// 柱状体的主干\r\nfunction lineData(chartData) {\r\n  let lineData = [];\r\n  chartData.forEach((item) => {\r\n    let geoCoord = city_point.value[item.name];\r\n    if (geoCoord) {\r\n      let coords = [geoCoord, [geoCoord[0], geoCoord[1] + item.value * lineMaxHeight(chartData)]];\r\n      lineData.push({\r\n        coords: coords,\r\n      });\r\n    }\r\n  });\r\n  return lineData;\r\n}\r\n\r\n// 柱状体的顶部\r\nfunction scatterData(chartData) {\r\n  let scatterData = [];\r\n  chartData.forEach((item) => {\r\n    let geoCoord = city_point.value[item.name];\r\n    if (geoCoord) {\r\n      scatterData.push([geoCoord[0], geoCoord[1] + item.value * lineMaxHeight(chartData)]);\r\n    }\r\n  });\r\n  return scatterData;\r\n}\r\n\r\n// 柱状体的底部\r\nfunction scatterData2(chartData) {\r\n  let scatterData2 = [];\r\n  chartData.forEach((item) => {\r\n    let geoCoord = city_point.value[item.name];\r\n    if (geoCoord) {\r\n      scatterData2.push({\r\n        name: item.name,\r\n        value: geoCoord,\r\n      })\r\n    }\r\n  });\r\n  return scatterData2;\r\n}\r\n\r\n// 挂载时查询数据\r\nonMounted(async () => {\r\n  await queryCityCenter()\r\n  await queryData();\r\n});\r\n</script>\r\n\r\n<style>\r\n.content {\r\n  margin: 5px;\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-work/components/echarts/map/JBarMap/index.vue'\nwx.createComponent(Component)"], "names": ["ref", "useChartHook", "computed", "mapName", "getGeoCoordMap", "_a", "merge", "lineData", "scatterData", "scatterData2", "onMounted", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,MAAM,gBAAgB,MAAW;;;;;AAKjC,UAAM,QAAQ;AAId,UAAM,SAASA,cAAAA,IAAI,CAAA,CAAE;AACrB,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB,KAAK;AAAA,QACH,KAAK;AAAA,QACL,WAAW,CAAE;AAAA,MACd;AAAA,MACD,SAAS;AAAA,QACP,WAAW;AAAA,UACT,OAAO;AAAA,QACR;AAAA,QACD,SAAS;AAAA,QACT,WAAW;AAAA,MACZ;AAAA,IACH,CAAC;AACD,QAAI;AAAA,MAAC,EAAE,YAAY,QAAQ,UAAU,QAAO,aAAY,SAAQ,aAAY,WAAY;AAAA,MACtF,EAAE,WAAU,aAAY,iBAAgB,uBAAsB,iBAAgB,gBAAe;AAAA,IAAC,IAAIC,wCAAAA,aAAa,OAAO,UAAU;AAClI,UAAM,WAAWD,cAAAA,IAAI,EAAE;AAELE,kBAAAA,SAAS,OAAO,EAAE,MAAM,YAAY,OAAO,MAAM,YAAY,MAAK,EAAG;AAGvF,QAAI,cAAc,CAAA;AAClB,aAAe,WAAW,MAAK;AAAA;;AAC7B,YAAI,YAAY,WAAW;AAC3B,YAAIC,WAAU,MAAM;AACpB,YAAI;AAEF,wBAAcC,uCAAc,eAAC,YAAY,KAAK;AAC9C,sBAAY,MAAM,IAAI,MAAMD;AAC5B,cAAI,YAAU,sCAAQ,iBAAR,mBAAsB,YAAW;AAC/C,cAAI,aAAW,sCAAQ,iBAAR,mBAAsB,aAAY;AACjD,cAAI,cAAY,sCAAQ,iBAAR,mBAAsB,cAAa;AACnD,sBAAY,MAAM,SAAS;AAAA,YACzB;AAAA,cACE,UAAU;AAAA;AAAA,cAEV,kBAAkB;AAAA,cAClB,MAAM;AAAA,cACN,MAAM;AAAA,cACN,OAAO;AAAA,gBACL,MAAM;AAAA,gBACN,OAAO;AAAA,cACR;AAAA,cACD,UAAU;AAAA,gBACR,OAAO;AAAA,gBACP,MAAM;AAAA,gBACN,WAAU;AAAA,kBACR,WAAW;AAAA,kBACX,aAAa;AAAA,kBACb,OAAO;AAAA,gBACR;AAAA,cACF;AAAA,cACD,WAAW;AAAA,gBACT,aAAa;AAAA,gBACb,aAAa;AAAA,gBACb,WAAW;AAAA,cACZ;AAAA,cACD,KAAKA;AAAA;AAAA,cACL,WAAW;AAAA,cACX,MAAM;AAAA,YACP;AAAA,YACD;AAAA,cACE,MAAM;AAAA,cACN,QAAQ;AAAA,cACR,UAAU;AAAA,cACV,QAAQ;AAAA,gBACN,MAAM;AAAA,gBACN,YAAY;AAAA;AAAA,cACb;AAAA,cACD,WAAW;AAAA,gBACT,OAAO;AAAA;AAAA,gBACP,OAAO;AAAA,kBACL,MAAM;AAAA,kBACN,GAAG;AAAA,kBACH,GAAG;AAAA,kBACH,IAAI;AAAA,kBACJ,IAAI;AAAA,kBACJ,YAAY;AAAA,oBACV;AAAA,sBACE,QAAQ;AAAA,sBACR,OAAO;AAAA;AAAA,oBACR;AAAA,oBACD;AAAA,sBACE,QAAQ;AAAA,sBACR,OAAO;AAAA;AAAA,oBACR;AAAA,oBACD;AAAA,sBACE,QAAQ;AAAA,sBACR,OAAO;AAAA;AAAA,oBACR;AAAA,oBACD;AAAA,sBACE,QAAQ;AAAA,sBACR,OAAO;AAAA;AAAA,oBACR;AAAA,oBACD;AAAA,sBACE,QAAQ;AAAA,sBACR,OAAO;AAAA;AAAA,oBACR;AAAA,kBACF;AAAA,kBACD,QAAQ;AAAA;AAAA,gBACT;AAAA,gBACD,SAAS;AAAA;AAAA,gBACT,WAAW;AAAA;AAAA,cACZ;AAAA,cACD,OAAO;AAAA,gBACL,MAAM;AAAA,gBACN,UAAU;AAAA,gBACV,WAAW;AAAA,cACZ;AAAA,cACD,QAAQ;AAAA,cACR,MAAM,SAAS,SAAS;AAAA,YACzB;AAAA,YACD;AAAA,cACE,MAAM;AAAA,cACN,kBAAkB;AAAA,cAClB,UAAU;AAAA,cACV,QAAQ;AAAA,cACR,OAAO;AAAA,gBACL,MAAM;AAAA,gBACN,UAAU;AAAA,gBACV,WAAW,CAAC,WAAW;;AACrB,0BAAOE,MAAA,KAAK,OAAO,SAAS,MAArB,gBAAAA,IAAwB;AAAA,gBAChC;AAAA,gBACD,SAAS,CAAC,GAAG,CAAC;AAAA,gBACd,iBAAiB;AAAA,gBACjB,cAAc;AAAA,gBACd,aAAa;AAAA,gBACb,aAAa;AAAA,gBACb,OAAO;AAAA,cACR;AAAA,cACD,QAAQ;AAAA,cACR,YAAY,CAAC,UAAU,GAAG,CAAC;AAAA,cAC3B,WAAW;AAAA,gBACT,OAAO;AAAA,gBACP,SAAS;AAAA,cACV;AAAA,cACD,QAAQ;AAAA,cACR,MAAM,YAAY,SAAS;AAAA,YAC5B;AAAA,YACD;AAAA,cACE,MAAM;AAAA,cACN,kBAAkB;AAAA,cAClB,UAAU;AAAA,cACV,QAAQ;AAAA,cACR,OAAO;AAAA,gBACL,WAAW;AAAA,gBACX,UAAU;AAAA,gBACV,OAAO;AAAA,gBACP,UAAU;AAAA,gBACV,UAAU;AAAA,gBACV,MAAM;AAAA,cACP;AAAA,cACD,QAAQ;AAAA,cACR,YAAY,CAAC,UAAU,GAAG,CAAC;AAAA,cAC3B,WAAW;AAAA;AAAA,gBAET,OAAO;AAAA,kBACL,MAAM;AAAA,kBACN,GAAG;AAAA,kBACH,GAAG;AAAA,kBACH,IAAI;AAAA,kBACJ,IAAI;AAAA,kBACJ,YAAY;AAAA,oBACV;AAAA,sBACE,QAAQ;AAAA,sBACR,OAAO;AAAA;AAAA,oBACR;AAAA,oBACD;AAAA,sBACE,QAAQ;AAAA,sBACR,OAAO;AAAA;AAAA,oBACR;AAAA,oBACD;AAAA,sBACE,QAAQ;AAAA,sBACR,OAAO;AAAA;AAAA,oBACR;AAAA,oBACD;AAAA,sBACE,QAAQ;AAAA,sBACR,OAAO;AAAA;AAAA,oBACR;AAAA,oBACD;AAAA,sBACE,QAAQ;AAAA,sBACR,OAAO;AAAA;AAAA,oBACR;AAAA,kBACF;AAAA,kBACD,QAAQ;AAAA;AAAA,gBACT;AAAA,gBACD,SAAS;AAAA;AAAA;AAAA;AAAA,cAIV;AAAA,cACD,QAAQ;AAAA,cACR,MAAM,aAAa,SAAS;AAAA,YAC7B;AAAA,UACP;AAEI,cAAI,MAAM,UAAU,MAAM,OAAO,QAAQ;AACvCC,0BAAK,MAAC,YAAY,OAAO,MAAM,OAAO,MAAM;AAC5C,wBAAY,QAAQ,gBAAgB,YAAY,OAAO,MAAM,MAAM;AACnE,wBAAY,QAAQ,sBAAsB,MAAM,UAAU,YAAY,OAAO,MAAM,QAAQ,SAAS;AACpG,wBAAY,QAAQ,gBAAgB,YAAY,KAAK;AACrD,uBAAW,MAAM;AACf,qBAAO,QAAQ,mBAAK,YAAY;AAChC,sBAAQ,IAAI,oBAAoB,OAAO,KAAK;AAC5C,uBAAS,OAAO;AAChB,uBAAS,QAAQ,MAAM;AAAA,YACxB,GAAE,GAAG;AAAA,UACP;AACD,cAAI,WAAW,SAAS,WAAW,MAAM,WAAW,GAAG;AACrD,qBAAS,SAAS;AAClB,qBAAS,OAAO;AAAA,UACjB;AAAA,QACF,SAAQ,GAAG;AACV,kBAAQ,IAAI,UAAU,CAAC;AAAA,QACxB;AAAA,MACH;AAAA;AAEA,aAAS,cAAc,WAAW;AAChC,YAAM,WAAW,KAAK,IAAI,GAAG,UAAU,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC;AAChE,aAAO,WAAW,KAAK,WAAW,IAAI;AAAA,IACxC;AAGA,aAAS,SAAS,WAAW;AAC3B,UAAIC,YAAW,CAAA;AACf,gBAAU,QAAQ,CAAC,SAAS;AAC1B,YAAI,WAAW,WAAW,MAAM,KAAK,IAAI;AACzC,YAAI,UAAU;AACZ,cAAI,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC,IAAI,KAAK,QAAQ,cAAc,SAAS,CAAC,CAAC;AAC1F,UAAAA,UAAS,KAAK;AAAA,YACZ;AAAA,UACR,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AACD,aAAOA;AAAA,IACT;AAGA,aAAS,YAAY,WAAW;AAC9B,UAAIC,eAAc,CAAA;AAClB,gBAAU,QAAQ,CAAC,SAAS;AAC1B,YAAI,WAAW,WAAW,MAAM,KAAK,IAAI;AACzC,YAAI,UAAU;AACZ,UAAAA,aAAY,KAAK,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC,IAAI,KAAK,QAAQ,cAAc,SAAS,CAAC,CAAC;AAAA,QACpF;AAAA,MACL,CAAG;AACD,aAAOA;AAAA,IACT;AAGA,aAAS,aAAa,WAAW;AAC/B,UAAIC,gBAAe,CAAA;AACnB,gBAAU,QAAQ,CAAC,SAAS;AAC1B,YAAI,WAAW,WAAW,MAAM,KAAK,IAAI;AACzC,YAAI,UAAU;AACZ,UAAAA,cAAa,KAAK;AAAA,YAChB,MAAM,KAAK;AAAA,YACX,OAAO;AAAA,UACf,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AACD,aAAOA;AAAA,IACT;AAGAC,kBAAAA,UAAU,MAAY;AACpB,YAAM,gBAAiB;AACvB,YAAM,UAAS;AAAA,IACjB,EAAC;;;;;;;;;;;;;;;;;;ACpSD,GAAG,gBAAgBC,SAAS;"}