"use strict";
const common_vendor = require("../vendor.js");
const pcaa = freezeDeep(usePlatPcaaData());
function usePlatPcaaData() {
  const { city_list: city, county_list: county, province_list: province } = common_vendor.areaList;
  const dataMap = /* @__PURE__ */ new Map();
  const flatData = { "86": province };
  Object.keys(province).forEach((code) => {
    flatData[code] = {};
    dataMap.set(code.slice(0, 2), flatData[code]);
  });
  Object.keys(city).forEach((code) => {
    flatData[code] = {};
    dataMap.set(code.slice(0, 4), flatData[code]);
    const getProvince = dataMap.get(code.slice(0, 2));
    if (getProvince) {
      getProvince[code] = city[code];
    }
  });
  Object.keys(county).forEach((code) => {
    const getCity = dataMap.get(code.slice(0, 4));
    if (getCity) {
      getCity[code] = county[code];
    }
  });
  return flatData;
}
function freezeDeep(obj) {
  if (obj != null) {
    if (Array.isArray(obj)) {
      obj.forEach((item) => freezeDeep(item));
    } else if (typeof obj === "object") {
      Object.values(obj).forEach((value) => {
        freezeDeep(value);
      });
    }
    Object.freeze(obj);
  }
  return obj;
}
exports.pcaa = pcaa;
//# sourceMappingURL=pcaUtils.js.map
