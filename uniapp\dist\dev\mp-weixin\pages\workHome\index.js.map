{"version": 3, "file": "index.js", "sources": ["../../../../../src/pages/workHome/index.vue", "../../../../../uniPage:/cGFnZXMvd29ya0hvbWUvaW5kZXgudnVl"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n  <PageLayout :navbarShow=\"false\">\r\n    <view class=\"header\"></view>\r\n    <view\r\n      class=\"wrap\"\r\n      :style=\"{\r\n        '--nav-height': `${statusBarHeight + navHeight}px`,\r\n        '--status-bar-height': `${statusBarHeight}px`,\r\n      }\"\r\n    >\r\n      <wd-tabs :customClass=\"getClass()\" v-model=\"tabActive\">\r\n        <template v-for=\"(item, index) in tabList\" :key=\"index\">\r\n          <wd-tab :title=\"item.title\" :name=\"item.key\">\r\n            <dragList v-if=\"item.key === '1'\"></dragList>\r\n            <bigScreenList v-if=\"item.key === '2'\"></bigScreenList>\r\n          </wd-tab>\r\n        </template>\r\n      </wd-tabs>\r\n    </view>\r\n  </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport bigScreenList from './components/bigScreenList.vue'\r\nimport dragList from './components/dragList.vue'\r\nimport { platform, isMp } from '@/utils/platform'\r\ndefineOptions({\r\n  name: 'workHome',\r\n  options: {\r\n    styleIsolation: 'shared',\r\n  },\r\n})\r\nimport { ref } from 'vue'\r\nconst globalData = getApp().globalData\r\nconst { systemInfo, navHeight } = globalData\r\nconst { statusBarHeight } = systemInfo\r\nconsole.log('systemInfo:::', systemInfo)\r\nconst tabList = ref([\r\n  { key: '1', title: '仪表盘' },\r\n  { key: '2', title: '大屏' },\r\n  // { key: '3', title: 'Online' },\r\n])\r\nconst tabActive = ref<string>('1')\r\nconst getClass = () => {\r\n  return `${platform} ${isMp ? 'mp' : ''}`\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.header{\r\n\t//距离顶部 --status-bar-height 自动调整\r\n\tmargin-top:var(--status-bar-height);\r\n}\r\n\r\n.wrap {\r\n  height: 100%;\r\n}\r\n:deep(.wd-tabs) {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  &.mp {\r\n    .wd-tabs__nav-container {\r\n      padding-right: 7%;\r\n    }\r\n  }\r\n  .wd-tabs__nav {\r\n    background: linear-gradient(45deg, #0081ff, #1cbbb4);\r\n    height: var(--nav-height);\r\n    padding-top: var(--status-bar-height);\r\n    .wd-tabs__nav-item {\r\n      color: #fff;\r\n    }\r\n  }\r\n  .wd-tabs__container {\r\n    flex: 1;\r\n    width: 100%;\r\n  }\r\n  .wd-tabs__body {\r\n    position: relative;\r\n  }\r\n  .wd-tabs__line {\r\n    background-color: #fff;\r\n  }\r\n}\r\n:deep(.wd-tab) {\r\n  .wd-tab__body {\r\n    position: absolute;\r\n    height: 100%;\r\n    width: 100%;\r\n    top: 0;\r\n    left: 0;\r\n  }\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages/workHome/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "platform", "isMp"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,MAAA,gBAA0B,MAAA;AAC1B,MAAA,WAAqB,MAAA;;;;;;;;;AASf,UAAA,aAAa,SAAS;AACtB,UAAA,EAAE,YAAY,UAAA,IAAc;AAC5B,UAAA,EAAE,oBAAoB;AACpB,YAAA,IAAI,iBAAiB,UAAU;AACvC,UAAM,UAAUA,cAAAA,IAAI;AAAA,MAClB,EAAE,KAAK,KAAK,OAAO,MAAM;AAAA,MACzB,EAAE,KAAK,KAAK,OAAO,KAAK;AAAA;AAAA,IAAA,CAEzB;AACK,UAAA,YAAYA,kBAAY,GAAG;AACjC,UAAM,WAAW,MAAM;AACrB,aAAO,GAAGC,eAAAA,QAAQ,IAAIC,eAAAA,OAAO,OAAO,EAAE;AAAA,IACxC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChDA,GAAG,WAAW,eAAe;"}