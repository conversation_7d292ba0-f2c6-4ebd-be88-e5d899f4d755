"use strict";
const common_vendor = require("../../common/vendor.js");
const store_user = require("../../store/user.js");
const store_pageParams = require("../../store/page-params.js");
require("../../plugin/uni-mini-router/router/index.js");
const plugin_uniMiniRouter_core_index = require("../../plugin/uni-mini-router/core/index.js");
if (!Array) {
  const _easycom_wd_img2 = common_vendor.resolveComponent("wd-img");
  const _easycom_wd_text2 = common_vendor.resolveComponent("wd-text");
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_img2 + _easycom_wd_text2 + _easycom_wd_button2 + _easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_wd_img = () => "../../node-modules/wot-design-uni/components/wd-img/wd-img.js";
const _easycom_wd_text = () => "../../node-modules/wot-design-uni/components/wd-text/wd-text.js";
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  (_easycom_wd_img + _easycom_wd_text + _easycom_wd_button + _easycom_PageLayout)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "personPage",
  setup(__props) {
    var _a, _b;
    const userStore = store_user.useUserStore();
    const paramsStore = store_pageParams.useParamsStore();
    const router = plugin_uniMiniRouter_core_index.useRouter();
    const params = paramsStore.getPageParams("personPage");
    const backRouteName = (_a = common_vendor.ref(params.backRouteName)) != null ? _a : {};
    let data = (_b = params.data) != null ? _b : {};
    const handleGo = () => {
      var parmas = {
        fromAvatar: data.avatar,
        fromUserName: data.realname || data.username,
        msgFrom: userStore.userInfo.userid,
        msgTo: data.id,
        type: "friend"
      };
      paramsStore.setPageParams("chat", { back: "personPage", data: parmas });
      router.push({ name: "chat" });
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          ["custom-class"]: "avatar",
          width: "75px",
          height: "75px",
          radius: "50%",
          src: common_vendor.unref(data).avatar
        }),
        b: common_vendor.p({
          ["custom-class"]: "realname center",
          text: common_vendor.unref(data).realname
        }),
        c: common_vendor.o(handleGo),
        d: common_vendor.p({
          ["custom-class"]: ""
        }),
        e: common_vendor.t(common_vendor.unref(data).phone || "未填写"),
        f: common_vendor.t(common_vendor.unref(data).email || "未填写"),
        g: common_vendor.p({
          backRouteName: common_vendor.unref(backRouteName),
          navLeftText: "",
          navBgTransparent: true,
          navFixed: true
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-ecc09b92"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=personPage.js.map
