"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../../common/vendor.js");
if (!Math) {
  wdPopup();
}
const wdPopup = () => "../wd-popup/wd-popup.js";
const __default__ = {
  name: "wd-notify",
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: "shared"
  }
};
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, __default__), {
  props: common_vendor.notifyProps,
  emits: ["update:visible", "click", "closed", "opened"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emits = __emit;
    const state = common_vendor.inject(common_vendor.getNotifyOptionKey(props.selector), common_vendor.ref(props));
    const customStyle = common_vendor.computed(() => {
      const { safeHeight, position } = state.value;
      let customStyle2 = "";
      switch (position) {
        case "top":
          customStyle2 = `top: calc(var(--window-top) + ${common_vendor.addUnit(safeHeight || 0)})`;
          break;
        case "bottom":
          customStyle2 = "bottom: var(--window-bottom)";
          break;
      }
      return customStyle2;
    });
    const onClick = (event) => {
      if (common_vendor.isFunction(state.value.onClick))
        return state.value.onClick(event);
      emits("click", event);
    };
    const onClosed = () => {
      if (common_vendor.isFunction(state.value.onClosed))
        return state.value.onClosed();
      emits("closed");
    };
    const onOpened = () => {
      if (common_vendor.isFunction(state.value.onOpened))
        return state.value.onOpened();
      emits("opened");
    };
    common_vendor.watch(
      () => state.value.visible,
      (visible) => {
        emits("update:visible", visible);
      },
      { deep: true }
    );
    return (_ctx, _cache) => {
      return {
        a: common_vendor.t(common_vendor.unref(state).message),
        b: common_vendor.n(`wd-notify--${common_vendor.unref(state).type}`),
        c: common_vendor.unref(state).color,
        d: common_vendor.unref(state).background,
        e: common_vendor.o(onClick),
        f: common_vendor.o(onClosed),
        g: common_vendor.o(onOpened),
        h: common_vendor.o(($event) => common_vendor.unref(state).visible = $event),
        i: common_vendor.p({
          ["custom-style"]: customStyle.value,
          position: common_vendor.unref(state).position,
          ["z-index"]: common_vendor.unref(state).zIndex,
          duration: 250,
          modal: false,
          modelValue: common_vendor.unref(state).visible
        })
      };
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-4fabcdf5"]]);
wx.createComponent(Component);
//# sourceMappingURL=wd-notify.js.map
