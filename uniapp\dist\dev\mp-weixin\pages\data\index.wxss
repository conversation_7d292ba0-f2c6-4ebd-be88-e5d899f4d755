/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-container.data-v-d3826533 {
  display: flex;
  flex-direction: column;
  height: 100vh;
  min-height: 0;
  background-color: #f5f7fa;
  padding-bottom: 40rpx;
}
.page-header.data-v-d3826533 {
  background-color: #07C160;
  padding: 40rpx 30rpx 60rpx;
  color: white;
  border-radius: 0 0 30rpx 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.2);
}
.page-header .page-title.data-v-d3826533 {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  display: block;
}
.page-header .page-subtitle.data-v-d3826533 {
  font-size: 28rpx;
  opacity: 0.9;
}
.page-scroll-view.data-v-d3826533 {
  flex: 1;
  min-height: 0;
  width: 100%;
  background-color: #f5f7fa;
  overflow: auto;
}
.category-title.data-v-d3826533 {
  display: flex;
  align-items: center;
  margin: 30rpx 10rpx 20rpx;
}
.category-title .title-indicator.data-v-d3826533 {
  width: 8rpx;
  height: 30rpx;
  background-color: #07C160;
  border-radius: 4rpx;
  margin-right: 16rpx;
}
.category-title .title-text.data-v-d3826533 {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}
.data-grid.data-v-d3826533 {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 30rpx;
}
.data-grid .data-card.data-v-d3826533 {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}
.data-grid .data-card.data-v-d3826533:active {
  transform: scale(0.98);
  background-color: #fafafa;
}
.data-grid .data-card .card-icon.data-v-d3826533 {
  background-color: rgba(7, 193, 96, 0.1);
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}
.data-grid .data-card .card-image.data-v-d3826533 {
  width: 50rpx;
  height: 50rpx;
  display: block;
}
.data-grid .data-card .card-content.data-v-d3826533 {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.data-grid .data-card .card-content .card-title.data-v-d3826533 {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
  white-space: pre-line;
}
.data-grid .data-card .card-content .card-action.data-v-d3826533 {
  display: flex;
  align-items: center;
}
.data-grid .data-card .card-content .card-action .action-text.data-v-d3826533 {
  font-size: 24rpx;
  color: #07C160;
  margin-right: 8rpx;
}
.help-section.data-v-d3826533 {
  padding: 0 10rpx;
  margin-top: 20rpx;
}
.help-section .help-card.data-v-d3826533 {
  display: flex;
  align-items: center;
  background-color: #f0f9f4;
  border-radius: 16rpx;
  padding: 20rpx 24rpx;
  border-left: 8rpx solid #07C160;
}
.help-section .help-card .help-icon.data-v-d3826533 {
  background-color: #07C160;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}
.help-section .help-card .help-content.data-v-d3826533 {
  flex: 1;
}
.help-section .help-card .help-content .help-title.data-v-d3826533 {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 4rpx;
}
.help-section .help-card .help-content .help-text.data-v-d3826533 {
  font-size: 24rpx;
  color: #666;
}
.search-container.data-v-d3826533 {
  background: #FFFFFF;
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}
.search-container .search-label.data-v-d3826533 {
  margin-bottom: 40rpx;
}
.search-container .search-label text.data-v-d3826533 {
  font-size: 28rpx;
  color: #333;
}
.search-container .search-input-container.data-v-d3826533 {
  display: inline-block;
  vertical-align: middle;
  position: relative;
}
.search-container .search-input-container .search-input.data-v-d3826533 {
  width: 130%;
  height: 80rpx;
  background: #F5F5F5;
  border-radius: 8rpx;
  padding: 0 60rpx 0 20rpx;
  font-size: 28rpx;
}
.search-container .search-input-container .clear-icon.data-v-d3826533 {
  position: absolute;
  right: -30%;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
  padding: 10rpx;
}
.search-container .search-button.data-v-d3826533 {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #07C160;
  height: 80rpx;
  border-radius: 8rpx;
  cursor: pointer;
  padding: 0 30rpx;
  margin-top: 20rpx;
  vertical-align: middle;
}
.search-container .search-button .search-button-text.data-v-d3826533 {
  color: #FFFFFF;
  font-size: 28rpx;
  margin-left: 10rpx;
}
.recent-data.data-v-d3826533 {
  padding: 0 20rpx 20rpx;
  position: relative;
  min-height: 200rpx;
}
.recent-data .loading-container.data-v-d3826533 {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
}
.recent-data .empty-data.data-v-d3826533 {
  padding: 30rpx 0;
  text-align: center;
  color: #999;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.recent-data .empty-data .empty-image.data-v-d3826533 {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}
.recent-data .data-items .data-card.data-v-d3826533 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background-color: white;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
  margin-left: -20rpx;
  margin-right: -20rpx;
}
.recent-data .data-items .data-card.data-v-d3826533:active {
  transform: scale(0.99);
  background-color: #fafafa;
}
.recent-data .data-items .data-card .data-info.data-v-d3826533 {
  display: flex;
  align-items: flex-start;
  flex: 1;
}
.recent-data .data-items .data-card .data-info .data-icon.data-v-d3826533 {
  margin-right: 30rpx;
  width: 90rpx;
  height: 90rpx;
  flex-shrink: 0;
  overflow: hidden;
  border-radius: 50%;
}
.recent-data .data-items .data-card .data-info .data-icon .card-image.data-v-d3826533 {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 50%;
}
.recent-data .data-items .data-card .data-info .data-details.data-v-d3826533 {
  flex: 1;
  overflow: hidden;
}
.recent-data .data-items .data-card .data-info .data-details .data-title-row.data-v-d3826533 {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8rpx;
}
.recent-data .data-items .data-card .data-info .data-details .data-title-row .data-type.data-v-d3826533 {
  font-size: 30rpx;
  color: #333333;
  font-weight: 500;
}
.recent-data .data-items .data-card .data-info .data-details .data-content .data-fields.data-v-d3826533 {
  display: flex;
  flex-direction: column;
}
.recent-data .data-items .data-card .data-info .data-details .data-content .data-fields .field-value.data-v-d3826533 {
  color: #666666;
  font-size: 24rpx;
  display: block;
}
.recent-data .data-items .data-card .data-action.data-v-d3826533 {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
}
.recent-data .data-items .data-card .data-action .action-text.data-v-d3826533 {
  color: #07C160;
  font-size: 26rpx;
  margin-right: 10rpx;
}