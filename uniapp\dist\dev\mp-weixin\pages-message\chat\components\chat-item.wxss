/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.chat-item.data-v-adacc9d7 {
  display: flex;
  flex-direction: column;
  padding: 20rpx;
}
.chat-time.data-v-adacc9d7 {
  padding: 4rpx 0rpx;
  text-align: center;
  font-size: 22rpx;
  color: #aaaaaa;
}
.chat-container.data-v-adacc9d7 {
  display: flex;
  flex-direction: row;
}
.chat-location-me.data-v-adacc9d7 {
  flex-direction: row-reverse;
  text-align: right;
}
.chat-icon-container.data-v-adacc9d7 {
  margin-top: 12rpx;
}
.chat-icon.data-v-adacc9d7 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8px;
  background-color: #eeeeee;
}
.chat-content-container.data-v-adacc9d7 {
  margin: 0rpx 15rpx;
}
.chat-user-name.data-v-adacc9d7 {
  font-size: 26rpx;
  color: #888888;
}
.chat-text-container.data-v-adacc9d7,
.chat-voice-container.data-v-adacc9d7 {
  text-align: left;
  background-color: #fff;
  border-radius: 8rpx;
  padding: 7px 10px;
  margin-top: 10rpx;
  max-width: 500rpx;
}
.chat-text-container-me.data-v-adacc9d7,
.chat-voice-container.data-v-adacc9d7 {
  background-color: #55aaff;
}
.chat-voice-container.data-v-adacc9d7 {
  display: flex;
  align-items: center;
  color: #fff;
}
.chat-text-container-super.data-v-adacc9d7 {
  display: flex;
  flex-direction: row;
}
.chat-text-container-super.flex-end.data-v-adacc9d7 {
  justify-content: flex-end;
}
.chat-text-container-super.flex-start.data-v-adacc9d7 {
  justify-content: flex-start;
}
.chat-text.data-v-adacc9d7 {
  font-size: 28rpx;
  word-break: break-all;
}
.chat-text-me.data-v-adacc9d7 {
  color: white;
}