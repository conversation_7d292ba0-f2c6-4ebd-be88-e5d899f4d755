"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const store_user = require("../../store/user.js");
require("../../utils/http.js");
const common_constants = require("../../common/constants.js");
require("../../plugin/uni-mini-router/router/index.js");
const plugin_uniMiniRouter_core_index = require("../../plugin/uni-mini-router/core/index.js");
const store_pageParams = require("../../store/page-params.js");
if (!Array) {
  const _easycom_wd_text2 = common_vendor.resolveComponent("wd-text");
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _easycom_wd_notify2 = common_vendor.resolveComponent("wd-notify");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_text2 + _easycom_wd_button2 + _easycom_wd_notify2 + _easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_wd_text = () => "../../node-modules/wot-design-uni/components/wd-text/wd-text.js";
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
const _easycom_wd_notify = () => "../../node-modules/wot-design-uni/components/wd-notify/wd-notify.js";
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  (_easycom_wd_text + _easycom_wd_button + _easycom_wd_notify + _easycom_PageLayout)();
}
const defLogo = "https://www.mograine.cn/images/logo2.png";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "login",
  options: {
    // apply-shared‌：当前页面样式会影响到子组件样式.(小程序)
    // shared‌：当前页面样式影响到子组件，子组件样式也会影响到当前页面.(小程序)
    styleIsolation: "shared"
  }
}), {
  __name: "login",
  setup(__props) {
    plugin_uniMiniRouter_core_index.useRouter();
    const compLogo = common_vendor.ref(defLogo);
    const compTitle = common_vendor.ref("心 衰 院 外 管 理");
    const userCategory = common_vendor.ref(-1);
    const paramsStore = store_pageParams.useParamsStore();
    paramsStore.reset();
    const userStore = store_user.useUserStore();
    const handleCategoryLogin = (e, category) => {
      userCategory.value = category;
      mobileLogin(e);
    };
    const mobileLogin = (e) => {
      console.log("手机号授权登录", e);
      return new Promise((resolve, reject) => __async(this, null, function* () {
        if (!e) {
          console.error("e 未定义");
          return resolve(false);
        }
        if (e.errMsg !== "getPhoneNumber:ok") {
          console.log("用户拒绝授权，不打印登录授权码和手机号授权码");
          common_vendor.index.showToast({
            title: "您已拒绝授权手机号",
            icon: "none"
          });
          return resolve(false);
        }
        let categoryText = "患者端";
        if (userCategory.value === 0) {
          categoryText = "医生端";
        } else if (userCategory.value === 2) {
          categoryText = "社工/社区医生端";
        }
        common_vendor.index.showModal({
          title: "登录确认",
          content: `您确定要登录${categoryText}吗？`,
          success: function(res) {
            return __async(this, null, function* () {
              if (res.confirm) {
                const phoneCode = e.code;
                try {
                  const codeResult = yield common_vendor.wx$1.login();
                  if (codeResult.errMsg !== "login:ok") {
                    return resolve(false);
                  }
                  const loginCode = codeResult.code;
                  console.log("登录授权码:", loginCode);
                  console.log("手机号授权码:", phoneCode);
                  common_vendor.index.request({
                    url: `${"https://www.mograine.cn/api"}/wx/login`,
                    method: "POST",
                    data: {
                      phoneCode,
                      loginCode,
                      userCategory: userCategory.value
                      // 将用户类型传给后端
                    },
                    success: (res2) => {
                      console.log("手机号授权成功返回参数", res2.data);
                      const result = res2.data.result;
                      if (result) {
                        userStore.setUserInfo({
                          token: result.token,
                          userid: result.userInfo.id,
                          username: result.userInfo.username,
                          realname: result.userInfo.realname,
                          avatar: result.userInfo.avatar,
                          phone: result.phoneNumber,
                          tenantId: result.userInfo.loginTenantId,
                          openid: result.openid,
                          sex: result.userInfo.sex,
                          userCategory: userCategory.value,
                          // 使用类型断言
                          roleList: result.userInfo.roleList,
                          caseNumber: result.userInfo.caseNumber,
                          localStorageTime: +/* @__PURE__ */ new Date()
                        });
                        console.log("userStore.userInfo", userStore.userInfo);
                        common_vendor.wx$1.setStorageSync(common_constants.ACCESS_TOKEN, result.accessToken);
                        common_vendor.wx$1.setStorageSync("user", userStore.userInfo);
                        common_vendor.index.switchTab({
                          url: "/pages/index/index"
                        });
                      } else {
                        common_vendor.index.showToast({
                          title: "登录失败，请重试",
                          icon: "none"
                        });
                      }
                    },
                    fail: (err) => {
                      console.error("请求失败", err);
                      common_vendor.index.showToast({
                        title: "网络请求失败",
                        icon: "none"
                      });
                      resolve(false);
                    }
                  });
                } catch (error) {
                  console.error("微信登录失败", error);
                  common_vendor.index.showToast({
                    title: "登录失败",
                    icon: "none"
                  });
                  resolve(false);
                }
              } else {
                console.log("用户取消登录确认");
                return resolve(false);
              }
            });
          }
        });
      }));
    };
    return (_ctx, _cache) => {
      return {
        a: compLogo.value,
        b: common_vendor.t(compTitle.value),
        c: common_vendor.p({
          text: "请选择登录类型："
        }),
        d: common_vendor.n(userCategory.value === 1 ? "active" : ""),
        e: common_vendor.o((e) => handleCategoryLogin(e, 1)),
        f: common_vendor.o(($event) => userCategory.value = 1),
        g: common_vendor.p({
          ["custom-class"]: "mt-20px",
          hairline: true,
          ["open-type"]: "getPhoneNumber"
        }),
        h: common_vendor.n(userCategory.value === 0 ? "active" : ""),
        i: common_vendor.o((e) => handleCategoryLogin(e, 0)),
        j: common_vendor.o(($event) => userCategory.value = 0),
        k: common_vendor.p({
          ["custom-class"]: "mt-20px",
          hairline: true,
          ["open-type"]: "getPhoneNumber"
        }),
        l: common_vendor.n(userCategory.value === 2 ? "active" : ""),
        m: common_vendor.o((e) => handleCategoryLogin(e, 2)),
        n: common_vendor.o(($event) => userCategory.value = 2),
        o: common_vendor.p({
          ["custom-class"]: "mt-20px",
          hairline: true,
          ["open-type"]: "getPhoneNumber"
        }),
        p: common_vendor.p({
          navbarShow: false
        })
      };
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-cdfe2409"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=login.js.map
