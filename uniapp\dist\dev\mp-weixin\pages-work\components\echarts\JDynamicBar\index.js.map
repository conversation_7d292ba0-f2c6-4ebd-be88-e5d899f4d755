{"version": 3, "file": "index.js", "sources": ["../../../../../../../src/pages-work/components/echarts/JDynamicBar/index.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvcGFnZXMtd29yay9jb21wb25lbnRzL2VjaGFydHMvSkR5bmFtaWNCYXIvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"content\">\r\n    <statusTip v-if=\"pageTips.show\" :status=\"pageTips.status\"></statusTip>\r\n\t  <echartsUniapp v-else :option=\"option\"></echartsUniapp>\r\n  </view>\r\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { echartProps } from '@/pages-work/components/echarts/props';\r\nimport {deepMerge, handleTotalAndUnit, disposeGridLayout} from '../../common/echartUtil';\r\nimport useChartHook from '@/pages-work/components/hooks/useEchart';\r\nimport { deepClone } from '@/uni_modules/da-tree/utils';\r\nimport echartsUniapp from '@/pages-work/components/echarts/index.vue';\r\nimport statusTip from '@/pages-work/components/statusTip.vue';\r\nimport { merge, pull, cloneDeep } from 'lodash-es';\r\n//组件传参\r\nconst props = defineProps({\r\n\t...echartProps\r\n})\r\n\r\n//最终图表配置项\r\nconst option = ref({});\r\nlet chartOption: any = {\r\n  title: {\r\n    show: true,\r\n  },\r\n  legend: {\r\n    show: true,\r\n    data: [],\r\n  },\r\n  xAxis: {\r\n    max: 'dataMax',\r\n    type: 'value',\r\n  },\r\n  yAxis: {\r\n    type: 'category',\r\n    inverse: false,\r\n    animationDuration: 300,\r\n    animationDurationUpdate: 300,\r\n    nameTextStyle: {\r\n      align:\"right\"\r\n    },\r\n    axisLine: {\r\n      show: true\r\n    }\r\n  },\r\n  series: [],\r\n  dataset: {\r\n    dimensions: [],\r\n    source: [],\r\n  },\r\n  animationDuration: 0,\r\n  animationDurationUpdate: 2000,\r\n  animationEasing: 'linear',\r\n  animationEasingUpdate: 'linear',\r\n};\r\n//图表数据查询\r\nlet [{ dataSource, reload, pageTips, config }, { queryData }] = useChartHook(\r\n  props,\r\n  initOption\r\n)\r\n\r\n\r\n//初始化图表配置项\r\nfunction initOption(data) {\r\n  let chartData: any = dataSource.value\r\n  if (typeof chartData === 'string') {\r\n    chartData = JSON.parse(chartData)\r\n  }\r\n  if (chartData && chartData.length > 0) {\r\n    let configOption = props.config.option;\r\n    let dataset = getDataSet(chartData);\r\n    chartOption.dataset = dataset;\r\n    chartOption.series = [];\r\n    dataset.dimensions.forEach((series, index) => {\r\n      if (index > 0) {\r\n        chartOption.series.push({\r\n          realtimeSort: true,\r\n          type: 'bar', //TODO 自定义图表类型\r\n          color: '', //TODO 自定义颜色\r\n          series: series, //TODO 系列，冗余数据，只是table展示使用\r\n          label: {\r\n            show: true,\r\n            position: 'right',\r\n            valueAnimation: true,\r\n          },\r\n        });\r\n      }\r\n    });\r\n    chartOption.legend.data = chartOption.series.map((item) => item.series).filter(type=>type);\r\n    chartOption.yAxis.type = pull(['category', 'value'], configOption?.xAxis?.type)[0];\r\n    // 合并配置\r\n    if (props.config && config.option) {\r\n      merge(chartOption, config.option)\r\n      synchSeries(chartOption, props?.config?.option?.series);\r\n\t\t  option.value = deepClone(chartOption)\r\n\t\t  pageTips.show = false\r\n    }\r\n  } else {\r\n    pageTips.status = 1\r\n    pageTips.show = true\r\n  }\r\n}\r\n\r\n/**\r\n * 同步图例和系列\r\n * @param chartOption\r\n * @param series\r\n */\r\nfunction synchSeries (chartOption, series) {\r\n  if (chartOption?.series?.length && series?.length) {\r\n    const _series = cloneDeep(series);\r\n    if (_series[0]?.itemStyle?.color) {\r\n      // 不同步柱体颜色\r\n      delete _series[0]?.itemStyle?.color;\r\n    };\r\n    chartOption.series.forEach(() => {\r\n      _series.push(_series[0]);\r\n    });\r\n    merge(chartOption, { series: _series });\r\n  }\r\n};\r\n\r\n/**\r\n * 获取数据集\r\n * @param chartData\r\n */\r\nfunction getDataSet(chartData) {\r\n  let dataObj = { dimensions: [], source: [] };\r\n  let dataList = [];\r\n  //获取系列\r\n  let seriesArr:any = chartData.map((item:any) => item['type']);\r\n  // @ts-ignore\r\n  let dimensions = seriesArr && seriesArr.length > 0 ? ['dynamic', ...new Set(seriesArr)] : [];\r\n  //获取name\r\n  // @ts-ignore\r\n  let nameArr = [...new Set(chartData.map((item:any) => item['name']))];\r\n  //遍历name获取value\r\n  nameArr.forEach((name, index) => {\r\n    //筛选出指定name的对象集合\r\n    let arr = chartData.filter((item) => item['name'] == name);\r\n    //获取对象集合的value\r\n    let valueList = arr.map((item) => item['value']);\r\n    //首位置存放的是当前name\r\n    valueList.unshift(name);\r\n    dataList.push(valueList);\r\n  });\r\n  dataObj.dimensions = dimensions;\r\n  dataObj.source = dataList;\r\n  return dataObj;\r\n}\r\nonMounted(()=>{\r\n\tqueryData();\r\n})\r\n\r\n</script>\r\n<style>\r\n.content {\r\n  padding: 10px;\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-work/components/echarts/JDynamicBar/index.vue'\nwx.createComponent(Component)"], "names": ["ref", "useChartHook", "pull", "merge", "deepClone", "chartOption", "cloneDeep", "onMounted", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAYA,MAAA,gBAA0B,MAAA;AAC1B,MAAA,YAAsB,MAAA;;;;;AAGtB,UAAM,QAAQ;AAKR,UAAA,SAASA,cAAI,IAAA,EAAE;AACrB,QAAI,cAAmB;AAAA,MACrB,OAAO;AAAA,QACL,MAAM;AAAA,MACR;AAAA,MACA,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,MAAM,CAAA;AAAA,MACR;AAAA,MACA,OAAO;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,MACR;AAAA,MACA,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,QACT,mBAAmB;AAAA,QACnB,yBAAyB;AAAA,QACzB,eAAe;AAAA,UACb,OAAM;AAAA,QACR;AAAA,QACA,UAAU;AAAA,UACR,MAAM;AAAA,QAAA;AAAA,MAEV;AAAA,MACA,QAAQ,CAAC;AAAA,MACT,SAAS;AAAA,QACP,YAAY,CAAC;AAAA,QACb,QAAQ,CAAA;AAAA,MACV;AAAA,MACA,mBAAmB;AAAA,MACnB,yBAAyB;AAAA,MACzB,iBAAiB;AAAA,MACjB,uBAAuB;AAAA,IACzB;AAEI,QAAA,CAAC,EAAE,YAAY,QAAQ,UAAU,UAAU,EAAE,UAAW,CAAA,IAAIC,qCAAA;AAAA,MAC9D;AAAA,MACA;AAAA,IACF;AAIA,aAAS,WAAW,MAAM;;AACxB,UAAI,YAAiB,WAAW;AAC5B,UAAA,OAAO,cAAc,UAAU;AACrB,oBAAA,KAAK,MAAM,SAAS;AAAA,MAAA;AAE9B,UAAA,aAAa,UAAU,SAAS,GAAG;AACjC,YAAA,eAAe,MAAM,OAAO;AAC5B,YAAA,UAAU,WAAW,SAAS;AAClC,oBAAY,UAAU;AACtB,oBAAY,SAAS,CAAC;AACtB,gBAAQ,WAAW,QAAQ,CAAC,QAAQ,UAAU;AAC5C,cAAI,QAAQ,GAAG;AACb,wBAAY,OAAO,KAAK;AAAA,cACtB,cAAc;AAAA,cACd,MAAM;AAAA;AAAA,cACN,OAAO;AAAA;AAAA,cACP;AAAA;AAAA,cACA,OAAO;AAAA,gBACL,MAAM;AAAA,gBACN,UAAU;AAAA,gBACV,gBAAgB;AAAA,cAAA;AAAA,YAClB,CACD;AAAA,UAAA;AAAA,QACH,CACD;AACD,oBAAY,OAAO,OAAO,YAAY,OAAO,IAAI,CAAC,SAAS,KAAK,MAAM,EAAE,OAAO,CAAA,SAAM,IAAI;AAC7E,oBAAA,MAAM,OAAOC,cAAAA,KAAK,CAAC,YAAY,OAAO,IAAG,kDAAc,UAAd,mBAAqB,IAAI,EAAE,CAAC;AAE7E,YAAA,MAAM,UAAU,OAAO,QAAQ;AAC3BC,8BAAA,aAAa,OAAO,MAAM;AAChC,sBAAY,cAAa,0CAAO,WAAP,mBAAe,WAAf,mBAAuB,MAAM;AACjD,iBAAA,QAAQC,mCAAU,WAAW;AACpC,mBAAS,OAAO;AAAA,QAAA;AAAA,MAChB,OACK;AACL,iBAAS,SAAS;AAClB,iBAAS,OAAO;AAAA,MAAA;AAAA,IAClB;AAQO,aAAA,YAAaC,cAAa,QAAQ;;AACzC,YAAIA,kDAAa,WAAbA,mBAAqB,YAAU,iCAAQ,SAAQ;AAC3C,cAAA,UAAUC,wBAAU,MAAM;AAChC,aAAI,mBAAQ,CAAC,MAAT,mBAAY,cAAZ,mBAAuB,OAAO;AAEzB,iBAAA,aAAQ,CAAC,MAAT,mBAAY,cAAZ,wBAAuB;AAAA,QAAA;AAEhCD,qBAAY,OAAO,QAAQ,MAAM;AACvB,kBAAA,KAAK,QAAQ,CAAC,CAAC;AAAA,QAAA,CACxB;AACDF,sBAAAA,MAAME,cAAa,EAAE,QAAQ,QAAA,CAAS;AAAA,MAAA;AAAA,IACxC;AAOF,aAAS,WAAW,WAAW;AAC7B,UAAI,UAAU,EAAE,YAAY,CAAA,GAAI,QAAQ,CAAA,EAAG;AAC3C,UAAI,WAAW,CAAC;AAEhB,UAAI,YAAgB,UAAU,IAAI,CAAC,SAAa,KAAK,MAAM,CAAC;AAE5D,UAAI,aAAa,aAAa,UAAU,SAAS,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,SAAS,CAAC,IAAI,CAAC;AAG3F,UAAI,UAAU,CAAC,GAAG,IAAI,IAAI,UAAU,IAAI,CAAC,SAAa,KAAK,MAAM,CAAC,CAAC,CAAC;AAE5D,cAAA,QAAQ,CAAC,MAAM,UAAU;AAE3B,YAAA,MAAM,UAAU,OAAO,CAAC,SAAS,KAAK,MAAM,KAAK,IAAI;AAEzD,YAAI,YAAY,IAAI,IAAI,CAAC,SAAS,KAAK,OAAO,CAAC;AAE/C,kBAAU,QAAQ,IAAI;AACtB,iBAAS,KAAK,SAAS;AAAA,MAAA,CACxB;AACD,cAAQ,aAAa;AACrB,cAAQ,SAAS;AACV,aAAA;AAAA,IAAA;AAETE,kBAAAA,UAAU,MAAI;AACH,gBAAA;AAAA,IAAA,CACV;;;;;;;;;;;;;;;;ACxJD,GAAG,gBAAgBC,SAAS;"}