"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../../common/vendor.js");
if (!Math) {
  (wdIcon + wdButton)();
}
const wdIcon = () => "../wd-icon/wd-icon.js";
const wdButton = () => "../wd-button/wd-button.js";
const __default__ = {
  name: "wd-pagination",
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: "shared"
  }
};
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, __default__), {
  props: common_vendor.paginationProps,
  emits: ["change", "update:modelValue"],
  setup(__props, { emit: __emit }) {
    const { translate } = common_vendor.useTranslate("pagination");
    const props = __props;
    const emit = __emit;
    const totalPageNum = common_vendor.ref(0);
    common_vendor.watch(
      () => props.totalPage,
      (newValue) => {
        if (!totalPageNum.value && newValue) {
          totalPageNum.value = newValue;
        }
      },
      { immediate: true, deep: true }
    );
    common_vendor.watch(
      () => props.total,
      () => {
        updateTotalPage();
      },
      { immediate: true, deep: true }
    );
    function add() {
      const { modelValue } = props;
      if (modelValue > totalPageNum.value - 1) {
        return;
      }
      emit("change", { value: modelValue + 1 });
      emit("update:modelValue", modelValue + 1);
    }
    function sub() {
      const { modelValue } = props;
      if (modelValue < 2) {
        return;
      }
      emit("change", { value: modelValue - 1 });
      emit("update:modelValue", modelValue - 1);
    }
    function updateTotalPage() {
      const { total, pageSize } = props;
      totalPageNum.value = Math.ceil(total / pageSize);
    }
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: !(_ctx.hideIfOnePage && totalPageNum.value === 1)
      }, !(_ctx.hideIfOnePage && totalPageNum.value === 1) ? common_vendor.e$1({
        b: !_ctx.showIcon
      }, !_ctx.showIcon ? {
        c: common_vendor.t(_ctx.prevText || common_vendor.unref(translate)("prev"))
      } : {
        d: common_vendor.p({
          ["custom-class"]: `wd-pager__left wd-pager__icon ${_ctx.modelValue <= 1 ? "wd-pager__nav--disabled" : "wd-pager__nav--active"}`,
          name: "arrow-right"
        })
      }, {
        e: common_vendor.o(sub),
        f: common_vendor.p({
          plain: _ctx.modelValue > 1,
          type: "info",
          size: "small",
          disabled: _ctx.modelValue <= 1,
          ["custom-class"]: "wd-pager__nav"
        }),
        g: common_vendor.t(_ctx.modelValue),
        h: common_vendor.t(totalPageNum.value),
        i: !_ctx.showIcon
      }, !_ctx.showIcon ? {
        j: common_vendor.t(_ctx.nextText || common_vendor.unref(translate)("next"))
      } : {
        k: common_vendor.p({
          ["custom-class"]: `wd-pager__icon ${_ctx.modelValue >= totalPageNum.value ? "wd-pager__nav--disabled" : "wd-pager__nav--active"}`,
          name: "arrow-right"
        })
      }, {
        l: common_vendor.o(add),
        m: common_vendor.p({
          plain: _ctx.modelValue < totalPageNum.value,
          type: "info",
          size: "small",
          disabled: _ctx.modelValue >= totalPageNum.value,
          ["custom-class"]: "wd-pager__nav"
        }),
        n: _ctx.showMessage
      }, _ctx.showMessage ? common_vendor.e$1({
        o: common_vendor.t(common_vendor.unref(translate)("page", _ctx.modelValue)),
        p: _ctx.total
      }, _ctx.total ? {
        q: common_vendor.t(common_vendor.unref(translate)("total", _ctx.total))
      } : {}, {
        r: common_vendor.t(common_vendor.unref(translate)("size", _ctx.pageSize))
      }) : {}, {
        s: common_vendor.n(`wd-pager ${_ctx.customClass}`),
        t: common_vendor.s(_ctx.customStyle)
      }) : {});
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-51068025"]]);
wx.createComponent(Component);
//# sourceMappingURL=wd-pagination.js.map
