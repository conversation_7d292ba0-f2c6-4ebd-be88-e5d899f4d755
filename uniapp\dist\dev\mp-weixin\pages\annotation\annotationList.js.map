{"version": 3, "file": "annotationList.js", "sources": ["../../../../../src/pages/annotation/annotationList.vue", "../../../../../uniPage:/cGFnZXMvYW5ub3RhdGlvbi9hbm5vdGF0aW9uTGlzdC52dWU"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n  <PageLayout navTitle=\"我的消息\" :backRouteName=\"backRouteName\" routeMethod=\"pushTab\">\r\n    <view class=\"wrap\">\r\n      <z-paging ref=\"paging\" :fixed=\"false\" v-model=\"dataList\" @query=\"queryList\">\r\n        <template v-for=\"(item, index) in dataList\">\r\n          <wd-swipe-action>\r\n            <view class=\"list bg-white\" @click=\"showDetail(item)\">\r\n              <view class=\"cIcon\">\r\n                <view\r\n                  v-if=\"['email'].includes(item.busType)\"\r\n                  class=\"u-iconfont u-icon-email\"\r\n                ></view>\r\n                <view\r\n                  v-else-if=\"['bpm_task', 'bpm'].includes(item.busType)\"\r\n                  class=\"u-iconfont u-icon-bpm\"\r\n                ></view>\r\n                <view\r\n                  v-else-if=\"['msgCategory'].includes(item.busType)\"\r\n                  class=\"u-iconfont u-icon-message\"\r\n                ></view>\r\n                <view v-else class=\"u-iconfont u-icon-msg\"></view>\r\n              </view>\r\n              <view class=\"content\">\r\n                <text class=\"title ellipsis\">{{ item.titile }}</text>\r\n                <text class=\"desc\">{{ getDesc(item) }}</text>\r\n              </view>\r\n              <view class=\"operate\">\r\n                <view class=\"star-area\" @click.stop=\"changeStarFlag(item)\">\r\n                  <view\r\n                    v-if=\"item.starFlag == '1'\"\r\n                    class=\"u-iconfont u-icon-star-fill\"\r\n                    style=\"color: #f7de2d\"\r\n                  ></view>\r\n                  <view v-else class=\"u-iconfont u-icon-star\" style=\"color: #777\"></view>\r\n                </view>\r\n                <text class=\"time\">{{ item.sendTime?.substring(0, 10) }}</text>\r\n              </view>\r\n            </view>\r\n            <template #right>\r\n              <view class=\"action\">\r\n                <view class=\"button\" @click=\"handleAction('del', item)\">删除</view>\r\n              </view>\r\n            </template>\r\n          </wd-swipe-action>\r\n        </template>\r\n      </z-paging>\r\n    </view>\r\n    <template #navRight>\r\n      <view\r\n        class=\"cuIcon-filter font-size-20px color-white\"\r\n        @click=\"() => (conditionFilter.show = true)\"\r\n      ></view>\r\n    </template>\r\n    <rightConditionFilter\r\n      v-if=\"conditionFilter.show\"\r\n      @close=\"() => (conditionFilter.show = false)\"\r\n      :starFlag=\"starFlag\"\r\n      :conditionStartDate=\"conditionStartDate\"\r\n      :conditionEndDate=\"conditionEndDate\"\r\n      @change=\"handleFilterChange\"\r\n    ></rightConditionFilter>\r\n  </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\n//\r\nimport { onLaunch, onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'\r\nimport { http } from '@/utils/http'\r\nimport { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'\r\nimport { useRouter } from '@/plugin/uni-mini-router'\r\nimport rightConditionFilter from './components/rightConditionFilter.vue'\r\n\r\ndefineOptions({\r\n  name: 'annotationList',\r\n  options: {\r\n    // apply-shared‌：当前页面样式会影响到子组件样式.(小程序)\r\n    // shared‌：当前页面样式影响到子组件，子组件样式也会影响到当前页面.(小程序)\r\n    styleIsolation: '‌apply-shared‌',\r\n  },\r\n})\r\nconst toast = useToast()\r\nconst router = useRouter()\r\nconst paging = ref(null)\r\nconst dataList = ref([])\r\nconst starFlag = ref('')\r\nconst conditionFilter = reactive({ show: false })\r\nconst backRouteName = ref('index')\r\n// 开始时间结束时间\r\nconst conditionStartDate = ref(null)\r\nconst conditionEndDate = ref(null)\r\nconst getParams = ({ pageNo, pageSize }) => {\r\n  let result: any = {\r\n    pageNo,\r\n    pageSize,\r\n    starFlag: starFlag.value,\r\n    rangeDateKey: 'zdy',\r\n  }\r\n  if (conditionStartDate.value) {\r\n    result.beginDate = dayjs(conditionStartDate.value).format('YYYY-MM-DD') + ' 00:00:00'\r\n  } else {\r\n    result.beginDate = ''\r\n  }\r\n  if (conditionEndDate.value) {\r\n    result.endDate = dayjs(conditionEndDate.value).format('YYYY-MM-DD') + ' 23:59:59'\r\n  } else {\r\n    result.endDate = ''\r\n  }\r\n  return result\r\n}\r\n// @query所绑定的方法不要自己调用！！需要刷新列表数据时，只需要调用paging.value.reload()即可\r\nconst queryList = (pageNo, pageSize) => {\r\n  const params = getParams({ pageNo, pageSize })\r\n  http\r\n    .get('/sys/annountCement/vue3List', { ...params })\r\n    .then((res: any) => {\r\n      if (res.success) {\r\n        paging.value.complete(res.result)\r\n      } else {\r\n        paging.value.complete(false)\r\n      }\r\n    })\r\n    .catch((res) => {\r\n      paging.value.complete(false)\r\n    })\r\n}\r\nconst showDetail = (record) => {\r\n  if (record.busType == 'email') {\r\n    goEmailDetailPage(record)\r\n  } else if (record.busType == 'bpm') {\r\n    // goBpmList(record.busId)\r\n    toast.warning('暂未实现~')\r\n  } else if (record.busType == 'bpm_task') {\r\n    // goBpmList(record.busId)\r\n    toast.warning('暂未实现~')\r\n  } else {\r\n    uni.navigateTo({\r\n      url: '/pages/annotation/annotationDetail?item=' + encodeURIComponent(JSON.stringify(record)),\r\n    })\r\n  }\r\n}\r\nconst goEmailDetailPage = (item) => {\r\n  if (item.readFlag == '0') {\r\n    paging.value.reload()\r\n    let readUrl = '/sys/sysAnnouncementSend/editByAnntIdAndUserId'\r\n    http.put(readUrl, { anntId: item.anntId })\r\n  }\r\n  uni.navigateTo({\r\n    url: '/pages/mail/mailDetail?id=' + item.busId,\r\n  })\r\n}\r\nconst goBpmList = (taskId) => {\r\n  const url = '/act/process/extActProcessNode/getProcessNodeInfo'\r\n  let params = { taskId: taskId, datatimes: new Date().getTime() }\r\n  http.get(url, { params }).then((res: any) => {\r\n    console.log('000>>', res)\r\n    const data = res.result\r\n    if (data.taskIsHandel == true) {\r\n      toast.show('任务已经处理完成!')\r\n    } else {\r\n      let params = {\r\n        id: taskId,\r\n        taskId: taskId,\r\n        isSignTask: data.isSignTask,\r\n        taskDefKey: data.taskDefKey,\r\n        instanceId: data.records.BPM_INST_ID,\r\n        pageCur: 'peoplelis',\r\n      }\r\n      console.log('goBpmList***********params>>', params)\r\n\r\n\r\n\r\n\r\n      let url = `/pages/index/index?pageCur=peoplelis&id=${taskId}&taskId=${taskId}&isSignTask=${data.isSignTask}&taskDefKey=${data.taskDefKey}&instanceId=${data.records.BPM_INST_ID}`\r\n      uni.reLaunch({\r\n        url: url,\r\n      })\r\n\r\n    }\r\n  })\r\n}\r\n\r\nconst handleFilterChange = ([flag, startTime, endTime]) => {\r\n  starFlag.value = flag\r\n  startTime && (conditionStartDate.value = startTime)\r\n  endTime && (conditionEndDate.value = endTime)\r\n  paging.value.reload()\r\n}\r\n// 收藏与取消收藏\r\nconst changeStarFlag = (item) => {\r\n  const url = '/sys/sysAnnouncementSend/edit'\r\n  let starFlag = '1'\r\n  if (item.starFlag == starFlag) {\r\n    starFlag = '0'\r\n  }\r\n  const params = {\r\n    starFlag,\r\n    id: item.sendId,\r\n  }\r\n  http.put(url, params).then((res: any) => {\r\n    if (res.success) {\r\n      item.starFlag = starFlag\r\n    } else {\r\n      toast.warning(res.message)\r\n    }\r\n  })\r\n}\r\n// 滑动删除\r\nconst handleAction = (flag, item) => {\r\n  http\r\n    .delete('/sys/sysAnnouncementSend/delete', { id: item.sendId })\r\n    .then((res: any) => {\r\n      if (res.success) {\r\n        paging.value.reload()\r\n      }\r\n    })\r\n    .catch((e) => {\r\n      console.log('al delUrl请求错误2', e)\r\n    })\r\n}\r\n// 根据类型获取描述\r\nconst getDesc = (item) => {\r\n  if (item.busType == 'email') {\r\n    // 邮件提醒\r\n    return '您收到一封新的邮件，请及时处理。'\r\n  } else if (item.busType == 'bpm') {\r\n    // 流程催办\r\n    return '您收到一条流程催办，请及时处理。'\r\n  } else if (item.busType == 'bpm_task') {\r\n    // 流程任务\r\n    return '您收到一条流程任务，请及时处理。'\r\n  } else if (item.msgCategory == '2') {\r\n    // 系统消息\r\n    return '您收到一条系统消息，请及时处理。'\r\n  } else if (item.msgCategory == '1') {\r\n    // 通知公告\r\n    return '您收到一条通知公告，请及查看。'\r\n  }\r\n}\r\nonLoad((options) => {\r\n  if (options?.backRouteName) {\r\n    backRouteName.value = options.backRouteName\r\n  }\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n//\r\n.wrap {\r\n  height: 100%;\r\n}\r\n.wd-swipe-action {\r\n  &:first-child {\r\n    margin-top: 10px;\r\n  }\r\n}\r\n.list {\r\n  padding: 14px 14px;\r\n  background-color: #fff;\r\n  border-bottom: 1px solid #eee;\r\n  margin-bottom: 10px;\r\n  display: flex;\r\n  align-items: center;\r\n  .cIcon {\r\n    flex: none;\r\n    text-align: center;\r\n    line-height: 33px;\r\n    width: 33px;\r\n    height: 33px;\r\n    background-color: #f37b1d;\r\n    color: #fff;\r\n    border-radius: 4px;\r\n    margin-right: 10px;\r\n  }\r\n  .content {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    margin-right: 20px;\r\n    overflow: hidden;\r\n    .title {\r\n      font-size: 15px;\r\n      margin-bottom: 2px;\r\n    }\r\n    .desc {\r\n      font-size: 13px;\r\n      color: rgb(153, 153, 153);\r\n    }\r\n  }\r\n  .operate {\r\n    text-align: right;\r\n    width: 70px;\r\n    .u-iconfont {\r\n      font-size: 20px;\r\n      margin-bottom: 8px;\r\n    }\r\n    .time {\r\n      font-size: 12px;\r\n      color: #aaa;\r\n    }\r\n  }\r\n}\r\n.action {\r\n  width: 70px;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  .button {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    flex: 1;\r\n    height: 100%;\r\n    color: #fff;\r\n    &:first-child {\r\n      background-color: #fa4350;\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages/annotation/annotationList.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useToast", "useRouter", "ref", "reactive", "dayjs", "http", "uni", "starFlag", "onLoad"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0EA,MAAA,uBAAiC,MAAA;;;;;;;;;;;AAUjC,UAAM,QAAQA,cAAAA,SAAS;AACRC,oCAAU,UAAA;AACnB,UAAA,SAASC,kBAAI,IAAI;AACjB,UAAA,WAAWA,cAAI,IAAA,EAAE;AACjB,UAAA,WAAWA,kBAAI,EAAE;AACvB,UAAM,kBAAkBC,cAAA,SAAS,EAAE,MAAM,OAAO;AAC1C,UAAA,gBAAgBD,kBAAI,OAAO;AAE3B,UAAA,qBAAqBA,kBAAI,IAAI;AAC7B,UAAA,mBAAmBA,kBAAI,IAAI;AACjC,UAAM,YAAY,CAAC,EAAE,QAAQ,eAAe;AAC1C,UAAI,SAAc;AAAA,QAChB;AAAA,QACA;AAAA,QACA,UAAU,SAAS;AAAA,QACnB,cAAc;AAAA,MAChB;AACA,UAAI,mBAAmB,OAAO;AAC5B,eAAO,YAAYE,cAAAA,QAAM,mBAAmB,KAAK,EAAE,OAAO,YAAY,IAAI;AAAA,MAAA,OACrE;AACL,eAAO,YAAY;AAAA,MAAA;AAErB,UAAI,iBAAiB,OAAO;AAC1B,eAAO,UAAUA,cAAAA,QAAM,iBAAiB,KAAK,EAAE,OAAO,YAAY,IAAI;AAAA,MAAA,OACjE;AACL,eAAO,UAAU;AAAA,MAAA;AAEZ,aAAA;AAAA,IACT;AAEM,UAAA,YAAY,CAAC,QAAQ,aAAa;AACtC,YAAM,SAAS,UAAU,EAAE,QAAQ,UAAU;AAE1CC,sBAAA,IAAI,+BAA+B,mBAAK,OAAQ,EAChD,KAAK,CAAC,QAAa;AAClB,YAAI,IAAI,SAAS;AACR,iBAAA,MAAM,SAAS,IAAI,MAAM;AAAA,QAAA,OAC3B;AACE,iBAAA,MAAM,SAAS,KAAK;AAAA,QAAA;AAAA,MAC7B,CACD,EACA,MAAM,CAAC,QAAQ;AACP,eAAA,MAAM,SAAS,KAAK;AAAA,MAAA,CAC5B;AAAA,IACL;AACM,UAAA,aAAa,CAAC,WAAW;AACzB,UAAA,OAAO,WAAW,SAAS;AAC7B,0BAAkB,MAAM;AAAA,MAAA,WACf,OAAO,WAAW,OAAO;AAElC,cAAM,QAAQ,OAAO;AAAA,MAAA,WACZ,OAAO,WAAW,YAAY;AAEvC,cAAM,QAAQ,OAAO;AAAA,MAAA,OAChB;AACLC,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,6CAA6C,mBAAmB,KAAK,UAAU,MAAM,CAAC;AAAA,QAAA,CAC5F;AAAA,MAAA;AAAA,IAEL;AACM,UAAA,oBAAoB,CAAC,SAAS;AAC9B,UAAA,KAAK,YAAY,KAAK;AACxB,eAAO,MAAM,OAAO;AACpB,YAAI,UAAU;AACdD,mBAAA,KAAK,IAAI,SAAS,EAAE,QAAQ,KAAK,QAAQ;AAAA,MAAA;AAE3CC,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,+BAA+B,KAAK;AAAA,MAAA,CAC1C;AAAA,IACH;AAgCA,UAAM,qBAAqB,CAAC,CAAC,MAAM,WAAW,OAAO,MAAM;AACzD,eAAS,QAAQ;AACjB,oBAAc,mBAAmB,QAAQ;AACzC,kBAAY,iBAAiB,QAAQ;AACrC,aAAO,MAAM,OAAO;AAAA,IACtB;AAEM,UAAA,iBAAiB,CAAC,SAAS;AAC/B,YAAM,MAAM;AACZ,UAAIC,YAAW;AACX,UAAA,KAAK,YAAYA,WAAU;AAC7BA,oBAAW;AAAA,MAAA;AAEb,YAAM,SAAS;AAAA,QACb,UAAAA;AAAAA,QACA,IAAI,KAAK;AAAA,MACX;AACAF,iBAAA,KAAK,IAAI,KAAK,MAAM,EAAE,KAAK,CAAC,QAAa;AACvC,YAAI,IAAI,SAAS;AACf,eAAK,WAAWE;AAAAA,QAAA,OACX;AACC,gBAAA,QAAQ,IAAI,OAAO;AAAA,QAAA;AAAA,MAC3B,CACD;AAAA,IACH;AAEM,UAAA,eAAe,CAAC,MAAM,SAAS;AAEhCF,sBAAA,OAAO,mCAAmC,EAAE,IAAI,KAAK,QAAQ,EAC7D,KAAK,CAAC,QAAa;AAClB,YAAI,IAAI,SAAS;AACf,iBAAO,MAAM,OAAO;AAAA,QAAA;AAAA,MACtB,CACD,EACA,MAAM,CAAC,MAAM;AACJ,gBAAA,IAAI,kBAAkB,CAAC;AAAA,MAAA,CAChC;AAAA,IACL;AAEM,UAAA,UAAU,CAAC,SAAS;AACpB,UAAA,KAAK,WAAW,SAAS;AAEpB,eAAA;AAAA,MAAA,WACE,KAAK,WAAW,OAAO;AAEzB,eAAA;AAAA,MAAA,WACE,KAAK,WAAW,YAAY;AAE9B,eAAA;AAAA,MAAA,WACE,KAAK,eAAe,KAAK;AAE3B,eAAA;AAAA,MAAA,WACE,KAAK,eAAe,KAAK;AAE3B,eAAA;AAAA,MAAA;AAAA,IAEX;AACAG,kBAAA,OAAO,CAAC,YAAY;AAClB,UAAI,mCAAS,eAAe;AAC1B,sBAAc,QAAQ,QAAQ;AAAA,MAAA;AAAA,IAChC,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrPD,GAAG,WAAW,eAAe;"}