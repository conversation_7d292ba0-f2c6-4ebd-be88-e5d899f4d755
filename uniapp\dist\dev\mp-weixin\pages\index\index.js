"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
require("../../plugin/uni-mini-router/router/index.js");
const plugin_uniMiniRouter_core_index = require("../../plugin/uni-mini-router/core/index.js");
const store_user = require("../../store/user.js");
const utils_http = require("../../utils/http.js");
if (!Array) {
  const _easycom_wd_text2 = common_vendor.resolveComponent("wd-text");
  const _easycom_wd_loading2 = common_vendor.resolveComponent("wd-loading");
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_text2 + _easycom_wd_loading2 + _easycom_uni_icons2 + _easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_wd_text = () => "../../node-modules/wot-design-uni/components/wd-text/wd-text.js";
const _easycom_wd_loading = () => "../../node-modules/wot-design-uni/components/wd-loading/wd-loading.js";
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  (_easycom_wd_text + _easycom_wd_loading + _easycom_uni_icons + _easycom_PageLayout)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "index",
  options: {
    // apply-shared‌：当前页面样式会影响到子组件样式.(小程序)
    // shared‌：当前页面样式影响到子组件，子组件样式也会影响到当前页面.(小程序)
    styleIsolation: "shared"
  }
}), {
  __name: "index",
  setup(__props) {
    const currentSwiper = common_vendor.ref(0);
    const currentSwiperTitle = common_vendor.computed(() => {
      var _a;
      if (carouselList.value.length === 0)
        return "";
      return ((_a = carouselList.value[currentSwiper.value]) == null ? void 0 : _a.title) || "";
    });
    const onSwiperChange = (e) => {
      currentSwiper.value = e.detail.current;
    };
    common_vendor.useToast();
    plugin_uniMiniRouter_core_index.useRouter();
    const userStore = store_user.useUserStore();
    const { safeAreaInsets } = common_vendor.index.getSystemInfoSync();
    const carouselList = common_vendor.ref([]);
    common_vendor.ref([]);
    common_vendor.index.getStorageSync("token");
    const getRealFileUrl = (objectName) => __async(this, null, function* () {
      console.log("获取文件URL，objectName:", objectName);
      try {
        const res = yield common_vendor.index.request({
          url: `${"https://www.mograine.cn/api"}/file/url`,
          method: "GET",
          data: { objectName },
          header: {
            "X-Access-Token": userStore.userInfo.token
          }
        });
        let data = res && res.data ? res.data : Array.isArray(res) && res[1] && res[1].data ? res[1].data : null;
        if (data && data.success && data.result && data.result.fileUrl) {
          return data.result.fileUrl;
        } else {
          console.warn("响应数据格式异常:", data);
          return "";
        }
      } catch (error) {
        console.error("获取文件URL失败:", error);
        return "";
      }
    });
    const fetchCarouselList = () => __async(this, null, function* () {
      const token2 = userStore.userInfo.token;
      console.log("当前TOKEN:", token2);
      const res = yield common_vendor.index.request({
        url: `${"https://www.mograine.cn/api"}/educationResource/showList`,
        method: "GET",
        header: {
          "X-Access-Token": userStore.userInfo.token
        }
      });
      console.log("获取宣教资源轮播图数据:", res);
      let data = res && res.data ? res.data : Array.isArray(res) && res[1] && res[1].data ? res[1].data : null;
      if (data && data.success && Array.isArray(data.result)) {
        const posts = data.result;
        const imgPromises = posts.map((item) => item.image ? getRealFileUrl(item.image) : "");
        const imgUrls = yield Promise.all(imgPromises);
        const videoPromises = posts.map((item) => item.videoName ? getRealFileUrl(item.videoName) : "");
        const videoUrls = yield Promise.all(videoPromises);
        carouselList.value = posts.map((item, idx) => __spreadProps(__spreadValues({}, item), {
          imageUrl: imgUrls[idx],
          videoUrl: videoUrls[idx]
        }));
      } else {
        carouselList.value = [];
      }
    });
    const onSwiperClick = (item) => {
      common_vendor.index.navigateTo({
        url: `/pages/index/postDetail?title=${encodeURIComponent(item.title)}&videoUrl=${encodeURIComponent(item.videoUrl)}&content=${encodeURIComponent(item.content)}&type=${encodeURIComponent(item.type)}&author=${encodeURIComponent(item.author)}&createTime=${encodeURIComponent(item.createTime)}&updateTime=${encodeURIComponent(item.updateTime)}`
      });
    };
    common_vendor.ref(0);
    common_vendor.ref({ mailHome: false });
    const recentDataList = common_vendor.ref([]);
    const loadingRecentData = common_vendor.ref(false);
    const isPatientRole = common_vendor.computed(() => {
      var _a;
      return ((_a = userStore.userInfo.roleList) == null ? void 0 : _a.includes("1")) || false;
    });
    const isDocterRole = common_vendor.computed(() => {
      var _a;
      return ((_a = userStore.userInfo.roleList) == null ? void 0 : _a.includes("0")) || false;
    });
    const isSocialRole = common_vendor.computed(() => {
      var _a;
      return ((_a = userStore.userInfo.roleList) == null ? void 0 : _a.includes("2")) || false;
    });
    const hasCommAndDataPermission = common_vendor.computed(() => {
      const userRoles = userStore.userInfo.roleList || [];
      const userCategory = Number(userStore.userInfo.userCategory);
      if (userCategory === 0) {
        return userRoles.includes("0");
      } else if (userCategory === 1) {
        return userRoles.includes("1");
      } else if (userCategory === 2) {
        return userRoles.includes("2");
      }
      return false;
    });
    const getIconByType = (type) => {
      const iconMap = {
        "日常体征监测": "https://www.mograine.cn/images/vital-signs.png",
        "用药情况": "https://www.mograine.cn/images/medication.png",
        "院外检查报告": "https://www.mograine.cn/images/report.png",
        "监测表": "https://www.mograine.cn/images/monitor.png",
        "心理量表": "https://www.mograine.cn/images/psychology.png",
        "日常生活指数评估": "https://www.mograine.cn/images/dailyLife.png",
        "知情同意与登记表": "https://www.mograine.cn/images/registration.png"
      };
      return iconMap[type];
    };
    const goToDataPage = () => {
      common_vendor.index.navigateTo({
        url: "/pages/index/data"
      });
    };
    const viewDataDetail = (item) => {
      console.log("查看详情，数据类型:", item.type);
      let targetUrl = "";
      let urlParams = "";
      switch (item.type) {
        case "日常体征监测":
          targetUrl = "/pages-data/vital-signs/form";
          common_vendor.index.setStorageSync("detail_query", { id: item.id });
          urlParams = `id=${item.id}&mode=view`;
          break;
        case "用药情况":
          targetUrl = "/pages-data/medication/form";
          urlParams = `idList=${item.idList.join(",")}&mode=view`;
          break;
        case "院外检查报告":
          targetUrl = "/pages-data/examinationReport/form";
          urlParams = `idList=${item.idList.join(",")}&mode=view`;
          break;
        case "监测表":
          targetUrl = "/pages-data/monitor/form";
          common_vendor.index.setStorageSync("detail_query", { id: item.id });
          urlParams = `id=${item.id}&mode=view`;
          break;
        case "心理量表":
          targetUrl = "/pages-data/psychology/form";
          common_vendor.index.setStorageSync("detail_query", { id: item.id });
          urlParams = `id=${item.id}&mode=view`;
          break;
        case "日常生活指数评估":
          targetUrl = "/pages-data/dailyLife/form";
          common_vendor.index.setStorageSync("detail_query", { id: item.id });
          urlParams = `id=${item.id}&mode=view`;
          break;
        default:
          targetUrl = "";
          urlParams = "";
      }
      console.log("跳转到页面:", targetUrl);
      if (targetUrl) {
        common_vendor.index.navigateTo({
          url: `${targetUrl}?${urlParams}`
        });
      }
    };
    const getRecentData = () => {
      if (!hasCommAndDataPermission.value) {
        recentDataList.value = [];
        return;
      }
      loadingRecentData.value = true;
      const params = {};
      if (Number(userStore.userInfo.userCategory) === 1) {
        params.userId = userStore.userInfo.userid;
      }
      utils_http.http.get("/patient/getalldata", params).then((res) => {
        if (res.success && res.result) {
          recentDataList.value = (res.result.records || []).filter((item) => item.type !== "知情同意与登记表").slice(0, 5).map((item) => {
            const iconPath = getIconByType(item.type);
            return __spreadProps(__spreadValues({}, item), {
              typeName: item.type,
              iconPath
            });
          });
        } else {
          console.error("获取近期数据失败", res.message);
        }
      }).finally(() => {
        loadingRecentData.value = false;
      });
    };
    const goToRegistration = () => {
      common_vendor.index.navigateTo({
        url: "/pages-data/registration/consent"
      });
    };
    const goToDoctorRegistration = () => {
      common_vendor.index.navigateTo({
        url: "/pages-data/doctor/form"
      });
    };
    const goToSocialWorkerRegistration = () => {
      common_vendor.index.navigateTo({
        url: "/pages-data/social/form"
      });
    };
    const goToChat = () => {
      common_vendor.index.navigateTo({
        url: "/pages-data/chat/chat"
      });
    };
    common_vendor.onMounted(() => {
      fetchCarouselList();
      getRecentData();
    });
    common_vendor.onShow(() => {
      fetchCarouselList();
      getRecentData();
    });
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.p({
          text: currentSwiperTitle.value
        }),
        b: common_vendor.f(carouselList.value, (item, index, i0) => {
          return common_vendor.e$1({
            a: item.imageUrl
          }, item.imageUrl ? {
            b: item.imageUrl
          } : {}, {
            c: index,
            d: common_vendor.o(($event) => onSwiperClick(item), index)
          });
        }),
        c: common_vendor.o(onSwiperChange),
        d: !(Number(common_vendor.unref(userStore).userInfo.userCategory) === 2 && isSocialRole.value)
      }, !(Number(common_vendor.unref(userStore).userInfo.userCategory) === 2 && isSocialRole.value) ? common_vendor.e$1({
        e: Number(common_vendor.unref(userStore).userInfo.userCategory) === 1 && !isPatientRole.value
      }, Number(common_vendor.unref(userStore).userInfo.userCategory) === 1 && !isPatientRole.value ? {
        f: common_vendor.o(goToRegistration)
      } : {}, {
        g: Number(common_vendor.unref(userStore).userInfo.userCategory) === 0 && !isDocterRole.value
      }, Number(common_vendor.unref(userStore).userInfo.userCategory) === 0 && !isDocterRole.value ? {
        h: common_vendor.o(goToDoctorRegistration)
      } : {}, {
        i: Number(common_vendor.unref(userStore).userInfo.userCategory) === 2 && !isSocialRole.value
      }, Number(common_vendor.unref(userStore).userInfo.userCategory) === 2 && !isSocialRole.value ? {
        j: common_vendor.o(goToSocialWorkerRegistration)
      } : {}, {
        k: Number(common_vendor.unref(userStore).userInfo.userCategory) !== 2 && hasCommAndDataPermission.value
      }, Number(common_vendor.unref(userStore).userInfo.userCategory) !== 2 && hasCommAndDataPermission.value ? {
        l: common_vendor.o(goToChat)
      } : {}) : {}, {
        m: Number(common_vendor.unref(userStore).userInfo.userCategory) === 0 && !isDocterRole.value || Number(common_vendor.unref(userStore).userInfo.userCategory) === 1 && !isPatientRole.value || Number(common_vendor.unref(userStore).userInfo.userCategory) === 2 && !isSocialRole.value
      }, Number(common_vendor.unref(userStore).userInfo.userCategory) === 0 && !isDocterRole.value || Number(common_vendor.unref(userStore).userInfo.userCategory) === 1 && !isPatientRole.value || Number(common_vendor.unref(userStore).userInfo.userCategory) === 2 && !isSocialRole.value ? common_vendor.e$1({
        n: Number(common_vendor.unref(userStore).userInfo.userCategory) === 0 && !isDocterRole.value
      }, Number(common_vendor.unref(userStore).userInfo.userCategory) === 0 && !isDocterRole.value ? {} : {}, {
        o: Number(common_vendor.unref(userStore).userInfo.userCategory) === 1 && !isPatientRole.value
      }, Number(common_vendor.unref(userStore).userInfo.userCategory) === 1 && !isPatientRole.value ? {} : {}, {
        p: Number(common_vendor.unref(userStore).userInfo.userCategory) === 2 && !isSocialRole.value
      }, Number(common_vendor.unref(userStore).userInfo.userCategory) === 2 && !isSocialRole.value ? {} : {}) : {}, {
        q: hasCommAndDataPermission.value
      }, hasCommAndDataPermission.value ? common_vendor.e$1({
        r: common_vendor.p({
          text: Number(common_vendor.unref(userStore).userInfo.userCategory) === 1 ? "近期录入数据" : "患者近期记录"
        }),
        s: common_vendor.o(goToDataPage),
        t: loadingRecentData.value
      }, loadingRecentData.value ? {} : recentDataList.value.length === 0 ? {
        w: common_vendor.p({
          text: "暂无记录"
        })
      } : {
        x: common_vendor.f(recentDataList.value, (item, index, i0) => {
          return common_vendor.e$1({
            a: item.iconPath,
            b: common_vendor.t(item.type)
          }, Number(common_vendor.unref(userStore).userInfo.userCategory) !== 1 ? {
            c: common_vendor.t(item.userName)
          } : {}, {
            d: common_vendor.t(item.createTime),
            e: "83a5a03c-6-" + i0 + ",83a5a03c-1",
            f: index,
            g: common_vendor.o(($event) => viewDataDetail(item), index)
          });
        }),
        y: Number(common_vendor.unref(userStore).userInfo.userCategory) !== 1,
        z: common_vendor.p({
          type: "right",
          size: "16",
          color: "#07C160"
        })
      }, {
        v: recentDataList.value.length === 0
      }) : {}, {
        A: common_vendor.p({
          navLeftArrow: false,
          navLeftText: ""
        })
      });
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-83a5a03c"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=index.js.map
