{"version": 3, "file": "pet.js", "sources": ["../../../../../src/service/app/pet.ts"], "sourcesContent": ["/* eslint-disable */\r\n// @ts-ignore\r\nimport request from '@/utils/request';\r\nimport { CustomRequestOptions } from '@/interceptors/request';\r\n\r\nimport * as API from './types';\r\n\r\n/** Update an existing pet PUT /pet */\r\nexport async function updatePet({\r\n  body,\r\n  options,\r\n}: {\r\n  body: API.Pet;\r\n  options?: CustomRequestOptions;\r\n}) {\r\n  return request<unknown>('/pet', {\r\n    method: 'PUT',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n    },\r\n    data: body,\r\n    ...(options || {}),\r\n  });\r\n}\r\n\r\n/** Add a new pet to the store POST /pet */\r\nexport async function addPet({\r\n  body,\r\n  options,\r\n}: {\r\n  body: API.Pet;\r\n  options?: CustomRequestOptions;\r\n}) {\r\n  return request<unknown>('/pet', {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n    },\r\n    data: body,\r\n    ...(options || {}),\r\n  });\r\n}\r\n\r\n/** Find pet by ID Returns a single pet GET /pet/${param0} */\r\nexport async function getPetById({\r\n  params,\r\n  options,\r\n}: {\r\n  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)\r\n  params: API.getPetByIdParams;\r\n  options?: CustomRequestOptions;\r\n}) {\r\n  const { petId: param0, ...queryParams } = params;\r\n\r\n  return request<API.Pet>(`/pet/${param0}`, {\r\n    method: 'GET',\r\n    params: { ...queryParams },\r\n    ...(options || {}),\r\n  });\r\n}\r\n\r\n/** Updates a pet in the store with form data POST /pet/${param0} */\r\nexport async function updatePetWithForm({\r\n  params,\r\n  body,\r\n  options,\r\n}: {\r\n  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)\r\n  params: API.updatePetWithFormParams;\r\n  body: {\r\n    /** Updated name of the pet */\r\n    name?: string;\r\n    /** Updated status of the pet */\r\n    status?: string;\r\n  };\r\n  options?: CustomRequestOptions;\r\n}) {\r\n  const { petId: param0, ...queryParams } = params;\r\n\r\n  return request<unknown>(`/pet/${param0}`, {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'application/x-www-form-urlencoded',\r\n    },\r\n    params: { ...queryParams },\r\n    data: body,\r\n    ...(options || {}),\r\n  });\r\n}\r\n\r\n/** Deletes a pet DELETE /pet/${param0} */\r\nexport async function deletePet({\r\n  params,\r\n  options,\r\n}: {\r\n  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)\r\n  params: API.deletePetParams;\r\n  options?: CustomRequestOptions;\r\n}) {\r\n  const { petId: param0, ...queryParams } = params;\r\n\r\n  return request<unknown>(`/pet/${param0}`, {\r\n    method: 'DELETE',\r\n    params: { ...queryParams },\r\n    ...(options || {}),\r\n  });\r\n}\r\n\r\n/** uploads an image POST /pet/${param0}/uploadImage */\r\nexport async function uploadFile({\r\n  params,\r\n  body,\r\n  file,\r\n  options,\r\n}: {\r\n  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)\r\n  params: API.uploadFileParams;\r\n  body: {\r\n    /** Additional data to pass to server */\r\n    additionalMetadata?: string;\r\n  };\r\n  file?: File;\r\n  options?: CustomRequestOptions;\r\n}) {\r\n  const { petId: param0, ...queryParams } = params;\r\n  const formData = new FormData();\r\n\r\n  if (file) {\r\n    formData.append('file', file);\r\n  }\r\n\r\n  Object.keys(body).forEach((ele) => {\r\n    const item = (body as { [key: string]: any })[ele];\r\n\r\n    if (item !== undefined && item !== null) {\r\n      if (typeof item === 'object' && !(item instanceof File)) {\r\n        if (item instanceof Array) {\r\n          item.forEach((f) => formData.append(ele, f || ''));\r\n        } else {\r\n          formData.append(ele, JSON.stringify(item));\r\n        }\r\n      } else {\r\n        formData.append(ele, item);\r\n      }\r\n    }\r\n  });\r\n\r\n  return request<API.ApiResponse>(`/pet/${param0}/uploadImage`, {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'multipart/form-data',\r\n    },\r\n    params: { ...queryParams },\r\n    data: formData,\r\n    ...(options || {}),\r\n  });\r\n}\r\n\r\n/** Finds Pets by status Multiple status values can be provided with comma separated strings GET /pet/findByStatus */\r\nexport async function findPetsByStatus({\r\n  params,\r\n  options,\r\n}: {\r\n  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)\r\n  params: API.findPetsByStatusParams;\r\n  options?: CustomRequestOptions;\r\n}) {\r\n  return request<API.Pet[]>('/pet/findByStatus', {\r\n    method: 'GET',\r\n    params: {\r\n      ...params,\r\n    },\r\n    ...(options || {}),\r\n  });\r\n}\r\n\r\n/** Finds Pets by tags Multiple tags can be provided with comma separated strings. Use tag1, tag2, tag3 for testing. GET /pet/findByTags */\r\nexport async function findPetsByTags({\r\n  params,\r\n  options,\r\n}: {\r\n  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)\r\n  params: API.findPetsByTagsParams;\r\n  options?: CustomRequestOptions;\r\n}) {\r\n  return request<API.Pet[]>('/pet/findByTags', {\r\n    method: 'GET',\r\n    params: {\r\n      ...params,\r\n    },\r\n    ...(options || {}),\r\n  });\r\n}\r\n"], "names": ["request"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+JA,SAAsB,iBAAiB,IAOpC;AAAA,6CAPoC;AAAA,IACrC;AAAA,IACA;AAAA,EACF,GAIG;AACD,WAAOA,cAAAA,QAAmB,qBAAqB;AAAA,MAC7C,QAAQ;AAAA,MACR,QAAQ,mBACH;AAAA,OAED,WAAW,CAAA,EAChB;AAAA,EACH;AAAA;;"}