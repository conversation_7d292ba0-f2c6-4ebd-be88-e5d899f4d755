{"version": 3, "file": "useRequest.js", "sources": ["../../../../src/hooks/useRequest.ts"], "sourcesContent": ["import { UnwrapRef } from 'vue'\r\n\r\ntype IUseRequestOptions<T> = {\r\n  /** 是否立即执行 */\r\n  immediate?: boolean\r\n  /** 初始化数据 */\r\n  initialData?: T\r\n}\r\n\r\n/**\r\n * useRequest是一个定制化的请求钩子，用于处理异步请求和响应。\r\n * @param func 一个执行异步请求的函数，返回一个包含响应数据的Promise。\r\n * @param options 包含请求选项的对象 {immediate, initialData}。\r\n * @param options.immediate 是否立即执行请求，默认为false。\r\n * @param options.initialData 初始化数据，默认为undefined。\r\n * @returns 返回一个对象{loading, error, data, run}，包含请求的加载状态、错误信息、响应数据和手动触发请求的函数。\r\n */\r\nexport default function useRequest<T>(\r\n  func: () => Promise<IResData<T>>,\r\n  options: IUseRequestOptions<T> = { immediate: false },\r\n) {\r\n  const loading = ref(false)\r\n  const error = ref(false)\r\n  const data = ref<T>(options.initialData)\r\n  const run = async () => {\r\n    loading.value = true\r\n    return func()\r\n      .then((res) => {\r\n        data.value = res.data as UnwrapRef<T>\r\n        error.value = false\r\n        return data.value\r\n      })\r\n      .catch((err) => {\r\n        error.value = err\r\n        throw err\r\n      })\r\n      .finally(() => {\r\n        loading.value = false\r\n      })\r\n  }\r\n\r\n  options.immediate && run()\r\n  return { loading, error, data, run }\r\n}\r\n"], "names": ["ref"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAiBA,SAAwB,WACtB,MACA,UAAiC,EAAE,WAAW,SAC9C;AACM,QAAA,UAAUA,kBAAI,KAAK;AACnB,QAAA,QAAQA,kBAAI,KAAK;AACjB,QAAA,OAAOA,cAAAA,IAAO,QAAQ,WAAW;AACvC,QAAM,MAAM,MAAY;AACtB,YAAQ,QAAQ;AAChB,WAAO,KAAK,EACT,KAAK,CAAC,QAAQ;AACb,WAAK,QAAQ,IAAI;AACjB,YAAM,QAAQ;AACd,aAAO,KAAK;AAAA,IAAA,CACb,EACA,MAAM,CAAC,QAAQ;AACd,YAAM,QAAQ;AACR,YAAA;AAAA,IAAA,CACP,EACA,QAAQ,MAAM;AACb,cAAQ,QAAQ;AAAA,IAAA,CACjB;AAAA,EACL;AAEA,UAAQ,aAAa,IAAI;AACzB,SAAO,EAAE,SAAS,OAAO,MAAM,IAAI;AACrC;;"}