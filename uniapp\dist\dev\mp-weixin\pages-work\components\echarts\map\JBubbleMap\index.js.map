{"version": 3, "file": "index.js", "sources": ["../../../../../../../../src/pages-work/components/echarts/map/JBubbleMap/index.vue", "../../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvcGFnZXMtd29yay9jb21wb25lbnRzL2VjaGFydHMvbWFwL0pCdWJibGVNYXAvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"content\">\r\n    <statusTip v-if=\"pageTips.show\" :status=\"pageTips.status\"></statusTip>\r\n    <!-- #ifdef APP-PLUS || H5 -->\r\n<!--    <EchartsMap v-else v-model:option=\"option\" v-model:map=\"mapObject\" v-model:echartId=\"echartId\" />-->\r\n    <!-- #endif -->\r\n    <!-- #ifdef APP-PLUS || H5 || MP-WEIXIN -->\r\n    <echartsUniapp v-else :option=\"option\" :mapName=\"mapName\" :mapData=\"mapDataJson\"></echartsUniapp>\r\n    <!-- #endif -->\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted } from 'vue';\r\nimport { echartProps } from '@/pages-work/components/echarts/props'\r\nimport EchartsMap from \"../index.vue\";\r\nimport echartsUniapp from \"../../index.vue\";\r\nimport { deepMerge, handleTotalAndUnit, disposeGridLayout } from '../../../common/echartUtil'\r\nimport useChartHook from '@/pages-work/components/hooks/useEchartMap'\r\nimport { merge, pull, cloneDeep } from 'lodash-es';\r\nimport {isArray} from \"@/utils/is\";\r\n// 定义 props\r\nconst props = defineProps({\r\n  ...echartProps\r\n});\r\n// 定义响应式数据\r\nconst option = ref({});\r\nconst chartOption = ref({\r\n  geo: {\r\n    map: '',\r\n    itemStyle: {},\r\n  },\r\n  tooltip: {\r\n    textStyle: {\r\n      color: \"#fff\"\r\n    },\r\n    padding: 5,\r\n    formatter: null\r\n  }\r\n});\r\nconst mapName = ref(\"\");\r\nlet [{ dataSource, reload, pageTips, config,mapDataJson,getAreaCode },\r\n  { queryData,registerMap,setGeoAreaColor,handleTotalAndUnitMap,handleCommonOpt,getConvertData }] = useChartHook(props, initOption)\r\nconst echartId = ref(\"\");\r\n// 计算属性\r\nconst mapObject = computed(() => ({ code: getAreaCode.value, data: mapDataJson.value }));\r\n// 初始化配置选项\r\nasync function initOption(data){\r\n  let chartData = dataSource.value;\r\n  mapName.value = await registerMap();\r\n  try {\r\n    //地图配置\r\n    chartOption.value.tooltip = {\r\n      enterable: true,\r\n      transitionDuration: 1,\r\n      textStyle: {\r\n        color: '#000',\r\n        decoration: 'none',\r\n      },\r\n      trigger: 'item',\r\n      formatter: (params)=>{\r\n        let value = params?.value ?isArray(params.value)?params.value[2]:params.value : 0;\r\n        return `${params.seriesName} <br/> ${params.name}:   ${value}`;\r\n      },\r\n    };\r\n    //使用 registerMap 注册的地图名称\r\n    chartOption.value.geo.map = mapName.value;\r\n    // update-begin--author:liaozhiyang---date:20241029---for：【TV360X-2800】大屏改造成暗黑效果\r\n    // 解决区域高亮颜色选择不生效\r\n    merge(chartOption.value.geo, props.config.option.geo);\r\n    // update-end--author:liaozhiyang---date:20241029---for：【TV360X-2800】大屏改造成暗黑效果\r\n    //series配置数据顺序调整后需要调整视觉映射属性seriesIndex,seriesIndex对应数据配置\r\n    chartOption.value.series = [\r\n      {\r\n        name: '数据',\r\n        type: props.config.option?.area?.markerType,\r\n        coordinateSystem: 'geo',\r\n        data: chartData && chartData.length>0 ? getConvertData(\r\n            chartData\r\n                .sort(function (a, b) {\r\n                  return b.value - a.value;\r\n                })\r\n                .slice(0, (config.option?.area?.markerCount || 0) > 0 ? config.option?.area.markerCount : chartData.length)\r\n        ):[],\r\n        symbol: config.option?.area?.markerShape || 'circle',\r\n        symbolSize: function (val) {\r\n          return  config.option?.area?.markerSize && config.option?.area?.markerSize>20 ? config.option?.area?.markerSize : 20;\r\n        },\r\n        showEffectOn: 'render',\r\n        //涟漪配置\r\n        rippleEffect: {\r\n          brushType: 'stroke',\r\n        },\r\n        label: {\r\n          position:  config.option?.area?.scatterLabelPosition || 'top',\r\n          show:  config.option?.area?.scatterLabelShow || false,\r\n          color: config.option?.area?.scatterLabelColor || '#ffffff',\r\n          fontSize: config.option?.area?.scatterFontSize || 12,\r\n          formatter: (params)=>{\r\n            if (isArray(params.value)){\r\n              return `${params.name || ''}:${params.value[2]}`;\r\n            }\r\n            return `${params.name || '空'}:${params.value}`;\r\n          }\r\n        },\r\n        emphasis: {\r\n          show: false,\r\n        },\r\n        animation: true,\r\n        itemStyle: {\r\n          color: config?.option?.area?.markerColor || 'auto',\r\n          shadowColor: config?.option?.area?.shadowColor  || 'auto',\r\n          opacity: config?.option?.area?.markerOpacity,\r\n        },\r\n      },\r\n      {\r\n        name: '地图',\r\n        type: 'map',\r\n        map: mapName.value,\r\n        geoIndex: 0,\r\n        aspectScale: 0.75, //长宽比\r\n        showLegendSymbol: false, // 存在legend时显示\r\n        label: {\r\n          show: true,\r\n          color: '#000',\r\n        },\r\n        emphasis: {\r\n          show: true,\r\n          color: '#000',\r\n          itemStyle:{\r\n            areaColor: '#2B91B7',\r\n          }\r\n        },\r\n        roam: true,\r\n        itemStyle: {\r\n          areaColor: '#3B5077',\r\n          borderColor: '#3B5077',\r\n        },\r\n        animation: true,\r\n        data: chartData && chartData.length>0 ?chartData:[],\r\n        zlevel: 1,\r\n      },\r\n    ];\r\n    // 合并配置\r\n    if (props.config && props.config.option) {\r\n      merge(chartOption.value, props.config.option);\r\n      chartOption.value = setGeoAreaColor(chartOption.value, props.config);\r\n      chartOption.value = handleTotalAndUnitMap(props.compName, chartOption.value, props.config, chartData);\r\n      chartOption.value = handleCommonOpt(chartOption.value);\r\n      setTimeout(() => {\r\n        option.value = { ...chartOption.value };\r\n        console.log(\"散点地图最终的option.value\", option.value);\r\n        pageTips.show = false;\r\n        echartId.value = props.i\r\n      }, 300);\r\n    }\r\n    if (dataSource.value && dataSource.value.length === 0) {\r\n      pageTips.status = 1;\r\n      pageTips.show = true;\r\n    }\r\n  } catch (e) {\r\n    // TODO handle the exception\r\n    console.log(\"散点地图报错\", e);\r\n  }\r\n};\r\n\r\n// 挂载时查询数据\r\nonMounted(() => {\r\n  queryData();\r\n});\r\n</script>\r\n\r\n<style>\r\n.content {\r\n  margin: 5px;\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-work/components/echarts/map/JBubbleMap/index.vue'\nwx.createComponent(Component)"], "names": ["ref", "useChartHook", "computed", "isArray", "merge", "_b", "_a", "_d", "_c", "_f", "_e", "onMounted", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,MAAM,gBAAgB,MAAW;;;;;AAMjC,UAAM,QAAQ;AAId,UAAM,SAASA,cAAAA,IAAI,CAAA,CAAE;AACrB,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB,KAAK;AAAA,QACH,KAAK;AAAA,QACL,WAAW,CAAE;AAAA,MACd;AAAA,MACD,SAAS;AAAA,QACP,WAAW;AAAA,UACT,OAAO;AAAA,QACR;AAAA,QACD,SAAS;AAAA,QACT,WAAW;AAAA,MACZ;AAAA,IACH,CAAC;AACD,UAAM,UAAUA,cAAAA,IAAI,EAAE;AACtB,QAAI;AAAA,MAAC,EAAE,YAAY,QAAQ,UAAU,QAAO,aAAY,YAAa;AAAA,MACnE,EAAE,WAAU,aAAY,iBAAgB,uBAAsB,iBAAgB,eAAc;AAAA,IAAE,IAAIC,wCAAAA,aAAa,OAAO,UAAU;AAClI,UAAM,WAAWD,cAAAA,IAAI,EAAE;AAELE,kBAAAA,SAAS,OAAO,EAAE,MAAM,YAAY,OAAO,MAAM,YAAY,MAAK,EAAG;AAEvF,aAAe,WAAW,MAAK;AAAA;;AAC7B,YAAI,YAAY,WAAW;AAC3B,gBAAQ,QAAQ,MAAM;AACtB,YAAI;AAEF,sBAAY,MAAM,UAAU;AAAA,YAC1B,WAAW;AAAA,YACX,oBAAoB;AAAA,YACpB,WAAW;AAAA,cACT,OAAO;AAAA,cACP,YAAY;AAAA,YACb;AAAA,YACD,SAAS;AAAA,YACT,WAAW,CAAC,WAAS;AACnB,kBAAI,SAAQ,iCAAQ,SAAOC,SAAO,QAAC,OAAO,KAAK,IAAE,OAAO,MAAM,CAAC,IAAE,OAAO,QAAQ;AAChF,qBAAO,GAAG,OAAO,UAAU,UAAU,OAAO,IAAI,OAAO,KAAK;AAAA,YAC7D;AAAA,UACP;AAEI,sBAAY,MAAM,IAAI,MAAM,QAAQ;AAGpCC,8BAAM,YAAY,MAAM,KAAK,MAAM,OAAO,OAAO,GAAG;AAGpD,sBAAY,MAAM,SAAS;AAAA,YACzB;AAAA,cACE,MAAM;AAAA,cACN,OAAM,iBAAM,OAAO,WAAb,mBAAqB,SAArB,mBAA2B;AAAA,cACjC,kBAAkB;AAAA,cAClB,MAAM,aAAa,UAAU,SAAO,IAAI;AAAA,gBACpC,UACK,KAAK,SAAU,GAAG,GAAG;AACpB,yBAAO,EAAE,QAAQ,EAAE;AAAA,gBACrC,CAAiB,EACA,MAAM,MAAI,kBAAO,WAAP,mBAAe,SAAf,mBAAqB,gBAAe,KAAK,KAAI,YAAO,WAAP,mBAAe,KAAK,cAAc,UAAU,MAAM;AAAA,cAC1H,IAAU,CAAE;AAAA,cACJ,UAAQ,kBAAO,WAAP,mBAAe,SAAf,mBAAqB,gBAAe;AAAA,cAC5C,YAAY,SAAU,KAAK;;AACzB,yBAAQC,OAAAC,MAAA,OAAO,WAAP,gBAAAA,IAAe,SAAf,gBAAAD,IAAqB,iBAAcE,OAAAC,MAAA,OAAO,WAAP,gBAAAA,IAAe,SAAf,gBAAAD,IAAqB,cAAW,MAAKE,OAAAC,MAAA,OAAO,WAAP,gBAAAA,IAAe,SAAf,gBAAAD,IAAqB,aAAa;AAAA,cACnH;AAAA,cACD,cAAc;AAAA;AAAA,cAEd,cAAc;AAAA,gBACZ,WAAW;AAAA,cACZ;AAAA,cACD,OAAO;AAAA,gBACL,YAAW,kBAAO,WAAP,mBAAe,SAAf,mBAAqB,yBAAwB;AAAA,gBACxD,QAAO,kBAAO,WAAP,mBAAe,SAAf,mBAAqB,qBAAoB;AAAA,gBAChD,SAAO,kBAAO,WAAP,mBAAe,SAAf,mBAAqB,sBAAqB;AAAA,gBACjD,YAAU,kBAAO,WAAP,mBAAe,SAAf,mBAAqB,oBAAmB;AAAA,gBAClD,WAAW,CAAC,WAAS;AACnB,sBAAIN,SAAO,QAAC,OAAO,KAAK,GAAE;AACxB,2BAAO,GAAG,OAAO,QAAQ,EAAE,IAAI,OAAO,MAAM,CAAC,CAAC;AAAA,kBAC/C;AACD,yBAAO,GAAG,OAAO,QAAQ,GAAG,IAAI,OAAO,KAAK;AAAA,gBAC7C;AAAA,cACF;AAAA,cACD,UAAU;AAAA,gBACR,MAAM;AAAA,cACP;AAAA,cACD,WAAW;AAAA,cACX,WAAW;AAAA,gBACT,SAAO,4CAAQ,WAAR,mBAAgB,SAAhB,mBAAsB,gBAAe;AAAA,gBAC5C,eAAa,4CAAQ,WAAR,mBAAgB,SAAhB,mBAAsB,gBAAgB;AAAA,gBACnD,UAAS,4CAAQ,WAAR,mBAAgB,SAAhB,mBAAsB;AAAA,cAChC;AAAA,YACF;AAAA,YACD;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,KAAK,QAAQ;AAAA,cACb,UAAU;AAAA,cACV,aAAa;AAAA;AAAA,cACb,kBAAkB;AAAA;AAAA,cAClB,OAAO;AAAA,gBACL,MAAM;AAAA,gBACN,OAAO;AAAA,cACR;AAAA,cACD,UAAU;AAAA,gBACR,MAAM;AAAA,gBACN,OAAO;AAAA,gBACP,WAAU;AAAA,kBACR,WAAW;AAAA,gBACZ;AAAA,cACF;AAAA,cACD,MAAM;AAAA,cACN,WAAW;AAAA,gBACT,WAAW;AAAA,gBACX,aAAa;AAAA,cACd;AAAA,cACD,WAAW;AAAA,cACX,MAAM,aAAa,UAAU,SAAO,IAAG,YAAU,CAAE;AAAA,cACnD,QAAQ;AAAA,YACT;AAAA,UACP;AAEI,cAAI,MAAM,UAAU,MAAM,OAAO,QAAQ;AACvCC,0BAAK,MAAC,YAAY,OAAO,MAAM,OAAO,MAAM;AAC5C,wBAAY,QAAQ,gBAAgB,YAAY,OAAO,MAAM,MAAM;AACnE,wBAAY,QAAQ,sBAAsB,MAAM,UAAU,YAAY,OAAO,MAAM,QAAQ,SAAS;AACpG,wBAAY,QAAQ,gBAAgB,YAAY,KAAK;AACrD,uBAAW,MAAM;AACf,qBAAO,QAAQ,mBAAK,YAAY;AAChC,sBAAQ,IAAI,uBAAuB,OAAO,KAAK;AAC/C,uBAAS,OAAO;AAChB,uBAAS,QAAQ,MAAM;AAAA,YACxB,GAAE,GAAG;AAAA,UACP;AACD,cAAI,WAAW,SAAS,WAAW,MAAM,WAAW,GAAG;AACrD,qBAAS,SAAS;AAClB,qBAAS,OAAO;AAAA,UACjB;AAAA,QACF,SAAQ,GAAG;AAEV,kBAAQ,IAAI,UAAU,CAAC;AAAA,QACxB;AAAA,MACH;AAAA;AAGAO,kBAAAA,UAAU,MAAM;AACd;IACF,CAAC;;;;;;;;;;;;;;;;;;ACxKD,GAAG,gBAAgBC,SAAS;"}