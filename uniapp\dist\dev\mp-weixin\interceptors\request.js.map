{"version": 3, "file": "request.js", "sources": ["../../../../src/interceptors/request.ts"], "sourcesContent": ["/* eslint-disable no-param-reassign */\r\nimport qs from 'qs'\r\nimport { useUserStore } from '@/store'\r\nimport { platform } from '@/utils/platform'\r\nimport { getEnvBaseUrl } from '@/utils'\r\n\r\nexport type CustomRequestOptions = UniApp.RequestOptions & {\r\n  query?: Record<string, any>\r\n  /** 出错时是否隐藏错误提示 */\r\n  hideErrorToast?: boolean\r\n} & IUniUploadFileOptions // 添加uni.uploadFile参数类型\r\n\r\n// 请求基准地址\r\nconst baseUrl = getEnvBaseUrl()\r\n\r\n// 拦截器配置\r\nconst httpInterceptor = {\r\n  // 拦截前触发\r\n  invoke(options: CustomRequestOptions) {\r\n    // 接口请求支持通过 query 参数配置 queryString\r\n    if (options.query) {\r\n      const queryStr = qs.stringify(options.query)\r\n      if (options.url.includes('?')) {\r\n        options.url += `&${queryStr}`\r\n      } else {\r\n        options.url += `?${queryStr}`\r\n      }\r\n    }\r\n    // 非 http 开头需拼接地址\r\n    if (!options.url.startsWith('http')) {\r\n      // #ifdef H5\r\n      // console.log(__VITE_APP_PROXY__)\r\n      if (JSON.parse(__VITE_APP_PROXY__)) {\r\n        // 啥都不需要做\r\n      } else {\r\n        options.url = baseUrl + options.url\r\n      }\r\n      // #endif\r\n      // 非H5正常拼接\r\n      // #ifndef H5\r\n      options.url = baseUrl + options.url\r\n      // #endif\r\n      // TIPS: 如果需要对接多个后端服务，也可以在这里处理，拼接成所需要的地址\r\n    }\r\n    // 1. 请求超时\r\n    options.timeout = 10000 // 10s\r\n    // 2. （可选）添加小程序端请求头标识\r\n    options.header = {\r\n      platform, // 可选，与 uniapp 定义的平台一致，告诉后台来源\r\n      ...options.header,\r\n    }\r\n    // 3. 添加 token 请求头标识\r\n    const userStore = useUserStore()\r\n    const { token } = userStore.userInfo as unknown as IUserInfo\r\n    if (token) {\r\n      options.header.Authorization = `${token}`\r\n    }\r\n  },\r\n}\r\n\r\nexport const requestInterceptor = {\r\n  install() {\r\n    // 拦截 request 请求\r\n    uni.addInterceptor('request', httpInterceptor)\r\n    // 拦截 uploadFile 文件上传\r\n    uni.addInterceptor('uploadFile', httpInterceptor)\r\n  },\r\n}\r\n"], "names": ["getEnvBaseUrl", "qs", "platform", "useUserStore", "uni"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAaA,MAAM,UAAUA,YAAAA,cAAc;AAG9B,MAAM,kBAAkB;AAAA;AAAA,EAEtB,OAAO,SAA+B;AAEpC,QAAI,QAAQ,OAAO;AACjB,YAAM,WAAWC,cAAA,GAAG,UAAU,QAAQ,KAAK;AAC3C,UAAI,QAAQ,IAAI,SAAS,GAAG,GAAG;AACrB,gBAAA,OAAO,IAAI,QAAQ;AAAA,MAAA,OACtB;AACG,gBAAA,OAAO,IAAI,QAAQ;AAAA,MAAA;AAAA,IAC7B;AAGF,QAAI,CAAC,QAAQ,IAAI,WAAW,MAAM,GAAG;AAW3B,cAAA,MAAM,UAAU,QAAQ;AAAA,IAAA;AAKlC,YAAQ,UAAU;AAElB,YAAQ,SAAS;AAAA,MAAA,UACfC,eAAA;AAAA,OACG,QAAQ;AAGb,UAAM,YAAYC,WAAAA,aAAa;AACzB,UAAA,EAAE,UAAU,UAAU;AAC5B,QAAI,OAAO;AACD,cAAA,OAAO,gBAAgB,GAAG,KAAK;AAAA,IAAA;AAAA,EACzC;AAEJ;AAEO,MAAM,qBAAqB;AAAA,EAChC,UAAU;AAEJC,wBAAA,eAAe,WAAW,eAAe;AAEzCA,wBAAA,eAAe,cAAc,eAAe;AAAA,EAAA;AAEpD;;"}