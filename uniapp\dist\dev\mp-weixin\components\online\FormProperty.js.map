{"version": 3, "file": "FormProperty.js", "sources": ["../../../../../src/components/online/FormProperty.ts"], "sourcesContent": ["// 定义 FormProperty 函数\r\nconst FormProperty = (propertyId, formSchema, required = []) => {\r\n    // 初始化私有属性\r\n    const _propertyId = propertyId;\r\n    const _formSchem = formSchema;\r\n    const _required = required;\r\n\r\n    // 定义 formSchema 的 getter 方法\r\n    const getFormSchema = () => {\r\n        return _formSchem || {};\r\n    };\r\n\r\n    // 定义 key 的 getter 方法\r\n    const getKey = () => {\r\n        return _propertyId;\r\n    };\r\n\r\n    // 定义 type 的 getter 方法\r\n    const getType = () => {\r\n        return getFormSchema().view;\r\n    };\r\n\r\n    // 定义 disabled 的 getter 方法\r\n    const getDisabled = () => {\r\n        if (_formSchem && _formSchem.ui && _formSchem.ui.widgetattrs && _formSchem.ui.widgetattrs.disabled === true) {\r\n            return true;\r\n        }\r\n        return false;\r\n    };\r\n\r\n    // 定义 label 的 getter 方法\r\n    const getLabel = () => {\r\n        const schema = getFormSchema();\r\n        return schema.title || getKey();\r\n    };\r\n\r\n    // 定义 placeholder 的 getter 方法\r\n    const getPlaceholder = () => {\r\n        const viewType = getType();\r\n        const label = getLabel();\r\n        if (viewType.indexOf('date') >= 0 || viewType.indexOf('select') >= 0 || viewType.indexOf('list') >= 0) {\r\n            return \"请选择\" + label;\r\n        } else if (viewType.indexOf('upload') >= 0 || viewType.indexOf('file') >= 0 || viewType.indexOf('image') >= 0) {\r\n            return \"请上传\" + label;\r\n        } else {\r\n            return \"请输入\" + label;\r\n        }\r\n    };\r\n\r\n    // 定义 dictStr 的 getter 方法\r\n    const getDictStr = () => {\r\n        const viewType = getType();\r\n        if (viewType === 'sel_search') {\r\n            const schema = getFormSchema();\r\n            return schema.dictTable + ',' + schema.dictText + ',' + schema.dictCode;\r\n        }\r\n        return '';\r\n    };\r\n\r\n    // 定义 listSource 的 getter 方法\r\n    const getListSource = () => {\r\n        const schema = getFormSchema();\r\n        if (!schema.enum) {\r\n            return [];\r\n        }\r\n        const arr = [...schema.enum];\r\n        for (let a = 0; a < arr.length; a++) {\r\n            if (!arr[a].label) {\r\n                arr[a].label = arr[a].text;\r\n            }\r\n            if (schema.type === 'number') {\r\n                arr[a].value = parseInt(arr[a].value);\r\n            }\r\n        }\r\n        return arr;\r\n    };\r\n\r\n    // 定义 popupCode 的 getter 方法\r\n    const getPopupCode = () => {\r\n        return getFormSchema().code;\r\n    };\r\n\r\n    // 定义 dest 的 getter 方法\r\n    const getDest = () => {\r\n        return getFormSchema().destFields;\r\n    };\r\n\r\n    // 定义 ogn 的 getter 方法\r\n    const getOgn = () => {\r\n        return getFormSchema().orgFields;\r\n    };\r\n\r\n    // 定义 rules 的 getter 方法\r\n    const getRules = () => {\r\n        const rules = [];\r\n        const isRequired = _required?.includes(getKey()) ?? false;\r\n        if (isRequired) {\r\n            let msg = getLabel() + '为必填项';\r\n            rules.push({ required: true, message: msg });\r\n        }\r\n        let viewType = getType();\r\n        if ('list' === viewType || 'markdown' === viewType || 'pca' === viewType) {\r\n            return rules;\r\n        }\r\n        if (viewType.indexOf('upload') >= 0 || viewType.indexOf('file') >= 0 || viewType.indexOf('image') >= 0) {\r\n            return rules;\r\n        }\r\n\r\n        const schema = getFormSchema();\r\n        if (schema.pattern) {\r\n            if (schema.pattern === 'only') {\r\n                // 这里 checkOnlyMethod 未定义，需要根据实际情况补充\r\n                rules.push({ validator: () => {} });\r\n            } else if (schema.pattern === 'z') {\r\n                if (schema.type === 'number' || schema.type === 'integer') {\r\n                    // 这里 onlyInteger 未定义，需要根据实际情况处理\r\n                } else {\r\n                    rules.push({ pattern: '^-?[1-9]\\\\d*$', message: '请输入整数' });\r\n                }\r\n            } else {\r\n                let msg = getLabel() + '校验未通过';\r\n                rules.push({ pattern: schema.pattern, message: msg });\r\n            }\r\n        }\r\n        return rules;\r\n    };\r\n\r\n    // 返回包含所有 getter 方法的对象\r\n    return {\r\n        get formSchema() {\r\n            return getFormSchema();\r\n        },\r\n        get key() {\r\n            return getKey();\r\n        },\r\n        get type() {\r\n            return getType();\r\n        },\r\n        get disabled() {\r\n            return getDisabled();\r\n        },\r\n        get label() {\r\n            return getLabel();\r\n        },\r\n        get placeholder() {\r\n            return getPlaceholder();\r\n        },\r\n        get dictStr() {\r\n            return getDictStr();\r\n        },\r\n        get listSource() {\r\n            return getListSource();\r\n        },\r\n        get popupCode() {\r\n            return getPopupCode();\r\n        },\r\n        get dest() {\r\n            return getDest();\r\n        },\r\n        get ogn() {\r\n            return getOgn();\r\n        },\r\n        get rules() {\r\n            return getRules();\r\n        }\r\n    };\r\n};\r\n\r\nexport default FormProperty;\r\n"], "names": [], "mappings": ";AACA,MAAM,eAAe,CAAC,YAAY,YAAY,WAAW,CAAA,MAAO;AAE5D,QAAM,cAAc;AACpB,QAAM,aAAa;AACnB,QAAM,YAAY;AAGlB,QAAM,gBAAgB,MAAM;AACxB,WAAO,cAAc,CAAC;AAAA,EAC1B;AAGA,QAAM,SAAS,MAAM;AACV,WAAA;AAAA,EACX;AAGA,QAAM,UAAU,MAAM;AAClB,WAAO,cAAgB,EAAA;AAAA,EAC3B;AAGA,QAAM,cAAc,MAAM;AAClB,QAAA,cAAc,WAAW,MAAM,WAAW,GAAG,eAAe,WAAW,GAAG,YAAY,aAAa,MAAM;AAClG,aAAA;AAAA,IAAA;AAEJ,WAAA;AAAA,EACX;AAGA,QAAM,WAAW,MAAM;AACnB,UAAM,SAAS,cAAc;AACtB,WAAA,OAAO,SAAS,OAAO;AAAA,EAClC;AAGA,QAAM,iBAAiB,MAAM;AACzB,UAAM,WAAW,QAAQ;AACzB,UAAM,QAAQ,SAAS;AACvB,QAAI,SAAS,QAAQ,MAAM,KAAK,KAAK,SAAS,QAAQ,QAAQ,KAAK,KAAK,SAAS,QAAQ,MAAM,KAAK,GAAG;AACnG,aAAO,QAAQ;AAAA,IAAA,WACR,SAAS,QAAQ,QAAQ,KAAK,KAAK,SAAS,QAAQ,MAAM,KAAK,KAAK,SAAS,QAAQ,OAAO,KAAK,GAAG;AAC3G,aAAO,QAAQ;AAAA,IAAA,OACZ;AACH,aAAO,QAAQ;AAAA,IAAA;AAAA,EAEvB;AAGA,QAAM,aAAa,MAAM;AACrB,UAAM,WAAW,QAAQ;AACzB,QAAI,aAAa,cAAc;AAC3B,YAAM,SAAS,cAAc;AAC7B,aAAO,OAAO,YAAY,MAAM,OAAO,WAAW,MAAM,OAAO;AAAA,IAAA;AAE5D,WAAA;AAAA,EACX;AAGA,QAAM,gBAAgB,MAAM;AACxB,UAAM,SAAS,cAAc;AACzB,QAAA,CAAC,OAAO,MAAM;AACd,aAAO,CAAC;AAAA,IAAA;AAEZ,UAAM,MAAM,CAAC,GAAG,OAAO,IAAI;AAC3B,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,UAAI,CAAC,IAAI,CAAC,EAAE,OAAO;AACf,YAAI,CAAC,EAAE,QAAQ,IAAI,CAAC,EAAE;AAAA,MAAA;AAEtB,UAAA,OAAO,SAAS,UAAU;AAC1B,YAAI,CAAC,EAAE,QAAQ,SAAS,IAAI,CAAC,EAAE,KAAK;AAAA,MAAA;AAAA,IACxC;AAEG,WAAA;AAAA,EACX;AAGA,QAAM,eAAe,MAAM;AACvB,WAAO,cAAgB,EAAA;AAAA,EAC3B;AAGA,QAAM,UAAU,MAAM;AAClB,WAAO,cAAgB,EAAA;AAAA,EAC3B;AAGA,QAAM,SAAS,MAAM;AACjB,WAAO,cAAgB,EAAA;AAAA,EAC3B;AAGA,QAAM,WAAW,MAAM;;AACnB,UAAM,QAAQ,CAAC;AACf,UAAM,cAAa,4CAAW,SAAS,OAAA,OAApB,YAAiC;AACpD,QAAI,YAAY;AACR,UAAA,MAAM,aAAa;AACvB,YAAM,KAAK,EAAE,UAAU,MAAM,SAAS,KAAK;AAAA,IAAA;AAE/C,QAAI,WAAW,QAAQ;AACvB,QAAI,WAAW,YAAY,eAAe,YAAY,UAAU,UAAU;AAC/D,aAAA;AAAA,IAAA;AAEX,QAAI,SAAS,QAAQ,QAAQ,KAAK,KAAK,SAAS,QAAQ,MAAM,KAAK,KAAK,SAAS,QAAQ,OAAO,KAAK,GAAG;AAC7F,aAAA;AAAA,IAAA;AAGX,UAAM,SAAS,cAAc;AAC7B,QAAI,OAAO,SAAS;AACZ,UAAA,OAAO,YAAY,QAAQ;AAErB,cAAA,KAAK,EAAE,WAAW,MAAM;AAAA,QAAA,GAAI;AAAA,MAAA,WAC3B,OAAO,YAAY,KAAK;AAC/B,YAAI,OAAO,SAAS,YAAY,OAAO,SAAS;AAAW;AAAA,aAEpD;AACH,gBAAM,KAAK,EAAE,SAAS,iBAAiB,SAAS,SAAS;AAAA,QAAA;AAAA,MAC7D,OACG;AACC,YAAA,MAAM,aAAa;AACvB,cAAM,KAAK,EAAE,SAAS,OAAO,SAAS,SAAS,KAAK;AAAA,MAAA;AAAA,IACxD;AAEG,WAAA;AAAA,EACX;AAGO,SAAA;AAAA,IACH,IAAI,aAAa;AACb,aAAO,cAAc;AAAA,IACzB;AAAA,IACA,IAAI,MAAM;AACN,aAAO,OAAO;AAAA,IAClB;AAAA,IACA,IAAI,OAAO;AACP,aAAO,QAAQ;AAAA,IACnB;AAAA,IACA,IAAI,WAAW;AACX,aAAO,YAAY;AAAA,IACvB;AAAA,IACA,IAAI,QAAQ;AACR,aAAO,SAAS;AAAA,IACpB;AAAA,IACA,IAAI,cAAc;AACd,aAAO,eAAe;AAAA,IAC1B;AAAA,IACA,IAAI,UAAU;AACV,aAAO,WAAW;AAAA,IACtB;AAAA,IACA,IAAI,aAAa;AACb,aAAO,cAAc;AAAA,IACzB;AAAA,IACA,IAAI,YAAY;AACZ,aAAO,aAAa;AAAA,IACxB;AAAA,IACA,IAAI,OAAO;AACP,aAAO,QAAQ;AAAA,IACnB;AAAA,IACA,IAAI,MAAM;AACN,aAAO,OAAO;AAAA,IAClB;AAAA,IACA,IAAI,QAAQ;AACR,aAAO,SAAS;AAAA,IAAA;AAAA,EAExB;AACJ;;"}