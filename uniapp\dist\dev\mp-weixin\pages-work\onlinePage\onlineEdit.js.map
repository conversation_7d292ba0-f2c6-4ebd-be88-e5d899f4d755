{"version": 3, "file": "onlineEdit.js", "sources": ["../../../../../src/pages-work/onlinePage/onlineEdit.vue", "../../../../../uniPage:/cGFnZXMtd29ya1xvbmxpbmVQYWdlXG9ubGluZUVkaXQudnVl"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n  <PageLayout :navTitle=\"navTitle\" :backRouteName=\"backRouteName\">\r\n    <scroll-view scroll-y>\r\n      <online-loader\r\n        ref=\"online\"\r\n        :table=\"tableName\"\r\n        :title=\"navTitle\"\r\n        :dataId=\"dataId\"\r\n        edit\r\n        show-footer\r\n        @success=\"handleSuccess\"\r\n        @back=\"backRoute\"\r\n      ></online-loader>\r\n    </scroll-view>\r\n  </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport OnlineLoader from '@/components/online/online-loader.vue'\r\nimport router from '@/router'\r\nimport { onLoad } from '@dcloudio/uni-app'\r\n// 定义响应式数据\r\nconst tableName = ref('')\r\nconst navTitle = ref('')\r\nconst dataId = ref('')\r\nconst online = ref(null)\r\nconst backRouteName = ref('onlineTable')\r\n// 定义 initForm 方法\r\nconst initForm = (item) => {\r\n  console.log('initForm item', item)\r\n  // 表名\r\n  tableName.value = item.desformCode\r\n  // 表描述\r\n  navTitle.value = `表单【${item.desformName}】`\r\n  // 数据ID\r\n  dataId.value = item.id\r\n  // 返回上一页面\r\n  item.backRouteName && (backRouteName.value = item.backRouteName)\r\n  nextTick(() => {\r\n    online.value.loadByTableName(tableName.value)\r\n  })\r\n}\r\n\r\nconst backRoute = () => {\r\n  router.back()\r\n}\r\n\r\n// 定义 handleSuccess 方法\r\nconst handleSuccess = (id) => {\r\n  uni.$emit('refreshList')\r\n  backRoute()\r\n}\r\n\r\n// onLoad 生命周期钩子\r\nonLoad((option) => {\r\n  initForm(option)\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n//\r\n</style>\r\n", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-work/onlinePage/onlineEdit.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "nextTick", "router", "uni", "onLoad"], "mappings": ";;;;;;;;;;;;AAsBA,MAAA,eAAyB,MAAA;;;;AAInB,UAAA,YAAYA,kBAAI,EAAE;AAClB,UAAA,WAAWA,kBAAI,EAAE;AACjB,UAAA,SAASA,kBAAI,EAAE;AACf,UAAA,SAASA,kBAAI,IAAI;AACjB,UAAA,gBAAgBA,kBAAI,aAAa;AAEjC,UAAA,WAAW,CAAC,SAAS;AACjB,cAAA,IAAI,iBAAiB,IAAI;AAEjC,gBAAU,QAAQ,KAAK;AAEd,eAAA,QAAQ,MAAM,KAAK,WAAW;AAEvC,aAAO,QAAQ,KAAK;AAEf,WAAA,kBAAkB,cAAc,QAAQ,KAAK;AAClDC,oBAAAA,WAAS,MAAM;AACN,eAAA,MAAM,gBAAgB,UAAU,KAAK;AAAA,MAAA,CAC7C;AAAA,IACH;AAEA,UAAM,YAAY,MAAM;AACtBC,mBAAAA,OAAO,KAAK;AAAA,IACd;AAGM,UAAA,gBAAgB,CAAC,OAAO;AAC5BC,oBAAA,MAAI,MAAM,aAAa;AACb,gBAAA;AAAA,IACZ;AAGAC,kBAAA,OAAO,CAAC,WAAW;AACjB,eAAS,MAAM;AAAA,IAAA,CAChB;;;;;;;;;;;;;;;;;;;;;;;;AC3DD,GAAG,WAAW,eAAe;"}