{"version": 3, "file": "wd-tabs.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-tabs/wd-tabs.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC10YWJzL3dkLXRhYnMudnVl"], "sourcesContent": ["<template>\n  <template v-if=\"sticky\">\n    <wd-sticky-box>\n      <view\n        :class=\"`wd-tabs ${customClass} ${innerSlidable ? 'is-slide' : ''} ${mapNum < children.length && mapNum !== 0 ? 'is-map' : ''}`\"\n        :style=\"customStyle\"\n      >\n        <wd-sticky :offset-top=\"offsetTop\">\n          <view class=\"wd-tabs__nav wd-tabs__nav--sticky\">\n            <view class=\"wd-tabs__nav--wrap\">\n              <scroll-view :scroll-x=\"innerSlidable\" scroll-with-animation :scroll-left=\"state.scrollLeft\">\n                <view class=\"wd-tabs__nav-container\">\n                  <view\n                    @click=\"handleSelect(index)\"\n                    v-for=\"(item, index) in children\"\n                    :key=\"index\"\n                    :class=\"`wd-tabs__nav-item  ${state.activeIndex === index ? 'is-active' : ''} ${item.disabled ? 'is-disabled' : ''}`\"\n                    :style=\"state.activeIndex === index ? (color ? 'color:' + color : '') : inactiveColor ? 'color:' + inactiveColor : ''\"\n                  >\n                    <wd-badge v-if=\"item.badgeProps\" v-bind=\"item.badgeProps\">\n                      <text class=\"wd-tabs__nav-item-text\">{{ item.title }}</text>\n                    </wd-badge>\n                    <text v-else class=\"wd-tabs__nav-item-text\">{{ item.title }}</text>\n\n                    <view class=\"wd-tabs__line wd-tabs__line--inner\" v-if=\"state.activeIndex === index && state.useInnerLine\"></view>\n                  </view>\n                  <view class=\"wd-tabs__line\" :style=\"state.lineStyle\"></view>\n                </view>\n              </scroll-view>\n            </view>\n            <view class=\"wd-tabs__map\" v-if=\"mapNum < children.length && mapNum !== 0\">\n              <view :class=\"`wd-tabs__map-btn  ${state.animating ? 'is-open' : ''}`\" @click=\"toggleMap\">\n                <view :class=\"`wd-tabs__map-arrow  ${state.animating ? 'is-open' : ''}`\">\n                  <wd-icon name=\"arrow-down\" />\n                </view>\n              </view>\n              <view class=\"wd-tabs__map-header\" :style=\"`${state.mapShow ? '' : 'display:none;'}  ${state.animating ? 'opacity:1;' : ''}`\">\n                {{ mapTitle || translate('all') }}\n              </view>\n              <view :class=\"`wd-tabs__map-body  ${state.animating ? 'is-open' : ''}`\" :style=\"state.mapShow ? '' : 'display:none'\">\n                <view class=\"wd-tabs__map-nav-item\" v-for=\"(item, index) in children\" :key=\"index\" @click=\"handleSelect(index)\">\n                  <view\n                    :class=\"`wd-tabs__map-nav-btn ${state.activeIndex === index ? 'is-active' : ''}  ${item.disabled ? 'is-disabled' : ''}`\"\n                    :style=\"\n                      state.activeIndex === index\n                        ? color\n                          ? 'color:' + color + ';border-color:' + color\n                          : ''\n                        : inactiveColor\n                        ? 'color:' + inactiveColor\n                        : ''\n                    \"\n                  >\n                    {{ item.title }}\n                  </view>\n                </view>\n              </view>\n            </view>\n          </view>\n        </wd-sticky>\n\n        <view class=\"wd-tabs__container\" @touchstart=\"onTouchStart\" @touchmove=\"onTouchMove\" @touchend=\"onTouchEnd\" @touchcancel=\"onTouchEnd\">\n          <view :class=\"['wd-tabs__body', animated ? 'is-animated' : '']\" :style=\"bodyStyle\">\n            <slot />\n          </view>\n        </view>\n\n        <view\n          class=\"wd-tabs__mask\"\n          :style=\"`${state.mapShow ? '' : 'display:none;'} ${state.animating ? 'opacity:1;' : ''}`\"\n          @click=\"toggleMap\"\n        ></view>\n      </view>\n    </wd-sticky-box>\n  </template>\n\n  <template v-else>\n    <view :class=\"`wd-tabs ${customClass} ${innerSlidable ? 'is-slide' : ''} ${mapNum < children.length && mapNum !== 0 ? 'is-map' : ''}`\">\n      <view class=\"wd-tabs__nav\">\n        <view class=\"wd-tabs__nav--wrap\">\n          <scroll-view :scroll-x=\"innerSlidable\" scroll-with-animation :scroll-left=\"state.scrollLeft\">\n            <view class=\"wd-tabs__nav-container\">\n              <view\n                v-for=\"(item, index) in children\"\n                @click=\"handleSelect(index)\"\n                :key=\"index\"\n                :class=\"`wd-tabs__nav-item ${state.activeIndex === index ? 'is-active' : ''} ${item.disabled ? 'is-disabled' : ''}`\"\n                :style=\"state.activeIndex === index ? (color ? 'color:' + color : '') : inactiveColor ? 'color:' + inactiveColor : ''\"\n              >\n                <wd-badge custom-class=\"wd-tabs__nav-item-badge\" v-if=\"item.badgeProps\" v-bind=\"item.badgeProps\">\n                  <text class=\"wd-tabs__nav-item-text\">{{ item.title }}</text>\n                </wd-badge>\n                <text v-else class=\"wd-tabs__nav-item-text\">{{ item.title }}</text>\n                <view class=\"wd-tabs__line wd-tabs__line--inner\" v-if=\"state.activeIndex === index && state.useInnerLine\"></view>\n              </view>\n              <view class=\"wd-tabs__line\" :style=\"state.lineStyle\"></view>\n            </view>\n          </scroll-view>\n        </view>\n        <view class=\"wd-tabs__map\" v-if=\"mapNum < children.length && mapNum !== 0\">\n          <view class=\"wd-tabs__map-btn\" @click=\"toggleMap\">\n            <view :class=\"`wd-tabs__map-arrow ${state.animating ? 'is-open' : ''}`\">\n              <wd-icon name=\"arrow-down\" />\n            </view>\n          </view>\n          <view class=\"wd-tabs__map-header\" :style=\"`${state.mapShow ? '' : 'display:none;'}  ${state.animating ? 'opacity:1;' : ''}`\">\n            {{ mapTitle || translate('all') }}\n          </view>\n          <view :class=\"`wd-tabs__map-body ${state.animating ? 'is-open' : ''}`\" :style=\"state.mapShow ? '' : 'display:none'\">\n            <view class=\"wd-tabs__map-nav-item\" v-for=\"(item, index) in children\" :key=\"index\" @click=\"handleSelect(index)\">\n              <view :class=\"`wd-tabs__map-nav-btn ${state.activeIndex === index ? 'is-active' : ''}  ${item.disabled ? 'is-disabled' : ''}`\">\n                {{ item.title }}\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <view class=\"wd-tabs__container\" @touchstart=\"onTouchStart\" @touchmove=\"onTouchMove\" @touchend=\"onTouchEnd\" @touchcancel=\"onTouchEnd\">\n        <view :class=\"['wd-tabs__body', animated ? 'is-animated' : '']\" :style=\"bodyStyle\">\n          <slot />\n        </view>\n      </view>\n\n      <view class=\"wd-tabs__mask\" :style=\"`${state.mapShow ? '' : 'display:none;'}  ${state.animating ? 'opacity:1' : ''}`\" @click=\"toggleMap\"></view>\n    </view>\n  </template>\n</template>\n<script lang=\"ts\">\nexport default {\n  name: 'wd-tabs',\n  options: {\n    addGlobalClass: true,\n    virtualHost: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n<script lang=\"ts\" setup>\nimport wdIcon from '../wd-icon/wd-icon.vue'\nimport wdSticky from '../wd-sticky/wd-sticky.vue'\nimport wdStickyBox from '../wd-sticky-box/wd-sticky-box.vue'\nimport { computed, getCurrentInstance, onMounted, watch, nextTick, reactive, type CSSProperties, type ComponentInstance } from 'vue'\nimport { addUnit, checkNumRange, debounce, getRect, isDef, isNumber, isString, objToStyle } from '../common/util'\nimport { useTouch } from '../composables/useTouch'\nimport { TABS_KEY, tabsProps, type TabsExpose } from './types'\nimport { useChildren } from '../composables/useChildren'\nimport { useTranslate } from '../composables/useTranslate'\n\nconst $item = '.wd-tabs__nav-item'\nconst $itemText = '.wd-tabs__nav-item-text'\nconst $container = '.wd-tabs__nav-container'\n\nconst props = defineProps(tabsProps)\nconst emit = defineEmits(['change', 'disabled', 'click', 'update:modelValue'])\n\nconst { translate } = useTranslate('tabs')\n\nconst state = reactive({\n  activeIndex: 0, // 选中值的索引，默认第一个\n  lineStyle: 'display:none;', // 激活项边框线样式\n  useInnerLine: false, // 是否使用内部激活项边框线，当外部激活下划线未成功渲染时显示内部定位的\n  inited: false, // 是否初始化\n  animating: false, // 是否动画中\n  mapShow: false, // map的开关\n  scrollLeft: 0 // scroll-view偏移量\n})\n\nconst { children, linkChildren } = useChildren(TABS_KEY)\nlinkChildren({ state, props })\n\nconst { proxy } = getCurrentInstance() as any\n\nconst touch = useTouch()\n\nconst innerSlidable = computed(() => {\n  return props.slidable === 'always' || children.length > props.slidableNum\n})\n\nconst bodyStyle = computed(() => {\n  if (!props.animated) {\n    return ''\n  }\n\n  return objToStyle({\n    left: -100 * state.activeIndex + '%',\n    'transition-duration': props.duration + 'ms',\n    '-webkit-transition-duration': props.duration + 'ms'\n  })\n})\n\nconst getTabName = (tab: ComponentInstance<any>, index: number) => {\n  return isDef(tab.name) ? tab.name : index\n}\n\n/**\n * 更新激活项\n * @param value 激活值\n * @param init 是否已初始化\n * @param setScroll // 是否设置scroll-view滚动\n */\nconst updateActive = (value: number | string = 0, init: boolean = false, setScroll: boolean = true) => {\n  // 没有tab子元素，不执行任何操作\n  if (children.length === 0) return\n\n  value = getActiveIndex(value)\n  // 被禁用，不执行任何操作\n  if (children[value].disabled) return\n  state.activeIndex = value\n  if (setScroll) {\n    updateLineStyle(init === false)\n    scrollIntoView()\n  }\n  setActiveTab()\n}\n\n/**\n * @description 修改选中的tab Index\n * @param {String |Number } value - radio绑定的value或者tab索引，默认值0\n * @param {Boolean } init - 是否伴随初始化操作\n */\nconst setActive = debounce(updateActive, 100, { leading: true })\n\nwatch(\n  () => props.modelValue,\n  (newValue) => {\n    if (!isNumber(newValue) && !isString(newValue)) {\n      console.error('[wot design] error(wd-tabs): the type of value should be number or string')\n    }\n    // 保证不为非空字符串，小于0的数字\n    if (newValue === '' || !isDef(newValue)) {\n      // eslint-disable-next-line quotes\n      console.error(\"[wot design] error(wd-tabs): tabs's value cannot be '' null or undefined\")\n    }\n    if (typeof newValue === 'number' && newValue < 0) {\n      // eslint-disable-next-line quotes\n      console.error(\"[wot design] error(wd-tabs): tabs's value cannot be less than zero\")\n    }\n  },\n  {\n    immediate: true,\n    deep: true\n  }\n)\n\nwatch(\n  () => props.modelValue,\n  (newValue) => {\n    const index = getActiveIndex(newValue)\n    setActive(newValue, false, index !== state.activeIndex)\n  },\n  {\n    immediate: false,\n    deep: true\n  }\n)\n\nwatch(\n  () => children.length,\n  () => {\n    if (state.inited) {\n      nextTick(() => {\n        setActive(props.modelValue)\n      })\n    }\n  }\n)\n\nwatch(\n  () => props.slidableNum,\n  (newValue) => {\n    checkNumRange(newValue, 'slidableNum')\n  }\n)\n\nwatch(\n  () => props.mapNum,\n  (newValue) => {\n    checkNumRange(newValue, 'mapNum')\n  }\n)\n\nonMounted(() => {\n  state.inited = true\n  nextTick(() => {\n    updateActive(props.modelValue, true)\n    state.useInnerLine = true\n  })\n})\n\nfunction toggleMap() {\n  if (state.mapShow) {\n    state.animating = false\n    setTimeout(() => {\n      state.mapShow = false\n    }, 300)\n  } else {\n    state.mapShow = true\n    setTimeout(() => {\n      state.animating = true\n    }, 100)\n  }\n}\n\n/**\n * 更新 underline的偏移量\n * @param animation 是否开启动画\n */\nasync function updateLineStyle(animation: boolean = true) {\n  if (!state.inited) return\n  const { autoLineWidth, lineWidth, lineHeight } = props\n  try {\n    const lineStyle: CSSProperties = {}\n    if (isDef(lineWidth)) {\n      lineStyle.width = addUnit(lineWidth)\n    } else {\n      if (autoLineWidth) {\n        const textRects = await getRect($itemText, true, proxy)\n        const textWidth = Number(textRects[state.activeIndex].width)\n        lineStyle.width = addUnit(textWidth)\n      }\n    }\n    if (isDef(lineHeight)) {\n      lineStyle.height = addUnit(lineHeight)\n      lineStyle.borderRadius = `calc(${addUnit(lineHeight)} / 2)`\n    }\n    const rects = await getRect($item, true, proxy)\n    const rect = rects[state.activeIndex]\n    let left = rects.slice(0, state.activeIndex).reduce((prev, curr) => prev + Number(curr.width), 0) + Number(rect.width) / 2\n    if (left) {\n      lineStyle.transform = `translateX(${left}px) translateX(-50%)`\n      if (animation) {\n        lineStyle.transition = 'width 0.3s cubic-bezier(0.4, 0, 0.2, 1), transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);'\n      }\n      state.useInnerLine = false\n      state.lineStyle = objToStyle(lineStyle)\n    }\n  } catch (error) {\n    console.error('[wot design] error(wd-tabs): update line style failed', error)\n  }\n}\n\nfunction setActiveTab() {\n  if (!state.inited) return\n  const name = getTabName(children[state.activeIndex], state.activeIndex)\n  if (name !== props.modelValue) {\n    emit('change', {\n      index: state.activeIndex,\n      name: name\n    })\n    emit('update:modelValue', name)\n  }\n}\n\nfunction scrollIntoView() {\n  if (!state.inited) return\n  Promise.all([getRect($item, true, proxy), getRect($container, false, proxy)]).then(([navItemsRects, navRect]) => {\n    // 选中元素\n    const selectItem = navItemsRects[state.activeIndex]\n    // 选中元素之前的节点的宽度总和\n    const offsetLeft = (navItemsRects as any).slice(0, state.activeIndex).reduce((prev: any, curr: any) => prev + curr.width, 0)\n    // scroll-view滑动到selectItem的偏移量\n    const left = offsetLeft - ((navRect as any).width - Number(selectItem.width)) / 2\n    if (left === state.scrollLeft) {\n      state.scrollLeft = left + Math.random() / 10000\n    } else {\n      state.scrollLeft = left\n    }\n  })\n}\n\n/**\n * @description 单击tab的处理\n * @param index\n */\nfunction handleSelect(index: number) {\n  if (index === undefined) return\n  const { disabled } = children[index]\n  const name = getTabName(children[index], index)\n\n  if (disabled) {\n    emit('disabled', {\n      index,\n      name\n    })\n    return\n  }\n  state.mapShow && toggleMap()\n  setActive(index)\n  emit('click', {\n    index,\n    name\n  })\n}\nfunction onTouchStart(event: any) {\n  if (!props.swipeable) return\n  touch.touchStart(event)\n}\nfunction onTouchMove(event: any) {\n  if (!props.swipeable) return\n  touch.touchMove(event)\n}\nfunction onTouchEnd() {\n  if (!props.swipeable) return\n  const { direction, deltaX, offsetX } = touch\n  const minSwipeDistance = 50\n  if (direction.value === 'horizontal' && offsetX.value >= minSwipeDistance) {\n    if (deltaX.value > 0 && state.activeIndex !== 0) {\n      setActive(state.activeIndex - 1)\n    } else if (deltaX.value < 0 && state.activeIndex !== children.length - 1) {\n      setActive(state.activeIndex + 1)\n    }\n  }\n}\nfunction getActiveIndex(value: number | string) {\n  // name代表的索引超过了children长度的边界，自动用0兜底\n  if (isNumber(value) && value >= children.length) {\n    // eslint-disable-next-line prettier/prettier\n    console.error('[wot design] warning(wd-tabs): the type of tabs\\' value is Number shouldn\\'t be less than its children')\n    value = 0\n  }\n  // 如果是字符串直接匹配，匹配不到用0兜底\n  if (isString(value)) {\n    const index = children.findIndex((item) => item.name === value)\n    value = index === -1 ? 0 : index\n  }\n\n  return value\n}\n\ndefineExpose<TabsExpose>({\n  setActive,\n  scrollIntoView,\n  updateLineStyle\n})\n</script>\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-tabs/wd-tabs.vue'\nwx.createComponent(Component)"], "names": ["useTranslate", "reactive", "useChildren", "TABS_KEY", "getCurrentInstance", "useTouch", "computed", "objToStyle", "isDef", "debounce", "watch", "isNumber", "isString", "nextTick", "checkNumRange", "onMounted", "addUnit", "getRect"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2IA,MAAA,SAAmB,MAAA;AACnB,MAAA,WAAqB,MAAA;AACrB,MAAA,cAAwB,MAAA;AAZxB,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,gBAAgB;AAAA,EAAA;AAEpB;;;;;AAaA,UAAM,QAAQ;AACd,UAAM,YAAY;AAClB,UAAM,aAAa;AAEnB,UAAM,QAAQ;AACd,UAAM,OAAO;AAEb,UAAM,EAAE,UAAA,IAAcA,cAAA,aAAa,MAAM;AAEzC,UAAM,QAAQC,cAAAA,SAAS;AAAA,MACrB,aAAa;AAAA;AAAA,MACb,WAAW;AAAA;AAAA,MACX,cAAc;AAAA;AAAA,MACd,QAAQ;AAAA;AAAA,MACR,WAAW;AAAA;AAAA,MACX,SAAS;AAAA;AAAA,MACT,YAAY;AAAA;AAAA,IAAA,CACb;AAED,UAAM,EAAE,UAAU,iBAAiBC,cAAAA,YAAYC,cAAAA,QAAQ;AAC1C,iBAAA,EAAE,OAAO,OAAO;AAEvB,UAAA,EAAE,MAAM,IAAIC,iCAAmB;AAErC,UAAM,QAAQC,cAAAA,SAAS;AAEjB,UAAA,gBAAgBC,cAAAA,SAAS,MAAM;AACnC,aAAO,MAAM,aAAa,YAAY,SAAS,SAAS,MAAM;AAAA,IAAA,CAC/D;AAEK,UAAA,YAAYA,cAAAA,SAAS,MAAM;AAC3B,UAAA,CAAC,MAAM,UAAU;AACZ,eAAA;AAAA,MAAA;AAGT,aAAOC,yBAAW;AAAA,QAChB,MAAM,OAAO,MAAM,cAAc;AAAA,QACjC,uBAAuB,MAAM,WAAW;AAAA,QACxC,+BAA+B,MAAM,WAAW;AAAA,MAAA,CACjD;AAAA,IAAA,CACF;AAEK,UAAA,aAAa,CAAC,KAA6B,UAAkB;AACjE,aAAOC,cAAAA,MAAM,IAAI,IAAI,IAAI,IAAI,OAAO;AAAA,IACtC;AAQA,UAAM,eAAe,CAAC,QAAyB,GAAG,OAAgB,OAAO,YAAqB,SAAS;AAErG,UAAI,SAAS,WAAW;AAAG;AAE3B,cAAQ,eAAe,KAAK;AAExB,UAAA,SAAS,KAAK,EAAE;AAAU;AAC9B,YAAM,cAAc;AACpB,UAAI,WAAW;AACb,wBAAgB,SAAS,KAAK;AACf,uBAAA;AAAA,MAAA;AAEJ,mBAAA;AAAA,IACf;AAOA,UAAM,YAAYC,cAAAA,SAAS,cAAc,KAAK,EAAE,SAAS,MAAM;AAE/DC,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,aAAa;AACZ,YAAI,CAACC,cAAS,SAAA,QAAQ,KAAK,CAACC,cAAA,SAAS,QAAQ,GAAG;AAC9C,kBAAQ,MAAM,2EAA2E;AAAA,QAAA;AAG3F,YAAI,aAAa,MAAM,CAACJ,cAAA,MAAM,QAAQ,GAAG;AAEvC,kBAAQ,MAAM,0EAA0E;AAAA,QAAA;AAE1F,YAAI,OAAO,aAAa,YAAY,WAAW,GAAG;AAEhD,kBAAQ,MAAM,oEAAoE;AAAA,QAAA;AAAA,MAEtF;AAAA,MACA;AAAA,QACE,WAAW;AAAA,QACX,MAAM;AAAA,MAAA;AAAA,IAEV;AAEAE,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,aAAa;AACN,cAAA,QAAQ,eAAe,QAAQ;AACrC,kBAAU,UAAU,OAAO,UAAU,MAAM,WAAW;AAAA,MACxD;AAAA,MACA;AAAA,QACE,WAAW;AAAA,QACX,MAAM;AAAA,MAAA;AAAA,IAEV;AAEAA,kBAAA;AAAA,MACE,MAAM,SAAS;AAAA,MACf,MAAM;AACJ,YAAI,MAAM,QAAQ;AAChBG,wBAAAA,WAAS,MAAM;AACb,sBAAU,MAAM,UAAU;AAAA,UAAA,CAC3B;AAAA,QAAA;AAAA,MACH;AAAA,IAEJ;AAEAH,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,aAAa;AACZI,sBAAA,cAAc,UAAU,aAAa;AAAA,MAAA;AAAA,IAEzC;AAEAJ,kBAAA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,aAAa;AACZI,sBAAA,cAAc,UAAU,QAAQ;AAAA,MAAA;AAAA,IAEpC;AAEAC,kBAAAA,UAAU,MAAM;AACd,YAAM,SAAS;AACfF,oBAAAA,WAAS,MAAM;AACA,qBAAA,MAAM,YAAY,IAAI;AACnC,cAAM,eAAe;AAAA,MAAA,CACtB;AAAA,IAAA,CACF;AAED,aAAS,YAAY;AACnB,UAAI,MAAM,SAAS;AACjB,cAAM,YAAY;AAClB,mBAAW,MAAM;AACf,gBAAM,UAAU;AAAA,WACf,GAAG;AAAA,MAAA,OACD;AACL,cAAM,UAAU;AAChB,mBAAW,MAAM;AACf,gBAAM,YAAY;AAAA,WACjB,GAAG;AAAA,MAAA;AAAA,IACR;AAOa,aAAA,gBAAgB,YAAqB,MAAM;AAAA;AACxD,YAAI,CAAC,MAAM;AAAQ;AACnB,cAAM,EAAE,eAAe,WAAW,WAAe,IAAA;AAC7C,YAAA;AACF,gBAAM,YAA2B,CAAC;AAC9B,cAAAL,cAAAA,MAAM,SAAS,GAAG;AACV,sBAAA,QAAQQ,sBAAQ,SAAS;AAAA,UAAA,OAC9B;AACL,gBAAI,eAAe;AACjB,oBAAM,YAAY,MAAMC,cAAAA,QAAQ,WAAW,MAAM,KAAK;AACtD,oBAAM,YAAY,OAAO,UAAU,MAAM,WAAW,EAAE,KAAK;AACjD,wBAAA,QAAQD,sBAAQ,SAAS;AAAA,YAAA;AAAA,UACrC;AAEE,cAAAR,cAAAA,MAAM,UAAU,GAAG;AACX,sBAAA,SAASQ,sBAAQ,UAAU;AACrC,sBAAU,eAAe,QAAQA,cAAAA,QAAQ,UAAU,CAAC;AAAA,UAAA;AAEtD,gBAAM,QAAQ,MAAMC,cAAAA,QAAQ,OAAO,MAAM,KAAK;AACxC,gBAAA,OAAO,MAAM,MAAM,WAAW;AAChC,cAAA,OAAO,MAAM,MAAM,GAAG,MAAM,WAAW,EAAE,OAAO,CAAC,MAAM,SAAS,OAAO,OAAO,KAAK,KAAK,GAAG,CAAC,IAAI,OAAO,KAAK,KAAK,IAAI;AACzH,cAAI,MAAM;AACE,sBAAA,YAAY,cAAc,IAAI;AACxC,gBAAI,WAAW;AACb,wBAAU,aAAa;AAAA,YAAA;AAEzB,kBAAM,eAAe;AACf,kBAAA,YAAYV,yBAAW,SAAS;AAAA,UAAA;AAAA,iBAEjC,OAAO;AACN,kBAAA,MAAM,yDAAyD,KAAK;AAAA,QAAA;AAAA,MAC9E;AAAA;AAGF,aAAS,eAAe;AACtB,UAAI,CAAC,MAAM;AAAQ;AACnB,YAAM,OAAO,WAAW,SAAS,MAAM,WAAW,GAAG,MAAM,WAAW;AAClE,UAAA,SAAS,MAAM,YAAY;AAC7B,aAAK,UAAU;AAAA,UACb,OAAO,MAAM;AAAA,UACb;AAAA,QAAA,CACD;AACD,aAAK,qBAAqB,IAAI;AAAA,MAAA;AAAA,IAChC;AAGF,aAAS,iBAAiB;AACxB,UAAI,CAAC,MAAM;AAAQ;AACnB,cAAQ,IAAI,CAACU,cAAA,QAAQ,OAAO,MAAM,KAAK,GAAGA,cAAQ,QAAA,YAAY,OAAO,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,eAAe,OAAO,MAAM;AAEzG,cAAA,aAAa,cAAc,MAAM,WAAW;AAElD,cAAM,aAAc,cAAsB,MAAM,GAAG,MAAM,WAAW,EAAE,OAAO,CAAC,MAAW,SAAc,OAAO,KAAK,OAAO,CAAC;AAE3H,cAAM,OAAO,cAAe,QAAgB,QAAQ,OAAO,WAAW,KAAK,KAAK;AAC5E,YAAA,SAAS,MAAM,YAAY;AAC7B,gBAAM,aAAa,OAAO,KAAK,OAAW,IAAA;AAAA,QAAA,OACrC;AACL,gBAAM,aAAa;AAAA,QAAA;AAAA,MACrB,CACD;AAAA,IAAA;AAOH,aAAS,aAAa,OAAe;AACnC,UAAI,UAAU;AAAW;AACzB,YAAM,EAAE,SAAA,IAAa,SAAS,KAAK;AACnC,YAAM,OAAO,WAAW,SAAS,KAAK,GAAG,KAAK;AAE9C,UAAI,UAAU;AACZ,aAAK,YAAY;AAAA,UACf;AAAA,UACA;AAAA,QAAA,CACD;AACD;AAAA,MAAA;AAEF,YAAM,WAAW,UAAU;AAC3B,gBAAU,KAAK;AACf,WAAK,SAAS;AAAA,QACZ;AAAA,QACA;AAAA,MAAA,CACD;AAAA,IAAA;AAEH,aAAS,aAAa,OAAY;AAChC,UAAI,CAAC,MAAM;AAAW;AACtB,YAAM,WAAW,KAAK;AAAA,IAAA;AAExB,aAAS,YAAY,OAAY;AAC/B,UAAI,CAAC,MAAM;AAAW;AACtB,YAAM,UAAU,KAAK;AAAA,IAAA;AAEvB,aAAS,aAAa;AACpB,UAAI,CAAC,MAAM;AAAW;AACtB,YAAM,EAAE,WAAW,QAAQ,QAAY,IAAA;AACvC,YAAM,mBAAmB;AACzB,UAAI,UAAU,UAAU,gBAAgB,QAAQ,SAAS,kBAAkB;AACzE,YAAI,OAAO,QAAQ,KAAK,MAAM,gBAAgB,GAAG;AACrC,oBAAA,MAAM,cAAc,CAAC;AAAA,QAAA,WACtB,OAAO,QAAQ,KAAK,MAAM,gBAAgB,SAAS,SAAS,GAAG;AAC9D,oBAAA,MAAM,cAAc,CAAC;AAAA,QAAA;AAAA,MACjC;AAAA,IACF;AAEF,aAAS,eAAe,OAAwB;AAE9C,UAAIN,cAAS,SAAA,KAAK,KAAK,SAAS,SAAS,QAAQ;AAE/C,gBAAQ,MAAM,sGAAwG;AAC9G,gBAAA;AAAA,MAAA;AAGN,UAAAC,cAAAA,SAAS,KAAK,GAAG;AACnB,cAAM,QAAQ,SAAS,UAAU,CAAC,SAAS,KAAK,SAAS,KAAK;AACtD,gBAAA,UAAU,KAAK,IAAI;AAAA,MAAA;AAGtB,aAAA;AAAA,IAAA;AAGgB,aAAA;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjbD,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}