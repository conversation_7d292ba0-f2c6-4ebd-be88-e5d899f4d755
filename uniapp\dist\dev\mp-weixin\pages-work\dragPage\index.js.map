{"version": 3, "file": "index.js", "sources": ["../../../../../src/pages-work/dragPage/index.vue", "../../../../../uniPage:/cGFnZXMtd29ya1xkcmFnUGFnZVxpbmRleC52dWU"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n<PageLayout :navTitle=\"title\">\r\n <scroll-view class=\"scroll-area\" :scroll-y=\"true\" :scroll-with-animation=\"scrollAnimation\" :scroll-top=\"scrollTop\" :scroll-into-view=\"scrollToView\">\r\n\t<view v-for=\"(item,index) in dragData.compsData\" :key=\"index\">\r\n\t  <view class=\"mt-4 \" :class=\"[dragData.style=='bigScreen'?'bg-white':'bg-white']\" :id=\"'drag'+item.i\" :style=\"[getStyle(item.component)]\">\r\n\t\t\t<template v-if=\"compList.includes(item.component)\">\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\t\t\t\t<dynamic-component :compName=\"item.component\" :i.sync=\"item.i\" :config.sync=\"item.config\" :size=\"item.config?.size\"></dynamic-component>\r\n\r\n\t\t\t</template>\r\n\t\t  <template v-else>\r\n\t\t\t\t<view class=\"flex flex-col flex-justify-center flex-items-center\" style=\"min-height: 600upx;height:100%\">\r\n\t\t\t\t\t<wd-icon name=\"info-circle-filled\" size=\"64px\"></wd-icon>\r\n\t\t\t\t\t<view class=\"text-bold\">\r\n\t\t\t\t\t\t<text>暂不支持</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</template>\r\n\t\t</view>\r\n\t</view>\r\n </scroll-view>\r\n </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { compList, noActionList, COMP_NAME_PREFIX } from '../components/common/concants'\r\nimport { http } from '@/utils/http';\r\n\r\nimport dynamicComponent from '../components/echarts/dynamic-component.vue';\r\n\r\n\r\ndefineOptions({\r\n  name: 'dragPage',\r\n})\r\nconst title = ref('仪表盘示例');\r\nconst pageId = ref('');\r\nconst appId = ref('');\r\nconst compObj = ref({});\r\nconst dragData = ref({\r\n  name: '',\r\n  compsData: [],\r\n  style:'default'\r\n})\r\n\r\nconst scrollY = true;\r\nconst scrollAnimation = false;\r\nconst scrollTop = 0;\r\nconst scrollToView = '';\r\nconst getStyle = computed(()=>{\r\n\treturn (component: string)=> {\r\n\t\tlet isSetHeight = component === \"JDragEditor\"?false:true;\r\n\t\tif(component === \"JText\"){\r\n\t\t\treturn {\r\n\t\t\t\theight: 'auto',\r\n\t\t\t\tzIndex: 1000\r\n\t\t\t}\r\n\t\t};\r\n\t\tif(component === \"JCalendar\"){\r\n\t\t\treturn {\r\n        minHeight: '600rpx',\r\n\t\t\t\theight: 'auto',\r\n\t\t\t\tzIndex: 1000\r\n\t\t\t}\r\n\t\t};\r\n\t\tif(component === \"JList\"){\r\n\t\t\treturn {\r\n        minHeight: '400rpx',\r\n\t\t\t\theight: 'auto',\r\n\t\t\t\tzIndex: 1000\r\n\t\t\t}\r\n\t\t};\r\n\t\tif(component === \"JFilterQuery\"){\r\n\t\t\treturn {\r\n\t\t\t\theight: 'auto',\r\n\t\t\t\tzIndex: 1000\r\n\t\t\t}\r\n\t\t};\r\n\t\treturn {\r\n\t\t\tminHeight: '600rpx',\r\n\t\t\theight: isSetHeight ? '600rpx' : 'auto',\r\n\t\t\tzIndex: 1000\r\n\t\t}\r\n\t}\r\n})\r\n//查询仪表盘数据\r\nfunction queryData(){\r\n\thttp.get('/drag/page/queryById',{id:unref(pageId)})\r\n\t.then((res:any)=>{\r\n\t\tif (res.success && res.result) {\r\n\t\t\tlet result = res.result;\r\n\t\t\tlet template = result.template ? JSON.parse(result.template) : [];\r\n\t\t\tdragData.value.name = result.name;\r\n\t\t\tdragData.value.style = result?.style || \"default\";\r\n\t\t\ttitle.value = result.name;\r\n\t\t\ttemplate.forEach((item:any)=>{\r\n\t\t\t\tif(item.component === \"JFilterQuery\"){\r\n\t\t\t\t\titem[\"mobileY\"] = 0;\r\n\t\t\t\t}else{\r\n\t\t\t\t\titem[\"mobileY\"] = item[\"mobileY\"]||item[\"mobileY\"]==0?item[\"mobileY\"]:1;\r\n\t\t\t\t}\r\n\t\t\t\tif(item.config.filter && !item.config.filter.customTime){\r\n\t\t\t\t\titem.config.filter['customTime'] = [];\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\ttemplate.sort((a,b)=>a.mobileY - b.mobileY)\r\n\t\t\tdragData.value.compsData = template || [];\r\n\t\t}\r\n\t})\r\n}\r\nonLoad((option)=>{\r\n\tlet params:any = option;\r\n\tpageId.value = params.id;\r\n\tqueryData();\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n//\r\n</style>\r\n", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-work/dragPage/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "http", "unref", "onLoad"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CA,MAAA,mBAA6B,MAAA;AAiB7B,MAAM,kBAAkB;AACxB,MAAM,YAAY;AAClB,MAAM,eAAe;;;;;;AAbf,UAAA,QAAQA,kBAAI,OAAO;AACnB,UAAA,SAASA,kBAAI,EAAE;AACPA,kBAAAA,IAAI,EAAE;AACJA,kBAAAA,IAAI,CAAE,CAAA;AACtB,UAAM,WAAWA,cAAAA,IAAI;AAAA,MACnB,MAAM;AAAA,MACN,WAAW,CAAC;AAAA,MACZ,OAAM;AAAA,IAAA,CACP;AAMK,UAAA,WAAWC,cAAAA,SAAS,MAAI;AAC7B,aAAO,CAAC,cAAqB;AACxB,YAAA,cAAc,cAAc,gBAAc,QAAM;AACpD,YAAG,cAAc,SAAQ;AACjB,iBAAA;AAAA,YACN,QAAQ;AAAA,YACR,QAAQ;AAAA,UACT;AAAA,QAAA;AAED,YAAG,cAAc,aAAY;AACrB,iBAAA;AAAA,YACF,WAAW;AAAA,YACf,QAAQ;AAAA,YACR,QAAQ;AAAA,UACT;AAAA,QAAA;AAED,YAAG,cAAc,SAAQ;AACjB,iBAAA;AAAA,YACF,WAAW;AAAA,YACf,QAAQ;AAAA,YACR,QAAQ;AAAA,UACT;AAAA,QAAA;AAED,YAAG,cAAc,gBAAe;AACxB,iBAAA;AAAA,YACN,QAAQ;AAAA,YACR,QAAQ;AAAA,UACT;AAAA,QAAA;AAEM,eAAA;AAAA,UACN,WAAW;AAAA,UACX,QAAQ,cAAc,WAAW;AAAA,UACjC,QAAQ;AAAA,QACT;AAAA,MACD;AAAA,IAAA,CACA;AAED,aAAS,YAAW;AACdC,iBAAAA,KAAA,IAAI,wBAAuB,EAAC,IAAGC,cAAAA,MAAM,MAAM,GAAE,EACjD,KAAK,CAAC,QAAU;AACZ,YAAA,IAAI,WAAW,IAAI,QAAQ;AAC9B,cAAI,SAAS,IAAI;AACb,cAAA,WAAW,OAAO,WAAW,KAAK,MAAM,OAAO,QAAQ,IAAI,CAAC;AACvD,mBAAA,MAAM,OAAO,OAAO;AACpB,mBAAA,MAAM,SAAQ,iCAAQ,UAAS;AACxC,gBAAM,QAAQ,OAAO;AACZ,mBAAA,QAAQ,CAAC,SAAW;AACzB,gBAAA,KAAK,cAAc,gBAAe;AACpC,mBAAK,SAAS,IAAI;AAAA,YAAA,OACd;AACC,mBAAA,SAAS,IAAI,KAAK,SAAS,KAAG,KAAK,SAAS,KAAG,IAAE,KAAK,SAAS,IAAE;AAAA,YAAA;AAEvE,gBAAG,KAAK,OAAO,UAAU,CAAC,KAAK,OAAO,OAAO,YAAW;AACvD,mBAAK,OAAO,OAAO,YAAY,IAAI,CAAC;AAAA,YAAA;AAAA,UACrC,CACA;AACD,mBAAS,KAAK,CAAC,GAAE,MAAI,EAAE,UAAU,EAAE,OAAO;AACjC,mBAAA,MAAM,YAAY,YAAY,CAAC;AAAA,QAAA;AAAA,MACzC,CACA;AAAA,IAAA;AAEFC,kBAAA,OAAO,CAAC,WAAS;AAChB,UAAI,SAAa;AACjB,aAAO,QAAQ,OAAO;AACZ,gBAAA;AAAA,IAAA,CACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9HD,GAAG,WAAW,eAAe;"}