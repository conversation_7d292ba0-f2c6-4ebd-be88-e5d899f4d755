{"version": 3, "file": "index.js", "sources": ["../../../../../../../src/pages-work/components/echarts/JRing/index.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvcGFnZXMtd29yay9jb21wb25lbnRzL2VjaGFydHMvSlJpbmcvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"content\">\r\n    <statusTip v-if=\"pageTips.show\" :status=\"pageTips.status\"></statusTip>\r\n    <echartsUniapp v-else :option=\"option\"></echartsUniapp>\r\n  </view>\r\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { echartProps } from '@/pages-work/components/echarts/props'\r\nimport { deepMerge, handleTotalAndUnit, getCustomColor } from '../../common/echartUtil'\r\nimport { isNumber } from '@/utils/is'\r\nimport useChartHook from '@/pages-work/components/hooks/useEchart'\r\nimport { deepClone } from '@/uni_modules/da-tree/utils'\r\nimport echartsUniapp from '@/pages-work/components/echarts/index.vue'\r\nimport statusTip from '@/pages-work/components/statusTip.vue'\r\nimport { onMounted } from 'vue'\r\nimport {merge} from \"lodash-es\";\r\n//组件传参\r\nconst props = defineProps({\r\n  ...echartProps,\r\n})\r\n//最终图表配置项\r\nconst option = ref({})\r\n//操作图表配置项\r\nlet chartOption = {\r\n  title: {\r\n    show: true,\r\n  },\r\n  card: {\r\n    title: '',\r\n  },\r\n  legend: {\r\n    t: 0,\r\n    r: 0,\r\n  },\r\n  tooltip: {\r\n    formatter: '',\r\n  },\r\n  series: [{}] as any,\r\n}\r\n//图表数据查询\r\nlet [{ dataSource, reload, pageTips, config }, { queryData }] = useChartHook(props, initOption)\r\n\r\n//初始化图表配置项\r\nfunction initOption(data) {\r\n  let chartData: any = dataSource.value\r\n  if (typeof chartData === 'string') {\r\n    chartData = JSON.parse(chartData)\r\n  }\r\n  if (chartData && chartData.length > 0) {\r\n    //显示坐标轴前几项\r\n    if (config.dataFilterNum && isNumber(config.dataFilterNum)) {\r\n      chartData = chartData.slice(0, config.dataFilterNum)\r\n    }\r\n    const colors = getCustomColor(config.option.customColor)\r\n    //设置配色\r\n    chartData = chartData.map((item, index) => {\r\n      let legendColor = config.option.series[0].color ? config.option.series[0].color[index] : null\r\n      return {\r\n        ...item,\r\n        itemStyle: { color: legendColor || colors[index].color || null },\r\n      }\r\n    })\r\n    chartOption.series[0].data = chartData\r\n    chartOption.series[0].center = [\r\n      (config?.option?.grid?.left || 50) + '%',\r\n      (config?.option?.grid?.top || 50) + '%',\r\n    ]\r\n\r\n    if (props.config && config.option) {\r\n      merge(chartOption, config.option)\r\n      chartOption = handleTotalAndUnit(props.compName, chartOption, config, chartData)\r\n      chartOption.series[0]['label']['position'] = 'outside'\r\n      chartOption.tooltip.formatter = '{b} : {c}'\r\n      option.value = deepClone(chartOption)\r\n      pageTips.show = false\r\n    }\r\n  } else {\r\n    pageTips.status = 1\r\n    pageTips.show = true\r\n  }\r\n}\r\n\r\nonMounted(() => {\r\n  queryData()\r\n})\r\n</script>\r\n<style>\r\n.content {\r\n  margin: 5px;\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-work/components/echarts/JRing/index.vue'\nwx.createComponent(Component)"], "names": ["ref", "useChartHook", "isNumber", "getCustomColor", "merge", "handleTotalAndUnit", "deepClone", "onMounted", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaA,MAAA,gBAA0B,MAAA;AAC1B,MAAA,YAAsB,MAAA;;;;;AAItB,UAAM,QAAQ;AAIR,UAAA,SAASA,cAAI,IAAA,EAAE;AAErB,QAAI,cAAc;AAAA,MAChB,OAAO;AAAA,QACL,MAAM;AAAA,MACR;AAAA,MACA,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AAAA,MACA,SAAS;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,QAAQ,CAAC,CAAE,CAAA;AAAA,IACb;AAEA,QAAI,CAAC,EAAE,YAAY,QAAQ,UAAU,OAAA,GAAU,EAAE,WAAW,IAAIC,kDAAa,OAAO,UAAU;AAG9F,aAAS,WAAW,MAAM;;AACxB,UAAI,YAAiB,WAAW;AAC5B,UAAA,OAAO,cAAc,UAAU;AACrB,oBAAA,KAAK,MAAM,SAAS;AAAA,MAAA;AAE9B,UAAA,aAAa,UAAU,SAAS,GAAG;AAErC,YAAI,OAAO,iBAAiBC,SAAS,SAAA,OAAO,aAAa,GAAG;AAC1D,sBAAY,UAAU,MAAM,GAAG,OAAO,aAAa;AAAA,QAAA;AAErD,cAAM,SAASC,uCAAA,eAAe,OAAO,OAAO,WAAW;AAEvD,oBAAY,UAAU,IAAI,CAAC,MAAM,UAAU;AACzC,cAAI,cAAc,OAAO,OAAO,OAAO,CAAC,EAAE,QAAQ,OAAO,OAAO,OAAO,CAAC,EAAE,MAAM,KAAK,IAAI;AAClF,iBAAA,iCACF,OADE;AAAA,YAEL,WAAW,EAAE,OAAO,eAAe,OAAO,KAAK,EAAE,SAAS,KAAK;AAAA,UACjE;AAAA,QAAA,CACD;AACW,oBAAA,OAAO,CAAC,EAAE,OAAO;AACjB,oBAAA,OAAO,CAAC,EAAE,SAAS;AAAA,aAC5B,4CAAQ,WAAR,mBAAgB,SAAhB,mBAAsB,SAAQ,MAAM;AAAA,aACpC,4CAAQ,WAAR,mBAAgB,SAAhB,mBAAsB,QAAO,MAAM;AAAA,QACtC;AAEI,YAAA,MAAM,UAAU,OAAO,QAAQ;AAC3BC,8BAAA,aAAa,OAAO,MAAM;AAChC,wBAAcC,uCAAmB,mBAAA,MAAM,UAAU,aAAa,QAAQ,SAAS;AAC/E,sBAAY,OAAO,CAAC,EAAE,OAAO,EAAE,UAAU,IAAI;AAC7C,sBAAY,QAAQ,YAAY;AACzB,iBAAA,QAAQC,mCAAU,WAAW;AACpC,mBAAS,OAAO;AAAA,QAAA;AAAA,MAClB,OACK;AACL,iBAAS,SAAS;AAClB,iBAAS,OAAO;AAAA,MAAA;AAAA,IAClB;AAGFC,kBAAAA,UAAU,MAAM;AACJ,gBAAA;AAAA,IAAA,CACX;;;;;;;;;;;;;;;;ACpFD,GAAG,gBAAgBC,SAAS;"}