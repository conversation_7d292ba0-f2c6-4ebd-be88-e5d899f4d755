"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../../common/vendor.js");
const __default__ = {
  name: "wd-col",
  options: {
    addGlobalClass: true,
    virtualHost: true,
    styleIsolation: "shared"
  }
};
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, __default__), {
  props: common_vendor.colProps,
  setup(__props) {
    const props = __props;
    const { parent: row } = common_vendor.useParent(common_vendor.ROW_KEY);
    const rootStyle = common_vendor.computed(() => {
      const gutter = common_vendor.isDef(row) ? row.props.gutter || 0 : 0;
      const padding = `${gutter / 2}px`;
      const style = gutter > 0 ? `padding-left: ${padding}; padding-right: ${padding};background-clip: content-box;` : "";
      return `${style}${props.customStyle}`;
    });
    common_vendor.watch([() => props.span, () => props.offset], () => {
      check();
    });
    function check() {
      const { span, offset } = props;
      if (span < 0 || offset < 0) {
        console.error("[wot-design] warning(wd-col): attribute span/offset must be greater than or equal to 0");
      }
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.n(_ctx.span && "wd-col__" + _ctx.span),
        b: common_vendor.n(_ctx.offset && "wd-col__offset-" + _ctx.offset),
        c: common_vendor.n(_ctx.customClass),
        d: common_vendor.s(rootStyle.value)
      };
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-ce06229d"]]);
wx.createComponent(Component);
//# sourceMappingURL=wd-col.js.map
