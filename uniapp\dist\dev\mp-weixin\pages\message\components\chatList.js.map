{"version": 3, "file": "chatList.js", "sources": ["../../../../../../src/pages/message/components/chatList.vue", "../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvcGFnZXMvbWVzc2FnZS9jb21wb25lbnRzL2NoYXRMaXN0LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <z-paging\r\n    ref=\"paging\"\r\n    :fixed=\"false\"\r\n    v-model=\"dataList\"\r\n    @query=\"queryList\"\r\n    :default-page-size=\"100\"\r\n  >\r\n    <uni-list>\r\n      <template v-for=\"(item, index) in dataList\" :key=\"index\">\r\n        <view\r\n          :class=\"{ list: true, isTop: item.izTop == 1 }\"\r\n          @longpress.prevent=\"handleLongPress(item)\"\r\n          @click=\"handleGo(item)\"\r\n        >\r\n          <template v-if=\"['systemNotice'].includes(item.type)\">\r\n            <view class=\"avatar\">\r\n              <text class=\"cuIcon-notice text-white\"></text>\r\n            </view>\r\n          </template>\r\n          <template v-if=\"['group'].includes(item.type) && !item.fromAvatar\">\r\n            <view class=\"group avatar\">\r\n              <text class=\"text-white\">{{ getFirstStr(item.fromUserName) }}</text>\r\n            </view>\r\n          </template>\r\n          <uni-list-chat\r\n            avatarCircle\r\n            clickable\r\n            :title=\"item.fromUserName\"\r\n            :avatar=\"getAvatar(item)\"\r\n            :note=\"getChatType(item)\"\r\n            :time=\"beautifyTime(item.sendTime)\"\r\n            badge-positon=\"left\"\r\n            :badge-text=\"item.unreadNum\"\r\n            @click=\"handleGo(item)\"\r\n          ></uni-list-chat>\r\n        </view>\r\n      </template>\r\n    </uni-list>\r\n  </z-paging>\r\n  <BottomOperate\r\n    v-if=\"bottomOperatePopup.show\"\r\n    v-bind=\"bottomOperatePopup\"\r\n    @close=\"() => (bottomOperatePopup.show = false)\"\r\n    @change=\"handleChange\"\r\n  ></BottomOperate>\r\n  <wd-toast />\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { onLaunch, onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'\r\nimport { http } from '@/utils/http'\r\nimport { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'\r\nimport { useRouter } from '@/plugin/uni-mini-router'\r\nimport { beautifyTime } from '@/common/uitls'\r\nimport { useParamsStore } from '@/store/page-params'\r\n\r\ndefineOptions({\r\n  name: 'chatList',\r\n  options: {\r\n    styleIsolation: 'shared',\r\n  },\r\n})\r\nconst toast = useToast()\r\nconst router = useRouter()\r\nconst paramsStore = useParamsStore()\r\nconst paging = ref(null)\r\nconst avatarList = ref()\r\nconst dataList = ref([])\r\n\r\nconst options = [\r\n  { key: 'backtop', icon: 'backtop', label: '置顶' },\r\n  { key: 'cancelbacktop', icon: 'translate-bold', label: '取消置顶' },\r\n  { key: 'delete', icon: 'delete', label: '删除', color: 'red' },\r\n]\r\nconst bottomOperatePopup = reactive({\r\n  show: false,\r\n  title: '',\r\n  data: {},\r\n  options: [],\r\n})\r\n\r\nconst queryList = () => {\r\n  http\r\n    .get('/eoa/im/newApi/getChatList')\r\n    .then((res: any) => {\r\n      if (res.success) {\r\n        paging.value.complete(res.result.logVoList)\r\n        nextTick(() => {\r\n          setTimeout(() => {\r\n            sortByIzTop(dataList.value)\r\n            dataList.value = [...dataList.value]\r\n          }, 10)\r\n        })\r\n      } else {\r\n        paging.value.complete(false)\r\n      }\r\n    })\r\n    .catch((res) => {\r\n      paging.value.complete(false)\r\n    })\r\n}\r\nconst sortByIzTop = (arr) => {\r\n  return arr.sort((a, b) => {\r\n    if (a.izTop && !b.izTop) {\r\n      return -1 // a 排在 b 前面\r\n    } else if (!a.izTop && b.izTop) {\r\n      return 1 // b 排在 a 前面\r\n    } else {\r\n      return 0 // 保持原有顺序\r\n    }\r\n  })\r\n}\r\nconst getFirstStr = (val) => {\r\n  return val ? val.substr(0, 1) : val\r\n}\r\nconst getAvatar = (item) => {\r\n  if (['systemNotice'].includes(item.type)) {\r\n  } else if (['group'].includes(item.type)) {\r\n  }\r\n  return item.fromAvatar ?? ''\r\n}\r\nconst getChatType = (item) => {\r\n  switch (item.type) {\r\n    case 'discussion':\r\n      return '[组消息]'\r\n    case 'systemNotice':\r\n      return '[系统消息]'\r\n    case 'friend':\r\n      return item.status == 'offline' ? '[离线]' : '[在线]'\r\n    case 'group':\r\n      return '[群消息]'\r\n    default:\r\n      return ''\r\n  }\r\n}\r\nconst handleLongPress = (item) => {\r\n  bottomOperatePopup.show = true\r\n  bottomOperatePopup.title = item.fromUserName\r\n  bottomOperatePopup.data = item\r\n  bottomOperatePopup.options = options.filter((o) => {\r\n    if (o.key == 'backtop' && item.izTop == 1) {\r\n      return false\r\n    } else if (o.key == 'cancelbacktop' && item.izTop == 0) {\r\n      return false\r\n    }\r\n    return true\r\n  })\r\n}\r\nconst handleChange = ({ option, data }) => {\r\n  if (['cancelbacktop', 'backtop'].includes(option.key)) {\r\n    let izTop = 1\r\n    if (option.key === 'cancelbacktop') {\r\n      izTop = 0\r\n    }\r\n    http\r\n      .post('/eoa/im/newApi/chatToTop', {\r\n        id: data.id,\r\n        izTop,\r\n      })\r\n      .then((res: any) => {\r\n        if (res.success) {\r\n          paging.value.reload()\r\n        }\r\n      })\r\n  } else if (option.key === 'delete') {\r\n    http\r\n      .post('/eoa/im/newApi/removeChat', {\r\n        id: data.id,\r\n      })\r\n      .then((res: any) => {\r\n        if (res.success) {\r\n          paging.value.reload()\r\n        }\r\n      })\r\n  }\r\n}\r\n// 跳转\r\nconst handleGo = (item) => {\r\n  if (['systemNotice'].includes(item.type)) {\r\n    //1.系统消息\r\n    router.push({ name: 'annotationList', params: { backRouteName: 'message' } })\r\n  } else if (['friend'].includes(item.type)) {\r\n    //2.聊天\r\n    paramsStore.setPageParams('chat', { data: item })\r\n    router.push({ name: 'chat' })\r\n  } else {\r\n    ///3.群组和讨论组\r\n    // TODO\r\n    toast.warning('暂不支持')\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.z-paging-content {\r\n  background-color: #f1f1f1;\r\n}\r\n.list {\r\n  &.isTop {\r\n    :deep(.uni-list-chat) {\r\n      background-color: #eee;\r\n    }\r\n  }\r\n  position: relative;\r\n  .avatar {\r\n    &.group {\r\n      background-color: #f37b1d;\r\n      font-size: 18px;\r\n    }\r\n    position: absolute;\r\n    top: 10px;\r\n    left: 15px;\r\n    background-color: #0081ff;\r\n    font-size: 24px;\r\n    width: 45px;\r\n    height: 45px;\r\n    z-index: 1;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    border-radius: 50%;\r\n  }\r\n}\r\n:deep(.uni-list-chat) {\r\n  .uni-list-chat__badge {\r\n    z-index: 2;\r\n  }\r\n  .uni-list-chat__content-title {\r\n    color: #9ca3af;\r\n  }\r\n  .uni-list-chat__content-title {\r\n    font-size: 15px;\r\n  }\r\n  .uni-list-chat__header {\r\n    background-color: #eee;\r\n  }\r\n}\r\n:deep(.wd-popup) {\r\n  &.wd-popup--bottom {\r\n    bottom: 50px;\r\n  }\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages/message/components/chatList.vue'\nwx.createComponent(Component)"], "names": ["useToast", "useRouter", "useParamsStore", "ref", "reactive", "http", "nextTick"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+DA,UAAM,QAAQA,cAAAA,SAAS;AACvB,UAAM,SAASC,gCAAAA,UAAU;AACzB,UAAM,cAAcC,iBAAAA,eAAe;AAC7B,UAAA,SAASC,kBAAI,IAAI;AACJA,kBAAI,IAAA;AACjB,UAAA,WAAWA,cAAI,IAAA,EAAE;AAEvB,UAAM,UAAU;AAAA,MACd,EAAE,KAAK,WAAW,MAAM,WAAW,OAAO,KAAK;AAAA,MAC/C,EAAE,KAAK,iBAAiB,MAAM,kBAAkB,OAAO,OAAO;AAAA,MAC9D,EAAE,KAAK,UAAU,MAAM,UAAU,OAAO,MAAM,OAAO,MAAM;AAAA,IAC7D;AACA,UAAM,qBAAqBC,cAAAA,SAAS;AAAA,MAClC,MAAM;AAAA,MACN,OAAO;AAAA,MACP,MAAM,CAAC;AAAA,MACP,SAAS,CAAA;AAAA,IAAC,CACX;AAED,UAAM,YAAY,MAAM;AACtBC,iBAAAA,KACG,IAAI,4BAA4B,EAChC,KAAK,CAAC,QAAa;AAClB,YAAI,IAAI,SAAS;AACf,iBAAO,MAAM,SAAS,IAAI,OAAO,SAAS;AAC1CC,wBAAAA,WAAS,MAAM;AACb,uBAAW,MAAM;AACf,0BAAY,SAAS,KAAK;AAC1B,uBAAS,QAAQ,CAAC,GAAG,SAAS,KAAK;AAAA,eAClC,EAAE;AAAA,UAAA,CACN;AAAA,QAAA,OACI;AACE,iBAAA,MAAM,SAAS,KAAK;AAAA,QAAA;AAAA,MAC7B,CACD,EACA,MAAM,CAAC,QAAQ;AACP,eAAA,MAAM,SAAS,KAAK;AAAA,MAAA,CAC5B;AAAA,IACL;AACM,UAAA,cAAc,CAAC,QAAQ;AAC3B,aAAO,IAAI,KAAK,CAAC,GAAG,MAAM;AACxB,YAAI,EAAE,SAAS,CAAC,EAAE,OAAO;AAChB,iBAAA;AAAA,QACE,WAAA,CAAC,EAAE,SAAS,EAAE,OAAO;AACvB,iBAAA;AAAA,QAAA,OACF;AACE,iBAAA;AAAA,QAAA;AAAA,MACT,CACD;AAAA,IACH;AACM,UAAA,cAAc,CAAC,QAAQ;AAC3B,aAAO,MAAM,IAAI,OAAO,GAAG,CAAC,IAAI;AAAA,IAClC;AACM,UAAA,YAAY,CAAC,SAAS;;AAC1B,UAAI,CAAC,cAAc,EAAE,SAAS,KAAK,IAAI;AAAG;AAAA,eAC/B,CAAC,OAAO,EAAE,SAAS,KAAK,IAAI;AAAG;AAE1C,cAAO,UAAK,eAAL,YAAmB;AAAA,IAC5B;AACM,UAAA,cAAc,CAAC,SAAS;AAC5B,cAAQ,KAAK,MAAM;AAAA,QACjB,KAAK;AACI,iBAAA;AAAA,QACT,KAAK;AACI,iBAAA;AAAA,QACT,KAAK;AACI,iBAAA,KAAK,UAAU,YAAY,SAAS;AAAA,QAC7C,KAAK;AACI,iBAAA;AAAA,QACT;AACS,iBAAA;AAAA,MAAA;AAAA,IAEb;AACM,UAAA,kBAAkB,CAAC,SAAS;AAChC,yBAAmB,OAAO;AAC1B,yBAAmB,QAAQ,KAAK;AAChC,yBAAmB,OAAO;AAC1B,yBAAmB,UAAU,QAAQ,OAAO,CAAC,MAAM;AACjD,YAAI,EAAE,OAAO,aAAa,KAAK,SAAS,GAAG;AAClC,iBAAA;AAAA,QAAA,WACE,EAAE,OAAO,mBAAmB,KAAK,SAAS,GAAG;AAC/C,iBAAA;AAAA,QAAA;AAEF,eAAA;AAAA,MAAA,CACR;AAAA,IACH;AACA,UAAM,eAAe,CAAC,EAAE,QAAQ,WAAW;AACzC,UAAI,CAAC,iBAAiB,SAAS,EAAE,SAAS,OAAO,GAAG,GAAG;AACrD,YAAI,QAAQ;AACR,YAAA,OAAO,QAAQ,iBAAiB;AAC1B,kBAAA;AAAA,QAAA;AAEVD,mBAAA,KACG,KAAK,4BAA4B;AAAA,UAChC,IAAI,KAAK;AAAA,UACT;AAAA,QAAA,CACD,EACA,KAAK,CAAC,QAAa;AAClB,cAAI,IAAI,SAAS;AACf,mBAAO,MAAM,OAAO;AAAA,UAAA;AAAA,QACtB,CACD;AAAA,MAAA,WACM,OAAO,QAAQ,UAAU;AAClCA,mBAAA,KACG,KAAK,6BAA6B;AAAA,UACjC,IAAI,KAAK;AAAA,QAAA,CACV,EACA,KAAK,CAAC,QAAa;AAClB,cAAI,IAAI,SAAS;AACf,mBAAO,MAAM,OAAO;AAAA,UAAA;AAAA,QACtB,CACD;AAAA,MAAA;AAAA,IAEP;AAEM,UAAA,WAAW,CAAC,SAAS;AACzB,UAAI,CAAC,cAAc,EAAE,SAAS,KAAK,IAAI,GAAG;AAEjC,eAAA,KAAK,EAAE,MAAM,kBAAkB,QAAQ,EAAE,eAAe,UAAU,GAAG;AAAA,MAAA,WACnE,CAAC,QAAQ,EAAE,SAAS,KAAK,IAAI,GAAG;AAEzC,oBAAY,cAAc,QAAQ,EAAE,MAAM,MAAM;AAChD,eAAO,KAAK,EAAE,MAAM,OAAA,CAAQ;AAAA,MAAA,OACvB;AAGL,cAAM,QAAQ,MAAM;AAAA,MAAA;AAAA,IAExB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9LA,GAAG,gBAAgB,SAAS;"}