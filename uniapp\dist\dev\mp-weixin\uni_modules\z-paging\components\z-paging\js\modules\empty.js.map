{"version": 3, "file": "empty.js", "sources": ["../../../../../../../../../src/uni_modules/z-paging/components/z-paging/js/modules/empty.js"], "sourcesContent": ["// [z-paging]空数据图view模块\r\nimport u from '.././z-paging-utils'\r\n\r\nexport default {\r\n\tprops: {\r\n\t\t// 是否强制隐藏空数据图，默认为否\r\n\t\thideEmptyView: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('hideEmptyView', false)\r\n\t\t},\r\n\t\t// 空数据图描述文字，默认为“没有数据哦~”\r\n\t\temptyViewText: {\r\n\t\t\ttype: [String, Object],\r\n\t\t\tdefault: u.gc('emptyViewText', null)\r\n\t\t},\r\n\t\t// 是否显示空数据图重新加载按钮(无数据时)，默认为否\r\n\t\tshowEmptyViewReload: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('showEmptyViewReload', false)\r\n\t\t},\r\n\t\t// 加载失败时是否显示空数据图重新加载按钮，默认为是\r\n\t\tshowEmptyViewReloadWhenError: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('showEmptyViewReloadWhenError', true)\r\n\t\t},\r\n\t\t// 空数据图点击重新加载文字，默认为“重新加载”\r\n\t\temptyViewReloadText: {\r\n\t\t\ttype: [String, Object],\r\n\t\t\tdefault: u.gc('emptyViewReloadText', null)\r\n\t\t},\r\n\t\t// 空数据图图片，默认使用z-paging内置的图片\r\n\t\temptyViewImg: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('emptyViewImg', '')\r\n\t\t},\r\n\t\t// 空数据图“加载失败”描述文字，默认为“很抱歉，加载失败”\r\n\t\temptyViewErrorText: {\r\n\t\t\ttype: [String, Object],\r\n\t\t\tdefault: u.gc('emptyViewErrorText', null)\r\n\t\t},\r\n\t\t// 空数据图“加载失败”图片，默认使用z-paging内置的图片\r\n\t\temptyViewErrorImg: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('emptyViewErrorImg', '')\r\n\t\t},\r\n\t\t// 空数据图样式\r\n\t\temptyViewStyle: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault: u.gc('emptyViewStyle', {})\r\n\t\t},\r\n\t\t// 空数据图容器样式\r\n\t\temptyViewSuperStyle: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault: u.gc('emptyViewSuperStyle', {})\r\n\t\t},\r\n\t\t// 空数据图img样式\r\n\t\temptyViewImgStyle: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault: u.gc('emptyViewImgStyle', {})\r\n\t\t},\r\n\t\t// 空数据图描述文字样式\r\n\t\temptyViewTitleStyle: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault: u.gc('emptyViewTitleStyle', {})\r\n\t\t},\r\n\t\t// 空数据图重新加载按钮样式\r\n\t\temptyViewReloadStyle: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault: u.gc('emptyViewReloadStyle', {})\r\n\t\t},\r\n\t\t// 空数据图片是否铺满z-paging，默认为否，即填充满z-paging内列表(滚动区域)部分。若设置为否，则为填铺满整个z-paging\r\n\t\temptyViewFixed: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('emptyViewFixed', false)\r\n\t\t},\r\n\t\t// 空数据图片是否垂直居中，默认为是，若设置为否即为从空数据容器顶部开始显示。emptyViewFixed为false时有效\r\n\t\temptyViewCenter: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('emptyViewCenter', true)\r\n\t\t},\r\n\t\t// 加载中时是否自动隐藏空数据图，默认为是\r\n\t\tautoHideEmptyViewWhenLoading: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('autoHideEmptyViewWhenLoading', true)\r\n\t\t},\r\n\t\t// 用户下拉列表触发下拉刷新加载中时是否自动隐藏空数据图，默认为是\r\n\t\tautoHideEmptyViewWhenPull: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('autoHideEmptyViewWhenPull', true)\r\n\t\t},\r\n\t\t// 空数据view的z-index，默认为9\r\n\t\temptyViewZIndex: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: u.gc('emptyViewZIndex', 9)\r\n\t\t},\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tcustomerEmptyViewErrorText: ''\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\tfinalEmptyViewImg() {\r\n\t\t\treturn this.isLoadFailed ? this.emptyViewErrorImg : this.emptyViewImg;\r\n\t\t},\r\n\t\tfinalShowEmptyViewReload() {\r\n\t\t\treturn this.isLoadFailed ? this.showEmptyViewReloadWhenError : this.showEmptyViewReload;\r\n\t\t},\r\n\t\t// 是否展示空数据图\r\n\t\tshowEmpty() {\r\n\t\t\tif (this.refresherOnly || this.hideEmptyView || this.realTotalData.length) return false;\r\n\t\t\tif (this.autoHideEmptyViewWhenLoading) {\r\n\t\t\t\tif (this.isAddedData && !this.firstPageLoaded && !this.loading) return true;\r\n\t\t\t} else {\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\t\t\treturn !this.autoHideEmptyViewWhenPull && !this.isUserReload;\r\n\t\t},\r\n\t},\r\n\tmethods: {\r\n\t\t// 点击了空数据view重新加载按钮\r\n\t\t_emptyViewReload() {\r\n\t\t\tlet callbacked = false;\r\n\t\t\tthis.$emit('emptyViewReload', reload => {\r\n\t\t\t\tif (reload === undefined || reload === true) {\r\n\t\t\t\t\tthis.fromEmptyViewReload = true;\r\n\t\t\t\t\tthis.reload().catch(() => {});\r\n\t\t\t\t}\r\n\t\t\t\tcallbacked = true;\r\n\t\t\t});\r\n\t\t\t// 如果用户没有禁止默认的点击重新加载刷新列表事件，则触发列表重新刷新\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tif (!callbacked) {\r\n\t\t\t\t\tthis.fromEmptyViewReload = true;\r\n\t\t\t\t\tthis.reload().catch(() => {});\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 点击了空数据view\r\n\t\t_emptyViewClick() {\r\n\t\t\tthis.$emit('emptyViewClick');\r\n\t\t},\r\n\t}\r\n}"], "names": ["u"], "mappings": ";;AAGA,MAAe,cAAA;AAAA,EACd,OAAO;AAAA;AAAA,IAEN,eAAe;AAAA,MACd,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,iBAAiB,KAAK;AAAA,IACpC;AAAA;AAAA,IAED,eAAe;AAAA,MACd,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,iBAAiB,IAAI;AAAA,IACnC;AAAA;AAAA,IAED,qBAAqB;AAAA,MACpB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,uBAAuB,KAAK;AAAA,IAC1C;AAAA;AAAA,IAED,8BAA8B;AAAA,MAC7B,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,gCAAgC,IAAI;AAAA,IAClD;AAAA;AAAA,IAED,qBAAqB;AAAA,MACpB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,uBAAuB,IAAI;AAAA,IACzC;AAAA;AAAA,IAED,cAAc;AAAA,MACb,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,gBAAgB,EAAE;AAAA,IAChC;AAAA;AAAA,IAED,oBAAoB;AAAA,MACnB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,sBAAsB,IAAI;AAAA,IACxC;AAAA;AAAA,IAED,mBAAmB;AAAA,MAClB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,qBAAqB,EAAE;AAAA,IACrC;AAAA;AAAA,IAED,gBAAgB;AAAA,MACf,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,kBAAkB,CAAA,CAAE;AAAA,IAClC;AAAA;AAAA,IAED,qBAAqB;AAAA,MACpB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,uBAAuB,CAAA,CAAE;AAAA,IACvC;AAAA;AAAA,IAED,mBAAmB;AAAA,MAClB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,qBAAqB,CAAA,CAAE;AAAA,IACrC;AAAA;AAAA,IAED,qBAAqB;AAAA,MACpB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,uBAAuB,CAAA,CAAE;AAAA,IACvC;AAAA;AAAA,IAED,sBAAsB;AAAA,MACrB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,wBAAwB,CAAA,CAAE;AAAA,IACxC;AAAA;AAAA,IAED,gBAAgB;AAAA,MACf,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,kBAAkB,KAAK;AAAA,IACrC;AAAA;AAAA,IAED,iBAAiB;AAAA,MAChB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,mBAAmB,IAAI;AAAA,IACrC;AAAA;AAAA,IAED,8BAA8B;AAAA,MAC7B,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,gCAAgC,IAAI;AAAA,IAClD;AAAA;AAAA,IAED,2BAA2B;AAAA,MAC1B,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,6BAA6B,IAAI;AAAA,IAC/C;AAAA;AAAA,IAED,iBAAiB;AAAA,MAChB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,mBAAmB,CAAC;AAAA,IAClC;AAAA,EACD;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,4BAA4B;AAAA,IAC5B;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACT,oBAAoB;AACnB,aAAO,KAAK,eAAe,KAAK,oBAAoB,KAAK;AAAA,IACzD;AAAA,IACD,2BAA2B;AAC1B,aAAO,KAAK,eAAe,KAAK,+BAA+B,KAAK;AAAA,IACpE;AAAA;AAAA,IAED,YAAY;AACX,UAAI,KAAK,iBAAiB,KAAK,iBAAiB,KAAK,cAAc;AAAQ,eAAO;AAClF,UAAI,KAAK,8BAA8B;AACtC,YAAI,KAAK,eAAe,CAAC,KAAK,mBAAmB,CAAC,KAAK;AAAS,iBAAO;AAAA,MAC3E,OAAU;AACN,eAAO;AAAA,MACP;AACD,aAAO,CAAC,KAAK,6BAA6B,CAAC,KAAK;AAAA,IAChD;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAER,mBAAmB;AAClB,UAAI,aAAa;AACjB,WAAK,MAAM,mBAAmB,YAAU;AACvC,YAAI,WAAW,UAAa,WAAW,MAAM;AAC5C,eAAK,sBAAsB;AAC3B,eAAK,OAAQ,EAAC,MAAM,MAAM;AAAA,UAAE,CAAA;AAAA,QAC5B;AACD,qBAAa;AAAA,MACjB,CAAI;AAED,WAAK,UAAU,MAAM;AACpB,YAAI,CAAC,YAAY;AAChB,eAAK,sBAAsB;AAC3B,eAAK,OAAQ,EAAC,MAAM,MAAM;AAAA,UAAE,CAAA;AAAA,QAC5B;AAAA,MACL,CAAI;AAAA,IACD;AAAA;AAAA,IAED,kBAAkB;AACjB,WAAK,MAAM,gBAAgB;AAAA,IAC3B;AAAA,EACD;AACF;;"}