{"version": 3, "file": "wd-icon.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-icon/wd-icon.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1pY29uL3dkLWljb24udnVl"], "sourcesContent": ["<template>\n  <view @click=\"handleClick\" :class=\"rootClass\" :style=\"rootStyle\">\n    <image v-if=\"isImage\" class=\"wd-icon__image\" :src=\"name\"></image>\n  </view>\n</template>\n\n<script lang=\"ts\">\nexport default {\n  name: 'wd-icon',\n  options: {\n    virtualHost: true,\n    addGlobalClass: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport { computed, type CSSProperties } from 'vue'\nimport { addUnit, isDef, objToStyle } from '../common/util'\nimport { iconProps } from './types'\n\nconst props = defineProps(iconProps)\nconst emit = defineEmits(['click', 'touch'])\n\nconst isImage = computed(() => {\n  return isDef(props.name) && props.name.includes('/')\n})\n\nconst rootClass = computed(() => {\n  const prefix = props.classPrefix\n  return `${prefix} ${props.customClass} ${isImage.value ? 'wd-icon--image' : prefix + '-' + props.name}`\n})\n\nconst rootStyle = computed(() => {\n  const style: CSSProperties = {}\n  if (props.color) {\n    style['color'] = props.color\n  }\n  if (props.size) {\n    style['font-size'] = addUnit(props.size)\n  }\n  return `${objToStyle(style)} ${props.customStyle}`\n})\n\nfunction handleClick(event: any) {\n  emit('click', event)\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-icon/wd-icon.vue'\nwx.createComponent(Component)"], "names": ["computed", "isDef", "addUnit", "objToStyle"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAOA,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,EAAA;AAEpB;;;;;AAQA,UAAM,QAAQ;AACd,UAAM,OAAO;AAEP,UAAA,UAAUA,cAAAA,SAAS,MAAM;AAC7B,aAAOC,cAAAA,MAAM,MAAM,IAAI,KAAK,MAAM,KAAK,SAAS,GAAG;AAAA,IAAA,CACpD;AAEK,UAAA,YAAYD,cAAAA,SAAS,MAAM;AAC/B,YAAM,SAAS,MAAM;AACrB,aAAO,GAAG,MAAM,IAAI,MAAM,WAAW,IAAI,QAAQ,QAAQ,mBAAmB,SAAS,MAAM,MAAM,IAAI;AAAA,IAAA,CACtG;AAEK,UAAA,YAAYA,cAAAA,SAAS,MAAM;AAC/B,YAAM,QAAuB,CAAC;AAC9B,UAAI,MAAM,OAAO;AACT,cAAA,OAAO,IAAI,MAAM;AAAA,MAAA;AAEzB,UAAI,MAAM,MAAM;AACd,cAAM,WAAW,IAAIE,sBAAQ,MAAM,IAAI;AAAA,MAAA;AAEzC,aAAO,GAAGC,cAAAA,WAAW,KAAK,CAAC,IAAI,MAAM,WAAW;AAAA,IAAA,CACjD;AAED,aAAS,YAAY,OAAY;AAC/B,WAAK,SAAS,KAAK;AAAA,IAAA;;;;;;;;;;;;;;;AC7CrB,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}