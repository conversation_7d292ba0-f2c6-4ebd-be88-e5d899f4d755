"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../common/vendor.js");
const pagesSub_online_utils_index = require("../utils/index.js");
const utils_is = require("../../../utils/is.js");
const common_uitls = require("../../../common/uitls.js");
const common_areaData_Area = require("../../../common/areaData/Area.js");
if (!Array) {
  const _easycom_wd_img2 = common_vendor.resolveComponent("wd-img");
  const _easycom_ImgPreview2 = common_vendor.resolveComponent("ImgPreview");
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  (_easycom_wd_img2 + _easycom_ImgPreview2 + _easycom_wd_button2)();
}
const _easycom_wd_img = () => "../../../node-modules/wot-design-uni/components/wd-img/wd-img.js";
const _easycom_ImgPreview = () => "../../../components/ImgPreview/ImgPreview.js";
const _easycom_wd_button = () => "../../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
if (!Math) {
  (_easycom_wd_img + _easycom_ImgPreview + _easycom_wd_button)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "onlineTableCell",
  options: {
    styleIsolation: "shared"
  }
}), {
  __name: "onlineTableCell",
  props: {
    columnsInfo: {
      type: Object,
      default: () => {
      }
    },
    column: {
      type: Object,
      default: () => {
      }
    },
    record: {
      type: Object,
      default: () => {
      }
    }
  },
  setup(__props) {
    const props = __props;
    const imgPreview = common_vendor.ref({
      show: false,
      urls: []
    });
    const handleDownload = (text) => {
      common_vendor.index.downloadFile({
        url: text,
        success: (res) => {
          if (res.statusCode === 200) {
            console.log("下载成功");
            console.log(res);
          }
        }
      });
    };
    const getPcaText = (code) => {
      if (!code) {
        return "";
      }
      return common_areaData_Area.getAreaTextByCode(code);
    };
    const getFirstImg = (text) => {
      if (utils_is.isString(text)) {
        var imgs = text.split(",");
        return common_uitls.getFileAccessHttpUrl(imgs[0]);
      } else {
        return "";
      }
    };
    const handleClickImg = () => {
      imgPreview.value.show = true;
    };
    const renderVal = (record, column) => {
      const { customRender, hrefSlotName, dataIndex, fieldType } = column;
      let text = record[dataIndex];
      if (["date", "Date"].includes(column["fieldType"])) {
        if (!text) {
          return "";
        }
        if (text.length > 10) {
          return text.substring(0, 10);
        }
        return text;
      } else if (["popup_dict"].includes(column["fieldType"])) {
        const dict = record[dataIndex + "_dictText"];
        if (dict != void 0) {
          return record[dataIndex + "_dictText"];
        }
        return text;
      } else if (customRender) {
        let dictCode = customRender;
        let replaceFlag = "_replace_text_";
        let value = text;
        if (dictCode) {
          if (dictCode.startsWith(replaceFlag)) {
            let textFieldName = dictCode.replace(replaceFlag, "");
            value = record[textFieldName];
          } else {
            value = pagesSub_online_utils_index.filterMultiDictText(common_vendor.unref(props.columnsInfo.dictOptions)[dictCode], text + "");
          }
        }
        if (column.showLength) {
          if (value && value.length > column.showLength) {
            value = value.substr(0, column.showLength) + "...";
          }
        }
        return value;
      } else {
        return text;
      }
    };
    const init = () => {
      var _a, _b;
      const field = props.column.dataIndex;
      if (((_b = (_a = props.column) == null ? void 0 : _a.scopedSlots) == null ? void 0 : _b.customRender) === "imgSlot") {
        const text = props.record[field];
        if (utils_is.isString(text)) {
          imgPreview.value.urls = text.split(",").map((item) => common_uitls.getFileAccessHttpUrl(item));
        } else {
          return "";
        }
      }
    };
    init();
    return (_ctx, _cache) => {
      var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o, _p, _q, _r, _s, _t;
      return common_vendor.e$1({
        a: ((_b = (_a = __props.column) == null ? void 0 : _a.scopedSlots) == null ? void 0 : _b.customRender) === "imgSlot"
      }, ((_d = (_c = __props.column) == null ? void 0 : _c.scopedSlots) == null ? void 0 : _d.customRender) === "imgSlot" ? common_vendor.e$1({
        b: __props.record[__props.column.dataIndex]
      }, __props.record[__props.column.dataIndex] ? common_vendor.e$1({
        c: common_vendor.o(handleClickImg),
        d: common_vendor.p({
          width: "30",
          height: "30",
          src: getFirstImg(__props.record[__props.column.dataIndex])
        }),
        e: common_vendor.unref(imgPreview).show
      }, common_vendor.unref(imgPreview).show ? {
        f: common_vendor.o(() => common_vendor.unref(imgPreview).show = false),
        g: common_vendor.p({
          urls: common_vendor.unref(imgPreview).urls
        })
      } : {}) : {}) : ((_f = (_e = __props.column) == null ? void 0 : _e.scopedSlots) == null ? void 0 : _f.customRender) === "fileSlot" ? common_vendor.e$1({
        i: __props.record[__props.column.dataIndex]
      }, __props.record[__props.column.dataIndex] ? {
        j: common_vendor.o(($event) => handleDownload(__props.record[__props.column.dataIndex]))
      } : {}) : ((_h = (_g = __props.column) == null ? void 0 : _g.scopedSlots) == null ? void 0 : _h.customRender) === "htmlSlot" ? common_vendor.e$1({
        l: __props.column.fieldHref
      }, __props.column.fieldHref ? {} : {
        m: __props.record[__props.column.dataIndex]
      }) : ((_j = (_i = __props.column) == null ? void 0 : _i.scopedSlots) == null ? void 0 : _j.customRender) === "pcaSlot" ? {
        o: common_vendor.t(getPcaText(__props.record[__props.column.dataIndex]) || "                ")
      } : ((_l = (_k = __props.column) == null ? void 0 : _k.scopedSlots) == null ? void 0 : _l.customRender) === "dateSlot" ? {
        q: common_vendor.t(common_vendor.unref(pagesSub_online_utils_index.getFormatDate)(__props.record[__props.column.dataIndex], __props.column) || "                ")
      } : {
        r: common_vendor.t(renderVal(__props.record, __props.column) || "                ")
      }, {
        h: ((_n = (_m = __props.column) == null ? void 0 : _m.scopedSlots) == null ? void 0 : _n.customRender) === "fileSlot",
        k: ((_p = (_o = __props.column) == null ? void 0 : _o.scopedSlots) == null ? void 0 : _p.customRender) === "htmlSlot",
        n: ((_r = (_q = __props.column) == null ? void 0 : _q.scopedSlots) == null ? void 0 : _r.customRender) === "pcaSlot",
        p: ((_t = (_s = __props.column) == null ? void 0 : _s.scopedSlots) == null ? void 0 : _t.customRender) === "dateSlot"
      });
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-89175823"]]);
wx.createComponent(Component);
//# sourceMappingURL=onlineTableCell.js.map
