"use strict";
const common_vendor = require("../../../common/vendor.js");
const utils_is = require("../../../utils/is.js");
if (!Array) {
  const _easycom_wd_datetime_picker2 = common_vendor.resolveComponent("wd-datetime-picker");
  _easycom_wd_datetime_picker2();
}
const _easycom_wd_datetime_picker = () => "../../../node-modules/wot-design-uni/components/wd-datetime-picker/wd-datetime-picker.js";
if (!Math) {
  _easycom_wd_datetime_picker();
}
const _sfc_main = {
  __name: "online-date",
  props: {
    label: {
      type: String,
      default: "",
      required: false
    },
    labelWidth: {
      type: String,
      default: "80px",
      required: false
    },
    name: {
      type: String,
      default: "",
      required: false
    },
    type: {
      type: String,
      default: "date",
      required: false
    },
    value: {
      type: [String, Number],
      required: false
    },
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  emits: ["input", "change", "update:value"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    common_vendor.ref(false);
    const currentTime = common_vendor.ref("");
    common_vendor.watch(
      () => props.value,
      (val) => {
        if (val) {
          currentTime.value = val && utils_is.isString(val) ? new Date(val).getTime() : val;
        } else {
          currentTime.value = "";
        }
      }
    );
    const handleConfirm = (e) => {
      emit("update:value", currentTime.value);
      emit("change", currentTime.value);
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(handleConfirm),
        b: common_vendor.o(($event) => common_vendor.isRef(currentTime) ? currentTime.value = $event : null),
        c: common_vendor.p({
          disabled: __props.disabled,
          type: __props.type,
          labelWidth: __props.labelWidth,
          label: __props.label,
          modelValue: common_vendor.unref(currentTime)
        })
      };
    };
  }
};
wx.createComponent(_sfc_main);
//# sourceMappingURL=online-date.js.map
