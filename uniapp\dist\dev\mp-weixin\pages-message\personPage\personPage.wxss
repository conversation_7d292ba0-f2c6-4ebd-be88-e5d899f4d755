/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.topArea.data-v-ecc09b92 {
  background: linear-gradient(45deg, #0081ff, #1cbbb4);
  min-height: 170px;
}
.middleArea.data-v-ecc09b92 {
  position: relative;
  display: flex;
  flex-direction: column;
  padding-bottom: 30px;
}
.middleArea .avatar.data-v-ecc09b92 {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translate(-50%, -50%);
}
.middleArea .realname.data-v-ecc09b92 {
  padding-top: 50px;
  font-size: 18px;
  font-weight: 700;
  color: #333;
  margin-bottom: 20px;
}
.bottomArea .list.data-v-ecc09b92 {
  border-top: 1px solid #f1f1f1;
  display: flex;
  align-items: center;
  padding: 10px;
}
.bottomArea .list .iconBox.data-v-ecc09b92 {
  font-size: 28px;
  margin-right: 10px;
}
.bottomArea .list .label.data-v-ecc09b92 {
  font-size: 15px;
  margin-bottom: 4px;
}
.bottomArea .value.data-v-ecc09b92 {
  color: #3665cb;
}