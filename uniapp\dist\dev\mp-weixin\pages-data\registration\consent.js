"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
const store_user = require("../../store/user.js");
if (!Array) {
  const _component_NavBar = common_vendor.resolveComponent("NavBar");
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _easycom_wd_signature2 = common_vendor.resolveComponent("wd-signature");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_component_NavBar + _easycom_wd_button2 + _easycom_wd_signature2 + _easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
const _easycom_wd_signature = () => "../../node-modules/wot-design-uni/components/wd-signature/wd-signature.js";
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  (_easycom_wd_button + _easycom_wd_signature + _easycom_PageLayout)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "vitalSignsForm"
}), {
  __name: "consent",
  setup(__props) {
    const height = common_vendor.ref(0);
    const width = common_vendor.ref(0);
    const inited = common_vendor.ref(false);
    const showSignature = common_vendor.ref(false);
    const showLandscapeGuide = common_vendor.ref(true);
    const signatureData = common_vendor.ref("");
    const showError = common_vendor.ref(false);
    const canUndo = common_vendor.ref(false);
    const canRedo = common_vendor.ref(false);
    const signatureRef = common_vendor.ref(null);
    const userStore = store_user.useUserStore();
    const query = common_vendor.ref({});
    common_vendor.onMounted(() => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      query.value = currentPage.options || {};
      if (query.value.mode === "view" && query.value.id) {
        loadFormData(query.value.id);
      }
      const { windowWidth } = common_vendor.index.getSystemInfoSync();
      width.value = Math.min(windowWidth * 0.85, 600);
      height.value = width.value * 0.6;
    });
    const mode = common_vendor.computed(() => query.value.mode || "add");
    const isViewMode = common_vendor.computed(() => mode.value === "view");
    const formData = common_vendor.ref({
      date: "",
      signature: ""
    });
    const loadFormData = (id) => {
    };
    const openSignature = () => {
      showSignature.value = true;
      showLandscapeGuide.value = true;
      const { windowWidth, windowHeight } = common_vendor.index.getSystemInfoSync();
      const isLandscape = windowWidth > windowHeight;
      if (isLandscape) {
        width.value = windowWidth * 0.85;
        height.value = windowHeight * 0.7;
        showLandscapeGuide.value = false;
      } else {
        width.value = windowHeight * 0.85;
        height.value = windowWidth * 0.7;
      }
      try {
        common_vendor.wx$1.setPageOrientation({
          orientation: "landscape",
          success: () => {
            console.log("已切换到横屏模式");
            showLandscapeGuide.value = false;
            setTimeout(() => {
              const { windowWidth: windowWidth2, windowHeight: windowHeight2 } = common_vendor.index.getSystemInfoSync();
              width.value = windowWidth2 * 0.85;
              height.value = windowHeight2 * 0.7;
            }, 300);
          },
          fail: (err) => {
            console.error("切换横屏模式失败:", err);
          }
        });
      } catch (error) {
        console.error("设置横屏失败:", error);
      }
      common_vendor.index.onWindowResize(handleSizeChange);
      common_vendor.pause(300).then(() => {
        inited.value = true;
      });
    };
    const handleSizeChange = () => {
      const { windowWidth, windowHeight } = common_vendor.index.getSystemInfoSync();
      const isLandscape = windowWidth > windowHeight;
      if (isLandscape && showSignature.value) {
        showLandscapeGuide.value = false;
        width.value = windowWidth * 0.85;
        height.value = windowHeight * 0.7;
      } else if (showSignature.value) {
        showLandscapeGuide.value = true;
      }
    };
    const cancelSignature = () => {
      showSignature.value = false;
      common_vendor.index.offWindowResize(handleSizeChange);
      try {
        common_vendor.wx$1.setPageOrientation({
          orientation: "portrait",
          success: () => {
            console.log("已恢复竖屏模式");
          }
        });
      } catch (error) {
        console.error("设置竖屏失败:", error);
      }
    };
    const handleConfirm = (result) => {
      if (result && result.success && result.tempFilePath) {
        signatureData.value = result.tempFilePath;
        formData.value.signature = result.tempFilePath;
        showError.value = false;
        common_vendor.index.showToast({
          title: "签名已保存",
          icon: "success",
          duration: 2e3
        });
      }
      showSignature.value = false;
      common_vendor.index.offWindowResize(handleSizeChange);
      try {
        common_vendor.wx$1.setPageOrientation({
          orientation: "portrait",
          success: () => {
            console.log("已恢复竖屏模式");
          }
        });
      } catch (error) {
        console.error("设置竖屏失败:", error);
      }
    };
    const clear = () => {
      if (signatureRef.value) {
        signatureRef.value.clear && signatureRef.value.clear();
        canUndo.value = false;
        canRedo.value = false;
      }
    };
    const confirm = () => {
      if (signatureRef.value) {
        signatureRef.value.confirm && signatureRef.value.confirm();
      }
    };
    const updateCanUndoRedo = () => {
      if (signatureRef.value) {
        common_vendor.nextTick$1(() => {
          if (typeof signatureRef.value.canUndo === "function") {
            canUndo.value = signatureRef.value.canUndo();
          }
          if (typeof signatureRef.value.canRedo === "function") {
            canRedo.value = signatureRef.value.canRedo();
          }
        });
      }
    };
    const submitForm = () => {
      if (!signatureData.value) {
        showError.value = true;
        common_vendor.index.showToast({
          title: "请先完成签名",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "提交中..."
      });
      const submitData = {
        userId: userStore.userInfo.userid
      };
      console.log("提交到/patient/savesignature的数据:", {
        signature: signatureData.value,
        // 签名图片临时文件路径
        formData: submitData
        // 表单数据，包含userId
      });
      common_vendor.index.uploadFile({
        url: `${"https://www.mograine.cn/api"}/patient/savesignature`,
        // 后端签名接口
        filePath: signatureData.value,
        // 签名图片路径
        name: "file",
        formData: submitData,
        success: (res) => {
          let response;
          try {
            response = JSON.parse(res.data);
          } catch (e) {
            response = res.data;
          }
          if (res.statusCode === 200 && response.success) {
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: "提交成功",
              icon: "success",
              duration: 2e3
            });
            setTimeout(() => {
              common_vendor.index.navigateTo({
                url: "/pages-data/registration/form"
              });
            }, 1e3);
          } else {
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: response.message || "提交失败，请重试",
              icon: "none",
              duration: 2e3
            });
          }
        },
        fail: (err) => {
          console.error("提交失败:", err);
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "网络错误，请稍后重试",
            icon: "none",
            duration: 2e3
          });
        }
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.p({
          title: mode.value === "add" ? "新增" : "查看",
          showBack: true
        }),
        b: common_vendor.o(openSignature),
        c: signatureData.value
      }, signatureData.value ? {
        d: signatureData.value
      } : {}, {
        e: showError.value && !signatureData.value
      }, showError.value && !signatureData.value ? {} : {}, {
        f: !isViewMode.value
      }, !isViewMode.value ? {
        g: common_vendor.o(submitForm)
      } : {}, {
        h: showSignature.value
      }, showSignature.value ? common_vendor.e$1({
        i: inited.value
      }, inited.value ? {
        j: common_vendor.o(clear),
        k: common_vendor.p({
          size: "small",
          plain: true,
          ["custom-style"]: "min-width: 30rpx; height: 25rpx; line-height: 68rpx; font-size: 16rpx;"
        }),
        l: common_vendor.o(confirm),
        m: common_vendor.p({
          size: "small",
          type: "primary",
          ["custom-style"]: "min-width: 30rpx; height: 25rpx; line-height: 68rpx; font-size: 16rpx;"
        }),
        n: common_vendor.o(cancelSignature),
        o: common_vendor.p({
          size: "small",
          plain: true,
          ["custom-style"]: "min-width: 30rpx; height: 25rpx; line-height: 68rpx; font-size: 16rpx;"
        }),
        p: common_vendor.sr(signatureRef, "8e7e0087-3,8e7e0087-1", {
          "k": "signatureRef"
        }),
        q: common_vendor.o(handleConfirm),
        r: common_vendor.o(updateCanUndoRedo),
        s: common_vendor.o(updateCanUndoRedo),
        t: common_vendor.p({
          height: height.value,
          width: width.value,
          ["enable-history"]: true,
          pressure: true,
          ["min-width"]: 1,
          ["max-width"]: 5,
          ["min-speed"]: 1.5,
          ["export-scale"]: 3,
          ["file-type"]: "png",
          ["background-color"]: "#f5f5f5"
        })
      } : {}) : {});
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-8e7e0087"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=consent.js.map
