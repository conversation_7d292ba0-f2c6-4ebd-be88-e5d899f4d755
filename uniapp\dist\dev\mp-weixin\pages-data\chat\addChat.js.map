{"version": 3, "file": "addChat.js", "sources": ["../../../../../src/pages-data/chat/addChat.vue", "../../../../../uniPage:/cGFnZXMtZGF0YVxjaGF0XGFkZENoYXQudnVl"], "sourcesContent": ["\r\n<template>\n<layout-default-uni >\r\n  <PageLayout :navLeftArrow=\"true\" navLeftText=\"返回\">\r\n    <scroll-view class=\"chat-list\" scroll-y=\"true\">\r\n      <!-- 优化的头像和姓名区域 -->\r\n      <view class=\"user-header-section\">\r\n        <view class=\"user-info-card\">\r\n          <image\r\n            class=\"user-avatar\"\r\n            :src=\"getAvatarUrl(userStore.userInfo.avatar)\"\r\n            mode=\"aspectFill\"\r\n            @error=\"handleAvatarError\"\r\n          />\r\n          <view class=\"user-details\">\r\n            <text class=\"user-name\">{{ userStore.userInfo.realname || '未知用户' }}</text>\r\n            <text class=\"user-role\">{{ getUserRoleText() }}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n\r\n\r\n      <!-- 表单内容区域 -->\r\n      <view class=\"form-content\">\r\n        <!-- 医生选择下拉菜单 -->\r\n        <view class=\"form-item\">\r\n          <view class=\"form-label\">选择医生 <text class=\"optional\">（可选择多个）</text></view>\r\n          <view class=\"doctor-select-container\">\r\n            <!-- 下拉菜单 -->\r\n            <picker\r\n              mode=\"selector\"\r\n              :range=\"doctorList\"\r\n              range-key=\"realName\"\r\n              @change=\"onDoctorSelect\"\r\n              class=\"doctor-picker\"\r\n            >\r\n              <view class=\"picker-display\">\r\n                <text class=\"picker-text\">{{ selectedDoctors.length > 0 ? '点击选择更多医生' : '请选择医生' }}</text>\r\n                <text class=\"picker-arrow\">▼</text>\r\n              </view>\r\n            </picker>\r\n\r\n            <!-- 已选择的医生显示 -->\r\n            <view v-if=\"selectedDoctors.length > 0\" class=\"selected-doctors-display\">\r\n              <view\r\n                v-for=\"(doctor, index) in selectedDoctors\"\r\n                :key=\"doctor.userId || index\"\r\n                class=\"doctor-tag\"\r\n                @click=\"removeDoctorSelection(index)\"\r\n              >\r\n                <text class=\"doctor-name\">@{{ doctor.realName || '未知医生' }}</text>\r\n                <text class=\"remove-doctor\">×</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 标题输入框 -->\r\n        <view class=\"form-item\">\r\n          <view class=\"form-label\">问题标题 <text class=\"optional\">（可选）</text></view>\r\n          <input\r\n            class=\"form-input title-input\"\r\n            v-model=\"formData.title\"\r\n            placeholder=\"请输入问题标题（可选）\"\r\n            maxlength=\"50\"\r\n          />\r\n          <view class=\"char-count\">{{ formData.title.length }}/50</view>\r\n        </view>\r\n\r\n        <!-- 问题描述输入框 -->\r\n        <view class=\"form-item\">\r\n          <view class=\"form-label\">问题描述 <text class=\"required\">*</text></view>\r\n          <textarea\r\n            class=\"form-textarea question-input\"\r\n            v-model=\"formData.question\"\r\n            placeholder=\"请详细描述您的问题...\"\r\n            maxlength=\"1000\"\r\n            :auto-height=\"true\"\r\n            :show-confirm-bar=\"false\"\r\n          />\r\n          <view class=\"char-count\">{{ formData.question.length }}/1000</view>\r\n        </view>\r\n\r\n        <!-- 图片上传区域 -->\r\n        <view class=\"form-item\">\r\n          <view class=\"form-label\">上传图片 <text class=\"optional\">（最多6张）</text></view>\r\n          <view class=\"image-upload-area\">\r\n            <!-- 已选择的图片 -->\r\n            <view class=\"uploaded-images\" v-if=\"selectedImages.length > 0\">\r\n              <view\r\n                class=\"image-item\"\r\n                v-for=\"(img, index) in selectedImages\"\r\n                :key=\"index\"\r\n                @click=\"previewImage(img)\"\r\n              >\r\n                <image class=\"uploaded-image\" :src=\"img\" mode=\"aspectFill\" />\r\n                <view class=\"remove-image\" @click.stop=\"removeImage(index)\">×</view>\r\n              </view>\r\n            </view>\r\n            <!-- 上传按钮 -->\r\n            <view class=\"upload-buttons\">\r\n              <button class=\"upload-btn\" @click=\"chooseFromGallery\" :disabled=\"selectedImages.length >= 6\">\r\n                📷 从相册选择\r\n              </button>\r\n              <button class=\"upload-btn\" @click=\"takePhoto\" :disabled=\"selectedImages.length >= 6\">\r\n                📸 拍照\r\n              </button>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 提交按钮 -->\r\n        <view class=\"submit-section\">\r\n          <button\r\n            class=\"submit-btn\"\r\n            @click=\"submitQuestion\"\r\n            :disabled=\"isSubmitting || !formData.question.trim()\"\r\n            :class=\"{ 'disabled': isSubmitting || !formData.question.trim() }\"\r\n          >\r\n            {{ isSubmitting ? '提交中...' : '提交问题' }}\r\n          </button>\r\n        </view>\r\n      </view>\r\n    </scroll-view>\r\n  </PageLayout>\r\n</layout-default-uni>\n</template>\r\n<script lang=\"ts\" setup>\r\nimport { ref } from 'vue';\r\nimport { onLoad } from '@dcloudio/uni-app';\r\nimport { useUserStore } from '@/store/user';\r\nimport { http } from '@/utils/http';\r\n\r\nconst userStore = useUserStore();\r\n\r\n// 表单数据\r\nconst formData = ref({\r\n  title: '',\r\n  question: '',\r\n  patientId: ''\r\n});\r\n\r\n// 图片相关\r\nconst selectedImages = ref<string[]>([]);\r\nconst isSubmitting = ref(false);\r\n\r\n// 医生选择相关\r\nconst doctorList = ref<any[]>([]);\r\nconst selectedDoctors = ref<any[]>([]);\r\nconst loadingDoctors = ref(false);\r\n\r\n// 选中的医生信息（保持兼容性）\r\nconst selectedDoctorIds = ref('');\r\nconst selectedDoctorNames = ref('');\r\n\r\n// 默认头像\r\nconst defAvatar = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNGNUY1RjUiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iIzk5OTk5OSIvPgo8cGF0aCBkPSJNMzAgMzJDMzAgMjYuNDc3MSAyNS41MjI5IDIyIDIwIDIyQzE0LjQ3NzEgMjIgMTAgMjYuNDc3MSAxMCAzMkgzMFoiIGZpbGw9IiM5OTk5OTkiLz4KPC9zdmc+'\r\n\r\n\r\n// 通用头像处理函数\r\nconst getAvatarUrl = (avatar: string | null | undefined) => {\r\n\r\n  // 如果头像为空、null、undefined或空字符串，返回默认头像\r\n  if (!avatar || avatar.trim() === '') {\r\n    return defAvatar;\r\n  }\r\n\r\n  // 检查是否已经是完整URL (http或https开头)\r\n  if (avatar.startsWith('http://') || avatar.startsWith('https://')) {\r\n    return avatar;\r\n  }\r\n\r\n  // 检查是否是base64格式\r\n  if (avatar.startsWith('data:image/')) {\r\n    return avatar;\r\n  }\r\n\r\n  // 如果是相对路径，拼接基础URL\r\n  if (avatar.startsWith('/')) {\r\n    const fullUrl = 'https://www.mograine.cn' + avatar;\r\n    return fullUrl;\r\n  }\r\n\r\n  // 如果是文件名，拼接完整路径\r\n  const fullUrl = 'https://www.mograine.cn/images/' + avatar;\r\n  return fullUrl;\r\n}\r\n\r\n// 头像加载错误处理\r\nconst handleAvatarError = (event: any) => {\r\n  event.target.src = defAvatar;\r\n}\r\n\r\n// 获取用户角色文本\r\nconst getUserRoleText = () => {\r\n  const userCategory = Number(userStore.userInfo.userCategory);\r\n  switch (userCategory) {\r\n    case 0:\r\n      return '医生';\r\n    case 1:\r\n      return '患者';\r\n    case 2:\r\n      return '社工/社区医生';\r\n    default:\r\n      return '用户';\r\n  }\r\n}\r\n\r\n// 获取医生列表\r\nconst fetchDoctorList = async () => {\r\n  try {\r\n    loadingDoctors.value = true;\r\n    const response = await http.get('/sys/user/doctorList');\r\n\r\n    if (response.success && response.result) {\r\n      // 如果result是对象且包含records数组，使用records\r\n      let doctorData = response.result;\r\n\r\n      console.log('doctorData',doctorData)\r\n            // 处理医生数据，直接使用API返回的realName字段\r\n      const doctors = (doctorData || []).map((doctor: any, index: number) => {\r\n\r\n        // 确保使用API返回的原始realName字段\r\n        if (!doctor.realName) {\r\n          console.warn(`⚠️ 医生${index}缺少realName字段:`, doctor);\r\n        }\r\n\r\n        return {\r\n          ...doctor,\r\n          // 确保有唯一标识符\r\n          userId: doctor.id || doctor.userId || doctor.username || `doctor_${index}`,\r\n          // 保持API返回的原始realName，不做任何修改\r\n          realName: doctor.realname || '未知医生',\r\n          displayName: doctor.realname || '未知医生' // 为了兼容性\r\n        };\r\n      });\r\n\r\n      doctorList.value = doctors;\r\n      console.log('获取医生列表成功，共', doctors.length, '位医生');\r\n      console.log('医生列表详情:', doctors.map(d => ({ userId: d.userId, realName: d.realName, id: d.id })));\r\n    } else {\r\n      console.error('获取医生列表失败:', response.message);\r\n      uni.showToast({\r\n        title: '获取医生列表失败',\r\n        icon: 'none'\r\n      });\r\n    }\r\n  } catch (error: any) {\r\n    console.error('获取医生列表异常:', error);\r\n    uni.showToast({\r\n      title: '获取医生列表失败',\r\n      icon: 'none'\r\n    });\r\n  } finally {\r\n    loadingDoctors.value = false;\r\n  }\r\n}\r\n\r\n// 处理医生选择\r\nconst onDoctorSelect = (event: any) => {\r\n  const selectedIndex = event.detail.value;\r\n  const selectedDoctor = doctorList.value[selectedIndex];\r\n\r\n  if (!selectedDoctor) return;\r\n\r\n  // 检查是否已经选择过该医生\r\n  console.log('🔍 检查医生是否已选择:', {\r\n    selectedDoctor: selectedDoctor,\r\n    selectedDoctorUserId: selectedDoctor.userId,\r\n    currentSelectedDoctors: selectedDoctors.value.map(d => ({ userId: d.userId, realName: d.realName }))\r\n  });\r\n\r\n  const isAlreadySelected = selectedDoctors.value.some(\r\n    doctor => {\r\n      // 确保比较的是字符串类型\r\n      const doctorId = String(doctor.userId);\r\n      const selectedId = String(selectedDoctor.userId);\r\n      const isSame = doctorId === selectedId;\r\n      console.log(`比较医生: ${doctor.realName}(${doctorId}) vs ${selectedDoctor.realName}(${selectedId}) = ${isSame}`);\r\n      return isSame;\r\n    }\r\n  );\r\n\r\n  if (isAlreadySelected) {\r\n    uni.showToast({\r\n      title: '该医生已选择',\r\n      icon: 'none'\r\n    });\r\n    return;\r\n  }\r\n\r\n  // 添加到已选择列表\r\n  selectedDoctors.value.push(selectedDoctor);\r\n\r\n  // 更新兼容性字段\r\n  updateSelectedDoctorInfo();\r\n\r\n  console.log('选择医生:', selectedDoctor);\r\n  console.log('当前已选择医生:', selectedDoctors.value);\r\n}\r\n\r\n// 移除医生选择\r\nconst removeDoctorSelection = (index: number) => {\r\n  selectedDoctors.value.splice(index, 1);\r\n  updateSelectedDoctorInfo();\r\n  console.log('移除医生后的列表:', selectedDoctors.value);\r\n}\r\n\r\n// 更新选中医生的ID和姓名字符串（使用API返回的realName）\r\nconst updateSelectedDoctorInfo = () => {\r\n  const doctorIds = selectedDoctors.value.map(doctor => doctor.userId).join(',');\r\n  const doctorNames = selectedDoctors.value.map(doctor => `@${doctor.realName || '未知医生'}`).join(' ');\r\n\r\n  selectedDoctorIds.value = doctorIds;\r\n  selectedDoctorNames.value = doctorNames;\r\n\r\n  console.log('📝 更新医生信息:', {\r\n    doctorIds: selectedDoctorIds.value,\r\n    doctorNames: selectedDoctorNames.value\r\n  });\r\n}\r\n\r\n// 从相册选择图片\r\nfunction chooseFromGallery() {\r\n  if (selectedImages.value.length >= 6) {\r\n    uni.showToast({ title: '最多只能上传6张图片', icon: 'none' });\r\n    return;\r\n  }\r\n\r\n  const remainingCount = 6 - selectedImages.value.length;\r\n  uni.chooseImage({\r\n    count: remainingCount,\r\n    sourceType: ['album'],\r\n    success: (res: any) => {\r\n      if (res.tempFilePaths && res.tempFilePaths.length > 0) {\r\n        selectedImages.value.push(...res.tempFilePaths);\r\n      }\r\n    },\r\n    fail: (err: any) => {\r\n      console.error('选择图片失败:', err);\r\n      uni.showToast({ title: '选择图片失败', icon: 'none' });\r\n    }\r\n  });\r\n}\r\n\r\n// 拍照\r\nfunction takePhoto() {\r\n  if (selectedImages.value.length >= 6) {\r\n    uni.showToast({ title: '最多只能上传6张图片', icon: 'none' });\r\n    return;\r\n  }\r\n\r\n  uni.chooseImage({\r\n    count: 1,\r\n    sourceType: ['camera'],\r\n    success: (res: any) => {\r\n      if (res.tempFilePaths && res.tempFilePaths.length > 0) {\r\n        selectedImages.value.push(...res.tempFilePaths);\r\n      }\r\n    },\r\n    fail: (err: any) => {\r\n      console.error('拍照失败:', err);\r\n      uni.showToast({ title: '拍照失败', icon: 'none' });\r\n    }\r\n  });\r\n}\r\n\r\n// 移除图片\r\nfunction removeImage(index: number) {\r\n  selectedImages.value.splice(index, 1);\r\n}\r\n\r\n// 预览图片\r\nfunction previewImage(url: string) {\r\n  uni.previewImage({\r\n    current: url,\r\n    urls: selectedImages.value\r\n  });\r\n}\r\n\r\n// 验证表单\r\nfunction validateForm() {\r\n  if (!formData.value.question.trim()) {\r\n    uni.showToast({ title: '请输入问题描述', icon: 'none' });\r\n    return false;\r\n  }\r\n\r\n  // 如果填写了标题，验证长度\r\n  if (formData.value.title.trim() && formData.value.title.length > 50) {\r\n    uni.showToast({ title: '标题不能超过50个字符', icon: 'none' });\r\n    return false;\r\n  }\r\n\r\n  if (formData.value.question.length > 1000) {\r\n    uni.showToast({ title: '问题描述不能超过1000个字符', icon: 'none' });\r\n    return false;\r\n  }\r\n\r\n  return true;\r\n}\r\n\r\n// 提交问题\r\nasync function submitQuestion() {\r\n  if (!validateForm()) return;\r\n\r\n  if (isSubmitting.value) {\r\n    uni.showToast({ title: '正在提交中，请勿重复操作', icon: 'none' });\r\n    return;\r\n  }\r\n\r\n  try {\r\n    isSubmitting.value = true;\r\n\r\n    // 构建提交数据\r\n    const submitData = {\r\n      title: formData.value.title.trim() || '',\r\n      patientId: userStore.userInfo.userid || '',\r\n      question: formData.value.question.trim(),\r\n      images: selectedImages.value,\r\n      doctorIds: selectedDoctors.value.map(doctor => doctor.userId) // 传递选中的医生ID数组\r\n    };\r\n\r\n    console.log('提交数据:', submitData);\r\n\r\n    // 调用API\r\n    const response = await http.post('/communication/saveCommunication', submitData);\r\n\r\n    if (response.success) {\r\n      uni.showToast({\r\n        title: '提交成功',\r\n        icon: 'success',\r\n        duration: 2000\r\n      });\r\n\r\n      // 清空当前页面的医生选择状态\r\n      selectedDoctors.value = [];\r\n      selectedDoctorIds.value = '';\r\n      selectedDoctorNames.value = '';\r\n\r\n      // 延迟返回上一页，并传递清空标识\r\n      setTimeout(() => {\r\n        uni.navigateBack({\r\n          delta: 1,\r\n          success: () => {\r\n            // 通过全局事件总线通知清空医生选择\r\n            uni.$emit('clearDoctorSelection');\r\n          }\r\n        });\r\n      }, 2000);\r\n    } else {\r\n      throw new Error(response.message || '提交失败');\r\n    }\r\n\r\n  } catch (error: any) {\r\n    console.error('提交失败:', error);\r\n    uni.showToast({\r\n      title: error.message || '提交失败，请重试',\r\n      icon: 'none',\r\n      duration: 3000\r\n    });\r\n  } finally {\r\n    isSubmitting.value = false;\r\n  }\r\n}\r\n\r\nonLoad((options: any) => {\r\n  // 初始化页面数据\r\n  formData.value.patientId = userStore.userInfo.userid || '';\r\n  // 获取医生列表\r\n  fetchDoctorList();\r\n\r\n  // 接收传递的医生信息\r\n  if (options?.selectedDoctorIds) {\r\n    selectedDoctorIds.value = decodeURIComponent(options.selectedDoctorIds);\r\n  }\r\n\r\n  if (options?.selectedDoctorNames) {\r\n    selectedDoctorNames.value = decodeURIComponent(options.selectedDoctorNames);\r\n  }\r\n\r\n  // 强制触发头像处理\r\n  const avatarUrl = getAvatarUrl(userStore.userInfo.avatar);\r\n});\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\r\n\r\n// 用户头像和姓名区域样式\r\n.user-header-section {\r\n  padding: 24rpx;\r\n  background: #fff;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.user-info-card {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 24rpx;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\r\n  border-radius: 16rpx;\r\n  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);\r\n  border: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.user-avatar {\r\n  width: 96rpx;\r\n  height: 96rpx;\r\n  border-radius: 50%;\r\n  margin-right: 24rpx;\r\n  border: 3rpx solid #fff;\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.user-details {\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  flex: 1;\r\n}\r\n\r\n.user-name {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #1a1a1a;\r\n  margin-bottom: 8rpx;\r\n  line-height: 1.2;\r\n}\r\n\r\n.user-role {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n  background: #f0f7ff;\r\n  color: #1890ff;\r\n  padding: 4rpx 12rpx;\r\n  border-radius: 12rpx;\r\n  align-self: flex-start;\r\n  font-weight: 500;\r\n}\r\n\r\n.chat-list {\r\n  flex: 1;\r\n  overflow: auto;\r\n  padding: 0 0 40rpx 0;\r\n}\r\n\r\n// 表单内容样式\r\n.form-content {\r\n  padding: 24rpx;\r\n}\r\n\r\n// 医生选择样式\r\n.doctor-select-container {\r\n  border: 1rpx solid #d9d9d9;\r\n  border-radius: 8rpx;\r\n  background: #fff;\r\n  overflow: hidden;\r\n}\r\n\r\n.doctor-picker {\r\n  width: 100%;\r\n}\r\n\r\n.picker-display {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 24rpx 16rpx;\r\n  min-height: 80rpx;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.picker-text {\r\n  font-size: 28rpx;\r\n  color: #666;\r\n  flex: 1;\r\n}\r\n\r\n.picker-arrow {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  margin-left: 16rpx;\r\n}\r\n\r\n.selected-doctors-display {\r\n  border-top: 1rpx solid #f0f0f0;\r\n  padding: 16rpx;\r\n  background: #f8f9fa;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 12rpx;\r\n}\r\n\r\n.doctor-tag {\r\n  display: flex;\r\n  align-items: center;\r\n  background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);\r\n  border: 1rpx solid #d4edda;\r\n  border-radius: 20rpx;\r\n  padding: 8rpx 16rpx;\r\n  font-size: 24rpx;\r\n}\r\n\r\n.doctor-name {\r\n  color: #07C160;\r\n  font-weight: 500;\r\n  margin-right: 8rpx;\r\n}\r\n\r\n.remove-doctor {\r\n  color: #999;\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  cursor: pointer;\r\n  width: 24rpx;\r\n  height: 24rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: 50%;\r\n  background: rgba(0, 0, 0, 0.1);\r\n\r\n  &:hover {\r\n    background: rgba(0, 0, 0, 0.2);\r\n    color: #666;\r\n  }\r\n}\r\n\r\n.form-item {\r\n  margin-bottom: 32rpx;\r\n}\r\n\r\n.form-label {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  margin-bottom: 16rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.required {\r\n  color: #ff4d4f;\r\n  margin-left: 4rpx;\r\n}\r\n\r\n.optional {\r\n  color: #999;\r\n  font-weight: normal;\r\n  font-size: 24rpx;\r\n}\r\n\r\n.form-input {\r\n  width: 100%;\r\n  height: 80rpx;\r\n  border: 1rpx solid #d9d9d9;\r\n  border-radius: 8rpx;\r\n  padding: 0 16rpx;\r\n  font-size: 28rpx;\r\n  background: #fff;\r\n  box-sizing: border-box;\r\n\r\n  &:focus {\r\n    border-color: #1890ff;\r\n    box-shadow: 0 0 0 2rpx rgba(24, 144, 255, 0.2);\r\n  }\r\n}\r\n\r\n.form-textarea {\r\n  width: 100%;\r\n  min-height: 160rpx;\r\n  border: 1rpx solid #d9d9d9;\r\n  border-radius: 8rpx;\r\n  padding: 16rpx;\r\n  font-size: 28rpx;\r\n  background: #fff;\r\n  box-sizing: border-box;\r\n  line-height: 1.5;\r\n\r\n  &:focus {\r\n    border-color: #1890ff;\r\n    box-shadow: 0 0 0 2rpx rgba(24, 144, 255, 0.2);\r\n  }\r\n}\r\n\r\n.char-count {\r\n  text-align: right;\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  margin-top: 8rpx;\r\n}\r\n\r\n// 图片上传样式\r\n.image-upload-area {\r\n  border: 2rpx dashed #d9d9d9;\r\n  border-radius: 8rpx;\r\n  padding: 24rpx;\r\n  background: #fafafa;\r\n}\r\n\r\n.uploaded-images {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 16rpx;\r\n  margin-bottom: 24rpx;\r\n}\r\n\r\n.image-item {\r\n  position: relative;\r\n  width: 160rpx;\r\n  height: 160rpx;\r\n}\r\n\r\n.uploaded-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 8rpx;\r\n  object-fit: cover;\r\n}\r\n\r\n.remove-image {\r\n  position: absolute;\r\n  top: -8rpx;\r\n  right: -8rpx;\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n  background: #ff4d4f;\r\n  color: #fff;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 20rpx;\r\n  font-weight: bold;\r\n  cursor: pointer;\r\n}\r\n\r\n.upload-buttons {\r\n  display: flex;\r\n  gap: 16rpx;\r\n}\r\n\r\n.upload-btn {\r\n  flex: 1;\r\n  height: 80rpx;\r\n  background: #fff;\r\n  border: 1rpx solid #d9d9d9;\r\n  border-radius: 8rpx;\r\n  font-size: 28rpx;\r\n  color: #666;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n\r\n  &:disabled {\r\n    background: #f5f5f5;\r\n    color: #ccc;\r\n    border-color: #f0f0f0;\r\n  }\r\n\r\n  &:not(:disabled):active {\r\n    background: #f0f0f0;\r\n  }\r\n}\r\n\r\n// 提交按钮样式\r\n.submit-section {\r\n  padding: 40rpx 24rpx;\r\n  background: #fff;\r\n  border-top: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.submit-btn {\r\n  width: 100%;\r\n  height: 88rpx;\r\n  background: #07C160;\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 8rpx;\r\n  font-size: 32rpx;\r\n  font-weight: 500;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n\r\n  &.disabled {\r\n    background: #d9d9d9;\r\n    color: #fff;\r\n  }\r\n\r\n  &:not(.disabled):active {\r\n    background: #06A050;\r\n  }\r\n}\r\n</style>", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-data/chat/addChat.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "ref", "fullUrl", "http", "uni", "onLoad"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6JA,MAAM,YAAY;;;;AAvBlB,UAAM,YAAYA,WAAAA,aAAa;AAG/B,UAAM,WAAWC,cAAAA,IAAI;AAAA,MACnB,OAAO;AAAA,MACP,UAAU;AAAA,MACV,WAAW;AAAA,IAAA,CACZ;AAGK,UAAA,iBAAiBA,cAAc,IAAA,EAAE;AACjC,UAAA,eAAeA,kBAAI,KAAK;AAGxB,UAAA,aAAaA,cAAW,IAAA,EAAE;AAC1B,UAAA,kBAAkBA,cAAW,IAAA,EAAE;AAC/B,UAAA,iBAAiBA,kBAAI,KAAK;AAG1B,UAAA,oBAAoBA,kBAAI,EAAE;AAC1B,UAAA,sBAAsBA,kBAAI,EAAE;AAO5B,UAAA,eAAe,CAAC,WAAsC;AAG1D,UAAI,CAAC,UAAU,OAAO,KAAA,MAAW,IAAI;AAC5B,eAAA;AAAA,MAAA;AAIT,UAAI,OAAO,WAAW,SAAS,KAAK,OAAO,WAAW,UAAU,GAAG;AAC1D,eAAA;AAAA,MAAA;AAIL,UAAA,OAAO,WAAW,aAAa,GAAG;AAC7B,eAAA;AAAA,MAAA;AAIL,UAAA,OAAO,WAAW,GAAG,GAAG;AAC1B,cAAMC,WAAU,4BAA4B;AACrCA,eAAAA;AAAAA,MAAA;AAIT,YAAM,UAAU,oCAAoC;AAC7C,aAAA;AAAA,IACT;AAGM,UAAA,oBAAoB,CAAC,UAAe;AACxC,YAAM,OAAO,MAAM;AAAA,IACrB;AAGA,UAAM,kBAAkB,MAAM;AAC5B,YAAM,eAAe,OAAO,UAAU,SAAS,YAAY;AAC3D,cAAQ,cAAc;AAAA,QACpB,KAAK;AACI,iBAAA;AAAA,QACT,KAAK;AACI,iBAAA;AAAA,QACT,KAAK;AACI,iBAAA;AAAA,QACT;AACS,iBAAA;AAAA,MAAA;AAAA,IAEb;AAGA,UAAM,kBAAkB,MAAY;AAC9B,UAAA;AACF,uBAAe,QAAQ;AACvB,cAAM,WAAW,MAAMC,gBAAK,IAAI,sBAAsB;AAElD,YAAA,SAAS,WAAW,SAAS,QAAQ;AAEvC,cAAI,aAAa,SAAS;AAElB,kBAAA,IAAI,cAAa,UAAU;AAEnC,gBAAM,WAAW,cAAc,CAAA,GAAI,IAAI,CAAC,QAAa,UAAkB;AAGjE,gBAAA,CAAC,OAAO,UAAU;AACpB,sBAAQ,KAAK,QAAQ,KAAK,iBAAiB,MAAM;AAAA,YAAA;AAG5C,mBAAA,iCACF,SADE;AAAA;AAAA,cAGL,QAAQ,OAAO,MAAM,OAAO,UAAU,OAAO,YAAY,UAAU,KAAK;AAAA;AAAA,cAExE,UAAU,OAAO,YAAY;AAAA,cAC7B,aAAa,OAAO,YAAY;AAAA;AAAA,YAClC;AAAA,UAAA,CACD;AAED,qBAAW,QAAQ;AACnB,kBAAQ,IAAI,cAAc,QAAQ,QAAQ,KAAK;AAC/C,kBAAQ,IAAI,WAAW,QAAQ,IAAI,CAAA,OAAM,EAAE,QAAQ,EAAE,QAAQ,UAAU,EAAE,UAAU,IAAI,EAAE,KAAK,CAAC;AAAA,QAAA,OAC1F;AACG,kBAAA,MAAM,aAAa,SAAS,OAAO;AAC3CC,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACP;AAAA,QAAA;AAAA,eAEI,OAAY;AACX,gBAAA,MAAM,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,MAAA,UACD;AACA,uBAAe,QAAQ;AAAA,MAAA;AAAA,IAE3B;AAGM,UAAA,iBAAiB,CAAC,UAAe;AAC/B,YAAA,gBAAgB,MAAM,OAAO;AAC7B,YAAA,iBAAiB,WAAW,MAAM,aAAa;AAErD,UAAI,CAAC;AAAgB;AAGrB,cAAQ,IAAI,iBAAiB;AAAA,QAC3B;AAAA,QACA,sBAAsB,eAAe;AAAA,QACrC,wBAAwB,gBAAgB,MAAM,IAAI,CAAM,OAAA,EAAE,QAAQ,EAAE,QAAQ,UAAU,EAAE,SAAA,EAAW;AAAA,MAAA,CACpG;AAEK,YAAA,oBAAoB,gBAAgB,MAAM;AAAA,QAC9C,CAAU,WAAA;AAEF,gBAAA,WAAW,OAAO,OAAO,MAAM;AAC/B,gBAAA,aAAa,OAAO,eAAe,MAAM;AAC/C,gBAAM,SAAS,aAAa;AAC5B,kBAAQ,IAAI,SAAS,OAAO,QAAQ,IAAI,QAAQ,QAAQ,eAAe,QAAQ,IAAI,UAAU,OAAO,MAAM,EAAE;AACrG,iBAAA;AAAA,QAAA;AAAA,MAEX;AAEA,UAAI,mBAAmB;AACrBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MAAA;AAIc,sBAAA,MAAM,KAAK,cAAc;AAGhB,+BAAA;AAEjB,cAAA,IAAI,SAAS,cAAc;AAC3B,cAAA,IAAI,YAAY,gBAAgB,KAAK;AAAA,IAC/C;AAGM,UAAA,wBAAwB,CAAC,UAAkB;AAC/B,sBAAA,MAAM,OAAO,OAAO,CAAC;AACZ,+BAAA;AACjB,cAAA,IAAI,aAAa,gBAAgB,KAAK;AAAA,IAChD;AAGA,UAAM,2BAA2B,MAAM;AAC/B,YAAA,YAAY,gBAAgB,MAAM,IAAI,YAAU,OAAO,MAAM,EAAE,KAAK,GAAG;AAC7E,YAAM,cAAc,gBAAgB,MAAM,IAAI,CAAU,WAAA,IAAI,OAAO,YAAY,MAAM,EAAE,EAAE,KAAK,GAAG;AAEjG,wBAAkB,QAAQ;AAC1B,0BAAoB,QAAQ;AAE5B,cAAQ,IAAI,cAAc;AAAA,QACxB,WAAW,kBAAkB;AAAA,QAC7B,aAAa,oBAAoB;AAAA,MAAA,CAClC;AAAA,IACH;AAGA,aAAS,oBAAoB;AACvB,UAAA,eAAe,MAAM,UAAU,GAAG;AACpCA,sBAAA,MAAI,UAAU,EAAE,OAAO,cAAc,MAAM,QAAQ;AACnD;AAAA,MAAA;AAGI,YAAA,iBAAiB,IAAI,eAAe,MAAM;AAChDA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,YAAY,CAAC,OAAO;AAAA,QACpB,SAAS,CAAC,QAAa;AACrB,cAAI,IAAI,iBAAiB,IAAI,cAAc,SAAS,GAAG;AACrD,2BAAe,MAAM,KAAK,GAAG,IAAI,aAAa;AAAA,UAAA;AAAA,QAElD;AAAA,QACA,MAAM,CAAC,QAAa;AACV,kBAAA,MAAM,WAAW,GAAG;AAC5BA,wBAAA,MAAI,UAAU,EAAE,OAAO,UAAU,MAAM,QAAQ;AAAA,QAAA;AAAA,MACjD,CACD;AAAA,IAAA;AAIH,aAAS,YAAY;AACf,UAAA,eAAe,MAAM,UAAU,GAAG;AACpCA,sBAAA,MAAI,UAAU,EAAE,OAAO,cAAc,MAAM,QAAQ;AACnD;AAAA,MAAA;AAGFA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,YAAY,CAAC,QAAQ;AAAA,QACrB,SAAS,CAAC,QAAa;AACrB,cAAI,IAAI,iBAAiB,IAAI,cAAc,SAAS,GAAG;AACrD,2BAAe,MAAM,KAAK,GAAG,IAAI,aAAa;AAAA,UAAA;AAAA,QAElD;AAAA,QACA,MAAM,CAAC,QAAa;AACV,kBAAA,MAAM,SAAS,GAAG;AAC1BA,wBAAA,MAAI,UAAU,EAAE,OAAO,QAAQ,MAAM,QAAQ;AAAA,QAAA;AAAA,MAC/C,CACD;AAAA,IAAA;AAIH,aAAS,YAAY,OAAe;AACnB,qBAAA,MAAM,OAAO,OAAO,CAAC;AAAA,IAAA;AAItC,aAAS,aAAa,KAAa;AACjCA,oBAAAA,MAAI,aAAa;AAAA,QACf,SAAS;AAAA,QACT,MAAM,eAAe;AAAA,MAAA,CACtB;AAAA,IAAA;AAIH,aAAS,eAAe;AACtB,UAAI,CAAC,SAAS,MAAM,SAAS,QAAQ;AACnCA,sBAAA,MAAI,UAAU,EAAE,OAAO,WAAW,MAAM,QAAQ;AACzC,eAAA;AAAA,MAAA;AAIL,UAAA,SAAS,MAAM,MAAM,KAAA,KAAU,SAAS,MAAM,MAAM,SAAS,IAAI;AACnEA,sBAAA,MAAI,UAAU,EAAE,OAAO,eAAe,MAAM,QAAQ;AAC7C,eAAA;AAAA,MAAA;AAGT,UAAI,SAAS,MAAM,SAAS,SAAS,KAAM;AACzCA,sBAAA,MAAI,UAAU,EAAE,OAAO,mBAAmB,MAAM,QAAQ;AACjD,eAAA;AAAA,MAAA;AAGF,aAAA;AAAA,IAAA;AAIT,aAAe,iBAAiB;AAAA;AAC9B,YAAI,CAAC,aAAa;AAAG;AAErB,YAAI,aAAa,OAAO;AACtBA,wBAAA,MAAI,UAAU,EAAE,OAAO,gBAAgB,MAAM,QAAQ;AACrD;AAAA,QAAA;AAGE,YAAA;AACF,uBAAa,QAAQ;AAGrB,gBAAM,aAAa;AAAA,YACjB,OAAO,SAAS,MAAM,MAAM,KAAU,KAAA;AAAA,YACtC,WAAW,UAAU,SAAS,UAAU;AAAA,YACxC,UAAU,SAAS,MAAM,SAAS,KAAK;AAAA,YACvC,QAAQ,eAAe;AAAA,YACvB,WAAW,gBAAgB,MAAM,IAAI,CAAA,WAAU,OAAO,MAAM;AAAA;AAAA,UAC9D;AAEQ,kBAAA,IAAI,SAAS,UAAU;AAG/B,gBAAM,WAAW,MAAMD,WAAAA,KAAK,KAAK,oCAAoC,UAAU;AAE/E,cAAI,SAAS,SAAS;AACpBC,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YAAA,CACX;AAGD,4BAAgB,QAAQ,CAAC;AACzB,8BAAkB,QAAQ;AAC1B,gCAAoB,QAAQ;AAG5B,uBAAW,MAAM;AACfA,4BAAAA,MAAI,aAAa;AAAA,gBACf,OAAO;AAAA,gBACP,SAAS,MAAM;AAEbA,gCAAA,MAAI,MAAM,sBAAsB;AAAA,gBAAA;AAAA,cAClC,CACD;AAAA,eACA,GAAI;AAAA,UAAA,OACF;AACL,kBAAM,IAAI,MAAM,SAAS,WAAW,MAAM;AAAA,UAAA;AAAA,iBAGrC,OAAY;AACX,kBAAA,MAAM,SAAS,KAAK;AAC5BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,MAAM,WAAW;AAAA,YACxB,MAAM;AAAA,YACN,UAAU;AAAA,UAAA,CACX;AAAA,QAAA,UACD;AACA,uBAAa,QAAQ;AAAA,QAAA;AAAA,MACvB;AAAA;AAGFC,kBAAA,OAAO,CAAC,YAAiB;AAEvB,eAAS,MAAM,YAAY,UAAU,SAAS,UAAU;AAExC,sBAAA;AAGhB,UAAI,mCAAS,mBAAmB;AACZ,0BAAA,QAAQ,mBAAmB,QAAQ,iBAAiB;AAAA,MAAA;AAGxE,UAAI,mCAAS,qBAAqB;AACZ,4BAAA,QAAQ,mBAAmB,QAAQ,mBAAmB;AAAA,MAAA;AAI1D,mBAAa,UAAU,SAAS,MAAM;AAAA,IAAA,CACzD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACleD,GAAG,WAAW,eAAe;"}