"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
const store_user = require("../../store/user.js");
if (!Array) {
  const _component_NavBar = common_vendor.resolveComponent("NavBar");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_component_NavBar + _easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  _easycom_PageLayout();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "psychologyForm"
}), {
  __name: "form",
  setup(__props) {
    const userStore = store_user.useUserStore();
    const query = common_vendor.ref({});
    common_vendor.onMounted(() => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      query.value = currentPage.options || {};
      if (query.value.mode === "view" && query.value.id) {
        loadFormData(query.value.id);
      }
      if (userStore.userInfo.realname) {
        formData.value.userName = userStore.userInfo.realname;
      }
    });
    const mode = common_vendor.computed(() => query.value.mode || "add");
    (/* @__PURE__ */ new Date()).toISOString().split("T")[0];
    const formData = common_vendor.ref({
      userName: "",
      apathyDay: "",
      sadDay: "",
      sleepDay: "",
      tiredDay: "",
      appetiteDay: "",
      awfulDay: "",
      focusedDay: "",
      slowDay: "",
      nervousDay: "",
      concernDay: "",
      stopDay: "",
      relaxDay: "",
      anxiousDay: "",
      worryDay: "",
      scaredDay: ""
    });
    const loadFormData = (id) => {
      console.log("开始加载日常生活指数评估详情，ID", id);
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      const url = `${"https://www.mograine.cn/api"}/patient/${id}/dailyassessment`;
      console.log("请求详情URL:", url);
      common_vendor.index.request({
        url,
        method: "GET",
        success: (res) => {
          const response = res.data;
          console.log("详情接口返回数据:", response);
          if (response && response.code === 200) {
            if (response.result) {
              formData.value = {
                userName: response.result.userName || "",
                apathyDay: response.result.apathyDay || "",
                sadDay: response.result.sadDay || "",
                sleepDay: response.result.sleepDay || "",
                tiredDay: response.result.tiredDay || "",
                appetiteDay: response.result.appetiteDay || "",
                awfulDay: response.result.awfulDay || "",
                focusedDay: response.result.focusedDay || "",
                slowDay: response.result.slowDay || "",
                nervousDay: response.result.nervousDay || "",
                concernDay: response.result.concernDay || "",
                stopDay: response.result.stopDay || "",
                relaxDay: response.result.relaxDay || "",
                anxiousDay: response.result.anxiousDay || "",
                worryDay: response.result.worryDay || "",
                scaredDay: response.result.scaredDay || ""
              };
              console.log("解析后的详情数据:", formData.value);
            } else {
              console.log("没有找到详情数据");
              common_vendor.index.showToast({
                title: "未找到记录详情",
                icon: "none"
              });
            }
          } else {
            common_vendor.index.showToast({
              title: response.msg || "获取详情失败",
              icon: "none"
            });
          }
        },
        fail: (err) => {
          console.error("获取体征详情失败:", err);
          common_vendor.index.showToast({
            title: "网络异常，请稍后重试",
            icon: "none"
          });
        },
        complete: () => {
          common_vendor.index.hideLoading();
        }
      });
    };
    const submitForm = () => {
      console.log("提交表单数据:", formData.value);
      if (!formData.value.userName) {
        common_vendor.index.showToast({
          title: "请输入姓名",
          icon: "none"
        });
        return;
      }
      if (!formData.value.nervousDay) {
        common_vendor.index.showToast({
          title: "请选择感觉紧张，焦虑或急切的天数",
          icon: "none"
        });
        return;
      }
      if (!formData.value.stopDay) {
        common_vendor.index.showToast({
          title: "请选择不能够停止或控制担忧的天数",
          icon: "none"
        });
        return;
      }
      if (!formData.value.concernDay) {
        common_vendor.index.showToast({
          title: "请选择对各种各样的事情担忧过多的天数",
          icon: "none"
        });
        return;
      }
      if (!formData.value.relaxDay) {
        common_vendor.index.showToast({
          title: "请选择很难放松下来的天数",
          icon: "none"
        });
        return;
      }
      if (!formData.value.anxiousDay) {
        common_vendor.index.showToast({
          title: "请选择由于不安而无法静坐的天数",
          icon: "none"
        });
        return;
      }
      if (!formData.value.worryDay) {
        common_vendor.index.showToast({
          title: "请选择变得容易烦恼或急躁的天数",
          icon: "none"
        });
        return;
      }
      if (!formData.value.scaredDay) {
        common_vendor.index.showToast({
          title: "请选择似乎有可怕的事情发生而害怕天数",
          icon: "none"
        });
        return;
      }
      if (!formData.value.apathyDay) {
        common_vendor.index.showToast({
          title: "请选择做事时提不起劲或者没乐趣的天数",
          icon: "none"
        });
        return;
      }
      if (!formData.value.sadDay) {
        common_vendor.index.showToast({
          title: "请选择感到心情低落的天数",
          icon: "none"
        });
        return;
      }
      if (!formData.value.sleepDay) {
        common_vendor.index.showToast({
          title: "请选择入睡困难的天数",
          icon: "none"
        });
        return;
      }
      if (!formData.value.tiredDay) {
        common_vendor.index.showToast({
          title: "请选择感觉疲劳或没有活力的天数",
          icon: "none"
        });
        return;
      }
      if (!formData.value.appetiteDay) {
        common_vendor.index.showToast({
          title: "请选择食欲不振或吃太多的天数",
          icon: "none"
        });
        return;
      }
      if (!formData.value.awfulDay) {
        common_vendor.index.showToast({
          title: "请选择感觉自己很糟的天数",
          icon: "none"
        });
        return;
      }
      if (!formData.value.focusedDay) {
        common_vendor.index.showToast({
          title: "请选择对事物专注有困难的天数",
          icon: "none"
        });
        return;
      }
      if (!formData.value.slowDay) {
        common_vendor.index.showToast({
          title: "请选择动作或说话速度缓慢的天数",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: mode.value === "add" ? "提交中..." : "保存中..."
      });
      const requestData = {
        userName: formData.value.userName,
        apathyDay: formData.value.apathyDay,
        sadDay: formData.value.sadDay,
        sleepDay: formData.value.sleepDay,
        tiredDay: formData.value.tiredDay,
        appetiteDay: formData.value.appetiteDay,
        awfulDay: formData.value.awfulDay,
        focusedDay: formData.value.focusedDay,
        slowDay: formData.value.slowDay,
        nervousDay: formData.value.nervousDay,
        concernDay: formData.value.concernDay,
        stopDay: formData.value.stopDay,
        relaxDay: formData.value.relaxDay,
        anxiousDay: formData.value.anxiousDay,
        worryDay: formData.value.worryDay,
        scaredDay: formData.value.scaredDay,
        userId: userStore.userInfo.userid
      };
      if (mode.value === "view") {
        requestData.id = query.value.id;
        requestData.updateUserId = userStore.userInfo.userid;
      }
      console.log("formData为：", formData.value);
      console.log("requestData为", requestData);
      common_vendor.index.request({
        url: `${"https://www.mograine.cn/api"}/patient/savedailyassessment`,
        method: "POST",
        data: requestData,
        success: (res) => {
          var _a, _b;
          common_vendor.index.hideLoading();
          if ((_a = res.data) == null ? void 0 : _a.success) {
            common_vendor.index.showModal({
              title: mode.value === "add" ? "提交成功" : "保存成功",
              showCancel: false,
              success: () => {
                common_vendor.index.navigateBack();
              }
            });
          } else {
            const errorMsg = ((_b = res.data) == null ? void 0 : _b.message) || "提交失败，未知错误";
            common_vendor.index.showModal({
              title: mode.value === "add" ? "提交失败" : "保存失败",
              content: errorMsg,
              showCancel: false
            });
          }
        },
        fail: (err) => {
          common_vendor.index.hideLoading();
          const errorMsg = err.errMsg || "网络错误，请稍后重试";
          common_vendor.index.showModal({
            title: "提交失败",
            content: errorMsg,
            showCancel: false
          });
        }
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          title: mode.value === "add" ? "新增" : "查看",
          showBack: true
        }),
        b: formData.value.userName,
        c: common_vendor.o(($event) => formData.value.userName = $event.detail.value),
        d: formData.value.nervousDay === "完全不会" ? 1 : "",
        e: common_vendor.o(($event) => formData.value.nervousDay = "完全不会"),
        f: formData.value.nervousDay === "好几天" ? 1 : "",
        g: common_vendor.o(($event) => formData.value.nervousDay = "好几天"),
        h: formData.value.nervousDay === "超过一周" ? 1 : "",
        i: common_vendor.o(($event) => formData.value.nervousDay = "超过一周"),
        j: formData.value.nervousDay === "几乎每天" ? 1 : "",
        k: common_vendor.o(($event) => formData.value.nervousDay = "几乎每天"),
        l: formData.value.stopDay === "完全不会" ? 1 : "",
        m: common_vendor.o(($event) => formData.value.stopDay = "完全不会"),
        n: formData.value.stopDay === "好几天" ? 1 : "",
        o: common_vendor.o(($event) => formData.value.stopDay = "好几天"),
        p: formData.value.stopDay === "超过一周" ? 1 : "",
        q: common_vendor.o(($event) => formData.value.stopDay = "超过一周"),
        r: formData.value.stopDay === "几乎每天" ? 1 : "",
        s: common_vendor.o(($event) => formData.value.stopDay = "几乎每天"),
        t: formData.value.concernDay === "完全不会" ? 1 : "",
        v: common_vendor.o(($event) => formData.value.concernDay = "完全不会"),
        w: formData.value.concernDay === "好几天" ? 1 : "",
        x: common_vendor.o(($event) => formData.value.concernDay = "好几天"),
        y: formData.value.concernDay === "超过一周" ? 1 : "",
        z: common_vendor.o(($event) => formData.value.concernDay = "超过一周"),
        A: formData.value.concernDay === "几乎每天" ? 1 : "",
        B: common_vendor.o(($event) => formData.value.concernDay = "几乎每天"),
        C: formData.value.relaxDay === "完全不会" ? 1 : "",
        D: common_vendor.o(($event) => formData.value.relaxDay = "完全不会"),
        E: formData.value.relaxDay === "好几天" ? 1 : "",
        F: common_vendor.o(($event) => formData.value.relaxDay = "好几天"),
        G: formData.value.relaxDay === "超过一周" ? 1 : "",
        H: common_vendor.o(($event) => formData.value.relaxDay = "超过一周"),
        I: formData.value.relaxDay === "几乎每天" ? 1 : "",
        J: common_vendor.o(($event) => formData.value.relaxDay = "几乎每天"),
        K: formData.value.anxiousDay === "完全不会" ? 1 : "",
        L: common_vendor.o(($event) => formData.value.anxiousDay = "完全不会"),
        M: formData.value.anxiousDay === "好几天" ? 1 : "",
        N: common_vendor.o(($event) => formData.value.anxiousDay = "好几天"),
        O: formData.value.anxiousDay === "超过一周" ? 1 : "",
        P: common_vendor.o(($event) => formData.value.anxiousDay = "超过一周"),
        Q: formData.value.anxiousDay === "几乎每天" ? 1 : "",
        R: common_vendor.o(($event) => formData.value.anxiousDay = "几乎每天"),
        S: formData.value.worryDay === "完全不会" ? 1 : "",
        T: common_vendor.o(($event) => formData.value.worryDay = "完全不会"),
        U: formData.value.worryDay === "好几天" ? 1 : "",
        V: common_vendor.o(($event) => formData.value.worryDay = "好几天"),
        W: formData.value.worryDay === "超过一周" ? 1 : "",
        X: common_vendor.o(($event) => formData.value.worryDay = "超过一周"),
        Y: formData.value.worryDay === "几乎每天" ? 1 : "",
        Z: common_vendor.o(($event) => formData.value.worryDay = "几乎每天"),
        aa: formData.value.scaredDay === "完全不会" ? 1 : "",
        ab: common_vendor.o(($event) => formData.value.scaredDay = "完全不会"),
        ac: formData.value.scaredDay === "好几天" ? 1 : "",
        ad: common_vendor.o(($event) => formData.value.scaredDay = "好几天"),
        ae: formData.value.scaredDay === "超过一周" ? 1 : "",
        af: common_vendor.o(($event) => formData.value.scaredDay = "超过一周"),
        ag: formData.value.scaredDay === "几乎每天" ? 1 : "",
        ah: common_vendor.o(($event) => formData.value.scaredDay = "几乎每天"),
        ai: formData.value.apathyDay === "完全不会" ? 1 : "",
        aj: common_vendor.o(($event) => formData.value.apathyDay = "完全不会"),
        ak: formData.value.apathyDay === "几天" ? 1 : "",
        al: common_vendor.o(($event) => formData.value.apathyDay = "几天"),
        am: formData.value.apathyDay === "一半以上" ? 1 : "",
        an: common_vendor.o(($event) => formData.value.apathyDay = "一半以上"),
        ao: formData.value.apathyDay === "几乎每天" ? 1 : "",
        ap: common_vendor.o(($event) => formData.value.apathyDay = "几乎每天"),
        aq: formData.value.sadDay === "完全不会" ? 1 : "",
        ar: common_vendor.o(($event) => formData.value.sadDay = "完全不会"),
        as: formData.value.sadDay === "几天" ? 1 : "",
        at: common_vendor.o(($event) => formData.value.sadDay = "几天"),
        av: formData.value.sadDay === "一半以上" ? 1 : "",
        aw: common_vendor.o(($event) => formData.value.sadDay = "一半以上"),
        ax: formData.value.sadDay === "几乎每天" ? 1 : "",
        ay: common_vendor.o(($event) => formData.value.sadDay = "几乎每天"),
        az: formData.value.sleepDay === "完全不会" ? 1 : "",
        aA: common_vendor.o(($event) => formData.value.sleepDay = "完全不会"),
        aB: formData.value.sleepDay === "几天" ? 1 : "",
        aC: common_vendor.o(($event) => formData.value.sleepDay = "几天"),
        aD: formData.value.sleepDay === "一半以上" ? 1 : "",
        aE: common_vendor.o(($event) => formData.value.sleepDay = "一半以上"),
        aF: formData.value.sleepDay === "几乎每天" ? 1 : "",
        aG: common_vendor.o(($event) => formData.value.sleepDay = "几乎每天"),
        aH: formData.value.tiredDay === "完全不会" ? 1 : "",
        aI: common_vendor.o(($event) => formData.value.tiredDay = "完全不会"),
        aJ: formData.value.tiredDay === "几天" ? 1 : "",
        aK: common_vendor.o(($event) => formData.value.tiredDay = "几天"),
        aL: formData.value.tiredDay === "一半以上" ? 1 : "",
        aM: common_vendor.o(($event) => formData.value.tiredDay = "一半以上"),
        aN: formData.value.tiredDay === "几乎每天" ? 1 : "",
        aO: common_vendor.o(($event) => formData.value.tiredDay = "几乎每天"),
        aP: formData.value.appetiteDay === "完全不会" ? 1 : "",
        aQ: common_vendor.o(($event) => formData.value.appetiteDay = "完全不会"),
        aR: formData.value.appetiteDay === "几天" ? 1 : "",
        aS: common_vendor.o(($event) => formData.value.appetiteDay = "几天"),
        aT: formData.value.appetiteDay === "一半以上" ? 1 : "",
        aU: common_vendor.o(($event) => formData.value.appetiteDay = "一半以上"),
        aV: formData.value.appetiteDay === "几乎每天" ? 1 : "",
        aW: common_vendor.o(($event) => formData.value.appetiteDay = "几乎每天"),
        aX: formData.value.awfulDay === "完全不会" ? 1 : "",
        aY: common_vendor.o(($event) => formData.value.awfulDay = "完全不会"),
        aZ: formData.value.awfulDay === "几天" ? 1 : "",
        ba: common_vendor.o(($event) => formData.value.awfulDay = "几天"),
        bb: formData.value.awfulDay === "一半以上" ? 1 : "",
        bc: common_vendor.o(($event) => formData.value.awfulDay = "一半以上"),
        bd: formData.value.awfulDay === "几乎每天" ? 1 : "",
        be: common_vendor.o(($event) => formData.value.awfulDay = "几乎每天"),
        bf: formData.value.focusedDay === "完全不会" ? 1 : "",
        bg: common_vendor.o(($event) => formData.value.focusedDay = "完全不会"),
        bh: formData.value.focusedDay === "几天" ? 1 : "",
        bi: common_vendor.o(($event) => formData.value.focusedDay = "几天"),
        bj: formData.value.focusedDay === "一半以上" ? 1 : "",
        bk: common_vendor.o(($event) => formData.value.focusedDay = "一半以上"),
        bl: formData.value.focusedDay === "几乎每天" ? 1 : "",
        bm: common_vendor.o(($event) => formData.value.focusedDay = "几乎每天"),
        bn: formData.value.slowDay === "完全不会" ? 1 : "",
        bo: common_vendor.o(($event) => formData.value.slowDay = "完全不会"),
        bp: formData.value.slowDay === "几天" ? 1 : "",
        bq: common_vendor.o(($event) => formData.value.slowDay = "几天"),
        br: formData.value.slowDay === "一半以上" ? 1 : "",
        bs: common_vendor.o(($event) => formData.value.slowDay = "一半以上"),
        bt: formData.value.slowDay === "几乎每天" ? 1 : "",
        bv: common_vendor.o(($event) => formData.value.slowDay = "几乎每天"),
        bw: common_vendor.t(mode.value === "add" ? "提交" : "保存"),
        bx: common_vendor.o(submitForm)
      };
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-cff85637"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=form.js.map
