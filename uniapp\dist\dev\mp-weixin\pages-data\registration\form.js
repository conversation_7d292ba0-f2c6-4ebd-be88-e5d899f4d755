"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
const components_online_areaPickerData = require("../../components/online/area-picker-data.js");
const store_user = require("../../store/user.js");
if (!Array) {
  const _component_NavBar = common_vendor.resolveComponent("NavBar");
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  const _easycom_wd_picker2 = common_vendor.resolveComponent("wd-picker");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_component_NavBar + _easycom_uni_icons2 + _easycom_wd_picker2 + _easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
const _easycom_wd_picker = () => "../../node-modules/wot-design-uni/components/wd-picker/wd-picker.js";
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  (_easycom_uni_icons + _easycom_wd_picker + _easycom_PageLayout)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "RegistrationForm"
}), {
  __name: "form",
  setup(__props) {
    const userStore = store_user.useUserStore();
    const query = common_vendor.ref({});
    common_vendor.onMounted(() => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      query.value = currentPage.options || {};
      if (userStore.userInfo.phone) {
        formData.value.phone = userStore.userInfo.phone;
      }
    });
    const mode = common_vendor.computed(() => query.value.mode || "add");
    const isViewMode = common_vendor.computed(() => mode.value === "view");
    const formDateString = (/* @__PURE__ */ new Date()).toISOString().split("T")[0];
    const selectedArea = common_vendor.ref([]);
    const tempSelectedArea = common_vendor.ref([]);
    const district = __spreadValues({}, components_online_areaPickerData.areaData);
    const areaColumns = common_vendor.ref([
      district[0],
      district[district[0][0].value],
      district[district[district[0][0].value][0].value]
    ]);
    const onAreaColumnChange = (pickerView, value, columnIndex, resolve) => {
      var _a, _b, _c;
      const item = value[columnIndex];
      if (columnIndex === 0) {
        pickerView.setColumnData(1, district[item.value]);
        pickerView.setColumnData(2, district[district[item.value][0].value]);
        if (!tempSelectedArea.value[0] || ((_a = tempSelectedArea.value[0]) == null ? void 0 : _a.value) !== item.value) {
          tempSelectedArea.value[0] = item;
          tempSelectedArea.value[1] = district[item.value][0];
          tempSelectedArea.value[2] = district[district[item.value][0].value][0];
        }
      } else if (columnIndex === 1) {
        pickerView.setColumnData(2, district[item.value]);
        if (!tempSelectedArea.value[1] || ((_b = tempSelectedArea.value[1]) == null ? void 0 : _b.value) !== item.value) {
          tempSelectedArea.value[1] = item;
          tempSelectedArea.value[2] = district[item.value][0];
        }
      } else if (columnIndex === 2) {
        if (!tempSelectedArea.value[2] || ((_c = tempSelectedArea.value[2]) == null ? void 0 : _c.value) !== item.value) {
          tempSelectedArea.value[2] = item;
        }
      }
      resolve();
    };
    const onAreaConfirm = ({ value, selectedItems }) => {
      var _a, _b, _c, _d, _e, _f;
      console.log("onAreaConfirm调用，value:", JSON.stringify(value));
      console.log("onAreaConfirm调用，selectedItems:", JSON.stringify(selectedItems));
      console.log("onAreaConfirm调用，临时selectedArea:", JSON.stringify(tempSelectedArea.value));
      if (selectedItems && selectedItems.length === 3) {
        const province = ((_a = selectedItems[0]) == null ? void 0 : _a.label) || "";
        const city = ((_b = selectedItems[1]) == null ? void 0 : _b.label) || "";
        const district2 = ((_c = selectedItems[2]) == null ? void 0 : _c.label) || "";
        console.log("最终提取的省市区文字:", province, city, district2);
        selectedArea.value = [...tempSelectedArea.value];
        formData.value.area = `${province},${city},${district2}`;
        formData.value.areaCode = ((_d = selectedItems[2]) == null ? void 0 : _d.value) || "";
        common_vendor.nextTick$1(() => {
          console.log("区域选择更新后的值:", formData.value.area);
        });
        console.log("最终设置的地区值(文字):", formData.value.area);
        console.log("最终设置的区域编码:", formData.value.areaCode);
      } else {
        console.error("selectedItems数据不完整:", selectedItems);
        console.error("当前selectedArea:", selectedArea.value);
        if (value && Array.isArray(value) && value.length === 3) {
          try {
            const provinceCode = value[0];
            const cityCode = value[1];
            const districtCode = value[2];
            const provinceItem = district[0].find((item) => item.value === provinceCode);
            const cityItem = (_e = district[provinceCode]) == null ? void 0 : _e.find((item) => item.value === cityCode);
            const districtItem = (_f = district[cityCode]) == null ? void 0 : _f.find((item) => item.value === districtCode);
            if (provinceItem && cityItem && districtItem) {
              formData.value.area = `${provinceItem.label},${cityItem.label},${districtItem.label}`;
              formData.value.areaCode = districtCode;
              console.log("通过value数组查找成功，地区值:", formData.value.area);
            }
          } catch (error) {
            console.error("通过value查找地区信息失败:", error);
          }
        }
      }
    };
    const temperatureSites = ["请选择", "口腔", "腋下", "直肠", "其他"];
    const formData = common_vendor.ref({
      name: "",
      phone: "",
      sex: null,
      maritalStatus: "",
      ageGroup: "",
      birthDate: "",
      occupationalStatus: "",
      income: "",
      occupation: "",
      education: "",
      area: "",
      address: "",
      areaCode: "",
      // 添加区域编码字段
      medicalNumber: "",
      conditions: "",
      hasConsent: true,
      vitalSigns: {
        bodyPart: false,
        temperature: false,
        heartRate: false,
        breathing: false,
        bloodPressure: false,
        oxygen: false
      },
      temperatureSiteIndex: 0,
      temperatureSite: "请选择",
      // 添加体温测量部位文字
      tempertureValue: "",
      heartRateValue: "",
      breathingValue: "",
      bloodPressureValue: "",
      oxygenValue: "",
      medicationStatus: "",
      medicationDesc: "",
      healthProblem: "",
      problemDesc: "",
      compliance: "",
      diseases: {
        smoking: false,
        drinking: false,
        hypertension: false,
        diabetes: false,
        hyperlipidemia: false,
        chronicKidney: false,
        chronicLung: false,
        tumor: false,
        mainDiagnosis: false,
        diseaseSummary: false
      },
      mainDiagnosisDesc: "",
      diseaseSummaryDesc: ""
    });
    const onBirthDateChange = (e) => {
      formData.value.birthDate = e.detail.value;
      if (e.detail.value) {
        new Date(e.detail.value).getFullYear();
        (/* @__PURE__ */ new Date()).getFullYear();
      }
    };
    const submitForm = () => {
      var _a, _b, _c;
      console.log("提交表单数据:", formData.value);
      console.log("formData.area:", formData.value.area);
      console.log("selectedArea:", selectedArea.value);
      if (selectedArea.value && Array.isArray(selectedArea.value) && selectedArea.value.length === 3) {
        try {
          console.log("检查区域信息，selectedArea:", JSON.stringify(selectedArea.value));
          if (selectedArea.value && selectedArea.value.length === 3 && ((_a = selectedArea.value[0]) == null ? void 0 : _a.label) && ((_b = selectedArea.value[1]) == null ? void 0 : _b.label) && ((_c = selectedArea.value[2]) == null ? void 0 : _c.label)) {
            const province = selectedArea.value[0].label;
            const city = selectedArea.value[1].label;
            const district2 = selectedArea.value[2].label;
            console.log("提交前确认的省市区文字:", province, city, district2);
            formData.value.area = `${province},${city},${district2}`;
            formData.value.areaCode = selectedArea.value[2].value;
            console.log("提交前确认的地区值:", formData.value.area);
          } else {
            console.log("selectedArea数据格式不正确或不完整:", selectedArea.value);
            if (!formData.value.area) {
              console.warn("地区信息缺失，请重新选择地区");
            }
          }
        } catch (error) {
          console.error("设置地区值出错:", error, selectedArea.value);
        }
      }
      if (!formData.value.name) {
        common_vendor.index.showToast({
          title: "请输入患者姓名",
          icon: "none"
        });
        return;
      }
      if (!formData.value.phone) {
        common_vendor.index.showToast({
          title: "请输入联系电话",
          icon: "none"
        });
        return;
      }
      if (!formData.value.sex) {
        common_vendor.index.showToast({
          title: "请选择性别",
          icon: "none"
        });
        return;
      }
      if (!formData.value.maritalStatus) {
        common_vendor.index.showToast({
          title: "请选择婚姻状况",
          icon: "none"
        });
        return;
      }
      if (!formData.value.birthDate) {
        common_vendor.index.showToast({
          title: "请选择出生日期",
          icon: "none"
        });
        return;
      }
      if (!formData.value.ageGroup) {
        common_vendor.index.showToast({
          title: "请选择年龄段",
          icon: "none"
        });
        return;
      }
      if (!formData.value.occupationalStatus) {
        common_vendor.index.showToast({
          title: "请选择职业状态",
          icon: "none"
        });
        return;
      }
      if (!formData.value.income) {
        common_vendor.index.showToast({
          title: "请选择年度经济收入状况",
          icon: "none"
        });
        return;
      }
      if (!formData.value.occupation) {
        common_vendor.index.showToast({
          title: "请选择您目前从事的职业",
          icon: "none"
        });
        return;
      }
      if (!formData.value.education) {
        common_vendor.index.showToast({
          title: "请选择您的学历",
          icon: "none"
        });
        return;
      }
      if (!formData.value.area) {
        console.log("地区为空，检查选择器:", selectedArea.value);
        common_vendor.index.showToast({
          title: "请选择所在地区",
          icon: "none"
        });
        return;
      }
      if (!formData.value.address) {
        common_vendor.index.showToast({
          title: "请输入街道地址",
          icon: "none"
        });
        return;
      }
      if (!formData.value.vitalSigns || !formData.value.vitalSigns.bodyPart && !formData.value.vitalSigns.temperature && !formData.value.vitalSigns.heartRate && !formData.value.vitalSigns.breathing && !formData.value.vitalSigns.bloodPressure && !formData.value.vitalSigns.oxygen) {
        common_vendor.index.showToast({
          title: "请选择至少一项体征监测情况",
          icon: "none"
        });
        return;
      }
      if (formData.value.vitalSigns.bodyPart && formData.value.temperatureSiteIndex === 0) {
        common_vendor.index.showToast({
          title: "请选择体温测量部位",
          icon: "none"
        });
        return;
      }
      if (formData.value.vitalSigns.temperature && !formData.value.tempertureValue) {
        common_vendor.index.showToast({
          title: "请输入体温值",
          icon: "none"
        });
        return;
      }
      if (formData.value.vitalSigns.heartRate && !formData.value.heartRateValue) {
        common_vendor.index.showToast({
          title: "请输入心率值",
          icon: "none"
        });
        return;
      }
      if (formData.value.vitalSigns.breathing && !formData.value.breathingValue) {
        common_vendor.index.showToast({
          title: "请输入呼吸值",
          icon: "none"
        });
        return;
      }
      if (formData.value.vitalSigns.bloodPressure && !formData.value.bloodPressureValue) {
        common_vendor.index.showToast({
          title: "请输入血压值",
          icon: "none"
        });
        return;
      }
      if (formData.value.vitalSigns.oxygen && !formData.value.oxygenValue) {
        common_vendor.index.showToast({
          title: "请输入血氧饱和度值",
          icon: "none"
        });
        return;
      }
      if (!formData.value.medicationStatus) {
        common_vendor.index.showToast({
          title: "请选择是否服用药物或接受特殊治疗",
          icon: "none"
        });
        return;
      }
      if (formData.value.medicationStatus === "是" && !formData.value.medicationDesc) {
        common_vendor.index.showToast({
          title: "请输入药物名称或治疗类型",
          icon: "none"
        });
        return;
      }
      if (!formData.value.conditions) {
        common_vendor.index.showToast({
          title: "请输入身体状况描述",
          icon: "none"
        });
        return;
      }
      if (!formData.value.healthProblem) {
        common_vendor.index.showToast({
          title: "请选择是否有已知的健康问题或慢性疾病",
          icon: "none"
        });
        return;
      }
      if (formData.value.healthProblem === "是" && !formData.value.problemDesc) {
        common_vendor.index.showToast({
          title: "请列出健康问题或慢性疾病",
          icon: "none"
        });
        return;
      }
      if (!formData.value.compliance) {
        common_vendor.index.showToast({
          title: "请选择患者依从性",
          icon: "none"
        });
        return;
      }
      if (!formData.value.diseases || !formData.value.diseases.smoking && !formData.value.diseases.drinking && !formData.value.diseases.hypertension && !formData.value.diseases.diabetes && !formData.value.diseases.hyperlipidemia && !formData.value.diseases.chronicKidney && !formData.value.diseases.chronicLung && !formData.value.diseases.tumor && !formData.value.diseases.mainDiagnosis && !formData.value.diseases.diseaseSummary) {
        common_vendor.index.showToast({
          title: "请至少选择一项疾病情况",
          icon: "none"
        });
        return;
      }
      if (formData.value.diseases.mainDiagnosis && !formData.value.mainDiagnosisDesc) {
        common_vendor.index.showToast({
          title: "请输入主要疾病诊断",
          icon: "none"
        });
        return;
      }
      if (formData.value.diseases.diseaseSummary && !formData.value.diseaseSummaryDesc) {
        common_vendor.index.showToast({
          title: "请简述疾病情况",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "提交中..."
      });
      const requestData = {
        name: formData.value.name,
        phone: formData.value.phone,
        sex: formData.value.sex,
        maritalStatus: formData.value.maritalStatus,
        birthDate: formData.value.birthDate,
        ageGroup: formData.value.ageGroup,
        occupationalStatus: formData.value.occupationalStatus,
        income: formData.value.income,
        occupation: formData.value.occupation,
        education: formData.value.education,
        area: formData.value.area,
        address: formData.value.address,
        temperatureSite: formData.value.temperatureSite,
        tempertureValue: formData.value.tempertureValue,
        heartRateValue: formData.value.heartRateValue,
        breathingValue: formData.value.breathingValue,
        bloodPressureValue: formData.value.bloodPressureValue,
        oxygenValue: formData.value.oxygenValue,
        medicationDesc: formData.value.medicationDesc,
        conditions: formData.value.conditions,
        problemDesc: formData.value.problemDesc,
        compliance: formData.value.compliance,
        diseases: formData.value.diseases,
        mainDiagnosisDesc: formData.value.mainDiagnosisDesc,
        diseaseSummaryDesc: formData.value.diseaseSummaryDesc,
        userId: userStore.userInfo.userid
      };
      console.log("formData为：", formData.value);
      console.log("requestData为", requestData);
      common_vendor.index.request({
        url: `${"https://www.mograine.cn/api"}/patient/saveregistration`,
        method: "POST",
        data: requestData,
        success: (res) => {
          var _a2, _b2;
          common_vendor.index.hideLoading();
          if ((_a2 = res.data) == null ? void 0 : _a2.success) {
            userStore.editUserInfo({ realname: formData.value.name });
            userStore.editUserInfo({ sex: formData.value.sex });
            callAddRoleAPI();
            common_vendor.index.showModal({
              title: "提交成功",
              showCancel: false,
              success: () => {
                common_vendor.index.switchTab({
                  url: "/pages/index/index"
                });
              }
            });
          } else {
            const errorMsg = ((_b2 = res.data) == null ? void 0 : _b2.message) || "提交失败，未知错误";
            common_vendor.index.showModal({
              title: "提交失败",
              content: errorMsg,
              showCancel: false
            });
          }
        },
        fail: (err) => {
          common_vendor.index.hideLoading();
          const errorMsg = err.errMsg || "网络错误，请稍后重试";
          common_vendor.index.showModal({
            title: "提交失败",
            content: errorMsg,
            showCancel: false
          });
        }
      });
    };
    const callAddRoleAPI = () => {
      const userId = userStore.userInfo.userid;
      const userCategory = userStore.userInfo.userCategory;
      console.log("调用 addRole 接口", { userId, userCategory });
      common_vendor.index.request({
        url: `${"https://www.mograine.cn/api"}/sys/role/addRole`,
        method: "POST",
        header: {
          "X-Access-Token": userStore.userInfo.token
        },
        data: {
          userId,
          userCategory
        },
        success: (res) => {
          console.log("addRole 接口调用成功", res.data);
          const currentRoles = userStore.userInfo.roleList || [];
          if (!currentRoles.includes("1")) {
            userStore.editUserInfo({ roleList: [...currentRoles, "1"] });
          }
        },
        fail: (err) => {
          console.error("addRole 接口调用失败", err);
        }
      });
    };
    const onTemperatureSiteChange = (e) => {
      const index = parseInt(e.detail.value);
      formData.value.temperatureSiteIndex = index;
      formData.value.temperatureSite = temperatureSites[index];
    };
    common_vendor.watch(() => formData.value.medicationStatus, (newVal) => {
      if (newVal === "否") {
        formData.value.medicationDesc = "";
      }
    });
    common_vendor.watch(() => formData.value.healthProblem, (newVal) => {
      if (newVal === "否") {
        formData.value.problemDesc = "";
      }
    });
    const toggleVitalSign = (sign) => {
      if (!formData.value.vitalSigns) {
        formData.value.vitalSigns = {
          bodyPart: false,
          temperature: false,
          heartRate: false,
          breathing: false,
          bloodPressure: false,
          oxygen: false
        };
      }
      const newStatus = !formData.value.vitalSigns[sign];
      formData.value.vitalSigns = __spreadProps(__spreadValues({}, formData.value.vitalSigns), {
        [sign]: newStatus
      });
      if (!newStatus) {
        switch (sign) {
          case "bodyPart":
            formData.value.temperatureSiteIndex = 0;
            formData.value.temperatureSite = temperatureSites[0];
            break;
          case "temperature":
            formData.value.tempertureValue = "";
            break;
          case "heartRate":
            formData.value.heartRateValue = "";
            break;
          case "breathing":
            formData.value.breathingValue = "";
            break;
          case "bloodPressure":
            formData.value.bloodPressureValue = "";
            break;
          case "oxygen":
            formData.value.oxygenValue = "";
            break;
        }
      }
    };
    const toggleDisease = (disease) => {
      if (!formData.value.diseases) {
        formData.value.diseases = {
          smoking: false,
          drinking: false,
          hypertension: false,
          diabetes: false,
          hyperlipidemia: false,
          chronicKidney: false,
          chronicLung: false,
          tumor: false,
          mainDiagnosis: false,
          diseaseSummary: false
        };
      }
      const newStatus = !formData.value.diseases[disease];
      formData.value.diseases = __spreadProps(__spreadValues({}, formData.value.diseases), {
        [disease]: newStatus
      });
      if (!newStatus) {
        switch (disease) {
          case "mainDiagnosis":
            formData.value.mainDiagnosisDesc = "";
            break;
          case "diseaseSummary":
            formData.value.diseaseSummaryDesc = "";
            break;
        }
      }
    };
    const displayAreaText = common_vendor.computed(() => {
      if (formData.value.area) {
        return formData.value.area;
      }
      return "";
    });
    common_vendor.watch(selectedArea, (newVal) => {
      console.log("selectedArea变化:", JSON.stringify(newVal));
      console.log("selectedArea类型:", typeof newVal, Array.isArray(newVal));
      if (Array.isArray(newVal) && newVal.length > 0) {
        console.log("第一个元素类型:", typeof newVal[0], newVal[0]);
      }
    }, { deep: true });
    common_vendor.watch(tempSelectedArea, (newVal) => {
      console.log("tempSelectedArea变化:", JSON.stringify(newVal));
    }, { deep: true });
    common_vendor.watch(() => formData.value.area, (newVal) => {
      console.log("formData.area变化:", newVal);
    });
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.p({
          title: mode.value === "add" ? "新增登记表" : "查看登记表",
          showBack: true
        }),
        b: isViewMode.value,
        c: formData.value.name,
        d: common_vendor.o(($event) => formData.value.name = $event.detail.value),
        e: isViewMode.value,
        f: formData.value.phone,
        g: common_vendor.o(($event) => formData.value.phone = $event.detail.value),
        h: formData.value.sex === 1 ? 1 : "",
        i: common_vendor.o(($event) => !isViewMode.value && (formData.value.sex = 1)),
        j: formData.value.sex === 2 ? 1 : "",
        k: common_vendor.o(($event) => !isViewMode.value && (formData.value.sex = 2)),
        l: formData.value.maritalStatus === "未婚" ? 1 : "",
        m: common_vendor.o(($event) => !isViewMode.value && (formData.value.maritalStatus = "未婚")),
        n: formData.value.maritalStatus === "已婚" ? 1 : "",
        o: common_vendor.o(($event) => !isViewMode.value && (formData.value.maritalStatus = "已婚")),
        p: formData.value.maritalStatus === "离异" ? 1 : "",
        q: common_vendor.o(($event) => !isViewMode.value && (formData.value.maritalStatus = "离异")),
        r: formData.value.birthDate
      }, formData.value.birthDate ? {
        s: common_vendor.t(formData.value.birthDate)
      } : {}, {
        t: !isViewMode.value
      }, !isViewMode.value ? {
        v: common_vendor.p({
          type: "calendar",
          size: "18"
        })
      } : {}, {
        w: formData.value.birthDate,
        x: common_vendor.unref(formDateString),
        y: common_vendor.o(onBirthDateChange),
        z: isViewMode.value,
        A: formData.value.ageGroup === "18岁以下" ? 1 : "",
        B: common_vendor.o(($event) => !isViewMode.value && (formData.value.ageGroup = "18岁以下")),
        C: formData.value.ageGroup === "18~25岁" ? 1 : "",
        D: common_vendor.o(($event) => !isViewMode.value && (formData.value.ageGroup = "18~25岁")),
        E: formData.value.ageGroup === "26~30岁" ? 1 : "",
        F: common_vendor.o(($event) => !isViewMode.value && (formData.value.ageGroup = "26~30岁")),
        G: formData.value.ageGroup === "31~40岁" ? 1 : "",
        H: common_vendor.o(($event) => !isViewMode.value && (formData.value.ageGroup = "31~40岁")),
        I: formData.value.ageGroup === "41~50岁" ? 1 : "",
        J: common_vendor.o(($event) => !isViewMode.value && (formData.value.ageGroup = "41~50岁")),
        K: formData.value.ageGroup === "51~60岁" ? 1 : "",
        L: common_vendor.o(($event) => !isViewMode.value && (formData.value.ageGroup = "51~60岁")),
        M: formData.value.ageGroup === "60岁以上" ? 1 : "",
        N: common_vendor.o(($event) => !isViewMode.value && (formData.value.ageGroup = "60岁以上")),
        O: formData.value.occupationalStatus === "在职" ? 1 : "",
        P: common_vendor.o(($event) => !isViewMode.value && (formData.value.occupationalStatus = "在职")),
        Q: formData.value.occupationalStatus === "退休" ? 1 : "",
        R: common_vendor.o(($event) => !isViewMode.value && (formData.value.occupationalStatus = "退休")),
        S: formData.value.income === "5万以下" ? 1 : "",
        T: common_vendor.o(($event) => !isViewMode.value && (formData.value.income = "5万以下")),
        U: formData.value.income === "5万以上" ? 1 : "",
        V: common_vendor.o(($event) => !isViewMode.value && (formData.value.income = "5万以上")),
        W: formData.value.income === "10万以上" ? 1 : "",
        X: common_vendor.o(($event) => !isViewMode.value && (formData.value.income = "10万以上")),
        Y: formData.value.occupation === "市场/销售/商务" ? 1 : "",
        Z: common_vendor.o(($event) => !isViewMode.value && (formData.value.occupation = "市场/销售/商务")),
        aa: formData.value.occupation === "采购" ? 1 : "",
        ab: common_vendor.o(($event) => !isViewMode.value && (formData.value.occupation = "采购")),
        ac: formData.value.occupation === "行政" ? 1 : "",
        ad: common_vendor.o(($event) => !isViewMode.value && (formData.value.occupation = "行政")),
        ae: formData.value.occupation === "人力" ? 1 : "",
        af: common_vendor.o(($event) => !isViewMode.value && (formData.value.occupation = "人力")),
        ag: formData.value.occupation === "产品/运营人员" ? 1 : "",
        ah: common_vendor.o(($event) => !isViewMode.value && (formData.value.occupation = "产品/运营人员")),
        ai: formData.value.occupation === "个体经营者" ? 1 : "",
        aj: common_vendor.o(($event) => !isViewMode.value && (formData.value.occupation = "个体经营者")),
        ak: formData.value.occupation === "财务/会计/出纳/审计" ? 1 : "",
        al: common_vendor.o(($event) => !isViewMode.value && (formData.value.occupation = "财务/会计/出纳/审计")),
        am: formData.value.occupation === "企业管理者" ? 1 : "",
        an: common_vendor.o(($event) => !isViewMode.value && (formData.value.occupation = "企业管理者")),
        ao: formData.value.occupation === "律师/法务" ? 1 : "",
        ap: common_vendor.o(($event) => !isViewMode.value && (formData.value.occupation = "律师/法务")),
        aq: formData.value.occupation === "设计从业者" ? 1 : "",
        ar: common_vendor.o(($event) => !isViewMode.value && (formData.value.occupation = "设计从业者")),
        as: formData.value.occupation === "服务业人员" ? 1 : "",
        at: common_vendor.o(($event) => !isViewMode.value && (formData.value.occupation = "服务业人员")),
        av: formData.value.occupation === "技术开发/工程师" ? 1 : "",
        aw: common_vendor.o(($event) => !isViewMode.value && (formData.value.occupation = "技术开发/工程师")),
        ax: formData.value.occupation === "农林牧渔劳动者" ? 1 : "",
        ay: common_vendor.o(($event) => !isViewMode.value && (formData.value.occupation = "农林牧渔劳动者")),
        az: formData.value.occupation === "工人劳动者" ? 1 : "",
        aA: common_vendor.o(($event) => !isViewMode.value && (formData.value.occupation = "工人劳动者")),
        aB: formData.value.occupation === "全职家庭主妇/夫" ? 1 : "",
        aC: common_vendor.o(($event) => !isViewMode.value && (formData.value.occupation = "全职家庭主妇/夫")),
        aD: formData.value.occupation === "自由职业" ? 1 : "",
        aE: common_vendor.o(($event) => !isViewMode.value && (formData.value.occupation = "自由职业")),
        aF: formData.value.occupation === "离休/退休" ? 1 : "",
        aG: common_vendor.o(($event) => !isViewMode.value && (formData.value.occupation = "离休/退休")),
        aH: formData.value.occupation === "学生" ? 1 : "",
        aI: common_vendor.o(($event) => !isViewMode.value && (formData.value.occupation = "学生")),
        aJ: formData.value.occupation === "老师" ? 1 : "",
        aK: common_vendor.o(($event) => !isViewMode.value && (formData.value.occupation = "老师")),
        aL: formData.value.occupation === "医护人员" ? 1 : "",
        aM: common_vendor.o(($event) => !isViewMode.value && (formData.value.occupation = "医护人员")),
        aN: formData.value.occupation === "科研人员" ? 1 : "",
        aO: common_vendor.o(($event) => !isViewMode.value && (formData.value.occupation = "科研人员")),
        aP: formData.value.occupation === "党政机关人员" ? 1 : "",
        aQ: common_vendor.o(($event) => !isViewMode.value && (formData.value.occupation = "党政机关人员")),
        aR: formData.value.education === "初中及以下" ? 1 : "",
        aS: common_vendor.o(($event) => !isViewMode.value && (formData.value.education = "初中及以下")),
        aT: formData.value.education === "高中/中专" ? 1 : "",
        aU: common_vendor.o(($event) => !isViewMode.value && (formData.value.education = "高中/中专")),
        aV: formData.value.education === "大学专科" ? 1 : "",
        aW: common_vendor.o(($event) => !isViewMode.value && (formData.value.education = "大学专科")),
        aX: formData.value.education === "大学本科" ? 1 : "",
        aY: common_vendor.o(($event) => !isViewMode.value && (formData.value.education = "大学本科")),
        aZ: formData.value.education === "研究生及以上" ? 1 : "",
        ba: common_vendor.o(($event) => !isViewMode.value && (formData.value.education = "研究生及以上")),
        bb: common_vendor.t(displayAreaText.value || "请选择"),
        bc: !isViewMode.value
      }, !isViewMode.value ? {
        bd: common_vendor.p({
          type: "arrow-right",
          size: "16",
          color: "#666"
        })
      } : {}, {
        be: common_vendor.o(onAreaConfirm),
        bf: common_vendor.o(($event) => tempSelectedArea.value = $event),
        bg: common_vendor.p({
          columns: areaColumns.value,
          ["column-change"]: onAreaColumnChange,
          disabled: isViewMode.value,
          ["label-key"]: "label",
          modelValue: tempSelectedArea.value
        }),
        bh: isViewMode.value,
        bi: formData.value.address,
        bj: common_vendor.o(($event) => formData.value.address = $event.detail.value),
        bk: formData.value.vitalSigns && formData.value.vitalSigns.bodyPart ? 1 : "",
        bl: common_vendor.o(($event) => !isViewMode.value && toggleVitalSign("bodyPart")),
        bm: formData.value.vitalSigns && formData.value.vitalSigns.temperature ? 1 : "",
        bn: common_vendor.o(($event) => !isViewMode.value && toggleVitalSign("temperature")),
        bo: formData.value.vitalSigns && (formData.value.vitalSigns.bodyPart || formData.value.vitalSigns.temperature)
      }, formData.value.vitalSigns && (formData.value.vitalSigns.bodyPart || formData.value.vitalSigns.temperature) ? common_vendor.e$1({
        bp: formData.value.vitalSigns && formData.value.vitalSigns.bodyPart
      }, formData.value.vitalSigns && formData.value.vitalSigns.bodyPart ? common_vendor.e$1({
        bq: common_vendor.t(temperatureSites[formData.value.temperatureSiteIndex]),
        br: !isViewMode.value
      }, !isViewMode.value ? {
        bs: common_vendor.p({
          type: "arrow-down",
          size: "14"
        })
      } : {}, {
        bt: formData.value.temperatureSiteIndex,
        bv: temperatureSites,
        bw: common_vendor.o(onTemperatureSiteChange),
        bx: isViewMode.value
      }) : {}, {
        by: formData.value.vitalSigns && formData.value.vitalSigns.temperature
      }, formData.value.vitalSigns && formData.value.vitalSigns.temperature ? {
        bz: isViewMode.value,
        bA: formData.value.tempertureValue,
        bB: common_vendor.o(($event) => formData.value.tempertureValue = $event.detail.value)
      } : {}) : {}, {
        bC: formData.value.vitalSigns && formData.value.vitalSigns.heartRate ? 1 : "",
        bD: common_vendor.o(($event) => !isViewMode.value && toggleVitalSign("heartRate")),
        bE: formData.value.vitalSigns && formData.value.vitalSigns.breathing ? 1 : "",
        bF: common_vendor.o(($event) => !isViewMode.value && toggleVitalSign("breathing")),
        bG: formData.value.vitalSigns && (formData.value.vitalSigns.heartRate || formData.value.vitalSigns.breathing)
      }, formData.value.vitalSigns && (formData.value.vitalSigns.heartRate || formData.value.vitalSigns.breathing) ? common_vendor.e$1({
        bH: formData.value.vitalSigns && formData.value.vitalSigns.heartRate
      }, formData.value.vitalSigns && formData.value.vitalSigns.heartRate ? {
        bI: isViewMode.value,
        bJ: formData.value.heartRateValue,
        bK: common_vendor.o(($event) => formData.value.heartRateValue = $event.detail.value)
      } : {}, {
        bL: formData.value.vitalSigns && formData.value.vitalSigns.breathing
      }, formData.value.vitalSigns && formData.value.vitalSigns.breathing ? {
        bM: isViewMode.value,
        bN: formData.value.breathingValue,
        bO: common_vendor.o(($event) => formData.value.breathingValue = $event.detail.value)
      } : {}) : {}, {
        bP: formData.value.vitalSigns && formData.value.vitalSigns.bloodPressure ? 1 : "",
        bQ: common_vendor.o(($event) => !isViewMode.value && toggleVitalSign("bloodPressure")),
        bR: formData.value.vitalSigns && formData.value.vitalSigns.oxygen ? 1 : "",
        bS: common_vendor.o(($event) => !isViewMode.value && toggleVitalSign("oxygen")),
        bT: formData.value.vitalSigns && (formData.value.vitalSigns.bloodPressure || formData.value.vitalSigns.oxygen)
      }, formData.value.vitalSigns && (formData.value.vitalSigns.bloodPressure || formData.value.vitalSigns.oxygen) ? common_vendor.e$1({
        bU: formData.value.vitalSigns && formData.value.vitalSigns.bloodPressure
      }, formData.value.vitalSigns && formData.value.vitalSigns.bloodPressure ? {
        bV: isViewMode.value,
        bW: formData.value.bloodPressureValue,
        bX: common_vendor.o(($event) => formData.value.bloodPressureValue = $event.detail.value)
      } : {}, {
        bY: formData.value.vitalSigns && formData.value.vitalSigns.oxygen
      }, formData.value.vitalSigns && formData.value.vitalSigns.oxygen ? {
        bZ: isViewMode.value,
        ca: formData.value.oxygenValue,
        cb: common_vendor.o(($event) => formData.value.oxygenValue = $event.detail.value)
      } : {}) : {}, {
        cc: formData.value.medicationStatus === "是" ? 1 : "",
        cd: common_vendor.o(($event) => !isViewMode.value && (formData.value.medicationStatus = "是")),
        ce: formData.value.medicationStatus === "是"
      }, formData.value.medicationStatus === "是" ? {
        cf: isViewMode.value,
        cg: formData.value.medicationDesc,
        ch: common_vendor.o(($event) => formData.value.medicationDesc = $event.detail.value)
      } : {}, {
        ci: formData.value.medicationStatus === "否" ? 1 : "",
        cj: common_vendor.o(($event) => !isViewMode.value && (formData.value.medicationStatus = "否")),
        ck: isViewMode.value,
        cl: formData.value.conditions,
        cm: common_vendor.o(($event) => formData.value.conditions = $event.detail.value),
        cn: formData.value.healthProblem === "是" ? 1 : "",
        co: common_vendor.o(($event) => !isViewMode.value && (formData.value.healthProblem = "是")),
        cp: formData.value.healthProblem === "是"
      }, formData.value.healthProblem === "是" ? {
        cq: isViewMode.value,
        cr: formData.value.problemDesc,
        cs: common_vendor.o(($event) => formData.value.problemDesc = $event.detail.value)
      } : {}, {
        ct: formData.value.healthProblem === "否" ? 1 : "",
        cv: common_vendor.o(($event) => !isViewMode.value && (formData.value.healthProblem = "否")),
        cw: formData.value.compliance === "好" ? 1 : "",
        cx: common_vendor.o(($event) => !isViewMode.value && (formData.value.compliance = "好")),
        cy: formData.value.compliance === "一般" ? 1 : "",
        cz: common_vendor.o(($event) => !isViewMode.value && (formData.value.compliance = "一般")),
        cA: formData.value.compliance === "不好" ? 1 : "",
        cB: common_vendor.o(($event) => !isViewMode.value && (formData.value.compliance = "不好")),
        cC: formData.value.diseases && formData.value.diseases.smoking ? 1 : "",
        cD: common_vendor.o(($event) => !isViewMode.value && toggleDisease("smoking")),
        cE: formData.value.diseases && formData.value.diseases.drinking ? 1 : "",
        cF: common_vendor.o(($event) => !isViewMode.value && toggleDisease("drinking")),
        cG: formData.value.diseases && formData.value.diseases.hypertension ? 1 : "",
        cH: common_vendor.o(($event) => !isViewMode.value && toggleDisease("hypertension")),
        cI: formData.value.diseases && formData.value.diseases.diabetes ? 1 : "",
        cJ: common_vendor.o(($event) => !isViewMode.value && toggleDisease("diabetes")),
        cK: formData.value.diseases && formData.value.diseases.hyperlipidemia ? 1 : "",
        cL: common_vendor.o(($event) => !isViewMode.value && toggleDisease("hyperlipidemia")),
        cM: formData.value.diseases && formData.value.diseases.chronicKidney ? 1 : "",
        cN: common_vendor.o(($event) => !isViewMode.value && toggleDisease("chronicKidney")),
        cO: formData.value.diseases && formData.value.diseases.chronicLung ? 1 : "",
        cP: common_vendor.o(($event) => !isViewMode.value && toggleDisease("chronicLung")),
        cQ: formData.value.diseases && formData.value.diseases.tumor ? 1 : "",
        cR: common_vendor.o(($event) => !isViewMode.value && toggleDisease("tumor")),
        cS: formData.value.diseases && formData.value.diseases.mainDiagnosis ? 1 : "",
        cT: common_vendor.o(($event) => !isViewMode.value && toggleDisease("mainDiagnosis")),
        cU: formData.value.diseases && formData.value.diseases.mainDiagnosis
      }, formData.value.diseases && formData.value.diseases.mainDiagnosis ? {
        cV: isViewMode.value,
        cW: formData.value.mainDiagnosisDesc,
        cX: common_vendor.o(($event) => formData.value.mainDiagnosisDesc = $event.detail.value)
      } : {}, {
        cY: formData.value.diseases && formData.value.diseases.diseaseSummary ? 1 : "",
        cZ: common_vendor.o(($event) => !isViewMode.value && toggleDisease("diseaseSummary")),
        da: formData.value.diseases && formData.value.diseases.diseaseSummary
      }, formData.value.diseases && formData.value.diseases.diseaseSummary ? {
        db: isViewMode.value,
        dc: formData.value.diseaseSummaryDesc,
        dd: common_vendor.o(($event) => formData.value.diseaseSummaryDesc = $event.detail.value)
      } : {}, {
        de: !isViewMode.value
      }, !isViewMode.value ? {
        df: common_vendor.o(submitForm)
      } : {});
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-a1fd1861"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=form.js.map
