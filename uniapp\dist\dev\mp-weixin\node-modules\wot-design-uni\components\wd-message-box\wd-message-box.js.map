{"version": 3, "file": "wd-message-box.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-message-box/wd-message-box.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1tZXNzYWdlLWJveC93ZC1tZXNzYWdlLWJveC52dWU"], "sourcesContent": ["<template>\n  <view>\n    <wd-popup\n      transition=\"zoom-in\"\n      v-model=\"messageState.show\"\n      :close-on-click-modal=\"messageState.closeOnClickModal\"\n      :lazy-render=\"messageState.lazyRender\"\n      custom-class=\"wd-message-box\"\n      @click-modal=\"toggleModal('modal')\"\n      :z-index=\"messageState.zIndex\"\n      :duration=\"200\"\n    >\n      <view :class=\"rootClass\">\n        <view :class=\"bodyClass\">\n          <view v-if=\"messageState.title\" class=\"wd-message-box__title\">\n            {{ messageState.title }}\n          </view>\n          <view class=\"wd-message-box__content\">\n            <template v-if=\"messageState.type === 'prompt'\">\n              <wd-input\n                v-model=\"messageState.inputValue\"\n                :type=\"messageState.inputType\"\n                :size=\"messageState.inputSize\"\n                :placeholder=\"messageState.inputPlaceholder\"\n                @input=\"inputValChange\"\n              />\n              <view v-if=\"messageState.showErr\" class=\"wd-message-box__input-error\">\n                {{ messageState.inputError || translate('inputNoValidate') }}\n              </view>\n            </template>\n            <slot>{{ messageState.msg }}</slot>\n          </view>\n        </view>\n        <view :class=\"`wd-message-box__actions ${messageState.showCancelButton ? 'wd-message-box__flex' : 'wd-message-box__block'}`\">\n          <wd-button v-bind=\"customCancelProps\" v-if=\"messageState.showCancelButton\" @click=\"toggleModal('cancel')\">\n            {{ messageState.cancelButtonText || translate('cancel') }}\n          </wd-button>\n          <wd-button v-bind=\"customConfirmProps\" @click=\"toggleModal('confirm')\">\n            {{ messageState.confirmButtonText || translate('confirm') }}\n          </wd-button>\n        </view>\n      </view>\n    </wd-popup>\n  </view>\n</template>\n<script lang=\"ts\">\nexport default {\n  name: 'wd-message-box',\n  options: {\n    virtualHost: true,\n    addGlobalClass: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport wdPopup from '../wd-popup/wd-popup.vue'\nimport wdButton from '../wd-button/wd-button.vue'\nimport wdInput from '../wd-input/wd-input.vue'\nimport { computed, inject, reactive, ref, watch } from 'vue'\nimport { messageBoxProps, type MessageOptionsWithCallBack, type MessageResult } from './types'\nimport { defaultOptions, getMessageDefaultOptionKey } from '.'\nimport { deepAssign, isDef, isFunction, isUndefined, omitBy } from '../common/util'\nimport { useTranslate } from '../composables/useTranslate'\nimport type { ButtonProps } from '../wd-button/types'\n\nconst props = defineProps(messageBoxProps)\n\nconst { translate } = useTranslate('message-box')\n\nconst rootClass = computed(() => {\n  return `wd-message-box__container ${props.customClass}`\n})\n\nconst bodyClass = computed(() => {\n  return `wd-message-box__body ${!messageState.title ? 'is-no-title' : ''} ${messageState.type === 'prompt' ? 'is-prompt' : ''}`\n})\n\nconst messageOptionKey = getMessageDefaultOptionKey(props.selector)\nconst messageOption = inject(messageOptionKey, ref<MessageOptionsWithCallBack>(defaultOptions)) // message选项\n\nconst messageState = reactive<MessageOptionsWithCallBack>({\n  msg: '', // 消息内容\n  show: false, // 是否显示弹框\n  title: '', // 标题\n  showCancelButton: false, // 是否展示取消按钮\n  closeOnClickModal: true, // 是否支持点击蒙层关闭\n  confirmButtonText: '', // 确定按钮文案\n  cancelButtonText: '', // 取消按钮文案\n  type: 'alert', // 弹框类型\n  inputType: 'text', // 输入框类型\n  inputValue: '', // 输入框初始值\n  inputPlaceholder: '', // 输入框placeholder\n  inputError: '', // 输入框错误提示文案\n  showErr: false, // 是否显示错误提示\n  zIndex: 99, // 弹窗层级\n  lazyRender: true // 弹层内容懒渲染\n})\n\n/**\n * 确认按钮属性\n */\nconst customConfirmProps = computed(() => {\n  const buttonProps: Partial<ButtonProps> = deepAssign(\n    {\n      block: true\n    },\n    isDef(messageState.confirmButtonProps) ? omitBy(messageState.confirmButtonProps, isUndefined) : {}\n  )\n  buttonProps.customClass = `${buttonProps.customClass || ''} wd-message-box__actions-btn`\n  return buttonProps\n})\n\n/**\n * 取消按钮属性\n */\nconst customCancelProps = computed(() => {\n  const buttonProps: Partial<ButtonProps> = deepAssign(\n    {\n      block: true,\n      type: 'info'\n    },\n    isDef(messageState.cancelButtonProps) ? omitBy(messageState.cancelButtonProps, isUndefined) : {}\n  )\n  buttonProps.customClass = `${buttonProps.customClass || ''} wd-message-box__actions-btn`\n  return buttonProps\n})\n\n// 监听options变化展示\nwatch(\n  () => messageOption.value,\n  (newVal: MessageOptionsWithCallBack) => {\n    reset(newVal)\n  },\n  {\n    deep: true,\n    immediate: true\n  }\n)\n\nwatch(\n  () => messageState.show,\n  (newValue) => {\n    resetErr(!!newValue)\n  },\n  {\n    deep: true,\n    immediate: true\n  }\n)\n\n/**\n * 点击操作\n * @param action\n */\nfunction toggleModal(action: 'confirm' | 'cancel' | 'modal') {\n  if (action === 'modal' && !messageState.closeOnClickModal) {\n    return\n  }\n  if (messageState.type === 'prompt' && action === 'confirm' && !validate()) {\n    return\n  }\n  switch (action) {\n    case 'confirm':\n      if (messageState.beforeConfirm) {\n        messageState.beforeConfirm({\n          resolve: (isPass) => {\n            if (isPass) {\n              handleConfirm({\n                action: action,\n                value: messageState.inputValue\n              })\n            }\n          }\n        })\n      } else {\n        handleConfirm({\n          action: action,\n          value: messageState.inputValue\n        })\n      }\n      break\n    case 'cancel':\n      handleCancel({\n        action: action\n      })\n      break\n    default:\n      handleCancel({\n        action: 'modal'\n      })\n      break\n  }\n}\n\n/**\n * 确认回调\n * @param result\n */\nfunction handleConfirm(result: MessageResult) {\n  messageState.show = false\n  if (isFunction(messageState.success)) {\n    messageState.success(result)\n  }\n}\n\n/**\n * 取消回调\n * @param result\n */\nfunction handleCancel(result: MessageResult) {\n  messageState.show = false\n  if (isFunction(messageState.fail)) {\n    messageState.fail(result)\n  }\n}\n\n/**\n * 如果存在校验规则行为，则进行判断校验是否通过规则。默认不存在校验直接铜鼓。\n */\nfunction validate() {\n  if (messageState.inputPattern && !messageState.inputPattern.test(String(messageState.inputValue))) {\n    messageState.showErr = true\n    return false\n  }\n  if (typeof messageState.inputValidate === 'function') {\n    const validateResult = messageState.inputValidate(messageState.inputValue!)\n    if (!validateResult) {\n      messageState.showErr = true\n      return false\n    }\n  }\n  messageState.showErr = false\n  return true\n}\n\n/**\n * @description show关闭时，销毁错误提示\n * @param val\n */\nfunction resetErr(val: boolean) {\n  if (val === false) {\n    messageState.showErr = false\n  }\n}\nfunction inputValChange({ value }: { value: string | number }) {\n  if (value === '') {\n    messageState.showErr = false\n    return\n  }\n  messageState.inputValue = value\n}\n\n/**\n * 重置message选项值\n * @param option message选项值\n */\nfunction reset(option: MessageOptionsWithCallBack) {\n  if (option) {\n    messageState.title = isDef(option.title) ? option.title : ''\n    messageState.showCancelButton = isDef(option.showCancelButton) ? option.showCancelButton : false\n    messageState.show = option.show\n    messageState.closeOnClickModal = option.closeOnClickModal\n    messageState.confirmButtonText = option.confirmButtonText\n    messageState.cancelButtonText = option.cancelButtonText\n    messageState.msg = option.msg\n    messageState.type = option.type\n    messageState.inputType = option.inputType\n    messageState.inputSize = option.inputSize\n    messageState.inputValue = option.inputValue\n    messageState.inputPlaceholder = option.inputPlaceholder\n    messageState.inputPattern = option.inputPattern!\n    messageState.inputValidate = option.inputValidate\n    messageState.success = option.success\n    messageState.fail = option.fail\n    messageState.beforeConfirm = option.beforeConfirm\n    messageState.inputError = option.inputError\n    messageState.showErr = option.showErr\n    messageState.zIndex = option.zIndex\n    messageState.lazyRender = option.lazyRender\n    messageState.confirmButtonProps = option.confirmButtonProps\n    messageState.cancelButtonProps = option.cancelButtonProps\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-message-box/wd-message-box.vue'\nwx.createComponent(Component)"], "names": ["useTranslate", "computed", "getMessageDefaultOptionKey", "inject", "ref", "defaultOptions", "reactive", "deepAssign", "isDef", "omitBy", "isUndefined", "watch", "isFunction"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAyDA,MAAA,UAAoB,MAAA;AACpB,MAAA,WAAqB,MAAA;AACrB,MAAA,UAAoB,MAAA;AAbpB,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,EAAA;AAEpB;;;;AAcA,UAAM,QAAQ;AAEd,UAAM,EAAE,UAAA,IAAcA,cAAA,aAAa,aAAa;AAE1C,UAAA,YAAYC,cAAAA,SAAS,MAAM;AACxB,aAAA,6BAA6B,MAAM,WAAW;AAAA,IAAA,CACtD;AAEK,UAAA,YAAYA,cAAAA,SAAS,MAAM;AACxB,aAAA,wBAAwB,CAAC,aAAa,QAAQ,gBAAgB,EAAE,IAAI,aAAa,SAAS,WAAW,cAAc,EAAE;AAAA,IAAA,CAC7H;AAEK,UAAA,mBAAmBC,cAAAA,2BAA2B,MAAM,QAAQ;AAClE,UAAM,gBAAgBC,cAAA,OAAO,kBAAkBC,cAAA,IAAgCC,cAAc,gBAAA,CAAC;AAE9F,UAAM,eAAeC,cAAAA,SAAqC;AAAA,MACxD,KAAK;AAAA;AAAA,MACL,MAAM;AAAA;AAAA,MACN,OAAO;AAAA;AAAA,MACP,kBAAkB;AAAA;AAAA,MAClB,mBAAmB;AAAA;AAAA,MACnB,mBAAmB;AAAA;AAAA,MACnB,kBAAkB;AAAA;AAAA,MAClB,MAAM;AAAA;AAAA,MACN,WAAW;AAAA;AAAA,MACX,YAAY;AAAA;AAAA,MACZ,kBAAkB;AAAA;AAAA,MAClB,YAAY;AAAA;AAAA,MACZ,SAAS;AAAA;AAAA,MACT,QAAQ;AAAA;AAAA,MACR,YAAY;AAAA;AAAA,IAAA,CACb;AAKK,UAAA,qBAAqBL,cAAAA,SAAS,MAAM;AACxC,YAAM,cAAoCM,cAAA;AAAA,QACxC;AAAA,UACE,OAAO;AAAA,QACT;AAAA,QACAC,cAAA,MAAM,aAAa,kBAAkB,IAAIC,qBAAO,aAAa,oBAAoBC,cAAAA,WAAW,IAAI,CAAA;AAAA,MAClG;AACA,kBAAY,cAAc,GAAG,YAAY,eAAe,EAAE;AACnD,aAAA;AAAA,IAAA,CACR;AAKK,UAAA,oBAAoBT,cAAAA,SAAS,MAAM;AACvC,YAAM,cAAoCM,cAAA;AAAA,QACxC;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACAC,cAAA,MAAM,aAAa,iBAAiB,IAAIC,qBAAO,aAAa,mBAAmBC,cAAAA,WAAW,IAAI,CAAA;AAAA,MAChG;AACA,kBAAY,cAAc,GAAG,YAAY,eAAe,EAAE;AACnD,aAAA;AAAA,IAAA,CACR;AAGDC,kBAAA;AAAA,MACE,MAAM,cAAc;AAAA,MACpB,CAAC,WAAuC;AACtC,cAAM,MAAM;AAAA,MACd;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,WAAW;AAAA,MAAA;AAAA,IAEf;AAEAA,kBAAA;AAAA,MACE,MAAM,aAAa;AAAA,MACnB,CAAC,aAAa;AACH,iBAAA,CAAC,CAAC,QAAQ;AAAA,MACrB;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,WAAW;AAAA,MAAA;AAAA,IAEf;AAMA,aAAS,YAAY,QAAwC;AAC3D,UAAI,WAAW,WAAW,CAAC,aAAa,mBAAmB;AACzD;AAAA,MAAA;AAEF,UAAI,aAAa,SAAS,YAAY,WAAW,aAAa,CAAC,YAAY;AACzE;AAAA,MAAA;AAEF,cAAQ,QAAQ;AAAA,QACd,KAAK;AACH,cAAI,aAAa,eAAe;AAC9B,yBAAa,cAAc;AAAA,cACzB,SAAS,CAAC,WAAW;AACnB,oBAAI,QAAQ;AACI,gCAAA;AAAA,oBACZ;AAAA,oBACA,OAAO,aAAa;AAAA,kBAAA,CACrB;AAAA,gBAAA;AAAA,cACH;AAAA,YACF,CACD;AAAA,UAAA,OACI;AACS,0BAAA;AAAA,cACZ;AAAA,cACA,OAAO,aAAa;AAAA,YAAA,CACrB;AAAA,UAAA;AAEH;AAAA,QACF,KAAK;AACU,uBAAA;AAAA,YACX;AAAA,UAAA,CACD;AACD;AAAA,QACF;AACe,uBAAA;AAAA,YACX,QAAQ;AAAA,UAAA,CACT;AACD;AAAA,MAAA;AAAA,IACJ;AAOF,aAAS,cAAc,QAAuB;AAC5C,mBAAa,OAAO;AAChB,UAAAC,cAAA,WAAW,aAAa,OAAO,GAAG;AACpC,qBAAa,QAAQ,MAAM;AAAA,MAAA;AAAA,IAC7B;AAOF,aAAS,aAAa,QAAuB;AAC3C,mBAAa,OAAO;AAChB,UAAAA,cAAA,WAAW,aAAa,IAAI,GAAG;AACjC,qBAAa,KAAK,MAAM;AAAA,MAAA;AAAA,IAC1B;AAMF,aAAS,WAAW;AACd,UAAA,aAAa,gBAAgB,CAAC,aAAa,aAAa,KAAK,OAAO,aAAa,UAAU,CAAC,GAAG;AACjG,qBAAa,UAAU;AAChB,eAAA;AAAA,MAAA;AAEL,UAAA,OAAO,aAAa,kBAAkB,YAAY;AACpD,cAAM,iBAAiB,aAAa,cAAc,aAAa,UAAW;AAC1E,YAAI,CAAC,gBAAgB;AACnB,uBAAa,UAAU;AAChB,iBAAA;AAAA,QAAA;AAAA,MACT;AAEF,mBAAa,UAAU;AAChB,aAAA;AAAA,IAAA;AAOT,aAAS,SAAS,KAAc;AAC9B,UAAI,QAAQ,OAAO;AACjB,qBAAa,UAAU;AAAA,MAAA;AAAA,IACzB;AAEO,aAAA,eAAe,EAAE,SAAqC;AAC7D,UAAI,UAAU,IAAI;AAChB,qBAAa,UAAU;AACvB;AAAA,MAAA;AAEF,mBAAa,aAAa;AAAA,IAAA;AAO5B,aAAS,MAAM,QAAoC;AACjD,UAAI,QAAQ;AACV,qBAAa,QAAQJ,oBAAM,OAAO,KAAK,IAAI,OAAO,QAAQ;AAC1D,qBAAa,mBAAmBA,oBAAM,OAAO,gBAAgB,IAAI,OAAO,mBAAmB;AAC3F,qBAAa,OAAO,OAAO;AAC3B,qBAAa,oBAAoB,OAAO;AACxC,qBAAa,oBAAoB,OAAO;AACxC,qBAAa,mBAAmB,OAAO;AACvC,qBAAa,MAAM,OAAO;AAC1B,qBAAa,OAAO,OAAO;AAC3B,qBAAa,YAAY,OAAO;AAChC,qBAAa,YAAY,OAAO;AAChC,qBAAa,aAAa,OAAO;AACjC,qBAAa,mBAAmB,OAAO;AACvC,qBAAa,eAAe,OAAO;AACnC,qBAAa,gBAAgB,OAAO;AACpC,qBAAa,UAAU,OAAO;AAC9B,qBAAa,OAAO,OAAO;AAC3B,qBAAa,gBAAgB,OAAO;AACpC,qBAAa,aAAa,OAAO;AACjC,qBAAa,UAAU,OAAO;AAC9B,qBAAa,SAAS,OAAO;AAC7B,qBAAa,aAAa,OAAO;AACjC,qBAAa,qBAAqB,OAAO;AACzC,qBAAa,oBAAoB,OAAO;AAAA,MAAA;AAAA,IAC1C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1RF,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}