"use strict";
const common_vendor = require("../../common/vendor.js");
require("../../plugin/uni-mini-router/router/index.js");
const plugin_uniMiniRouter_core_index = require("../../plugin/uni-mini-router/core/index.js");
const common_work = require("../../common/work.js");
if (!Array) {
  const _easycom_SelectUser2 = common_vendor.resolveComponent("SelectUser");
  const _easycom_SelectDept2 = common_vendor.resolveComponent("SelectDept");
  const _easycom_ProgressMap2 = common_vendor.resolveComponent("ProgressMap");
  const _easycom_uni_calendar2 = common_vendor.resolveComponent("uni-calendar");
  const _easycom_wd_img2 = common_vendor.resolveComponent("wd-img");
  const _easycom_ImgPreview2 = common_vendor.resolveComponent("ImgPreview");
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _easycom_wd_cell2 = common_vendor.resolveComponent("wd-cell");
  const _easycom_wd_cell_group2 = common_vendor.resolveComponent("wd-cell-group");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_SelectUser2 + _easycom_SelectDept2 + _easycom_ProgressMap2 + _easycom_uni_calendar2 + _easycom_wd_img2 + _easycom_ImgPreview2 + _easycom_wd_button2 + _easycom_wd_cell2 + _easycom_wd_cell_group2 + _easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_SelectUser = () => "../../components/SelectUser/SelectUser.js";
const _easycom_SelectDept = () => "../../components/SelectDept/SelectDept.js";
const _easycom_ProgressMap = () => "../../components/ProgressMap/ProgressMap.js";
const _easycom_uni_calendar = () => "../../uni_modules/uni-calendar/components/uni-calendar/uni-calendar.js";
const _easycom_wd_img = () => "../../node-modules/wot-design-uni/components/wd-img/wd-img.js";
const _easycom_ImgPreview = () => "../../components/ImgPreview/ImgPreview.js";
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
const _easycom_wd_cell = () => "../../node-modules/wot-design-uni/components/wd-cell/wd-cell.js";
const _easycom_wd_cell_group = () => "../../node-modules/wot-design-uni/components/wd-cell-group/wd-cell-group.js";
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  (_easycom_SelectUser + _easycom_SelectDept + _easycom_ProgressMap + _easycom_uni_calendar + _easycom_wd_img + _easycom_ImgPreview + _easycom_wd_button + Grid + _easycom_wd_cell + _easycom_wd_cell_group + _easycom_PageLayout)();
}
const Grid = () => "../../components/Grid/Grid.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "demo",
  setup(__props) {
    const toast = common_vendor.useToast();
    const user = common_vendor.ref("");
    const dept = common_vendor.ref("");
    const message = common_vendor.useMessage();
    const { showNotify } = common_vendor.useNotify();
    const router = plugin_uniMiniRouter_core_index.useRouter();
    const selected = common_vendor.ref([]);
    const gridData = common_vendor.ref([]);
    common_work.us.data.forEach((item, index) => {
      if (index < 9) {
        gridData.value.push({ text: item.title, img: item.icon, itemKey: index });
      }
    });
    const imgPreview = common_vendor.ref({
      show: false,
      urls: [
        "https://jeecgos.oss-cn-beijing.aliyuncs.com/files/site/projectCase/mini/banner/10bdc1.jpg"
      ]
    });
    const change = () => {
    };
    const monthChange = () => {
    };
    const proDataSource = [
      {
        activeStep: true,
        data: [
          { label: "流程节点：start" },
          { label: "申请人：神经蛙02" },
          { label: "申请时间：2023-12-06 16:15:14" },
          { label: "已完成" }
        ]
      },
      {
        activeStep: false,
        data: [
          { label: "流程节点：填写" },
          { label: "申请人：神经蛙01" },
          { label: "申请时间：2023-12-06 16:15:14" }
        ]
      },
      {
        activeStep: false,
        data: [
          { label: "流程节点：填写" },
          { label: "申请人：神经蛙03" },
          { label: "申请时间：2023-12-06 16:15:14" }
        ]
      }
    ];
    const dataSource = common_vendor.ref([
      { title: "万年历组件" },
      { title: "图片预览" }
      // {
      //   group: [
      //     { title: '树组件', path: '/pages/demo/tree' },
      //     { title: '通讯录', path: '/pages/demo/indexBar' },
      //     { title: '单选多选', path: '/pages/demo/selectPicker' },
      //     { title: '表单', path: '/pages/demo/form' },
      //   ],
      // },
    ]);
    const handleSkip = (path) => {
      router.push({ path });
    };
    const handleToast = (value) => {
      switch (value) {
        case 0:
          toast.info({ msg: "常规提示信息", duration: 1e4 });
          break;
        case 1:
          toast.warning({ msg: "提示信息", duration: 1e4 });
          break;
        case 2:
          toast.success({ msg: "操作成功", duration: 1e4 });
          break;
        case 3:
          toast.error({ msg: "手机验证码输入错误，请重新输入", duration: 1e4 });
          break;
        case 4:
          toast.show({ msg: "手机验证码输入错误，请重新输入", duration: 1e4 });
          break;
      }
    };
    const handleConfirm = (params) => {
      message.confirm({
        msg: "提示文案",
        title: "标题"
      }).then(() => {
        showNotify({ type: "success", message: "点击了确认按钮" });
      }).catch(() => {
        showNotify({ type: "warning", message: "点击了取消按钮" });
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(($event) => user.value = $event),
        b: common_vendor.p({
          label: "用户：",
          required: true,
          modelValue: user.value
        }),
        c: common_vendor.o(($event) => dept.value = $event),
        d: common_vendor.p({
          label: "部门：",
          required: true,
          modelValue: dept.value
        }),
        e: common_vendor.p({
          title: "流程历史跟踪",
          dataSource: proDataSource
        }),
        f: common_vendor.f(dataSource.value, (item, index, i0) => {
          return common_vendor.e$1({
            a: index === 0
          }, index === 0 ? {
            b: common_vendor.o(change),
            c: common_vendor.o(monthChange),
            d: "ef01f733-5-" + i0 + ",ef01f733-1",
            e: common_vendor.p({
              showMonth: true,
              selected: selected.value
            })
          } : common_vendor.e$1({
            f: common_vendor.t(item.title),
            g: ["图片预览"].includes(item.title)
          }, ["图片预览"].includes(item.title) ? common_vendor.e$1({
            h: common_vendor.o(() => imgPreview.value.show = true),
            i: "ef01f733-6-" + i0 + ",ef01f733-1",
            j: common_vendor.p({
              ["custom-class"]: "imgView",
              width: 220,
              height: 120,
              src: "https://jeecgos.oss-cn-beijing.aliyuncs.com/files/site/projectCase/mini/banner/10bdc1.jpg"
            }),
            k: imgPreview.value.show
          }, imgPreview.value.show ? {
            l: common_vendor.o(() => imgPreview.value.show = false),
            m: "ef01f733-7-" + i0 + ",ef01f733-1",
            n: common_vendor.p({
              urls: imgPreview.value.urls
            })
          } : {}) : {
            o: common_vendor.o(($event) => handleSkip(item.path)),
            p: "ef01f733-8-" + i0 + ",ef01f733-1"
          }));
        }),
        g: common_vendor.o(($event) => handleSkip("/pages/demo/tree")),
        h: common_vendor.o(($event) => handleSkip("/pages/demo/indexBar")),
        i: common_vendor.o(($event) => handleSkip("/pages/demo/selectPicker")),
        j: common_vendor.o(($event) => handleSkip("/pages/demo/form")),
        k: common_vendor.o((item) => common_vendor.unref(toast).info(`点击了${item.text}`)),
        l: common_vendor.o(($event) => gridData.value = $event),
        m: common_vendor.p({
          column: 3,
          modelValue: gridData.value
        }),
        n: common_vendor.p({
          title: "组织管理",
          ["is-link"]: true,
          icon: "computer"
        }),
        o: common_vendor.p({
          title: "安全设置",
          ["is-link"]: true,
          icon: "setting"
        }),
        p: common_vendor.p({
          title: "个人设置",
          ["is-link"]: true,
          icon: "user"
        }),
        q: common_vendor.p({
          title: "退出登录",
          ["is-link"]: true,
          icon: "login"
        }),
        r: common_vendor.p({
          border: true,
          clickable: true,
          ["custom-class"]: "shadow-warp"
        }),
        s: common_vendor.o(($event) => handleToast(0)),
        t: common_vendor.p({
          ["custom-class"]: "mb-2 info"
        }),
        v: common_vendor.o(($event) => handleToast(1)),
        w: common_vendor.p({
          ["custom-class"]: "mb-2 warning"
        }),
        x: common_vendor.o(($event) => handleToast(2)),
        y: common_vendor.p({
          ["custom-class"]: "mb-2 success"
        }),
        z: common_vendor.o(($event) => handleToast(3)),
        A: common_vendor.p({
          ["custom-class"]: "mb-2 error"
        }),
        B: common_vendor.o(($event) => handleToast(4)),
        C: common_vendor.p({
          ["custom-class"]: "mb-2 basic"
        }),
        D: common_vendor.o(handleConfirm),
        E: common_vendor.p({
          backRouteName: "index",
          navTitle: "组件示例",
          routeMethod: "pushTab"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-ef01f733"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=demo.js.map
