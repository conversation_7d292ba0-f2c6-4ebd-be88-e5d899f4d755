{"version": 3, "file": "online-checkbox.js", "sources": ["../../../../../../src/components/online/view/online-checkbox.vue", "../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvY29tcG9uZW50cy9vbmxpbmUvdmlldy9vbmxpbmUtY2hlY2tib3gudnVl"], "sourcesContent": ["<template>\r\n\t<wd-select-picker\r\n\t\t\t:label-width=\"labelWidth\"\r\n\t\t\t:label=\"label\"\r\n\t\t\tv-model=\"selected\"\r\n\t\t\tfilterable\r\n\t\t\tclearable\r\n\t\t\t:columns=\"options\"\r\n\t\t\t:disabled=\"disabled\"\r\n\t\t\tplaceholder=\"请选择\"\r\n\t\t\t@change=\"handleChange\"\r\n\t></wd-select-picker>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, watch, onMounted } from 'vue'\r\nimport { isArray, isString } from 'lodash'\r\nimport {http} from \"@/utils/http\"; // 假设使用 lodash 来判断类型\r\n\r\n// 定义 props\r\nconst props = defineProps({\r\n\tdict: {\r\n\t\ttype: [Array, String],\r\n\t\tdefault: () => [],\r\n\t\trequired: true,\r\n\t},\r\n\tlabel: {\r\n\t\ttype: String,\r\n\t\tdefault: '',\r\n\t\trequired: false,\r\n\t},\r\n\tlabelWidth: {\r\n\t\ttype: String,\r\n\t\tdefault: '80px',\r\n\t\trequired: false,\r\n\t},\r\n\tname: {\r\n\t\ttype: String,\r\n\t\tdefault: '',\r\n\t\trequired: false,\r\n\t},\r\n\tdictStr: {\r\n\t\ttype: String,\r\n\t\tdefault: '',\r\n\t\trequired: false,\r\n\t},\r\n\ttype: {\r\n\t\ttype: String,\r\n\t\tdefault: '',\r\n\t\trequired: false,\r\n\t},\r\n\tvalue: {\r\n\t\ttype: [Array, String],\r\n\t\trequired: false,\r\n\t},\r\n\tdisabled: {\r\n\t\ttype: Boolean,\r\n\t\tdefault: false,\r\n\t\trequired: false,\r\n\t},\r\n})\r\n\r\n// 定义 emits\r\nconst emit = defineEmits(['input', 'change', 'update:value'])\r\n\r\n// 定义响应式数据\r\nconst selected = ref([]);\r\nconst options = ref([]);\r\n\r\n// 初始化选项\r\nconst initSelections = async () => {\r\n\toptions.value = []\r\n\tif (props.type === 'sel_search' && props.dictStr) {\r\n\t\tlet temp = props.dictStr\r\n\t\tif (temp.indexOf(' ') > 0) {\r\n\t\t\ttemp = encodeURI(props.dictStr)\r\n\t\t}\r\n\t\ttry {\r\n\t\t\tconst res = await http.get('/sys/dict/getDictItems/' + temp)\r\n\t\t\tif (res.success) {\r\n\t\t\t\toptions.value = res.result\r\n\t\t\t}\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('请求数据出错:', error)\r\n\t\t}\r\n\t}\r\n\telse {\r\n\t\tif (!props.dict || props.dict.length === 0) {\r\n\t\t\treturn\r\n\t\t}\r\n\t\tif (isString(props.dict)) {\r\n\t\t\ttry {\r\n\t\t\t\tconst res = await http.get('/sys/dict/getDictItems/' + props.dict)\r\n\t\t\t\tif (res.success) {\r\n\t\t\t\t\toptions.value = res.result\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('请求数据出错:', error)\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tprops.dict.forEach((item) => {\r\n\t\t\t\toptions.value.push(item)\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n\tconsole.log(\"options.value \",options.value )\r\n}\r\n\r\n// 选择器改变事件处理函数\r\nconst handleChange = (e) => {\r\n\tlet value = \"\";\r\n\tif (selected.value && isArray(selected.value)) {\r\n\t\tvalue = selected.value.join(',')\r\n\t}\r\n\temit('update:value', value);\r\n\temit('change', value);\r\n}\r\n\r\n// 监听 dict 和 value 的变化\r\nwatch(() => props.dict, () => {\r\n\tinitSelections();\r\n});\r\n// 监听 value 的变化\r\nwatch(\r\n\t\t() => props.value,\r\n\t\t(val) => {\r\n\t\t\tselected.value = !val? [] : val.split(',');\r\n\t\t},\r\n\t\t{ immediate: true },\r\n)\r\n\r\n// 组件挂载时初始化选项\r\nonMounted(() => {\r\n\tinitSelections()\r\n})\r\n</script>\r\n\r\n<style></style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/components/online/view/online-checkbox.vue'\nwx.createComponent(Component)"], "names": ["ref", "http", "isString", "isArray", "watch", "onMounted", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBA,UAAM,QAAQ;AA2Cd,UAAM,OAAO;AAGb,UAAM,WAAWA,cAAAA,IAAI,CAAA,CAAE;AACvB,UAAM,UAAUA,cAAAA,IAAI,CAAA,CAAE;AAGtB,UAAM,iBAAiB,MAAY;AAClC,cAAQ,QAAQ,CAAE;AAClB,UAAI,MAAM,SAAS,gBAAgB,MAAM,SAAS;AACjD,YAAI,OAAO,MAAM;AACjB,YAAI,KAAK,QAAQ,GAAG,IAAI,GAAG;AAC1B,iBAAO,UAAU,MAAM,OAAO;AAAA,QAC9B;AACD,YAAI;AACH,gBAAM,MAAM,MAAMC,WAAAA,KAAK,IAAI,4BAA4B,IAAI;AAC3D,cAAI,IAAI,SAAS;AAChB,oBAAQ,QAAQ,IAAI;AAAA,UACpB;AAAA,QACD,SAAQ,OAAO;AACf,kBAAQ,MAAM,WAAW,KAAK;AAAA,QAC9B;AAAA,MACD,OACI;AACJ,YAAI,CAAC,MAAM,QAAQ,MAAM,KAAK,WAAW,GAAG;AAC3C;AAAA,QACA;AACD,YAAIC,cAAQ,cAAA,SAAC,MAAM,IAAI,GAAG;AACzB,cAAI;AACH,kBAAM,MAAM,MAAMD,WAAI,KAAC,IAAI,4BAA4B,MAAM,IAAI;AACjE,gBAAI,IAAI,SAAS;AAChB,sBAAQ,QAAQ,IAAI;AAAA,YACpB;AAAA,UACD,SAAQ,OAAO;AACf,oBAAQ,MAAM,WAAW,KAAK;AAAA,UAC9B;AAAA,QACJ,OAAS;AACN,gBAAM,KAAK,QAAQ,CAAC,SAAS;AAC5B,oBAAQ,MAAM,KAAK,IAAI;AAAA,UAC3B,CAAI;AAAA,QACD;AAAA,MACD;AACD,cAAQ,IAAI,kBAAiB,QAAQ,KAAO;AAAA,IAC7C;AAGA,UAAM,eAAe,CAAC,MAAM;AAC3B,UAAI,QAAQ;AACZ,UAAI,SAAS,SAASE,cAAO,cAAA,QAAC,SAAS,KAAK,GAAG;AAC9C,gBAAQ,SAAS,MAAM,KAAK,GAAG;AAAA,MAC/B;AACD,WAAK,gBAAgB,KAAK;AAC1B,WAAK,UAAU,KAAK;AAAA,IACrB;AAGAC,kBAAAA,MAAM,MAAM,MAAM,MAAM,MAAM;AAC7B;IACD,CAAC;AAEDA,kBAAK;AAAA,MACH,MAAM,MAAM;AAAA,MACZ,CAAC,QAAQ;AACR,iBAAS,QAAQ,CAAC,MAAK,CAAE,IAAG,IAAI,MAAM,GAAG;AAAA,MACzC;AAAA,MACD,EAAE,WAAW,KAAM;AAAA,IACrB;AAGAC,kBAAAA,UAAU,MAAM;AACf,qBAAgB;AAAA,IACjB,CAAC;;;;;;;;;;;;;;;;;;;ACrID,GAAG,gBAAgBC,SAAS;"}