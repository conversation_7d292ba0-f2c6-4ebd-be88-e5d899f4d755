"use strict";
const common_vendor = require("../../../common/vendor.js");
const plugin_uniMiniRouter_symbols_index = require("../symbols/index.js");
function useRouter() {
  const router = common_vendor.inject(plugin_uniMiniRouter_symbols_index.routerKey);
  if (router) {
    return router;
  } else {
    throw new Error('useRouter 只可以在 Vue 上下文中使用，请确保你已经正确地注册了 "uni-mini-router" 并且当前正处于 Vue 上下文中');
  }
}
exports.useRouter = useRouter;
//# sourceMappingURL=index.js.map
