"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
const utils_http = require("../../utils/http.js");
require("../../plugin/uni-mini-router/router/index.js");
const plugin_uniMiniRouter_core_index = require("../../plugin/uni-mini-router/core/index.js");
const store_user = require("../../store/user.js");
if (!Array) {
  const _easycom_wd_input2 = common_vendor.resolveComponent("wd-input");
  const _easycom_wd_cell2 = common_vendor.resolveComponent("wd-cell");
  const _easycom_wd_select_picker2 = common_vendor.resolveComponent("wd-select-picker");
  const _easycom_wd_cell_group2 = common_vendor.resolveComponent("wd-cell-group");
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _easycom_wd_form2 = common_vendor.resolveComponent("wd-form");
  const _easycom_PageLayout2 = common_vendor.resolveComponent("PageLayout");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_input2 + _easycom_wd_cell2 + _easycom_wd_select_picker2 + _easycom_wd_cell_group2 + _easycom_wd_button2 + _easycom_wd_form2 + _easycom_PageLayout2 + _component_layout_default_uni)();
}
const _easycom_wd_input = () => "../../node-modules/wot-design-uni/components/wd-input/wd-input.js";
const _easycom_wd_cell = () => "../../node-modules/wot-design-uni/components/wd-cell/wd-cell.js";
const _easycom_wd_select_picker = () => "../../node-modules/wot-design-uni/components/wd-select-picker/wd-select-picker.js";
const _easycom_wd_cell_group = () => "../../node-modules/wot-design-uni/components/wd-cell-group/wd-cell-group.js";
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
const _easycom_wd_form = () => "../../node-modules/wot-design-uni/components/wd-form/wd-form.js";
const _easycom_PageLayout = () => "../../components/PageLayout/PageLayout.js";
if (!Math) {
  (_easycom_wd_input + avatar + _easycom_wd_cell + _easycom_wd_select_picker + _easycom_wd_cell_group + _easycom_wd_button + _easycom_wd_form + _easycom_PageLayout)();
}
const avatar = () => "./components/avatar.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "chatList",
  options: {
    styleIsolation: "shared"
  }
}), {
  __name: "userEdit",
  setup(__props) {
    const toast = common_vendor.useToast();
    const router = plugin_uniMiniRouter_core_index.useRouter();
    const userStore = store_user.useUserStore();
    const columns = [
      { label: "男", value: 1 },
      { label: "女", value: 2 }
    ];
    const model = common_vendor.reactive({
      username: userStore.userInfo.username,
      realname: userStore.userInfo.realname,
      avatar: userStore.userInfo.avatar,
      sex: userStore.userInfo.sex,
      phone: userStore.userInfo.phone,
      // email: userStore.userInfo.email,
      caseNumber: userStore.userInfo.caseNumber
    });
    const avatarFileName = common_vendor.ref("");
    const rules = {
      realname: (value) => {
        var _a;
        if ((_a = value == null ? void 0 : value.trim()) == null ? void 0 : _a.length) {
          return true;
        } else {
          toast.warning("请输入名称");
          return false;
        }
      },
      phone: (value) => {
        var _a;
        if ((_a = value == null ? void 0 : value.trim()) == null ? void 0 : _a.length) {
          if (/^1[3-9]\d{9}$/.test(value)) {
            return true;
          } else {
            toast.warning("请输入正确的手机号");
            return false;
          }
        } else {
          toast.warning("请输入手机号");
          return false;
        }
      }
    };
    const form = common_vendor.ref();
    function handleSubmit() {
      form.value.validate().then(({ valid, errors }) => {
        if (valid) {
          if (!model.avatar) {
            toast.warning("上传头像");
            return;
          }
          const data = __spreadProps(__spreadValues({}, model), { id: userStore.userInfo.userid });
          delete data.username;
          if (avatarFileName.value) {
            data.avatar = avatarFileName.value;
          }
          common_vendor.index.showLoading();
          utils_http.http.post("/sys/user/login/setting/userEdit", __spreadValues({}, data)).then((res) => {
            common_vendor.index.hideLoading();
            if (res.success) {
              toast.success("修改成功~");
              setTimeout(() => {
                userStore.editUserInfo(__spreadValues({}, data));
                router.replaceAll({ name: "people" });
              }, 1e3);
            } else {
              toast.warning(res.message);
            }
          }).catch(() => {
            toast.error("提交失败");
            common_vendor.index.hideLoading();
          });
        }
      }).catch((error) => {
        console.log(error, "error");
      });
    }
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.o(($event) => model.username = $event),
        b: common_vendor.p({
          label: "用户名",
          prop: "username",
          clearable: true,
          ["label-width"]: "70px",
          readonly: true,
          placeholder: "请输入用户名",
          modelValue: model.username
        }),
        c: common_vendor.o(($event) => model.realname = $event),
        d: common_vendor.p({
          label: "姓名",
          prop: "realname",
          clearable: true,
          ["label-width"]: "70px",
          placeholder: "请输入用户名",
          rules: [{
            validator: rules.realname
          }],
          modelValue: model.realname
        }),
        e: common_vendor.unref(userStore).userInfo.userCategory === 1
      }, common_vendor.unref(userStore).userInfo.userCategory === 1 ? {
        f: common_vendor.o(($event) => model.caseNumber = $event),
        g: common_vendor.p({
          label: "病案号",
          prop: "caseNumber",
          clearable: true,
          ["label-width"]: "70px",
          placeholder: "请输入病案号",
          modelValue: model.caseNumber
        })
      } : {}, {
        h: common_vendor.o(($event) => avatarFileName.value = $event),
        i: common_vendor.o(($event) => model.avatar = $event),
        j: common_vendor.p({
          modelValue: model.avatar
        }),
        k: common_vendor.p({
          title: "头像",
          ["title-width"]: "70px"
        }),
        l: common_vendor.o(($event) => model.sex = $event),
        m: common_vendor.p({
          label: "性别",
          type: "radio",
          columns,
          title: "请选择性别",
          ["safe-area-inset-bottom"]: false,
          modelValue: model.sex
        }),
        n: common_vendor.o(($event) => model.phone = $event),
        o: common_vendor.p({
          label: "手机号",
          prop: "phone",
          clearable: true,
          ["label-width"]: "70px",
          readonly: true,
          placeholder: "请输入手机号",
          rules: [{
            validator: rules.phone
          }],
          modelValue: model.phone
        }),
        p: common_vendor.p({
          border: true
        }),
        q: common_vendor.o(handleSubmit),
        r: common_vendor.p({
          type: "primary",
          size: "large",
          block: true
        }),
        s: common_vendor.sr(form, "7342d100-2,7342d100-1", {
          "k": "form"
        }),
        t: common_vendor.p({
          ["custom-class"]: "pt3",
          model
        }),
        v: common_vendor.p({
          navTitle: "编辑资料",
          backRouteName: "people",
          routeMethod: "pushTab"
        })
      });
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-7342d100"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=userEdit.js.map
