{"version": 3, "file": "wd-cell.js", "sources": ["../../../../../../../node_modules/wot-design-uni/components/wd-cell/wd-cell.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9ub2RlX21vZHVsZXMvd290LWRlc2lnbi11bmkvY29tcG9uZW50cy93ZC1jZWxsL3dkLWNlbGwudnVl"], "sourcesContent": ["<template>\n  <view\n    :class=\"['wd-cell', isBorder ? 'is-border' : '', size ? 'is-' + size : '', center ? 'is-center' : '', customClass]\"\n    :style=\"customStyle\"\n    :hover-class=\"isLink || clickable ? 'is-hover' : 'none'\"\n    :hover-stay-time=\"70\"\n    @click=\"onClick\"\n  >\n    <view :class=\"['wd-cell__wrapper', vertical ? 'is-vertical' : '']\">\n      <view\n        :class=\"['wd-cell__left', isRequired ? 'is-required' : '']\"\n        :style=\"titleWidth ? 'min-width:' + titleWidth + ';max-width:' + titleWidth + ';' : ''\"\n      >\n        <!--左侧icon部位-->\n        <wd-icon v-if=\"icon\" :name=\"icon\" :custom-class=\"`wd-cell__icon  ${customIconClass}`\"></wd-icon>\n        <slot v-else name=\"icon\" />\n\n        <view class=\"wd-cell__title\">\n          <!--title BEGIN-->\n          <view v-if=\"title\" :class=\"customTitleClass\">{{ title }}</view>\n          <slot v-else name=\"title\"></slot>\n          <!--title END-->\n\n          <!--label BEGIN-->\n          <view v-if=\"label\" :class=\"`wd-cell__label ${customLabelClass}`\">{{ label }}</view>\n          <slot v-else name=\"label\" />\n          <!--label END-->\n        </view>\n      </view>\n      <!--right content BEGIN-->\n      <view class=\"wd-cell__right\">\n        <view class=\"wd-cell__body\">\n          <!--文案内容-->\n          <view :class=\"`wd-cell__value ${customValueClass}`\">\n            <slot>{{ value }}</slot>\n          </view>\n          <!--箭头-->\n          <wd-icon v-if=\"isLink\" custom-class=\"wd-cell__arrow-right\" name=\"arrow-right\" />\n          <slot v-else name=\"right-icon\" />\n        </view>\n        <view v-if=\"errorMessage\" class=\"wd-cell__error-message\">{{ errorMessage }}</view>\n      </view>\n      <!--right content END-->\n    </view>\n  </view>\n</template>\n\n<script lang=\"ts\">\nexport default {\n  name: 'wd-cell',\n  options: {\n    addGlobalClass: true,\n    virtualHost: true,\n    styleIsolation: 'shared'\n  }\n}\n</script>\n\n<script lang=\"ts\" setup>\nimport wdIcon from '../wd-icon/wd-icon.vue'\nimport { computed } from 'vue'\nimport { useCell } from '../composables/useCell'\nimport { useParent } from '../composables/useParent'\nimport { FORM_KEY } from '../wd-form/types'\nimport { cellProps } from './types'\nimport { isDef } from '../common/util'\n\nconst props = defineProps(cellProps)\nconst emit = defineEmits(['click'])\n\nconst cell = useCell()\n\nconst isBorder = computed(() => {\n  return Boolean(isDef(props.border) ? props.border : cell.border.value)\n})\n\nconst { parent: form } = useParent(FORM_KEY)\n\nconst errorMessage = computed(() => {\n  if (form && props.prop && form.errorMessages && form.errorMessages[props.prop]) {\n    return form.errorMessages[props.prop]\n  } else {\n    return ''\n  }\n})\n\n// 是否展示必填\nconst isRequired = computed(() => {\n  let formRequired = false\n  if (form && form.props.rules) {\n    const rules = form.props.rules\n    for (const key in rules) {\n      if (Object.prototype.hasOwnProperty.call(rules, key) && key === props.prop && Array.isArray(rules[key])) {\n        formRequired = rules[key].some((rule) => rule.required)\n      }\n    }\n  }\n  return props.required || props.rules.some((rule) => rule.required) || formRequired\n})\n\n/**\n * @description 点击cell的handle\n */\nfunction onClick() {\n  const url = props.to\n\n  if (props.clickable || props.isLink) {\n    emit('click')\n  }\n  if (url && props.isLink) {\n    if (props.replace) {\n      uni.redirectTo({ url })\n    } else {\n      uni.navigateTo({ url })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/node_modules/wot-design-uni/components/wd-cell/wd-cell.vue'\nwx.createComponent(Component)"], "names": ["useCell", "computed", "isDef", "useParent", "FORM_KEY", "uni"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AA2DA,MAAA,SAAmB,MAAA;AAXnB,MAAe,cAAA;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IACP,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,gBAAgB;AAAA,EAAA;AAEpB;;;;;AAYA,UAAM,QAAQ;AACd,UAAM,OAAO;AAEb,UAAM,OAAOA,cAAAA,QAAQ;AAEf,UAAA,WAAWC,cAAAA,SAAS,MAAM;AACvB,aAAA,QAAQC,oBAAM,MAAM,MAAM,IAAI,MAAM,SAAS,KAAK,OAAO,KAAK;AAAA,IAAA,CACtE;AAED,UAAM,EAAE,QAAQ,SAASC,cAAAA,UAAUC,cAAAA,QAAQ;AAErC,UAAA,eAAeH,cAAAA,SAAS,MAAM;AAC9B,UAAA,QAAQ,MAAM,QAAQ,KAAK,iBAAiB,KAAK,cAAc,MAAM,IAAI,GAAG;AACvE,eAAA,KAAK,cAAc,MAAM,IAAI;AAAA,MAAA,OAC/B;AACE,eAAA;AAAA,MAAA;AAAA,IACT,CACD;AAGK,UAAA,aAAaA,cAAAA,SAAS,MAAM;AAChC,UAAI,eAAe;AACf,UAAA,QAAQ,KAAK,MAAM,OAAO;AACtB,cAAA,QAAQ,KAAK,MAAM;AACzB,mBAAW,OAAO,OAAO;AACvB,cAAI,OAAO,UAAU,eAAe,KAAK,OAAO,GAAG,KAAK,QAAQ,MAAM,QAAQ,MAAM,QAAQ,MAAM,GAAG,CAAC,GAAG;AACvG,2BAAe,MAAM,GAAG,EAAE,KAAK,CAAC,SAAS,KAAK,QAAQ;AAAA,UAAA;AAAA,QACxD;AAAA,MACF;AAEK,aAAA,MAAM,YAAY,MAAM,MAAM,KAAK,CAAC,SAAS,KAAK,QAAQ,KAAK;AAAA,IAAA,CACvE;AAKD,aAAS,UAAU;AACjB,YAAM,MAAM,MAAM;AAEd,UAAA,MAAM,aAAa,MAAM,QAAQ;AACnC,aAAK,OAAO;AAAA,MAAA;AAEV,UAAA,OAAO,MAAM,QAAQ;AACvB,YAAI,MAAM,SAAS;AACbI,8BAAA,WAAW,EAAE,KAAK;AAAA,QAAA,OACjB;AACDA,8BAAA,WAAW,EAAE,KAAK;AAAA,QAAA;AAAA,MACxB;AAAA,IACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClHF,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}