{"version": 3, "file": "util.js", "sources": ["../../../../../../../src/uni_modules/uni-calendar/components/uni-calendar/util.js"], "sourcesContent": ["import CALENDAR from './calendar.js'\r\n\r\nclass Calendar {\r\n\tconstructor({\r\n\t\tdate,\r\n\t\tselected,\r\n\t\tstartDate,\r\n\t\tendDate,\r\n\t\trange\r\n\t} = {}) {\r\n\t\t// 当前日期\r\n\t\tthis.date = this.getDate(new Date()) // 当前初入日期\r\n\t\t// 打点信息\r\n\t\tthis.selected = selected || [];\r\n\t\t// 范围开始\r\n\t\tthis.startDate = startDate\r\n\t\t// 范围结束\r\n\t\tthis.endDate = endDate\r\n\t\tthis.range = range\r\n\t\t// 多选状态\r\n\t\tthis.cleanMultipleStatus()\r\n\t\t// 每周日期\r\n\t\tthis.weeks = {}\r\n\t\t// this._getWeek(this.date.fullDate)\r\n\t}\r\n\t/**\r\n\t * 设置日期\r\n\t * @param {Object} date\r\n\t */\r\n\tsetDate(date) {\r\n\t\tthis.selectDate = this.getDate(date)\r\n\t\tthis._getWeek(this.selectDate.fullDate)\r\n\t}\r\n\r\n\t/**\r\n\t * 清理多选状态\r\n\t */\r\n\tcleanMultipleStatus() {\r\n\t\tthis.multipleStatus = {\r\n\t\t\tbefore: '',\r\n\t\t\tafter: '',\r\n\t\t\tdata: []\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * 重置开始日期\r\n\t */\r\n\tresetSatrtDate(startDate) {\r\n\t\t// 范围开始\r\n\t\tthis.startDate = startDate\r\n\r\n\t}\r\n\r\n\t/**\r\n\t * 重置结束日期\r\n\t */\r\n\tresetEndDate(endDate) {\r\n\t\t// 范围结束\r\n\t\tthis.endDate = endDate\r\n\t}\r\n\r\n\t/**\r\n\t * 获取任意时间\r\n\t */\r\n\tgetDate(date, AddDayCount = 0, str = 'day') {\r\n\t\tif (!date) {\r\n\t\t\tdate = new Date()\r\n\t\t}\r\n\t\tif (typeof date !== 'object') {\r\n\t\t\tdate = date.replace(/-/g, '/')\r\n\t\t}\r\n\t\tconst dd = new Date(date)\r\n\t\tswitch (str) {\r\n\t\t\tcase 'day':\r\n\t\t\t\tdd.setDate(dd.getDate() + AddDayCount) // 获取AddDayCount天后的日期\r\n\t\t\t\tbreak\r\n\t\t\tcase 'month':\r\n\t\t\t\tif (dd.getDate() === 31 && AddDayCount>0) {\r\n\t\t\t\t\tdd.setDate(dd.getDate() + AddDayCount)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconst preMonth = dd.getMonth()\r\n\t\t\t\t\tdd.setMonth(preMonth + AddDayCount) // 获取AddDayCount天后的日期\r\n\t\t\t\t\tconst nextMonth = dd.getMonth()\r\n\t\t\t\t\t// 处理 pre 切换月份目标月份为2月没有当前日(30 31) 切换错误问题\r\n\t\t\t\t\tif(AddDayCount<0 && preMonth!==0 && nextMonth-preMonth>AddDayCount){\r\n\t\t\t\t\t\tdd.setMonth(nextMonth+(nextMonth-preMonth+AddDayCount))\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 处理 next 切换月份目标月份为2月没有当前日(30 31) 切换错误问题\r\n\t\t\t\t\tif(AddDayCount>0 && nextMonth-preMonth>AddDayCount){\r\n\t\t\t\t\t\tdd.setMonth(nextMonth-(nextMonth-preMonth-AddDayCount))\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tbreak\r\n\t\t\tcase 'year':\r\n\t\t\t\tdd.setFullYear(dd.getFullYear() + AddDayCount) // 获取AddDayCount天后的日期\r\n\t\t\t\tbreak\r\n\t\t}\r\n\t\tconst y = dd.getFullYear()\r\n\t\tconst m = dd.getMonth() + 1 < 10 ? '0' + (dd.getMonth() + 1) : dd.getMonth() + 1 // 获取当前月份的日期，不足10补0\r\n\t\tconst d = dd.getDate() < 10 ? '0' + dd.getDate() : dd.getDate() // 获取当前几号，不足10补0\r\n\t\treturn {\r\n\t\t\tfullDate: y + '-' + m + '-' + d,\r\n\t\t\tyear: y,\r\n\t\t\tmonth: m,\r\n\t\t\tdate: d,\r\n\t\t\tday: dd.getDay()\r\n\t\t}\r\n\t}\r\n\r\n\r\n\t/**\r\n\t * 获取上月剩余天数\r\n\t */\r\n\t_getLastMonthDays(firstDay, full) {\r\n\t\tlet dateArr = []\r\n\t\tfor (let i = firstDay; i > 0; i--) {\r\n\t\t\tconst beforeDate = new Date(full.year, full.month - 1, -i + 1).getDate()\r\n\t\t\tdateArr.push({\r\n\t\t\t\tdate: beforeDate,\r\n\t\t\t\tmonth: full.month - 1,\r\n\t\t\t\tlunar: this.getlunar(full.year, full.month - 1, beforeDate),\r\n\t\t\t\tdisable: true\r\n\t\t\t})\r\n\t\t}\r\n\t\treturn dateArr\r\n\t}\r\n\t/**\r\n\t * 获取本月天数\r\n\t */\r\n\t_currentMonthDys(dateData, full) {\r\n\t\tlet dateArr = []\r\n\t\tlet fullDate = this.date.fullDate\r\n\t\tfor (let i = 1; i <= dateData; i++) {\r\n\t\t\tlet nowDate = full.year + '-' + (full.month < 10 ?\r\n\t\t\t\tfull.month : full.month) + '-' + (i < 10 ?\r\n\t\t\t\t'0' + i : i)\r\n\t\t\t// 是否今天\r\n\t\t\tlet isDay = fullDate === nowDate\r\n\t\t\t// 获取打点信息\r\n\t\t\tlet info = this.selected && this.selected.find((item) => {\r\n\t\t\t\tif (this.dateEqual(nowDate, item.date)) {\r\n\t\t\t\t\treturn item\r\n\t\t\t\t}\r\n\t\t\t})\r\n\r\n\t\t\t// 日期禁用\r\n\t\t\tlet disableBefore = true\r\n\t\t\tlet disableAfter = true\r\n\t\t\tif (this.startDate) {\r\n\t\t\t\t// let dateCompBefore = this.dateCompare(this.startDate, fullDate)\r\n\t\t\t\t// disableBefore = this.dateCompare(dateCompBefore ? this.startDate : fullDate, nowDate)\r\n\t\t\t\tdisableBefore = this.dateCompare(this.startDate, nowDate)\r\n\t\t\t}\r\n\r\n\t\t\tif (this.endDate) {\r\n\t\t\t\t// let dateCompAfter = this.dateCompare(fullDate, this.endDate)\r\n\t\t\t\t// disableAfter = this.dateCompare(nowDate, dateCompAfter ? this.endDate : fullDate)\r\n\t\t\t\tdisableAfter = this.dateCompare(nowDate, this.endDate)\r\n\t\t\t}\r\n\t\t\tlet multiples = this.multipleStatus.data\r\n\t\t\tlet checked = false\r\n\t\t\tlet multiplesStatus = -1\r\n\t\t\tif (this.range) {\r\n\t\t\t\tif (multiples) {\r\n\t\t\t\t\tmultiplesStatus = multiples.findIndex((item) => {\r\n\t\t\t\t\t\treturn this.dateEqual(item, nowDate)\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tif (multiplesStatus !== -1) {\r\n\t\t\t\t\tchecked = true\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tlet data = {\r\n\t\t\t\tfullDate: nowDate,\r\n\t\t\t\tyear: full.year,\r\n\t\t\t\tdate: i,\r\n\t\t\t\tmultiple: this.range ? checked : false,\r\n\t\t\t\tbeforeMultiple: this.dateEqual(this.multipleStatus.before, nowDate),\r\n\t\t\t\tafterMultiple: this.dateEqual(this.multipleStatus.after, nowDate),\r\n\t\t\t\tmonth: full.month,\r\n\t\t\t\tlunar: this.getlunar(full.year, full.month, i),\r\n\t\t\t\tdisable: !(disableBefore && disableAfter),\r\n\t\t\t\tisDay\r\n\t\t\t}\r\n\t\t\tif (info) {\r\n\t\t\t\tdata.extraInfo = info\r\n\t\t\t}\r\n\r\n\t\t\tdateArr.push(data)\r\n\t\t}\r\n\t\treturn dateArr\r\n\t}\r\n\t/**\r\n\t * 获取下月天数\r\n\t */\r\n\t_getNextMonthDays(surplus, full) {\r\n\t\tlet dateArr = []\r\n\t\tfor (let i = 1; i < surplus + 1; i++) {\r\n\t\t\tdateArr.push({\r\n\t\t\t\tdate: i,\r\n\t\t\t\tmonth: Number(full.month) + 1,\r\n\t\t\t\tlunar: this.getlunar(full.year, Number(full.month) + 1, i),\r\n\t\t\t\tdisable: true\r\n\t\t\t})\r\n\t\t}\r\n\t\treturn dateArr\r\n\t}\r\n\r\n\t/**\r\n\t * 获取当前日期详情\r\n\t * @param {Object} date\r\n\t */\r\n\tgetInfo(date) {\r\n\t\tif (!date) {\r\n\t\t\tdate = new Date()\r\n\t\t}\r\n\t\tconst dateInfo = this.canlender.find(item => item.fullDate === this.getDate(date).fullDate)\r\n\t\treturn dateInfo\r\n\t}\r\n\r\n\t/**\r\n\t * 比较时间大小\r\n\t */\r\n\tdateCompare(startDate, endDate) {\r\n\t\t// 计算截止时间\r\n\t\tstartDate = new Date(startDate.replace('-', '/').replace('-', '/'))\r\n\t\t// 计算详细项的截止时间\r\n\t\tendDate = new Date(endDate.replace('-', '/').replace('-', '/'))\r\n\t\tif (startDate <= endDate) {\r\n\t\t\treturn true\r\n\t\t} else {\r\n\t\t\treturn false\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * 比较时间是否相等\r\n\t */\r\n\tdateEqual(before, after) {\r\n\t\t// 计算截止时间\r\n\t\tbefore = new Date(before.replace('-', '/').replace('-', '/'))\r\n\t\t// 计算详细项的截止时间\r\n\t\tafter = new Date(after.replace('-', '/').replace('-', '/'))\r\n\t\tif (before.getTime() - after.getTime() === 0) {\r\n\t\t\treturn true\r\n\t\t} else {\r\n\t\t\treturn false\r\n\t\t}\r\n\t}\r\n\r\n\r\n\t/**\r\n\t * 获取日期范围内所有日期\r\n\t * @param {Object} begin\r\n\t * @param {Object} end\r\n\t */\r\n\tgeDateAll(begin, end) {\r\n\t\tvar arr = []\r\n\t\tvar ab = begin.split('-')\r\n\t\tvar ae = end.split('-')\r\n\t\tvar db = new Date()\r\n\t\tdb.setFullYear(ab[0], ab[1] - 1, ab[2])\r\n\t\tvar de = new Date()\r\n\t\tde.setFullYear(ae[0], ae[1] - 1, ae[2])\r\n\t\tvar unixDb = db.getTime() - 24 * 60 * 60 * 1000\r\n\t\tvar unixDe = de.getTime() - 24 * 60 * 60 * 1000\r\n\t\tfor (var k = unixDb; k <= unixDe;) {\r\n\t\t\tk = k + 24 * 60 * 60 * 1000\r\n\t\t\tarr.push(this.getDate(new Date(parseInt(k))).fullDate)\r\n\t\t}\r\n\t\treturn arr\r\n\t}\r\n\t/**\r\n\t * 计算阴历日期显示\r\n\t */\r\n\tgetlunar(year, month, date) {\r\n\t\treturn CALENDAR.solar2lunar(year, month, date)\r\n\t}\r\n\t/**\r\n\t * 设置打点\r\n\t */\r\n\tsetSelectInfo(data, value) {\r\n\t\tthis.selected = value\r\n\t\tthis._getWeek(data)\r\n\t}\r\n\r\n\t/**\r\n\t *  获取多选状态\r\n\t */\r\n\tsetMultiple(fullDate) {\r\n\t\tlet {\r\n\t\t\tbefore,\r\n\t\t\tafter\r\n\t\t} = this.multipleStatus\r\n\r\n\t\tif (!this.range) return\r\n\t\tif (before && after) {\r\n\t\t\tthis.multipleStatus.before = fullDate\r\n\t\t\tthis.multipleStatus.after = ''\r\n\t\t\tthis.multipleStatus.data = []\r\n\t\t} else {\r\n\t\t\tif (!before) {\r\n\t\t\t\tthis.multipleStatus.before = fullDate\r\n\t\t\t} else {\r\n\t\t\t\tthis.multipleStatus.after = fullDate\r\n\t\t\t\tif (this.dateCompare(this.multipleStatus.before, this.multipleStatus.after)) {\r\n\t\t\t\t\tthis.multipleStatus.data = this.geDateAll(this.multipleStatus.before, this.multipleStatus.after);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.multipleStatus.data = this.geDateAll(this.multipleStatus.after, this.multipleStatus.before);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\tthis._getWeek(fullDate)\r\n\t}\r\n\r\n\t/**\r\n\t * 获取每周数据\r\n\t * @param {Object} dateData\r\n\t */\r\n\t_getWeek(dateData) {\r\n\t\tconst {\r\n\t\t\tyear,\r\n\t\t\tmonth\r\n\t\t} = this.getDate(dateData)\r\n\t\tlet firstDay = new Date(year, month - 1, 1).getDay()\r\n\t\tlet currentDay = new Date(year, month, 0).getDate()\r\n\t\tlet dates = {\r\n\t\t\tlastMonthDays: this._getLastMonthDays(firstDay, this.getDate(dateData)), // 上个月末尾几天\r\n\t\t\tcurrentMonthDys: this._currentMonthDys(currentDay, this.getDate(dateData)), // 本月天数\r\n\t\t\tnextMonthDays: [], // 下个月开始几天\r\n\t\t\tweeks: []\r\n\t\t}\r\n\t\tlet canlender = []\r\n\t\tconst surplus = 42 - (dates.lastMonthDays.length + dates.currentMonthDys.length)\r\n\t\tdates.nextMonthDays = this._getNextMonthDays(surplus, this.getDate(dateData))\r\n\t\tcanlender = canlender.concat(dates.lastMonthDays, dates.currentMonthDys, dates.nextMonthDays)\r\n\t\tlet weeks = {}\r\n\t\t// 拼接数组  上个月开始几天 + 本月天数+ 下个月开始几天\r\n\t\tfor (let i = 0; i < canlender.length; i++) {\r\n\t\t\tif (i % 7 === 0) {\r\n\t\t\t\tweeks[parseInt(i / 7)] = new Array(7)\r\n\t\t\t}\r\n\t\t\tweeks[parseInt(i / 7)][i % 7] = canlender[i]\r\n\t\t}\r\n\t\tthis.canlender = canlender\r\n\t\tthis.weeks = weeks\r\n\t}\r\n\r\n\t//静态方法\r\n\t// static init(date) {\r\n\t// \tif (!this.instance) {\r\n\t// \t\tthis.instance = new Calendar(date);\r\n\t// \t}\r\n\t// \treturn this.instance;\r\n\t// }\r\n}\r\n\r\n\r\nexport default Calendar\r\n"], "names": ["CALENDAR"], "mappings": ";;AAEA,MAAM,SAAS;AAAA,EACd,YAAY;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACA,IAAG,IAAI;AAEP,SAAK,OAAO,KAAK,QAAQ,oBAAI,KAAI,CAAE;AAEnC,SAAK,WAAW,YAAY;AAE5B,SAAK,YAAY;AAEjB,SAAK,UAAU;AACf,SAAK,QAAQ;AAEb,SAAK,oBAAqB;AAE1B,SAAK,QAAQ,CAAE;AAAA,EAEf;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,QAAQ,MAAM;AACb,SAAK,aAAa,KAAK,QAAQ,IAAI;AACnC,SAAK,SAAS,KAAK,WAAW,QAAQ;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA,EAKD,sBAAsB;AACrB,SAAK,iBAAiB;AAAA,MACrB,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,MAAM,CAAE;AAAA,IACR;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,eAAe,WAAW;AAEzB,SAAK,YAAY;AAAA,EAEjB;AAAA;AAAA;AAAA;AAAA,EAKD,aAAa,SAAS;AAErB,SAAK,UAAU;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAKD,QAAQ,MAAM,cAAc,GAAG,MAAM,OAAO;AAC3C,QAAI,CAAC,MAAM;AACV,aAAO,oBAAI,KAAM;AAAA,IACjB;AACD,QAAI,OAAO,SAAS,UAAU;AAC7B,aAAO,KAAK,QAAQ,MAAM,GAAG;AAAA,IAC7B;AACD,UAAM,KAAK,IAAI,KAAK,IAAI;AACxB,YAAQ,KAAG;AAAA,MACV,KAAK;AACJ,WAAG,QAAQ,GAAG,QAAO,IAAK,WAAW;AACrC;AAAA,MACD,KAAK;AACJ,YAAI,GAAG,QAAO,MAAO,MAAM,cAAY,GAAG;AACzC,aAAG,QAAQ,GAAG,QAAO,IAAK,WAAW;AAAA,QAC1C,OAAW;AACN,gBAAM,WAAW,GAAG,SAAU;AAC9B,aAAG,SAAS,WAAW,WAAW;AAClC,gBAAM,YAAY,GAAG,SAAU;AAE/B,cAAG,cAAY,KAAK,aAAW,KAAK,YAAU,WAAS,aAAY;AAClE,eAAG,SAAS,aAAW,YAAU,WAAS,YAAY;AAAA,UACtD;AAED,cAAG,cAAY,KAAK,YAAU,WAAS,aAAY;AAClD,eAAG,SAAS,aAAW,YAAU,WAAS,YAAY;AAAA,UACtD;AAAA,QACD;AACD;AAAA,MACD,KAAK;AACJ,WAAG,YAAY,GAAG,YAAW,IAAK,WAAW;AAC7C;AAAA,IACD;AACD,UAAM,IAAI,GAAG,YAAa;AAC1B,UAAM,IAAI,GAAG,SAAQ,IAAK,IAAI,KAAK,OAAO,GAAG,SAAU,IAAG,KAAK,GAAG,SAAU,IAAG;AAC/E,UAAM,IAAI,GAAG,QAAO,IAAK,KAAK,MAAM,GAAG,QAAO,IAAK,GAAG,QAAS;AAC/D,WAAO;AAAA,MACN,UAAU,IAAI,MAAM,IAAI,MAAM;AAAA,MAC9B,MAAM;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,KAAK,GAAG,OAAQ;AAAA,IAChB;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAMD,kBAAkB,UAAU,MAAM;AACjC,QAAI,UAAU,CAAE;AAChB,aAAS,IAAI,UAAU,IAAI,GAAG,KAAK;AAClC,YAAM,aAAa,IAAI,KAAK,KAAK,MAAM,KAAK,QAAQ,GAAG,CAAC,IAAI,CAAC,EAAE,QAAS;AACxE,cAAQ,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,OAAO,KAAK,QAAQ;AAAA,QACpB,OAAO,KAAK,SAAS,KAAK,MAAM,KAAK,QAAQ,GAAG,UAAU;AAAA,QAC1D,SAAS;AAAA,MACb,CAAI;AAAA,IACD;AACD,WAAO;AAAA,EACP;AAAA;AAAA;AAAA;AAAA,EAID,iBAAiB,UAAU,MAAM;AAChC,QAAI,UAAU,CAAE;AAChB,QAAI,WAAW,KAAK,KAAK;AACzB,aAAS,IAAI,GAAG,KAAK,UAAU,KAAK;AACnC,UAAI,UAAU,KAAK,OAAO,OAAO,KAAK,QAAQ,KAC7C,KAAK,QAAQ,KAAK,SAAS,OAAO,IAAI,KACtC,MAAM,IAAI;AAEX,UAAI,QAAQ,aAAa;AAEzB,UAAI,OAAO,KAAK,YAAY,KAAK,SAAS,KAAK,CAAC,SAAS;AACxD,YAAI,KAAK,UAAU,SAAS,KAAK,IAAI,GAAG;AACvC,iBAAO;AAAA,QACP;AAAA,MACL,CAAI;AAGD,UAAI,gBAAgB;AACpB,UAAI,eAAe;AACnB,UAAI,KAAK,WAAW;AAGnB,wBAAgB,KAAK,YAAY,KAAK,WAAW,OAAO;AAAA,MACxD;AAED,UAAI,KAAK,SAAS;AAGjB,uBAAe,KAAK,YAAY,SAAS,KAAK,OAAO;AAAA,MACrD;AACD,UAAI,YAAY,KAAK,eAAe;AACpC,UAAI,UAAU;AACd,UAAI,kBAAkB;AACtB,UAAI,KAAK,OAAO;AACf,YAAI,WAAW;AACd,4BAAkB,UAAU,UAAU,CAAC,SAAS;AAC/C,mBAAO,KAAK,UAAU,MAAM,OAAO;AAAA,UACzC,CAAM;AAAA,QACD;AACD,YAAI,oBAAoB,IAAI;AAC3B,oBAAU;AAAA,QACV;AAAA,MACD;AACD,UAAI,OAAO;AAAA,QACV,UAAU;AAAA,QACV,MAAM,KAAK;AAAA,QACX,MAAM;AAAA,QACN,UAAU,KAAK,QAAQ,UAAU;AAAA,QACjC,gBAAgB,KAAK,UAAU,KAAK,eAAe,QAAQ,OAAO;AAAA,QAClE,eAAe,KAAK,UAAU,KAAK,eAAe,OAAO,OAAO;AAAA,QAChE,OAAO,KAAK;AAAA,QACZ,OAAO,KAAK,SAAS,KAAK,MAAM,KAAK,OAAO,CAAC;AAAA,QAC7C,SAAS,EAAE,iBAAiB;AAAA,QAC5B;AAAA,MACA;AACD,UAAI,MAAM;AACT,aAAK,YAAY;AAAA,MACjB;AAED,cAAQ,KAAK,IAAI;AAAA,IACjB;AACD,WAAO;AAAA,EACP;AAAA;AAAA;AAAA;AAAA,EAID,kBAAkB,SAAS,MAAM;AAChC,QAAI,UAAU,CAAE;AAChB,aAAS,IAAI,GAAG,IAAI,UAAU,GAAG,KAAK;AACrC,cAAQ,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,OAAO,OAAO,KAAK,KAAK,IAAI;AAAA,QAC5B,OAAO,KAAK,SAAS,KAAK,MAAM,OAAO,KAAK,KAAK,IAAI,GAAG,CAAC;AAAA,QACzD,SAAS;AAAA,MACb,CAAI;AAAA,IACD;AACD,WAAO;AAAA,EACP;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,QAAQ,MAAM;AACb,QAAI,CAAC,MAAM;AACV,aAAO,oBAAI,KAAM;AAAA,IACjB;AACD,UAAM,WAAW,KAAK,UAAU,KAAK,UAAQ,KAAK,aAAa,KAAK,QAAQ,IAAI,EAAE,QAAQ;AAC1F,WAAO;AAAA,EACP;AAAA;AAAA;AAAA;AAAA,EAKD,YAAY,WAAW,SAAS;AAE/B,gBAAY,IAAI,KAAK,UAAU,QAAQ,KAAK,GAAG,EAAE,QAAQ,KAAK,GAAG,CAAC;AAElE,cAAU,IAAI,KAAK,QAAQ,QAAQ,KAAK,GAAG,EAAE,QAAQ,KAAK,GAAG,CAAC;AAC9D,QAAI,aAAa,SAAS;AACzB,aAAO;AAAA,IACV,OAAS;AACN,aAAO;AAAA,IACP;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,UAAU,QAAQ,OAAO;AAExB,aAAS,IAAI,KAAK,OAAO,QAAQ,KAAK,GAAG,EAAE,QAAQ,KAAK,GAAG,CAAC;AAE5D,YAAQ,IAAI,KAAK,MAAM,QAAQ,KAAK,GAAG,EAAE,QAAQ,KAAK,GAAG,CAAC;AAC1D,QAAI,OAAO,QAAS,IAAG,MAAM,QAAO,MAAO,GAAG;AAC7C,aAAO;AAAA,IACV,OAAS;AACN,aAAO;AAAA,IACP;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,UAAU,OAAO,KAAK;AACrB,QAAI,MAAM,CAAE;AACZ,QAAI,KAAK,MAAM,MAAM,GAAG;AACxB,QAAI,KAAK,IAAI,MAAM,GAAG;AACtB,QAAI,KAAK,oBAAI,KAAM;AACnB,OAAG,YAAY,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;AACtC,QAAI,KAAK,oBAAI,KAAM;AACnB,OAAG,YAAY,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;AACtC,QAAI,SAAS,GAAG,QAAO,IAAK,KAAK,KAAK,KAAK;AAC3C,QAAI,SAAS,GAAG,QAAO,IAAK,KAAK,KAAK,KAAK;AAC3C,aAAS,IAAI,QAAQ,KAAK,UAAS;AAClC,UAAI,IAAI,KAAK,KAAK,KAAK;AACvB,UAAI,KAAK,KAAK,QAAQ,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,QAAQ;AAAA,IACrD;AACD,WAAO;AAAA,EACP;AAAA;AAAA;AAAA;AAAA,EAID,SAAS,MAAM,OAAO,MAAM;AAC3B,WAAOA,wDAAQ,SAAC,YAAY,MAAM,OAAO,IAAI;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA,EAID,cAAc,MAAM,OAAO;AAC1B,SAAK,WAAW;AAChB,SAAK,SAAS,IAAI;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAKD,YAAY,UAAU;AACrB,QAAI;AAAA,MACH;AAAA,MACA;AAAA,IACA,IAAG,KAAK;AAET,QAAI,CAAC,KAAK;AAAO;AACjB,QAAI,UAAU,OAAO;AACpB,WAAK,eAAe,SAAS;AAC7B,WAAK,eAAe,QAAQ;AAC5B,WAAK,eAAe,OAAO,CAAE;AAAA,IAChC,OAAS;AACN,UAAI,CAAC,QAAQ;AACZ,aAAK,eAAe,SAAS;AAAA,MACjC,OAAU;AACN,aAAK,eAAe,QAAQ;AAC5B,YAAI,KAAK,YAAY,KAAK,eAAe,QAAQ,KAAK,eAAe,KAAK,GAAG;AAC5E,eAAK,eAAe,OAAO,KAAK,UAAU,KAAK,eAAe,QAAQ,KAAK,eAAe,KAAK;AAAA,QACpG,OAAW;AACN,eAAK,eAAe,OAAO,KAAK,UAAU,KAAK,eAAe,OAAO,KAAK,eAAe,MAAM;AAAA,QAC/F;AAAA,MACD;AAAA,IACD;AACD,SAAK,SAAS,QAAQ;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,SAAS,UAAU;AAClB,UAAM;AAAA,MACL;AAAA,MACA;AAAA,IACH,IAAM,KAAK,QAAQ,QAAQ;AACzB,QAAI,WAAW,IAAI,KAAK,MAAM,QAAQ,GAAG,CAAC,EAAE,OAAQ;AACpD,QAAI,aAAa,IAAI,KAAK,MAAM,OAAO,CAAC,EAAE,QAAS;AACnD,QAAI,QAAQ;AAAA,MACX,eAAe,KAAK,kBAAkB,UAAU,KAAK,QAAQ,QAAQ,CAAC;AAAA;AAAA,MACtE,iBAAiB,KAAK,iBAAiB,YAAY,KAAK,QAAQ,QAAQ,CAAC;AAAA;AAAA,MACzE,eAAe,CAAA;AAAA,IAEhB;AACA,QAAI,YAAY,CAAE;AAClB,UAAM,UAAU,MAAM,MAAM,cAAc,SAAS,MAAM,gBAAgB;AACzE,UAAM,gBAAgB,KAAK,kBAAkB,SAAS,KAAK,QAAQ,QAAQ,CAAC;AAC5E,gBAAY,UAAU,OAAO,MAAM,eAAe,MAAM,iBAAiB,MAAM,aAAa;AAC5F,QAAI,QAAQ,CAAE;AAEd,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC1C,UAAI,IAAI,MAAM,GAAG;AAChB,cAAM,SAAS,IAAI,CAAC,CAAC,IAAI,IAAI,MAAM,CAAC;AAAA,MACpC;AACD,YAAM,SAAS,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,UAAU,CAAC;AAAA,IAC3C;AACD,SAAK,YAAY;AACjB,SAAK,QAAQ;AAAA,EACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASF;;"}