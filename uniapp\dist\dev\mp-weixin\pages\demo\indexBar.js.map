{"version": 3, "file": "indexBar.js", "sources": ["../../../../../src/pages/demo/indexBar.vue", "../../../../../uniPage:/cGFnZXMvZGVtby9pbmRleEJhci52dWU"], "sourcesContent": ["\r\n\r\n<template>\n<layout-default-uni >\r\n  <PageLayout navTitle=\"通讯录\">\r\n    <wd-search\r\n      hide-cancel\r\n      placeholder=\"我要去哪里？\"\r\n      v-model=\"keyword\"\r\n      @search=\"handleSearch\"\r\n      @clear=\"handleClear\"\r\n    />\r\n    <view class=\"wraper\">\r\n      <wd-index-bar sticky v-if=\"showList.length\">\r\n        <view v-for=\"item in showList\" :key=\"item.index\">\r\n          <wd-index-anchor :index=\"item.index\" />\r\n          <wd-cell\r\n            border\r\n            clickable\r\n            v-for=\"city in item.data\"\r\n            :key=\"city\"\r\n            :title=\"city\"\r\n            @click=\"handleClick(item.index, city)\"\r\n          ></wd-cell>\r\n        </view>\r\n      </wd-index-bar>\r\n    </view>\r\n  </PageLayout>\r\n</layout-default-uni>\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { useToast } from 'wot-design-uni'\r\nimport { nextTick, onMounted, ref } from 'vue'\r\nconst { show: showToast } = useToast()\r\n\r\nonMounted(() => {\r\n  handleSearch()\r\n})\r\nconst handleClickLeft = (params) => {\r\n  console.log('导航-返回')\r\n}\r\n\r\nconst keyword = ref('')\r\n\r\nconst showList = ref<any>([])\r\n\r\nconst indexList = [\r\n  {\r\n    index: 'A',\r\n    data: ['阿坝', '阿拉善', '阿里', '安康', '安庆', '鞍山', '安顺', '安阳', '澳门'],\r\n  },\r\n  {\r\n    index: 'B',\r\n    data: [\r\n      '北京',\r\n      '白银',\r\n      '保定',\r\n      '宝鸡',\r\n      '保山',\r\n      '包头',\r\n      '巴中',\r\n      '北海',\r\n      '蚌埠',\r\n      '本溪',\r\n      '毕节',\r\n      '滨州',\r\n      '百色',\r\n      '亳州',\r\n    ],\r\n  },\r\n  {\r\n    index: 'C',\r\n    data: [\r\n      '重庆',\r\n      '成都',\r\n      '长沙',\r\n      '长春',\r\n      '沧州',\r\n      '常德',\r\n      '昌都',\r\n      '长治',\r\n      '常州',\r\n      '巢湖',\r\n      '潮州',\r\n      '承德',\r\n      '郴州',\r\n      '赤峰',\r\n      '池州',\r\n      '崇左',\r\n      '楚雄',\r\n      '滁州',\r\n      '朝阳',\r\n    ],\r\n  },\r\n  {\r\n    index: 'D',\r\n    data: [\r\n      '大连',\r\n      '东莞',\r\n      '大理',\r\n      '丹东',\r\n      '大庆',\r\n      '大同',\r\n      '大兴安岭',\r\n      '德宏',\r\n      '德阳',\r\n      '德州',\r\n      '定西',\r\n      '迪庆',\r\n      '东营',\r\n    ],\r\n  },\r\n  {\r\n    index: 'E',\r\n    data: ['鄂尔多斯', '恩施', '鄂州'],\r\n  },\r\n  {\r\n    index: 'F',\r\n    data: ['福州', '防城港', '佛山', '抚顺', '抚州', '阜新', '阜阳'],\r\n  },\r\n  {\r\n    index: 'G',\r\n    data: ['广州', '桂林', '贵阳', '甘南', '赣州', '甘孜', '广安', '广元', '贵港', '果洛'],\r\n  },\r\n  {\r\n    index: 'H',\r\n    data: [\r\n      '杭州',\r\n      '哈尔滨',\r\n      '合肥',\r\n      '海口',\r\n      '呼和浩特',\r\n      '海北',\r\n      '海东',\r\n      '海南',\r\n      '海西',\r\n      '邯郸',\r\n      '汉中',\r\n      '鹤壁',\r\n      '河池',\r\n      '鹤岗',\r\n      '黑河',\r\n      '衡水',\r\n      '衡阳',\r\n      '河源',\r\n      '贺州',\r\n      '红河',\r\n      '淮安',\r\n      '淮北',\r\n      '怀化',\r\n      '淮南',\r\n      '黄冈',\r\n      '黄南',\r\n      '黄山',\r\n      '黄石',\r\n      '惠州',\r\n      '葫芦岛',\r\n      '呼伦贝尔',\r\n      '湖州',\r\n      '菏泽',\r\n    ],\r\n  },\r\n]\r\n\r\nfunction handleClick(index: string, city: string) {\r\n  showToast(`当前点击项：${index}，城市：${city}`)\r\n}\r\n\r\nfunction handleSearch() {\r\n  showList.value = []\r\n  nextTick(() => {\r\n    if (keyword.value) {\r\n      showList.value = indexList.filter((item) => {\r\n        return item.data.some((city) => {\r\n          return city.includes(keyword.value)\r\n        })\r\n      })\r\n    } else {\r\n      showList.value = indexList\r\n    }\r\n  })\r\n\r\n  // 筛选indexList项中data包含keyword的项\r\n}\r\n\r\nfunction handleClear() {\r\n  keyword.value = ''\r\n  handleSearch()\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.wraper {\r\n  height: calc(100vh - var(--window-top));\r\n  height: calc(100vh - var(--window-top) - constant(safe-area-inset-bottom));\r\n  height: calc(100vh - var(--window-top) - env(safe-area-inset-bottom));\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages/demo/indexBar.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useToast", "onMounted", "ref", "nextTick", "MiniProgramPage"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAkCA,UAAM,EAAE,MAAM,UAAU,IAAIA,uBAAS;AAErCC,kBAAAA,UAAU,MAAM;AACD,mBAAA;AAAA,IAAA,CACd;AAKK,UAAA,UAAUC,kBAAI,EAAE;AAEhB,UAAA,WAAWA,cAAS,IAAA,EAAE;AAE5B,UAAM,YAAY;AAAA,MAChB;AAAA,QACE,OAAO;AAAA,QACP,MAAM,CAAC,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,MAC9D;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,MAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QAAA;AAAA,MAEJ;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,MAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QAAA;AAAA,MAEJ;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,MAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QAAA;AAAA,MAEJ;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,MAAM,CAAC,QAAQ,MAAM,IAAI;AAAA,MAC3B;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,MAAM,CAAC,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,MAClD;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,MAAM,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,MACnE;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,MAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QAAA;AAAA,MACF;AAAA,IAEJ;AAES,aAAA,YAAY,OAAe,MAAc;AAChD,gBAAU,SAAS,KAAK,OAAO,IAAI,EAAE;AAAA,IAAA;AAGvC,aAAS,eAAe;AACtB,eAAS,QAAQ,CAAC;AAClBC,oBAAAA,WAAS,MAAM;AACb,YAAI,QAAQ,OAAO;AACjB,mBAAS,QAAQ,UAAU,OAAO,CAAC,SAAS;AAC1C,mBAAO,KAAK,KAAK,KAAK,CAAC,SAAS;AACvB,qBAAA,KAAK,SAAS,QAAQ,KAAK;AAAA,YAAA,CACnC;AAAA,UAAA,CACF;AAAA,QAAA,OACI;AACL,mBAAS,QAAQ;AAAA,QAAA;AAAA,MACnB,CACD;AAAA,IAAA;AAKH,aAAS,cAAc;AACrB,cAAQ,QAAQ;AACH,mBAAA;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3Lf,GAAG,WAAWC,SAAe;"}