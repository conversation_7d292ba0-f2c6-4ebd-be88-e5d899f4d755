{"version": 3, "file": "index.js", "sources": ["../../../../src/router/index.ts"], "sourcesContent": ["import { createRouter } from '@/plugin/uni-mini-router'\r\n// 导入pages.json\r\nimport pagesJson from '../pages.json'\r\nconsole.log(\"pagesJson::\",pagesJson);\r\n// 引入uni-parse-pages\r\nimport pagesJsonToRoutes from 'uni-parse-pages'\r\nimport { useUserStore } from '@/store/user'\r\n// 生成路由表\r\nconst routes = pagesJsonToRoutes(pagesJson)\r\nsetRouteName(routes)\r\nconst router = createRouter({\r\n  routes: [...routes], // 路由表信息\r\n})\r\nexport const whiteList = ['/pages/login/login']\r\nexport const loginPage = '/pages/login/login'\r\n\r\nexport const beforEach = (to, from, next) => {\r\n  const userStore = useUserStore()\r\n  if (userStore.isLogined) {\r\n    // 有登录态\r\n    next(true)\r\n  } else {\r\n    // 无登录态\r\n    if (whiteList.includes(to.path)) {\r\n      next()\r\n    } else {\r\n      next({ path: loginPage })\r\n    }\r\n  }\r\n}\r\n// 全局前置守卫\r\nrouter.beforeEach(beforEach)\r\n\r\n// 路由的最后一级为路由名字不可重复\r\nfunction setRouteName(routes) {\r\n  routes.forEach((item) => {\r\n    if (item.path) {\r\n      const name = item.path.split('/').pop()\r\n      item.name = name\r\n    }\r\n  })\r\n}\r\nexport default router\r\n"], "names": ["pagesJson", "pagesJsonToRoutes", "createRouter", "useUserStore", "routes"], "mappings": ";;;;;AAGA,QAAQ,IAAI,eAAcA,cAAS;AAKnC,MAAM,SAASC,gBAAkBD,cAAS;AAC1C,aAAa,MAAM;AACnB,MAAM,SAASE,2BAAAA,aAAa;AAAA,EAC1B,QAAQ,CAAC,GAAG,MAAM;AAAA;AACpB,CAAC;AACY,MAAA,YAAY,CAAC,oBAAoB;AACvC,MAAM,YAAY;AAElB,MAAM,YAAY,CAAC,IAAI,MAAM,SAAS;AAC3C,QAAM,YAAYC,WAAAA,aAAa;AAC/B,MAAI,UAAU,WAAW;AAEvB,SAAK,IAAI;AAAA,EAAA,OACJ;AAEL,QAAI,UAAU,SAAS,GAAG,IAAI,GAAG;AAC1B,WAAA;AAAA,IAAA,OACA;AACA,WAAA,EAAE,MAAM,WAAW;AAAA,IAAA;AAAA,EAC1B;AAEJ;AAEA,OAAO,WAAW,SAAS;AAG3B,SAAS,aAAaC,SAAQ;AAC5BA,UAAO,QAAQ,CAAC,SAAS;AACvB,QAAI,KAAK,MAAM;AACb,YAAM,OAAO,KAAK,KAAK,MAAM,GAAG,EAAE,IAAI;AACtC,WAAK,OAAO;AAAA,IAAA;AAAA,EACd,CACD;AACH;;;"}