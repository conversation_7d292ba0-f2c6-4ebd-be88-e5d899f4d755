"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../../../../common/vendor.js");
const pagesWork_components_echarts_props = require("../../props.js");
require("../../../common/echartUtil.js");
const pagesWork_components_hooks_useEchartMap = require("../../../hooks/useEchartMap.js");
if (!Array) {
  const _component_statusTip = common_vendor.resolveComponent("statusTip");
  _component_statusTip();
}
if (!Math) {
  echartsUniapp();
}
const echartsUniapp = () => "../../index.js";
const _sfc_main = {
  __name: "index",
  props: __spreadValues({}, pagesWork_components_echarts_props.echartProps),
  setup(__props) {
    const props = __props;
    const option = common_vendor.ref({});
    const chartOption = common_vendor.ref({
      geo: {
        map: "",
        itemStyle: {}
      },
      tooltip: {
        textStyle: {
          color: "#fff"
        },
        padding: 5,
        formatter: null
      }
    });
    let [
      { dataSource, reload, pageTips, config, mapDataJson, mapName, getAreaCode, city_point },
      {
        queryData,
        registerMap,
        setGeoAreaColor,
        handleTotalAndUnitMap,
        handleCommonOpt,
        queryCityCenter,
        getHeatMapData
      }
    ] = pagesWork_components_hooks_useEchartMap.useChartHook(props, initOption);
    const echartId = common_vendor.ref("");
    common_vendor.computed(() => ({ code: getAreaCode.value, data: mapDataJson.value }));
    function initOption(data) {
      return __async(this, null, function* () {
        var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j;
        let chartData = dataSource.value;
        let mapName2 = yield registerMap();
        try {
          chartOption.value.tooltip = {
            enterable: true,
            transitionDuration: 1,
            textStyle: {
              color: "#000",
              decoration: "none"
            },
            trigger: "item",
            formatter: (params) => {
              let value = params.value || 0;
              return `${params.name || "空"}:${value}`;
            }
          };
          chartOption.value.geo.map = mapName2;
          chartOption.value.series = [
            {
              name: "地图",
              type: "map",
              map: mapName2,
              geoIndex: 0,
              aspectScale: 0.75,
              //长宽比
              showLegendSymbol: false,
              // 存在legend时显示
              label: {
                show: true,
                color: "#000"
              },
              emphasis: {
                show: true,
                color: "#000",
                itemStyle: {
                  areaColor: "#2B91B7"
                }
              },
              roam: true,
              itemStyle: {
                areaColor: "#3B5077",
                borderColor: "#3B5077"
              },
              animation: true,
              data: chartData,
              zlevel: 1
            },
            {
              name: "数据",
              type: "heatmap",
              coordinateSystem: "geo",
              blurSize: ((_b = (_a = config.commonOption) == null ? void 0 : _a.heat) == null ? void 0 : _b.blurSize) || 20,
              pointSize: ((_d = (_c = config.commonOption) == null ? void 0 : _c.heat) == null ? void 0 : _d.pointSize) || 15,
              maxOpacity: ((_f = (_e = config.commonOption) == null ? void 0 : _e.heat) == null ? void 0 : _f.maxOpacity) || 1,
              data: getHeatMapData(chartData)
            }
          ];
          if (props.config && props.config.option) {
            common_vendor.merge(chartOption.value, props.config.option);
            chartOption.value = setGeoAreaColor(chartOption.value, props.config);
            chartOption.value = handleTotalAndUnitMap(
              props.compName,
              chartOption.value,
              props.config,
              chartData
            );
            chartOption.value = handleCommonOpt(chartOption.value);
          }
          if (((_h = (_g = chartOption.value) == null ? void 0 : _g.visualMap) == null ? void 0 : _h.max) === 0 && chartData.length > 0) {
            let maxValue = chartData.reduce((max, data2) => Math.max(max, data2.value), chartData[0].value);
            chartOption.value.visualMap.max = maxValue;
          }
          if ((_j = (_i = chartOption.value) == null ? void 0 : _i.visualMap) == null ? void 0 : _j.top) {
            chartOption.value.visualMap.top = "auto";
            chartOption.value.visualMap.bottom = "1%";
          }
          setTimeout(() => {
            option.value = __spreadValues({}, chartOption.value);
            pageTips.show = false;
            echartId.value = props.i;
          }, 300);
          if (dataSource.value && dataSource.value.length === 0) {
            pageTips.status = 1;
            pageTips.show = true;
          }
        } catch (e) {
          console.log("热力地图报错", e);
        }
      });
    }
    common_vendor.onMounted(() => __async(this, null, function* () {
      yield queryCityCenter();
      yield queryData();
    }));
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.unref(pageTips).show
      }, common_vendor.unref(pageTips).show ? {
        b: common_vendor.p({
          status: common_vendor.unref(pageTips).status
        })
      } : {
        c: common_vendor.p({
          option: option.value,
          mapName: common_vendor.unref(mapName),
          mapData: common_vendor.unref(mapDataJson)
        })
      });
    };
  }
};
wx.createComponent(_sfc_main);
//# sourceMappingURL=index.js.map
