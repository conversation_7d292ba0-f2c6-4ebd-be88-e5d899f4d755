{"version": 3, "file": "index.js", "sources": ["../../../../../../../src/pages-work/components/echarts/JScatter/index.vue", "../../../../../../../uniComponent:/RDoveWFuaml1L0hmRmx1cEFwcF9QYWdlL3VuaWFwcC9zcmMvcGFnZXMtd29yay9jb21wb25lbnRzL2VjaGFydHMvSlNjYXR0ZXIvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"content\">\r\n    <statusTip v-if=\"pageTips.show\" :status=\"pageTips.status\"></statusTip>\r\n    <echartsUniapp v-else :option=\"option\"></echartsUniapp>\r\n  </view>\r\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { echartProps } from '@/pages-work/components/echarts/props'\r\nimport { deepMerge, handleTotalAndUnit, disposeGridLayout } from '../../common/echartUtil'\r\nimport { isNumber } from '@/utils/is'\r\nimport useChartHook from '@/pages-work/components/hooks/useEchart'\r\nimport { deepClone } from '@/uni_modules/da-tree/utils'\r\nimport echartsUniapp from '@/pages-work/components/echarts/index.vue'\r\nimport statusTip from '@/pages-work/components/statusTip.vue'\r\nimport {merge} from \"lodash-es\";\r\n//组件传参\r\nconst props = defineProps({\r\n  ...echartProps,\r\n})\r\n\r\n//最终图表配置项\r\nconst option = ref({})\r\nlet chartOption = {\r\n  tooltip: {\r\n    trigger: 'axis',\r\n    axisPointer: {\r\n      type: 'shadow',\r\n      label: {\r\n        show: true,\r\n        backgroundColor: '#333',\r\n      },\r\n    },\r\n  },\r\n  legend: {\r\n    bottom: '5%',\r\n  },\r\n  xAxis: {},\r\n  yAxis: {\r\n    type: 'value',\r\n  },\r\n  series: [\r\n    {\r\n      type: 'scatter',\r\n      data: [],\r\n    },\r\n  ],\r\n}\r\n//图表数据查询\r\nlet [{ dataSource, reload, pageTips, config }, { queryData }] = useChartHook(props, initOption)\r\n\r\n//初始化图表配置项\r\nfunction initOption(data) {\r\n  let chartData: any = dataSource.value\r\n  if (typeof chartData === 'string') {\r\n    chartData = JSON.parse(chartData)\r\n  }\r\n  if (chartData && chartData.length > 0) {\r\n    //显示坐标轴前几项\r\n    if (config.dataFilterNum && isNumber(config.dataFilterNum)) {\r\n      chartData = chartData.slice(0, config.dataFilterNum)\r\n    }\r\n    let seriesData = chartData.map((item) => {\r\n      return item.value\r\n    })\r\n    let xAxisData = chartData.map((item) => {\r\n      return item.name\r\n    })\r\n    chartOption.series[0].data = seriesData\r\n    if (config.option.xAxis && config.option.xAxis.type) {\r\n      let type = config.option.xAxis['type'] == 'value' ? 'category' : 'value'\r\n      chartOption.yAxis['type'] = type\r\n    }\r\n    if (chartOption.yAxis.type == 'category') {\r\n      chartOption.yAxis.data = xAxisData\r\n    } else {\r\n      chartOption.xAxis.data = xAxisData\r\n    }\r\n    // 合并配置\r\n    if (props.config && config.option) {\r\n      merge(chartOption, config.option)\r\n      chartOption = handleTotalAndUnit(props.compName, chartOption, config, chartData)\r\n      chartOption = disposeGridLayout(props.compName, chartOption, config, chartData)\r\n      option.value = deepClone(chartOption)\r\n      pageTips.show = false\r\n    }\r\n  } else {\r\n    pageTips.status = 1\r\n    pageTips.show = true\r\n  }\r\n}\r\n\r\nonMounted(() => {\r\n  queryData()\r\n})\r\n</script>\r\n<style>\r\n.content {\r\n  padding: 10px;\r\n}\r\n</style>\r\n", "import Component from 'D:/yanjiu/HfFlupApp_Page/uniapp/src/pages-work/components/echarts/JScatter/index.vue'\nwx.createComponent(Component)"], "names": ["ref", "useChartHook", "isNumber", "merge", "handleTotalAndUnit", "disposeGridLayout", "deepClone", "onMounted", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAaA,MAAA,gBAA0B,MAAA;AAC1B,MAAA,YAAsB,MAAA;;;;;AAGtB,UAAM,QAAQ;AAKR,UAAA,SAASA,cAAI,IAAA,EAAE;AACrB,QAAI,cAAc;AAAA,MAChB,SAAS;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,UACX,MAAM;AAAA,UACN,OAAO;AAAA,YACL,MAAM;AAAA,YACN,iBAAiB;AAAA,UAAA;AAAA,QACnB;AAAA,MAEJ;AAAA,MACA,QAAQ;AAAA,QACN,QAAQ;AAAA,MACV;AAAA,MACA,OAAO,CAAC;AAAA,MACR,OAAO;AAAA,QACL,MAAM;AAAA,MACR;AAAA,MACA,QAAQ;AAAA,QACN;AAAA,UACE,MAAM;AAAA,UACN,MAAM,CAAA;AAAA,QAAC;AAAA,MACT;AAAA,IAEJ;AAEA,QAAI,CAAC,EAAE,YAAY,QAAQ,UAAU,OAAA,GAAU,EAAE,WAAW,IAAIC,kDAAa,OAAO,UAAU;AAG9F,aAAS,WAAW,MAAM;AACxB,UAAI,YAAiB,WAAW;AAC5B,UAAA,OAAO,cAAc,UAAU;AACrB,oBAAA,KAAK,MAAM,SAAS;AAAA,MAAA;AAE9B,UAAA,aAAa,UAAU,SAAS,GAAG;AAErC,YAAI,OAAO,iBAAiBC,SAAS,SAAA,OAAO,aAAa,GAAG;AAC1D,sBAAY,UAAU,MAAM,GAAG,OAAO,aAAa;AAAA,QAAA;AAErD,YAAI,aAAa,UAAU,IAAI,CAAC,SAAS;AACvC,iBAAO,KAAK;AAAA,QAAA,CACb;AACD,YAAI,YAAY,UAAU,IAAI,CAAC,SAAS;AACtC,iBAAO,KAAK;AAAA,QAAA,CACb;AACW,oBAAA,OAAO,CAAC,EAAE,OAAO;AAC7B,YAAI,OAAO,OAAO,SAAS,OAAO,OAAO,MAAM,MAAM;AACnD,cAAI,OAAO,OAAO,OAAO,MAAM,MAAM,KAAK,UAAU,aAAa;AACrD,sBAAA,MAAM,MAAM,IAAI;AAAA,QAAA;AAE1B,YAAA,YAAY,MAAM,QAAQ,YAAY;AACxC,sBAAY,MAAM,OAAO;AAAA,QAAA,OACpB;AACL,sBAAY,MAAM,OAAO;AAAA,QAAA;AAGvB,YAAA,MAAM,UAAU,OAAO,QAAQ;AAC3BC,8BAAA,aAAa,OAAO,MAAM;AAChC,wBAAcC,uCAAmB,mBAAA,MAAM,UAAU,aAAa,QAAQ,SAAS;AAC/E,wBAAcC,uCAAkB,kBAAA,MAAM,UAAU,WAA8B;AACvE,iBAAA,QAAQC,mCAAU,WAAW;AACpC,mBAAS,OAAO;AAAA,QAAA;AAAA,MAClB,OACK;AACL,iBAAS,SAAS;AAClB,iBAAS,OAAO;AAAA,MAAA;AAAA,IAClB;AAGFC,kBAAAA,UAAU,MAAM;AACJ,gBAAA;AAAA,IAAA,CACX;;;;;;;;;;;;;;;;AC7FD,GAAG,gBAAgBC,SAAS;"}