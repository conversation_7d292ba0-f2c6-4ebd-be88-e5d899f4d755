"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
const common_vendor = require("../../../../common/vendor.js");
const pagesWork_components_echarts_props = require("../props.js");
const pagesWork_components_common_echartUtil = require("../../common/echartUtil.js");
const pagesWork_components_hooks_useEchart = require("../../hooks/useEchart.js");
const uni_modules_daTree_utils = require("../../../../uni_modules/da-tree/utils.js");
if (!Math) {
  (statusTip + echartsUniapp)();
}
const echartsUniapp = () => "../index.js";
const statusTip = () => "../../statusTip.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  props: __spreadValues({}, pagesWork_components_echarts_props.echartProps),
  setup(__props) {
    const props = __props;
    const option = common_vendor.ref({});
    let chartOption = {
      title: {
        show: true
      },
      legend: {
        show: true,
        data: []
      },
      xAxis: {
        type: "value"
      },
      yAxis: {
        type: "category"
      },
      series: [],
      dataset: {
        dimensions: [],
        source: []
      }
    };
    let [{ dataSource, reload, pageTips, config }, { queryData }] = pagesWork_components_hooks_useEchart.useChartHook(
      props,
      initOption
    );
    function initOption(data) {
      let chartData = dataSource.value;
      if (typeof chartData === "string") {
        chartData = JSON.parse(chartData);
      }
      if (chartData && chartData.length > 0) {
        const colors = pagesWork_components_common_echartUtil.getCustomColor(config.option.customColor);
        let configOption = config.option;
        let dataset = pagesWork_components_common_echartUtil.getDataSet(chartData, config);
        let label = configOption.series.length > 0 && configOption.series[0].label ? configOption.series[0].label : {};
        chartOption.dataset = dataset;
        chartOption.series = [];
        dataset.dimensions.forEach((series, index) => {
          if (index > 0) {
            let legengColor = configOption.series.length > 0 && configOption.series[0].color ? configOption.series[0].color[index - 1] : null;
            let color = colors && colors[index - 1] ? colors[index - 1].color : "";
            console.log("colors====>", colors);
            chartOption.series.push({
              type: "bar",
              //TODO 自定义图表类型
              color: legengColor || color,
              //TODO 自定义颜色
              series,
              //TODO 系列，冗余数据，只是table展示使用
              label
            });
          }
        });
        chartOption.legend.data = chartOption.series.map((item) => item.series);
        if (config.option.xAxis && config.option.xAxis.type) {
          let type = config.option.xAxis["type"] == "value" ? "category" : "value";
          chartOption.yAxis["type"] = type;
        }
        if (props.config && config.option) {
          common_vendor.merge(chartOption, config.option);
          chartOption = pagesWork_components_common_echartUtil.handleTotalAndUnit(props.compName, chartOption, config, chartData);
          chartOption = pagesWork_components_common_echartUtil.disposeGridLayout(props.compName, chartOption);
          option.value = uni_modules_daTree_utils.deepClone(chartOption);
          pageTips.show = false;
        }
      } else {
        pageTips.status = 1;
        pageTips.show = true;
      }
    }
    common_vendor.onMounted(() => {
      queryData();
    });
    return (_ctx, _cache) => {
      return common_vendor.e$1({
        a: common_vendor.unref(pageTips).show
      }, common_vendor.unref(pageTips).show ? {
        b: common_vendor.p({
          status: common_vendor.unref(pageTips).status
        })
      } : {
        c: common_vendor.p({
          option: common_vendor.unref(option)
        })
      });
    };
  }
});
wx.createComponent(_sfc_main);
//# sourceMappingURL=index.js.map
