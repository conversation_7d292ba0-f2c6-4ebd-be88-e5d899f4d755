{"version": 3, "file": "pet.vuequery.js", "sources": ["../../../../../src/service/app/pet.vuequery.ts"], "sourcesContent": ["/* eslint-disable */\r\n// @ts-ignore\r\nimport { queryOptions, useMutation } from '@tanstack/vue-query';\r\nimport type { DefaultError } from '@tanstack/vue-query';\r\nimport request from '@/utils/request';\r\nimport { CustomRequestOptions } from '@/interceptors/request';\r\n\r\nimport * as apis from './pet';\r\nimport * as API from './types';\r\n\r\n/** Update an existing pet PUT /pet */\r\nexport function useUpdatePetMutation(options?: {\r\n  onSuccess?: (value?: unknown) => void;\r\n  onError?: (error?: DefaultError) => void;\r\n}) {\r\n  const { onSuccess, onError } = options || {};\r\n\r\n  const response = useMutation({\r\n    mutationFn: apis.updatePet,\r\n    onSuccess(data: unknown) {\r\n      onSuccess?.(data);\r\n    },\r\n    onError(error) {\r\n      onError?.(error);\r\n    },\r\n  });\r\n\r\n  return response;\r\n}\r\n\r\n/** Add a new pet to the store POST /pet */\r\nexport function useAddPetMutation(options?: {\r\n  onSuccess?: (value?: unknown) => void;\r\n  onError?: (error?: DefaultError) => void;\r\n}) {\r\n  const { onSuccess, onError } = options || {};\r\n\r\n  const response = useMutation({\r\n    mutationFn: apis.addPet,\r\n    onSuccess(data: unknown) {\r\n      onSuccess?.(data);\r\n    },\r\n    onError(error) {\r\n      onError?.(error);\r\n    },\r\n  });\r\n\r\n  return response;\r\n}\r\n\r\n/** Find pet by ID Returns a single pet GET /pet/${param0} */\r\nexport function getPetByIdQueryOptions(options: {\r\n  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)\r\n  params: API.getPetByIdParams;\r\n  options?: CustomRequestOptions;\r\n}) {\r\n  return queryOptions({\r\n    queryFn: async ({ queryKey }) => {\r\n      return apis.getPetById(queryKey[1] as typeof options);\r\n    },\r\n    queryKey: ['getPetById', options],\r\n  });\r\n}\r\n\r\n/** Updates a pet in the store with form data POST /pet/${param0} */\r\nexport function useUpdatePetWithFormMutation(options?: {\r\n  onSuccess?: (value?: unknown) => void;\r\n  onError?: (error?: DefaultError) => void;\r\n}) {\r\n  const { onSuccess, onError } = options || {};\r\n\r\n  const response = useMutation({\r\n    mutationFn: apis.updatePetWithForm,\r\n    onSuccess(data: unknown) {\r\n      onSuccess?.(data);\r\n    },\r\n    onError(error) {\r\n      onError?.(error);\r\n    },\r\n  });\r\n\r\n  return response;\r\n}\r\n\r\n/** Deletes a pet DELETE /pet/${param0} */\r\nexport function useDeletePetMutation(options?: {\r\n  onSuccess?: (value?: unknown) => void;\r\n  onError?: (error?: DefaultError) => void;\r\n}) {\r\n  const { onSuccess, onError } = options || {};\r\n\r\n  const response = useMutation({\r\n    mutationFn: apis.deletePet,\r\n    onSuccess(data: unknown) {\r\n      onSuccess?.(data);\r\n    },\r\n    onError(error) {\r\n      onError?.(error);\r\n    },\r\n  });\r\n\r\n  return response;\r\n}\r\n\r\n/** uploads an image POST /pet/${param0}/uploadImage */\r\nexport function useUploadFileMutation(options?: {\r\n  onSuccess?: (value?: API.ApiResponse) => void;\r\n  onError?: (error?: DefaultError) => void;\r\n}) {\r\n  const { onSuccess, onError } = options || {};\r\n\r\n  const response = useMutation({\r\n    mutationFn: apis.uploadFile,\r\n    onSuccess(data: API.ApiResponse) {\r\n      onSuccess?.(data);\r\n    },\r\n    onError(error) {\r\n      onError?.(error);\r\n    },\r\n  });\r\n\r\n  return response;\r\n}\r\n\r\n/** Finds Pets by status Multiple status values can be provided with comma separated strings GET /pet/findByStatus */\r\nexport function findPetsByStatusQueryOptions(options: {\r\n  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)\r\n  params: API.findPetsByStatusParams;\r\n  options?: CustomRequestOptions;\r\n}) {\r\n  return queryOptions({\r\n    queryFn: async ({ queryKey }) => {\r\n      return apis.findPetsByStatus(queryKey[1] as typeof options);\r\n    },\r\n    queryKey: ['findPetsByStatus', options],\r\n  });\r\n}\r\n\r\n/** Finds Pets by tags Multiple tags can be provided with comma separated strings. Use tag1, tag2, tag3 for testing. GET /pet/findByTags */\r\nexport function findPetsByTagsQueryOptions(options: {\r\n  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)\r\n  params: API.findPetsByTagsParams;\r\n  options?: CustomRequestOptions;\r\n}) {\r\n  return queryOptions({\r\n    queryFn: async ({ queryKey }) => {\r\n      return apis.findPetsByTags(queryKey[1] as typeof options);\r\n    },\r\n    queryKey: ['findPetsByTags', options],\r\n  });\r\n}\r\n"], "names": ["queryOptions", "apis.findPetsByStatus"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AA6HO,SAAS,6BAA6B,SAI1C;AACD,SAAOA,2BAAa;AAAA,IAClB,SAAS,CAAO,OAAiB,eAAjB,KAAiB,WAAjB,EAAE,YAAe;AAC/B,aAAOC,gBAAK,iBAAiB,SAAS,CAAC,CAAmB;AAAA,IAC5D;AAAA,IACA,UAAU,CAAC,oBAAoB,OAAO;AAAA,EAAA,CACvC;AACH;;"}